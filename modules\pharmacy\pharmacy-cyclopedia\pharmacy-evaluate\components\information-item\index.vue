<!-- 业务配置列表组件 <business-list :list="xxx" :url="xxx"></business-list> -->
<template>
	<view class="home-course-business-list">
		<view class="li"  v-for="(item, index) in list" :key="index" @tap="jump(item)">
          <view class="t">
              <view class="left">
                <image mode="aspectFill" :src="item.coverPath ?  item.coverPath : $static_ctx + 'image/system/avatar/icon-default-avatar.png'"  class="role-image"/>
              </view>
              <view class="right">
                  <text class="title">{{item.title}}</text>
                  <view class="content">
                      {{item.introduction}}
                  </view>
                  <view class="footer">
                      <view class="l">
                        <text>{{item.author}}</text>
                      </view>
                      <view class="r">
                        <text>{{item.creationTimeText}}</text>
                      </view>
                  </view>
              </view>
          </view>
		</view>
	</view>
</template>

<script>
export default {
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      $validate: this.$validate
    }
  },
  props: {
    isStore: {
      type: Boolean,
      default() {
        return false
      }
    },
    list: { // 数据列表
      type: Array,
      default() {
        return []
      }
    },
    url: {
      type: String,
      default() {
        return ''
      }
    },
    // 显示字段
    field: {
      type: Array,
      default() {
        return []
      }
    }
  },
  methods: {
    jump(param) {
      // this.url && this.$navto.push(this.url, param)
      // !this.url && this.$emit('returnFn', param)
      // if (param.type === 4){
      //   debugger
      //   this.$navto.push('WebHtmlView', { src: param.linkAddress, title: param.title })
      //   return
      // }
      this.$navto.push('InformationDetail', { id: param.id})
    }
  }
}
</script>

<style lang="scss" scoped>
.home-course-business-list {
    view{
        line-height: 0;
    }
    //padding: 0upx 30upx 24upx;
    .li {
        padding: 16upx;
        background: #fff;
        //@include rounded(20upx);
        box-sizing: border-box;
        overflow: hidden;
        margin-bottom: 1upx;
        position: relative;
        .left {
            vertical-align: top;
            display: inline-block;
            width: 240upx;
            height: 180upx;
            overflow: hidden;
            @include rounded(10upx);

            .role-image {
                width: 100%;
                height: 100%;
            }
        }

        .right {
            vertical-align: middle;
            display: inline-block;
            width: calc(100% - 260upx);
            margin-left: 20upx;
            box-sizing: border-box;

            .title {
                font-size: 32upx;
                line-height: 48upx;
                @include ellipsis(1);
                width: 100%;
                box-sizing: border-box;
            }

            .content {
                font-size: 24upx;
                line-height: 36upx;
                font-weight: 400;
                color: #999999;
                @include ellipsis(1);
                width: 100%;
                box-sizing: border-box;
            }
            .footer{
              .l {
                bottom: 10upx;
                position: absolute;
                text {
                  display: inline-block;
                  //margin: 0 30upx 8upx 0;
                  padding: 0 10upx;
                  border: 2upx solid #cfcaca;
                  font-size: 24upx;
                  line-height: 36upx;
                  color: #c1c1c1;
                  @include rounded(6upx);
                }
              }
              .r {
                position: absolute;
                right: 20upx;
                bottom: 30upx;
                text {
                  color: #bbbaba;
                  font-size: 24upx;
                }
              }
            }
        }
    }
    .li:last-of-type{
        margin-bottom: 0;
    }
}

</style>
