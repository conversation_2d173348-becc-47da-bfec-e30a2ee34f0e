<template>
  <view class="hb-comment">
    <!-- 评论主体-顶部数量及发表评论按钮-start -->
    <view class="comment-num">
      <view class="comment-title"
        >精彩评论（{{ commentData.commentSize >= 0 ? commentData.commentSize : 0 }}）</view
      >
      <!-- <view class="comment-title" v-if="commentData.commentPublishCount"
        >精彩评论（{{ commentData.commentPublishCount }}）</view
      > -->
      <view class="comment-add-btn" @click="addComment"> 写留言 </view>
    </view>
    <!-- 评论主体-顶部数量及发表评论按钮-end -->
    <view style="padding: 32rpx 0; color: #aaa;" v-if="launchOptions.scene === 1154">
      请前往小程序使用完整服务
    </view>
    <template v-else>
      <!-- 评论主体-start -->
      <view class="comment-list" v-if="commentData.comment.length != 0">
        <!-- 评论列表-start -->
        <view
          class="comment-box"
          :key="item.id"
          v-for="item in commentData.comment"
        >
          <view class="comment-box-item">
            <view @tap="userClick(item)">
              <image
                v-if="item.businessType == 10 && !item.userId"
                :src="
                  file_ctx +
                  (item.headPath || '0/msg-reply/807939727497555969.jpg')
                "
                mode="aspectFill"
                class="avatar"
              >
              </image>
              <image
                v-else-if="item.businessType == 10 && item.userId"
                :src="
                  item.headPath.includes(file_ctx)
                    ? item.headPath
                    : file_ctx + item.headPath
                "
                mode="aspectFill"
                class="avatar"
                @error="handleError(item)"
              >
              </image>
              <image
                v-else
                :src="item.avatarUrl || emptyAvatar"
                mode="aspectFill"
                class="avatar"
              ></image>
            </view>
            <view class="comment-main">
              <!-- 父评论体-start -->
              <view class="comment-main-top">
                <view class="nick-name-box" @tap="userClick(item)">
                  <template>
                    <view
                      class="flex-align-center-box"
                      v-if="item.businessType == 10 && !item.userId"
                    >
                      <text class="nick-name nick-name-width-1">葫芦大夫</text>
                      <text
                        v-if="item.businessType !== 10"
                        class="tip"
                      >
                        {{ item.province || "未知" }}
                      </text>
                    </view>
                    <view
                      v-else
                      class="flex-align-center-box"
                    >
                      <text class="nick-name nick-name-width-1">{{ item.nickName }}</text>
                      <template v-if="item.doctorPost">
                        <text> · </text>
                        {{ item.doctorPost }}
                      </template>
                      <text
                        v-if="item.businessType !== 10"
                        class="tip"
                      >
                        {{ item.province || "未知" }}
                      </text>
                    </view>
                    
                  </template>
                  <view
                    class="nick-hospitcal"
                    v-if="item.businessType == 10 && item.userId"
                  >
                    <view v-if="item.hospital">
                      {{ item.hospital }}
                      <span> · </span>{{ item.deptName }}
                    </view>
                  </view>
                </view>
                <view v-if="item.status !== -1" class="flex-align-center-box">
                  <view
                    class="foot-btn"
                    @click="reply(item.nickName, item.nickName, item.id, item)"
                    >回复</view
                  >
                  <view
                    class="foot-btn"
                    v-if="item.owner"
                    @click="confirmDelete(item)"
                    >删除</view
                  >
                  <view class="zan-box flex-align-center-box" @click="like(item)">
                    <img
                      v-if="!item.hasLike"
                      class="like-icon"
                      :src="file_ctx + 'static/image/business/hulu-v2/icon-like.png'"
                    />
                    <img
                      v-else
                      class="like-icon"
                      :src="file_ctx + 'static/image/business/hulu-v2/icon-like-active.png'"
                    />
                    <span class="like-text">{{
                      item.likeNum == 0 ? "抢首赞" : item.likeNum
                    }}</span>
                  </view>
                </view>
              </view>
              <view class="comment-main-content">
                <template v-if="item.status !== -1">
                  <view v-if="item.imagePath">
                    <image
                      @click="previewImage(item.imagePath.split(','), eIndex)"
                      v-for="(e, eIndex) in item.imagePath.split(',')"
                      :key="eIndex"
                      :src="file_ctx + e"
                      mode="widthFix"
                      class="content-img"
                    ></image>
                  </view>

                  <!-- 评论内容 -->
                  <!-- <view class="charInfo mgL20 left clearFix"> -->
                  <block
                    v-for="(item2, index2) in item.parsedComment"
                    :key="index2"
                  >
                    <text style="word-break: break-all" v-if="item2.type === 1">{{
                      item2.content
                    }}</text>
                    <view
                      v-if="item2.type === 2"
                      style="
                        display: inline-block;
                        height: 55rpx;
                        width: 55rpx;
                        overflow: hidden;
                        vertical-align: middle;
                      "
                    >
                      <view
                        :class="item2.imageClass"
                        :style="{
                          'background-image': 'url(' + emojiSource + ')',
                          transform: 'scale(' + lineHeight / 64 + ')',
                        }"
                        style="transform-origin: 0 0"
                      >
                      </view>
                    </view>
                  </block>
                </template>

                <view class="foot-btn" v-else>该评论已被删除</view>
              </view>
              <view class="foot-source" v-if="item.dataSource"
                >来源<span>{{ item.dataSource }}</span></view
              >
              <view class="comment-main-foot">
                <!-- <view class="foot-time">{{item.createTime | getTimeStringAutoShort2}} <text class="tip">来自{{ item.province && item.province != '' ? item.province : '未知'}}</text></view> -->
                <view class="foot-time">
                  {{ item.putawayTime | getTimeStringAutoShort2 }}
                </view>
              </view>
              <!-- 父评论体-end -->
              <!-- 子评论列表-start -->
              <view class="comment-sub-box" v-if="(item.children && item.children.length > 0) || item.isHasMore">
                <view class="comment-sub-item" v-for="each in item.children" :key="each.id">
                  <view class="comment-sub-item-top">
                    <image
                      :src="each.avatarUrl || emptyAvatar"
                      mode="aspectFill"
                      class="avatar"
                      @tap="userClick(each)"
                    >
                    </image>
                    <view class="mutual-comment-box">
                      <text
                        class="nick-name nick-name-width-2"
                        @tap="userClick(each)"
                      >{{ each.nickName }}</text>
                    </view>
                    <view class="zan-box flex-align-center-box" @click="like(each)">
                      <img
                        v-if="!item.hasLike"
                        class="like-icon"
                        :src="file_ctx + 'static/image/business/hulu-v2/icon-like.png'"
                      />
                      <img
                        v-else
                        class="like-icon"
                        :src="file_ctx + 'static/image/business/hulu-v2/icon-like-active.png'"
                      />
                      <span class="like-text">{{
                        each.likeNum == 0 ? "抢首赞" : each.likeNum
                      }}</span>
                    </view>
                  </view>
                  <view class="comment-main">
                    <view class="comment-main-content">
                      <view v-if="each.imagePath">
                        <image
                          @click="previewImage(each.imagePath.split(','), eIndex)"
                          v-for="(e, eIndex) in each.imagePath.split(',')"
                          :key="eIndex"
                          :src="file_ctx + e"
                          mode="widthFix"
                          class="content-img"
                        ></image>
                      </view>
                      <block
                        v-for="(item2, index2) in each.parsedComment"
                        :key="index2"
                      >
                        <text
                          style="word-break: break-all"
                          v-if="item2.type === 1"
                          >{{ item2.content }}</text
                        >
                        <view
                          v-if="item2.type === 2"
                          style="
                            display: inline-block;
                            height: 55rpx;
                            width: 55rpx;
                            overflow: hidden;
                            vertical-align: middle;
                          "
                        >
                          <view
                            :class="item2.imageClass"
                            :style="{
                              'background-image': 'url(' + emojiSource + ')',
                              transform: 'scale(' + lineHeight / 64 + ')',
                            }"
                            style="transform-origin: 0 0"
                          >
                          </view>
                        </view>
                      </block>
                    </view>
                    <view class="comment-main-foot">
                      <view class="foot-time">
                        {{ each.createTime | getTimeStringAutoShort2 }}
                        <text class="tip" v-if="item.businessType !== 10"
                          >{{
                            each.province && each.province != ""
                              ? each.province
                              : "未知"
                          }}</text
                        >
                      </view>
                      <template v-if="each.status !== -1">
                        <view
                          class="foot-btn"
                          @click="
                            reply(item.nickName, each.nickName, item.id, each)
                          "
                        >
                          回复</view
                        >
                        <view
                          class="foot-btn"
                          v-if="each.owner"
                          @click="confirmDelete(each)"
                          >删除
                        </view>
                      </template>
                      <view
                        class="foot-btn"
                        v-if="each.owner && each.status === -1"
                        >该评论已被删除</view
                      >
                    </view>
                  </view>
                </view>
                <view class="comment-sub-more" v-if="item.moreLoading">
                  <text class="comment-sub-more-text">正在加载评论</text>
                  <uni-icons color="#8B8B8B" type="spinner-cycle" />
                </view>
                <view
                  class="comment-sub-more"
                  v-else-if="item.isHasMore"
                  @tap="openComment(item)"
                >
                  <text
                    class="comment-sub-more-text"
                    v-if="$validate.isNull(item.children)"
                    >展开{{ item.mutualNumber }}条回复</text
                  >
                  <text class="comment-sub-more-text" v-else>展开更多回复</text>
                  <uni-icons color="#8B8B8B" type="bottom" />
                </view>
              </view>
              <!-- 子评论列表-end -->
            </view>
          </view>
        </view>
        <view class="more" v-if="moreLoading">
          <text class="more-text">正在加载评论</text>
          <uni-icons color="#00D29D" type="spinner-cycle" />
        </view>
        <view
          class="more"
          v-else-if="commentData.isHasMore"
          @tap="$emit('loadMore')"
        >
          <text class="more-text">查看更多评论</text>
          <uni-icons color="#00D29D" type="bottom" />
        </view>
        <template v-else>
          <nui-nomore
            defaultStyle="margin: 0 auto 24rpx;"
            backgroundColor="#fff"
            text="没有更多评论"
          ></nui-nomore>
        </template>
        <!-- 评论列表-end -->
      </view>
      <!-- 评论主体-end -->
      <!-- 评论loading-start -->
      <view class="comment-loading" v-else-if="commentData.loading">
        <text class="comment-loading-text">正在加载评论</text>
        <uni-icons color="#8B8B8B" type="spinner-cycle" />
      </view>
      <!-- 评论loading-end -->
      <!-- 无评论-start -->
      <view class="comment-none" v-else>
        <image :src="file_ctx + 'static/image/business/hulu-v2/icon-comment-empty.png'" class="comment-empty-img" />
        <text class="comment-empty-text">暂无评论</text>
      </view>
      <!-- 无评论-end -->
    </template>
    <!-- 新增评论-start -->
    <view
      class="comment-submit-box"
      :style="{
        display: submit ? 'flex' : 'none',
      }"
      @click="closeInput"
    >
      <!-- 下边的click.stop.prevent用于让上边的click不传下去，以防点到下边的空白处触发closeInput方法 -->
      <view
        class="comment-add"
        @click.stop.prevent="stopPrevent"
        :style="'bottom:' + KeyboardHeight + 'px'"
      >
        <textarea
          class="textarea"
          v-model="commentReq.content"
          :placeholder="placeholder ? placeholder : '回复楼主'"
          :adjust-position="false"
          :show-confirm-bar="false"
          @blur="blur"
          @focus="focusOn"
          :focus="focus"
          @input="onInput"
          maxlength="250"
        ></textarea>

        <template v-if="!$validate.isNull(sendImgages)">
          <view
            v-for="(e, eIndex) in sendImgages.map((item) => item.dir)"
            :key="eIndex"
            class="send-img-box"
          >
            <view class="send-img-remove" @click.stop="sendImgages = []"></view>
            <image
              @click="previewImage(sendImgages, eIndex)"
              :src="file_ctx + e"
              mode="aspectFill"
              class="send-img"
            ></image>
          </view>
        </template>
        <view class="comment-add-bottom">
          <view class="d-t">
            <image
              @tap="clickSendImg"
              :src="file_ctx + 'static/image/business/hulu-v2/icon-image.png'"
              mode="aspectFill"
              class="select-icon"
            ></image>
            <image
              @click="showEmoji"
              :src="file_ctx + 'static/image/business/hulu-v2/icon-emoji.png'"
              mode="aspectFill"
              class="select-icon"
            ></image>
          </view>
          <title-img
            disabled
            ref="sendImgRef"
            :config="config.img"
            @uploadFinish="sendImg"
          ></title-img>
          <view class="comment-add-bottom-right">
            <button
              @click="handleRelease"
              class="release-btn"
              type="default"
              size="mini"
              :disabled="!commentReq.content && $validate.isNull(sendImgages)"
            >
              发布
            </button>
          </view>
        </view>

        <!-- 表情包的引用 -->
        <view
          class="reply_panel_wrp"
          :style="{ height: emojiShow ? 300 + 'px' : 200 + 'px' }"
          :hidden="!emojiShow && !functionShow"
        >
          <view
            class="reply_panel"
            :class="[emojiShow ? 'show' : '']"
            :hidden="!emojiShow"
          >
            <nui-emoji
              ref="emojiRef"
              :source="emojiSource"
              class="mp-emoji"
              @insertemoji="insertEmoji"
              @delemoji="deleteEmoji"
              @send="onsend"
              :padding="0"
            ></nui-emoji>
          </view>
        </view>
      </view>
    </view>
    <!-- 新增评论-end -->
  </view>
</template>

<script>
import { mapState } from 'vuex'
import timePlugin from '@/common/util/time-plugin'
import nuiNomore from '@/modules/community/components/nui-nomore/nui-nomore.vue'
import UniIcons from '@/components/uni/uni-icons/uni-icons.vue'
import TitleImg from "@/components/business/module/title-img/index.vue"
import common from '@/common/util/main'


import nuiEmoji from '@/modules/community/components/nui-emoji/nui-emoji.vue'


export default {
  name: 'hb-comment',
  components: {
    nuiNomore,
    UniIcons,
    TitleImg,
    nuiEmoji
  },
  props: {
    cmData: {
      type: Object,
      default: () => {
        return null;
      }
    },
    deleteTip: {
      type: String,
      default: () => {
        return '操作不可逆，如果评论下有子评论，也将被一并删除，确认？';
      }
    },
    moreLoading: {
      type: Boolean,
      default: false
    },
    isShowBtn:{
      type: Boolean,
      default: true
    },
  },
  watch: {
    cmData: {
      handler: function (newVal, oldVal) {
        // console.log(this.cmData,'this.cmData')
        this.init(newVal);
      },
      immediate: true
    },
    replyTarget: {
      handler: function () {
        this.clearInput()
        this.$emit('changeVal', { target: this.replyTarget, content: this.commentReq.content, imagePath: this.sendImgages })
      },
      deep: true
    },
    sendImgages: {
      handler: function () {
        this.$emit('changeVal', { target: this.replyTarget, content: this.commentReq.content, imagePath: this.sendImgages })
      },
      deep: true
    },
    'commentReq.content': {
      handler: function () {
        this.$emit('changeVal', { target: this.replyTarget, content: this.commentReq.content, imagePath: this.sendImgages })
      },
      immediate: true
    }
  },
  data() {
    return {
      // 表情业务逻辑
      titleHeight: "45",
      statusHeight: 0,
      // 评论输入框
      keyboardHeight: 0,
      lineHeight: 60,
      functionShow: false,
      emojiShow: false,
      comment: '',
      focus: false,
      cursor: 0,
      _keyboardShow: false,
      emojiSource: this.$static_ctx + 'image/system/bg-face.png',
      parsedComment: [{
        type: 2,
        content: "[憨笑]",
        imageClass: "smiley_28"
      },
      {
        type: 1,
        content: "44"
      },
      {
        type: 2,
        content: "[呲牙]",
        imageClass: "smiley_13"
      },
      {
        type: 2,
        content: "[调皮]",
        imageClass: "smiley_12"
      }
      ],
      // $static_ctx: this.$static_ctx,
      emojiURL: this.$static_ctx + 'image/system/icon-face.png',


      // -------------------------------------
      "emptyAvatar": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAABF5JREFUWEfFl11oHFUUx//nbmKwxdJ9qFL7YPEhRJC2gljjF60WG0WsoNkWfSnZ7Jy7FVPF0AoKpmBFqyIG3Jk7G1iIQXHbBz+KbbGtRfBbMe2LseKjiVDoiqIhwZ0jV3fb3cnM7kQCuU+7M+ec/+9+nHPPEJZ50DLrY1EApVJp9fz8/BYRuZ2INgDYWJvAWRE5R0RnZmZmPh4ZGZlPOrFEAMVi8e4gCPYSUZ+IXGGFgiCYIaJpKyQi1yql1orIFgAXARxRSvm5XO67diBtAYwxRQCDAE4RUUkpdWxwcNCKLBiu665TSj0kIpqIbgTgMzO3gmgJYIz5CMB9AIaZ+bXGQMVi8RoRuZeI/lZKHc1ms3/U34+Pj6+cnZ3dC+AggGPMfH8cRCyAMUZqThlmPlwPUCgU0qlUyq7Kww1BrbjHzPsahYwx/QDK9hkzR2pFPjTGnAWwIexkjOkRkRIR3Rozo5Miskdr/VMIxE7mHDPXD+2l1wsA6nseBMHmfD7/dSjQOwB2tTlYC/bddd1blFJfARhj5lyjfxNA7bSfitpz3/d3iYgFaDuUUjeHM8AY8zSAV5VS9+RyudP1IE0Axpj3Aaxk5m1hFc/zPiWiO9uq/2dwiJn3h22NMScB/MnMOxYAjI6Orurq6rpgU0hrXYpw/hFAd0KAD5n5wbCt7/t7ROT1ubm5NUNDQ7/b95dWwHXd7Uqp452dnVcPDAxciACwDlclAbAFynGcdRGr2EtEnwdB0JfP5080ARhjDhLRbY7jbI0SMcZUAKxOAgDgN2ZOx8SxGfEiMz/bBOD7/lgQBCu01o/GOH4PYFNCgElmvinK1vf9X2xxchzHVtfLW2CMOQpgipmHYwBeBtBUaOJgRKSgtX48Js63AH5l5geaADzPe1cpddFxnHyM42YAXyZYAXt+epn557iVFJHzWuudYYDnlFJ9juPcESfi+35JRHa3gdjPzIfibIwxNtPe0Fq/EAZ4hIhcZl4T5+y67nql1CcA1kfZENFnrSZQ6ycqItKvtT4SBthIRJMdHR092WzW5nzk8H1/WEReiQHY4TjOB3G+nuf9qyEim7TW9r65fAjL5fKVlUrlPICXmPnNFquwWym1oFBZ+yAItubz+TMtAJ4gon3pdLo7k8nMNgHYP7ZeE5EWkbuYeaYx0NjY2HXValUDeApAV4zIJIADzPxe+H2hULg+lUp9U6sBl3qLprugXC6nKpXKF0R02nGcZ2wQ3/e3ichOEckQ0aoEWQDbsgF4a3p6eqLeH3qeN0FE3el0ujeTyVTrcaKuY1uIjIg8CaCfiLYnEW1hY4WPi8gEgMeY+e1G27iGxHYxtptZynGYmTPhgK1asqWEiBRfcAjDdMaYpYCIFW8LUMuM54nIsb3/YvbDXskiYtuzA6382n4X1CDWAnCSgNSFa98ETakcWbwWMytjzAoAPUEQ3JBKpXrs75r/VLVanVJK/VC7Uf9KGjfRCiQN9n/slh3gHz9i4jC+FVL5AAAAAElFTkSuQmCC",
      "commentData": null,
      "placeholder": "请输入评论",
      "commentReq": {
        "pId": null, // 评论父id
        "content": null // 评论内容
      },
      "pUser": null, // 标签-回复人
      "showTag": false, // 标签展示与否
      "focus": false, // 输入框自动聚焦
      "submit": false, // 弹出评论
      "KeyboardHeight": 0, // 键盘高度
      $validate: this.$validate,
      file_ctx: this.file_ctx,
      isAnonymous: false, // 发表评论是否匿名
      replyTarget: null,
      config: {
        img: {
          count: 1,
          background: 'rgba(0,0,0,0)',
          formData: {
            groupId: "26000",
            relId: ''
          },
          showImg: false
        }
      },
      sendImgages: [],
      launchOptions: uni.getLaunchOptionsSync()
    };
  },
  computed: {
    ...mapState('user', {
      isLogin: state => state.isLogin
    })
  },
  mounted: function () {
    uni.onKeyboardHeightChange(res => {
      this.KeyboardHeight = res.height;
      // console.log('res',res)
    })

    // 表情尺寸
    const systemInfo = uni.getSystemInfoSync();
    var radio = 750 / systemInfo.windowWidth;
    this.lineHeight = 50 / radio;
  },
  methods: {
    /**
     * 跳转到登录页
     * @return Boolean 是否跳转到登录页
     */
    navtoGoLogin() {
      if (this.isLogin) return false
      this.$navto.push('Login', {
        formPage: this.$Route.name,
        formPageParams: encodeURIComponent(
          JSON.stringify(this.$Route.query)
        )
      })
      return true
    },
    // 图片失效
    handleError(item) {
      item.headPath = this.file_ctx + 'static/image/system/avatar/icon-doctor-head.png'
    },

    // 表情业务逻辑交互
    _cancelEvent(e) {
      // console.log('你点击了取消');
      this.isShown = false;
    },

    _confirmEvent(e) {
      // console.log('你点击了确定');
      this.isShown = false;
    },
    showDialog() {
      // console.log('执行了')
      this.isShown = true;
    },
    startDrop(e) {
      // var count = e.target.dataset.count;
      if (!this.dropHeight) {
        // this.setData({
        // 	dropHeight: true,
        // })
        this.dropHeight = true;
      }
    },
    Comment(e) {
      // console.log(e)
      var type = e.target.dataset.type;
      var isComment = false
      if (type == 'open') {
        isComment = true
      }
      this.isComment = isComment;
      this.dropHeight = false;
    },
    // // 发送评论
    // sendPL(){
    //   console.log('点击了评论')
    // },
    // 评论
    onkeyboardHeightChange(e) {
      const {
        height
      } = e.detail
      this.keyboardHeight = height;
    },

    hideAllPanel() {
      this.functionShow = false;
      this.emojiShow = false;
    },
    showEmoji() {
      this.focus = false;
      this.functionShow = false;
      this.emojiShow = this._keyboardShow || !this.emojiShow
    },
    showFunction() {
      this.functionShow = this._keyboardShow || !this.functionShow;
      this.emojiShow = false;
    },
    chooseImage() { },
    onFocus() {
      this._keyboardShow = true;

      this.hideAllPanel()
    },
    onBlur(e) {
      this._keyboardShow = false
      this.cursor = e.detail.cursor || 0
    },
    onInput(e) {
      const value = e.detail.value
      // this.comment = value
      // this.cursor = value.length
    },
    onConfirm() {
      this.onsend()
    },
    insertEmoji(evt) {
      const emotionName = evt.emotionName
      let {
        cursor,
        comment
      } = this;
      comment = this.commentReq.content || ''
      const newComment =
        comment.slice(0, cursor) + emotionName + comment.slice(cursor)

      // this.comment = newComment;
      this.commentReq.content = newComment;

      this.cursor = cursor + emotionName.length
    },
    onsend() {
      // const comment = this.comment;
      const comment = this.commentReq.content
      // console.log(comment);
      // const parsedComment = this.parseEmoji(this.comment);
      let parsedComment = this.$refs.emojiRef.parseEmoji(this.commentReq.content)
      // console.log(parsedComment)
      this.CommentArr.push({
        avator: '/image/bg.jpg',
        parsedComment: parsedComment
      })
    },
    deleteEmoji: function () {
      const pos = this.data.cursor
      const comment = this.data.comment
      let result = '',
        cursor = 0

      let emojiLen = 6
      let startPos = pos - emojiLen
      if (startPos < 0) {
        startPos = 0
        emojiLen = pos
      }
      const str = comment.slice(startPos, pos)
      const matchs = str.match(/\[([\u4e00-\u9fa5\w]+)\]$/g)
      // 删除表情
      if (matchs) {
        const rawName = matchs[0]
        const left = emojiLen - rawName.length
        if (this.emojiNames.indexOf(rawName) >= 0) {
          const replace = str.replace(rawName, '')
          result = comment.slice(0, startPos) + replace + comment.slice(pos)
          cursor = startPos + left
        }
        // 删除字符
      } else {
        let endPos = pos - 1
        if (endPos < 0) endPos = 0
        const prefix = comment.slice(0, endPos)
        const suffix = comment.slice(pos)
        result = prefix + suffix
        cursor = endPos
      }
      this.commentReq.content = result;
      // this.comment = result;
      this.cursor = cursor;

    },

    // ----------------------
    userClick(data) {
      const accountId = data.accountId
      const id = data.userId
      // console.log(data, 'data6666')
      if (data.businessType == 10 && id) {
        this.navtoGo('DoctorDetail', { id })
      } else if (data.businessType == 10 && !id) {
        this.navtoGo('PersonalHomePage', { homePageAccountId: "807651524714201090" })
      } else if (this.$common.getKeyVal('user', 'accountId') === accountId) {
        // 是否是自己
        this.$navto.pushTab('Personal', {})
      } else {
        this.navtoGo('PersonalHomePage', { homePageAccountId:accountId,isShowBtn:this.isShowBtn })
      }
    },
    navtoGo(url, obj = {}) {
      this.$navto.push(url, obj)
    },
    clickSendImg() {
      this.$refs.sendImgRef.uploadImage()
    },
    sendImg(list) {
      this.sendImgages = list
      // this.$emit('comment', {target: this.replyTarget, imagePath: list, type: 2})
    },
    // 预览图片
    previewImage(list, index) {
      uni.previewImage({
        current: index,
        urls: list.map(item => this.file_ctx + item)
      })
    },
    handleRelease() {
      this.$emit('comment', { target: this.replyTarget, content: this.commentReq.content, imagePath: this.sendImgages })
    },
    changeAnonymous(e) {
      this.isAnonymous = e.detail.value.includes('anonymous')
    },
    openComment(item) {
      item.pageCurrent += 1 
      let list = item.children.reverse()
      const lastItem = list.find(item => !item.isAddCm)
      const params = {
        lastMsgId: (this.$validate.isNull(item.children) || this.$validate.isNull(lastItem)) ? '' : lastItem.id,
        level: 2,
        ownerCommentId: item.id,
        pageSize: this.pageSize,
        pageCurrent: item.pageCurrent,
      }
      this.$emit('loadMore', params, arguments[0])
    },
    // 初始化评论
    init(cmData) {
      // 表情包内容替换
      for (var i in cmData.comment) {
        if (!cmData.comment[i].parsedComment) {
          cmData.comment[i].parsedComment = this.$refs.emojiRef.parseEmoji(cmData.comment[i].content)
          // cmData.comment[i].parsedinit = true;
        }
        if (cmData.comment[i].children.length > 0) {
          for (let j = 0; j < cmData.comment[i].children.length; j++) {
            if (!cmData.comment[i].children[j].parsedComment) {
              cmData.comment[i].children[j].parsedComment = this.$refs.emojiRef.parseEmoji(cmData.comment[i].children[j].content)
            }
          }
        }
      }
      this.commentData = cmData;
      this.commentData.comment.forEach(item=>{
        if(!item.pageCurrent){
          item.pageCurrent = 0
        } 
      })
    },
    // 没用的方法，但不要删
    stopPrevent() { },
    // 回复评论
    reply(pUser, reUser, pId, e) {
      if (this.navtoGoLogin()) return
      this.$emit('replyBefore')
      this.replyTarget = e
      this.pUser = pUser;
      this.commentReq.pId = pId;
      if (reUser) {
        // this.commentReq.content = '@' + reUser + ' ';
        this.placeholder = '回复' + reUser
      } else {
        // this.commentReq.content = '';
        this.placeholder = '回复楼主'
      }
      this.showTag = true;
      this.commentInput();
    },
    // 删除评论前确认
    confirmDelete(item) {
      var that = this;
      uni.showModal({
        title: '警告',
        content: that.deleteTip,
        confirmText: '确认删除',
        success: function (res) {
          if (res.confirm) {
            that.$emit('del', item);
          }
        }
      });
    },
    // 新增评论
    add() {
      if (this.commentReq.content == null || this.commentReq.content.length < 2) {
        uni.showToast({
          title: '评论内容过短',
          duration: 2000
        });
        return
      }
      this.$emit('add', this.commentReq);
    },
    // 点赞评论
    like(item) {
      this.$emit('like', item);
    },
    // 新增完成
    addComplete() {
      this.commentReq.content = null;
      this.tagClose();
      this.closeInput();
    },
    // 点赞完成-本地修改点赞结果
    likeComplete(commentId) {
      for (var i in this.commentData.comment) {
        if (this.commentData.comment[i].id == commentId) {
          this.commentData.comment[i].hasLike ? this.commentData.comment[i].likeNum-- : this.commentData
            .comment[i].likeNum++;
          this.commentData.comment[i].hasLike = !this.commentData.comment[i].hasLike;
          return
        }
        for (var j in this.commentData.comment[i].children) {
          if (this.commentData.comment[i].children[j].id == commentId) {
            this.commentData.comment[i].children[j].hasLike ? this.commentData.comment[i].children[j]
              .likeNum-- : this.commentData.comment[i].children[j].likeNum++;
            this.commentData.comment[i].children[j].hasLike = !this.commentData.comment[i].children[j]
              .hasLike;
            return
          }
        }
      }
    },
    // 删除完成-本地删除评论
    deleteComplete(commentId) {
      for (var i in this.commentData.comment) {
        for (var j in this.commentData.comment[i].children) {
          if (this.commentData.comment[i].children[j].id == commentId) {
            this.commentData.comment[i].children.splice(Number(j), 1);
            return
          }
        }
        if (this.commentData.comment[i].id == commentId) {
          this.commentData.comment.splice(Number(i), 1);
          return
        }
      }
    },
    // 展开评论
    showMore(commentId) {
      for (var i in this.commentData.comment) {
        if (this.commentData.comment[i].id == commentId) {
          this.commentData.comment[i].hasShowMore = !this.commentData.comment[i].hasShowMore;
          this.$forceUpdate();
          return
        }
        for (var j in this.commentData.comment[i].children) {
          if (this.commentData.comment[i].children[j].id == commentId) {
            this.commentData.comment[i].children[j].hasShowMore = !this.commentData.comment[i].children[j]
              .hasShowMore;
            this.$forceUpdate();
            return
          }
        }
      }
    },
    // 输入框失去焦点
    blur(e) {
      this.focus = false;
      this.onBlur(e)
    },
    // 输入框聚焦
    focusOn() {
      // 表情包焦点偏移
      this.onFocus()
      this.$emit('focusOn');
    },
    // 标签关闭
    tagClose() {
      this.showTag = false;
      this.pUser = null;
      this.commentReq.pId = null;
    },
    // 输入评论
    commentInput() {
      if (this.launchOptions.scene === 1154) return this.$uniPlugin.toast('请前往小程序使用完整服务')
      // TODO 调起键盘方法
      this.submit = true;
      setTimeout(() => {
        this.focus = true;
      }, 50)
    },
    // 关闭输入评论
    closeInput() {
      // this.clearInput()
      this.focus = false;
      this.submit = false;
    },
    // 清空输入框
    clearInput() {
      this.commentReq = {
        "pId": null, // 评论父id
        "content": null // 评论内容
      }
      this.sendImgages = []
    },
    addComment() {
      if (this.launchOptions.scene === 1154) return this.$uniPlugin.toast('请前往小程序使用完整服务')
      if (this.navtoGoLogin()) return
      this.$emit('replyBefore')
      this.placeholder = ''
      this.replyTarget = null
      this.clearInput()
      this.commentInput()
    }
  },
  destroyed() {
    uni.offKeyboardHeightChange()
  },
  filters: {
    getTimeStringAutoShort2(timestamp) {
      // return timestamp ? common.formatDate(new Date(timestamp), 'yyyy-MM-dd HH:mm').replace(/-/g, '/') : ''
      return timestamp ? timePlugin.formatDate(new Date().valueOf(), timestamp) : ''
    }
  }
};
</script>

<style lang="scss" scoped>
.tip {
  font-size: 22rpx;
  color: #868C9C;
  line-height: 32rpx;
  padding-left: 12rpx;
}
.d-t {
  display: flex;
  align-items: center;
}
// 表情包辅助CSS

@import "@/modules/community/components/nui-emoji/index.css";
.emoji {
  // width: 50rpx;
  // height: 50rpx;
  width: 20px;
  height: 20px;
  margin-left: 20rpx;
}
.hb-comment {
  padding: 10rpx;
}

.top-read {
  font-size: 28rpx;
  padding-left: 10rpx;
  color: #999999;
}

.seg_line_box {
  display: flex;
  height: 5rpx;
  justify-content: space-between;
  margin: 5rpx 0;
}

.seg_line {
  width: 45%;
  border-bottom: 1rpx solid #e1e1e1;
}

.seg_dot {
  width: 8%;
  border-bottom: 5rpx dotted #dbdbdb;
}

.comment-title {
  font-weight: 500;
  font-size: 36rpx;
  color: #1d2029;
  line-height: 50rpx;
}

.comment-num {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.comment-box {
  padding-top: 64rpx;
}

.comment-box-item {
  display: flex;
}

.comment-main {
  flex: 1;
  width: 0;
  padding-left: 16rpx;
  box-sizing: border-box;
}

.comment-main-top {
  width: 100%;
  // padding-top: 6rpx;
  display: flex;
  justify-content: space-between;
}

.comment-sub-item-top {
  display: flex;
  align-items: center;
}

.avatar {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
}

.nick-name-box {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  .nick-hospitcal {
    font-size: 22rpx;
    color: #767680;
    span {
      font-weight: 700;
    }
  }
}

.comLogo {
  margin-right: 18rpx;
  font-size: 22rpx;
  border-radius: 10rpx;
  padding: 5rpx 15rpx;
  color: #ffffff;
}

.com1 {
  background-color: #d218b1;
}

.com2 {
  background-color: #f19c0b;
}

.com3 {
  background-color: #c8da85;
}

.com4 {
  background-color: #bfd0da;
}

.nick-name {
  flex: 1;
  font-weight: 400;
  font-size: 28rpx;
  color: #4E5569;
  line-height: 40rpx;
}

.like-text {
  font-size: 26rpx;
  color: #4E5569;
  line-height: 36rpx;
  padding-left: 8rpx;
}

.comment-main-content {
  padding: 10rpx 10rpx 10rpx 0;
}

.comment-main-foot {
  display: flex;
  align-items: center;
  font-size: 22rpx;
}

.replayTag {
  color: #909399;
  margin-bottom: 5px;
  border: 1px solid #c8c9cc;
  background-color: #f4f4f5;
  border-radius: 3px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16rpx;
  padding: 5px 10px;
}

.replyTagClose {
  font-size: 20px;
  line-height: 12px;
  padding: 0 0 2px 5px;
}

.foot-btn {
  padding-left: 10rpx;
  font-size: 26rpx;
  color: #4E5569;
  line-height: 36rpx;
}

.comment-sub-box {
  width: 100%;
  padding: 24rpx;
  background: #F8F8FA;
  border-radius: 16rpx;
  box-sizing: border-box;
  margin-top: 24rpx;
}

.comment-sub-item {
  width: 100%;
  display: flex;
  flex-direction: column;
  .avatar {
    width: 48rpx;
    height: 48rpx;
    border-radius: 50%;
  }
  & + .comment-sub-item {
    padding-top: 32rpx;
  }
  .comment-main {
    flex: auto;
    width: 100%;
    padding: 0;
  }
}

.comment-none {
  text-align: center;
  padding-top: 144rpx;
}
.comment-empty-img {
  display: block;
  margin: 0 auto;
  width: 286rpx;
  height: 234rpx;
  margin-bottom: 24rpx;
}
.comment-empty-text {
  font-size: 24rpx;
  color: #4E5569;
  line-height: 34rpx;
}

.comment-submit-box {
  position: fixed;
  display: flex;
  align-items: flex-end;
  z-index: 9900;
  left: 0;
  top: var(--window-top);
  bottom: 0;
  background-color: rgba($color: #000000, $alpha: 0.5);
  width: 100%;
}

.comment-add {
  position: absolute;
  background-color: #ffffff;
  width: 100%;
  border: 1px solid #ddd;
  transition: 0.3s;
  -webkit-transition: 0.3s;
  padding: 24rpx;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  box-sizing: border-box;
  &-bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12rpx 0;
    &-right {
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }
  .d-t {
    display: flex;
    align-items: center;
  }
  .select-icon {
    width: 36rpx;
    height: 36rpx;
    &+.select-icon {
      margin-left: 48rpx;
    }
  }
}

.btn-click {
  color: #007aff;
  font-size: 28rpx;
  padding: 10rpx;
}

.cancel {
  color: #606266;
}

.textarea {
  height: 208rpx;
  padding: 24rpx;
  width: 100%;
  background: #F0F2F7;
  border-radius: 16rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #1D2029;
  line-height: 40rpx;
}

.comment-submit {
  padding: 5rpx 20rpx 0 20rpx;
  border-bottom: 1px dashed #ddd;
  width: calc(100% - 40rpx);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.comment-add-btn {
  position: static;
  font-weight: 500;
  font-size: 28rpx;
  color: #00b484;
  line-height: 40rpx;
  color: $topicC;
}
.more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 24rpx;
  &-text {
    font-size: 24rpx;
    font-weight: 400;
    color: $topicC;
    line-height: 34rpx;
  }
}
.comment-sub-more {
  display: flex;
  align-items: center;
  padding-top: 12rpx;
  &-text {
    font-size: 24rpx;
    font-weight: 400;
    color: #8b8b8b;
    line-height: 34rpx;
  }
}
.comment-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 12rpx;
  &-text {
    font-size: 24rpx;
    font-weight: 400;
    color: #8b8b8b;
    line-height: 34rpx;
  }
}
.mutual-comment-box {
  flex: 1;
  display: flex;
  align-items: center;
  padding-left: 12rpx;
  .reply-text {
    width: 0;
    height: 0;
    border-top: 8rpx solid transparent;
    border-left: 14rpx solid #8b8b8b;
    border-bottom: 8rpx solid transparent;
    margin: 0 24rpx;
  }
  .nick-name {
    font-size: 26rpx;
    color: #4E5569;
    line-height: 36rpx;
  }
}
.foot-source {
  font-size: 26rpx;
  color: #8b8b8b;
  span {
    background-color: #ecf5ff;
    display: inline-block;
    height: 40rpx;
    line-height: 40rpx;
    border-radius: 10rpx;
    color: #409eff;
    border: 1px solid #d9ecff;
    white-space: nowrap;
    padding: 0 10rpx;
    margin-left: 5rpx;
  }
}
.foot-time {
  font-size: 24rpx;
  color: #868C9C;
  line-height: 34rpx;
}
.content-img {
  width: 200rpx;
  height: 100%;
}
.release-btn {
  height: 60rpx;
  background-color: #00B484;
  border-radius: 30rpx;
  font-weight: 500;
  font-size: 28rpx;
  color: #FFFFFF;
  line-height: 60rpx;
  padding: 0 24rpx;
}
.nick-name-width-1 {
  display: inline-block;
  vertical-align: middle;
  @include ellipsis(1);
}
.nick-name-width-2 {
  display: inline-block;
  vertical-align: middle;
  @include ellipsis(1);
}
.zan-box {
  padding-left: 32rpx;
}
.like-icon {
  width: 26rpx;
  height: 26rpx;
}
.send-img-box {
  width: 116rpx;
  height: 116rpx;
  background-size: cover;
  background-position: center center;
  display: inline-block;
  position: relative;
  .send-img {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
  .send-img-remove {
    position: absolute;
    top: 0rpx;
    right: 0rpx;
    @include iconImg(40, 40, "/business/icon-close-black-circle.png");
    margin: 0;
    z-index: 2;
  }
}
.flex-align-center-box {
  display: flex;
  align-items: center;
}
</style>
