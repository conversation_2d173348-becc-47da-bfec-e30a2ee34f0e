<template>
	<view>
		<view class="statusBar" :style="{'height':statusHeight + 'px','background-color':headerobj.headBgColor}">
		</view>
		<view class="header mobile-item" :style="{height:titleHeight + 'px',top:statusHeight + 'px','position': position}">
			<view class="hx-view">
				<view class="customNavigation" :style="{'background-color':headerobj.headBgColor,height:titleHeight + 'px'
					,color:headerobj.contentColor}">

					<view class="customeNavbg hx-search-box" :class="[headerobj.alignL ? 'left' : 'center']"
						v-if="headerobj.currentIndex == 0">

						<view class="tx-center items">
							<block v-if="headerobj.titleType == 'txt'">
								{{ headerobj.titleTxt }}
							</block>
							<image style="height: 60rpx" mode="heightFix" :src="headerobj.titleImg" alt="" v-else />
						</view>
					</view>
					<block v-if="headerobj.currentIndex == 3">
						<slot></slot>
					</block>
					<view class="back-up" @click="preStep" v-if="isBack"></view>

				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "ly-header",
		props: {
			headerobj: {
				type: Object,
				default: function (){
					return {}
				},
			},
      position:{
        type:String,
        // position
        default:"fixed"
      }
		},
		data() {
			return {
				isBack: false,
				statusHeight: 0,
				titleHeight:0,
				allheights:0,
				// 没有返回页面路径
        nopages:[],
				// nopages:['pages/home/<USER>','pages/index/index','pages/category/index','pages/customize/index','pages/tabulation/index'],

			};
		},
		watch: {
		},
		mounted() {
			var pages = getCurrentPages()

			if(this.nopages.indexOf(pages[pages.length - 1].__route__) == -1){
				this.isBack = true;
			}

			// // 在组件实例进入页面节点树时执行
			// 初始化头部导航栏
			this.initHeader();
		},
		methods: {
			setData(obj) {


			},
			// 初始化头部导航栏
			initHeader() {
				let systemInfo = uni.getSystemInfoSync();
				let statusHeight = systemInfo.statusBarHeight; //状态栏的高度
				let titleHeight = 45; //导航栏高度，这个一般是固定的
				let allheights = systemInfo.windowHeight;
				// console.log((systemInfo))
				this.statusHeight = statusHeight;
				this.titleHeight = titleHeight;
				this.allheights = allheights;


			},
			preStep() {
        
        this.$emit('pre')
				// uni.navigateBack({
				// 	delta: 1
				// })
			}
		}
	}
</script>

<style lang="scss">
	.statusBar {
		position: fixed;
		top: 0;
		width: 100%;
		z-index: 99999
	}

	.header {
		// position: fixed;
		top: 0;
		height: 128rpx;
		z-index: 99999;
    
	}

	.back-up {
		// background: url('../../../../../static/image/directseeding/images/backIcon.png') no-repeat;
    background-image: url($imgUrl + '/business/hulu-v2/backIcon.png');
    background-repeat: no-repeat;
		background-size: 100% 100%;
		height: 60rpx;
		width: 60rpx;
		position: absolute;
		/* top: calc(50% - 14rpx); */
		top: 8rpx;
		left: 12rpx;
	}

	.header .customNavigation {
		height: 128rpx;
		/* background-image: url("http://hxui.loca.hx110.com/template/topNavBlack.png"); */
		// background-color: #ffffff;
		/* color: #000000; */
	}

	.header .customNavigation .customeNavbg {
		position: absolute;
		/* bottom: 10rpx; */
		height: 45px;
		line-height: 45px;
		padding: 0 20rpx;

	}

	.customeNavbg.center {
		left: 50%;
		transform: translateX(-50%);
	}

	.customeNavbg.left {
		left: 50rpx;

	}

	.header .customNavigation .hx-search-box {
		line-height: 40px;
	}

	.header .customNavigation .hx-search-item2 {
		position: absolute;
		bottom: 8px;
		/* bottom: 20rpx; */
		height: 60rpx;
		line-height: 60rpx;
		padding: 0 50rpx;
		/* padding-left: 50rpx; */

		width: 100%;
		padding-right: 205rpx;
		box-sizing: border-box;
	}

	.header .customNavigation .hx-search-item2 .items {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.header .customNavigation .tx-center {
		-webkit-box-flex: initial;
		-ms-flex: initial;
		flex: initial;
		-webkit-box-pack: center;
		-ms-flex-pack: center;
		justify-content: center;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		max-width: 400rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	// .header .customNavigation .tx-center p,
	// .header .customNavigation .tx-center span {
	// 	width: 320rpx;
	// 	font-size: 32rpx;
	// 	font-family: PingFangSC-Semibold, PingFang SC;
	// 	font-weight: 600;
	// 	text-align: center;
	// 	display: block;
	// 	overflow: hidden;
	// 	white-space: nowrap;
	// 	text-overflow: ellipsis;
	// }

	// .header .customNavigation .tx-center span {
	// 	width: 188rpx;
	// }

	.hx-view {
		position: relative;
		/* zoom: 0.5; */
		width: 750rpx;
	}

	.hx-search-box .hx-search {
		padding: 20rpx;
		position: relative;
		width: 100%;
	}

	.hx-search-box .search-ico {
		font-size: 40rpx;
		position: absolute;
		left: 35rpx;
		top: 50%;
		transform: translateY(-50%);
	}

	.hx-search-box .hx-search-input {
		height: 60rpx;
		flex: 1;
		border-radius: 40rpx;
		padding-left: 70rpx;
		padding-right: 70rpx;
		box-sizing: border-box;
		font-size: 24rpx;
		line-height: 60rpx;
		color: #c5c5c5;
		border: 3rpx solid #ecebef;
	}

	.hx-search-box .hx-search-ico {
		width: 70rpx;
		/* background: red; */
		height: 70rpx;
	}

	.hx-search-box .mgl20 {
		margin-left: 20rpx;
	}

	.hx-search-box .mgr20 {
		margin-right: 20rpx;
	}

	.hx-search-box .l_ico_b {
		position: relative;
	}

	.hx-search-box .square {
		padding: 5rpx;
		background-color: red;
		color: #fff;
		position: absolute;
		right: -10rpx;
		top: -8rpx;
		border-radius: 50%;
	}

	// .hx-search-box input {
	// 	outline: none;
	// }

	.hx-search-box .hx-dc {
		border: none;
	}

	.d-flex {
		display: flex;
		align-items: center;
	}

	.header .customNavigation .hx-search-left-ico {
		position: absolute;
		bottom: 10rpx;
		height: 60rpx;
		left: 20rpx;
	}

	// .header .customNavigation .hx-search-left-ico i {
	// 	font-size: 30rpx;
	// 	margin-right: 20rpx;
	// 	font-weight: 700;
	// }

	@font-face {
		font-family: "iconfont";
		src: url('//at.alicdn.com/t/font_2385527_jeivs6vjcv.eot?t=1616464836777');
		/* IE9 */
		src: url('//at.alicdn.com/t/font_2385527_jeivs6vjcv.eot?t=1616464836777#iefix') format('embedded-opentype'),
			/* IE6-IE8 */
			url('data:application/x-font-woff2;charset=utf-8;base64,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') format('woff2'), url('//at.alicdn.com/t/font_2385527_jeivs6vjcv.woff?t=1616464836777') format('woff'), url('//at.alicdn.com/t/font_2385527_jeivs6vjcv.ttf?t=1616464836777') format('truetype'),
			/* chrome, firefox, opera, Safari, Android, iOS 4.2+ */
			url('//at.alicdn.com/t/font_2385527_jeivs6vjcv.svg?t=1616464836777#iconfont') format('svg');
		/* iOS 4.1- */
	}

	.iconfont {
		font-family: "iconfont" !important;
		font-size: 16rpx;
		font-style: normal;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
	}

	.icon-sousuo:before {
		content: "\e62b";
	}

	.icon-gouwu:before {
		content: "\e603";
	}

	.icon-gouwuche:before {
		content: "\e656";
	}
</style>
