import common from '@/common/util/main'
import api from '@/service/api'
import store from '@/store'
import wechatPlugin from '@/common/util/wechat-plugin'
import uniPlugin from '@/common/util/uni-plugin'
import ext from '@/service/ext'
import env from '@/config/env'
import validate from '@/common/util/validate'

/**
 * 针对获取 支付宝 进行二次数据清洗、加工、转化，特别指API
 */
export default {
    getPhone(params) {
        uniPlugin.loading('登录中')
        return new Promise((resolve, reject) => {
          params.appId = env.appId
            api.alipay.getPhoneV3(params).then(res => {
                uniPlugin.hideLoading()

                resolve(res)
            }).catch(err => {
                uniPlugin.hideLoading()

                reject(err)
            })
        })
    }
}
