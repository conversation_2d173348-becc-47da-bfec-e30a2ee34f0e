import common from '@/common/util/main';
{
  subject:`api接口中类型有两种 Before以及After Before 时parametersComparison项里接受的参数是
          全量参数 也就是包括请求前的请求参数以及请求后的结果参数 After或者不填时只有请求后的参数
          route接口中类型中 如果传递了需要重写的方法 就会传递当前页面的this和该方法的传参以arguments的方式
          `
}
const PageMap = {"/pages/index/index":'首页',"/pages/circle-home/index":'交流'};
// 获取参数方法
export function getPOSTArgument (data){
  return typeof data.data === 'string' ? JSON.parse(data.data) : data.data;
}
export function getGETArgument (data){
  return data.params
}
// 获取页面标题
export function getPageTitle(){
  let pages = getCurrentPages()
  let current = pages[pages.length - 1]; // 获取到当前页面的信息
  let pageInfo = __wxConfig.page && __wxConfig.page[`${current.route}.html`] || ''; // 内部属性
  // console.log('调用获取页面名称',pageInfo.window.navigationBarTitleText);
  return pageInfo?.window?.navigationBarTitleText || '';
}
// 获取上一个页面的路由
export function getPreviousRoute(index = 3){
  let pages = getCurrentPages();
  if(pages.length < 2) return '';//不存在上一页;
  return pages[pages.length - index].$page.fullPath;
}
// 定义埋点任务映射表
export const stratagemAPIsOrRouterMaps = [
  // 内容曝光
  {
    type:'route', //类型
    shareTask:true,
    path:'modules/community/posts/detail/index', //页面路由如果是api类型则为api路径
    apiPath:'postmessage/query/one', //进入页面后需要监听的api路径
    eventName:'ContentExposure', // 事件名称
    parametersComparison:{
      content_id:subject=>subject.id, //内容ID
      content_author:subject=>subject.nickName, //内容作者
      content_author_id:subject=>common.getKeyVal('user', 'curSelectUserInfo', true).centerUserId, //内容作者ID
      content_name:subject=>subject.title, //内容名称
      content_belong_circle:subject=>subject.circleClassifyName, //内容所属圈子
      code_name:subject=>subject.lableIds //内容所属标签
    },
    parametersMap:null, //参数接受
    timeNum:1.5
  },
  // 帖子内容浏览or帖子内容浏览退出页面
  {
    type:'route', //类型
    shareTask:true,
    isExit:true, //判断是否要兼容页面退出
    path:'modules/community/posts/detail/index', //页面路由如果是api类型则为api路径
    apiPath:'postmessage/query/one', //进入页面后需要监听的api路径
    eventName(type){
      return this.enterType === 'onLoad' ? 'ContentDetailView' : 'EndContentView'
    }, // 事件名称
    parametersComparison:{
      content_id:subject=>subject.id, //内容ID
      content_author:subject=>subject.nickName, //内容作者
      content_author_id:subject=>common.getKeyVal('user', 'curSelectUserInfo', true).centerUserId, //内容作者ID
      content_name:subject=>subject.title, //内容名称
      content_belong_circle:subject=>subject.circleClassifyName, //内容所属圈子
      code_name:subject=>subject.lableIds, //内容所属标签
      duration:true //时长
    },
    parametersMap:null, //参数接受
    timeNum:0,
    enterType:''
  },
  // 内容搜索
  {
    type:'api', //类型
    apiType:'POST',
    shareTask:true,
    isBeforeRequest:true, // 判断是否是请求前埋点
    path:'postmessage/search', //页面路由如果是api类型则为api路径
    eventName:'SearchRequest', // 事件名称
    getArgument:getPOSTArgument, //获取参数方法
    parametersComparison:{
      page_name:getPageTitle, //页面标题
      search_keyword:subject=>subject.condition.keyword, //搜索关键词
      keyword_type:subject=>{
        return subject.condition.searchFlag ? '自定义' : '历史词';
      }, //关键词类型
      previous_page:subject=>PageMap[getPreviousRoute()]//向前页面
    },
    isBeforeParametersMap:null, //请求前参数接受
    parametersMap:null, //上次请求结果参数接受
    timeNum:0
  },
  // 内容搜索
  {
    type:'route', //类型
    path:'modules/common/system-search/index', //页面路由如果是api类型则为api路径
    getOverriddenMethod:subject=>[subject.$refs.hotListRef,'clickItem'], //获取重写方法的父对象以及重写方法的方法名
    eventName:'SearchRequest', // 事件名称
    parametersComparison:{
      page_name:getPageTitle, //页面标题
      search_keyword:subject=>subject.arguments[0].title, //搜索关键词
      keyword_type:subject=>'搜索发现', //关键词类型
      previous_page:subject=>PageMap[getPreviousRoute(2)]//向前页面
    },
    parametersMap:null, //参数接受
    timeNum:0
  },
  // 内容搜索or获取搜索结果
  {
    type:'api', //类型
    apiType:'POST',
    shareTask:true,
    isAfterRequest:true, // 判断是否是请求后埋点
    path:'postmessage/search', //页面路由如果是api类型则为api路径
    eventName:'GetSearchResult', // 事件名称
    getArgument:getPOSTArgument, //获取参数方法
    //类型有两种 Before以及After Before 时parametersComparison项里接受的参数是
    //全量参数 也就是包括请求前的请求参数以及请求后的结果参数 After或者不填时只有请求后的参数
    getArgumentType:'Before',
    parametersComparison:{
      previous_page:subject=>PageMap[getPreviousRoute(3)],//向前页面
      page_name:getPageTitle, //页面标题
      search_keyword:({config})=>getPOSTArgument(config).condition.keyword, //搜索关键词
      keyword_type:({config})=>getPOSTArgument(config).condition.searchFlag ? '自定义' : '历史词', //关键词类型
      result_number:({data})=>data.total //搜索结果数量（总）
    },
    isBeforeParametersMap:null, //请求前参数接受
    parametersMap:null, //上次请求结果参数接受
    timeNum:0
  },
  // 内容搜索
  {
    type:'route', //类型
    path:'modules/common/system-search/index', //页面路由如果是api类型则为api路径
    getOverriddenMethod:subject=>[subject.$refs.hotListRef,'clickItem'], //获取重写方法的父对象以及重写方法的方法名
    eventName:'GetSearchResult', // 事件名称
    parametersComparison:{
      previous_page:subject=>PageMap[getPreviousRoute(2)],//向前页面
      page_name:getPageTitle, //页面标题
      search_keyword:subject=>subject.arguments[0].title, //搜索关键词
      keyword_type:subject=>'搜索发现', //关键词类型
      result_number:({data})=>1 //搜索结果数量（总）
    },
    parametersMap:null, //参数接受
    timeNum:0
  },
  // 帖子内容浏览or帖子内容浏览退出页面
  {
    type:'route', //类型
    path:'modules/common/system-search/search-data', //页面路由如果是api类型则为api路径
    getOverriddenMethod:subject=>[subject.$refs.nuiList,'itemClick'], //获取重写方法的父对象以及重写方法的方法名
    eventName:'ClickSearchResult', // 事件名称
    parametersComparison:{
      previous_page:subject=>PageMap[getPreviousRoute(3)],//向前页面
      page_name:getPageTitle, //页面标题
      search_keyword:subject=>subject.search, //搜索关键词
      keyword_type:subject=>subject.searchFlag ? '自定义' : '历史词', //关键词类型
      searchresult_rank:subject=>subject.indexlist.findIndex(e=>subject.arguments[0].id === e.id) + 1 //搜索结果排序
    },
    parametersMap:null, //参数接受
    timeNum:0
  },
  // 内容搜索
  {
    type:'route', //类型
    path:'modules/common/system-search/index', //页面路由如果是api类型则为api路径
    getOverriddenMethod:subject=>[subject.$refs.hotListRef,'clickItem'], //获取重写方法的父对象以及重写方法的方法名
    eventName:'ClickSearchResult', // 事件名称
    parametersComparison:{
      previous_page:subject=>PageMap[getPreviousRoute(2)],//向前页面
      page_name:getPageTitle, //页面标题
      search_keyword:subject=>subject.arguments[0].title, //搜索关键词
      keyword_type:subject=>'搜索发现', //关键词类型
      searchresult_rank:subject=>subject.$refs.hotListRef.hotList.findIndex(e=>subject.arguments[0].id === e.id) + 1 //搜索结果排序
    },
    parametersMap:null, //参数接受
    timeNum:0
  },
]
