<template>
  <view class='study-statement'>
    <view class="my-data" :style="{'background-image':'url('+ file_ctx +'static/image/business/accompany-doctor/icon-accompany-doctor-course-head.png)','background-size': '100%'}">
      <view :style="'height:' + statusBarHeight + 'px;'"></view>
      <view class="top-nav">
        <view class="top-nav-l" @click.stop="handleBack">
          <image mode="aspectFit" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-left-arrows.png'" class="header-search-img"/>
        </view>
        <view class="top-nav-c">
          <view class="lineHide" @click="zIndexFlag = !zIndexFlag">
            <timePicker ref="selectTime" v-model="timeMap" type="daterange" @change="changeTime"></timePicker>
          </view>
        </view>
      </view>
    </view>
    <view class="study-content" :style="{'background-image':'url('+ file_ctx +'static/image/business/accompany-doctor/icon-accompany-doctor-course-content-bg.png)','background-size': '100%'}">
      <view class="study-head">
        <view class="title"><image :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-doctor-course-study-people.png'"></image></view>
        <view class="num">
          <image :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-doctor-course-num.png'"></image>
          共 {{ count }} 人
        </view>
      </view>
      <view class="study-item-content">
        <template v-if="contentList.length">
          <view class="study-item" v-for="(item,index) in contentList" :key="item.id">
            <view class="study-item-l">
              <view class="item-l-icon" v-if="index == 0"><image :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-doctor-course-one.png'"></image></view>
              <view class="item-l-icon" v-else-if="index == 1"><image :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-doctor-course-two.png'"></image></view>
              <view class="item-l-icon" v-else-if="index == 2"><image :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-doctor-course-three.png'"></image></view>
              <view class="item-l-icon" v-else>{{ index+1 }}</view>
              <view class="item-l-profile"><image :src="file_ctx + item.avatar"></image></view>
              <view class="item-l-text">{{ item.username }}</view>
            </view>
            <!-- <view class="study-item-r"><span>11999</span>时<span>28</span>分</view> -->
            <view class="study-item-r" v-if="item.visitTotal">
              <span v-if="item.visitTotal.hours">{{ item.visitTotal.hours }}时</span>
              <span v-if="item.visitTotal.minutes !== 0">{{ item.visitTotal.minutes }}分</span>
              <span v-else>0</span>
            </view>
          </view>
        </template>
        <view class="empty" v-else>
          <view class="empty-img"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-empty.png'"></image></view>
          <view class="text">暂无数据~</view>
        </view>
      </view>
    </view>
    <view class="study-bottom" v-if="employeeRankObj.employeeId" :style="{zIndex:zIndexFlag ? '-1' : '0'}">
      <view class="study-bottom-item">
        <view class="study-bottom-l">
          <view class="bottom-l-img"><image :src="file_ctx + employeeRankObj.avatar"></image></view>
          <view class="bottom-l-info">
            <view class="info-title">{{ employeeRankObj.username }}</view>
            <view class="info-ranking">我的排名：<span>{{ employeeRankObj.rank }}</span></view>
          </view>
        </view>
        <view class="study-bottom-r" v-if="employeeRankObj.visitTotal">
          <span v-if="employeeRankObj.visitTotal.hours">{{ employeeRankObj.visitTotal.hours }}时</span>
          <span v-if="employeeRankObj.visitTotal.minutes !== 0">{{ employeeRankObj.visitTotal.minutes }}分</span>
          <span v-else>0</span>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import timePicker from '../../components/uni-datetime-picker/uni-datetime-picker'
  export default {
    components: {
      timePicker
    },
    data(){
      return{
        file_ctx:this.file_ctx,
        statusBarHeight: 0,
        timeMap:[],
        providerId:null,
        contentList:[],
        count:0,//陪诊师人数
        employeeRankObj:null,
        userInfo:null,
        zIndexFlag:false,
      }
    },
    async onLoad(){
      const codeUserInfo = this.$common.getKeyVal('user', 'codeUserInfo',true);
      let {data:id} = await this.$api.accompanyDoctor.accompanyproviderUserProvider({userId:codeUserInfo.id})
      this.providerId = id;
      let {data} = await this.$api.accompanyDoctor.getAccompanyemployeeOneByUserId({userId:codeUserInfo.id})
      this.userInfo = data;
      this.timeMap = this.getDatesOfTodayAndLastWeek();
      if(this.timeMap.length){
        this.accompanybookCourseStudyStatistic()
        this.accompanybookCourseEmployeeRank()
      }
      this.accompanyemployeeQueryCount(id)
    },
    mounted(){
      this.$nextTick(() => {
        this.init()
      })
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
    },
    methods:{
      convertSecondsToTimeSimplified(seconds) {
        const hours = Math.floor(seconds / 3600); // 计算小时数
        const minutesInHour = seconds % 3600;     // 计算一小时内的秒数
        const minutes = minutesInHour >= 60 ? Math.floor(minutesInHour / 60) : 0; // 如果秒数大于等于60，则计算分钟数，否则为0
        // 格式化输出
        return {hours,minutes}
      },
      async accompanybookCourseEmployeeRank(){
        let params = {
          startTime:this.timeMap[0]+ ' 00:00:00',
          endTime:this.timeMap[1]+ ' 23:59:59',
          employeeId:this.userInfo.id,
        }
        const res = await this.$api.accompanyDoctor.accompanybookCourseEmployeeRank(params)
        if(res.data !==""){
          this.employeeRankObj = {...res.data,visitTotal:this.convertSecondsToTimeSimplified(res.data.visitTotal)}
        }
      },
      async accompanybookCourseStudyStatistic(){
        let params = {
          startTime:this.timeMap[0]+ ' 00:00:00',
          endTime:this.timeMap[1]+ ' 23:59:59',
          providerId:this.providerId,
        }
        const res = await this.$api.accompanyDoctor.accompanybookCourseStudyStatistic(params)
        if(res.data !== ""){
          this.contentList = res.data.map(item=>({...item,visitTotal:this.convertSecondsToTimeSimplified(item.visitTotal)}))
        }
        console.log(res,'res0101010')
      },
      async accompanyemployeeQueryCount(providerId){
        const res = await this.$api.accompanyDoctor.accompanyemployeeQueryCount({providerId})
        if(res.data !== ""){
          this.count = res.data
        }
      },
      changeTime(){
        console.log('点击了')
        this.accompanybookCourseStudyStatistic()
        this.accompanyemployeeQueryCount(this.providerId)
      },
      // getDatesOfTodayAndLastWeek() {
      //   const dates = [];
      //   const today = new Date();
      //   const lastMonth = new Date(today);
      //   lastMonth.setMonth(lastMonth.getMonth() - 1);

      //   if (lastMonth.getMonth() === 11 && today.getMonth() === 0) {
      //     lastMonth.setFullYear(lastMonth.getFullYear() - 1);
      //   }

      //   lastMonth.setDate(1);
      //   dates.push(this.$common.formatDate(lastMonth, 'yyyy-MM-dd'))
      //   dates.push(this.$common.formatDate(today, 'yyyy-MM-dd'))
      //   return dates;
      // },
      formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      },
      getDatesOfTodayAndLastWeek() {
        const dates = [];
        const today = new Date();
        // 获取一周前的今天
        const oneWeekAgo = new Date(today);
        oneWeekAgo.setDate(today.getDate() - 7);
        dates.push(this.formatDate(oneWeekAgo));
        // 获取今天的日期
        dates.push(this.formatDate(today));
        return dates;
      },
      handleBack(){
        this.$navto.back(1)
      },
    },
 }
</script>

<style lang='scss' scoped>
.img{
  width: 100%;
  height: 100%;
}
.study-statement{
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  background-color: #F1F9F7;
  .my-data{
    position: fixed;
    top: 0;
    height: 176rpx;
    width: 100%;
    .top-nav{
      width: calc(100% - 16rpx);
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 40px;
      line-height: 40px;
      padding: 0 16rpx;
      .top-nav-l{
        display: flex;
        width: 48rpx;
        height: 48rpx;
        image{
          width: 100%;
          height: 100%;
        }
      }
      .top-nav-c{
        display: flex;
        flex: 1;
        align-items: center;
        height: 40rpx;
        font-weight: 500;
        font-size: 32rpx;
        color: #2D2F38;
        line-height: 40rpx;
        .lineHide{
          position: relative;
          .my-icons{
            position: absolute;
            top: 8rpx;
            right: 268rpx;
          }
          ::v-deep.uni-date{
            width: 400rpx;
            .uni-date-editor{
              .uni-date-editor--x{
                height: 64rpx;
                border-radius: 32rpx;
                background: #fff;
                overflow: hidden;
                .uni-date-x{
                  flex: none;
                  padding-left: 32rpx;
                  .range-separator{
                    margin: 0 10rpx;
                  }
                  .uni-date__x-input{
                    flex: none;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  .study-content{
    height: calc(100vh - 176rpx);
    width: 100%;
    padding: 0 24rpx;
    box-sizing: border-box;
    margin-top: 176rpx;
    overflow-y: auto;
    .study-head{
      padding: 32rpx 36rpx 38rpx;
      .title{
        display: flex;
        width: 266rpx;
        height: 72rpx;
        margin-bottom: 8rpx;
        image{
          width: 100%;
          height: 100%;
        }
      }
      .num{
        display: flex;
        align-items: center;
        font-size: 28rpx;
        color: #FFFFFF;
        line-height: 40rpx;
        image{
          width: 28rpx;
          height: 28rpx;
          margin-right: 6rpx;
        }
      }
    }
    .study-item-content{
      background-color: #fff;
      border-radius: 16rpx;
      padding: 30rpx 32rpx 24rpx 30rpx;
      height: 100%;
      overflow-y: auto;
      .study-item{
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 32rpx;
        .study-item-l{
          display: flex;
          align-items: center;
          .item-l-icon{
            display: flex;
            justify-content: center;
            width: 48rpx;
            height: 48rpx;
            image{
              width: 100%;
              height: 100%;
            }
          }
          .item-l-profile{
            display: flex;
            width: 80rpx;
            height: 80rpx;
            margin: 0 24rpx 0 22rpx;
            border-radius: 50%;
            overflow: hidden;
            image{
              width: 100%;
              height: 100%;
            }
          }
          .item-l-text{
            font-size: 28rpx;
            color: #1D2029;
          }
        }
        .study-item-r{
          font-size: 20rpx;
          color: #1D2029;
          span{
            font-size: 36rpx;
          }
        }
        &:last-child{
          margin-bottom: 0;
        }
      }
      .empty{
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 60vh;
        .empty-img{
          display: flex;
          width: 286rpx;
          height: 212rpx;
        }
        .text{
          margin-top: 24rpx;
          font-size: 24rpx;
          color: #4E5569;
          line-height: 34rpx;
        }
      }
    }
  }
  .study-bottom{
    position: fixed;
    bottom: 0;
    left: 0;
    width: calc(100% - 96rpx);
    display: flex;
    // height: 180rpx;
    background: #D0EEE6;
    border-radius: 16rpx 16rpx 0rpx 0rpx;
    padding: 32rpx 48rpx 68rpx;
    // box-sizing: border-box;
    .study-bottom-item{
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: space-between;
      .study-bottom-l{
        display: flex;
        .bottom-l-img{
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          margin-right: 24rpx;
          overflow: hidden;
          image{
            width: 100%;
            height: 100%;
          }
        }
        .bottom-l-info{
          display: flex;
          flex-direction: column;
          .info-title{
            font-size: 28rpx;
            color: #1D2029;
            line-height: 40rpx;
            margin-bottom: 4rpx;
          }
          .info-ranking{
            font-size: 24rpx;
            color: #4E5569;
            line-height: 34rpx;
            span{
              font-size: 24rpx;
              color: #1D2029;
            }
          }
        }
      }
      .study-bottom-r{
        font-size: 28rpx;
        color: #1D2029;
        line-height: 40rpx;
        span{
          font-size: 20rpx;
          color: #1D2029;
          line-height: 28rpx;
        }
      }
    }
  }
}
</style>