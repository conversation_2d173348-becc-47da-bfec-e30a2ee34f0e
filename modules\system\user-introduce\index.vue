<template>
  <view class='user-introduce'>
    <view class="textarea-wrapper">
      <textarea 
        class="my-textarea" 
        v-model="intro"
        placeholder="介绍一下自己" 
        :maxlength="-1" 
        auto-height
        @input="handleInput"
      />
      <view class="counter">
        {{ currentLength }} / {{ 300 }}
      </view>
    </view>
    <view class="btn-wrapper">
      <view class="btn-bg" @tap="onSubmit">保存</view>
    </view>
  </view>
</template>

<script>
  import {mapState} from 'vuex'
  export default {
    data(){
      return{
        $constant: this.$constant,
        intro: "",       // 文本内容
        currentLength: 0,    // 当前字数
        paramsObj:{},
      }
    },
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId
      })
    },
    onLoad(options){
      const query = this.$validate.isNull(this.$Route.query) ? options : this.$Route.query
      this.paramsObj = query.nickName ? query : {}
      this.intro = query?.intro || ''
      this.currentLength = this.intro?.length || 0
    },
    mounted(){},
    methods:{
      onSubmit(){
        this.$uniPlugin.loading('保存中', true)
        this.$api.community.fansrecordInfoUpdate({...this.paramsObj,intro:this.intro,accountId: this.accountId }).then(res => {
          this.$uniPlugin.hideLoading()
          this.$uniPlugin.toast(res.msg)
          this.$ext.user.getCommunityFansRecord().then(() => {
            setTimeout(() => {
              this.$navto.back(1)
            }, this.$constant.noun.delayedOperationTime)
          })
        }).catch(() => {
          this.$uniPlugin.hideLoading()
        })
      },
      handleInput(e) {       
        const value = e.detail.value
        let newValue = value
        if (value.length > 300) {
          newValue = value.substring(0, 300)
        }
        // 更新文本框内容（如果被截断，则重新设置）
        if (newValue !== value) {
          // 因为v-model是异步的所以这里使用nextTick确保更新
          this.$nextTick(() => {
            this.intro = newValue
          })
        } else {
          this.intro = newValue
        }
        this.currentLength = newValue.length
      }
    },
 }
</script>

<style lang='scss' scoped>
.user-introduce{
  position: relative;
  height: calc(100vh - 28rpx);
  padding: 28rpx 32rpx 0;
  background-color: #fff;
  .textarea-wrapper{
    position: relative;
  }
  .my-textarea{
    width: 686rpx;
    min-height: 316rpx;
    overflow-y: scroll;
    overflow-x: hidden;
    background: #F4F6FA;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    padding: 20rpx 20rpx 60rpx;
    box-sizing: border-box;
  }
  .counter {
    position: relative;
    bottom: 60rpx;
    right: 40rpx;
    display: flex;
    flex: 1;
    justify-content: flex-end;
    width: 100%;
    font-size: 28rpx;
    color: #777777;
    padding: 0 10rpx;
  }
  .btn-wrapper{
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 156rpx;
    padding-top: 24rpx;
    background: #FFFFFF;
    box-shadow: 0rpx -1rpx 0rpx 0rpx #EAEBF0;
    border-radius: 0rpx 0rpx 0rpx 0rpx;
    .btn-bg{
      display: flex;
      align-self: center;
      justify-content: center;
      font-weight: 500;
      font-size: 32rpx;
      color: #FFFFFF;
      width: 646rpx;
      height: 88rpx;
      background: #00B484;
      border-radius: 44rpx 44rpx 44rpx 44rpx;
    }
  }
}
</style>