<template>
  <view class='web-white-video'>
    <view class="medication-content">
      <video 
        id="myVideo"
        :src="file_ctx + videoPath" 
        play-btn-position="center"
        :show-fullscreen-btn="false"  
        :show-progress="false"
        :show-center-play-btn="false" 
        :show-play-btn="false"
        :autoplay="true"
        :loop="true"
        :controls="false" 
        @click="togglePlayButton"  
      >
      </video>
      <view class="custom-play-btn" @click="toggleVideo" v-if="isPlaying">  
        <image class="play-icon" :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/play.png'"></image>  
      </view>  
    </view>
  </view>
</template>

<script>
  export default {
    data(){
      return{
        file_ctx: this.file_ctx,
        isPlaying:true,
        videoContext:null,
        videoPath:'static/image/business/pharmacy-cyclopedia/web-white-video.mp4',
        currentStatus:'',
      }
    },
    onLoad(){},
    mounted(){
      // 获取播放器上下文（后面的 this 需要传入，在微信小程序上无法暂停播放拖拽精度，所以需要传入这个）
      this.videoContext = uni.createVideoContext('myVideo', this)
      // if (typeof WeixinJSBridge == "object" && typeof WeixinJSBridge.invoke == "function") {
      //   WeixinJSBridge.invoke('getNetworkType', {}, (res)=> {
      //     this.videoContext.play();
      //   });
      // }else{
      //   this.$nextTick(() => {
      //     this.videoContext.play();
      //   })
      // }
    },
    methods:{
      toggleVideo() {  
        this.isPlaying = !this.isPlaying
        if (this.isPlaying) {  
          this.currentStatus = 'pause'
          this.videoContext.pause();  
        } else {  
          this.currentStatus = 'play'
          this.videoContext.play();  
        }  
      },  
      togglePlayButton(){
        this.toggleVideo()
      },
    },
 }
</script>

<style lang='scss' scoped>
.web-white-video{
  height: 100%;
  overflow-y: hidden;
  .medication-content{
    position: relative;
    display: flex;
    flex: 1;
    flex-direction: column;
    width: 100%;
    background-color: #000;
    video{
      width: 100%;
      height: 100vh;
    }
    .custom-play-btn{
      position: absolute;  
      top: 50%;  
      left: 50%;  
      transform: translate(-50%, -50%);  
      width: 96rpx;
      height: 96rpx;
      opacity: 1;
      image{
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>