<template>
  <view class="attention">
    <view class="circle-main">
      <template v-for="(item, index) in tabList">
        <view
          class="circle-item"
          :key="index"
          @tap="checkedItem(item)"
        >
          <view
            class="logo-div"
            :class="{
              active: checkboxList.includes(item.id),
            }"
          >
            <image
              :src="file_ctx + item.logoPath"
              class="circle-logo"
              mode="aspectFit"
            />
          </view>
          <text class="circle-name">{{ item.name }}</text>
        </view>
      </template>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    tabs: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      file_ctx: this.file_ctx,
      $validate: this.$validate,
      tabList: [],
      checkboxList: []
    }
  },
  watch: {
    tabs: {
      handler() {
        this.checkboxList = []
        this.tabList = this.tabs
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    checkedItem(e) {
      const index = this.checkboxList.findIndex(item => item === e.id)
      if (index === -1) {
        this.checkboxList.push(e.id)
      } else {
        this.checkboxList.splice(index, 1)
      }
      this.$emit('changeSelect', this.checkboxList)
    },
    changeAnonymous(e, id) {
      if (this.$validate.isNull(e.target.value)) {
        this.checkboxList.splice(this.checkboxList.findIndex(item => item === id), 1)
      } else {
        this.checkboxList.push(id)
      }
      this.$emit('changeSelect', this.checkboxList)
    }
  }

}
</script>

<style scoped lang="scss">
.attention {
  padding: 56rpx 32rpx;
  box-sizing: border-box;
  .circle-main {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    background: #ffffff;
    .circle-item {
      width: 25%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      box-sizing: border-box;
      &:nth-child(n + 5) {
        margin-top: 48rpx;
      }
      .logo-div {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 88rpx;
        height: 88rpx;
        box-sizing: border-box;
        margin-bottom: 16rpx;
        background-color: #D7FAF1;
        filter: grayscale(1);
        @include rounded(16rpx);
        &.active {
          border: 1px solid #00B484;
          filter: grayscale(0);
        }
      }
      .circle-logo {
        width: 50rpx;
        height: 50rpx;
      }
      .circle-name {
        font-weight: 400;
        font-size: 24rpx;
        color: #1D2029;
        line-height: 34rpx;
        display: inline-block;
        vertical-align: middle;
        @include ellipsis(1);
      }
      &-checked {
        border-color: #00d29d;
      }
    }
  }
}
</style>