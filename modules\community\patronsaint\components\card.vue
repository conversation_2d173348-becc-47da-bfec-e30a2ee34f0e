<template>
  <view>
    <uni-popup ref="popupRef" id="healthGuardPopup" type="center">
        <view class="fixed-box">
            <view class="content-box">
                <text class="close-btn" @tap="close">x</text>
                <image class="card-logo" mode="widthFix" :src="file_ctx + info.afterLightPopupPath" />
                <button class="card-take-btn" @tap="take" v-if="info.lightStatus == 2">立即收下</button>
            </view>
        </view>
    </uni-popup>
  </view>
</template>

<script>
import uniPopup from '@/components/uni/uni-popup'
export default {
    components: {
        uniPopup
    },
    props: {
        info: {
            type: Object,
            default: () => {}
        }
    },
    data () {
        return {
            file_ctx: this.file_ctx,
        }
    },
    methods: {
        open () {
            this.$refs.popupRef.open()
            // #ifdef MP-WEIXIN
            this.handleClickTrack(1)
            // #endif
        },
        close () {
            this.$refs.popupRef.close()
            // #ifdef MP-WEIXIN
            this.handleClickTrack(2)
            // #endif
        },
        // #ifdef MP-WEIXIN
        handleClickTrack(type){
          getApp().globalData.sensors.track("PopupClick",
            {
              'page_name' : '健康保护神',
              'popup_id' : 'healthGuardPopup',
              'popup_name' : '是否点亮',
              'click_type' : type == 1 ? '进入弹窗' : '取消弹窗',
            }
          ) 
        },
        // #endif
        take () {
            this.close()
            this.$emit('take')
        }
    }
}
</script>

<style scoped lang="scss">
.fixed-box {
    height: 100%;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .content-box {
        padding: 56upx 12upx;
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    .close-btn {
        position: absolute;
        right: 0;
        top: 0;
        border: 2upx solid #000;
        height: 42upx;
        width: 42upx;
        color: #000;
        @include rounded(50%);
        font-size: 36upx;
        text-align: center;
        line-height: 42upx;
    }
    .card-logo {
        display: block;
        width: 552upx;
    }
    .card-take-btn {
        width: 279upx;
        height: 85upx;
        background: #00D29D;
        border-radius: 43upx;
        margin-top: 59upx;
        font-size: 47upx;
        font-weight: 500;
        color: #FFFFFF;
        text-align: center;
        line-height: 85upx;
    }
}
</style>