/**
 * 发送 uni.request 流式请求（支持 HTTP 分块传输）
 * @param {string} url - 请求 URL
 * @param {object} options - 请求配置
 * @param {string} [options.method='GET'] - 请求方法
 * @param {object} [options.header] - 请求头
 * @param {object|string} [options.data] - 请求数据
 * @param {function} onMessage - 接收数据块的回调函数
 * @param {function} [onError] - 错误回调函数
 * @param {function} [onComplete] - 完成回调函数
 * @returns {function} - 取消请求的函数
 */
import env from '@/config/env'
import common from '@/common/util/main'
import constant from '@/constant'
// 上传文件请求头的terminalType参数在不同环境下的配置
let terminalType = ''
// #ifdef H5
terminalType = constant.system.terminal.h5
// #endif
// #ifdef APP-PLUS
terminalType = constant.system.terminal.app
// #endif
// #ifdef MP-WEIXIN
terminalType = constant.system.terminal.miniProgram
// #endif
// #ifdef MP-ALIPAY
terminalType = constant.system.terminal.alipay
// #endif
export function streamRequest(url, options = {}, onMessage, onError, onComplete) {
  const {
    method = 'GET',
    header = {},
    data = null,
  } = options;
  const token = common.getToken()
  const tenantId = common.getKeyVal('user', 'curSelectStoreId', true)
  const mergedHeader = {
    'terminal-type': terminalType,
    'accept': 'text/event-stream',
    'satoken': token,
    'auth-version': 'v2',
    'gb-tenant-id': (tenantId && tenantId !== 'undefined') ? tenantId : env.tenantId,
    'Content-Type': 'application/json; charset=utf-8',
    ...header
  }

  let buffer = ''

  // 仅支持微信小程序（需条件编译）
  const requestTask = wx.request({
    url,
    method,
    header: mergedHeader,
    data,
    responseType: 'text', // 必须设置为文本格式
    enableChunked: true, // 启用分块传输
    success: (res) => {
      if (res.statusCode !== 200) {
        onError?.(`HTTP错误: ${res.statusCode}`);
        return;
      }
      
      // 处理初始响应数据
      processChunk(res.data);
    },
    fail: (err) => {
      onError?.(err.errMsg);
    },
    complete: () => {
      // 处理可能剩余的不完整数据
      if (buffer) {
        processSseMessage(buffer);
      }
      onComplete?.();
    }
  });

  // 监听分块数据接收
  requestTask.onChunkReceived((chunk) => {
    const chunkData = chunk.data; // 分块数据（ArrayBuffer/String）
    // console.log('收到分块:', chunkData);
    processChunk(chunkData);
  });
  
  // 处理数据块
  async function processChunk(chunk) {
    if (!chunk) return;
    
    // 解码二进制数据
    try {
      const chunkText = decodeUTF8(chunk);
      buffer += chunkText;
      // ...后续处理逻辑
    } catch (decodingError) {
      console.error('解码错误:', decodingError);
      console.log('原始数据:', chunk); // 打印原始ArrayBuffer
    }
    
    // 使用更高效的字符串查找方式
    let startIndex = 0;
    let endIndex;
    
    while ((endIndex = buffer.indexOf('\n\n', startIndex)) !== -1) {
      const sseMessage = buffer.substring(startIndex, endIndex).trim();
      startIndex = endIndex + 2;
      
      if (sseMessage) {
        processSseMessage(sseMessage);
      }
    }
    
    // 移除已处理的部分
    if (startIndex > 0) {
      buffer = buffer.substring(startIndex);
    }

  }
  
  // 处理单条 SSE 消息
  function processSseMessage(message) {
    if (!message) return;
    try {
      onMessage(message);
    } catch (error) {
      console.error('解析 SSE 数据失败:', error);
      // 解析失败时，传递原始数据
      // onMessage(message);
    }
  }

  // 自定义 UTF-8 解码函数
  function decodeUTF8(arrayBuffer) {
    try {
      // 尝试使用 Uint8Array 和 String.fromCharCode 解码
      const bytes = new Uint8Array(arrayBuffer);
      let result = '';
      let i = 0;
      
      while (i < bytes.length) {
        const firstByte = bytes[i];
        
        if (firstByte < 128) {
          // 单字节字符 (0-127)
          result += String.fromCharCode(firstByte);
          i++;
        } else if (firstByte >= 192 && firstByte < 224) {
          // 双字节字符
          if (i + 1 < bytes.length) {
            const secondByte = bytes[i + 1];
            const codePoint = ((firstByte & 0x1F) << 6) | (secondByte & 0x3F);
            result += String.fromCharCode(codePoint);
            i += 2;
          } else {
            // 不完整的字符
            i++;
          }
        } else if (firstByte >= 224 && firstByte < 240) {
          // 三字节字符 (包括大多数常用汉字)
          if (i + 2 < bytes.length) {
            const secondByte = bytes[i + 1];
            const thirdByte = bytes[i + 2];
            const codePoint = ((firstByte & 0x0F) << 12) | 
                              ((secondByte & 0x3F) << 6) | 
                              (thirdByte & 0x3F);
            
            // 处理 Unicode 代理对
            if (codePoint > 0xFFFF) {
              codePoint -= 0x10000;
              result += String.fromCharCode(
                0xD800 + (codePoint >> 10),
                0xDC00 + (codePoint & 0x3FF)
              );
            } else {
              result += String.fromCharCode(codePoint);
            }
            
            i += 3;
          } else {
            // 不完整的字符
            i++;
          }
        } else {
          // 四字节字符或无效字符
          i++;
        }
      }
      
      return result;
    } catch (error) {
      console.error('解码失败:', error);
      // 出错时返回空字符串或原始数据的十六进制表示
      return '';
    }
  }
  
  // 返回取消请求的函数
  return () => requestTask.abort();
}