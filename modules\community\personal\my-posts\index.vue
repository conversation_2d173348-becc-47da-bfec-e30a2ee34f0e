<template>
  <page>
    <view slot="content" class="content">
      <!-- tab菜单-->
      <tabs-sticky
        v-model="curIndex"
        :fixed="false"
        :tabs="tabs"
        @change="changeTab"
      ></tabs-sticky>
      <view class="main">
        <swiper class="swiper" :current="curIndex" @change="swiperChange">
          <swiper-item v-for="(item, index) in tabs" :key="index">
            <posts-list ref="postsListRef" :params="item" :isShowBtn="isShowBtn" />
          </swiper-item>
        </swiper>
      </view>
    </view>
  </page>
</template>

<script>
import TabsSticky from '@/components/basics/tabs-sticky'
import postsList from './components/posts-list.vue'
export default {
  components: {
    TabsSticky,
    postsList
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      $validate: this.$validate,
      curIndex: 0, // 当前菜单下标,
      tabs: [{ name: '已发布', processStatus: 2 }, { name: '待审核', processStatus: 1 }],
      isShowBtn:true,
    }
  },
  onLoad() {
    const query = this.$Route.query
    if (!this.$validate.isNull(query) && !this.$validate.isNull(query.isShowBtn)) {
      this.isShowBtn = JSON.parse(query.isShowBtn)
    }
    if (!this.$validate.isNull(query) && !this.$validate.isNull(query.processStatus)) {
      const curIndex = this.tabs.findIndex(item => item.processStatus == query.processStatus)
      this.curIndex = curIndex
    }
    this.init()
    // #ifdef MP-WEIXIN
    this.handleClickTrack('OperationDetailsPageView')
    // #endif
  },
  onUnload(){
    // #ifdef MP-WEIXIN
    this.handleClickTrack('EndOperationDetailsPageView')
    // #endif
  },
  methods: {
    // #ifdef MP-WEIXIN
    handleClickTrack(type){
      let pages = getCurrentPages()
      let current = pages[pages.length - 1]; // 获取到当前页面的信息
      let pageInfo = __wxConfig.page && __wxConfig.page[`${current.route}.html`] || ''; // 内部属性
      getApp().globalData.sensors.track(type,
        {
          'page_name' : pageInfo?.window?.navigationBarTitleText || '',
          'first_operation_name' : '我的信息',
          'second_operation_name' : '我的帖子',
        }
      ) 
    },
    // #endif
    // 轮播菜单
    swiperChange(e) {
      this.changeTab(e.detail.current)
    },
    changeTab(index) {
      this.curIndex = index
    },
    navtoGo(url = '', obj = {}) {
      this.$navto.push(url, obj)
    },
    init(val) {
      this.$nextTick(() => {
        const curIndex = this.curIndex
        this.changeTab(curIndex)
        for (let i = 0; i < this.$refs.postsListRef.length; i++) {
          const item = this.$refs.postsListRef[i];
          item.init()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}
.main {
  flex: 1;
  .swiper {
    height: 100%;
  }
}
</style>
