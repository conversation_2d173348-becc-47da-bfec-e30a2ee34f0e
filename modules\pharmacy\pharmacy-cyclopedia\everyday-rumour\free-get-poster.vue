<template>
  <view class='free-get-poster'>
    <image :src="file_ctx + 'static/image/business/hulu-v2/icon-rumour-free-get-new-year.jpg'" :show-menu-by-longpress="true" @longpress="handleLongPress"></image>
  </view>
</template>

<script>
  import { mapState } from 'vuex'
  export default {
    data(){
      return{
        $constant:this.$constant,
        file_ctx:this.file_ctx,
      }
    },
    async onLoad(){
      this.handlePageexposurerecordInsert()
    },
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId,
        recordUserInfo: state => state.recordUserInfo, // 当前登录用户信息
      }),
    },
    mounted(){},
    methods:{
      async handleLongPress(){
        let openId = await this.$ext.wechat.getOpenId()
        await this.$api.drugBook.visitpagerecordInsert({openId,businessType:this.$constant.drugBook.businessTypeObj.reportTypeSqueezePage}) //businessType :水光针落地页 13
      },
     async handlePageexposurerecordInsert(){
        let openId = await this.$ext.wechat.getOpenId()
        let params = {
          openId,
          businessType:this.$constant.drugBook.businessTypeObj.reportTypeSqueezePage,
          imageUrl:this.file_ctx + 'static/image/business/hulu-v2/icon-rumour-free-get-new-year.jpg'
        }
        this.$api.drugBook.pageexposurerecordInsert({
          ...params,
          accountId:this.accountId,
          userId:this.recordUserInfo?.userId,
        })
      },
    },
 }
</script>

<style lang='scss' scoped>
.free-get-poster{
  width: 100%;
  height: 1448rpx;
  image{
    width: 100%;
    height: 100%;
  }
}
</style>