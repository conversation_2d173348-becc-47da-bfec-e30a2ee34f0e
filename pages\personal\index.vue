<template>
  <page>
    <view slot="content" class="body-main">
      <view class="main">
        <scroll-view scroll-y="true" class="scroll-view-main">
          <view class="m-main">
            <view class="my-data">
              <view class="my-bg" :style="{backgroundImage: 'url(' + static_ctx2 + 'image/business/hulu-v2/icon-user-bg.png)'}">
                <!-- #ifdef MP-WEIXIN || H5 || MP-ALIPAY -->
                <view>
                  <status-bar-height></status-bar-height>
                  <view :style="{ 'height':'44px' }"></view>
                </view>
                <!-- #endif -->
                <view class="my-user" :class="{
                  pdl40:infoObj.lightLogoPath
                }">
                  <!-- @tap="navtoGo('User')" -->
                  <view class="user-info" >
                    <view class="img-main" :style="infoObj.lightLogoPath ? 'margin-right:32rpx;' : ''" @tap="handleClickJumpUser">
                      <view class="user-head-pic">
                        <default-img
                          style="width: 100rpx;height: 100rpx;display: inline-block;"
                          :config="config.avatar"
                          :cData="infoObj.avatarUrl"
                          :cName="infoObj?infoObj.name:''"
                          class="role-image"
                        />
                        <view class="user-sex" v-if="isLogin">
                          <image class="width-height-atuo" :src="fansRecord.sex != $constant.noun.gender1515 ? $static_ctx + 'image/business/icon-boy.png' : $static_ctx + 'image/business/icon-girl.png'"/>
                        </view>
                      </view>
                      <!-- 头饰挂件 -->
                      <image v-if="infoObj.lightLogoPath" class="headimg-wrapper" :src="file_ctx + infoObj.lightLogoPath" mode="aspectFill"></image>
                    </view>
                    <view class="user-head-name" @tap="handleClickUser">
                      <template v-if="isLogin">
                        <view class="name">{{ infoObj.name || '暂无名称'}}</view>
                        <view class="edit" @tap="handleClickJump(1,1)">
                          <text class="text">编辑</text>
                          <image class="next-icon" :src="static_ctx2 + 'image/business/hulu-v2/icon-user-next.png'" mode="aspectFill"></image>
                          <!-- <text class="icon-edit-more"></text> -->
                        </view>
                      </template>
                      <view v-else @click="toLogin()">
                        <view class="name">登陆/注册<image class="next-icon2" :src="static_ctx2 + 'image/business/hulu-v2/icon-user-next2.png'" mode="aspectFill"></image></view>
                        <view class="edit">
                          <view class="text">
                            登陆手机号，同步自己的个人记录哦~
                          </view>
                        </view>

                      </view>
                    </view>
                  </view>
                </view>
              </view>

              <view class="my-card">
                <view class="my-card-main">
                  <view class="li" v-for="(item,index) in myCardList" :key="item.id" @click="handleClickJump(1,item,index)">
                    <view class="number" v-if="index == 0">{{ dataStatistics.postMessageCount || 0 }}</view>
                    <view class="number" v-if="index == 1">{{ dataStatistics.commitCount || 0 }}</view>
                    <view class="number" v-if="index == 2">{{ dataStatistics.collectionCount || 0 }}</view>
                    <view class="number" v-if="index == 3">{{ dataStatistics.likeCount || 0 }}</view>
                    <view class="text">{{ item.name }}</view>
                  </view>
                </view>
              </view>
            </view>

            <!--  #ifdef MP-WEIXIN-->
            <!-- <subscribe-remind></subscribe-remind> -->
            <!--#endif-->
            <view class="me-menu-main">
              <view class="me-menu-item" v-for="(item,index) in menuMainList" :key="item.id" @click="handleClickJump(2,item,index)">
                <image v-if="index == 0" :src="static_ctx2 + 'image/business/hulu-v2/icon-user-family-visitor.png'" class="me-menu-icon" mode="aspectFill"></image>
                <image v-if="index == 1" :src="static_ctx2 + 'image/business/hulu-v2/icon-user-my-consultation.png'" class="me-menu-icon" mode="aspectFill"></image>
                <image v-if="index == 2" :src="static_ctx2 + 'image/business/hulu-v2/icon-user-post-diagnosis-review.png'" class="me-menu-icon" mode="aspectFill"></image>
                <image v-if="index == 3" :src="static_ctx2 + 'image/business/hulu-v2/icon-user-drug-evaluation.png'" class="me-menu-icon" mode="aspectFill"></image>
                <view class="me-menu-text">{{ item.name }}</view>
              </view>
            </view>

            <view class="my-banner">
            <banner-ads ref="bannerAdsRef" :query-params="{useType: 5}" height="208rpx" />
            </view>

            <view class="more-function-box">
              <view class="more-function-t">
                更多功能
              </view>
              <view class="more-function-ul">
                <view class="more-function-item" v-for="(item,index) in moreUlList" :key="item.id" @click="handleClickJump(3,item,index)">
                  <!-- <image v-if="index == 0" :src="static_ctx2 + 'image/business/hulu-v2/icon-user-phone.png'" class="more-function-icon" mode="aspectFill"></image> -->
                  <image v-if="index == 0" :src="static_ctx2 + 'image/business/hulu-v2/icon-user-password.png'" class="more-function-icon" mode="aspectFill"></image>
                  <image v-if="index == 1" :src="static_ctx2 + 'image/business/hulu-v2/icon-user-feedback.png'" class="more-function-icon" mode="aspectFill"></image>
                  <image v-if="index == 2" :src="static_ctx2 + 'image/business/hulu-v2/icon-user-package-insert.png'" class="more-function-icon" mode="aspectFill"></image>
                  <image v-if="index == 3" :src="static_ctx2 + 'image/business/hulu-v2/icon-user-invitation-record.png'" class="more-function-icon" mode="aspectFill"></image>
                  <image v-if="index == 4" :src="static_ctx2 + 'image/business/hulu-v2/icon-user-questionnaire-activity.png'" class="more-function-icon" mode="aspectFill"></image>
                  <image v-if="index == 5" :src="static_ctx2 + 'image/business/hulu-v2/icon-user-setting.png'" class="more-function-icon" mode="aspectFill"></image>
                  <view class="more-function-text">{{ item.name }}</view>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
        <!-- 在线客服 -->
        <customerService v-if="isShowConfig" ref="customerService"></customerService>
      </view>
    </view>
  </page>
</template>

<script>
import defaultImg from '@/components/basics/default-avatar/index'
import { mapState } from 'vuex'
import StatusBarHeight from '@/components/business/module/status-bar-height/index'
import bannerAds from '@/components/basics/banner-ads/index.vue'
import customerService from '@/components/basics/customerService/index.vue'

export default {
  name: 'Personal',
  components: {
    StatusBarHeight,
    defaultImg,
    bannerAds,
    customerService
  },
  data() {
    return {
      interval:2000,
      duration: 500,
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $timePlugin: this.$timePlugin,
      $static_ctx: this.$static_ctx,
      static_ctx2: this.$static_ctx,
      roleList: [],
      navToName: undefined,
      config: {
        avatar: {
          widthHeightAuto: true,
          itemClass: {
            width: '100rpx',
            height: '100rpx',
            display: 'inline-block',
          }
        }
      },
      dataStatistics: {},
      defaultAvatar: this.$static_ctx + 'image/system/avatar/icon-default-avatar.png',
      myCardList:[
        {id:1,name:'我的帖子',title:'我的信息',orderIndex:2,path:'PersonalMyPosts',url:'/modules/community/personal/my-posts/index'},
        {id:2,name:'我的评论',title:'我的信息',orderIndex:3,path:'PersonalMyComment',url:'/modules/community/personal/my-comment/index'},
        {id:3,name:'我的收藏',title:'我的信息',orderIndex:4,path:'PersonalMyCollect',url:'/modules/community/personal/my-collect/index'},
        {id:4,name:'我的点赞',title:'我的信息',orderIndex:5,path:'PersonalMyLike',url:'/modules/community/personal/my-like/index'},
      ],
      menuMainList:[
        {id:1,name:'家庭就诊人',title:'功能使用',orderIndex:6,path:'Patients',url:'/modules/business/patients/index'},
        {id:2,name:'我的咨询',title:'功能使用',orderIndex:7,path:'Consult',url:'/modules/business/consult/index'},
        {id:3,name:'诊后点评',title:'功能使用',orderIndex:8,path:'diagnosisComment',type:1,url:'/modules/activity/pages/diagnosis/list'},
        {id:4,name:'用药评价',title:'功能使用',orderIndex:9,path:'diagnosisComment',type:2,url:'/modules/activity/pages/diagnosis/list'},
      ],
      moreUlList:[
        // {id:1,name:'我的手机',title:'更多功能',orderIndex:10,path:'ModifyPhone',url:'/modules/system/modify-phone/bind-phone'},
        {id:2,name:'修改密码',title:'更多功能',orderIndex:11,path:'Password',url:'/modules/system/pwd/index'},
        {id:3,name:'意见反馈',title:'更多功能',orderIndex:12,path:'Feedback',url:'/modules/system/feedback/index'},
        {id:4,name:'药品说明书',title:'更多功能',orderIndex:13,path:'History',url:'/modules/pharmacy/pharmacy-cyclopedia/history/index'},
        {id:5,name:'邀请记录',title:'更多功能',orderIndex:14,path:'InviteRecord',url:'/modules/community/posts/invite-record/index'},
        {id:6,name:'问卷记录',title:'更多功能',orderIndex:15,path:'Research',url:'/modules/activity/research/index'},
        {id:7,name:'系统设置',title:'更多功能',orderIndex:16,path:'Setting',url:'/modules/system/setting/index'},
      ],
      isShowConfig:false,
    }
  },
  computed: {
    ...mapState('user', {
      codeUserInfo: state => state.codeUserInfo, // 当前登录用户信息
      curSelectUserInfo: state => state.curSelectUserInfo, // 当前选中的用户信息
      curSelectUserInfoId: state => state.curSelectUserInfoId, // 当前选中的档案的ID
      recordUserInfo: state => state.recordUserInfo,
      accountId: state => state.accountId,
      fansRecord: state => state.fansRecord,
      isLogin: state => state.isLogin
    }),
    infoObj() {
      return {
        name: this.fansRecord.nickName,
        avatarUrl: this.fansRecord.headPath ? this.fansRecord.headPath : this.defaultAvatar,
        genderUrl: this.recordUserInfo.gender === 1?'icon-boy':'icon-girl',
        roleListData: [
        ],
        lightLogoPath: this.fansRecord.lightLogoPath
      }
    }
  },
  onLoad(paramsObj) {
    const that = this
    const query = that.$Route.query
    this.getCustomerserviceprofilesQueryNoParametersOne()
    if (!that.$validate.isNull(query)) {
      that.navToName = query.navToName
      if (!that.$validate.isNull(that.navToName)) {
        that.$uniPlugin.loading('正在跳转...', true)
        setTimeout(function() {
          that.$uniPlugin.hideLoading()
          that.$navto.push(that.navToName)
        }, that.$constant.noun.delayedOperationTime)
        that.$uniPlugin.hideLoading()
      }
    }
  },
  onShow() {
    // #ifdef MP-WEIXIN
    this.setTabBarIndex(4);
    // #endif
    const that = this
    that.getCommoncollectlikesGetMyStatistics()
    let reg = new RegExp(/^FS_/)
    if (reg.test(that.fansRecord.nickName)) {
      // 获取accoutId 绑定accountId
      uni.login({
          provider: 'weixin',
          scopes: 'auth_base',
          success: (res) => {
              const {code} = res
              that.$ext.user.getWxUserBindAccount({jsCode: code}).then(() => {
                that.$ext.user.addFans()
              }).catch(() => {
                that.$ext.user.addFans()
              })
          },
          fail: (err) => {
              console.log('微信授权失败',err)
          }
      })
    }
    this.$refs.bannerAdsRef.init()
  },
  methods: {
    // 在线客服
    async getCustomerserviceprofilesQueryNoParametersOne(){
      const res = await this.$api.drugBook.getCustomerserviceprofilesQueryNoParametersOne()
      if(res.data !==""){
        res.data.enableStatus == 1 && res.data.types.includes('4') ? this.isShowConfig = true : this.isShowConfig = false
      }
    },
    handleClickJumpUser(){
      if(this.isLogin && this.accountId){
        this.navtoGo('PersonalHomePage', { homePageAccountId:this.accountId,isShowBtn:true })
      } else {
        this.$navto.push('Login',{formPage: 'PersonalHomePage',formPageParams: encodeURIComponent(JSON.stringify({ homePageAccountId:this.accountId,isShowBtn:true }))})
      }
    },
    // 点击头像跳转
    handleClickUser(){
      // #ifdef MP-WEIXIN
      let obj = {
        title:'我的信息',
        name:'头像',
        id:6,
        url:'/modules/system/user/index',
        orderIndex:'1'
      }
      this.handleClickTrack(1,obj)
      // #endif
      if(this.isLogin){
        this.navtoGo('User')
      } else {
        this.$navto.push('Login',{formPage: 'User'})
      }
    },
    handleClickJump(floor,item,index){
      if(floor == 1){
        this.navtoGo(item.path, {orderIndex:item.orderIndex})
      } else if(floor == 2){
        if(this.isLogin){
          if(item.path == 'diagnosisComment'){
            this.navtoGo(item.path, {type:item.type})
          } else {
            // this.$navto.push('Login',{formPage: 'Personal'})
            this.navtoGo(item.path)
          }
        } else {
          //  this.$navto.push('Login',{formPage: item.path})
           this.$navto.push('Login',{formPage: 'Personal'})
        }
      } else if(floor == 3) {
        if(this.isLogin){
          this.navtoGo(item.path)
        } else {
          this.$navto.push('Login',{formPage: 'Personal'})
        }
      }
      // #ifdef MP-WEIXIN
      this.handleClickTrack(floor,item)
      // #endif
    },
    // #ifdef MP-WEIXIN
    handleClickTrack(floor,item){
      getApp().globalData.sensors.track("OperationClick",
        {
          'page_name' : '我的',
          'first_operation_name' : item.title,
          'second_operation_name' : item.name,
          'operation_floor' : floor,
          'operation_id' : item.id+'',
          'is_link_third' : '否',
          'target_url' : item.url,
          'position_rank' : item.orderIndex,
        }
      ) 
    },
    // #endif
    toLogin() {
      this.$navto.push('Login', { 'redirect': this.routerName })
    },
    async getCommoncollectlikesGetMyStatistics () {
      const param = {
        accountId: this.accountId
      }
      const res = await this.$api.community.commoncollectlikesGetMyGourdStatistics(param)
      let data = res.data;
      if(data.postMessageCount && data.postMessageCount > 9999) {
        data.postMessageCount = '9999+'
      }
      if(data.commitCount && data.commitCount > 9999) {
        data.commitCount = '9999+'
      }
      if(data.collectionCount && data.collectionCount > 9999) {
        data.collectionCount = '9999+'
      }
      if(data.topicCollectCount){ //和话题数量相加
        data.collectionCount += data.topicCollectCount
      }
      if(data.likeCount && data.likeCount > 9999) {
        data.likeCount = '9999+'
      }
      this.dataStatistics = data
    },
    navtoGo(name = '', params = {}) {
      if(name === 'no') {
        return this.$uniPlugin.toast('该功能未开方')
      }
      // #ifdef MP-ALIPAY
        if(['Patients','InviteRecord','ReceiptInform'].includes(name)) {
          return this.$uniPlugin.toast('该端不支持该功能')
        }
      // #endif
      this.$navto.push(name, params)
    },
    toastFn() {
      this.$uniPlugin.toast('敬请期待')
    }
  }
}
</script>

<style lang="scss" scoped>
.next-icon {
  width: 24upx;
  height: 24upx;
  vertical-align: middle;
}
.next-icon2 {
  width: 40rpx;
  height: 40rpx;
  vertical-align: middle;
  margin-top: -4rpx;
}
.more-function-box {
  margin: 20upx 32upx 0;
  padding: 32rpx 0;
  background-color: #fff;
  border-radius: 16upx;
  .more-function-t {
    height: 44rpx;
    font-size: 32rpx;
    color: #1F2021;
    line-height: 44rpx;
    padding: 0 38rpx;
  }
  .more-function-ul {
    display: flex;
    flex-wrap: wrap;
    margin-top: 40upx;
  }
  .more-function-icon {
    width: 64upx;
    height: 64upx;
  }
  .more-function-text {
    font-size: 24rpx;
    color: #636567;
    line-height: 34rpx;
    margin-top: 12upx;
    position: relative;
  }
  .more-function-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 25%;
    margin-bottom: 40rpx;
    &:nth-child(n+4){
      margin-bottom: 0;
    }
  }

}

.me-menu-main{
  margin: 20upx 32upx 0;
  background-color: #fff;
  padding: 32upx 0;
  display: flex;
  justify-content: space-around;
  border-radius: 16rpx;
  overflow: hidden;
  .me-menu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .me-menu-icon {
    width: 64upx;
    height: 64upx;
  }
  .me-menu-text {
    font-size: 24upx;
    color: #868C9C;
    line-height: 34upx;
    text-align: center;
    margin-top: 12upx;
    position: relative;
  }
}
  .my-banner{
    padding: 20rpx 32rpx 0rpx;
  }
  .body-main {
    height: 100%;
    background: #F4F6FA;
    // #ifdef MP-WEIXIN
    padding-bottom: calc(56px + env(safe-area-inset-bottom));
    // #endif
    .main {
      height: 100%;
      .scroll-view-main {
        height: 100%;
      }
    }
  }
.m-main {
  overflow: hidden;
  .my-data{
    position: relative;
    .my-bg {
      width: 100%;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      .pdl40.my-user {
        padding-left:40upx
      }
      .my-user{
        position: relative;
        height: 200upx;
        padding: 24upx 32upx 64upx;
        line-height: 0;
        .user-info {
          display: flex;
          align-items: center;
          position: absolute;
          left: 30upx;
          top: 30upx;
          line-height: 0;
          .img-main{
            position: relative;
            display: inline-block;
            width: 112upx;
            height: 112upx;
            position: relative;
            .role-image {
              width: 100upx;
              height: 100upx;
              display: inline-block;
            }
            .user-sex{
              position: absolute;
              right: 4upx;
              bottom: 4upx;
              width: 30upx;
              height: 30upx;
              z-index: 1;
              @include rounded(50%);
              overflow: hidden;
            }
          }
          .user-head-name{
            display: inline-block;
            vertical-align: top;
            .name{
              height: 50rpx;
              font-weight: 500;
              font-size: 36rpx;
              color: #1F2021;
              line-height: 50rpx;
            }
            .edit{
              margin-top: 8upx;
              .text{
                height: 34rpx;
                font-weight: 400;
                font-size: 24rpx;
                color: #4E5569;
                line-height: 34rpx;
              }
              .icon-edit-more{
                vertical-align: middle;
                display: inline-block;
                margin-left: 18upx;
                @include iconImg(32, 32, '/business/icon-edit-more.png');
              }
            }
          }
          .role {
            color: #da4d01;
            background: #ffc746;
            @include rounded(30upx);
          }
        }
      }
    }
  }
  .my-card{
    position: absolute;
    left: 32upx;
    right: 32upx;
    bottom: 4upx;
    height: 94upx;
    padding-left: 38rpx;
    padding-right: 38rpx;
    .my-card-main{
      display: flex;
      justify-content: space-between;
      .li{
        position: relative;
        width: 96upx;
        .text{
          font-weight: 400;
          font-size: 24rpx;
          color: #4E5569;
          text-align: center;
        }
        .number{
          font-size: 36rpx;
          color: #1D2029;
          line-height: 50rpx;
          text-align: center;
          margin-bottom: 8upx;
        }
      }
    }
  }

}
.width-height-atuo{
  width: 100%;
  height: 100%;
}
.headimg-wrapper {
	position: absolute;
	height: 74px;
	width: 74px;
	top: 50%;
  left: 50%;
  transform: translate(-50%,-50%);
}
</style>
