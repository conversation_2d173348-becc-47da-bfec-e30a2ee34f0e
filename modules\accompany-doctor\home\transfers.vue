<template>
  <view class="page">
    <view class="title">云陪诊门店</view>
    <view v-if="cloudProviders.length > 0">
      <view class="content" v-for="(provider, index) in cloudProviders" :key="index">
        <view>
            <image class="logo" :src="file_ctx + (provider.storeLogo || provider.logo)" />
            <view class="logoTitle">{{provider.appName}}</view>
            <view class="visit-time" v-if="index === 0 && provider.timestamp">最近一次访问</view>
        </view>
        <view class="button" @click="gotoAccom(provider)">进入</view>
      </view>
    </view>
    <view v-else class="empty-tip">加载中...</view>
  </view>
</template>
<script>
    export default {
        data(){
            return {
                file_ctx: this.file_ctx,
                showAccompany: true,
                cloudProviders: [],
                defaultProviderId: '2124021789005144070'
            }
        },
        mounted() {
            // this.loadData();
        },
        onShow() {
            this.loadData();
        },
        methods: {
            async loadData() {
                try {
                    // 从本地存储中获取云陪诊记录列表（带时间戳）
                    let providerRecords = this.$common.getKeyVal('system', 'cloudProviderRecords', true);

                    // 确保providerRecords是数组
                    if (!providerRecords || !Array.isArray(providerRecords)) {
                        providerRecords = [];
                    }

                    // 检查是否有默认的小葫芦陪诊记录
                    const hasDefaultProvider = providerRecords.some(record => record.providerId === this.defaultProviderId);

                    // 如果没有默认记录，添加一个
                    if (!hasDefaultProvider) {
                        providerRecords.push({
                            providerId: this.defaultProviderId,
                            timestamp: new Date().getTime()
                        });
                    }

                    // 按时间戳倒序排序
                    providerRecords.sort((a, b) => b.timestamp - a.timestamp);

                    // 保存更新后的记录列表
                    this.$common.setKeyVal('system', 'cloudProviderRecords', providerRecords, true);

                    // 同时保持原来的列表兼容
                    let providerIdList = providerRecords.map(record => record.providerId);
                    this.$common.setKeyVal('system', 'cloudProviderIdList', providerIdList, true);

                    // 清空当前列表
                    this.cloudProviders = [];

                    // 获取每个服务商的详细信息
                    for (let record of providerRecords) {
                        try {
                            let {data} = await this.$api.accompanyDoctor.accompanyproviderQueryOne({id: record.providerId});
                            if (data) {
                                // 只保留时间戳用于排序
                                data.timestamp = record.timestamp;
                                this.cloudProviders.push(data);
                            }
                        } catch (error) {
                            console.error('获取服务商信息失败:', record.providerId, error);
                        }
                    }

                    // 如果列表为空，直接进入小葫芦陪诊
                    if (this.cloudProviders.length === 0) {
                        this.directEnterDefaultProvider();
                    }
                } catch (error) {
                    console.error('加载云陪诊列表失败:', error);
                    this.directEnterDefaultProvider();
                }
            },
            directEnterDefaultProvider() {
                // 直接进入默认的小葫芦陪诊
                this.$common.setKeyVal('system', 'cloudProviderId', this.defaultProviderId, true);
                this.$uniPlugin.toast('进入小葫芦陪诊中...');
                uni.redirectTo({url:`/modules/accompany-doctor/home/<USER>
            },
            gotoAccom(provider) {
                let providerId = provider.id;
                // 将当前选择的providerId保存到本地
                this.$common.setKeyVal('system', 'cloudProviderId', providerId, true);

                // 更新访问记录
                let providerRecords = this.$common.getKeyVal('system', 'cloudProviderRecords', true) || [];
                const timestamp = new Date().getTime();

                // 更新或添加记录
                const existingIndex = providerRecords.findIndex(record => record.providerId === providerId);
                if (existingIndex !== -1) {
                    providerRecords[existingIndex].timestamp = timestamp;
                } else {
                    providerRecords.push({
                        providerId,
                        timestamp
                    });
                }

                // 保存更新后的记录
                this.$common.setKeyVal('system', 'cloudProviderRecords', providerRecords, true);

                this.$uniPlugin.toast('进入陪诊服务中...');
                uni.redirectTo({url:`/modules/accompany-doctor/home/<USER>
            }
        },
    }
</script>
<style lang="scss">
.page{
    height: 100vh;
    width: 100vw;
    position: fixed;
    top: 0;
    left: 0;
    overflow: scroll;
    z-index: *********;
    padding: 32rpx;
    box-sizing: border-box;
    background: #F4F6FA;
}
.title{
    font-weight: 400;
    font-size: 28rpx;
    color: rgba(22, 135, 247, 1);
}
.content{
    margin-top: 24rpx;
    width: 100%;
    height: 274rpx;
    background: linear-gradient( 96deg, rgba(227,253,247,0.3) 0%, rgba(205,252,241,0.8) 100%), #FFFFFF;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20rpx 0 24rpx;
    box-sizing: border-box;
    .logo{
        width: 232rpx;
        height: 85.81rpx;
        border-radius: 10rpx;
    }
    .logoTitle{
        font-weight: 800;
        font-size: 28rpx;
        color: #22BF98;
    }
    .visit-time {
        color: #999;
        margin-top: 10rpx;
    }
    .button{
        width: 188rpx;
        height: 70rpx;
        background: #22BF98;
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        font-weight: 500;
        font-size: 28rpx;
        color: #FFFFFF;
        text-align: center;
        line-height: 70rpx;
    }
}
.empty-tip {
    text-align: center;
    margin-top: 100rpx;
    color: #999;
    font-size: 28rpx;
}
</style>
