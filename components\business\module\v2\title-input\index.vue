
<template>
  <view class="title-input clear-float" :class="{'bdt':defaultConfig.bdt,'bdb':defaultConfig.bdb,'horizontal':horizontal}">
    <view class="l-l" :class='defaultConfig.titleClass' :style="{'color': defaultConfig.titleColor,'width':horizontal ? labelWidth : '100%'}"  v-if="defaultConfig.showLabel">
      <text class="star" v-if="defaultConfig.required && !defaultConfig.nextRequest" >*</text>
      {{defaultConfig.label}}
      <text class="star" v-if="defaultConfig.required && defaultConfig.nextRequest" >*</text>
    </view>
    <view class="l-r">
      <input :disabled="disabled"
        :value='value'
        :placeholder-class="['placeholderclass',defaultConfig.titleClass]"
        :type="defaultConfig.type"
        :maxlength="defaultConfig.maxlength"
        @input="returnFn"
        :placeholder="placeholder ? placeholder : `请输入${defaultConfig.label}`"
        :style="defaultConfig.style"
        class="input"
      />
    </view>
  </view>
</template>

<script>
// v1 版el-input 的优化版 用法
// <title-input v-model="form.name" :config="config.name" :disabled="config.name.disabled"></title-input>
export default {
  data() {
    return {
      defaultConfig: {
        bdt: true,
        bdb: true,
        titleColor: '#333',
        textColor: '#333',
        label: '单行输入框',
        name: 'input',
        required: false,
        type: 'text',
        showLabel: true,
        style: {},
        maxlength:5000,

      },

    }
  },
  watch: {
    config: {
      handler(val) {
        this.copyConfig()
      },
      deep: true
    },
  },
  props: {
    // 水平显示
    horizontal:{
      type:Boolean,
      default:false,
    },
    // 水平显示时生效，支持px
    labelWidth:{
      type:String,
      default:'100px',
    },

    value: [String,Number],
    placeholder: String,
    // 是否禁用disable
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    }
  },
  mounted() {
      this.copyConfig()
  },
  methods: {
    /**
       * 初始化拷贝config对象
       */
    copyConfig() {
      const that = this
      const obj = that.config
      console.log('obj',obj)
      Object.keys(obj).forEach(function(key) {
        that.defaultConfig[key] = obj[key]
      })
    },
    /**
       * picker触发选中事件
       * @param e
       */
    returnFn(e) {
      const that = this
      this.$emit('input',e.detail.value)
    },
  }
}
</script>

<style lang="scss" scoped>
  .title-input{
    background-color:#ffffff;
    .l-l{
      line-height: 88upx;
      color: #333333;
      font-size:30upx;
    }
    .l-l.font36{
      font-size: 36upx;
    }
    .l-r{
      margin-bottom: 5px;
      .input {
        height: 80upx;
        line-height: 80upx;
        color: #333;
        font-size: 32upx;
        text-align: left;
        display: inline-block;
        vertical-align: middle;
        width: 100%;
        border: 1upx solid #ede9e9;
        border-radius: 10upx;
        padding: 0 20upx;
        box-sizing: border-box;
      }
    }
  }
  .title-input.horizontal{
    display: flex;
    align-items: center;

    .l-r{
      flex: 1;
      margin-bottom: 0;

      .input{
        width: 100%;
        text-align: right;
        border: none;
      }
    }
  }
  .star{
    color: #F85E4C;
    padding-top: 6upx;
    font-size: 32upx;
    display: inline-block;
    margin: 0 10upx;
  }
</style>
