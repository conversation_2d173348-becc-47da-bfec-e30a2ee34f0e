<template>
  <page>
    <view slot="content" class="body-main">
      <view class="m-main-body">
        <scroll-refresh class="scroll-refresh-main" :isShowEmptySwitch="false" :fixed="false" :isAbsolute="false" :up="up" :down="down" @scrollInit="mMainScrollInit" @returnFn="mMainReturnFn">
          <view class="evaluate-content">
            <information-list ref="informationListRef" :list="courseList" :entryType="entryType" :index="curIndex" :shareImg="shareImg"></information-list>
          </view>
          <view class="empty-box" slot="empty">
            <image
              class="empty-img"
              mode="aspectFill"
              :src="file_ctx + 'static/image/business/hulu-v2/icon-circle-empty.png'"
            ></image>
            <text class="empty-text">~ 暂无相关数据 ~</text>
          </view>
        </scroll-refresh>
      </view>
    </view>
  </page>
</template>

<script>
  import { mapState } from 'vuex'
  import UniIcons from '@/components/uni/uni-icons/uni-icons'
  import informationList from './components/information-list'
  export default {
    components:{
      UniIcons,
      informationList
    },
    data(){
      return{
        file_ctx: this.file_ctx,
        $appId: this.$appId,
        courseList:[],
        curIndex:0,
        down: {
          auto: false
        },
        up: {
          auto: false
        },
        mMainScrollObj: {}, // 模块下拉组件初始化
        entryType:null,
        productId:null,
        shareImg:null,
        demandIdList:null,
      }
    },
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId,
        publishInfo:state => state.publishInfo
      }),
    },
    onLoad(option){
      const query = this.$validate.isNull(this.$Route.query) ? option : this.$Route.query
      // console.log(query,'query123321')
      if(query?.demandIdList){
        this.demandIdList = query?.demandIdList
      }
      // this.entryType = query.entryType
      // this.productId = query.id
      // let title = query?.title
      // this.shareImg = query?.evaluateImg
      // if(title){
      //   uni.setNavigationBarTitle({
      //     title: this.entryType == 2 ? title : this.entryType == 3 ? title :  '企业动态'
      //   });
      // }
      this.$nextTick(() => {
        this.init()
      })
    },
    onShareAppMessage(res) {
      if (res.from === 'button') {// 来自页面内分享按钮
      }
      return {
        title: '帖子专题', //分享的名称
        path: 'modules/community/post-subject/index?query=' + encodeURIComponent(JSON.stringify({
          demandIdList:this.demandIdList
        })),
        mpId: this.$appId, //此处配置微信小程序的AppId
      }
    },
    mounted(){},
    methods:{
      mMainReturnFn(obj) {
        if (this.curIndex === 0) {
          this.courseReturnFn(obj, (data) => {
            obj.successCallback && obj.successCallback(data || [])
          })
        }
      },
      courseReturnFn(obj, successCallback) {
        const that = this
        // let time = [this.$timePlugin.parseTime(new Date().getTime() - **********, '{y}-{m}-{d}') + ' 00:00:00', this.$timePlugin.parseTime(new Date().getTime() + ********, '{y}-{m}-{d}') + ' 23:59:59']
        // console.log(time,'time-----')
        function queryPage(pageNum, pageSize, fn) {
          const param = {
            current: pageNum,
            size: pageSize,
            condition: {
              // accountId: that.accountId,
              // entryType: that.entryType,
              // productId:that.productId,
              // startPutawayTime:time[0], //添加发布时间
              // endPutawayTime:time[1],
              processStatus:2,
              putawayStatus:1,
              demandIdList:that.demandIdList?.split(',')
              // eiTopSwitch:1, //是否置顶
            }
          }
          that.$ext.community.postmessageQueryPage(param).then(res => {
            if (res && res.data.records) {
              fn(res.data.records.length > 0 ? res.data.records : [])
            }
          })
        }
        if (that.accountId) {
          queryPage(obj.pageNum, obj.pageSize, (data) => {
            if (obj.pageNum === 1) {
              that.courseList = []
            }
            that.courseList = that.courseList.concat(data)
            successCallback(data || [])
          })
        }
      },

      mMainScrollInit(scroll) {
        scroll.optUp.page.num = 1
        scroll.optUp.page.size = 10
        this.mMainScrollObj = scroll
      },

      init() {
        this.$nextTick(() => {
          this.mMainScrollObj.triggerDownScroll()
        })
      },
    },
 }
</script>

<style lang='scss' scoped>
.body-main{
  height: 100%;
  // padding:20upx;
  background-color: #f6f6f6;
  .m-main-body{
    height: 100%;
    .scroll-refresh-main{
      height: 100%;
      /deep/ .z-paging-content{
        border-radius: 13upx;
      }
    }
  }
  .evaluate-content{
    height: 100%;
  }
  /deep/.zp-scroll-view-super{
    .zp-scroll-view-container{
      .zp-scroll-view{
        .zp-paging-touch-view{
          .zp-paging-main{
            .zp-paging-container{
              flex: 0 !important;
              flex-direction: row !important;
              .zp-paging-container-content{
                width: 100%;
              }
              .empty-box {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                width: 100vw;
                // height: 556rpx;
                height: 100vh;
                background: #FFFFFF;
                border-radius: 16rpx;
              }
      
              .empty-img {
                width: 232rpx;
                height: 190rpx;
                margin-bottom: 22rpx;
              }
      
              .empty-text {
                font-size: 24rpx;
                color: #4E5569;
                line-height: 34rpx;
              }
            }
          }
        }
      }
    }
  }
}
</style>