<template>
  <view>
    <uni-popup ref="dialog" type="dialog">
      <uni-popup-dialog
        mode="input"
        title="信息填写"
        :beforeClose="true"
        @confirm="confirm"
        @close="close"
      >
        <view class="dialog-content-box">
          <view @tap="openSelector('hospitalId')">
            <title-selector :value="regForm.hospitalId" :config="config.hospitalId" :disabled="true" ref="selectorHospitalRef" />
          </view>
          <view @tap="openSelector('officeId')">
            <title-selector :value="regForm.officeId" :config="config.officeId" :disabled="true" ref="selectorOfficeRef" />
          </view>
          <title-input
            v-model="regForm.doctorName"
            horizontal
            :config="config.doctorName"
            :placeholder="config.doctorName.placeholder"
          ></title-input>
          <view class="form-item-box">
            <view class="form-item-label">
              <text v-if="config.workCardPic.required" class="star">*</text>
              {{ config.workCardPic.label }}
            </view>
            <title-img
              :config="config.workCardPic"
              @returnFn="
                (obj) => {
                  imgReturnFn(obj, 'workCardPic');
                }
              "
            >
            </title-img>
          </view>
          <!-- <view @tap="openSelector('doctorId')">
            <title-selector :value="regForm.doctorId" :config="config.doctorId" :disabled="true" ref="selectorDoctorRef" />
          </view> -->
        </view>
      </uni-popup-dialog>
    </uni-popup>
    <page-list-select
      ref="pageListHospitalRef"
      v-model="regForm.hospitalId"
      :show.sync="hospitalSelectorShow"
      :query-api="hospitalQueryPage"
      :title="'请选择' + config.hospitalId.label"
      name="hospitalName"
      labelKey="hospitalName"
      valueKey="id"
      @confirm="selectorConfirm($event, 'hospitalId')"
    />
    <page-list-select
      ref="pageListofficeRef"
      v-model="regForm.officeId"
      :show.sync="officeSelectorShow"
      :query-api="officeQueryPage"
      :title="'请选择' + config.officeId.label"
      name="name"
      labelKey="name"
      valueKey="id"
      @confirm="selectorConfirm($event, 'officeId')"
    />
    <page-list-select
      ref="pageListDoctorRef"
      v-model="regForm.doctorId"
      :show.sync="doctorSelectorShow"
      :query-api="doctorQueryPage"
      :title="'请选择' + config.doctorId.label"
      name="name"
      labelKey="name"
      valueKey="id"
      @confirm="selectorConfirm($event, 'doctorId')"
    />
  </view>
</template>

<script>
import { mapState } from 'vuex'
import env from '@/config/env'
import uniPopup from '@/components/uni/uni-popup/uni-popup.vue'
import uniPopupDialog from '@/components/uni/uni-popup-dialog/uni-popup-dialog.vue'
import titleSelector from '@/components/business/module/v1/title-selector/index.vue'
import titleInput from '@/components/business/module/v2/title-input/index'
import pageListSelect from '@/components/basics/page-list-select/index.vue'
import titleImg from "@/components/business/module/title-img/index"

const defaultForm = {
  hospitalId: '', // 医院
  officeId: '', // 科室
  // doctorId: '' // 医生
  doctorName: '', // 医生
  workCardPic: '' // 工作证
}

const defaultConfig = {
  hospitalId: {
    label: '医院',
    required: true,
    array: [],
    placeholder: '请选择医院',
    textColor: '#333 !important',
    titleWidth: '130rpx'
  },
  officeId: {
    label: '科室',
    required: true,
    array: [],
    placeholder: '请选择科室',
    textColor: '#333 !important',
    titleWidth: '130rpx'
  },

  doctorName: {
    label: '医生',
    required: true,
    array: [],
    placeholder: '请输入医生名称',
    textColor: '#333 !important',
    titleWidth: '130rpx',
    bdt: false
  },
  workCardPic: {
    label: '工作证',
    multiSelectCount: 1,
    count: 1,
    show: true,
    required: true,
    padding: '32rpx 0',
  }
}

export default {
  props: {
    editid: {
			type: [String, Number],
			default: null
		},
    data: {
			type: Object,
			default: null
		},
  },
  components: {
    uniPopup,
    uniPopupDialog,
    titleSelector,
    titleInput,
    pageListSelect,
    titleImg
  },
  data() {
    return {
      regForm: JSON.parse(JSON.stringify(defaultForm)),
      config: JSON.parse(JSON.stringify(defaultConfig)),
      hospitalQueryPage: this.$api.hospital.hospitalQueryPage,
      hospitalSelectorShow: false,
      officeQueryPage: this.$api.hospital.departmentsQueryPage,
      officeSelectorShow: false,
      doctorQueryPage: this.$api.hospital.crawlershospitaldoctorQueryPage,
      doctorSelectorShow: false,
    }
  },
  computed: {
    ...mapState('user', {
      accountId: state => state.accountId,
      curSelectUserInfo: state => state.curSelectUserInfo
    })
  },
  methods: {
    // #ifdef MP-WEIXIN
    handleClickTrack(type){
      getApp().globalData.sensors.track("PopupClick",
        {
          'page_name' : '直播页面',
          'popup_id' : 'dialog',
          'popup_name' : '直播报名',
          'click_type' : type == 1 ? '进入弹窗' : '取消弹窗',
        }
      ) 
    },
    // #endif
    imgReturnFn(obj, key) {
      this.regForm[key] = obj.map(item => item.dir)
    },
    open() {
      this.$refs.dialog.open()
      // #ifdef MP-WEIXIN
      this.handleClickTrack(1)
      // #endif
    },
    close() {
      this.$refs.dialog.close()
      // #ifdef MP-WEIXIN
      this.handleClickTrack(2)
      // #endif
    },
    async confirm() {
      const { centerUserId = '' } = this.curSelectUserInfo || {};
      const params = {
        ...this.regForm,
        userId: centerUserId,
        meetingId: this.editid,
        accountId: this.accountId,
        type: this.data.activityStatus === 2 ? 1 : 2, // 1: 观看直播, 2: 观看回访
        workCardPic: this.$validate.isNull(this.regForm.workCardPic) ? '' : this.regForm.workCardPic[0]
      }
      let result = true
      Object.keys(this.regForm).forEach(key => {
        if (this.config[key] && this.config[key].required && !this.regForm[key]) {
          result = false
          this.$uniPlugin.toast(`${this.config[key].label}不得为空`)
        }
      })
      if (!result) return

      this.$uniPlugin.loading()
      const res = await this.$api.cloudClassroom.meetingregisterInsert(params)
      this.$uniPlugin.hideLoading()
      this.$uniPlugin.toast(res.msg)
      this.close()
      this.$emit('confirm')
    },
    selectorConfirm(e, id) {
      switch(id) {
        case 'hospitalId':
          this.config.hospitalId.array = [{
            ...e,
            value: e.id,
            label: e.hospitalName
          }]
          setTimeout(() => {
            this.$refs.selectorHospitalRef.copyConfig()
            this.$refs.selectorHospitalRef.watchVal()
          })
          break
        case 'officeId':
          this.config.officeId.array = [{
            ...e,
            value: e.id,
            label: e.name
          }]
          setTimeout(() => {
            this.$refs.selectorOfficeRef.copyConfig()
            this.$refs.selectorOfficeRef.watchVal()
          })
          break
        case 'doctorId':
          this.config.doctorId.array = [{
            ...e,
            value: e.id,
            label: e.name
          }]
          setTimeout(() => {
            this.$refs.selectorDoctorRef.copyConfig()
            this.$refs.selectorDoctorRef.watchVal()
          })
          break
        default:
      }
    },
    openSelector(id) {
      switch(id) {
        case 'hospitalId':
          this.hospitalSelectorShow = true
          break
        case 'officeId':
          this.officeSelectorShow = true
          break
        case 'doctorId':
          this.doctorSelectorShow = true
          break
        default:
      }
    },
    async subscribeMessage() {
      // #ifdef MP-WEIXIN
			await this.$uniPlugin.subscribeMessage(['RR84vyC1xY3wEKPw5SqquBUSIwmhz0IzmvYraAhil4k'])
			const tmplId = 'RR84vyC1xY3wEKPw5SqquBUSIwmhz0IzmvYraAhil4k'
			uni.requestSubscribeMessage({
				tmplIds: [tmplId],
				success(res) {
					if (res[tmplId] === 'accept') {
						this.$uniPlugin.toast('消息订阅成功')
            // #ifdef MP-WEIXIN
            getApp().globalData.sensors.track("Subscription",
              {
                'content_belong_circle' : '',
                'function_name' : '直播开播提醒',
                'subscription_type':'一次性订阅'
              }
            )
            // #endif
					} else {
						this.$uniPlugin.toast('消息订阅失败')
					}
					const {
						centerUserId = ''
					} = this.curSelectUserInfo || {};
					const params = {
						appId: env.appId,
						templateId: tmplId,
						openId: this.$common.getCache('openId') || '',
						subscribeStatus: res[tmplId],
						businessType: 1, // 直播活动
						businessId: this.editid,
						accountId: this.accountId,
						userId: centerUserId
					}
					this.$api.common.wxsubscribemessagelogInsert(params)
					// 消息订阅调起成功，返回值'accept'、'reject'、'ban'分别代表用户对此条订阅是同意、拒绝、后台禁用
				},
				fail(err) {
					console.log('err：', err)
					//消息订阅调起失败
					this.$uniPlugin.toast('消息订阅失败err：' + JSON.stringify(err))
				}
			})
			// #endif
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content-box {
  width: 100%;
}
.form-item-box {
  display: flex;
  justify-content: space-between;
}
.form-item-label {
  .star {
    color: #f85e4c;
    padding-top: 6rpx;
    font-size: 32rpx;
    display: inline-block;
    margin: 0 10rpx;
  }
  display: inline-block;
  vertical-align: middle;
  line-height: 88rpx;
  color: #333333;
  font-size: 32rpx;
  width: 320rpx;
  word-break: break-all;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
