<template>
  <view class="content" :style="{ 'background-image': 'url('+ file_ctx + 'static/image/business/redpacket/receive-redpacket-bg.png' +')' }">
    <uni-nav-bar backgroundColor="rgba(0,0,0,0)" :border="false" fixed statusBar>
      <image :src="file_ctx + 'static/image/business/redpacket/icon-home.png'" class="icon-home" mode="aspectFill" slot="left" @tap="back" />
      <view class="nav-bar-title">活动中转页</view>
    </uni-nav-bar>
    <template v-if="loading">
      <view class="empty-data-box">
        <text class="empty-data-text">加载中...</text>
      </view>
    </template>
    <uni-popup type="center" ref="popup" id="redTasksPopup" :is-maskClick="false">
      <view
        class="redpacket-box"
        :class="{
          'receive': !open,
          'no-receive': open
        }"
        @tap="openRedpacket"
      >
        <view v-if="open" class="redpacket-content">
          <view class="redpacket-title">{{ redpacketObj ? '恭喜获取' : '温馨提示' }}</view>
          <view class="redpacket-sub-title">{{ redpacketObj ? '任务奖励红包' : '暂无可领取红包' }}</view>
          <view class="redpacket-price">
            <text class="redpacket-price-text">{{ redpacketObj ? redpacketObj.totalAmountText : '0' }}</text>
            <text class="redpacket-price-unit">元</text>
          </view>
          <view class="redpacket-desc">{{ redpacketObj ? '红包已放入微信钱包，请到【我的-服务-钱包】查看' : '可继续扫码，完成关注任务领红包' }}</view>
          <view class="redpacket-btn" @tap.stop="receive">{{ redpacketObj ? '收下了' : '回到首页' }}</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import uniPopup from '@/components/uni/uni-popup/uni-popup.vue'
import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'
export default {
  components: {
    uniNavBar,
    uniPopup
  },
  data() {
    return {
      file_ctx: this.file_ctx,
      redpacketObj: null,
      open: false, // 红包的打开状态
      loading: false
    }
  },
  onLoad() {
    this.init()
  },
  methods: {
    // #ifdef MP-WEIXIN
    handleClickTrack(type){
      getApp().globalData.sensors.track("PopupClick",
        {
          'page_name' : '做任务领红包',
          'popup_id' : 'redTasksPopup',
          'popup_name' : '领红包弹窗',
          'click_type' : type == 1 ? '进入弹窗' : '取消弹窗',
        }
      ) 
    },
    // #endif
    goHome() {
      this.$navto.replaceAll('Index')
    },
    async openRedpacket() {
      if (this.open) return
      await this.receiveRedPacket()
      this.open = true
    },
    async receive() {
      this.goHome()
    },
    async receiveRedPacket() {
      this.$uniPlugin.loading()
      const openId = await this.$ext.wechat.getOpenId()
      await this.$api.redpacket.entpayorderEntpay({ openid: openId, mchBillno: this.redpacketObj.mchBillNo }).catch(() => { this.$uniPlugin.hideLoading() })
      this.$uniPlugin.hideLoading()
    },
    async init() {
      this.loading = true
      await this.getWaitReceiveRedpacket().catch(() => { this.loading = false })
      this.loading = false
      if (!this.redpacketObj) this.open = true
      this.$refs.popup.open()
      // #ifdef MP-WEIXIN
      this.handleClickTrack(1)
      // #endif
    },
    async getWaitReceiveRedpacket() {
      const openId = await this.$ext.wechat.getOpenId()
      const res = await this.$api.redpacket.waitReceiveList({ openId })
      const data = res.data || []
      this.redpacketObj = this.$validate.isNull(data) ? null : data.find(item => item.orderStatus === 2)
      if (!this.redpacketObj) return
      this.redpacketObj.totalAmountText = this.$accurateConversion.divide(this.redpacketObj.totalAmount, 100)
    },
    back() {
      let pages = getCurrentPages() // 获取栈实例
      if (pages.length > 1) {
        this.$navto.back()
      } else {
        this.goHome()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  background-repeat: no-repeat;
  background-size: 750rpx 1432rpx;
  background-position: top;
  background-color: #F4F6FA;
}
.icon-home {
  width: 64rpx;
  height: 64rpx;
}
.nav-bar-title {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 32rpx;
  color: #fff;
  line-height: 44rpx;
}
.redpacket-box {
  position: relative;
  width: 702rpx;
  height: 806rpx;
  background-size: 702rpx 806rpx;
  background-repeat: no-repeat;
  backface-visibility: hidden;/*当元素为反面的时候设置为不可见*/
  transition: all 1s;/*过渡效果*/
  background-image: url($imgUrl + '/business/redpacket/redpacket-open.png');
  &.receive {
    background-image: url($imgUrl + '/business/redpacket/redpacket.png');
  }
  &.no-receive {
    animation: turn 1s;
    background-image: url($imgUrl + '/business/redpacket/redpacket-open.png');
  }
  @keyframes turn {
    0% {
      transform: rotateY(0deg);
    }
    100% {
      transform: rotateY(-360deg);
    }
  }
}
.redpacket-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: show 1s;
  @keyframes show {
    0% {
      opacity: 0;
    }
    50% {
      opacity: 0;
    }
    51% {
      opacity: 1;
    }
    100% {
      opacity: 1;
    }
  }
}
.redpacket-title {
  font-weight: 600;
  font-size: 40rpx;
  color: #FFFFFF;
  line-height: 56rpx;
  margin-top: 74rpx;
  padding-left: 9rpx;
}
.redpacket-sub-title {
  font-weight: 600;
  font-size: 32rpx;
  color: #802E10;
  line-height: 44rpx;
  margin-top: 52rpx;
}
.redpacket-price {
  display: flex;
  align-items: flex-end;
  margin-top: 8rpx;
}
.redpacket-price-text {
  font-weight: 600;
  font-size: 88rpx;
  color: #EB413A;
  line-height: 124rpx;
  margin-right: 4rpx;
}
.redpacket-price-unit {
  font-weight: 400;
  font-size: 32rpx;
  color: #FF262D;
  line-height: 44rpx;
  margin-bottom: 24rpx;
}
.redpacket-desc {
  font-size: 24rpx;
  color: #6B7182;
  line-height: 34rpx;
  width: 288rpx;
  text-align: center;
  margin-top: 22rpx;
}
.redpacket-btn {
  position: absolute;
  bottom: 132rpx;
  width: 454rpx;
  height: 114rpx;
  text-align: center;
  font-weight: 600;
  font-size: 40rpx;
  color: #802E10;
  line-height: 114rpx;
}
.empty-data-box {
  padding-top: 500rpx;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
}
.empty-data-text {
  font-size: 28rpx;
  color: #1D2029;
  line-height: 40rpx;
}
/deep/ .uni-popup__wrapper.uni-custom.center .uni-popup__wrapper-box {
  max-width: 100%;
}
</style>