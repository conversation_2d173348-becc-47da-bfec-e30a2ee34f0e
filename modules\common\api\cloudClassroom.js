import request from '@/common/util/request'
import env from '@/config/env'

/**
 * 云课堂工具类，
 * 注意：API接口只做请求数据和格式化数据的事情，其他事务一概不处理
 */
export default {
  // 获取云课堂列表
  getMeetingQueryPage(param){
    const url = env.ctx + 'dm/api/v1/meeting/query/page'
    return request.postJson(url, param)
  },
  // 单一查询
  // /dm/api/v1/meeting/query/one
  getMeetingQueryOne(param){
    const url = env.ctx + 'dm/api/v1/meeting/query/one'
    return request.get(url, param)
  },

  // 获取聊天记录 /v1/meetingchat/query/list
  getMeetingChatQueryList(param){
    const url = env.ctx + 'dm/api/v1/meetingchat/query/list'
    return request.get(url, param)
  },

  // 评论数据
  meetingchatinsert(param,str){
     let url = env.ctx + 'dm/api/v1/meetingchat/insert'
    if(str && str != ''){
      url += '?' + str
    }

    return request.postJson(url, param)
  },
  // 互动栏目-根据多参数进行列表查询
  meetingcolumnQueryList(param) {
    const url =env.ctx + 'dm/api/v1/meetingcolumn/query/list'
    return request.get(url, param)
  },

  // 根据会议id获取视频回放
  meetingplaybackGetPlaybackByMeetingid(param) {
    const url = env.ctx + 'dm/api/v1/meetingplayback/get/playback/by/meetingid'
    return request.get(url, param)
  },

  // 获取直播列表
  getMeetingQueryList(param){
    const url = env.ctx + 'dm/api/v1/meeting/query/list'
    return request.get(url, param)
  },
  // 点赞收藏分享/行为记录流水 /dm/api/v1/commoncollectlikes/insert  post
  postcommoncollectlikes(param){
    const url = env.ctx + 'dm/api/v1/commoncollectlikes/insert'
    return request.postJson(url, param)
  },

  // 获取点赞状态
  getcommoncollectlikes(param){
    const url = env.ctx + 'dm/api/v1/commoncollectlikes/query/list'
    return request.get(url, param)
    // const url = env.ctx + 'dm/api/v1/commoncollectlikes/delete/one/' + param.id
    // return request.delete(url)
  },



  // 取消点赞状态 /dm/api/v1/commoncollectlikes/delete/one
  cannelcommoncollectlikes(param){
    const url = env.ctx + 'dm/api/v1/commoncollectlikes/delete/one/' + param.id
    return request.delete(url)
  },

  // 预约取消或者点亮
  postcommoncollectlikesupdate(param){
    const url = env.ctx + 'dm/api/v1/commoncollectlikes/add/or/update'
    return request.postJson(url, param)
    // const url = env.ctx + 'dm/api/v1/commoncollectlikes/delete/one/' + param.id
    // return request.delete(url)
  },

  // 进入课堂创建用户 /dm/api/v1/meetingviewuser/save/view/user
  meetingviewusersaveuser(param){
    const url = env.ctx + 'dm/api/v2/meetingviewuser/save/view/user'
    return request.postForm(url, param)
  },

  // 观看访问记录
  meetingviewlogvisit(param){
    const url = env.ctx + 'dm/api/v1/meetingviewlog/visit'
    return request.get(url, param)
  },

  // 获取参会人员观看状态
  getMeetingchatByUseridAndMainId(param) {
    const url =  env.ctx +'dm/api/v1/meetingviewuser/get/meetingchat/by/userid/and/mainId'
    return request.get(url, param)
  },

  // 获取直播栏目 dm/api/v1/
  getMeetingclassifyQueryList(param){
    const url =  env.ctx +'dm/api/v1/meetingclassify/query/list'
    return request.get(url, param)
  },

  // 获取当前用户提交登记
  meetingregisterCurrentRegister(param) {
    const url = env.ctx  + 'dm/api/v1/meetingregister/current/register'
    return request.get(url, param)
  },

  // 提交登记直播
  meetingregisterInsert(param) {
    const url = env.ctx  + 'dm/api/v1/meetingregister/insert'
    return request.postJson(url, param)
  }

}
