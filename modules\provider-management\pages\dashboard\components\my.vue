<template>
  <view class='my'>
    <view class="my-data">
      <view :style="'height:' + statusBarHeight + 'px;'"></view>
      <view class="top-nav">
        <view class="top-nav-l"></view>
        <view class="top-nav-c">我的</view>
      </view>
      <view class="update-data">
        <view class="update-data-l">
          <view class="data-l-img"><image class="img" :src="file_ctx + detailObj.logo"></image></view>
          <view class="data-l-info">
            <view class="title">{{ detailObj.providerName }}</view>
            <view class="time">入驻时间：{{ detailObj.createTime }}</view>
          </view>
        </view>
        <view class="update-data-r" @click="$navto.push('ProviderUpdateData',{...detailObj})">修改资料</view>
      </view>

      <!-- <view class="my-accompany" @click="$navto.push('accompanyIndex')">
        <view class="my-accompany-l">
          <view class="accompany-l-img"></view>
          <view class="accompany-l-name">我的陪诊师</view>
        </view>
        <view class="my-accompany-r"><image class="head-r-img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-right-arrows.png'"></image></view>
      </view> -->

    </view>
  </view>
</template>

<script>
  export default {
    props: {
      isShow:{
        type:Boolean,
        default:false,
      },
      detailObj:{
        type:Object,
        default(){
          return {}
        }
      }
    },
    data(){
      return{
        $navto:this.$navto,
        file_ctx:this.file_ctx,
        statusBarHeight:0,
        // detailObj:null,
      }
    },
    watch: {
      isShow(){
        if(this.isShow){
          this.accompanyproviderQueryOne()
        }
      }
    },
    onLoad(){
      // this.accompanyproviderQueryOne()
    },
    mounted(){
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
      // this.accompanyproviderQueryOne()
    },
    methods:{
      // accompanyproviderQueryOne(){
      //   const providerId = this.$common.getKeyVal('user', 'providerId',true)
      //   this.$api.accompanyDoctor.accompanyproviderQueryOne({id:providerId}).then(res=>{
      //     this.detailObj = {...res.data,createTime:this.$common.formatDate(new Date(res.data.createTime), 'yyyy-MM-dd').replace(/-/g, '.')}
      //   })
      // },
    },
 }
</script>

<style lang='scss' scoped>
.img{
  width: 100%;
  height: 100%;
}
.my-data{
  position: relative;
  height: 596rpx;
  background: linear-gradient( 180deg, #00B484 0%, #F4F6FA 100%);
}
.top-nav{
  // position: fixed;
  width: calc(100% - 16rpx);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  line-height: 40px;
  // padding: 0 32rpx;
  font-size: 32rpx;
  color: #FFFFFF;
  .top-nav-l{
    display: flex;
    width: 48rpx;
    height: 48rpx;
    image{
      width: 100%;
      height: 100%;
    }
  }
  .top-nav-c{
    flex: 1;
    text-align: center;
    height: 44rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #2D2F38;
    line-height: 44rpx;
    margin-right: 48rpx;
  }
}
.update-data{
  position: relative;
  display: flex;
  justify-content: space-between;
  background: #FFFFFF;
  border-radius: 16rpx;
  margin: 20rpx 32rpx;
  padding:40rpx 24rpx;
  .update-data-l{
    display: flex;
    align-items: center;
   .data-l-img{
    width: 112rpx;
    height: 112rpx;
    border-radius: 50%;
   }
   .data-l-info{
    margin-left: 16rpx;
    .title{
      font-size: 30rpx;
      color: #1D2029;
      font-weight: 600;
      line-height: 42rpx;
      margin-bottom: 8rpx;
    }
    .time{
      font-size: 24rpx;
      color: #1D2029;
      line-height: 34rpx;
    }
   }
  }
  .update-data-r{
    position: absolute;
    right: 0;
    top: 62rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 136rpx;
    height: 56rpx;
    background: #F4F6FA;
    border-radius: 28rpx 0rpx 0rpx 28rpx;
    font-size: 24rpx;
    color: #1F2021;
  }
}
.my-accompany{
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 38rpx 24rpx 40rpx 34rpx;
  margin: 0 32rpx;
  .my-accompany-l{
    display: flex;
    align-items: center;
    .accompany-l-img{
      height: 32rpx;
      width: 34rpx;
      background-color: skyblue;
    }
    .accompany-l-name{
      font-size: 30rpx;
      color: #1D2029;
      margin-left: 26rpx;
    }
  }
  .my-accompany-r{
    display: flex;
    width: 32rpx;
    height: 32rpx;
    image{
      width: 100%;
      height: 100%;
    }
  }
}
</style>
