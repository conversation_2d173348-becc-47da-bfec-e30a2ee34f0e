<template>
  <page>
    <view slot="content" class="content" :style="{ maxhHeight: `${scrollHeight}px` }">
      <uni-nav-bar backgroundColor="rgba(0,0,0,0)" :border="false" fixed statusBar>
        <image :src="file_ctx + 'h5/home/<USER>/icon-home.png'" class="icon-home" mode="aspectFill" slot="left" @tap="back" />
        <view class="nav-bar-title">支付出袋</view>
      </uni-nav-bar>
      <view class="content-bg"></view>
      <view class="content-cell">
        <view class="banner image">
          <image :src="imgList.banner" class="banner-img" mode="widthFix" />
        </view>
        <view class="content-center">
          <image
            v-if="!showError"
            mode="aspectFill"
            :src="imgList.whiteTop"
            class="whiteTop"
          />

          <view class="content-code centerC">
            <view v-if="isShow" class="button-list">
              <view class="guide-box">
                <text class="guide-text">
                  <template v-if="!appId">点击下方按钮支付出袋</template>
                  <template v-else>点击下方按钮领取环保袋</template>
                </text>
                <image mode="aspectFit" :src="file_ctx + 'h5/home/<USER>/icon-guide-bottom.png'" alt="" class="icon-guide-bottom">
              </view>
              <button
                v-if="!appId"
                class="button pay-btn"
                type="primary"
                @click="payBtnClick"
              >
                <div class="tag-box pay-box" :style="'background-image: url(' + file_ctx + 'h5/home/<USER>/icon-pay-tag.png);'">付费</div>
                <span>点击这里</span>
                <span>，</span>
                <span>支付出袋</span>
              </button>
              <button
                v-else
                class="button"
                type="primary"
                @tap="freeBtnClick"
              >
                <div class="tag-box free-box" :style="'background-image: url(' + file_ctx + 'h5/home/<USER>/icon-free-tag.png);'">免费</div>
                <span>点击这里</span>
                <span>，</span>
                <span>免费领袋</span>
              </button>
            </view>
            <view v-else>
              请重新扫码领袋
            </view>
          </view>
        </view>
        <!-- 底部 -->
        <view class="content-bottom">
          <image class="logo" mode="aspectFit" :src="imgList.logo"></image>
          <view class="tips-text" @click="handleTel('************')">
            <text>遇到出袋问题请拨打客服电话：</text>
            <text>************</text>
          </view>
        </view>
      </view>
      <!-- #ifndef H5 -->
      <sa-hover-menu />
      <!-- #endif -->
      <mobile-authorization ref="mobileAuthorizationRef" @add-record="addMobileAuthorizationRecord" @confirm="payBtnClick" />
    </view>
  </page>
</template>

<script>
import env from '@/config/env'
import common from '@/common/util/main'
import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'
import mobileAuthorization from '../components/mobile-authorization'
import { mapState } from 'vuex'
export default {
  name: 'PacketPay',
  components: {
    uniNavBar,
    mobileAuthorization
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $timePlugin: this.$timePlugin,
      //业务参数
      associateId:'',
      deviceId:'',
      entryWxId:'',
      entryType:'',

      height: '0px',
      accountId: null,
      appId: null,
      appPath: '',
      wxId: '',
      authWxId: '',
      accessTime: '',
      pushTime: '',
      authOpenId: '',
      appletUrl: '',
      authType: '',
      imgList: {
        bg: env.file_ctx + 'h5/home/<USER>/home-bg.png',
        white: env.file_ctx + 'h5/home/<USER>',
        code: env.file_ctx + 'h5/home/<USER>',
        logo: env.file_ctx + 'h5/home/<USER>/icon-logo.png',
        xiaobao: env.file_ctx + 'h5/home/<USER>',
        repair: env.file_ctx + 'h5/home/<USER>/device-repair.png',
        banner: env.file_ctx + 'h5/home/<USER>/home-banner.png',
        close: env.file_ctx + 'h5/home/<USER>',
        guide: env.file_ctx + 'h5/home/<USER>',
        hand: env.file_ctx + 'h5/home/<USER>/press-tips.gif',
        tips: env.file_ctx + 'h5/home/<USER>',
        whiteTop: env.file_ctx + 'h5/home/<USER>/device-bag.png'
      },
      scrollHeight: uni.getSystemInfoSync().screenHeight,
      tips: '',
      isShow: true,
      isGoChannelsUserProfile: false, // 是否前往视频号个人主页
      visitTime: Date.now(),
      deviceInfo: {}
    }
  },
  computed: {
    ...mapState('user', {
      isLogin: state => state.isLogin,
      curSelectUserInfo: state => state.curSelectUserInfo, // 当前登录用户信息
      curAccountId: state => state.accountId,
      fansRecord: state => state.fansRecord
    }),
  },
//   onShareAppMessage(res) {
//     if (res.from === 'button') {// 来自页面内分享按钮
//       console.log(res.target)
//     }
//     return {
//       title: this.regForm.title, //分享的名称
//       path: 'modules/business/information/detail?query='+ encodeURIComponent(JSON.stringify({
//         id: this.regForm.id
//       })),
//       mpId:this.$appId, //此处配置微信小程序的AppId
//       imageUrl: this.file_ctx + this.regForm.coverPath
//     }
//   },
// //分享到朋友圈
//   onShareTimeline(res) {
//     return {
//       title: this.regForm.title,
//       path: 'modules/business/information/detail?query='+ encodeURIComponent(JSON.stringify({
//         id: this.regForm.id
//       })),
//       imageUrl: this.file_ctx + this.regForm.coverPath
//     }
//   },
  async onLoad(paramsObj) {
    const query = this.$Route.query
    if (!this.$validate.isNull(query)) {
      this.associateId = query.associateId
      this.deviceId = query.deviceId
      this.entryWxId = query.entryWxId
      this.entryType = query.entryType
      this.sendType = query.sendType
      this.appId = query.sourceAppId
      this.wxId = query.wxId
      this.authWxId = query.authWxId
      this.accessTime = query.accessTime
      this.pushTime = query.pushTime
      this.authOpenId = query.authOpenId
      this.appletUrl = query.appletUrl ? decodeURIComponent(query.appletUrl) : query.appletUrl
      this.accountId = query.accountId
      this.authType = query.authType
      if (!this.appId) {
        const isOpenPhoneAuth = await this.getDeviceInfo()
        if (!isOpenPhoneAuth) this.payBtnClick()
      }
    }
    // #ifdef MP-WEIXIN
    // wx.showShareMenu({
    //   withShareTicket:true,
    //   //设置下方的Menus菜单，才能够让发送给朋友与分享到朋友圈两个按钮可以点击
    //   menus:["shareAppMessage","shareTimeline"]
    // })
    // #endif
  },
  onShow() {
    if (this.isGoChannelsUserProfile) {
      this.back()
      return
    }

    let visitTimeNew = Date.now();
    let visitTime = this.visitTime
    if (visitTime!=null){
      if ((visitTimeNew - visitTime)/1000 >= 20) {
        this.$navto.replaceAll('Index')
      }
    }
  },
  methods: {
    // 微信广点通广告
    getWxAdvertiseBusiness() {
      return new Promise((resolve, reject) => {
        if (this.$validate.isNull(this.deviceInfo)) return resolve(false)
        const { gdtAdSwitch = 2 } = this.deviceInfo
        if (gdtAdSwitch === 2) return resolve(false)
        this.$navto.replaceAll('PacketWxFullScreenAd')
        return resolve(true)
      })
    },
    /**
     * @param {number} type 类型
     * 1、自定义授权窗口-曝光
     * 2、自定义授权窗口-确认授权
     * 3、自定义授权窗口-取消
     * 4、官方授权窗口-允许
     * 5、官方授权窗口-取消
     * 6、成功授权手机
     */
    addMobileAuthorizationRecord(type) {
      const { centerUserId = '' } = this.curSelectUserInfo || {}
      const { nickName } = this.fansRecord
      this.$api.packet.packetoperationrecordInsert({
        accountId: this.curAccountId,
        userId: centerUserId,
        operationType: type,
        nickName
      })
    },
    // 获取当前用户是否已绑定了手机号（已授权过手机号）
    getUserIsBindPhone() {
      return new Promise((resolve, reject) => {
        this.$ext.user.getCommunityFansRecord().then(() => {
          this.$ext.user.getFansBindRecord().then(res => {
            const { phone } = res || {}
            return resolve(!!phone)
          }).catch(() => {
            resolve(false)
          })
        }).catch(() => {
          resolve(false)
        })
      })
    },
    // 判断是否打开授权手机号弹窗
    showMobileAuthorizationFn() {
      return new Promise(async (resolve, reject) => {
        const isBind = await this.getUserIsBindPhone().catch(() => { resolve(false) })
        if (isBind) return resolve(false)
        resolve(true)
        this.$refs.mobileAuthorizationRef.open()
        this.addMobileAuthorizationRecord(1)
      })
    },
    /**
     * 获取设备信息，用于判断是否开启了手机号授权
     */
    getDeviceInfo(){
      return new Promise(async (resolve, reject) => {
        const that = this
        if(!that.deviceId || that.deviceId === 'null') return resolve(false)
        const res = await that.$api.packet.getDeviceInfoByDeviceId({ deviceId: that.deviceId}).catch((err) => {
          resolve(false)
        })
        this.deviceInfo = res.data
        if (res.data.openPhoneAuth === 1) {
          const isOpenPopup = await that.showMobileAuthorizationFn()
          return resolve(isOpenPopup)
        }
        resolve(false)
      })
    },
    back() {
      let pages = getCurrentPages() // 获取栈实例
      if (pages.length > 1) {
        this.$navto.back()
      } else {
        this.$navto.replaceAll('Index')
      }
    },
    freeBtnClick() {
      if (this.authType == 7) return this.navToChannelsUserProfile()
      this.jumpApp()
    },
    navToChannelsUserProfile() {
      wx.openChannelsUserProfile({
        finderUserName: this.appId,
        success: () => {
          this.isGoChannelsUserProfile = true
        }
      })
    },
    jumpApp() {
      const vm = this
      uni.navigateToMiniProgram({
        appId: this.appId,
        path: this.appletUrl || '',
        success(res) {
          // 打开成功
          vm.isShow = false
          const accountId = vm.accountId
          vm.$api.packet.updateAccountRecommendWxIdCache({ accountId, wxId: vm.wxId }),
          vm.$api.packet.uaInsertEvent({
            appId: vm.appId,
            timestamp: "1",
            nonce: "1",
            signature: "1",
            deviceId: vm.deviceId,
            gbWxId: vm.authWxId,
            gbOpenId: vm.authOpenId,
            gbAccountId: vm.accountId,
            miniWxId: vm.wxId,
            pushTime: vm.pushTime || new Date().getTime(),
            // 业务需要参数
            miniOpenId: vm.authOpenId,
            accessTime: vm.accessTime || new Date().getTime(),
            operationType: 6
          })
        },
        fail(err) {
          console.log('err------------', err)
          vm.$uniPlugin.toast('跳转失败：' + JSON.stringify(err))
        }
      })
    },
    handleTel(text){
      uni.makePhoneCall({
        phoneNumber:text
      })
    },
    payBtnClick() {
      // console.log(d)
      const that = this
      const associateId = this.associateId
      const deviceId = this.deviceId
      const entryWxId = this.entryWxId
      const entryType = this.entryType
      const sendType = this.sendType
      const data = {
        associateId,
        deviceId,
        entryType,
        sendType
      }
      if (!associateId && !deviceId && !entryWxId && !entryType){
        that.$uniPlugin.toast('参数异常，请重新扫码！')
        return
      }
      this.$uniPlugin.toast('请求支付中')
      entryWxId ? data.entryWxId = entryWxId : ''
      this.$api.pay.payMiniApp(data).then(res => {
        console.log(res)
        that.$uniPlugin.hideLoading()
        const payInfo = JSON.parse(res.data.payInfo)
        let src = null
        // res.extendParams ? src = res.extendParams.freeLink : src = null
        console.log(payInfo)
        wx.requestPayment({
          timeStamp: payInfo.timeStamp,
          nonceStr: payInfo.nonceStr,
          package: payInfo.package,
          signType: payInfo.signType,
          paySign: payInfo.paySign,
          success(res) {
            that.getWxAdvertiseBusiness().then(adRes => {
              if (adRes) return
              that.$navto.replace('WebHtmlView', { src: 'https://gift.greenboniot.cn/wx/#/result', title: "领袋结果页" })
            })
            console.log(res)
          },
          fail(err) {
            console.log(err)
            if (err.errMsg.indexOf('cancel') == -1) {
              console.log('not')
            } else {
              console.log('has')
            }
          },
          complete(res) {
            uni.hideLoading()

          }
        })
      }).catch(() => {
        uni.hideLoading()
      })
    },
  }
}
</script>

<style lang="scss" scoped>
@import '../common.scss';
</style>
