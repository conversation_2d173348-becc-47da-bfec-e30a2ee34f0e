<template>
    <uniPopup type="bottom" ref='uniPopup' @change="changeShow">
      <view class="confirm">
        <view class="clickIn">{{getClockTitle}}</view>
        <view class="">
          <view class="clockInIcon" @click="clockIn">
            <image class="clockInBg" v-if="clockBefore" :src="clockInBg" mode=""></image>
            <image class="clockInBg" v-else :src="clockInBgAfter" mode=""></image>
            {{clockBefore ? '快捷打卡' : '已打卡'}}
            <view class="" v-if="clockInTime">{{clockInTime}}</view>
          </view>
        </view>
        <view class="">
          <view class="clickIn">上传图片（最多上传6张图片）</view>
          <title-img :config="{multiSelectCount:6}" @returnFn="imgReturnFn" :cData="imageObj"></title-img>
        </view>
        <!-- 底部 -->
        <view class="bottomBtn">
          <view class="comBtn" @click="changeOrder">确定</view>
        </view>
      </view>
    </uniPopup>
</template>

<script>
  import { mapState } from "vuex";
  import TitleImg from "@/components/business/module/title-img/index"
  import uniPopup from '@/components/uni/uni-popup/uni-popup.vue'
  import getPosition from '@/utils/getPosition'

  export default{
    components: {
        uniPopup,
        TitleImg
    },
    props:{
      openFlag:{
        type:Boolean,
        default:false
      },
      clockInType:{
        type:Number,
        default:1
      },
      orderDetails:{
        type:Object,
        default:{}
      }
    },
    watch:{
      openFlag(n){
          if(n){
            this.$refs.uniPopup.open()
          }else{
            this.$refs.uniPopup.close()
          }
      }
    },
    data(){
      return {
        clockInBg: this.$static_ctx + "image/business/hulu-v2/clockInBg.png",
        clockInBgAfter: this.$static_ctx + "image/business/hulu-v2/clockInBgAfter.png",
        selectIndex:'',
        imageObj:[],
        clockBefore:true,
        clockInTime:'',
        clockInOptions:null
      }
    },
    computed: {
      ...mapState("user", {
        accountId: (state) => state.accountId,
        fansRecord: (state) => state.fansRecord,
      }),
      getClockTitle(){
        let clockTitleMap = {'1':'签到打卡','4':'接到客户打卡','2':'签出打卡'}
        return clockTitleMap[this.clockInType.toString()]
      }
    },
     mounted() {
    },
    methods:{
      imgReturnFn(imageObj){
        this.imageObj = imageObj
      },
      timestampToDateTime(timestamp) {
        // 创建一个新的Date对象，传入的时间戳是以毫秒为单位的
        var date = new Date(timestamp);
        // 获取年、月、日、时、分、秒
        var hour = ("0" + date.getHours()).slice(-2); // 获取小时，并补零
        var minute = ("0" + date.getMinutes()).slice(-2); // 获取分钟，并补零
        var second = ("0" + date.getSeconds()).slice(-2); // 获取秒数，并补零

        // 返回格式化的字符串
        return `${hour}:${minute}:${second}`;
      },
      changeShow(res){
        !res.show && this.$emit('change',res.show)
      },
      async clockIn(options){
        if(!this.clockBefore) return
        uni.showLoading({title: '加载中',mask: true})

        try {
          console.log('getPosition',getPosition);
          console.log('getPosition.initLocationPerm',getPosition.initLocationPerm);
          let LocationPerm = await getPosition.initLocationPerm(true)
          console.log('LocationPerm',LocationPerm);

          if(!LocationPerm) {
            uni.hideLoading()
            return
          }

          let {lat,lng} = LocationPerm
          let queryOptions = {
            businessType:4,
            businessId:this.orderDetails.id,
            latitude: lat,
            longitude: lng,
            // type: this.signType // 类型:1-签入，2-签出，4-接到客户打卡
            type:this.clockInType,
          }
          console.log('queryOptions',queryOptions);

          let {data} = await this.$api.activity.savesigninlogInsert(queryOptions)
          this.clockInOptions = data;

          uni.showToast({
            icon:"none",
            mask:true,
            title:'签到成功'
          })

          this.clockBefore = false;
          this.clockInTime = this.timestampToDateTime(+new Date())

        } catch(e) {
          console.log('请求失败',e);
          if(e.msg === '当前时间不在打卡时间范围内!'){
            uni.showToast({
              icon:"none",
              mask:true,
              title:e.msg,
              duration:3000,
            })
          } else {
            uni.showToast({
              icon:"none",
              mask:true,
              title:'签到失败',
              duration:2000,
            })
          }
        } finally {
          uni.hideLoading()
        }
      },
      async changeOrder(){
        if(!this.clockInOptions){
          uni.showToast({
            icon:"none",
            title:'请先签到'
          })
          return
        }
        if(this.imageObj.length == 0 && this.clockInType !== 4){
          uni.showToast({
            icon:"none",
            title:'请上传图片'
          })
          return
        }
        uni.showLoading({title: '加载中',mask: true})

        try {
          console.log('getPosition',getPosition);
          console.log('getPosition.initLocationPerm',getPosition.initLocationPerm);

          let LocationPerm = await getPosition.initLocationPerm(true)
          console.log('LocationPerm',LocationPerm);
          if(!LocationPerm) {
            uni.hideLoading()
            return
          }

          let {lat,lng} = LocationPerm
          let queryOptions = {
            id:this.clockInOptions.id,
            businessType:4,
            businessId:this.orderDetails.id,
            imagePath:this.imageObj.map(e=>e.filePath).join(),
            latitude: lat,
            longitude: lng,
            // 类型:1-签入，2-签出
            type:this.clockInType,
          }

          await this.$api.providerManagement.signinlogUpdate(queryOptions)

          // 重置状态
          this.clockInOptions = null;
          this.imageObj = [];

          if(this.clockInType === 1 || this.clockInType === 4){
            this.clockBefore = true;
          }

          // 发送事件给父组件并立即关闭弹窗
          this.$emit('lcockIn');
          this.close()

        } catch (error) {
          console.error('打卡失败:', error);
          uni.showToast({
            icon:"none",
            title:'上传图片更新错误-' + (error.msg || '未知错误')
          })
        } finally {
          // 确保在所有情况下都关闭loading
          uni.hideLoading()
        }
      },
      close(){
        this.$emit('change',false)
      },
      upper(){
        console.log('触底');
        if(this.loadEnd) return
        this.current++;
      },
    }
  }
</script>

<style lang="scss">
  .bottomBtn{
    width: 750rpx;
    height: 180rpx;
    background: #FFFFFF;
    box-shadow: 0rpx -1rpx 0rpx 0rpx #EAEBF0;
    position: fixed;
    bottom: 0;
    left: 0;
    .comBtn{
      width: 686rpx;
      height: 88rpx;
      background: #00B484;
      border-radius: 44rpx;
      text-align: center;
      line-height: 88rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #FFFFFF;
      margin: 24rpx auto;
    }
  }
  .selectBox{
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    border: 2rpx solid rgb(220, 221, 226);
    background-color: rgba(1, 1, 1, 0);
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 32rpx;
  }
.confirm{
  position: fixed;
  bottom: 0;
  width: 750rpx;
  height:1244rpx;
  background: #F4F6FA;
  border-radius: 24rpx 24rpx 0rpx 0rpx;
  padding: 32rpx 32rpx 200rpx 32rpx;
  box-sizing: border-box;
  .clickIn{
    font-weight: 500;
    font-size: 32rpx;
    color: #1D2029;
    text-align: center;
  }
  .clockInIcon{
    margin: 48rpx auto 0;
    width: 304rpx;
    height: 304rpx;
    box-sizing: border-box;
    text-align: center;
    padding-top: 116rpx;
    position: relative;
    font-weight: 500;
    font-size: 40rpx;
    color: #FFFFFF;
    .clockInBg{
      width: 272rpx;
      height: 272rpx;
      position: absolute;
      top: 8rpx;
      left: 16rpx;
      z-index: -1;
    }
  }
  .headerTab{
    position: relative;
    width: 686rpx;
    height: 192rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    padding: 24rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    .serverIcon{
      width: 144rpx;
      height: 144rpx;
      background: #D8D8D8;
      border-radius: 12rpx;
      border: 1rpx solid #D9DBE0;
      margin-right: 20rpx;
    }
    .serviceName{
      font-weight: 500;
      font-size: 32rpx;
      color: #1D2029;
      .signal{
        font-weight: 400;
        font-size: 22rpx;
        color: #FF5500;
      }
      .serverNum{
        font-weight: 500;
        font-size: 36rpx;
        color: #FF5500;
      }
      .tag{
        font-weight: 400;
        font-size: 20rpx;
        color: #868C9C;
      }
    }
    .changeServer{
      width: 148rpx;
      height: 52rpx;
      background: #FFFFFF;
      border-radius: 36rpx;
      border: 1rpx solid #D9DBE0;
      margin-left: auto;
    }
  }
}
.scroll-Y{
  height: 528rpx;
}
</style>
