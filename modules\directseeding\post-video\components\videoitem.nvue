<template>
  <view
    class="livePlayerBox"
    @click="hiddenInput"
    :style="{
      width: width,
      height: height,
    }"
  >
    <view
      :style="{
        display: 'flex',
        width: width,
        height: height,
      }"
      class="videobox"
    >
      <!-- 我是视频 -->
      <template v-if="(data.videosPath && data.videosPath != '')">
        <view class="rounded-play-btn-wrapper" :class="{ 'touch-opacity': touchStatus, 'opacity-0': isShowimage }" v-show="currentStatus === 'pause'" @tap="tapVideo">
          <view class="rounded-play-btn"></view>
        </view>
        <video
          class="flex1"
          :id="'myVideo' + idx"
          :style="{
            width: width,
            height: videoHeight,
          }"
          :src="videoPathAll"
          :controls="false"
          :muted="mutedPlay"
          :poster="poster"
          :title="data.title"
          :show-center-play-btn="false"
          :show-play-btn="false"
          :loop="true"
          :show-fullscreen-btn="false"
          play-btn-position="center"
          :show-bottom-progress="false"
          object-fit="contain"
          @tap="tapVideo"
          @timeupdate="timeupdate"
          @touchmove="touchmove"
          @touchend="touchend"
        ></video>

        <image
          v-if="isShowimage == true"
        	:src="videoPathAll+'?x-oss-process=video/snapshot,t_'+ currenttimes +'000,f_jpg'"
        	mode="aspectFill"
        	:style="'width: 120rpx; height: 160rpx; border-radius: 10upx; z-index: 99; position: absolute; bottom: '+ (ProgressBarBottom + 160) +'rpx; left: '+ (currentPositions - 15) +'px;'"
        ></image>

        <!-- 1.注意：进度条这类拖拽的东西不能放进block\cell这些循环体中的，要不然touchmove方法会捕捉有误 -->
        <view v-if="data && isShowProgressBarTime == true" :style="'position: absolute; bottom: '+ (ProgressBarBottom + windowWidth*0.2)/2 +'px; left: '+ (windowWidth*2 - windowWidth*1.35)/2 +'px; z-index: 3;'">
        	<text style="font-size: 22px; font-weight: bold; color: #F1F1F1;">{{changeTime}} / {{videoTimes}}</text>
        </view>
        <!-- 这里就是进度条了：纯手工进度条，调整位置的话就把他们的 bottom 改成一下就行了 -->
        <view v-if="isDragging == false" style="position: absolute; bottom: 32rpx; left: 0; right: 0; z-index: 99;">
          <view @touchmove.stop="progressTouchmoveFn" @touchend.stop="progressTouchend" @touchstart.stop="progressTouchstart" @touchcancel.stop="progressTouchend">
            <!-- 1.这一步必须加，为了适配低端机型 -->
            <text :style="'width: '+ windowWidth +'px; opacity: 0;'">.</text>
            <!-- 2.这是未加载的时的右边的灰色部分 -->
            <view :style="'width: '+ windowWidth +'px; height: 4px; background-color: #C8C7CC; position: absolute; bottom: '+ ProgressBarBottom +'upx; opacity: '+ ProgressBarOpacity +';'"></view>
            <!-- 3.这里我采用的分离式办法：就是让滑动样式和不滑动的样式分开，这样相互不干扰，可以避免进度条闪动的问题 -->
            <!-- 4.注意：isShowProgressBarTime 加入了返回数据中 -->
            <view v-if="data && isShowProgressBarTime == false" :style="'width: '+ (currentPosition) +'px; height: 4px; background-color: #FFFFFF; position: absolute; bottom: '+ ProgressBarBottom +'upx; left: 0; opacity: '+ (ProgressBarOpacity - 0.1) +';'"></view>
            <view v-if="data && isShowProgressBarTime == true" :style="'width: '+ (currentPositions) +'px; height: 8px; background-color: #FFFFFF; position: absolute; bottom: '+ (ProgressBarBottom - 2) +'upx; left: 0; opacity: '+ (ProgressBarOpacity + 0.05) +';'"></view>
            <view v-if="data && isShowProgressBarTime == false" :style="'width: 4px; height: 4px; background-color: #FFFFFF; border-radius: 10px; position: absolute; bottom: '+ ProgressBarBottom +'upx; left: '+ (currentPosition) +'px; opacity: '+ ProgressBarOpacity +';'"></view>
            <view v-if="data && isShowProgressBarTime == true" :style="'width: '+ dotWidth +'px; height: '+ dotWidth +'px; background-color: #FFFFFF; border-radius: 10px; z-index: 999; position: absolute; bottom: '+ (ProgressBarBottom - 5) +'upx; left: '+ (currentPositions - 5) +'px; opacity: '+ ProgressBarOpacity +';'"></view>
          </view>
        </view>
      </template>
    </view>

    <view class="sidebar-bar-wrap" :class="{ 'touch-opacity': touchStatus, 'opacity-0': isShowimage }">
      <sidebar-bar :data="data" :isShowBtn="isShowBtn" />
    </view>
    <bottomInfo :class="{ 'touch-opacity': touchStatus, 'opacity-0': isShowimage }" :data="data"></bottomInfo>
  </view>
</template>

<script>
import { mapState } from "vuex";
import showbottommodel from "@/components/basics/showmodel/showmodel.vue";
import { isDomainUrl, param } from "@/utils";
import bottomInfo from "@/modules/directseeding/post-video/components/bottom-info.vue";
import sidebarBar from './sidebar-bar.vue'
export default {
  props: {
    url: {
      type: String,
      default: "",
    },
    data: {
      type: Object,
      default: function () {
        return {};
      },
    },
    width: {
      type: String,
      default: "100vw",
    },
    height: {
      type: String,
      default: "0vh",
    },
    visible: {
      type: Boolean,
      default: false,
    },
    once: {
      type: Boolean,
      default: false,
    },
    editid: {
      type: [String, Number],
      default: null,
    },
    idx: {
      type: Number,
      default: 0,
    },
    ishidden: {
      type: Boolean,
      default: false,
    },
    isShowBtn:{
      type:Boolean,
      default:true,
    },
    systemType: {
      type: String,
      default: null
    }
  },
  components: {
    showbottommodel,
    bottomInfo,
    sidebarBar
  },
  computed: {
    ...mapState("user", {
      codeUserInfo: (state) => state.codeUserInfo, // 当前登录用户信息
      accountId: (state) => state.accountId,
      curSelectUserInfo: (state) => state.curSelectUserInfo, // 当前选中的用户信息
      curSelectUserInfoId: (state) => state.curSelectUserInfoId, // 当前选中的档案的ID
    }),
  },
  data() {
    return {
      scrollIntoView: "",
      // 是否静音
      mutedPlay: true,

      forbiddenVisible: false,
      currentStatus: "stop",
      // 用户观看定时器
      UserStatusTimer: null,
      // 用户观看状态
      anWatchStatus: true,

      orientation: "vertical",

      showPlayBtn: false,

      loadingvisible: true,
      ishiddenFn: false,
      hour: "00",
      minute: "00",
      showMoreBtn: false,
      playurl1: "",
      videoHeight: "96vh",
      poster: null,
      videoPathAll: null,

      titleHeight: 0,
      statusHeight: 0,
      autoHeight: 70,

      updateclear: 0,
      noclick: false,
      pageChatCount: 20,
      iscommond: 0,
      updatecount: 0,
      touchStatus: false,
      windowWidth: uni.getSystemInfoSync().screenWidth, //获取屏幕宽度
      windowHeight: uni.getSystemInfoSync().screenHeight,

      videoTime: '',//视频总时长，这个主要用来截取时间数值💗
      videoTimes: '',//视频时长，用这个来获取时间值，例如：00:30这个时间值💗
      changeTime: '',//显示滑动进度条时变化的时间💗
      isShowimage: false,//是否显示封面【1.0.4已废弃，但是意思需要记住】
      currenttimes: 0,//当前时间💗
      isShowProgressBarTime: false,//是否拖动进度条，如果拖动（true）则显示进度条时间，否则不显示（false）【1.0.4已废弃，但是意思需要记住】
      ProgressBarOpacity: 0.7,//进度条不拖动时的默认值，就是透明的💗
      dotWidth: 0,//播放的小圆点，默认没有💗
      percent: 0,//百分小数💗
      currentPosition: 0,//滑块当前位置💗//2.0已弃用，现已用于后端参数
      currentPositions: 0,//滑块当前位置的副本💗//2.0已弃用，现已用于后端参数
      newTime: 0,//跟手滑动后的最新时间💗
      timeNumber: 0,//🌟💗
      ProgressBarBottom: 20,//进度条离底部的距离💗
      isDragging: false,//false代表停止滑动

      progressTouchmoveFn: () => {}
    };
  },
  created() {
    this.progressTouchmoveFn = this.$common.throttle(this.progressTouchmove, 50)
  },
  mounted() {
    this.initHeader();
    if (this.once) {
      this.mutedPlay = false;
      // 预告/回放
      let vid = "myVideo" + this.idx;
      this.videoContext = uni.createVideoContext(vid, this);
      setTimeout(() => {
        this.currentStatus = "stop";
        this.meetingviewusersaveuser(false);
      }, 1000);
    }
  },
  watch: {
    visible(n) {
      if (n) {
        this.mutedPlay = false;
        this.currentStatus = "stop";
        this.orientation = "vertical";
        this.anWatchStatus = 1;
        let vid = "myVideo" + this.idx;
        if (!this.videoContext) {
          this.videoContext = uni.createVideoContext(vid, this);
        }
        setTimeout(() => {
          // 初始化播放
          this.meetingviewusersaveuser();
        }, 500);

      } else {
        this.mutedPlay = true;

        this.forbiddenVisible = false;
        this.orientation = "vertical";
        this.anWatchStatus = 2;

        this.loadingvisible = true;

        this.stopVideo(true);
      }
    },
    ishidden(n) {
      this.ishiddenFn = n;
      this.isWindowOpen();
      if (!n) {
      }
    },
  },
  methods: {
    isWindowOpen() {
      let systemType = this.systemType + '';
      console.log('systemType===',systemType)
      if(systemType.indexOf('Window') !== -1) {
        if(this.visible) {
          console.log('jj002',this.editid)
          if(this.ishidden) {
            this.mutedPlay = true;
            this.pause()
          } else {
            if (this.currentStatus === 'play') {
              this.mutedPlay = true;
              this.pause()
            } else if (this.currentStatus === 'pause') {
              this.mutedPlay = false;
              this.play()
            }
          }
        }
      }
    },
    progressTouchstart(event){
    	this.isShowimage = true //刚触摸的时候就要显示预览视频图片了
    	this.isShowProgressBarTime = true //显示时间线
    	this.ProgressBarOpacity = 1 //让滑块显示起来更明显一点
    	this.dotWidth = 10 //让点显示起来更明显一点
    },
    progressTouchend(){//当手松开后，跳到最新时间
    	this.videoContext.seek(this.newTime)
    	if(this.currentStatus == 'pause'){
    		this.play()
    	}
    	this.isShowProgressBarTime = false //触摸结束后，隐藏时间线
    	this.isShowimage = false //触摸结束后，隐藏时间预览
    	this.ProgressBarOpacity = 0.5 //隐藏起来进度条，不那么明显了
    	this.dotWidth = 0 //隐藏起来进度条，不那么明显了
    },
    progressTouchmove(event){//当手移动滑块时，计算位置、百分小数、新的时间
    	let msg = []
    	if(this.videoTime !== ''){
    		msg = this.videoTime.split(':')
    	}
    	let timeNumber = this.$accurateConversion.plus(this.$accurateConversion.times(msg[0], 60), msg[1])
    	this.currentPositions = event.changedTouches[0].clientX
    	this.percent = this.$accurateConversion.divide(this.currentPositions.toFixed(2), this.windowWidth)
    	this.newTime = this.$accurateConversion.times(this.percent, timeNumber)
    	this.currenttimes = parseInt(this.newTime)
    	let theTime = +this.newTime
    	let middle = 0;// 分
    	if(theTime > 60) {
    		middle = parseInt(theTime/60);
    		theTime = parseInt(theTime%60);
    	}
    	this.changeTime = `${Math.round(middle)>9?Math.round(middle):'0'+Math.round(middle)}:${Math.round(theTime)>9?Math.round(theTime):'0'+Math.round(theTime)}`
    },
    timeupdate(event){//计算滑块当前位置，计算当前百分小数
    		// console.log(event)
    		let currenttime = event.detail.currentTime
    		this.timeNumber = Math.round(event.detail.duration)
    		this.getTime()
    		this.percent = this.$accurateConversion.divide(currenttime, this.timeNumber)
    		this.currentPosition = this.$accurateConversion.times(this.windowWidth, this.percent)
    		let theTime = currenttime
    		let middle = 0;// 分
    		if(theTime > 60) {
    			middle = parseInt(theTime/60);
    			theTime = parseInt(theTime%60);
    		}
    		this.changeTime = `${Math.round(middle)>9?Math.round(middle):'0'+Math.round(middle)}:${Math.round(theTime)>9?Math.round(theTime):'0'+Math.round(theTime)}`
    },
    getTime(){//得到时间函数
    	this.videoTime = this.formatSeconds(this.timeNumber);
    	// console.log(that.videoTime)
    	let msg = []
    	if(this.videoTime !== ''){
    		msg = this.videoTime.split(':')
    	}
    	this.videoTimes = `${msg[0]>9?msg[0]:'0'+msg[0]}:${msg[1]>9?msg[1]:'0'+msg[1]}`;
    },
    formatSeconds(value) {//获取时间函数
    	let theTime = parseInt(value);// 秒
    	let middle= 0;// 分
    	if(theTime > 60) {
    		middle= parseInt(theTime/60);
    		theTime = parseInt(theTime%60);
    	}
    	return `${middle>9?middle:middle}:${theTime>9?theTime:theTime}`;
    },
    touchmove() {
      this.touchStatus = true
    },
    touchend() {
      this.touchStatus = false
    },
    play() {
      this.currentStatus = 'play'
      this.videoContext.play()
    },
    pause() {
      this.currentStatus = 'pause'
      this.videoContext.pause()
    },
    tapVideo() {
      if (this.currentStatus === 'play') {
        this.pause()
      } else if (this.currentStatus === 'pause') {
        this.play()
      }
    },
    // 初始化播放
    meetingviewusersaveuser(isone) {
       this.bindPlay(isone);
    },
    bindfullscreenchange(e) {
      this.orientation = e.detail.orientation;
    },
    flv_destroy() {
      //停止
      //#ifdef H5
      if (this.player) {
        this.player.pause();
        this.player.unload();
        this.player.detachMediaElement();
        this.player.destroy();
        this.player = null;
        this.flvPlayer.stop();
        // let vid = 'myVideoyy' + this.idx;
        // document.getElementById(vid).removeChild(this.player)
      }
      // #endif
    },
    // 停止播放视频
    stopVideo(novisible) {
      if (this.currentStatus != "play") {
        return;
      }
      this.currentStatus = "stop";

      // #ifdef MP-WEIXIN
      let id = "livePlayer" + this.idx;
      if (!this.playerCtx) {
        this.playerCtx = uni.createLivePlayerContext(id, this);
      }
      // #endif
      if (this.playerCtx) {
        setTimeout(() => {
          this.playerCtx.stop({
            success: function () {
              console.log("停止播放成功");
            },
            error: function () {
              console.log("停止播放失败");
            },
          });
        }, 500);
      }

      // 停止播放视频
      // 预告/回放
      let vid = "myVideo" + this.idx;
      if (!this.videoContext) {
        this.videoContext = uni.createVideoContext(vid, this);
      }
      if (this.videoContext) {
        setTimeout(() => {
          this.videoContext.stop({
            success: function () {
              console.log("停止播放成功");
            },
            error: function () {
              console.log("停止播放失败");
            },
          });
        }, 500);
      }

      // H5 播放
      this.flv_destroy();
    },
    // 初始化头部导航栏
    initHeader() {
      let systemInfo = uni.getSystemInfoSync();
      let statusHeight = systemInfo.statusBarHeight; //状态栏的高度
      let titleHeight = 45; //导航栏高度，这个一般是固定的
      let allheights = systemInfo.windowHeight;
      console.log(systemInfo);
      this.statusHeight = statusHeight;
      this.titleHeight = titleHeight;
      this.autoHeight = this.statusHeight + this.titleHeight;
    },
    bindPlay(isonce) {
      if (this.currentStatus != "stop") {
        return;
      }
      this.currentStatus = "play";
      // 访问
      if (!isonce) {
        // 记录正在播放的id
        if (!this.ishiddenFn) {
          uni.$playingMeetingId = this.editid;
        }
      }

      this.videoPathAll = isDomainUrl(this.data.videosPath);
      console.log(this.data.videosPath);
      this.poster = isDomainUrl(this.data.imagesPath);

      this.$nextTick(() => {
        this.videoContext.play({
          success: function () {
            console.log("播放成功");
          },
          error: function () {
            console.log("播放失败");
          },
        });
      });
    },
    resetCommon() {
      this.scrollIntoView = "top-comment";
      setTimeout(() => {
        this.scrollIntoView = "";
      }, 300);
    },
    hiddenInput() {
      this.updatecount += 1;
    },
    //
    sheep(timer) {
      if (!timer) {
        // timer = 1200000;
        timer = 2000;
      }
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          resolve(true);
        }, timer);
      });
    },
  },
  destroyed() {
    this.ishiddenFn = true;
  },
};
</script>

<style scoped lang="scss">
$chatColor: #fff;
.livePlayerInput {
  background-color: rgba(255, 255, 255, 0.6);
  padding: 0 20rpx;
  border-radius: 10rpx;
}
.livePlayerBotton {
  font-size: 18rpx;
  margin-left: 20rpx;
}
.number-box {
  position: absolute;
  top: 50px;
  left: 45rpx;
  background-color: rgba(0, 0, 0, 0.4);
  padding: 5rpx 25rpx;
  border-radius: 20upx;
  // .playtxt{
  //   font-size: 28upx;
  // }
}
.horizontal-header {
  position: absolute;
  right: 20upx;
  height: 20upx;
  width: 20upx;
  overflow: visible;
  transform: rotate(90deg);
  // top: 50%;
  display: inline-block;
}
.nofindbox {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99999;
  background: #c8d0c7;
  // opacity: 0.4;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 38upx;
}
.horizontalbox {
  position: absolute;
  right: 20upx;
  bottom: 100px;
  color: #fff;
}

.seekmore {
  font-size: 24upx;
  color: #fff;
  line-height: 1.5;
  display: flex;
  justify-content: center;
}

.playtxt {
  font-size: 24upx;
}

.getheight {
  position: absolute;
  left: -10px;
  top: -10px;
}
.rightNav {
  position: absolute;
  right: 20upx;
  top: 50%;
  transform: translateY(-50%);
}
.videobox {
  position: relative;
  background-color: #000;
  align-items: center;
}

.center-tip {
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
}
.ewmbox {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  flex-direction: column;

  .ewmtip {
    font-size: 24upx;
    line-height: 1.5;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20upx;
    // align-items: center;
  }

  .ewmico {
    width: 300upx;
    height: 300upx;
    border: 1upx solid #dbdbdb;
    // background-color: #000;
  }
}

.hidden {
  display: none;
}
.livePlayerBox {
  position: relative;
  // overflow: hidden;
  height: 100vh;
  width: 100vw;
  tansform: tanslate3d(0, 0, 0);

  .textelipse {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    max-width: 100%;
  }

  .txt {
    font-size: 24rpx;
  }
  .txt2 {
    margin: 0 10upx;
  }
  .horizontal.livePlayerChat {
    transform: rotate(90deg);
    overflow-y: scroll;
    overflow-x: auto;
    position: absolute;
    height: 400upx;
    border-radius: 10upx;
    left: 20upx;
    width: calc(100vw - 240upx);
    top: 120upx;
    font-size: 28upx;
    line-height: 1.5;
    padding: 20upx 0;
  }
  .vertical.livePlayerChat {
    position: absolute;
    height: 400upx;
    border-radius: 10upx;
    overflow: hidden;
    left: 20upx;
    width: calc(100vw - 240upx);
    bottom: 140px;
    overflow-y: auto;
    font-size: 28upx;
    line-height: 1.5;
    padding: 20upx 0;
  }
  .livePlayerChat {
    .top-tip {
      color: $chatColor;
      // color: #ffff;
      font-size: 24rpx;
      margin-bottom: 10upx;
      background-color: rgba(0, 0, 0, 0.2);
      padding: 0 20upx;
      border-radius: 10upx;
      overflow: hidden;
    }
    .livePlayerChat-item {
      display: flex;
      margin-bottom: 10upx;
      background-color: rgba(0, 0, 0, 0.2);
      // padding: 20upx;
      border-radius: 10upx;
      overflow: hidden;
      padding: 0 20rpx;
    }
    .livePlayerChat-item-txt {
      color: $chatColor;
      white-space: nowrap;
      // line-height: 30upx;
      font-size: 24upx;
      display: flex;
      line-height: 55upx;
    }
    .livePlayerChat-item-info {
      color: #fff;
      display: flex;
      flex-wrap: wrap;
      align-items: center;

    }
  }
  .livePlayerBottom {
    display: flex;
  }
}
.rounded-play-btn-wrapper {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, 50%);
  animation-duration: .2s;
  animation-name: slidein;
  z-index: 2;
}
.rounded-play-btn {
  opacity: .2;
  width: 60rpx;
  height: 60rpx;
  border-bottom-left-radius: 24rpx;
  background-color: #fff;
  transform: rotate(90deg) skewX(-30deg) scale(1, .866);
  &::before, &::after {
    content: "";
    position: absolute;
    background-color: inherit;
    width: 60rpx;
    height: 60rpx;
    border-bottom-left-radius: 24rpx;
  }
  &::before {
    transform: rotate(135deg) skewY(-45deg) scale(.707, 1.414) translate(-50%, 0);
  }
  &::after {
    transform: rotate(-135deg) skewX(-45deg) scale(1.414, .707) translate(0%, 50%);
  }
}
@keyframes slidein {
  from {
    opacity: 0;
    transform: translate(-50%, 50%) scale(2);
  }

  to {
    transform: translate(-50%, 50%) scale(1);
  }
}
.sidebar-bar-wrap {

}
.touch-opacity {
  transition-duration: .3s;
  transition-property: opacity;
  opacity: .2;
}
.opacity-0 {
  opacity: 0;
}
</style>
