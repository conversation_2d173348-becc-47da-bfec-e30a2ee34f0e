const modalPlugin = requirePlugin('xlight');
Component({
  props: {
    onAdInstance: () => {},
    onAdLoad: () => {}
  },
  data: {
    interstitialAd: {},
    spaceCode:"50_2023072425000057897",
  },
  onInit() {
    const ad = new modalPlugin.CreateInterstitialAd();
    console.log('ad instance', ad)
    // console.log("getC===>",this.props.onAdInstance(ad), typeof this.props.onAdInstance)
    this.props.onAdInstance && this.props.onAdInstance(ad);
    this.setData({
      interstitialAd: ad
    })

    // this.data.interstitialAd.onLoad(() => {
    //   console.log('ad onLoad')
    //   console.log(this.data.interstitialAd, 'ad')
    //   this.props.onAdLoad()
    //   this.data.interstitialAd.show({
    //     spaceCode: this.data.spaceCode || 'TABLE_SCREEN_TEST_SPACE_0',
    //     rtaExtMap: {
    //   	organization_type: 'hospital',
    //   	organization_sub_type: 'general_hospital',
    //   	device_location: 'outpatient_department',
    //   	device_id: '111111',
    //   	public_opinion_level: 'low_risk',
    //   	touch_point_location: 'index',
    //   	touch_point_type: 'full_screen',
    //     },
    //   });
    // })

    // this.data.interstitialAd.onClose((isClick) => {
    //   console.log('close', isClick);
    // });

    // this.data.interstitialAd.onError((err) => {
    //   console.log('error', err);
    // });
  },
  methods: {},
});
