<template>
  <view class="">
  <!-- <view> -->
    <view class="Mask" v-if="isCodePopupFlagFn" @click="closeProp"></view>
    <view v-if="isCodePopupFlagFn" class="contentBox luckPopup" :style="{backgroundImage:`url(${popupParams.imageUrl.includes(file_ctx) ? popupParams.imageUrl : file_ctx + popupParams.imageUrl})`}" >
      <view class="integrationNum">+{{IntegrationData.integrationNum}}福币</view>
      <!-- <image class="luckPopupBtn" :src="luckPopupBtn" mode="" @click="gotoPages('/modules/pharmacy/pharmacy-cyclopedia/everyday-rumour/free-get-poster','WaterLightNeedle')"></image> -->
      <view class="content-img-btn-box">
        <view
          class="content-btn-item"
          v-for="(item,index) in popupParams.managementItemList"
          :key="item.id"
          :style="{width:popupParams.managementItemList.length > 1 ? '279rpx' : '522rpx',height:popupParams.managementItemList.length > 1 ? '166rpx' : '166rpx',marginLeft:index == 1 ? '12rpx' : ''}"
        >
          <!-- <image class="luckPopupBtn" :src="item.imageUrl.includes(file_ctx) ? item.imageUrl : file_ctx + item.imageUrl" @click.prevent.stop="handleClickJump(item)"></image> -->
          <image class="content-img-btn" :src="item.imageUrl" @click.prevent.stop="handleClickJump(item)"></image>
        </view>
      </view>
      <view class="iconCloseBtn"  @click="closeProp" :style="{backgroundImage:`url(${closeWhite})`}"></view>
    </view>
  </view>
</template>


<script>
  import common from '@/common/util/main'
  import { mapState } from "vuex"
  export default{
    props:{
      // 判断是否是通过任务来控制
      isTaskControl:{
        default:false,
        type:Boolean
      }
    },
    data(){
      return {
        $constant:this.$constant,
        file_ctx:this.file_ctx,
        showFlag:false,
        luckPopup: this.$static_ctx + "image/business/hulu-v2/luckPopup.png",
        luckPopupBtn: this.$static_ctx + "image/business/hulu-v2/luckPopupBtn.gif",
        blueBtn: this.$static_ctx + "image/business/hulu-v2/blueBtn.png",
        redBtn: this.$static_ctx + "image/business/hulu-v2/redBtn.png",
        closeWhite: this.$static_ctx + "image/business/hulu-v2/icon-home-popup-error-fault.png",
        popupParams:null,
        isCodePopupFlag:false,
      }
    },
    computed:{
      ...mapState('user', {
        recordUserInfo: state => state.recordUserInfo, // 当前登录用户信息
        fansRecord: state => state.fansRecord,
        accountId: state => state.accountId,
        curSelectUserInfo: state => state.curSelectUserInfo
      }),
      codePopupFlag(){
        return this.IntegrationData.codePopupFlag
      },
      isCodePopupFlagFn(){
        return this.isTaskControl ? this.IntegrationData.codePopupFlag : this.isCodePopupFlag
      }
    },
    async mounted() {
      console.log('渲染弹窗组件');
      let res = await this.$api.drugBook.advertisementmanagementListValidPopAccount({accountId:this.accountId,useType:3,adsId:''})
      // const now = new Date()
      // console.log('res.data',res.data);
      // if(res.data !==""){
      //   if(now.getTime() >= res.data.startTime && now.getTime() <= res.data.endTime){
          this.popupParams = res.data
          this.$nextTick(()=>{
           this.isCodePopupFlag = true;
          })
        // }
      // }
    },
    watch:{
      // async codePopupFlag(n){
      //   console.log('codePopupFlag',n);
      //   if(n){
      //     this.$api.drugBook.pageexposurerecordInsert({
      //       ...(await this.getOptions()),
      //     })
      //   }
      // }
    },
    methods:{
      async handleClickJump(item){
        await this.$api.drugBook.advertisementmanagementitemrecordInsert({accountId:this.accountId,advertisementItemId:item?.id,businessType:1})
        this.navigateTo(item)
      },
      // 页面跳转
      navigateTo(item,obj={}) {
        console.myLog("e=======:",item)
        // 跳转类型:1-静态，2-跳内部页面，3-跳Web地址,4-跳小程序
        item.jumpType = typeof(item.jumpType) === 'number' ? JSON.stringify(item.jumpType) : item.jumpType
        switch (item.jumpType) {
          case '2':
            if (item.jumpUrl) {
              // console.log(item.jumpUrl,'item.jumpUrl---')
              // this.$navto.push(item?.jumpUrl,obj)
              this.gotoPages(item.jumpUrl)
            }
            break
          case '3':
            this.$navto.push('WebHtmlView', { src: item.jumpUrl, title: '' })
            break
          default:
        }
      },
      async closeProp(){
        await this.$api.drugBook.advertisementmanagementrecordInsert({businessType: 3,accountId:this.accountId,advertisementId:this.popupParams?.id}) //businessType: 3,  //默认传3(关闭)
        this.isCodePopupFlag = false;
        this.IntegrationData.codePopupFlag = false;
      },
      // 解析hash模式路由参数
      parsedHashRouterOptions(){
        if(!window.location) return {}
        let routerOptions = window.location.href.split('?')[1]?.split('&')?.reduce((res,index)=>{
          let map = index.split('=');
          res[map[0]] = map[1];
          return res
        },{})
        return routerOptions || {}
      },
      gotoPages(url){
        uniWebview.navigateTo({url})
      },
      async getOptions(){
        let openId = await this.$ext.wechat.getOpenId();
        let launchParams = common.getKeyVal('system', 'launchParams');
        let accountId = launchParams.accountId;
        const { userId } = this.recordUserInfo;
        const businessType = this.$constant.drugBook.businessTypeObj['WaterLightNeedle']
        return {openId,accountId,userId,imageUrl:this.luckPopup,businessType}
      },
      gotoPages(url,type){
        uniWebview.navigateTo({url});
        this.Buried(type)
      },
      // 埋点
      async Buried(type){
        this.$api.drugBook.visitpagerecordInsert({...(await this.getOptions())})
      }
    }
  }
</script>

<style lang="scss" scoped>
  .luckPopup{
    width: 598rpx;
    height: 754rpx;
    background-size: 100% 100%;
  }
  .Mask{
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    background-color: black;
    opacity: 0.6;
    z-index: 999;
  }
  .contentBox{
    position: fixed;
    top: 260rpx;
    left: 50%;
    transform: translateX(-50%);
    z-index: 999;
    .integrationNum{
      font-weight: 600;
      font-size: 40rpx;
      color: #FFFCF2;
      margin-top: 186rpx;
      transform: translateX(-5rpx);
      width: 100%;
      text-align: center;
    }
    .content-img-btn-box{
      display: flex;
      position: absolute;
      // bottom: 18rpx;
      bottom: 10rpx;
      left: 50%;
      transform: translateX(-50%);
      .content-btn-item{
        position: relative;
        height: 166rpx;
        .content-img-btn{
          // width: 522rpx;
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .luckPopupBtn{
    width: 526rpx;
    height: 116rpx;
    position: absolute;
    left: 50%;
    bottom: 24rpx;
    transform: translateX(-50%);
  }
  .gotoBtn{
    width: 443rpx;
    height: 106rpx;
    background-size: 100% 100%;
    font-size: 35.76rpx;
    text-align: center;
    font-weight: bold;
    line-height: 90rpx;
    color: white;
  }
  .iconCloseBtn{
    background-size: 100% 100%;
    // width: 32rpx;
    // height: 32rpx;
    width: 40rpx;
    height: 40rpx;
    position: absolute;
    // top: -32rpx;
    // right: 0;
    bottom: -28rpx;
    left: 50%;
    transform: translateX(-50%);
  }
</style>
