<!--主要程序模板页面-->
<script>
import common from '@/common/util/main'
import ext from '@/service/ext'
/**
 * 引入神策插件
 */
import sensors from './js_sdk/Sensorsdata-UniPlugin-JS/uni-app-sdk/index'
  const SERVER_CONFIG = { // 配置服务器地址和测试地址，正式环境
    baseURL: 'https://data-access.greenbontech.com:8106/sa',
    projects: {
      development: 'default',
      production: 'production'
    }
  }

  //同意隐私协议后调用进行 SDK 初始化
  sensors.init({
    server_url: `${SERVER_CONFIG.baseURL}?project=${SERVER_CONFIG.projects[process.env.NODE_ENV] || 'production'}`,
    show_log:true,//是否开启日志
    name:"sensors",
    global_properties:{ // 配置全局属性，所有上报事件属性中均会携带
      platform_type: '小葫芦圈小程序'
    },
    autoTrack:{//小程序全埋点配置
      appLaunch: true, // 默认为 true，false 则关闭 $MPLaunch 事件采集
      appShow: true, // 默认为 true，false 则关闭 $MPShow 事件采集
      appHide: true, // 默认为 true，false 则关闭 $MPHide 事件采集
      pageShow: true, // 默认为 true，false 则关闭 $MPViewScreen 事件采集
      pageShare: true, // 默认为 true，false 则关闭 $MPShare 事件采集
      mpClick: false, // 默认为 false，true 则开启 $MPClick 事件采集
      mpFavorite: true, // 默认为 true，false 则关闭 $MPAddFavorites 事件采集
      pageLeave: false // 默认为 false， true 则开启 $MPPageLeave事件采集
    },
  });

let enterFlag = false;
export default {
  globalData:{
    sensors:sensors
  },
  onLaunch: function(option) {
    // #ifdef MP-WEIXIN
    if(option.path =='pages/index/my'){
      this.handleLaunchOrShow({path:'pages/index/my'})  
    } else {
      this.handleLaunchOrShow(option)
    }
    // #endif
    this.$ext.wechat.getOpenId()
    // 直播离开时间
    let liveCookieInfo = common.getCache('liveCookieInfo')
    if(liveCookieInfo && liveCookieInfo instanceof Object){
      common.setCache('liveCookieInfo','')
      this.meetingviewlogvisit2(liveCookieInfo.id,liveCookieInfo.val)

    }

    //微信小程序ios上音频播放没有声音
    // #ifdef MP-WEIXIN
    uni.setInnerAudioOption({
      obeyMuteSwitch: false
    });
    // #endif

    if (!this.$validate.isNull(option)) {
      if (!this.$validate.isNull(option.query)) {
        common.setKeyVal('system', 'launchParams', option.query, true)
      }
    }
    const query = this.$Route.query
    if (!this.$validate.isNull(query)) {
      if (query.storeId) {
        common.setKeyVal('user', 'curSelectStoreId', query.storeId, true)
      }
    }
  },
  onShow: function(option) {
    ext.common.parseAppScene(option)
    if (!this.$validate.isNull(option) && !this.$validate.isNull(option.query) && !this.$validate.isNull(option.query.scene)) {
      const sceneResult = {
        scene: option.scene,
        path: option.path,
        sceneCode: option.query.scene
      }
      common.setKeyVal('system', 'scene', sceneResult, true)
    } else {
      common.setKeyVal('system', 'scene', {}, true)
    }

    // 重新链接
    if (!this.$ext.webSocket.webSocket) {
      this.$ext.webSocket.webSocketInit()
    }
    this.$ext.common.addMinichannellinklogLaunchVisit()
    if (enterFlag) this.$ext.common.addMinichannellinklogvisit(3)
    enterFlag = true
  },
  onHide: function() {
    this.$ext.common.addMinichannellinklogvisit(2)
    // 断开链接
    if (!this.$validate.isNull(this.$ext.webSocket.webSocket)) {
      this.$ext.webSocket.webSocketIsReconnect = false
      this.$ext.webSocket.webSocketPingTimer = null
      // #ifdef H5
      this.$ext.webSocket.webSocket.close();
      // #endif

      // #ifndef H5
      this.$ext.webSocket.webSocket.close({})
      // #endif
    }
  },
  methods: {
    // 开屏页方法
    async handleLaunchOrShow(option){
      let arr = ['/modules/activity/calabash/calabashWebview','modules/activity/calabash/index'] //判断是祈福页面，为true不要再进来启动页
      if(arr.includes(option?.path)){
        return
      } 
      if(option?.query?.shareShow == '1'){
        return
      }

      //所有领袋入口,说明书入口,腾讯半屏落地页
      let pocketArr = ['pages/packet/index','pages/packet/pay/index','pages/packet/MiddlePage','pages/packet-dll/index','pages/packet-dll/result',
      'modules/pharmacy/pharmacy-cyclopedia/index','modules/pharmacy/pharmacy-cyclopedia/look-more/index','modules/common/web-html-view/index','modules/pharmacy/pharmacy-cyclopedia/history/poster-picture',
      'modules/foreign/target','modules/foreign/guide','modules/pharmacy/pharmacy-cyclopedia/history/free-get-glareme','modules/common/instruction-web-html-view/index',
      'modules/activity/health-testing/index','modules/activity/health-testing/testing-detail','modules/activity/pages/diagnosis/add','modules/accompany-doctor/hospital-ranking/index',
      'pages/post-message/index','modules/pharmacy/pharmacy-cyclopedia/publish-post/index','modules/pharmacy/pharmacy-cyclopedia/everyday-rumour/index','modules/community/posts/gambit/index','modules/community/posts/post-gambit/index',
      'modules/directseeding/shortVideo/list/index','modules/directseeding/post-video/index','modules/community/posts/detail/index','modules/business/chat-record/index',
      'modules/accompany-doctor/home/<USER>','modules/accompany-doctor/home/<USER>','modules/accompany-doctor/service-detail/index','modules/accompany-doctor/combo-detail/index','modules/accompany-doctor/service-reservation/index',
      'modules/activity/pages/accurate-promotion-of-small-gourd/index','modules/community/post-subject/index','modules/activity/user-activity/index','modules/activity/questionnaire/index','modules/accompany-doctor/accompany/home/<USER>',
      'modules/community/news/reply/index','modules/community/news/like-collect/index','modules/accompany-doctor/storeManagement/index','modules/accompany-doctor/storeManagement/fixtures',
      'modules/accompany-doctor/accompany/home/<USER>','modules/directseeding/video-list/index','modules/system/user/index','modules/community/personal/home-page/index','modules/pharmacy/pharmacy-cyclopedia/everyday-rumour/activity-prefecture',
      'modules/activity/hospital-ranking/index','modules/community/scratch/index','modules/common/system-search/search-data'
      ]

      if(!pocketArr.includes(option?.path)){ //领袋入口为true 不进启动页
        if(option?.query?.sampshare){
          delete option.query.sampshare
        }
        if(option.path !== 'pages/index/my'){
          this.$nextTick(()=>{
            uni.reLaunch({
              url: !this.$validate.isNull(option?.query) ? `/pages/index/my?path=${option.path}&params=${encodeURIComponent(JSON.stringify(option.query))}` : '/pages/index/my'  // 跳转到启动页
            })
          })
        }
      }
    },

    meetingviewlogvisit2(id,cookieId) {
      this.$api.cloudClassroom
        .meetingviewlogvisit({
          mainId: id,
          source: 2,
          cookieId:cookieId,
        })
        .then(ret => {});
    }
  }
}
</script>

<style lang="scss">
  @import './components/community/min.css';
  em,i{font-style:normal}

  text, view, button {
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, PingFang SC, HarmonyOS_Sans_SC, MiSans, OPPOSans, vivoSans, HONOR Sans, Roboto, Microsoft YaHei, Helvetica, Arial, sans-serif;
  }

  uni-view{
    line-height: 1;
  }
  /*checkbox选中后样式  */
  uni-checkbox .uni-checkbox-input.uni-checkbox-input-checked{
      border-color: #22daad!important;
      background-color: #22daad!important;
      color: #FFFFFF!important;
  }
  /*单选按钮*/
  .uni-radio-input-checked{
    border-color: #22daad!important;
    background-color: #22daad!important;
    color: #FFFFFF!important;
  }
  uni-tabbar .uni-tabbar-border{
    background-color:#F7F7F7!important;
  }
  .uni-checkbox .uni-checkbox-input{
    width: 40upx!important;
    height: 40upx!important;
  }
  [alt]{
    max-width:100%;
  }
  .isNoLogin{
    position: fixed;
    bottom: 200rpx;
    z-index: 999;
    background-color: #00B484;
    padding: 30rpx;
    border-radius: 30rpx;
    left: 50%;
    color: white;
    transform: translateX(-50%);
  }
</style>
