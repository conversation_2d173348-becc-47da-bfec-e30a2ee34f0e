Component({
  data() {
    return {
      pouchSpaceCode: '50_2024111125000203591',
      pouchAgreementNo: '',
      height: 75,
    };
  },
  props: {
    visible: false,
    onSuccess: () => { },
    onClose: () => { },
    onError: () => { },
    rtaExtMap: {}
  },
  methods: {
    onSuccess() {
      this.props.onSuccess()
    },
    onClose(err) {
      this.props.onClose(err)
    },
    onError(err) {
      this.props.onError(err)
    },
  },
});