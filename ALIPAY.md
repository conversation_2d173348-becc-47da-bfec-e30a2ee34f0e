#### 小葫芦兼容支付宝小程序常见问题

1. 非原生组件上不要写`class`和`style`，也包括v-show

2. `image`标签上的mode属性不要为空（`mode=""`），要么删掉，要么给个值（例如`mode="aspectFill"`）

3. 同一个`.vue`文件不要同时作为页面又作为组件被其他页面引用

4. 如需自定义导航栏，在支付宝需单独设置

   ```json
   // pages.json
   {
    ...,
    "style": {
      // #ifndef MP-ALIPAY
      "navigationBarTitleText": "小葫芦",
      // #endif
      ...,
      "mp-alipay": {
        "transparentTitle": "always",
        "titlePenetrate": "YES"
      }
    }
   },
   ```

   - `"transparentTitle": "always"`只是隐藏掉导航栏的占位，标题和返回按钮还是会存在，所以`navigationBarTitleText`视实际业务情况看看要不要去掉
   - 如果你的自定义导航栏需要有点击之类的操作还需设置`"titlePenetrate": "YES"`，否则不会触发
   - 返回按钮在支付宝小程序端没有办法去掉，所以你的自定义导航栏返回按钮也得判断下隐藏掉，例如

   ```html
   <uni-nav-bar
   ...
   :left-icon="isAliPay ? '' : 'left'"
   ></uni-nav-bar>
   
   <!-- 或者你的返回按钮图标或图片也得加 -->
   <!-- #ifndef MP-ALIPAY -->
   <image mode="aspectFit" :src="file_ctx + 'static/image/business/hulu-v2/icon-circle-left-arrows.png'" class="header-search-img"/>
   <!-- #endif -->
   ```

   