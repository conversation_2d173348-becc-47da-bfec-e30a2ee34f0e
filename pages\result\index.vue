<template>
  <view class="content">
    <view class="top">
      <image
        class="success"
        :src="`${file_ctx}/h5/success.png`"
        mode="aspectFit"
      ></image>
      <view style="width: 80vw; color: #fff; font-size: 32upx">
        感谢对环保的支持，出袋指令已下发，请留意手机出袋结果通知！
      </view>
    </view>
    <view class="tips">
      <view class="title">*如机器未出袋,请联系客服</view>
      <view class="call">
        <text>客服电话：</text>
        <text @click="handleTel">************</text>
      </view>
    </view>
  </view>
</template>
<script>
import { checkHttp } from '@/utils'
import env from '@/common/util/index.js'

export default {
  data() {
    return {
      urlParams
    }
  },
  computed: {
    file_ctx () {
      return env.file_ctx
    }
  },
  mounted() {
    this.$nextTick(function(){
      sitecheck.initi()
    })

    const paramsData = this.urlParams.paramsData
    if (paramsData) {
      if (paramsData.freeLink) {
        this.$nextTick(() => {
          setTimeout(() => {
            // this.$navto.push('WebHtmlView', { src: checkHttp(paramsData.freeLink), title: '' })
            // window.location.href = checkHttp(paramsData.freeLink)
          }, paramsData.delaySecond * 1000 )
        })

      }
    }
    console.log(paramsData)

  },
  methods: {
    handleTel () {
      uni.makePhoneCall({
        phoneNumber: '************'
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  font-size: 28upx;
  .top {
    width: 160vw;
    height: 60vh;
    left: -30vw;
    border-radius: 0 0 50% 50%;
    background: #00c08f;
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .success {
      width: 300upx;
      margin-top: -100upx;
    }
  }
  .tips {
    text-align: center;
    width: 100vw;
    position: fixed;
    bottom: calc(40upx + env(safe-area-inset-bottom));

    .title {
      color: #9c9c9c;
      margin-bottom: 20upx;
    }
    .call {
      a,
      text {
        color: #00c08f;
        line-height: 40upx;
      }
      color: #00c08f;
      line-height: 40upx;
    }
  }
}
</style>
