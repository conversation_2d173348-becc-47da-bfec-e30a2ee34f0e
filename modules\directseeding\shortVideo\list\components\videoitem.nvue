<template>
	<view class="livePlayerBox" @click.self="hiddenInput" @touchstart="touchstart" @touchend="touchend" :style="{
      width: width,

    }">
		<!-- // 活动状态：1-草稿，2-预告，3-直播中，4-直播结束，5-回放，6-下架 -->
		<!-- 直播状态 -->

		<view v-if="!showApplyBtn" class="liveBox" :style="{
			display: data.activityStatus == 3 ? 'block' : 'none',
			top:data.screenDirectionType === 2 && orientation == 'vertical' ? '383rpx' : '50%',
			transform:data.screenDirectionType === 2 && orientation == 'vertical' ? '' : 'translateY(-50%)'}">
			<!-- #ifdef MP-WEIXIN -->
			<!-- 暂时注释 调试用 -->
			<live-player
      style="clip-path: inset(0px 0px 4rpx 0px);"
      :style="{
					width: liveWidth,
					height: liveHeight,
				}" :src="playurl1" :autoplay="true" :id="getLiveKey()" 
				@statechange="statechange" 
				@error="error"
				:muted="mutedPlay"
				:picture-in-picture-mode="mediaPictureMode"
				:object-fit="ata.screenDirectionType == 1 || orientation == 'horizontal' ? 'contain' : 'fillCrop'"
				@fullscreenchange="bindfullscreenchange" :orientation="orientation" ></live-player>
			<!-- #endif -->
			<!-- #ifdef H5 -->
			<!-- :src="playurl1" -->
			<view class="" :id="'myVideoyy' + idx" :ref="'myVideoyy' + idx" :style="{ width: width, height: height }">
			</view>
			<!-- #endif -->
			<!-- 横屏观看 -->
			<!-- <view class="horizontalbox" v-if="orientation == 'vertical'">横屏观看</view> -->
		</view>
		<!-- 加载中 -->
		<!-- #ifdef MP-WEIXIN || H5 -->
		<!-- <template v-if="!showApplyBtn && data.activityStatus == 3 && (loadingvisible || forbiddenVisible)"> -->
		<template v-if="false">
			<view class="loadingbg">
				<div class="loadingbox">
					<div class="loadingitem"></div>
					<div class="loadingitem"></div>
				</div>
			</view>
		</template>
		<!-- #endif -->
		<view :style="{display: (data.activityStatus == 2 || data.activityStatus == 3 || data.activityStatus == 5) ? 'flex' : 'none',width: width}" class="videobox">
			<!-- 我是视频 -->
			<template v-if="!showApplyBtn && ((data.videoPath && data.videoPath != '' && data.activityStatus !== 3) || data.activityStatus == 5) && data.activityStatus != 2">
				<video class="flex1 videoPotions"
					v-if='showVideoFlag'
          :id="'myVideo' + idx"
          :style="{
            width:videoStyle.width,
            height:videoStyle.height,
            top:videoStyle.top,
            transform:videoStyle.transform
            }"
          :autoplay="true"
          @timeupdate='timeupdate'
          @fullscreenchange='fullscreenchange'
          @ended='ended'
          @play='()=>isOpen = true'
          @pause='()=>isOpen = false'
          @loadedmetadata='loadedmetadata'
					@longpress="longpressVideo"
					:picture-in-picture-mode='mediaPictureMode'
					:src="videoPathAll" :controls="showControl" :duration="duration" :muted="mutedPlay" :title="data.title"
					:enable-progress-gesture='true'>
				</video>
        <!-- 回放中的海报 -->
        	<view class="countbg countbgLive videoPBg"></view>
        	<image :src="poster || file_ctx+initialCoverPaths" class="cover coverP"></image>
			</template>
			<!-- 直播海报 -->
			<template v-else>
				<view class="countbg countbgLive"></view>
				<image :src="poster || file_ctx+initialCoverPaths" class="cover"></image>
			</template>
		</view>

    <!-- 视频组件控件 -->
		<videoControl
			:showVideoFlag="!showApplyBtn && ((data.videoPath && data.videoPath != '' && data.activityStatus !== 3) || data.activityStatus == 5) && data.activityStatus != 2"
      @week='videoSeek'
      @openOrPause='videoOpenOrPause'
      @fullScreen='videoFullScreen'
			@changeSpeed='changeSpeed'
			@changeSuspend="changeSuspend"
      ref="videoControl"
      :commentShow='commentShow'
      :showControl='showControl'
      :isOpen='isOpen'
      :ControlTimeMap='ControlTimeMap'>
    </videoControl>
		<!-- 分享链接进来找不到对应条目 -->
		<view class="nofindbox" v-if="data.activityStatus == 10 || data.activityStatus == 4">
			<!-- 直播已结束 -->
			<image :src="poster" class="live-compute-bg" style="width: 100%;" mode="widthFix"></image>
			<view class="live-compute-box">
				<view class="live-compute-top">
					<view class="live-compute-t">
						直播已结束
					</view>
					<image :src="coverPathsUrl" class="live-compute-icon" mode="aspectFill"></image>
					<view class="live-compute-tw">
						{{data.title}}
					</view>
					<!-- <view class="live-compute-info">
            认证：八点汇（北京）健康科技有限公司
          </view> -->
				</view>
			</view>
		</view>

		<!-- 竖屏标题 -->
		<lv-header :headerobj="headerobj" @pre="preNext" position="absolute" v-if="orientation != 'horizontal'">
			<view class="livePlayerHeader" v-if="data.activityStatus !== '10' && data.activityStatus !== 4">
				<view class="livePlayerHeader-c" :style="{
          'background-color': data.activityStatus == 10 ? '#c8d0c7' : 'rgba(0, 0, 0, 0.4)'
        }">
					<image :src="detailPageLogo || coverPathsUrl" class="livePlayerHeader-ico"></image>
					<scroll-view :scroll-x="true" class="livePlayerHeader-txt" :class="{
            'max200':data.qrCodePath && data.qrCodePath != ''
          }">
						<view class="liveTitle">{{ data.title }}</view>
						<!-- <view class="playtxt" v-if="data.activityStatus == 3 || data.activityStatus == 5">{{ data.virtualViewNumber - 0 + (data.realViewNumber - 0) }} 观看</view> -->
					</scroll-view>
					<!-- <view class="livePlayerHeader-btn" @click="addUser" v-if="data.qrCodePath && data.qrCodePath != ''">加入粉丝团</view> -->
				</view>
				<view class="number-box" v-if="!showApplyBtn && (data.activityStatus == 3 || data.activityStatus == 5)">
					<view class="playtxt" v-if="data.activityStatus == 3 || data.activityStatus == 5">
						<image :src="file_ctx+'static/image/business/hulu-v2/multi-user.png'" class="liveMultiUser"></image>
						{{formatNumber( data.virtualViewNumber - 0 + (data.realViewNumber - 0) )}}人观看
					</view>
					<scroll-view v-if="data.activityStatus == 5" :scroll-y="true" :scroll-top="scrollTop"
						@touchmove.stop="() => {}" class="vertical-livePlayerChat vertical Playback" @click.stop
						:show-scrollbar="false" :enhanced="true" :scroll-into-view="scrollIntoView">
						<commentMap @loadmore="loadMoreChat" :orientation="orientation === 'horizontal'" :commentShow='commentShow'
							:scrollTop='scrollTop' :scrollIntoView='scrollIntoView' :data='data'
							:commentListPage='commentListPage' :emojiSource='emojiSource' :commentList='commentList'
							:lineHeight='lineHeight'>
						</commentMap>
					</scroll-view>
				</view>
				<image @click="$refs.videoControl.openControlPanels('emit')" :src="file_ctx+'static/image/business/hulu-v2/emit-icon.png'" class="scaleIcon" />
			</view>
		</lv-header>

		<!-- 横屏标题 -->
		<template v-else>
			<view class="horizontalOverlay">
				<commentMap  @loadmore="loadMoreChat" :orientation="orientation === 'horizontal'" :commentShow='commentShow'
					:scrollTop='scrollTop' :scrollIntoView='scrollIntoView' :data='data'
					:commentListPage='commentListPage' :emojiSource='emojiSource' :commentList='commentList'
					:lineHeight='lineHeight'>
				</commentMap>
			</view>
			<view class="horizontal-header" :style="{top:autoHeight + 'px'}">
				<!-- 我是横屏标题 -->
				<view class="livePlayerHeader" v-if="data.activityStatus !== '10' && data.activityStatus !== 4"
					style="width:100vh;">
					<view class="livePlayerHeader-c pl20" :style="{
            'background-color': data.activityStatus == 10 ? '#c8d0c7' : 'rgba(0, 0, 0, 0.4)',
          }">
						<image :src="detailPageLogo || coverPathsUrl" class="livePlayerHeader-ico"></image>
						<view class="livePlayerHeader-txt">
							<view class="textelipse">{{ data.title }}</view>
						</view>

						<!-- <view class="livePlayerHeader-btn" @click="addUser" v-if="data.qrCodePath && data.qrCodePath != ''">加入粉丝团</view> -->
					</view>
					<view class="H playtxt"
						v-if="!showApplyBtn && (data.activityStatus == 3 || data.activityStatus == 5)">
						<image :src="file_ctx+'static/image/business/hulu-v2/multi-user.png'" class="liveMultiUser"></image>
						{{ formatNumber( data.virtualViewNumber - 0 + (data.realViewNumber - 0) ) }}人观看
					</view>
				</view>
			</view>
		</template>
		<!-- <view class="livePlayerHeader" :style="{
       top:statusHeight + 'px',
       height:titleHeight + 'px'
     }"> -->
		<!-- {{data.activityStatus}} -->

		<!-- 报名 -->
		<view class="countbg" v-if="false || showApplyBtn">
			<view class="countmain">
				<view class="countitemt">
					<view class="countTitle">{{data.title}}</view>
					<!-- 预告 -->
					<template v-if="data.activityStatus === 2">
						<view>
							<view class="countTb">直播倒计时</view>
						</view>
					</template>

					<!-- 直播中 -->
					<template v-if="data.activityStatus === 3">
						<view>
							<view class="countTb">正在直播中，请报名观看</view>
						</view>
					</template>

					<!-- 回放 -->
					<template v-if="data.activityStatus === 5">
						<view>
							<view class="countTb">正在回放中，请报名观看</view>
						</view>
					</template>

				</view>
				<view class="timerShow countitem" v-if="data.activityStatus === 2">
					<view class="countNums">{{ hour }}</view>
					<view class="counttip">时</view>
					<view class="countNums">{{ minute }}</view>
					<view class="counttip">分</view>
					<view class="countNums">{{ second }}</view>
					<view class="counttip">秒</view>
				</view>
			</view>

			<!-- 报名 -->
			<view class="reservationbox">
				<view class="reservationb" @tap.stop="applyLive"
					:class="{ cancelBox: applyRecord && ![3].includes(applyRecord.auditStatus) }">
					<view class="reserveBox">
						<view class="reserve"
							:class="{ cancel: applyRecord && ![3].includes(applyRecord.auditStatus) }">
							<template v-if="applyRecord">
								<template v-if="applyRecord.auditStatus === 1">已提交报名，请耐心等待审核结果</template>
								<template v-else-if="applyRecord.auditStatus === 2">报名成功</template>
								<template v-else-if="applyRecord.auditStatus === 3">
									报名失败，点击重新报名
									<text v-if="applyRecord.rejectReason"
										class="tips error">{{ applyRecord.rejectReason }}</text>
								</template>
							</template>
							<template v-else>立即报名</template>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 预告倒计时 -->
		<view class="countbg" v-else-if="data.activityStatus == 2">
			<view class="countmain">
				<view class="countitemt">
					<view>
						<view class="countTitle">{{data.title}}</view>
						<view class="countTb">直播倒计时</view>
					</view>
				</view>
				<view class="timerShow countitem">
					<view class="countNums">{{ hour }}</view>
					<view class="counttip">时</view>
					<view class="countNums">{{ minute }}</view>
					<view class="counttip">分</view>
					<view class="countNums">{{ second }}</view>
					<view class="counttip">秒</view>
				</view>
			</view>

			<!-- 预约按钮 -->
			<view class="reservationbox">
				<view class="reservationb" @tap.stop="reservationClick" :class="{cancelBox:likestatus2}">
					<view class="reserveBox">
						<view class="reserve" :class="{cancel:likestatus2}">{{!likestatus2 ? '立即预约' : '取消预约'}}</view>
					</view>
				</view>
			</view>
			<!-- 观看预告 -->
			<view class="reservationbox" v-if="data.activityStatus == 2 && (data.videoPath && data.videoPath != '')">
				<view class="reservationb cancelBox" @tap.stop="lookPredict(data.videoPath)">
					<view class="reserveBox">
						<view class="reserve cancel">观看预告</view>
					</view>
				</view>
			</view>

		</view>

		<view class="center-tip" v-if="updateLoadingFlagHX">{{updateLoadingFlagText}}</view>
		<view class="livePlayerBottom"
			:style="{display:(data.activityStatus == 3 && (showApplyBtn ? applyRecord.auditStatus === 2 : true )) || data.activityStatus == 2 ? 'block' : 'none',}">
			<bottom-input :visible2="visible" :once="once" :editid="editid" :likestatus="data.likeSubscribeStatus == 1"
				:updateclear="updateclear" :title='data.title' :updatehidden="updatecount" :qrCodePath="data.qrCodePath"
				:sendBtnLoading="sendBtnLoading" @send="sendInput" :data='data' @addUser="addUser" @changeMask='changeMask' @checkIsLogin='checkIsLogin' :showMoreBtn="showMoreBtn"
				:orientation="orientation" :screenDirection="
          data.screenDirectionType == 2 && data.activityStatus == 3
        " @fullscreenchange="bindfullscreenchange" @changeEmojiShow="changeEmojiShow">
				<scroll-view :scroll-y="true" :scroll-top="scrollTop" @touchmove.stop="() => {}"
					class="vertical-livePlayerChat vertical" :show-scrollbar="false" :enhanced="true"
					:scroll-into-view="scrollIntoView">
					<commentMap @loadmore="loadMoreChat" :orientation="orientation === 'horizontal'" :commentShow='commentShow'
						:scrollTop='scrollTop' :scrollIntoView='scrollIntoView' :data='data'
						:commentListPage='commentListPage' :emojiSource='emojiSource' :commentList='commentList'
						:lineHeight='lineHeight' :showMask='showMask'>
					</commentMap>
				</scroll-view>
			</bottom-input>

		</view>

		<!-- 					<view v-if="!showApplyBtn" class="livePlayerBottom" :style="{
        display: data.activityStatus == 3 || data.activityStatus == 2 ? 'block' : 'none'
      }">
						<bottom-input :visible2="visible" :once="once" :editid="editid"
							:likestatus="data.likeSubscribeStatus == 1" :updateclear="updateclear"
							:updatehidden="updatecount" :qrCodePath='data.qrCodePath' :sendBtnLoading='sendBtnLoading'
							@send="sendInput" @addUser='addUser' :showMoreBtn="showMoreBtn" :orientation='orientation'
							:screenDirection="data.screenDirectionType == 2 && data.activityStatus == 3"
							@fullscreenchange='bindfullscreenchange' @changeEmojiShow='changeEmojiShow'></bottom-input>
					</view> -->

		<!-- 侧边导航条 -->
		<!-- <view class="rightNav"> -->
		<template v-if="!showApplyBtn && data.activityStatus != 3 && data.activityStatus != 2">
			<bottom-input :visible2="visible" :once="once" :showtype="1" :editid="editid"
				:likestatus="data.likeSubscribeStatus == 1" :updateclear="updateclear" :updatehidden="updatecount"
				:qrCodePath='data.qrCodePath' :data='data' :sendBtnLoading='sendBtnLoading' @send="sendInput" @checkIsLogin='checkIsLogin' @clicktab="clicktab"
				@addUser='addUser' :showMoreBtn="showMoreBtn" :screenDirectionType='data.screenDirectionType'
				:screenDirection="data.screenDirectionType == 1 && data.activityStatus == 3"
				@changeEmojiShow='changeEmojiShow'>
			</bottom-input>
		</template>

		<view class="hidden"><nui-emoji ref="emojiRef" :padding="0"></nui-emoji></view>

		<!-- likeSubscribeStatus -->


		<view class="hidden"><nui-emoji ref="emojiRef" :padding="0"></nui-emoji></view>

		<!-- likeSubscribeStatus -->

		<showbottommodel :show="showbottomVisible" zindex="999" :autotitle="true" :clickbg="false" type="center"
			@cancel="cancel" dialogStyle="background-color:#fff;height:460rpx" root-class="showbottominputmodel">
			<view class="ewmbox" @longpress="longpress">
				<image :src="data.ewmurl" :show-menu-by-longpress="true" class="ewmico"></image>
				<view class="ewmtip">长按识别二维码</view>
			</view>
		</showbottommodel>

		<apply-dialog ref="applyDialogRef" :editid="editid" :data="data" @confirm="meetingregisterCurrentRegister" />
		<!-- 预告视频 -->
		<predictVideo
			v-if="showVideo"
			:videoPath="videoPathAll"
			:muted="mutedPlay"
			:poster="poster"
			:title="data.title"
			@close='closePredict'
			>
		</predictVideo>
	</view>
</template>

<script>
	//#ifdef H5
	import flvjs from "flv.js";
	// #endif
  import videoControl from './videoControl.vue'
	import bottomInput from "./bottomInput.vue";
	import applyDialog from "./apply-dialog.vue";
	import predictVideo from "./predictVideo.vue"
	import {
		mapState
	} from "vuex";
	import nuiEmoji from "@/modules/directseeding/components/nui-emoji/nui-emoji.vue";

	import commentMap from "./commentMap.vue";

	import showbottommodel from "@/components/basics/showmodel/showmodel.vue";

	import lvHeader from "../components/lv-header.nvue";
	import {
		isDomainUrl,
		param
	} from "../../../../../utils";
	// import sliderNav from './sliderNav.vue'
	import env from "@/config/env";
	export default {
		name: "videoItem",
		props: {
			url: {
				type: String,
				// default:"webrtc://player.greenbontech.com/live/841745330109198341.flv",
				default: "webrtc://player.greenbontech.com/live/823996187362414595.flv",
				// default:"rtmp://178600.push.tlivecloud.com/live/843854580746940423?txSecret=0d2a9e7f1ce6b38eb86ad374cca73750&txTime=644D3F00",
				// default: 'rtmp://178600.push.tlivecloud.com/live/843854580746940423?txSecret=0d2a9e7f1ce6b38eb86ad374cca73750&txTime=644D3F00'
			},
			data: {
				type: Object,
				default: function() {
					return {};
				},
			},
			width: {
				type: String,
				default: "100vw",
			},
      initialCoverPaths: {
				type: String,
				default: "",
			},
			height: {
				type: String,
				// default: '100vh'
				default: "0vh",
			},
			Theight: {
				type: String,
				// default: '100vh'
				default: "0vh",
			},
			visible: {
				type: Boolean,
				default: false,
			},
			hideFlag: {
				type: Boolean,
				default: true,
			},
			// editid:{
			//   type:String,
			//   default:"",
			// }
			once: {
				type: Boolean,
				default: false,
			},
			editid: {
				type: [String, Number],
				default: null,
			},
			idx: {
				type: Number,
				default: 0,
			},
			ishidden: {
				type: Boolean,
				default: false,
			},
			// 直播类型
			businessType: {
				type: [Number, String],
				default: 7,
			},
			scrollViewTop: {
				type: [Number, String],
				default: 0,
			},
			clickMoreUpdateCount: {
				type: [Number, String],
				default: 0,
			},
		},
		components: {
			bottomInput,
			nuiEmoji,
			showbottommodel,
			lvHeader,
			// sliderNav
			applyDialog,
			commentMap,
			predictVideo,
      videoControl
		},
		computed: {
			...mapState("user", {
				codeUserInfo: (state) => state.codeUserInfo, // 当前登录用户信息
				accountId: (state) => state.accountId,
				curSelectUserInfo: (state) => state.curSelectUserInfo, // 当前选中的用户信息
				curSelectUserInfoId: (state) => state.curSelectUserInfoId, // 当前选中的档案的ID
				isLogin: (state) => state.isLogin,
			}),
			showApplyBtn() {
				// 开启报名 并且 在预告中或者还没有报名或者报名之后不为审核通过
        if(this.applyRecord === null) return false
				return (
					this.data.registerStatus === 1 &&
					(this.data.activityStatus === 2 ||
						!this.applyRecord ||
						(this.applyRecord && this.applyRecord.auditStatus !== 2))
				);
			},
		},
		data() {
			return {
				randomKey: String(Math.random()).split('.')[1],
				showVideoFlag:true,
				mediaPictureMode:'pop',
        videoStyle:{width:'100vw'},
        showControl:false,
        isOpen:true,
        videoContext:null,
        // 控制组件时间集合
        ControlTimeMap:[],
				showMask:false,
				showVideo: false,
				// 存储当前直播的初始高度
				initialHeight: '',
				// 当前直播高度
				liveHeight: '',
        // 当前直播宽度
				liveWidth: '100vw',
				cItemTimer: null,
				getInitScrollLoading: false,
				scrollBoxHeight: 0,
				scrollTop: 0,
				sendBtnLoading: false,
				// 评论区域是否显示
				commentShow: true,
				scrollIntoView: "",
				// 是否静音
				mutedPlay: true,

				showModelVisible: false,
				forbiddenVisible: false,
				currentStatus: "stop",
				// 用户观看定时器
				UserStatusTimer: null,
				// 用户观看状态
				anWatchStatus: true,

				orientation: "vertical",
				// 初始化评论
				initUU: false,
				// 预约状态
				likestatus2: false,
				showCenterPlayBtn: false,
				showFullscreenBtn: false,
				showPlayBtn: false,
				playerDom: null,
				loadingvisible: true,
        detailPageLogo:'',
				coverPathsUrl: "",
				ishiddenFn: false,
				hour: "00",
				minute: "00",
				second: "00",
				showMoreBtn: false,
				playurl1: "",
				videoHeight: "96vh",
				poster: "",
				videoPathAll: null,
				headerobj: {
					currentIndex: 3,
					headBgColor: "transparent",
					contentColor: "#fff",
				},
				titleHeight: 0,
				statusHeight: 0,
				autoHeight: 70,
				updateLoadingFlagHX: false,
				updateLoadingFlagText: "",
				showbottomVisible: false,
				autoplay: false,
				updateclear: 0,
				emojiSource: this.$static_ctx + "image/system/bg-face.png",
				lineHeight: 1,
				file_ctx: this.file_ctx,
				// $static_ctx: this.$static_ctx,
				$static_ctx: "http://localhost:3000/",
				userinfo: {
					// ico: static_ctx + 'image/directseeding/logo.jpg'
				},
				// 聊天记录
				commentList: [],
				// 页面显示的聊天记录
				commentListPage: [],
				noclick: false,
				pageChatCount: 20,
				// pageChatCount:1,
				iscommond: 0,
				updatecount: 0,
				// 直播间用户
				userlist: [
					// {
					//   nickname: '何人借我一壶酒'
					// }
				],
        timeErSend:null,
				applyRecord: null, // 当前用户报名记录
			};
		},
		onShow() {
		},
		mounted() {
			this.meetingregisterCurrentRegister();
			this.computeLiveHeight();
			this.initHeader();
			this.setMediaPictureMode();
			if (true) {
				this.initUU = true;
				this.mutedPlay = false;
				// 预告获取预约状态
				if (this.data.activityStatus == 2) {
					this.getcommoncollectlikes();
				}
				// 回放获取历史评论记录
				if (this.data.activityStatus == 5) {
					this.initCharList();
				}
				if (this.iscommond == 0) {
					this.getChatList();
				}
				this.initCommentVisibleFn();

				this.meetingcolumnQueryList();

				this.startCount();
				// console.log('0909');
				// 预告/回放
				let vid = "myVideo" + this.idx;
				this.videoContext = uni.createVideoContext(vid, this);
        console.myLog('videoContext',this.videoContext);
				console.log('当前直播id',this.idx);
				// 直播
				// #ifdef MP-WEIXIN
				let id = this.getLiveKey();
				this.playerCtx = uni.createLivePlayerContext(id, this);
				// #endif

				// console.log('this.playerCtx', this.playerCtx);

				setTimeout(() => {
					console.log('触发创建用户', this.playerCtx);
					// let that = this;
					// this.bindPlay(true);
					this.currentStatus = "stop";
					// 创建用户
					this.meetingviewusersaveuser(false);
				}, 500);
			}

			// 表情尺寸
			const systemInfo = uni.getSystemInfoSync();
			var radio = 750 / systemInfo.windowWidth;
			this.lineHeight = 50 / radio;

			// this.$nextTick(() => {
			//   this.getsliderW();
			// });
		},
		watch: {
			// data() {
   //      // 每次更新时清除数据
   //      this.ControlTimeMap = [];
			// 	this.computeLiveHeight();
			// },
			mediaPictureMode(n) {
				console.log('mediaPictureMode实时变更',this.mediaPictureMode);

			},
			commentShow(n) {
				if (this.visible) {
					this.$emit("hiddenInput", {
						commentShow: n,
						uuid: this.data.id,
					});
				}
			},
			clickMoreUpdateCount(n) {
				if (this.visible) {
					this.loadMoreChat();
				}
			},
			scrollViewTop(n) {
				console.log("n", this.scrollTop);
				if (this.visible) {
					this.scrollTop = n;
				}
			},
			visible(n) {
				console.log('visible',n);
				if (n) {
					// this.countChatItem();
					this.$emit("updateSwiperHeight", {
						height: 0,
						loadMoreVisible: false,
					});
					this.scrollBoxHeight = 0;
					this.commentList = [];

					this.mutedPlay = false;
					this.commentShow = true;
					this.initCommentVisibleFn();

					this.currentStatus = "stop";
					this.orientation = "vertical";
					this.anWatchStatus = 1;
					this.initUU = true;
					// 初始化海报，避免iOS闪烁
					this.setPosterUrls();
					// this.getChatList();
					if (this.iscommond == 0) {
						this.getChatList();
					}

					// #ifdef MP-WEIXIN
					let id = this.getLiveKey();
					if (!this.playerCtx) {
						this.playerCtx = uni.createLivePlayerContext(id, this);
					}
					// #endif

					// 预告/回放
					let vid = "myVideo" + this.idx;
					if (!this.videoContext) {
						this.videoContext = uni.createVideoContext(vid, this);
					}

					setTimeout(() => {
						// 创建用户
						this.meetingviewusersaveuser();
					}, 500);
					this.meetingcolumnQueryList();

					this.startCount();

					// 预告获取预约状态
					if (this.data.activityStatus == 2) {
						this.getcommoncollectlikes();
					}
					// 回放获取历史评论记录
					if (this.data.activityStatus == 5) {
						this.initCharList();
					}
				} else {
					this.mutedPlay = true;

					this.forbiddenVisible = false;
					this.orientation = "vertical";
					this.anWatchStatus = 2;

					this.loadingvisible = true;
					this.initItem();

					this.stopVideo(true,1);

					this.clearCount();
				}
			},
			ishidden(n) {
				this.ishiddenFn = n;
				if (!n) {
					// if (this.iscommond == 0) {
					this.getChatList();
					// }
				}
			},
			// 用户观看状态
			anWatchStatus(n) {
				// 禁用用户观看
				// if (this.anWatchStatus == 1) {
				//    this.stopVideo();
				// }else if(this.anWatchStatus == 2){
				// this.stopVideo();
				// setTimeout(() => {
				//    // console.log('play');
				//    // let that = this;
				// }, 500);
				// }
			},
			orientation(n) {
				// 当前方向为竖屏时
				if (this.orientation === 'vertical') {
					this.liveHeight = this.initialHeight;
				} else {
					let scale = 16 / 9;
					let FaWidth = +this.getWindowWidth();
					console.log('FaWidth', FaWidth);
					this.liveHeight  = (scale * FaWidth) + 'px';
				}
				// this.$emit("hiddenInput", {
				// 	commentShow: false,
				// 	uuid: this.data.id,
				// });
			},
			// hideFlag(n) {
			// 	if(n) return;
			// 	if (uni.getSystemInfoSync().platform === 'ios' && this.mediaPictureMode === '') {
   //      	this.forceExitPictureInPicture();
   //  		}
			// }
		},
		methods: {
			getLiveKey(){
				return "livePlayer" + this.idx + this.randomKey;
			},
			forceExitPictureInPicture() {
			  // 强制清除画中画状态
			  if (this.videoContext) {
			      this.videoContext.exitPictureInPicture();
			  }
			  if (this.playerCtx) {
			      this.playerCtx.exitPictureInPicture();
			  }

			  // 重置组件状态
			  this.mediaPictureMode = '';
			  this.showVideoFlag = false;

			  this.$nextTick(() => {
			      this.showVideoFlag = true;
			  });
			},
      ended(){
        this.checkVideoContext()
        console.log('播放结束');
        this.isOpen = false;
        this.$refs.videoControl.ended()
      },
      fullscreenchange(event){
        this.showControl = event.detail.fullScreen;
        console.myLog('event',event.detail.fullScreen);
      },
      videoFullScreen(){
        this.checkVideoContext()
        this.videoContext.requestFullScreen()
      },
      videoOpenOrPause(){
        this.checkVideoContext()
        if(this.isOpen){
          this.videoContext.pause()
        }else{
          this.videoContext.play()
        }
        this.isOpen = !this.isOpen;
      },
      videoSeek(time){
        this.checkVideoContext()
        this.videoContext.seek(time)
      },
			changeSpeed(speed){
				this.videoContext.playbackRate(speed)
			},
			setMediaPictureMode(){
				let mediaPictureMode = uni.getStorageSync('mediaPictureMode')
				if(mediaPictureMode === '') {
					mediaPictureMode = 'pop';
					uni.setStorageSync('mediaPictureMode',mediaPictureMode)
				}
				mediaPictureMode = mediaPictureMode === 'none' ? '' : mediaPictureMode;
				// 如果是关闭模式 则手动清空小窗功能
				if(!mediaPictureMode){
					this.videoContext && this.videoContext.exitPictureInPicture()
					this.playerCtx && this.playerCtx.exitPictureInPicture()
				}
				this.mediaPictureMode = mediaPictureMode;
			},
			changeSuspend(flag){
				this.mediaPictureMode = flag ? 'pop' : '';
				uni.setStorageSync('mediaPictureMode', this.mediaPictureMode ? 'pop' : 'none');
				console.log('flag', flag,this.mediaPictureMode);
				if(!flag){
					this.videoContext && this.videoContext.exitPictureInPicture()
					this.playerCtx && this.playerCtx.exitPictureInPicture()
					this.showVideoFlag = false;
				}
				this.$nextTick(() => {
					this.showVideoFlag = true;
    		})
			},
			// 长按弹出倍速选择模块
			longpressVideo(){
				this.$refs.videoControl.openControlPanels('scpeed')
			},
      checkVideoContext(){
        let vid = "myVideo" + this.idx;
        console.myLog('this.videoContext',this.videoContext)
        if (!this.videoContext) {
        	this.videoContext = uni.createVideoContext(vid, this);
        }
      },
      loadedmetadata({target:{width,height}}){
        // 为了防止奇怪的尺寸比例 这里强制认为 宽度大于长度 视频就是横屏 否则则为竖屏
        console.myLog('event',width,height)
        let Pscale = 16 / 9;
        let Hscale = 9 / 16;
        let scale;
        let FaWidth = +this.getWindowWidth();
        if(width>height) {scale = Hscale;this.videoStyle.top = '383rpx';this.videoStyle.transform = 'translateY(0px)'}
        if(width<height) {scale = Pscale;this.videoStyle.top = '50%';this.videoStyle.transform = 'translateY(-50%)'}
        this.videoStyle.width = FaWidth + 'px';
        this.videoStyle.height = (scale * FaWidth - 1.5) + 'px';
        console.myLog('videoStyle',this.videoStyle)
      },
      timeupdate({detail:{currentTime,duration}}){
        this.ControlTimeMap = [currentTime,duration];
      },
      formatNumber(num) {
        if (num >= 10000) {
          let numberParts = (num / 10000).toFixed(1).toString().split('.');
          numberParts[1] == 0 ? num = numberParts[0] : num = numberParts.join('.');
          return num + '万';
        } else {
          return num;
        }
      },
			changeMask(n){
				this.showMask = n;
			},
			// 关闭视频组件
			closePredict(){
				this.showVideo = false;
			},
			lookPredict() {
				// 打开自己手作的视频组件
				this.showVideo = true;
				// 微信小程序提供的预览接口
				// wx.previewMedia({
				// 	sources: [{
				// 		url: this.videoPathAll,
				// 		type: 'video'
				// 	}],
				// 	complete(e){
				// 		console.myLog('预览调用结束',e)
				// 	}
				// })
			},
			// 计算当前直播组件的高度
			computeLiveHeight() {
				let height;
				let type = this.data.screenDirectionType;
				// 求得比例
				let verticalScale = 9 / 16; //竖屏
				let horizontalScale = 16 / 9; //横屏
				let scale = verticalScale
				if (this.orientation !== 'vertical' || type === 1) scale = horizontalScale
				let FaWidth = +this.getWindowWidth();
				height = (scale * FaWidth ) + 'px';
				// this.liveHeight = height;
				// if(this.orientation === 'vertical' && type === 1){
				// 	height = '100vh'
				// }
				this.liveHeight = height;
				this.liveWidth = FaWidth + 'px';
				// 存储初始高度
        if(!this.initialHeight){
          this.initialHeight = height;
        }
			},
			// 获取当前屏幕宽度
			getWindowWidth() {
				var systemInfo = uni.getSystemInfoSync();
				return systemInfo.windowWidth;
			},
			// 获取当前用户提交登记
			async meetingregisterCurrentRegister() {
				const params = {
					meetingId: this.editid,
					type: this.data.activityStatus === 2 ? 1 : 2, // 1: 观看直播, 2: 观看回访
					accountId: this.accountId,
				};
				const res = await this.$api.cloudClassroom.meetingregisterCurrentRegister(
					params
				);
				this.applyRecord = res.data;
			},
			applyLive() {
				if (this.applyRecord && ![3].includes(this.applyRecord.auditStatus))
					return;
				if (!this.isLogin) {
					this.$navto.replaceAll("Login", {
						formPage: "shortVideoList",
						formPageParams: encodeURIComponent(
							JSON.stringify({
								id: this.data.id,
							})
						),
					});
					return;
				}
				this.$refs.applyDialogRef.open();
			},
			changeEmojiShow({
				visible
			}) {
				this.$emit("changeEmojiShow", {
					visible: visible,
				});
			},
			countChatItem() {
				let xh = this.commentListPage.length * 20;
				// 暂时关闭
				// return
				if (xh > this.scrollBoxHeight) {
					this.getScrollViewHeight();
				}
			},
			touchend() {
				console.log("touchend");
				this.$emit("touchend");
			},
			touchstart() {
				console.log("touchstart");
				this.$emit("touchstart");
			},
			// 获取滚动区域高度
			async getScrollViewHeight() {
				// 暂时用不上获取滚动区高度 暂时搁置该方法
				return
				if (this.getInitScrollLoading) {
					return;
				}
				this.getInitScrollLoading = true;

				const that = this;
				await this.$nextTick();
				await this.sheep(500);
				const query = uni.createSelectorQuery().in(this);
				query
					.select("#livePlayerChatBox")
					.boundingClientRect((data) => {
						console.log("得到布局位置信息", data);
						console.log(
							"节点离页面顶部的距离为" + data.height,
							"that.scrollBoxHeight",
							that.scrollBoxHeight,
							that.scrollBoxHeight > data.height
						);
						if (that.scrollBoxHeight > data.height) {
							that.getInitScrollLoading = false;
							// that.getScrollViewHeight()
							return;
						} else {
							// console.log('8899---')
							let loadMoreVisible =
								that.commentListPage.length != that.commentList.length &&
								that.commentList.length > that.commentListPage.length;
							that.scrollBoxHeight = data.height;
							console.log("this.scrollBoxHeight", this.scrollBoxHeight);
							that.$emit("updateSwiperHeight", {
								height: data.height,
								loadMoreVisible: loadMoreVisible,
								uuid: that.data.id,
								// allHeight:data.height + (loadMoreVisible ? 24 : 0)
							});
							that.getInitScrollLoading = false;
						}
					})
					.exec();
			},
			initCommentVisibleFn() {
				if (this.data.activityStatus === 4 || this.data.activityStatus === "10") {
					this.commentShow = false;
				}
			},
			// 获取当前用户观看状态定时器
			setIntervalUserStatus() {
				if (this.UserStatusTimer) {
					clearInterval(this.UserStatusTimer);
				}

				// 20秒一次
				this.UserStatusTimer = setInterval(() => {
					if (this.ishiddenFn) {
						this.clearUserStatus();
					}
					this.getMeetingchatByUseridAndMainId();
				}, 20000);
				// if (this.ishiddenFn) {
				//   this.iscommond = 0;
				//   return;
				// }
				// // this.ceinit();
				// // return;
				// if (!this.data || !this.data.id) {
				//   // console.log(this.data)
				//   await this.sheep(500);
				//   this.getChatList();
				//   return;
				// }
			},
			clearUserStatus() {
				clearInterval(this.UserStatusTimer);
			},
			///** 获取当前用户观看状态 */
			async getMeetingchatByUseridAndMainId(isonce) {
				if (!this.visible) {
					return;
				}
				// 直播中才需要获取观看状态
				// if (this.meetingDetail.activityStatus == 3) {
				const res =
					await this.$api.cloudClassroom.getMeetingchatByUseridAndMainId({
						mainId: this.editid,
					});
				console.log("res", res);
				if (res.data) {
					let anWatchStatus = res.data?.anWatchStatus;

					//
					if (anWatchStatus == 1 && this.anWatchStatus == 2 && this.visible) {
						console.log("进来了", 1);

						this.stopVideo();
						this.anWatchStatus = anWatchStatus;
						// 禁用
						this.forbiddenVisible = true;
						if (this.showModelVisible) {
							return;
						}
						this.showModelVisible = true;
						const _this = this;
						uni.showModal({
							title: "提示",
							content: "你已经被禁用观看，如有需要，请联系管理员",
							success(res) {
								if (res.confirm) {
									console.log("用户点击确定");
									_this.showModelVisible = false;
								} else if (res.cancel) {
									console.log("用户点击取消");
									_this.showModelVisible = false;
								}
							},
						});
					} else if (
						anWatchStatus == 2 &&
						this.anWatchStatus == 1 &&
						this.visible
					) {
						this.forbiddenVisible = false;
						// this.stopVideo();
						this.anWatchStatus = anWatchStatus;
						setTimeout(() => {
							console.log("play");
							// let that = this;
							this.bindPlay(isonce);
						}, 500);
					} else if (anWatchStatus == 1 && this.anWatchStatus == 1) {
						this.forbiddenVisible = true;
						console.log("进来了", 2);
						if (this.showModelVisible) {
							return;
						}
						this.showModelVisible = true;

						const _this = this;
						uni.showModal({
							title: "提示",
							content: "你已经被禁用观看，如有需要，请联系管理员",
							success(res) {
								if (res.confirm) {
									console.log("用户点击确定");
									_this.showModelVisible = false;
								} else if (res.cancel) {
									console.log("用户点击取消");
									_this.showModelVisible = false;
								}
							},
						});
					}
				} else {
					this.anWatchStatus = 2;
					setTimeout(() => {
						console.log("play2",Date.now(),this.getLiveKey());
						// let that = this;
						this.bindPlay(isonce);
					}, 500);
				}

				// }
			},
			bindfullscreenchange(e) {
				console.log(e);
				this.orientation = e.detail.orientation;
			},
			// 切换画面
			toggleVertical() {},
			// 获取预告预约状态
			getcommoncollectlikes() {
				console.log("getcommoncollectlikes");
				this.getUserInfo().then(() => {
					// console.log('this.$api.cloudClassroom.getcommoncollectlikes',this.$api.cloudClassroom.getcommoncollectlikes)
					const {
						centerUserId = ""
					} = this.curSelectUserInfo || {};

					this.$api.cloudClassroom
						.getcommoncollectlikes({
							// type:2,
							businessType: 9,
							businessId: this.editid,
							// id:this.editid,
							source: 1, // 1-真实用户，2-马甲用户,3-系统用户
							accountId: this.accountId,
							userId: centerUserId,
							type: "6", // 预约
						})
						.then((ret) => {
							// console.log('ret', ret);
							// console.log('kkkkkk');
							if (ret.data && ret.data.length != 0) {
								if (ret.data[0].subscribeStatus == 1) {
									this.likestatus2 = true;
								} else {
									this.likestatus2 = false;
								}
								// this.likeid = ret.data[0].id;
							} else {
								this.likestatus2 = false;
							}
						});
				});
			},
			// 弹窗阻断
			popupBlocking() {
				return new Promise((resolve) => {
					uni.showModal({
						title: "提示",
						content: "你确定取消预约吗？",
						success(res) {
							if (res.confirm) {
								resolve(true);
								return;
							}
							resolve(false);
						},
					});
				});
			},
			//  直播结束
			async reservationClick() {
				//点赞接口调用
				await this.getUserInfo();
				// this.clickgood(...arguments);
				// console.log('this.$api.cloudClassroom.getcommoncollectlikes',this.$api.cloudClassroom.getcommoncollectlikes)
				const {
					centerUserId = ""
				} = this.curSelectUserInfo || {};
				// 分享操作
				// 没有登陆，跳往登陆页面
				if (!centerUserId || centerUserId == "") {
					// this.meetingviewlogvisit2();
					this.$navto.replaceAll("Login", {
						formPage: "shortVideoList",
						formPageParams: encodeURIComponent(
							JSON.stringify({
								id: this.data.id,
							})
						),
					});
					return;
				}
				// 消息提示
				// likestatus2
				let likestatus2 = this.likestatus2;
				var that = this;
				if (likestatus2) {
					let blocking = await this.popupBlocking();
					console.log("blocking", blocking);
					if (!blocking) return;
				}
				console.log("触发");
				that.$api.cloudClassroom.postcommoncollectlikesupdate({
					businessId: that.editid,
					source: 1, // 1-真实用户，2-马甲用户,3-系统用户
					accountId: that.accountId,
					userId: centerUserId,
					// type: '2', // 类型：1-评论，2-点赞，3-收藏，4-阅读，5-分享
					// businessType: 7 // 直播
					businessType: 9,
					type: 6, // 预约
					subscribeStatus: !that.likestatus2 ? 1 : 2, // 订阅状态1是，2否
				});
				let msg = `${!likestatus2 ? "预约成功" : "已取消预约"}`;
				this.$uniPlugin.successToast(msg, true);
				that.likestatus2 = !that.likestatus2;
				// this.postcommoncollect2(6)
				// 预约成功添加订阅消息接口方法
				if (that.likestatus2) {
					// #ifdef MP-WEIXIN
					await that.$uniPlugin.subscribeMessage([
						"RR84vyC1xY3wEKPw5SqquBUSIwmhz0IzmvYraAhil4k",
					]);
					const tmplId = "RR84vyC1xY3wEKPw5SqquBUSIwmhz0IzmvYraAhil4k";
					uni.requestSubscribeMessage({
						tmplIds: [tmplId],
						success(res) {
							if (res[tmplId] === "accept") {
								that.$uniPlugin.toast("消息订阅成功");
							} else {
								that.$uniPlugin.toast("消息订阅失败");
							}
							const {
								centerUserId = ""
							} = that.curSelectUserInfo || {};
							const params = {
								appId: env.appId,
								templateId: tmplId,
								openId: that.$common.getCache("openId") || "",
								subscribeStatus: res[tmplId],
								businessType: 1, // 直播活动
								businessId: that.editid,
								accountId: that.accountId,
								userId: centerUserId,
							};
							that.$api.common.wxsubscribemessagelogInsert(params);
							// 消息订阅调起成功，返回值'accept'、'reject'、'ban'分别代表用户对此条订阅是同意、拒绝、后台禁用
						},
						fail(err) {
							console.log("err：", err);
							//消息订阅调起失败
							that.$uniPlugin.toast("消息订阅失败err：" + JSON.stringify(err));
						},
						complete() {
							// 无论确定还是取消，只要点击按钮后都会执行
						},
					});
					// #endif
				}
			},
			// 初始化页面
			initItem() {
				this.noclick = false;
				this.commentList = [];
				// this.commentListPage = [];
				this.pageChatCount = 20;
			},
			// 加载更多评论
			loadMoreChat() {
				if (this.noclick) {
					return;
				}
				this.noclick = true;
				this.pageChatCount += 20;
				this.commentListPage = this.commentList.slice(0, this.pageChatCount);
				this.getScrollViewHeight().then((res) => {
					this.noclick = false;
				});
				// this.$nextTick(() => {
				//   this.noclick = false;
				// })
			},
			getLivePlayer() {
				uni.showLoading({
					mask: true,
					title: "loading...",
				});
				// 生成需要的video 组件
				// var player = document.getElementById('videoElement');
				let vid = "myVideoyy" + this.idx;

				var player = document.createElement("video");
				player.id = "video";
				player.style =
					"width:" + this.width + ";height:" + this.height + ";object-fit:fill";
				player.enableProgressGesture = this.enableProgressGesture;
				player.controls = this.controls;
				// player.muted = true;
				player.showCenterPlayBtn = this.showCenterPlayBtn;
				player.showPlayBtn = this.showPlayBtn;
				player.showFullscreenBtn = this.showFullscreenBtn;
				player.x5VideoPlayerType = "h5-page";
				player.x5VideoPlayerFullscreen = "false";
				player.autoplay = this.autoplay; // 以上均为 video标签的属性配置
				// player.style = 'fill';
				this.playerDom = player;
				document.getElementById(vid).innerHTML = "";
				document.getElementById(vid).appendChild(player);
				// console.log(player);
				// console.log(flvjs.isSupported());
				let urls = this.playurl1;
				if (flvjs.isSupported()) {
					this.flvPlayer = flvjs.createPlayer({
						type: "flv",
						isLive: true, //<====直播的话，加个这个
						url: urls, //直播流地址
					});
					this.flvPlayer.attachMediaElement(player);
					this.flvPlayer.load(); //加载

					setTimeout(() => {
						// var player2 = document.createElement('video');
						// player2.play();
						this.flvPlayer.play();
						this.loadingvisible = false;
					}, 2000);
					this.player = player;
					this.flvPlayer.on(
						flvjs.Events.ERROR,
						(errorType, errorDetail, errorInfo) => {
							console.log("播放错误");
							this.flv_destroy();
							// this.loadingvisible = true
						}
					);
					this.flvPlayer.on(
						flvjs.Events.LOADING_COMPLETE,
						(errorType, errorDetail, errorInfo) => {
							// console.log('播放错误');
							// this.flv_destroy();
							// this.loadingvisible = false
						}
					);
					uni.hideLoading();
				}
			},
			flv_start() {
				//开始
				console.log(this.player);
				this.player.play();
			},
			flv_pause() {
				//暂停
				this.player.pause();
			},
			flv_destroy() {
				//停止
				//#ifdef H5
				if (this.player) {
					this.player.pause();
					this.player.unload();
					this.player.detachMediaElement();
					this.player.destroy();
					this.player = null;
					this.flvPlayer.stop();
					// let vid = 'myVideoyy' + this.idx;
					// document.getElementById(vid).removeChild(this.player)
				}
				// #endif
			},
			flv_seekto() {
				// 复制其他人的  我还没用这个
				this.player.currentTime = parseFloat(
					document.getElementsByName("seekpoint")[0].value
				);
			},
			// 检测浏览器是否支持 flv.js
			createdPlay() {
				this.getLivePlayer();
				// console.log('createdPlay');
				// console.log(flvjs.isSupported());
				// if (flvjs.isSupported()) {
				//   let vid = 'myVideoyy' + this.idx;
				//   // var videoDom = document.getElementById(dom)
				//   // var videoDom = uni.createVideoContext(vid, this);
				//   var videoDom = this.$refs[vid];
				//   console.log('videoDom', videoDom);
				//   // console.log(flvjs.createPlayer)
				//   let urls = this.playurl1;
				//   // console.log('url',url)
				//   // 创建一个播放器实例
				//   try {
				//     // var player = flvjs.createPlayer({
				//     //   type: 'flv', // 媒体类型，默认是 flv,
				//     //   isLive: true, // 是否是直播流
				//     //   hasAudio: true, // 是否有音频
				//     //   hanVideo: true, // 是否有视频
				//     //   url: url // 流地址
				//     // },{
				//     //   // 其他的配置项可以根据项目实际情况参考 api 去配置
				//     //   autoCleanupMinBackwardDuration: true, // 清除缓存 对 SourceBuffer 进行自动清理
				//     // })
				//     var player = flvjs.createPlayer(
				//       {
				//         type: 'flv',
				//         isLive: true,
				//         url: urls
				//       },
				//       {
				//         enableWorker: false, //不启用分离线程
				//         enableStashBuffer: false, //关闭IO隐藏缓冲区
				//         isLive: true,
				//         lazyLoad: false
				//       }
				//     );
				//     flvjs.LoggingControl.enableInfo = false;
				//     flvjs.LoggingControl.enableWar = false;
				//     flvjs.LoggingControl.enableError = false;
				//     console.log(player);
				//     // this._mediaElement.play is not a function
				//     player.attachMediaElement(videoDom);
				//     player.load();
				//     player.play();
				//     // console.log(player)
				//     this.player = player;
				//   } catch (e) {
				//     console.log(e);
				//   }
				// }
			},
			preNext() {
				// this.meetingviewlogvisit2();
				this.ishiddenFn = true;
				this.$emit("pre");

				this.clearCount();

				this.$nextTick(() => {
					let pages = getCurrentPages();
					// console.log(pages)
					if (pages.length == 1) {
						uni.switchTab({
							url: "pages/index/index",
						});
					} else {
						uni.navigateBack({
							delta: 1,
							fail: function() {
								console.log("kkk");
							},
						});
					}
				});
				// this.$emit()
			},
			clearCount() {
				if (this.setTimer) {
					clearInterval(this.setTimer);
				}
			},
			// 开始计算
			startCount() {
				if (this.data.activityStatus != 2) {
					return;
				}
				// let date
				if (this.setTimer) {
					clearInterval(this.setTimer);
				}

				console.log("startCount",this.data);
				let inputTime = this.data.startTime - 0;
				this.setTimer = setInterval(() => {
					// 暂时关闭
					// return
					let nowTime = new Date().getTime();

					var times = (inputTime - nowTime) / 1000; //times 是剩余时间总的秒数
					console.log("times", times, times < 0);
					if (times < 0) {
						(this.hour = "00"), (this.minute = "00"), (this.second = "00");
						clearInterval(this.setTimer);
						return;
					}
					// var h = parseInt((times / 60 / 60) % 24); //计算小时
					var h = parseInt(times / 60 / 60); //计算小时
					h = h < 10 ? "0" + h : h;
					var m = parseInt((times / 60) % 60); //计算分钟
					m = m < 10 ? "0" + m : m;
					var s = parseInt(times % 60); //计算秒数
					s = s < 10 ? "0" + s : s;
					this.hour = h;
					this.minute = m;
					this.second = s;
					if (s == "00" && h == "00" && m == "00") {
						clearInterval(this.setTimer);
					}
				}, 1000);
			},
			// 获取互动栏目
			async meetingcolumnQueryList() {
				// const res = await this.$api.meeting.cloudClassroom({ mainId: this.editid });
				const res = await this.$api.cloudClassroom.meetingcolumnQueryList({
					mainId: this.editid,
				});
				if (res.data.length > 0) {
					this.showMoreBtn = true;
				} else {
					this.showMoreBtn = false;
				}
			},
			// getsliderW() {
			//   // console.log(this.$refs.sliderRef.clientWidth)
			//   var obj = uni.createSelectorQuery().in(this);
			//   let that = this;
			//   console.log(obj);
			//   setTimeout(() => {
			//     console.log('999')
			//     obj.select('.getheight').boundingClientRect(function(rect) {
			//       console.log('rect');
			//       console.log(rect);
			//       console.log(rect.height);
			//       console.log(rect.width);
			//       this.boxLineHeight = rect.height + 'px';
			//       console.log('this.boxLineHeight', this.boxLineHeight);

			//     });
			//   }, 1000);

			//   obj.exec();
			// },
			clicktab(obj) {
				console.log("obj", obj);
				// 预告/回放
				let vid = "myVideo" + this.idx;
				if (!this.videoContext) {
					this.videoContext = uni.createVideoContext(vid, this);
				}

				this.videoPathAll = isDomainUrl(obj.videoPath);
				this.poster = isDomainUrl(obj.coverPaths);
        console.myLog('poster1',this.poster)
        this.detailPageLogo = isDomainUrl(obj.detailPageLogo);
				this.coverPathsUrl = isDomainUrl(obj.coverPaths);

				this.videoContext.stop({
					success: function() {
						console.log("停止播放成功");
					},
					error: function() {
						console.log("停止播放失败");
					},
				});
				this.$nextTick(() => {
					this.videoContext.play({
						success: function() {
							console.log("播放成功");
						},
						error: function() {
							console.log("播放失败");
						},
					});
				});
			},
			// 停止播放视频
			stopVideo(novisible) {
				console.log('stopVideo')
				if (this.currentStatus != "play") {
					return;
				}
				this.currentStatus = "stop";

				// #ifdef MP-WEIXIN
				let id = this.getLiveKey();
				if (!this.playerCtx) {
					this.playerCtx = uni.createLivePlayerContext(id, this);
				}
				// #endif
				if (this.playerCtx) {
					setTimeout(() => {
						this.playerCtx.stop({
							success: function() {
								console.log("停止播放成功");
							},
							error: function() {
								console.log("停止播放失败");
							},
						});
					}, 500);
					// flv_destroy
				}

				// 停止播放视频
				// 预告/回放
				let vid = "myVideo" + this.idx;
				if (!this.videoContext) {
					this.videoContext = uni.createVideoContext(vid, this);
				}
				if (this.videoContext) {
					setTimeout(() => {
						this.videoContext.stop({
							success: function() {
								console.log("停止播放成功");
							},
							error: function() {
								console.log("停止播放失败");
							},
						});
					}, 500);
				}

				// H5 播放
				this.flv_destroy();

				// 滑动过快会出问题
				if (!novisible) {
					this.meetingviewlogvisit2();
				}
			},
			// 初始化头部导航栏
			initHeader() {
				let systemInfo = uni.getSystemInfoSync();
				let statusHeight = systemInfo.statusBarHeight; //状态栏的高度
				let titleHeight = 45; //导航栏高度，这个一般是固定的
				let allheights = systemInfo.windowHeight;
				console.log(systemInfo);
				this.statusHeight = statusHeight;
				this.titleHeight = titleHeight;
				this.autoHeight = this.statusHeight + this.titleHeight;
				// this.allheights = allheights;
			},
			// 创建用户
			meetingviewusersaveuser(isone) {
				const fansRecord = this.$common.getKeyVal("user", "fansRecord");
				// 用户id
				const tempParams = {};
				if (this.curSelectUserInfo && this.curSelectUserInfo.centerUserId) {
					tempParams.userId = this.curSelectUserInfo.centerUserId;
				}
				this.$api.cloudClassroom
					.meetingviewusersaveuser({
						mainId: this.editid,
						accountId: this.accountId,
						phone: "",
						nickName: fansRecord.nickName,
						...tempParams,
						// source:2,
					})
					.then((ret) => {
						// 获取用户观看状态
						this.setIntervalUserStatus();
						this.getMeetingchatByUseridAndMainId(isone);
					});
			},
			meetingviewlogvisit2() {
				this.$api.cloudClassroom
					.meetingviewlogvisit({
						mainId: this.editid,
						source: 2,
						// cookieId: this.$common.getTokenUuid()
						cookieId: this.$common.getCookieUuid(
							this.editid,
							this.$api.cloudClassroom.meetingviewlogvisit
						),
					})
					.then((ret) => {});
			},
			bindPlay(isonce) {
				console.myLog('触发bindPlay',isonce,this.currentStatus)
				if (this.currentStatus != "stop") {
					return;
				}
				this.currentStatus = "play";

				// console.log('999');
				// that.keyVoice.play();
				// try {
				//   // this.playerCtx.play();
				//   this.playerCtx.stop({
				//     success: function() {
				//       console.log('成功了');
				//       // this.$uniPlugin.toast('暂无功能');
				//     },
				//     error: function() {
				//       this.$uniPlugin.toast('加载失败');
				//     }
				//   });
				// } catch (e) {}
				// : 1-访问,2-点击,3-长按，4-关注，5-取关,6-点赞,7-收藏，8-删除评论，9-取消点费，10-取消收藏，1-阅读，12新增评论
				// this.postcommoncollect2(1);
				// this.postcommoncollect2(4);
				// 访问
				if (!isonce) {
					// 记录正在播放的id
					if (!this.ishiddenFn) {
						uni.$playingMeetingId = this.editid;
						this.meetingviewlogvisit2();
					}
				}
				// // 创建用户
				// this.meetingviewusersaveuser();
				console.log("this.data.activityStatus", this.data.activityStatus);
				if (this.data.activityStatus == 3) {
					let that = this;
					this.playurl1 = "rtmp" + this.data.playUrl.split("webrtc")[1];
					//#ifdef H5
					this.playurl1 = "http" + this.data.playUrl.split("webrtc")[1];
					// #endif

					console.log("this.data", this.data);
					console.log("this.playurl1", this.playurl1);
					// 统一设置海报，避免iOS闪烁
					this.setPosterUrls();
          console.myLog('poster2',this.poster,this.showApplyBtn)
					if (this.showApplyBtn) return;
					this.$nextTick(() => {
						//#ifdef MP
						try {
							console.log('触发直播模块',this.playerCtx)
							this.playerCtx.play({
								success: (res) => {
									console.log("play success");
								},
								fail: (res) => {
									console.log("play fail",res);
								},
								complete(res){
									console.log('直播模块触发',res)
								}
							});
						} catch (e) {
							console.log('直播模块报错',e)
						}
						// #endif

						//#ifdef H5
						this.createdPlay();
						// #endif
					});
				} else if (this.data.activityStatus == 5) {
					// 统一设置海报，避免iOS闪烁
					this.setPosterUrls();
					// 获取回放列表
					this.meetingplaybackGetPlaybackByMeetingid();
				} else {
					console.log("kkkk", this.data);
					// console.log(this.data.videoPath)
					this.videoPathAll = isDomainUrl(this.data.videoPath);
					console.log(this.data.videoPath);
					// 统一设置海报，避免iOS闪烁
					this.setPosterUrls();
          console.myLog('poster3',this.poster)

					this.$nextTick(() => {
						if (this.showApplyBtn) return;
						this.videoContext.play({
							success: function() {
								console.log("播放成功");
							},
							error: function() {
								console.log("播放失败");
							},
						});
					});
					// console.log('this.videoPathAll',this.videoPathAll)
				}
			},
			// 获取回放列表
			// getplaybacklist(){

			// },
			meetingplaybackGetPlaybackByMeetingid() {
				this.$api.cloudClassroom
					.meetingplaybackGetPlaybackByMeetingid({
						meetingId: this.editid,
					})
					.then((res) => {
						for (let i = 0; i < res.data.length; i++) {
							res.data[i].burningTimeText =
								this.$common.getDifferenceTimeTypeFormat(
									this.$common.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"),
									this.$common.formatDate(
										new Date(
											new Date().getTime() + res.data[i].burningTime * 1000
										),
										"yyyy-MM-dd HH:mm:ss"
									)
								);
						}
						console.log("res.data", res.data);

						if (res.data.length > 0) {
							this.videoPathAll = isDomainUrl(res.data[0].videoPath);
							// 统一设置回放海报，避免iOS闪烁
							this.setPlaybackPosterUrls(res.data[0]);
              console.myLog('poster4',this.poster,res.data[0])

							this.$nextTick(() => {
								if (this.showApplyBtn) return;
								this.videoContext.play({
									success: function() {
										console.log("播放成功");
									},
									error: function() {
										console.log("播放失败");
									},
								});
							});
						}

						// console.log('this.pdList',this.pdList)

						// this.pdList = res.data;
					});
			},
			// 行为流水
			postcommoncollect2(type) {
				this.getUserInfo().then(() => {
					// console.log('this.$api.cloudClassroom.getcommoncollectlikes',this.$api.cloudClassroom.getcommoncollectlikes)
					const {
						centerUserId = ""
					} = this.curSelectUserInfo || {};
					// 分享操作
					this.$api.cloudClassroom.postcommoncollectlikes({
						businessType: 9,
						businessId: this.editid,
						// id:this.editid,
						source: 1, // 1-真实用户，2-马甲用户,3-系统用户
						accountId: this.accountId,
						userId: centerUserId,
						type: type,
					});
				});
			},
			cancel() {
				this.showbottomVisible = false;
			},
			getUserInfo() {
				return this.$ext.user.authCommunityFansInfo();
			},
			longpress() {
				console.log("长按了");

				this.getUserInfo().then(() => {
					// console.log('this.$api.cloudClassroom.getcommoncollectlikes',this.$api.cloudClassroom.getcommoncollectlikes)
					const {
						centerUserId = ""
					} = this.curSelectUserInfo || {};
					// 分享操作
					this.$api.cloudClassroom.postcommoncollectlikes({
						businessType: 9,
						businessId: this.editid,
						// id:this.editid,
						source: 1, // 1-真实用户，2-马甲用户,3-系统用户
						accountId: this.accountId,
						userId: centerUserId,
						type: "3",
					});
				});
			},
			addUser() {
				this.showbottomVisible = true;
			},
			touchmove() {},
			noTool() {
				this.$uniPlugin.toast("暂无功能");
			},
			// 获取评论
			async initCharList(
				param = {
					orderDescValue: 1,
					// orderAscValue:1,
					businessType: this.businessType || 7, // 直播
				}
			) {
				param.mainId = this.data.id;

				await this.$api.cloudClassroom
					.getMeetingChatQueryList(param)
					.then(async (res) => {
						if (this.commentList.length != res.data.length) {
							// 手机号码加密
							for (let i = 0; i < res.data.length; i++) {
								if (
									res.data[i].nickName &&
									parseInt(res.data[i].nickName) == res.data[i].nickName &&
									res.data[i].nickName.length == 11
								) {
									res.data[i].nickName =
										res.data[i].nickName.substr(0, 3) +
										"****" +
										res.data[i].nickName.substr(7);
								}
								res.data[i].nickName =
									res.data[i].nickName && res.data[i].nickName != "undefined" ?
									res.data[i].nickName :
									"无昵称";
								res.data[i].parsedComment = this.$refs.emojiRef.parseEmoji(
									res.data[i].content
								);
							}
							if (this.data.id === param.mainId) {
								this.commentList = res.data;
								// pageChatCount
								// 默认显示聊天记录
								this.commentListPage = this.commentList.slice(
									0,
									this.pageChatCount
								);
								console.log("initCharList");
								// this.$nextTick(() )
								this.getScrollViewHeight();
								// 重置滚动条
								this.resetCommon();
							}
						}
					});
			},
			resetCommon() {
				this.scrollIntoView = "top-comment";
				setTimeout(() => {
					this.scrollIntoView = "";
				}, 300);
				if (this.visible) {
					this.$emit("resetCommon");
				}
			},
			// 检测是否登陆
			checkIsLogin(){
				if (!this.curSelectUserInfo || !this.curSelectUserInfo.centerUserId) {
					// this.curSelectUserInfo.centerUserId
					// this.$emit('update',this.data.id)
					// this.meetingviewlogvisit2();
					return this.$navto.replaceAll("Login", {
						formPage: "shortVideoList",
						formPageParams: encodeURIComponent(
							JSON.stringify({
								id: this.data.id,
							})
						),
					});
				}
			},
			sendInput(val) {
        // 上锁
        if(this.timeErSend)return;
        this.timeErSend = true;
				console.log("val", val);
				console.log("this.codeUserInfo", this.curSelectUserInfo);
				this.checkIsLogin()
				// console.log('this.data', this.data);
				// fansRecord
				const fansRecord = this.$common.getKeyVal("user", "fansRecord");
				this.sendBtnLoading = true;
				console.log("sendBtnLoading", this.sendBtnLoading);
				this.$api.cloudClassroom
					.meetingchatinsert({
							content: val.comment,
							mainId: this.data.id,
							phone: this.curSelectUserInfo.phone,
							userId: this.curSelectUserInfo.centerUserId,
							businessType: this.businessType || 7, // 直播
							accountId: this.$common.getKeyVal("user", "accountId", true),
						},
						"nickName=" + fansRecord.nickName
					)
					.then(async (res) => {
						console.log(res);
            this.timeErSend = null;
						this.$uniPlugin.toast(res.msg);

						this.updateclear += 1;

						// 获取评论
						await this.initCharList();
						this.sendBtnLoading = false;

						// : 1-访问,2-点击,3-长按，4-关注，5-取关,6-点赞,7-收藏，8-删除评论，9-取消点费，10-取消收藏，1-阅读，12新增评论
						// this.postcommoncollect2(12);
						// this.postcommoncollect2(1);

						// this.$
					})
					.catch((e) => {
            this.timeErSend = null;
						this.sendBtnLoading = false;
					});
			},
			hiddenInput() {
				console.log("hiddenInput");
				if (this.data.activityStatus === 4 || this.data.activityStatus === "10") {
					this.updatecount += 1;
					this.commentShow = false;
					return;
				}
				this.updatecount += 1;
				this.commentShow = !this.commentShow;
			},
			meetingchatinsert(param = {}) {
				// content
				// :
				// "777"
				// mainId
				// :
				// "841745330109198341"
				// phone
				// :
				// "19864543594"
				// userId
				// :
				// "843229693630271495"
			},
			//
			sheep(timer) {
				if (!timer) {
					// timer = 1200000;
					timer = 2000;
				}
				return new Promise((resolve, reject) => {
					setTimeout(() => {
						resolve(true);
					}, timer);
				});
			},
			// ceinit() {
			//   const res = {
			//     msg: 'ok',
			//     data: [
			//       {
			//         content: '[得意][发呆]我是 测试001wohkkkkkkkkkkk[发呆]kkkkkkkkkkkkkkkkkkkkkkkkkkkkkjjjjjjjjjjjjjjjjjjj[发呆]jjjjjjjjjjjjjjjjjjjjj',
			//         nickName: '晴天',
			//         id: '846449454121803781'
			//       },
			//       {
			//         content: '11',
			//         nickName: '13202221139',
			//         id: '846426433154007046'
			//       },
			//       {
			//         content: '测试',
			//         nickName: '13622896620',
			//         id: '846377127285932037'
			//       }
			//     ]
			//   };

			//   for (let i = 0; i < res.data.length; i++) {
			//     res.data[i].nickName = res.data[i].nickName && res.data[i].nickName != 'undefined' ? res.data[i].nickName : '无昵称';
			//     res.data[i].parsedComment = this.$refs.emojiRef.parseEmoji(res.data[i].content);
			//   }
			//   this.commentList = res.data;
			// },
			// 获取聊天记录
			async getChatList(
				param = {
					orderDescValue: 1,
					// orderAscValue:1,
					businessType: this.businessType || 7, // 直播
				}
			) {
				// console.log(this.ishiddenFn);
				// return

				if (this.ishiddenFn) {
					this.iscommond = 0;
					return;
				}
				// this.ceinit();
				// return;
				if (!this.data || !this.data.id) {
					// console.log(this.data)
					await this.sheep(500);
					this.getChatList();
					return;
				}

				// 回放不用轮训
				if (this.data.activityStatus == 5) {
					await this.sheep();

					if (this.visible && this.iscommond == 1) {
						this.iscommond = 0;
						this.getChatList();
					} else if (!this.visible && this.iscommond == 1) {
						this.iscommond = 0;
					}

					return;
				}
				param.mainId = this.data.id;
				// this.iscommond = true;
				this.iscommond += 1;
				this.$api.cloudClassroom
					.getMeetingChatQueryList(param)
					.then(async (res) => {
						if (this.commentList.length != res.data.length || this.initUU) {
							this.initUU = false;
							// 手机号码加密
							for (let i = 0; i < res.data.length; i++) {
								if (
									res.data[i].nickName &&
									parseInt(res.data[i].nickName) == res.data[i].nickName &&
									res.data[i].nickName.length == 11
								) {
									res.data[i].nickName =
										res.data[i].nickName.substr(0, 3) +
										"****" +
										res.data[i].nickName.substr(7);
								}
								res.data[i].nickName =
									res.data[i].nickName && res.data[i].nickName != "undefined" ?
									res.data[i].nickName :
									"无昵称";
								res.data[i].parsedComment = this.$refs.emojiRef.parseEmoji(
									res.data[i].content
								);
							}
							this.commentList = res.data;
							// pageChatCount
							// 默认显示聊天记录
							this.commentListPage = this.commentList.slice(
								0,
								this.pageChatCount
							);
							this.getScrollViewHeight();
							console.log("getChatList");
							// 重置滚动条
							this.resetCommon();
						} else {
							this.countChatItem();
						}

						await this.sheep();

						if (this.visible && this.iscommond == 1) {
							this.iscommond = 0;
							this.getChatList();
						} else if (!this.visible && this.iscommond == 1) {
							this.iscommond = 0;
						}
					});
			},
			statechange(e) {
				if (!e.detail) return;
				console.log('直播状态变化',e.detail.code);
				switch (e.detail.code) {
					case 2003:
						// 网络已连接，接收首个视频数据包
						break;
					case -2301:
						// 网络断连，且经多次重连抢救无效，更多重试请自行重启播放 
						this.reconnect()
						break;
					// 其他状态码...
				}
			},
			error(e) {
				console.log("直播组件错误");
				console.error("live-player error:", e.detail.errMsg);
			},
			reconnect(){
				uni.getNetworkType({
					success: (res) => {
						// 若当前用户端有网络 则说明是主播端网络故障
						if (res.networkType !== 'none') {
							return uni.showModal({
          			content: '主播网络不佳，正在努力恢复',
								showCancel:false,
          			success:(res) => {
          	 		 	if(res.confirm) {
          	  	 	  this.playerCtx.play()
          	  	 	} else {
          	  	 	  // 取消处理
										 uni.showToast({
											 title: '取消继续直播',
											 icon: 'none'
										 })
										 uni.navigateBack()
			 					 	}
          	 		}	
        			})
						}
						uni.showModal({
          			content: '请检查你的网络链接是否正常',
								showCancel:false,
          			success:(res) => {
          	 		 	if(res.confirm) {
          	  	 	  this.playerCtx.play()
          	  	 	} else {
          	  	 	  // 取消处理
										 uni.showToast({
											 title: '取消继续直播',
											 icon: 'none'
										 })
										 uni.navigateBack()
			 					 	}
          	 		}	
        			})
					}
				})

			},
			// 统一设置海报URL的方法，避免iOS闪烁
			setPosterUrls() {
				if (this.data && this.data.coverPaths) {
					const posterUrl = isDomainUrl(this.data.coverPaths);
					// 使用nextTick确保DOM更新完成后再设置，减少iOS闪烁
					this.$nextTick(() => {
						this.poster = posterUrl;
						this.coverPathsUrl = posterUrl;
						if (this.data.detailPageLogo) {
							this.detailPageLogo = isDomainUrl(this.data.detailPageLogo);
						} else {
							this.detailPageLogo = posterUrl;
						}
					});
				}
			},
			// 设置回放海报URL的方法
			setPlaybackPosterUrls(playbackData) {
				if (playbackData) {
					const posterUrl = isDomainUrl(playbackData.coverPaths || playbackData.imageCoverPath);
					const logoUrl = isDomainUrl(playbackData.detailPageLogo);
					// 使用nextTick确保DOM更新完成后再设置，减少iOS闪烁
					this.$nextTick(() => {
						this.poster = posterUrl;
						this.coverPathsUrl = posterUrl;
						this.detailPageLogo = logoUrl;
					});
				}
			},
		},
		destroyed() {
			this.ishiddenFn = true;

			this.clearCount();
		},
	};
</script>

<style scoped lang="scss">
	$chatColor: #fff;

	.Playback {
		position: fixed;
		bottom: 176rpx;
		left: 0;
	}

	.min48 {
		min-height: 48upx;
		width: 100%;
	}

	.live-compute-bg {
		opacity: 0.3;
	}

	.live-compute-box {
		position: absolute;
		top: 0px;
		left: 0;
		right: 0;
		bottom: 0;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;

		.live-compute-top {
			display: flex;
			flex-direction: column;
			align-items: center;

			// margin-bottom: 90rpx;
			.live-compute-t {}
		}

		.live-compute-t {
			margin-top: 105upx;
			margin-bottom: 55upx;
			font-size: 38upx;
		}

		.live-compute-icon {
			width: 90upx;
			height: 90upx;
			border-radius: 50%;
			overflow: hidden;
			margin-bottom: 10px;
		}

		.live-compute-info {
			color: #878e96;
			margin-top: 20rpx;
		}

		.live-compute-li-icon {
			width: 90upx;
			height: 100%;
		}

		.live-compute-li {
			padding: 20upx;
			display: flex;
			align-items: center;
			height: 128upx;
			margin: 0 30rpx;
		}

		.live-compute-li-c {
			flex: 1;
			padding: 0px 30rpx;
			max-width: 380upx;
		}

		.live-compute-li-ct {
			font-size: 28upx;
		}

		.live-compute-li-cinfo {}

		.live-compute-li-btn {
			font-size: 25upx;
			padding: 0 32upx;
			margin-left: 0;
			margin-right: 0;
		}

		.live-compute-li-cinfo {
			/* 隐藏溢出容器的文本 */
			overflow: hidden;
			/* 防止文本换行，使其在一行内显示 */
			white-space: nowrap;
			/* 使用省略号 (...) 来表示被隐藏的文本 */
			text-overflow: ellipsis;
		}
	}

	.number-box {
		position: absolute;
		top: 50px;
		left: 80rpx;
		.playtxt{
		  margin-left: 4rpx;
		}
	}

	.horizontal-header {
		position: absolute;
		right: 20upx;
		height: 20upx;
		width: 20upx;
		z-index: 999;
		overflow: visible;
		transform: rotate(90deg);
		// top: 50%;
		display: inline-block;
	}

	.horizontalOverlay {
		position: fixed;
		top: 250rpx;
		z-index: 999;
		transform: rotate(90deg);

		.swiper-overlay {
			position: initial !important;
		}
	}

	.nofindbox {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		z-index: 99999;
		// background: #c8d0c7;
		// opacity: 0.4;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #fff;
		font-size: 38upx;
	}

	.horizontalbox {
		position: absolute;
		right: 20upx;
		bottom: 100px;
		color: #fff;
	}

	.reservationbox {
		display: flex;
		justify-content: center;
		margin-top: 48rpx;

		.d_solid {
			width: 30upx;
			height: 30upx;
			border-radius: 50%;
			overflow: hidden;
			border: 2upx solid #fc6044;
			// margin-right: 20upx;
			display: flex;
			justify-content: center;
			align-items: center;
			box-sizing: border-box;
		}

		.d_solid_in {
			width: 18upx;
			height: 18upx;
			border-radius: 50%;
			background-color: #fc6044;
			animation: dsolidIn infinite 2s;
		}

		@keyframes dsolidIn {
			0% {
				opacity: 1;
			}

			100% {
				opacity: 0.2;
			}
		}

		.reservationc {
			flex: 1;
			font-size: 20upx;
			padding: 0 20upx;
		}

		.reservationct {
			font-size: 20upx;
			white-space: nowrap;
			overflow-x: auto;
			max-width: 250upx;
		}

		.reservationb {
			width: 526rpx;
			height: 96rpx;
			background: linear-gradient(90deg, #7cec96 0%, #67dcbd 100%);
			border-radius: 48rpx;
		}

		.reserveBox {
			display: flex;
			align-items: center;
			height: 100%;

			.reserve {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				width: 100%;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 32rpx;
				color: #1d2029;
				line-height: 44rpx;
				text-align: center;
			}

			.tips {
				font-weight: 400;
				font-size: 20rpx;
				line-height: 28rpx;
				color: #1d2029;
			}

			.error {
				color: #f56c6c;
			}

			.cancel {
				color: #7cec96;
			}
		}

		.cancelBox {
			background: linear-gradient(90deg,
					rgba(124, 236, 150, 0.2) 0%,
					rgba(103, 220, 189, 0.2) 100%);
		}

		.reserveNums {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 20rpx;
			color: #1d2029;
			text-align: center;
		}
	}

	.seekmore {
		font-size: 24upx;
		color: #fff;
		line-height: 1.5;
		display: flex;
		justify-content: center;
	}

	@keyframes leftLoad {
		0% {
			transform: translateX(0px) scale(1);
			z-index: 2;
		}

		25% {
			z-index: 5;
			transform: translateX(10px) scale(1.2);
		}

		50% {
			z-index: 2;
			transform: translateX(20px) scale(1);
		}

		75% {
			z-index: 1;
			transform: translateX(10px) scale(0.8);
		}

		100% {
			transform: translateX(0px) scale(1);
			z-index: 2;
		}
	}

	@keyframes rightLoad {
		0% {
			transform: translateX(0px) scale(1);
			z-index: 2;
		}

		25% {
			z-index: 1;
			transform: translateX(-10px) scale(0.8);
		}

		50% {
			z-index: 2;
			transform: translateX(-20px) scale(1);
		}

		75% {
			z-index: 5;
			transform: translateX(-10px) scale(1.2);
		}

		100% {
			transform: translateX(0px) scale(1);
			z-index: 2;
		}
	}

	.loadingbg {
		height: 100vh;
		width: 100vw;
		display: flex;
		align-items: center;
		justify-content: center;
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: #000;
		z-index: 99;
		// display: none;

		.loadingbox :nth-child(1) {
			width: 20px;
			height: 20px;
			background: #ed872d;
			border-radius: 50%;
			box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
			animation: leftLoad infinite linear 0.7s;
		}

		.loadingbox :nth-child(2) {
			width: 20px;
			height: 20px;
			background: #eaaa65;
			border-radius: 50%;
			box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
			animation: rightLoad infinite linear 0.7s;
		}
	}

	.playtxt {
		height: 52rpx;
		line-height: 52rpx;
		border-radius: 26rpx;
		background: rgba(0, 0, 0, 0.3);
		display: flex;
		align-items: center;
		padding: 0rpx 22rpx;
		box-sizing: border-box;
		font-size: 24rpx;
		color: white;
	}

	.H {
		margin-left: 14rpx;
	}

	.liveMultiUser {
		height: 28rpx;
		width: 34rpx;
		margin-right: 4rpx;
	}
	.countbgLive{
		// background: rgb(0, 0, 0) !important;
		background: rgba(0, 0, 0, 0.65) !important;
		backdrop-filter: blur(50px) !important;
	}
	.countbg {
		padding-top: 360rpx;
		color: #fff;
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.65);
		transform: translateZ(0);

		.countmain {
			display: flex;
			flex-direction: column;
		}

		.timerShow {
			align-items: baseline !important;
			margin-top: 24rpx;
		}

		.countitem {
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.countitem.yn {
			font-size: 28rpx;
			text-align: center;
			justify-content: center;
		}

		.countTitle {
			width: 100%;
			padding: 0 112rpx;
			font-weight: 500;
			font-size: 32rpx;
			color: #ffffff;
			line-height: 44rpx;
			text-align: center;
			font-style: normal;
			box-sizing: border-box;
		}

		.countTb {
			margin-top: 48rpx;
			font-size: 28rpx;
			color: #ffffff;
			line-height: 40rpx;
			text-align: center;
			font-style: normal;
		}

		.countitemt {
			font-size: 32upx;
			justify-content: center;
			color: #c5dcdb;
		}

		.countNums {
			font-size: 64rpx;
		}

		.counttip {
			line-height: 34rpx;
			margin: 0 16rpx;
		}
	}

	.getheight {
		position: absolute;
		left: -10px;
		top: -10px;
	}

	// .flex1{
	//   flex: 1;
	//   height: auto;
	// }
	.rightNav {
		position: absolute;
		right: 20upx;
		top: 50%;
		transform: translateY(-50%);
	}
  .videoPotions{
    z-index: 99;
    width: 100vw;
    position: absolute;
    top: 50%;
    left: 0;
    right:0;
    bottom: 0;
    z-index: 2;
    transform: translateY(-50%);
  }
  .videoPBg{
    z-index: 1;
  }
  .coverP{
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;
    /* 添加过渡效果减少iOS闪烁 */
    transition: opacity 0.3s ease-in-out;
  }
	.liveBox {
		position: absolute;
		top: 398rpx;
		z-index: 9;
	}

	.videobox {
		background-color: #000;
		align-items: center;
		height: 100vh;
		position: absolute;
		top: 0;
		.cover {
			width: 100%;
			height: 100%;
			/* 添加过渡效果减少iOS闪烁 */
			transition: opacity 0.3s ease-in-out;
		}
	}

	.center-tip {
		position: absolute;
		top: 40%;
		left: 50%;
		transform: translate(-50%, -50%);
		color: #fff;
	}

	.ewmbox {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 100%;
		flex-direction: column;

		.ewmtip {
			font-size: 24upx;
			line-height: 1.5;
			display: flex;
			justify-content: center;
			align-items: center;
			margin-top: 20upx;
			// align-items: center;
		}

		.ewmico {
			width: 300upx;
			height: 300upx;
			border: 1upx solid #dbdbdb;
			// background-color: #000;
		}
	}

	.hidden {
		display: none;
	}

	.livePlayerBox {
		position: relative;
		// overflow: hidden;
		height: 100vh;
		width: 100vw;
		tansform: tanslate3d(0, 0, 0);

		.livePlayerHeader {
			// position: absolute;
			// top: 0px;
			// height: 100upx;
			display: flex;
			// height: 100%;
			align-items: center;
			width: 100%;
			padding-left: 5rpx;
			z-index: 99999999;
			.liveTitle {
				padding-right: 20rpx;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
			.scaleIcon{
				width: 72rpx;
				height: 72rpx;
				margin-left: 20rpx;
			}
		}

		.livePlayerHeader-c {
			// background-color: rgba(0, 0, 0, 0.4);
			display: flex;
			height: 70rpx;
			align-items: center;
			font-size: 28upx;
			color: #fff;
			border-radius: 50upx;
			overflow: hidden;
			background-color: rgba(0, 0, 0, 0.2);
			max-width: 370rpx;
			margin-left: 80rpx;
			margin-top: 4rpx;
			padding-right: 10rpx;
		}

		.pl20.livePlayerHeader-c {
			padding-left: 20upx;
			max-width: 100vh;
		}

		.livePlayerHeader-txt {
			margin-left: 20upx;
			max-width: 350upx;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			overflow-x: auto;
		}

		.livePlayerHeader-txt.max200 {
			max-width: 380upx;
		}

		.textelipse {
			text-overflow: ellipsis;
			white-space: nowrap;
			overflow: hidden;
			max-width: 100%;
		}

		.txt {
			font-size: 24rpx;
			// line-height: 55upx;
		}

		.txt2 {
			// line-height: 55upx;

			margin: 0 10upx;
		}

		.livePlayerHeader-btn {
			background-color: #f33b31;
			margin-left: 20upx;
			padding: 10upx 30upx;
			border-radius: 30upx;
			overflow: hidden;
			font-size: 24upx;
			white-space: nowrap;
		}

		.livePlayerHeader-ico {
			width: 64rpx;
			height: 64rpx;
			flex-shrink: 0;
			border-radius: 50%;
			overflow: hidden;
		}

		.horizontal.livePlayerChat {
			transform: rotate(90deg);
			overflow-y: scroll;
			overflow-x: auto;

			position: absolute;
			height: 400upx;
			border-radius: 10upx;
			// overflow: hidden;
			// background-color: #fff;
			// right: 100upx;
			left: 20upx;
			width: calc(100vw - 240upx);
			// bottom: 120px;
			top: 120upx;
			// overflow-y: auto;
			font-size: 28upx;
			line-height: 1.5;
			// background-color: yellow;
			padding: 20upx 0;
		}

		.swiper-overlay {
			left: 20upx;
			width: 514rpx;
			background: rgba(0, 0, 0, 0.3);
			border-radius: 26rpx;
			bottom: 185rpx;
		}

		.swiper-overlay.b180 {
			bottom: 180upx;

			.vertical-livePlayerChat {
				// height: 520upx;
			}
		}

		.vertical-livePlayerChat {
			// height: 450upx;
			width: 550rpx;
			max-height: 494rpx;
			border-radius: 10upx;
			overflow: scroll;
		}

		.vertical.livePlayerChat {
			height: 400upx;
			font-size: 28upx;
			line-height: 1.5;
			padding: 20upx 0;
			overflow-y: auto;
		}

		.livePlayerChat,
		.vertical-livePlayerChat {
			// width: 90%;
			// z-index: 9;

			// pointer-events: none;
			// background-color: rgba(0, 0, 0, 0.3);
			.top-tip {
				color: $chatColor;
				// color: #ffff;
				font-size: 24rpx;
				margin-bottom: 10upx;
				color: #87f0d4;
				padding: 8rpx 16rpx;
				border-radius: 10upx;
				overflow: hidden;
			}

			.top-tip.pd0 {
				padding: 0;
			}

			.livePlayerChat-item {
				display: flex;
				// align-items: center;
				// line-height: 30upx;
				margin-bottom: 10upx;

				// padding: 20upx;
				// border-radius: 10upx;
				// overflow: hidden;
				// padding: 0 20rpx;
				// border-radius: 40rpx;
			}

			.colorH {
				color: #dbdbdb;
			}

			.inline-item {
				display: inline-flex;
				padding: 5upx 30rpx;
				border-radius: 40rpx;
				overflow: hidden;
			}

			.livePlayerChat-item-txt {
				color: $chatColor;
				white-space: nowrap;
				// line-height: 30upx;
				font-size: 24upx;
				display: flex;
				line-height: 40upx;
			}

			.livePlayerChat-item-info {
				// margin-left: 20upx;
				color: #fff;
				display: flex;
				flex-wrap: wrap;
				align-items: center;
				line-height: 40upx;
				font-size: 24rpx;
				background: rgba(0, 0, 0, 0.3);
				// display: flex;
				// align-items: center;
				// flex-wrap: wrap;
				// lline-height: 55upx;
			}

			// margin-left: 2%;
		}

		.livePlayerBottom {
			// z-index: 999;
			// position: absolute;
			// bottom: 0;
			// height: 100upx;
			// left: 0;
			// right: 0;
			// background-color: #fff;
		}

		.livePlayerBottom {
			display: flex;
		}

	}

	/*!-----The css code below is created by----*/
	.e2_02,
	.e2_04,
	.e2_05,
	.e2_06,
	.e2_09,
	.e2_11,
	.e2_12,
	.e2_14,
	.smiley_0,
	.smiley_1,
	.smiley_10,
	.smiley_11,
	.smiley_12,
	.smiley_13,
	.smiley_14,
	.smiley_15,
	.smiley_17,
	.smiley_18,
	.smiley_19,
	.smiley_2,
	.smiley_20,
	.smiley_21,
	.smiley_22,
	.smiley_23,
	.smiley_25,
	.smiley_26,
	.smiley_27,
	.smiley_28,
	.smiley_29,
	.smiley_3,
	.smiley_30,
	.smiley_31,
	.smiley_313,
	.smiley_314,
	.smiley_315,
	.smiley_316,
	.smiley_317,
	.smiley_318,
	.smiley_319,
	.smiley_32,
	.smiley_320,
	.smiley_321,
	.smiley_322,
	.smiley_33,
	.smiley_34,
	.smiley_36,
	.smiley_37,
	.smiley_38,
	.smiley_39,
	.smiley_4,
	.smiley_40,
	.smiley_41,
	.smiley_42,
	.smiley_44,
	.smiley_45,
	.smiley_46,
	.smiley_47,
	.smiley_48,
	.smiley_49,
	.smiley_5,
	.smiley_50,
	.smiley_51,
	.smiley_52,
	.smiley_54,
	.smiley_55,
	.smiley_56,
	.smiley_57,
	.smiley_6,
	.smiley_60,
	.smiley_61,
	.smiley_62,
	.smiley_63,
	.smiley_64,
	.smiley_65,
	.smiley_66,
	.smiley_67,
	.smiley_68,
	.smiley_7,
	.smiley_70,
	.smiley_74,
	.smiley_75,
	.smiley_76,
	.smiley_78,
	.smiley_79,
	.smiley_8,
	.smiley_80,
	.smiley_81,
	.smiley_82,
	.smiley_83,
	.smiley_84,
	.smiley_85,
	.smiley_89,
	.smiley_9,
	.smiley_92,
	.smiley_93,
	.smiley_94,
	.smiley_95,
	.u1F381,
	.u1F389,
	.u1F47B,
	.u1F4AA,
	.u1F602,
	.u1F604,
	.u1F612,
	.u1F614,
	.u1F61D,
	.u1F631,
	.u1F633,
	.u1F637,
	.u1F64F {
		display: inline-block;
		background-repeat: no-repeat;
	}

	.e2_02 {
		width: 64px;
		height: 64px;
		background-position: -66px 0;
	}

	.e2_04 {
		width: 64px;
		height: 64px;
		background-position: -462px -396px;
	}

	.e2_05 {
		width: 64px;
		height: 64px;
		background-position: 0 -66px;
	}

	.e2_06 {
		width: 64px;
		height: 64px;
		background-position: -66px -66px;
	}

	.e2_09 {
		width: 64px;
		height: 64px;
		background-position: -132px 0;
	}

	.e2_11 {
		width: 64px;
		height: 64px;
		background-position: -132px -66px;
	}

	.e2_12 {
		width: 64px;
		height: 64px;
		background-position: 0 -132px;
	}

	.e2_14 {
		width: 64px;
		height: 64px;
		background-position: -66px -132px;
	}

	.smiley_0 {
		width: 64px;
		height: 64px;
		background-position: -132px -132px;
	}

	.smiley_1 {
		width: 63px;
		height: 64px;
		background-position: -660px -594px;
	}

	.smiley_10 {
		width: 64px;
		height: 64px;
		background-position: -198px -66px;
	}

	.smiley_11 {
		width: 64px;
		height: 64px;
		background-position: -198px -132px;
	}

	.smiley_12 {
		width: 64px;
		height: 64px;
		background-position: 0 -198px;
	}

	.smiley_13 {
		width: 64px;
		height: 64px;
		background-position: -66px -198px;
	}

	.smiley_14 {
		width: 64px;
		height: 64px;
		background-position: -132px -198px;
	}

	.smiley_15 {
		width: 64px;
		height: 64px;
		background-position: -198px -198px;
	}

	.smiley_17 {
		width: 64px;
		height: 64px;
		background-position: -264px 0;
	}

	.smiley_18 {
		width: 64px;
		height: 64px;
		background-position: -264px -66px;
	}

	.smiley_19 {
		width: 64px;
		height: 64px;
		background-position: -264px -132px;
	}

	.smiley_2 {
		width: 64px;
		height: 64px;
		background-position: -264px -198px;
	}

	.smiley_20 {
		width: 64px;
		height: 64px;
		background-position: 0 -264px;
	}

	.smiley_21 {
		width: 64px;
		height: 64px;
		background-position: -66px -264px;
	}

	.smiley_22 {
		width: 64px;
		height: 64px;
		background-position: -132px -264px;
	}

	.smiley_23 {
		width: 64px;
		height: 64px;
		background-position: -198px -264px;
	}

	.smiley_25 {
		width: 64px;
		height: 64px;
		background-position: -264px -264px;
	}

	.smiley_26 {
		width: 64px;
		height: 64px;
		background-position: -330px 0;
	}

	.smiley_27 {
		width: 64px;
		height: 64px;
		background-position: -330px -66px;
	}

	.smiley_28 {
		width: 64px;
		height: 64px;
		background-position: -330px -132px;
	}

	.smiley_29 {
		width: 64px;
		height: 64px;
		background-position: -330px -198px;
	}

	.smiley_3 {
		width: 64px;
		height: 64px;
		background-position: -330px -264px;
	}

	.smiley_30 {
		width: 64px;
		height: 64px;
		background-position: 0 -330px;
	}

	.smiley_31 {
		width: 64px;
		height: 64px;
		background-position: -66px -330px;
	}

	.smiley_313 {
		width: 64px;
		height: 64px;
		background-position: -132px -330px;
	}

	.smiley_314 {
		width: 64px;
		height: 64px;
		background-position: -198px -330px;
	}

	.smiley_315 {
		width: 64px;
		height: 64px;
		background-position: -264px -330px;
	}

	.smiley_316 {
		width: 64px;
		height: 64px;
		background-position: -330px -330px;
	}

	.smiley_317 {
		width: 64px;
		height: 64px;
		background-position: -396px 0;
	}

	.smiley_318 {
		width: 64px;
		height: 64px;
		background-position: -396px -66px;
	}

	.smiley_319 {
		width: 64px;
		height: 64px;
		background-position: -396px -132px;
	}

	.smiley_32 {
		width: 64px;
		height: 64px;
		background-position: -396px -198px;
	}

	.smiley_320 {
		width: 64px;
		height: 64px;
		background-position: -396px -264px;
	}

	.smiley_321 {
		width: 64px;
		height: 64px;
		background-position: -396px -330px;
	}

	.smiley_322 {
		width: 64px;
		height: 64px;
		background-position: 0 -396px;
	}

	.smiley_33 {
		width: 64px;
		height: 64px;
		background-position: -66px -396px;
	}

	.smiley_34 {
		width: 64px;
		height: 64px;
		background-position: -132px -396px;
	}

	.smiley_36 {
		width: 64px;
		height: 64px;
		background-position: -198px -396px;
	}

	.smiley_37 {
		width: 64px;
		height: 64px;
		background-position: -264px -396px;
	}

	.smiley_38 {
		width: 64px;
		height: 64px;
		background-position: -330px -396px;
	}

	.smiley_39 {
		width: 64px;
		height: 64px;
		background-position: -396px -396px;
	}

	.smiley_4 {
		width: 64px;
		height: 64px;
		background-position: -462px 0;
	}

	.smiley_40 {
		width: 64px;
		height: 64px;
		background-position: -462px -66px;
	}

	.smiley_41 {
		width: 64px;
		height: 64px;
		background-position: -462px -132px;
	}

	.smiley_42 {
		width: 64px;
		height: 64px;
		background-position: -462px -198px;
	}

	.smiley_44 {
		width: 64px;
		height: 64px;
		background-position: -462px -264px;
	}

	.smiley_45 {
		width: 64px;
		height: 64px;
		background-position: -462px -330px;
	}

	.smiley_46 {
		width: 64px;
		height: 64px;
		background-position: 0 0;
	}

	.smiley_47 {
		width: 64px;
		height: 64px;
		background-position: 0 -462px;
	}

	.smiley_48 {
		width: 64px;
		height: 64px;
		background-position: -66px -462px;
	}

	.smiley_49 {
		width: 64px;
		height: 64px;
		background-position: -132px -462px;
	}

	.smiley_5 {
		width: 64px;
		height: 64px;
		background-position: -198px -462px;
	}

	.smiley_50 {
		width: 64px;
		height: 64px;
		background-position: -264px -462px;
	}

	.smiley_51 {
		width: 64px;
		height: 64px;
		background-position: -330px -462px;
	}

	.smiley_52 {
		width: 64px;
		height: 64px;
		background-position: -396px -462px;
	}

	.smiley_54 {
		width: 64px;
		height: 64px;
		background-position: -462px -462px;
	}

	.smiley_55 {
		width: 64px;
		height: 64px;
		background-position: -528px 0;
	}

	.smiley_56 {
		width: 64px;
		height: 64px;
		background-position: -528px -66px;
	}

	.smiley_57 {
		width: 64px;
		height: 64px;
		background-position: -528px -132px;
	}

	.smiley_6 {
		width: 64px;
		height: 64px;
		background-position: -528px -198px;
	}

	.smiley_60 {
		width: 64px;
		height: 64px;
		background-position: -528px -264px;
	}

	.smiley_61 {
		width: 64px;
		height: 64px;
		background-position: -528px -330px;
	}

	.smiley_62 {
		width: 64px;
		height: 64px;
		background-position: -528px -396px;
	}

	.smiley_63 {
		width: 64px;
		height: 64px;
		background-position: -528px -462px;
	}

	.smiley_64 {
		width: 64px;
		height: 64px;
		background-position: 0 -528px;
	}

	.smiley_65 {
		width: 64px;
		height: 64px;
		background-position: -66px -528px;
	}

	.smiley_66 {
		width: 64px;
		height: 64px;
		background-position: -132px -528px;
	}

	.smiley_67 {
		width: 64px;
		height: 64px;
		background-position: -198px -528px;
	}

	.smiley_68 {
		width: 64px;
		height: 64px;
		background-position: -264px -528px;
	}

	.smiley_7 {
		width: 64px;
		height: 64px;
		background-position: -330px -528px;
	}

	.smiley_70 {
		width: 64px;
		height: 64px;
		background-position: -396px -528px;
	}

	.smiley_74 {
		width: 64px;
		height: 64px;
		background-position: -462px -528px;
	}

	.smiley_75 {
		width: 64px;
		height: 64px;
		background-position: -528px -528px;
	}

	.smiley_76 {
		width: 64px;
		height: 64px;
		background-position: -594px 0;
	}

	.smiley_78 {
		width: 64px;
		height: 64px;
		background-position: -594px -66px;
	}

	.smiley_79 {
		width: 64px;
		height: 64px;
		background-position: -594px -132px;
	}

	.smiley_8 {
		width: 64px;
		height: 64px;
		background-position: -594px -198px;
	}

	.smiley_80 {
		width: 64px;
		height: 64px;
		background-position: -594px -264px;
	}

	.smiley_81 {
		width: 64px;
		height: 64px;
		background-position: -594px -330px;
	}

	.smiley_82 {
		width: 64px;
		height: 64px;
		background-position: -594px -396px;
	}

	.smiley_83 {
		width: 64px;
		height: 64px;
		background-position: -594px -462px;
	}

	.smiley_84 {
		width: 64px;
		height: 64px;
		background-position: -594px -528px;
	}

	.smiley_85 {
		width: 64px;
		height: 64px;
		background-position: 0 -594px;
	}

	.smiley_89 {
		width: 64px;
		height: 64px;
		background-position: -66px -594px;
	}

	.smiley_9 {
		width: 64px;
		height: 64px;
		background-position: -132px -594px;
	}

	.smiley_92 {
		width: 64px;
		height: 64px;
		background-position: -198px -594px;
	}

	.smiley_93 {
		width: 64px;
		height: 64px;
		background-position: -264px -594px;
	}

	.smiley_94 {
		width: 64px;
		height: 64px;
		background-position: -330px -594px;
	}

	.smiley_95 {
		width: 64px;
		height: 64px;
		background-position: -396px -594px;
	}

	.u1F381 {
		width: 64px;
		height: 64px;
		background-position: -462px -594px;
	}

	.u1F389 {
		width: 64px;
		height: 64px;
		background-position: -528px -594px;
	}

	.u1F47B {
		width: 64px;
		height: 64px;
		background-position: -594px -594px;
	}

	.u1F4AA {
		width: 64px;
		height: 64px;
		background-position: -660px 0;
	}

	.u1F602 {
		width: 64px;
		height: 64px;
		background-position: -660px -66px;
	}

	.u1F604 {
		width: 64px;
		height: 64px;
		background-position: -660px -132px;
	}

	.u1F612 {
		width: 64px;
		height: 64px;
		background-position: -660px -198px;
	}

	.u1F614 {
		width: 64px;
		height: 64px;
		background-position: -660px -264px;
	}

	.u1F61D {
		width: 64px;
		height: 64px;
		background-position: -660px -330px;
	}

	.u1F631 {
		width: 64px;
		height: 64px;
		background-position: -660px -396px;
	}

	.u1F633 {
		width: 64px;
		height: 64px;
		background-position: -660px -462px;
	}

	.u1F637 {
		width: 64px;
		height: 64px;
		background-position: -198px 0;
	}
	.xx{}
	.u1F64F {
		width: 64px;
		height: 64px;
		background-position: -660px -528px;
	}

	.weui-emotion_list {
		padding: 0 10px;
		overflow-y: auto;
		-webkit-overflow-scrolling: touch;
		width: 100%;
		height: 100%;
		-webkit-box-sizing: border-box;
		box-sizing: border-box;
		white-space: normal;
		font-size: 0;
	}

	.weui-emotion_item {
		display: inline-block;
		position: relative;
		width: 40px;
		height: 40px;
		padding-bottom: 5px;
		line-height: 40px;
	}

	.weui-icon_emotion {
		position: absolute;
		top: 4px;
		left: 4px;
		display: inline-block;
		transform-origin: 0 0;
		transform: scale(0.5);
	}

	.weui-emotion_head {
		margin: 14px 5px 8px;
		color: #000;
		font-size: 14px;
		text-align: left;
	}

	.weui-emoji_area {
		position: relative;
		width: 100%;
	}

	.weui-emoji_area__has-safe-bottom {
		padding-bottom: 34px;
	}

	.weui-emotion_del_btn {
		display: inline-block;
		width: 24px;
		height: 24px;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
	}

	.weui-emoji__operation {
		position: absolute;
		bottom: 34px;
		right: 12px;
		width: 120px;
		height: 44px;
		display: flex;
		align-items: center;
		justify-content: flex-end;
	}

	.weui-emoji__operation__delete {
		color: #000;
	}

	.weui-emoji__operation__delete,
	.weui-emoji__operation__send {
		display: inline-block;
		width: 56px;
		height: 44px;
		line-height: 44px;
		text-align: center;
		border-radius: 4px;
		font-size: 16px;
	}

	.weui-emoji__operation__delete {
		position: relative;
		/* font-size: 0; */
		// background-color: rgba(255, 255, 255);
		background-color: #fff;
	}

	.weui-emoji__operation__send {
		margin-left: 8px;
		background-color: #04c160;
		color: rgba(255, 255, 255, 0.9);
	}
</style>
