<template>
  <view class='my-evaluating'>
    <view class="m-main-body">
      <scroll-refresh class="scroll-refresh-main" :isShowEmptySwitch="false" :fixed="false" :isAbsolute="false" :up="up" :down="down" @returnFn="returnFn" @scrollInit="scrollInit">
        <!-- #ifdef MP-ALIPAY -->
        <view class="scroll-refresh-main">
        <!-- #endif -->
          <view class="health-content" v-if="indexlist.length">
            <view class="health-item" v-for="item in indexlist" :key="item.id" @click="handleClickDetail(item)">
              <view class="item-head">
                <view class="head-l">{{ item.title }}</view>
                <view class="head-r"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-my-evaluating-right.png'"></image></view>
              </view>
              <view class="item-time">{{ item.submitTime }}</view>
            </view>
          </view>
        <!-- #ifdef MP-ALIPAY -->
        </view>
        <!-- #endif -->
        <view class="empty" slot="empty">
          <image
            class="empty-img"
            mode="aspectFill"
            :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/rumour-activity-empty-bg.png'"
          ></image>
          <text class="text-empty">暂无历史测评，赶紧去测一测吧</text>
        </view>
      </scroll-refresh>
    </view>
  </view>
</template>

<script>
  export default {
    components:{
    },
    data(){
      return{
        $constant:this.$constant,
        file_ctx: this.file_ctx,
        indexlist:[],
        $constant: this.$constant,
        mescroll: null, // mescroll实例对象
        down: {
          auto: false
        },
        up: {
          auto: false
        },
      }
    },
    onLoad(){
      this.$nextTick(() => {
        this.init()
      })
    },
    mounted(){},
    methods:{
      handleClickDetail(item){
        this.$navto.push('TestingDetail', {id:item.id,resultScore:item.resultScore / 100,myEvaluating:true})
      },
      returnFn(obj) {
        const that = this
        setTimeout(function () {
          let params = {
            current: obj.pageNum,
            size: obj.pageSize,
            condition:{
              collectionType:5,
              mySelfHealthStatus:1,
            }
          }
          that.$api.activity.researchQueryPageMySelfHealth(params).then(res => {
            if(res.data?.records.length){
              let data = res.data.records?.map(item=>({...item,submitTime:(item.submitTime && that.$common.formatDate(new Date(item.submitTime),'yyyy-MM-dd HH:mm:ss') || '')})) || []
              if (obj.pageNum === 1) {
                that.indexlist = []
              }
              that.indexlist = [...that.indexlist, ...data]
              obj.successCallback && obj.successCallback(data)
            } else {
              obj.successCallback && obj.successCallback([])
            }
          })
        }, that.$constant.noun.scrollRefreshTime)
      },
      scrollInit(scroll) {
        scroll.optUp.page.num = 1
        scroll.optUp.page.size = 10
        this.mescroll = scroll
      },
      init() {
        this.$nextTick(() => {
          this.mescroll.triggerDownScroll()
        })
      },
    },
 }
</script>

<style lang='scss' scoped>
  .m-main-body{
    position: relative;
    height: 100vh;
    background-color: #F4F6FA;
    // height: 100%;
    .scroll-refresh-main{
      height: 100%;
      .health-content{
        padding: 0 32rpx;
        width: calc(100% - 64rpx);
        height: 100%;
        .health-item{
          display: flex;
          flex-direction: column;
          padding: 20rpx 24rpx;
          background-color: #fff;
          border-radius: 16rpx 16rpx 16rpx 16rpx;
          margin-top: 24rpx;
          .item-head{
            display: flex;
            justify-content: space-between;
            align-items: center;
            .head-l{
              font-size: 32rpx;
              color: #333333;
            }
            .head-r{
              display: flex;
              width: 32rpx;
              height: 32rpx;
              image{
                width: 100%;
                height: 100%;
              }
            }
          }
          .item-time{
            margin-top: 20rpx;
            font-size: 24rpx;
            color: #777777;
          }
        }
      }
      /deep/ .mescroll-uni{
        .z-paging-content{
          background-color: transparent !important;
        }
      }
    }
  }
    /deep/.zp-scroll-view-super{
    .zp-scroll-view-container{
      .zp-scroll-view{
        .zp-paging-touch-view{
          .zp-paging-main{
            .zp-paging-container{
              flex: 0 !important;
              flex-direction: row !important;
             .zp-paging-container-content{
                width: 100%;
              }
              .empty {
                position: relative;
                display: flex;
                flex-direction: column;
                height: 100vh;
                width: 100vw;
                align-items: center;
                justify-content: center;
                background: #F7F7F7;
              }
      
              .empty-img {
                position: absolute;
                top: 248rpx;
                display: flex;
                width: 426rpx;
                height: 456rpx;
              }
      
              .text-empty {
                position: absolute;
                top: 612rpx;
                font-size: 32rpx;
                color: #777777;
              }
            }
          }
        }
      }
    }
  }
</style>