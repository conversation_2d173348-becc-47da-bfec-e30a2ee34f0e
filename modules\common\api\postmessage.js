import request from '@/common/util/request'
import env from '@/config/env'

/**
 * 社区帖子评论请求工具类，
 * 注意：API接口只做请求数据和格式化数据的事情，其他事务一概不处理
 */
export default {
    // 帖子分页列表
    postmessageQueryPage (param) {
        const url = env.ctx + 'dm/api/v1/postmessage/query/page'
        return request.postJson(url, param)
    },
    // 帖子详情
    postmessageQueryOneV1 (param) {
        const url = env.ctx + 'dm/api/v1/postmessage/query/one'
        return request.get(url, param)
    },
    // 帖子详情
    postmessageQueryOne (param) {
        const url = env.ctx + 'dm/api/v2/postmessage/query/one'
        return request.get(url, param)
    },
    // 获取评论层级-分页
    commentQueryLevelPage (param) {
        const url = env.ctx + 'dm/api/v1/comment/query/level/page'
        return request.postJson(url, param)
    },
    // 评论新增点赞数
    commentIncreaseLikenumber (param) {
        const url = env.ctx + 'dm/api/v1/comment/increase/likenumber'
        return request.postForm(url, param)
    },
    // 取消点赞
    commentReduceLikenumber (param) {
        const url = env.ctx + 'dm/api/v1/comment/reduce/likenumber'
        return request.postForm(url, param)
    },
    // 新增评论
    commentPostMessageComment (param) {
        const url = env.ctx + 'dm/api/v1/comment/post/message/comment'
        return request.postJson(url, param)
    },
    // 删除评论
    commentDeleteOne (param) {
        const url = env.ctx + 'dm/api/v1/comment/delete/one/' + param.id
        return request.delete(url, param)
    },
    // 收藏帖子
    postmessageAddCollection (param) {
        const url = env.ctx + 'dm/api/v1/postmessage/add/collection'
        return request.postForm(url, param)
    },
    // 取消收藏
    postmessageCancelCollection (param) {
        const url = env.ctx + 'dm/api/v1/postmessage/cancel/collection'
        return request.postForm(url, param)
    },
    // 点赞功能
    postmessageAddLike (param) {
        const url = env.ctx + 'dm/api/v1/postmessage/add/like'
        return request.postForm(url, param)
    },
    // 取消点赞功能
    postmessageCancelLike (param) {
        const url = env.ctx + 'dm/api/v1/postmessage/cancel/like'
        return request.postForm(url, param)
    },
    // 获取推荐帖子列表
    postmessageQueryRecommendPage (param) {
        const url = env.ctx + 'dm/api/v1/postmessage/query/recommend/page'
        return request.postJson(url, param)
    },
    // 获取医生热点帖子列表
    postmessageQueryPhysicianHotPage (param) {
        const url = env.ctx + 'dm/api/v1/postmessage/query/physician/hot/page'
        return request.postJson(url, param)
    },
    // 获取精华帖子分页
    postmessageQueryEssencePage (param) {
        const url = env.ctx + 'dm/api/v1/postmessage/query/essence/page'
        return request.postJson(url, param)
    },
    // 新增帖子
    postmessageInsert (param) {
        const url = env.ctx + 'dm/api/v1/postmessage/app/insert'
        return request.postJson(url, param)
    },
    // 删除帖子
    postmessageDeleteOne (param) {
        const url = env.ctx + 'dm/api/v1/postmessage/delete/one/' + param.id
        return request.delete(url, param)
    },
    // 浏览帖子
    postmessageVisit (param) {
        const url = env.ctx + 'dm/api/v1/postmessage/visit'
        return request.get(url, param)
    },
    // 首页查询
    postmessageSearch (param) {
        const url = env.ctx + 'dm/api/v1/postmessage/search'
        return request.postJson(url, param)
    },
    // 编辑帖子
    postmessageUpdate (param) {
        const url = env.ctx + 'dm/api/v1/postmessage/app/update'
        return request.putJson(url, param)
    },
    // 批量邀请评论
    inviteBatch(param) {
      const url = env.ctx + 'dm/api/v1/postmessage/invite/batch'
      return request.postJson(url, param)
    },
    // 帖子分页列表
    postmessageQueryAppPage (param) {
      const url = env.ctx + 'dm/api/v1/postmessage/query/app/page'
      return request.postJson(url, param)
    },
    // 首页搜索框热度词
    searchbuzzwordQueryList (param) {
      const url = env.ctx + 'dm/api/v1/searchbuzzword/query/list'
      return request.get(url, param)
    },
    // 根据名称查询话题
    postmessagetopicQueryByName (param) {
      const url = env.ctx + 'dm/api/v1/postmessagetopic/query/by/name'
      return request.get(url, param)
    },
    // 话题分页列表
    postmessagetopicQueryPage (param) {
      const url = env.ctx + 'dm/api/v1/postmessagetopic/query/page'
      return request.postJson(url, param)
    },
    // 话题-统计-分页
    postmessagetopicStatisticQueryPage (param) {
      const url = env.ctx + 'dm/api/v1/postmessagetopic/query/statistic/page'
      return request.postJson(url, param)
    },
    // 话题-单一查询
    postmessagetopicQueryOne (param) {
      const url = env.ctx + 'dm/api/v1/postmessagetopic/query/one'
      return request.get(url, param)
    },
    // 话题-保存数据
    postmessagetopicInsert (param) {
      const url = env.ctx + 'dm/api/v1/postmessagetopic/insert'
      return request.postJson(url, param)
    },
    // 话题详情-新的帖子接口
    postmessageQueryRecommendTopicPage (param) {
      const url = env.ctx + 'dm/api/v1/postmessage/query/recommend/topic/page'
      return request.postJson(url, param)
    },
    // 话题详情-我的收藏话题
    postmessagetopicQueryMyCollection (param) {
      const url = env.ctx + 'dm/api/v1/postmessagetopic/query/myCollection'
      return request.postJson(url, param)
    },
    // 话题详情-取消收藏
    applicationoperatelogCancelTopicCollection (param) {
      const url = env.ctx + 'dm/api/v1/applicationoperatelog/cancel/topic/collection'
      return request.postForm(url, param)
    },
    // 预览 收藏 分享
    applicationoperateV2logInsert (param) {
      const url = env.ctx + 'dm/api/v1/applicationoperatelogv2/insert'
      return request.postJson(url, param)
    },
    // 话题 - 根据id删除
    postmessagetopicDeleteOne (param) {
      const url = env.ctx + 'dm/api/v1/postmessagetopic/delete/one'
      return request.delete(url, param)
    },
    // 用户活动 点赞功能
    postmessageActivitytypeAddLike (param) {
      const url = env.ctx + 'dm/api/v1/postmessage/activitytype/add/like'
      return request.postForm(url, param)
    },
    // 随机评论列表
    sockpuppetcommenttemplateitemCommonRandom(data) {
      const url = env.ctx + `dm/api/v1/sockpuppetcommenttemplateitem/common/random`
      return request.get(url, data)
    },
    // 帖子完读率 新增
    contentfinishrateInsert (param) {
      const url = env.ctx + 'dm/api/v1/contentfinishrate/insert'
      return request.postJson(url, param)
    },
    // 搜索页面话题列表
    postmessagetopicSearchPage (param) {
      const url = env.ctx + 'dm/api/v1/postmessagetopic/search/page'
      return request.postJson(url, param)
    },
    // 随机评论分页
    sockpuppetcommenttemplateQueryRelationItemPage(data) {
      const url = env.ctx + `dm/api/v1/sockpuppetcommenttemplate/query/relation/item/page`
      return request.postJson(url, data)
    },
    // 获取评论-分页
    commentQueryPage (param) {
      const url = env.ctx + 'dm/api/v1/comment/query/page'
      return request.postJson(url, param)
    },
    // 帖子AI回复
    postmessageAiReply (param) {
      const url = env.ctx + 'dm/api/v1/postmessage/ai/reply'
      return request.postForm(url, param)
    },

    // 帖子AI查询评论
    postCommentQuerySearchContentPage (param) {
      const url = env.ctx + 'dm/api/v1/comment/querySearchContentPage'
      return request.postJson(url, param)
    },
}
