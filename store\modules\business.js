
const business = {
  namespaced: true,
  state: {
    myWalletDto: {},
    entryWallet: false, // 是否能直接进入钱包
    openGuideSubscribe: true, // 是否弹出引导订阅页面
    circleClassifyInfoObj: {},
    channellinkIsRecord: false, // 渠道链进入的小程序日志是否记录，兼容接口报错的情况下数据丢失
    userActivityFinishObj: {} // 用户活动完成情况
  },

  mutations: {
    UPDATE_USERACTIVITYFINISHOBJ: (state, data) => {
      state.userActivityFinishObj = data
    },
    UPDATE_CHANNELLINKISRECORD: (state, data) => {
      state.channellinkIsRecord = data
    },
    UPDATE_CIRCLECLASSIFYINFOOBJ: (state, data) => {
      state.circleClassifyInfoObj = data
    },
    UPDATE_OPENGUIDESUBSCRIBE: (state, data) => {
      state.openGuideSubscribe = data
    },
    /**
     * 案例模板
     * @param state
     * @param data
     * @constructor
     */
    UPDATE_DEMO: (state, data) => {

    },
    /**
     * 更新钱包
     * @param state
     * @param data
     * @constructor
     */
    UPDATE_MYWALLETDTO: (state, data) => {
      state.myWalletDto = data
    },
    /**
     * 更新是否能直接进入钱包
     * @param state
     * @param data
     * @constructor
     */
    UPDATE_ENTRYWALLET: (state, data) => {
      state.entryWallet = data
    },
  },

  actions: {
    UpdateUserActivityFinishObj(context, data) {
      context.commit('UPDATE_USERACTIVITYFINISHOBJ', data)
    },
    UpdateChannellinkIsRecord(context, data) {
      context.commit('UPDATE_CHANNELLINKISRECORD', data)
    },
    UpdateCircleClassifyInfoObj(context, data) {
      context.commit('UPDATE_CIRCLECLASSIFYINFOOBJ', data)
    },
    UpdateOpenGuideSubscribe(context, data) {
      context.commit('UPDATE_OPENGUIDESUBSCRIBE', data)
    },
    /**
     * 案例模板
     * @param commit
     * @param data
     * @constructor
     */
    UpdateDemo({ commit }, data) {
      commit('UPDATE_DEMO', data)
    },
    UpdateMyWalletDto(context, data) {
      context.commit('UPDATE_MYWALLETDTO', data)
    },
    UpdateEntryWallet(context, data) {
      context.commit('UPDATE_ENTRYWALLET', data)
    }
  }
}

export default business
