<template>
  <view class="" v-if="isShow">
    <!-- S 遮罩层 -->
    <view class="mask" @click="isShow = false"></view>
    <!-- E 遮罩层 -->

    <!-- S 渲染列表 -->
    <view class="dataMap">
      <view class="dataTitle">
        <view class="title-area">{{popupTitle}}</view>
        <view class="dialog-close" @click="isShow = false">
          <view class="dialog-close-plus"></view>
          <view class="dialog-close-plus dialog-close-rotate"></view>
        </view>
        <!-- /关闭按钮 -->
      </view>
      <!-- /标题 -->
      <slot name="header"></slot>
      <view class="searchBox" v-if="hiddenSearch">
        <input class="searchArea" :placeholder="placeholder" @input="searchData" type="text" />
      </view>
      <view class="contentBox">
        <view class="item" v-for="(item,index) in getDataMap" :key="index" @click="selectItem(index,item)">
          <text class="item-text">{{item.text}}</text>
          <view class="check" v-if="item.isSelect"></view>
        </view>
      </view>
      <!-- /数据列表 -->
    </view>
    <!-- E 渲染列表 -->
  </view>
</template>

<script>
  export default{
    props:{
      localdata:{
        default:[],
        type:Array
      },
      popupTitle:{
        default:'',
        type:String
      },
      placeholder:{
        default:'模糊匹配搜索',
        type:String
      },
      value:{
        default:'',
        type:String
      },
      isMore:{
        default:false,
        type:Boolean
      },
      hiddenSearch:{
        default:true,
        type:Boolean
      }
    },
    data(){
      return {
        isShow:false,
        currentValue:'',
        currentValueMap:[],
        searchValue:''
      }
    },
    computed:{
      getDataMap(){
        if(this.searchValue === '') return this.localdata;
        return this.localdata.filter(e=>(e.text + '').indexOf(this.searchValue) >= 0)
      }
    },
    methods:{
      show(){
        this.isShow = true;
        this.searchValue = '';
      },
      close(){
        this.isShow = false;
        this.searchValue = '';
      },
      selectItem(index,item){
        this.localdata[index].isSelect = !this.localdata[index].isSelect
        if(this.isMore){
          let currentIndex = this.currentValueMap.findIndex(e=>e === item.value)
          if(currentIndex >= 0 && !this.localdata[index].isSelect){
            this.currentValueMap.splice(currentIndex,1)
          }else{
            this.currentValueMap.push(item.value)
          }
          this.$emit('change',{detail:{value:[this.currentValueMap]}})
          return
        }else if(this.localdata[index].isSelect){
          this.localdata.map((e,i)=>i === index ? '' : e.isSelect = false)
        }
        this.currentValue = item.value;
        this.isShow = false;
        this.$emit('change',{detail:{value:[item]}})
        this.$emit('input',item.value)
      },
      searchData({detail:{value}}){
        console.log('detail',value);
        this.searchValue = value;
      }
    }
  }
</script>

<style lang="scss" scoped>
  .mask{
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, .4);
    display: flex;
    flex-direction: column;
    z-index: 999;
  }
  .dataMap{
    position: fixed;
    left: 0;
    top: 20%;
    right: 0;
    bottom: 0;
    background-color: #FFFFFF;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    display: flex;
    flex-direction: column;
    z-index: 1002;
    box-sizing: border-box;
    padding: 10rpx;
    .dataTitle{
      height: 80rpx;
      position: relative;
      display: flex;
      flex-direction: row;
      .title-area{
        display: flex;
        align-items: center;
        margin: auto;
        padding: 0 10px;
      }
      .dialog-close {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0 15px;
        .dialog-close-plus {
          width: 16px;
          height: 2px;
          background-color: #666;
          border-radius: 2px;
          -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
        }
        .dialog-close-rotate {
          position: absolute;
          -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
        }
      }
    }
    .searchBox{
      width: 100%;
      height: 80rpx;
      padding: 10rpx;
      box-sizing: border-box;
      .searchArea{
        width: 100%;
        height: 100%;
        border: 1px solid #b1b1a4;
        border-radius: 10rpx;
        box-sizing: border-box;
        padding: 10rpx;
      }
    }
    .contentBox{
      width: 100%;
      height: calc(100% - 160rpx - var(--header-height, 0rpx));
      overflow: scroll;
    }
    .item{
      padding: 12rpx 15rpx;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      .check {
          margin-right: 5px;
          border: 2px solid #007aff;
          border-left: 0;
          border-top: 0;
          height: 12px;
          width: 6px;
          -webkit-transform-origin: center;
          transform-origin: center;
          transition: all 0.3s;
          -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
      }
    }
  }
</style>
