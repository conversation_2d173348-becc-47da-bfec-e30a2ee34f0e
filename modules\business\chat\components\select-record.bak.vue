<template>
    <view class="fixed-box" id="recordList">
        <view class="form">
            <template v-if="!showOther">
                <div class="content-top">
                    <text class="content-top-text" v-if="nodeConfig.nextEnabled === 2" @tap="skip">跳过</text>
                </div>
                <div class="card-box">
                    <view class="card-item" :class="{
                        'card-item-active': active === index
                    }" @click="active = index" v-for="(item,index) in recordList" :key="item.id">
                        {{ `${item.name} | ${item.gender === 1 ? '男' : item.gender === 2 ? '女' : '未知'} | ${item.age}岁` }}
                    </view>
                    <view class="card-item" :class="{
                        'card-item-active': active === -1
                    }" @click="active = -1">
                        新增咨询人
                    </view>
                </div>
            </template>

            <div v-else>
                <view class="back" @click="showOther = false" v-if="!$validate.isNull(recordList)">返回</view>
                <title-input v-model="form.name" :config="config.name" :disabled="config.name.disabled"></title-input>
                <!-- <title-input v-model="form.age" :config="config.age" :disabled="config.age.disabled"></title-input> -->
                <view class="title-input clear-float" :class="{'bdt':true,'bdb':true}">
                    <view class="l-l" :style="{'color': '#333'}">
                    <text class="star">*</text>
                        咨询人年龄
                    </view>
                    <view class="l-r">
                    <input
                        v-model="form.age"
                        type="number"
                        :maxlength="3"
                        placeholder="请输入咨询人年龄"
                        @blur.prevent="hideNumberKeyborad"
                        @focus.prevent="stopKeyborad"
                    />
                    </view>
                </view>
                <title-radio v-model="form.gender" :config="config.gender" />
                <title-selector v-model="form.familyTies" :config="config.familyTies" />
            </div>

            <button class="btn" type="primary" size="mini" @tap="confirm">确认</button>
        </view>
      <view :class="['keyboard', keyboradShow ? '' : 'active', isIphoneX ? 'isIphone' : '']">
        <block v-for="(item, index) in 9" :key="index">
          <view class="keyboard-item" @tap="keyboradKey(index + 1)">{{ index + 1 }}</view>
        </block>
        <view class="keyboard-item hide"></view>
        <view class="keyboard-item" @tap="keyboradKey(0)"><text>0</text></view>
        <!--<view class="keyboard-item" @tap="keyboradKey('.')"><text>.</text></view>-->
        <view class="keyboard-item delte" @tap="keyboradDel()">
          <image class="img" :src="keyboradDelteImage" mode="aspectFill" :lazy-load="true"></image>
        </view>
      </view>
    </view>
</template>

<script>
import { mapState } from 'vuex'

import TitleInput from "@/components/business/module/v1/title-input/index"
import TitleRadio from '@/components/business/module/v1/title-radio/index.vue'
import TitleSelector from "@/components/business/module/v1/title-selector/index.vue"
import HandleConsult from '@/service/ext/modules/websocket/receive/HandleConsult'
export default {
    components: {
        TitleInput,
        TitleRadio,
        TitleSelector
    },
    data () {
        return {
            // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
            $constant: this.$constant,
            $common: this.$common,
            $accurateConversion: this.$accurateConversion,
            file_ctx: this.file_ctx,
            $static_ctx: this.$static_ctx,
            $timePlugin: this.$timePlugin,
            recordList: [],
            keyboradShow: false,
            isIphoneX: false,
            keyboradDelteImage: this.$static_ctx + 'image/business/icon-del.png',
            active: 0,
            showOther: false,
            defaultAvatar: this.file_ctx + 'static/image/system/avatar/icon-default-avatar.png',
            config: {
                name: {
                    disabled: false,
                    label: '咨询人名称',
                    required: true
                },
                age: {
                    disabled: false,
                    label: '咨询人年龄',
                    required: true,
                    type: 'number',
                    maxlength: 3
                },
                gender: {
                    disabled: false,
                    label: '咨询人性别',
                    required: true,
                    array: [{label: '男',value: 1},{label: '女',value: 2}]
                },
                familyTies: {label: '家庭关系', required: true, array: [
                    { label: '本人', value: 7 },
                    { label: '配偶', value: 1 },
                    { label: '子女', value: 2 },
                    { label: '父母', value: 3 },
                    { label: '兄弟', value: 4 },
                    { label: '姐妹', value: 5 },
                    { label: '其他', value: 6 }],
                },
            },
            form: {
                name: '',
                age: '',
                gender: '',
                familyTies: 7
            }
        }
    },
    watch: {
        showOther: {
            handler () {
                this.$nextTick(() => {
                    this.updateBottomHeight()
                })
            }
        }
    },
    computed: {
        ...mapState('chat',{
            chatItem: state => state.chatItem,
            nodereplyconfig: state => state.nodereplyconfig,
            orderDetail: state => state.orderDetail
        }),
        // 选择咨询人档案节点配置
        nodeConfig() {
            return this.nodereplyconfig.find(item => item.pushType === 5)
        }
    },
    async created () {
        await this.getRecordList()
        this.$nextTick(() => {
            this.updateBottomHeight()
        })
        this.showOther = this.$validate.isNull(this.recordList) ? true : false
    },
    methods: {
      keyboradKey(key) {
        if (this.form.age.length ===3){
          this.$uniPlugin.toast('输入年龄不合法');
          return
        }
        this.form.age = this.form.age + '' + key;
        console.log(this.form.age)
        this.checkAge();
      },
      keyboradDel() {
        if (this.form.age && this.form.age > 0) {
          if (this.form.age.length === 1){
            this.form.age = '';
          } else {
            this.form.age = this.form.age.substr(0, this.form.age.length - 1);
            let val = parseInt(this.form.age);
            if (val == 0) val = '';
            this.form.age = val;
          }
          this.checkAge();
        }
      },
      checkAge() {
        if (parseFloat(this.form.age).toString() == 'NaN') {
          this.$uniPlugin.toast('输入年龄不合法');
        }
      },
      hideNumberKeyborad(){
        // this.keyboradShow = false
      },
      stopKeyborad() {
        this.keyboradShow = true
        uni.hideKeyboard();
      },
        // 跳过
        skip () {
            // 之前已经选择过咨询人，直接跳过 重新触发资讯节点流程
            if (this.orderDetail.gfPatientId) {
                new HandleConsult(this.$ext.webSocket).processMessage()
            } else {
                // 更新订单详情
                const data =  {
                    cmd: this.$constant.chat.CONSULT_CMD,
                    data: {
                        orderId: this.chatItem.orderId,
                        userId: this.chatItem.userId,
                        gfPatientId: "",
                        nodeConfigId: this.nodeConfig.id,
                        nodeConfigReplyContent: ""
                    }
                }
                this.$ext.webSocket.webSocketSend(this.$constant.chat.CONSULT_CMD, data)
            }
        },
        // changeAge (e) {
        //     let num = e.target.value
        //     const reg = new RegExp(/[0-9]/)
        //     try {
        //         num = +num
        //     } catch (err) {
        //         console.log(err)
        //     }
        //     if (reg.test(num)) {
        //         // this.form.age = num
        //     } else {
        //         this.$nextTick(() => {
        //             this.form.age = ''
        //         })
        //         this.$uniPlugin.toast('请输入咨询人年龄数字')
        //     }
        // },
        updateBottomHeight () {
            // 获取高度
            // #ifdef H5
            let getDiv = document.getElementById('recordList')
            // #endif

            // #ifndef H5
            const query = uni.createSelectorQuery().in(this)
            let getDiv = query.select('#recordList')
            // #endif
            this.getEl(getDiv).then(data => {
                this.$common.setKeyVal('chat', 'bottomBoxHeight', data.height + 20)
                // 更改底部高度之后再滚动
                this.$nextTick(() => {
                    uni.pageScrollTo({
                        scrollTop: 99999,
                        duration: 0, //小程序如果有滚动效果 input的焦点也会随着页面滚动...
                    })
                })
            })
        },
        confirm () {
            if (!this.showOther) {
                this.confirmCard()
            } else {
                this.confirmForm()
            }
        },
        // 确认咨询人卡片
        confirmCard () {
            let that = this
            // 选中其他人
            if (this.active === - 1) {
                this.showOther = true
            } else {
                // 更新订单详情
                const data =  {
                    cmd: this.$constant.chat.CONSULT_CMD,
                    data: {
                        orderId: this.chatItem.orderId,
                        userId: this.chatItem.userId,
                        gfPatientId: this.recordList[this.active].id,
                        nodeConfigId: this.nodeConfig.id,
                        nodeConfigReplyContent: this.recordList[this.active].id
                    }
                }
                this.$ext.webSocket.webSocketSend(this.$constant.chat.CONSULT_CMD, data)

                // 发送咨询问题引导语消息
                const item = this.recordList[this.active]
                const content = `${item.name} | ${item.gender === 1 ? '男' : '女'} | ${item.age}岁`
                const time = new Date().getTime()
                const dto = {
                    cmd: this.$constant.chat.SINGLE_CHAT_CMD,
                    data: {
                        msgType: 1,
                        msgContent: '',
                        content: content,
                        orderId: this.chatItem.orderId,
                        seatUserId: "",
                        touchType: this.$constant.chat.touchType.selectRecordGuide,
                        createTime: time
                    }
                }
                this.$ext.webSocket.webSocketSend(this.$constant.chat.SINGLE_CHAT_CMD, dto)

                // 咨询消息回显
                let listItem = {
                    hasBeenSentId: time + '-' + this.chatItem.userId, //已发送过去消息的id
                    content: content,
                    fromUserHeadImg: that.defaultAvatar, //用户头像
                    fromUserId: that.chatItem.userId,
                    isItMe: true,
                    createTime: time,
                    msgType: 1, // 1文字文本 2图片
                    msgCloudStatus: 2, //消息发送结果状态，1是正常，2是发送中，待回调，3是等待回调失败
                }

                let messageList = this.$common.getKeyVal('chat', 'messageList', false)
                this.$common.setKeyVal('chat', 'messageList', [...messageList, listItem])

                this.$nextTick(() => {
                    uni.pageScrollTo({
                        scrollTop: 99999,
                        duration: 0, //小程序如果有滚动效果 input的焦点也会随着页面滚动...
                    });
                });
            }
        },
        // 新增咨询人
        confirmForm () {
            let that = this
            // 表单校验
            for(const key in this.form) {
                if(!this.$validate.isNull(this.config[key])) {
                    if(this.config[key].required && !this.form[key]) {
                        this.$uniPlugin.toast(`${this.config[key].label}不得为空！`)
                        return
                    }
                }
            }
            const { name, gender, age, familyTies } = this.form

            this.$api.chat.patientinfoSimpleSave({
                userId: this.chatItem.userId || this.$common.getKeyVal('user', 'codeUserInfo', true).id,
                name,
                gender,
                age,
                familyTies
            }).then(res => {
                if (!that.$common.getKeyVal('user', 'curSelectStore', true).userTenantRecordList[0].id) {
                    that.$ext.user.getInfoGroup(() => {
                        that.$ext.user.bindWeixinAccount({})
                    })
                }

                // 更新订单详情
                const data =  {
                    cmd: this.$constant.chat.CONSULT_CMD,
                    data: {
                        orderId: this.chatItem.orderId,
                        userId: this.chatItem.userId,
                        gfPatientId: res.data.id,
                        nodeConfigId: this.nodeConfig.id,
                        nodeConfigReplyContent: res.data.id
                    }
                }
                this.$ext.webSocket.webSocketSend(this.$constant.chat.CONSULT_CMD, data)

                // 发送咨询问题引导语消息
                const content = `${name} | ${gender === 1 ? '男' : '女'} | ${age}岁`
                const time = new Date().getTime()
                const dto = {
                    cmd: this.$constant.chat.SINGLE_CHAT_CMD,
                    data: {
                        msgType: 1,
                        msgContent: '',
                        content: content,
                        orderId: this.chatItem.orderId,
                        seatUserId: "",
                        touchType: this.$constant.chat.touchType.selectRecordGuide,
                        createTime: time
                    }
                }
                this.$ext.webSocket.webSocketSend(this.$constant.chat.SINGLE_CHAT_CMD, dto)

                // 咨询消息回显
                let listItem = {
                    hasBeenSentId: time + '-' + this.chatItem.userId, //已发送过去消息的id
                    content: content,
                    fromUserHeadImg: that.defaultAvatar, //用户头像
                    fromUserId: that.chatItem.userId,
                    isItMe: true,
                    createTime: time,
                    msgType: 1, // 1文字文本 2图片
                    msgCloudStatus: 2, //消息发送结果状态，1是正常，2是发送中，待回调，3是等待回调失败
                }

                let messageList = this.$common.getKeyVal('chat', 'messageList', false)
                this.$common.setKeyVal('chat', 'messageList', [...messageList, listItem])

                this.$nextTick(() => {
                    uni.pageScrollTo({
                        scrollTop: 99999,
                        duration: 0, //小程序如果有滚动效果 input的焦点也会随着页面滚动...
                    });
                });
            })
        },
        // 获取档案列表
        async getRecordList () {
            const res = await this.$api.chat.patientinfoFindSimpleList({
                userId: this.chatItem.userId
            })
            this.recordList = res.data
        },
        // 异步获取元素
        getEl (getDiv) {
            return new Promise((resolve, reject) => {
                // #ifdef H5
                resolve(getDiv.getBoundingClientRect())
                // #endif

                // #ifndef H5
                if (getDiv.boundingClientRect) {
                    getDiv.boundingClientRect(data => {
                        console.log(data)
                        resolve(data)
                    }).exec()
                }
                // #endif
            })
        },
    }
}
</script>

<style lang="scss" scoped>

.keyboard {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #ebebeb;
  display: flex;
  justify-content: center;
  z-index: 2;
  flex-wrap: wrap;
  transition: all 0.2s ease-in 0.2s;
}
.active {
  bottom: -400rpx;
}
.keyboard-item {
  box-sizing: border-box;
  width: 250rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: #fff;
  font-size: 40rpx;
  color: #333;
  height: 99rpx;
  border: 1rpx solid #ebebeb;
  border-top: none;
  border-left: none;
}
.hide {
  opacity: 0;
}
.delte {
  background: none;
  box-shadow: none;
}
.delte image {
  width: 60rpx;
  height: 60rpx;
}
.isIphone {
  padding-bottom: 68rpx !important;
}

.fixed-box {
    position: fixed;
    z-index: 999;
    bottom: 0;
    width: 100%;
	font-size: 28upx;
    background: #fff;

    .form {
        display: flex;
        flex-direction: column;
        padding: 24upx;
        padding-bottom: calc(16upx + env(safe-area-inset-bottom) );

        .card-box {
            max-height: 350upx;
            overflow-y: auto;
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: space-between;
        }

        .card-item {
            width: 48%;
            text-align: center;
            padding: 24upx 0;
            background-color: #fff;
            color: $topicC;
            border: 1px solid $topicC;
            @include rounded(12upx);
            margin-bottom: 24upx;

            &-active {
                background-color: $topicC;
                color: #fff;
            }
        }

        .btn {
            display: block;
            width: 150upx;
            margin: 0 auto;
            background-color: #4cd964;
            border: none;
            height: 74upx;
            line-height: 74upx;
            margin-top: 24upx;
        }

        .back {
            color: $topicC;
            font-size: 30upx;
        }
    }
}
.color-topicC{
    color: $topicC !important;
  }
  .title-input{
    background-color:#ffffff;
    .l-l{
      line-height: 88upx;
      color: #333333;
      // font-weight: 600;
      font-size:30upx;
    }
    .l-r{
      margin-bottom: 5px;
      input {
        height: 80upx;
        line-height: 80upx;
        color: #333;
        font-size: 32upx;
        text-align: left;
        display: inline-block;
        vertical-align: middle;
        width: calc(100% - 64upx);
        border: 2upx solid #ede9e9;
        border-radius: 10upx;
      }
    }
  }
  .star{
    color: #F85E4C;
    padding-top: 6upx;
    font-size: 32upx;
    display: inline-block;
  }
.content-top {
    display: flex;
    justify-content: space-between;
    padding: 0 20upx 24upx;
    &-text {
        color: $topicC;
        font-size: 32upx;
        line-height: 42upx;
    }
}
</style>
