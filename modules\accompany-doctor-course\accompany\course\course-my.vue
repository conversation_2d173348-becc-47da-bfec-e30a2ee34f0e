<template>
  <view class='my'>
    <!-- <view class="my-data" :style="{'background-image':'url('+ file_ctx +'static/image/business/accompany-doctor/icon-accompany-my-bg.png)','background-size': '100%'}"> -->
    <view class="my-data">
      <view :style="'height:' + statusBarHeight + 'px;'"></view>
      <view class="top-nav">
        <view class="top-nav-l" @click.stop="handleBack"><image mode="aspectFit" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-left-arrows.png'" class="header-search-img"/></view>
        <view class="top-nav-c"></view>
      </view>
      <view class="my-head">
        <view class="profile">
          <default-img
            style="width: 112rpx;height: 112rpx;display: inline-block;"
            :config="config.avatar"
            :cData="infoObj.avatarUrl"
            :cName="infoObj?infoObj.name:''"
            class="role-image"
          />
        </view>
        <view class="name">{{ infoObj.name || '暂无名称'}}</view>
      </view>
    </view>
    <view class="overview-content">
      <view class="overview-item">
        <view class="title">本月订单</view>
        <view class="num">{{ orderStat.count }}</view>
        <view class="percentage" :style="{color:orderStat.monthChain.includes('+') ? '#008763' : '#FF5500'}">{{ orderStat.monthChain }}<uni-icons class="my-icons" :size="10" color="#A5AAB8" type="right" /></view>
        <!-- <view class="percentage" >+23.23%<uni-icons class="my-icons" :size="10" color="#A5AAB8" type="right" /></view> -->
      </view>
      <view class="overview-item">
        <!-- <view class="title">{{ postReadCountStatisticObj.name }}</view> -->
        <view class="title">本月评价</view>
        <view class="num"><image :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-month-star.png'"></image>{{ employeeStat.count }}</view>
        <view class="percentage" :style="{color:employeeStat.monthChain.includes('+') ? '#008763' : '#FF5500'}">{{ employeeStat.monthChain }}<uni-icons class="my-icons" :size="10" color="#A5AAB8" type="right" /></view>
      </view>
      <view class="overview-item">
        <!-- <view class="title">{{ incomeStatisticObj.name }}</view> -->
        <view class="title">本月学习时长</view>
        <view class="num" v-if="studyStat.count">
          <span v-if="studyStat.count.hours">{{ studyStat.count.hours }}时</span>
          <span v-if="studyStat.count.minutes !== 0">{{ studyStat.count.minutes }}分</span>
          <span v-else>0</span>
        </view>
        <!-- <view class="percentage" >+23.23%<uni-icons class="my-icons" :size="10" color="#A5AAB8" type="right" /></view> -->
        <view class="percentage" :style="{color:studyStat.monthChain.includes('+') ? '#008763' : '#FF5500'}">{{ studyStat.monthChain }}<uni-icons class="my-icons" :size="10" color="#A5AAB8" type="right" /></view>
      </view>
    </view>
    <!-- <view class="my-card">
      <view class="my-card-main">
        <view class="li l76" @tap="$navto.push('AccompanyPersonalMyPosts', {orderIndex: 1})">
          <view class="number">{{ dataStatistics.postMessageCount || 0 }}</view>
          <view class="text">
            我的帖子
          </view>
        </view>
        <view class="li l74" @tap="$navto.push('AccompanyPersonalMyComment', {orderIndex: 2})">
          <view class="number">{{ dataStatistics.commitCount || 0 }}</view>
          <view class="text">
            我的评论
          </view>
        </view>
        <view class="li l76" @tap="$navto.push('AccompanyPersonalMyCollect', {orderIndex: 3})">
          <view class="number">{{ dataStatistics.collectionCount || 0 }}</view>
          <view class="text">
            我的收藏
          </view>
        </view>
        <view class="li" @tap="$navto.push('AccompanyPersonalMyLike', {orderIndex: 4})">
          <view class="number">{{ dataStatistics.likeCount || 0 }}</view>
          <view class="text">
            我的点赞
          </view>
        </view>
      </view>
    </view> -->
    <view class="my-content">
      <template v-for="(item,index) in tabs">
        <view :key="item.name" v-if="item.showFlag" class="content-item" @click="handleClickJump(item)">
          <view class="item-l"><image class="img" :src="file_ctx + item.url"></image></view>
          <view class="item-r">
            <view class="text">{{ item.name }}</view>
            <image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-right-arrows.png'"></image>
          </view>
        </view>
      </template>
    </view>
  </view>
</template>

<script>
  import { mapState } from 'vuex'
  import defaultImg from '@/components/basics/default-avatar/index'
  import common from '@/common/util/main'
  export default {
    components: {
      defaultImg
    },
    data(){
      return{
        file_ctx:this.file_ctx,
        $constant: this.$constant,
        $static_ctx: this.$static_ctx,
        statusBarHeight: 0,
        tabs:[
          // {showFlag:false,showType:['all'],name:'我的回复',url:'static/image/business/accompany-doctor/icon-accompany-my-message.png',routerName:'AccompanyNewsReply'},
          // {showFlag:false,showType:['all'],name:'点赞与收藏',url:'static/image/business/accompany-doctor/icon-accompany-my-like.png',routerName:'AccompanyNewsLikeCollect'},
          {showFlag:true,showType:['all'],name:'最近学习',url:'static/image/business/accompany-doctor/icon-accompany-home-live-img.png',routerName:'RecentlyStudy'},
          {showFlag:true,showType:['all'],name:'学习报表',url:'static/image/business/accompany-doctor/icon-accompany-home-study-img.png',routerName:'StudyStatement'},
        ],

        config: {
          avatar: {
            widthHeightAuto: true,
            itemClass: {
              width: '112rpx',
              height: '112rpx',
              display: 'inline-block',
            }
          }
        },
        dataStatistics:{},
        // 是否是陪诊师
        hasEmployee:false,
        // 是否是服务商
        hasProvider:false,
        // 是否是个人消费者
        hasIndividual:false,
        studyStat:null,
        employeeStat:null,
        orderStat:null,
        userInfo:null,
      }
    },
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId,
        fansRecord: state => state.fansRecord,
        isLogin: state => state.isLogin
      }),
      infoObj() {
        return {
          name: this.fansRecord.nickName,
          avatarUrl: this.fansRecord.headPath ? this.fansRecord.headPath : this.defaultAvatar,
          lightLogoPath: this.fansRecord.lightLogoPath
        }
      }
    },
    onLoad(){},
    async mounted(){
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
      const codeUserInfo = this.$common.getKeyVal('user', 'codeUserInfo',true);
      //         陪诊师      服务商
      let {data:{hasEmployee,hasProvider}} = await this.$api.accompanyDoctor.accompanybookUserRole({userId:codeUserInfo.id})
      let res = await this.$api.accompanyDoctor.getAccompanyemployeeOneByUserId({userId:codeUserInfo.id})
      this.userInfo = res.data;
      this.accompanybookEmployeeStudyStatistic(this.userInfo.id)
      this.accompanybookEmployeeStarStatistic(this.userInfo.id)
      this.accompanybookEmployeeOrderStatistic(this.userInfo.id)

      this.getCommoncollectlikesGetMyStatistics()
      this.$common.setKeyVal('user','isUserRole',{hasEmployee,hasProvider},true)
      this.hasEmployee = hasEmployee;
      this.hasProvider = hasProvider;
      if(!hasEmployee && !hasProvider)this.hasIndividual = true;
      this.tabs.map((item,index)=>{
        let showFlag = item.showType.filter(e=>{
          if(e === 'all') return true
          if(e === 'hasEmployee') return this.hasEmployee
          if(e === 'hasProvider') return this.hasProvider
          if(e === 'hasIndividual') return this.hasIndividual
        }).length >= 1;
        this.setTabsItem(index,'showFlag',showFlag)
      })
    },
    methods:{
      convertSecondsToTimeSimplified(seconds) {
        const hours = Math.floor(seconds / 3600); // 计算小时数
        const minutesInHour = seconds % 3600;     // 计算一小时内的秒数
        const minutes = minutesInHour >= 60 ? Math.floor(minutesInHour / 60) : 0; // 如果秒数大于等于60，则计算分钟数，否则为0
        // 格式化输出
        return {hours,minutes}
      },
      // 本月学习时长
      async accompanybookEmployeeStudyStatistic(employeeId){
        const res = await this.$api.accompanyDoctor.accompanybookEmployeeStudyStatistic({employeeId})
        if(res.data !==""){
          this.studyStat = {...res.data,count:this.convertSecondsToTimeSimplified(res.data.count)}
        }
      },
      // 陪诊师星级统计
      async accompanybookEmployeeStarStatistic(employeeId){
        const res = await this.$api.accompanyDoctor.accompanybookEmployeeStarStatistic({employeeId})
        if(res.data !==""){
          this.employeeStat = res.data
        }
      },
      // 本月订单统计
      async accompanybookEmployeeOrderStatistic(employeeId){
        const res = await this.$api.accompanyDoctor.accompanybookEmployeeOrderStatistic({employeeId})
        if(res.data !==""){
          this.orderStat = res.data
        }        
      },
      async getCommoncollectlikesGetMyStatistics () {
        const param = {
          accountId: this.accountId
        }
        const res = await this.$api.community.commoncollectlikesGetMyStatistics(param)
        let data = res.data;
        if(data.postMessageCount && data.postMessageCount > 9999) {
          data.postMessageCount = '9999+'
        }
        if(data.commitCount && data.commitCount > 9999) {
          data.commitCount = '9999+'
        }
        if(data.collectionCount && data.collectionCount > 9999) {
          data.collectionCount = '9999+'
        }
        if(data.likeCount && data.likeCount > 9999) {
          data.likeCount = '9999+'
        }
        this.dataStatistics = data
      },
      setTabsItem(index,key,value){
        this.$set(this.tabs[index],key,value)
      },
      handleClickJump(item){
        if(item.routerName === 'loginOut'){
          const that = this
          uni.showModal({
            content: '是否退出登录？',
            confirmText: '确定',
            cancelText: '取消',
            success: function(data) {
              if (data.confirm) {
                that.$ext.user.loginOut()
              } else if (data.cancel) {

              }
            }
          })
          return
        }
        item.routerName && this.$navto.push(item.routerName)
      },
      handleBack(){
        this.$navto.back(1)
      },
    },
 }
</script>

<style lang='scss' scoped>
.my{
  position: relative;
  background: #F4F6FA;
  height: 100vh;
  // padding-bottom: 186rpx;
  overflow-y: scroll;
  overflow-x: hidden;
}
.my-data{
  height: 516rpx;
  width: 100%;
  background: #00B484;
  .top-nav{
    // position: fixed;
    width: calc(100% - 16rpx);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    line-height: 40px;
    padding: 0 16rpx;
    // z-index: 999;
    // padding: 0 32rpx 0 24rpx;
    .top-nav-l{
      display: flex;
      width: 48rpx;
      height: 48rpx;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .top-nav-c{
      flex: 1;
      text-align: center;
      height: 44rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #2D2F38;
      line-height: 44rpx;
      margin-right: 48rpx;
    }
  }
  .my-head{
    display: flex;
    align-items: center;
    padding: 24rpx 32rpx 0;
    .profile{
      width: 112rpx;
      height: 112rpx;
      border: 1rpx solid #EAEBF0;
      border-radius: 50%;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .name{
      font-size: 36rpx;
      color: #1F2021;
      margin-left: 24rpx;
    }
  }
}
  .overview-content{
    display: flex;
    flex-wrap: wrap;
    position: absolute;
    top: 364rpx;
    width: calc(100% - 64rpx);
    margin: 0 32rpx;
    padding: 32rpx 0;
    border-radius: 16rpx;
    background-color: #fff;
    .overview-item{
      display: flex;
      // flex: 33.33333%;
      flex: 1;
      align-items: center;
      flex-direction: column;
      .title{
        font-size: 24rpx;
        color: #1D2029;
        line-height: 34rpx;
      }
      .num{
        display: flex;
        align-items: center;
        font-size: 32rpx;
        font-weight: 600;
        color: #1D2029;
        line-height: 44rpx;
        image{
          display: flex;
          width: 26rpx;
          height: 24rpx;
          margin-right: 10rpx;
        }
      }
      .percentage{
        font-size: 24rpx;
        color: #008763;
        line-height: 34rpx;
      }
    }
  }
.my-card{
  position: absolute;
  top: 560rpx;
  width: calc(100% - 64rpx);
  // height: 94upx;
  margin: 0 32rpx;
  padding: 32rpx 0;
  border-radius: 16rpx;
  background-color: #fff;
  .my-card-main{
    display: flex;
    justify-content: space-evenly;
    padding: 0 24rpx;
    .li{
      // width: 100%;
      position: relative;
      width: 96upx;
      .text{
        width: 96upx;
        height: 34rpx;
        font-weight: 400;
        font-size: 24rpx;
        color: #4E5569;
        text-align: center;
      }
      .number{
        font-size: 36rpx;
        color: #1D2029;
        line-height: 50rpx;
        text-align: center;
        margin-bottom: 8upx;
      }
    }
    .li:last-of-type{
      padding-left: 2upx;
      position: relative;
      .border-left{
        width: 2upx;
        height: 70%;
        position: absolute;
        left: 0;
        top: 50%;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
        background: #f5f5f5;
        @include downBoxShadow(-4upx, 0, 20upx, 1, 204, 204, 204);
      }
    }
    .l76{
      margin-right: 76upx;
    }
    .l74{
      margin-right: 74upx;
    }
  }
}
.my-content{
  position: absolute;
  // top: 738rpx;
  top: 558rpx;
  width: calc(100% - 64rpx);
  background: #FFFFFF;
  border-radius: 16rpx;
  margin: 0 32rpx;
  .content-item{
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 36rpx 24rpx 0 32rpx;
    .item-l{
      display: flex;
      padding-bottom: 36rpx;
      .img{
        width: 40rpx;
        height: 40rpx;
      }
    }
    .item-r{
      display: flex;
      flex: 1;
      justify-content: space-between;
      border-bottom: 2rpx solid #F4F6FA;
      margin-left: 20rpx;
      padding-bottom: 36rpx;
      .text{
        font-size: 30rpx;
        color: #1D2029;
      }
      .img{
        width: 32rpx;
        height: 32rpx;
      }
    }
  }
}

</style>
