<template>
  <view>
    <image
      @longtap="longtap"
      :show-menu-by-longpress='true'
      class='txLandingPage'
      :src="txLanding"
      >
    </image>
  </view>
</template>

<script>
  import { mapState } from 'vuex'
  import common from '@/common/util/main'
  export default {
    data(){
      return {
      txLanding: this.$static_ctx + "image/business/hulu-v2/txLanding.png",
      pageOptions:[
        {
          pageImg:this.$static_ctx + "image/business/hulu-v2/txLanding.png",
        },
        {
          pageImg:this.$static_ctx + "image/business/hulu-v2/txLanding-2.png",
        }
      ],
      }
    },
    computed: {
      ...mapState('user', {
        codeUserInfo: state => state.codeUserInfo,
        isLogin: state => state.isLogin,
        accountId: state => state.accountId
      }),
      ...mapState('system', {
        isShowSubscribePanel: state => state.isShowSubscribePanel
      })
    },
    async onLoad(res){
      let currentOptions = this.pageOptions[+res.currentIndex];
      console.log('currentOptions',currentOptions);
      this.txLanding = currentOptions.pageImg;
      // 开启曝光量记录
      this.$api.drugBook.pageexposurerecordInsert({
        ...(await this.getOptions()),
      })
    },
    methods:{
      async getOptions(){
        let openId = await this.$ext.wechat.getOpenId();
        let accountId = this.accountId;
        return {openId,accountId,imageUrl:this.txLanding}
      },
      longtap(){
        let userId = this.codeUserInfo.id;
        let accountId = common.getKeyVal('user', 'accountId', true);
        let openId = this.$common.getCache('openId');
        this.$api.activity.insertiIageoperationrecord({
          userId,accountId,openId,imageUrl:this.txLanding,operatingType:2
        })
      },
    }
  }
</script>

<style lang="scss">
  .txLandingPage{
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
  }
  .btnBom{
    border-radius: 59rpx;
    width: 615rpx;
    height: 98rpx;
    line-height: 98rpx;
    background-color: #EA7800;
    border: 1rpx solid white;
    position: fixed;
    left: 50%;
    transform: translateX(-50%);
    bottom: 87rpx;
    text-align: center;
    color: white;
    font-size: 33.33rpx;
    font-weight: bold;
  }
</style>
