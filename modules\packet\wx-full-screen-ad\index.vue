<template>
  <page>
    <view slot="content" class="page-content">
      <!-- #ifdef MP-WEIXIN -->
      <view class="adContainer">
        <ad-custom class="full" unit-id="adunit-9b3e642b4b4b3661" @load="adLoad" @error="adError" @close="adClose" ad-intervals="30"></ad-custom>
      </view>
      <!-- #endif -->
    </view>
  </page>
</template>

<script>
export default {
  data() {
    return {
      adCustomError: {}
    }
  },
  methods: {
    adLoad(e) {
      console.log('广告位加载成功------', e)
    },
    adError(e) {
      this.adCustomError = true
      console.log('广告位加载异常------', e)
    },
    adClose(e) {
      this.adCustomError = true
      console.log('关闭广告位------', e)
    },
  }
}
</script>

<style lang="scss" scoped>
.page-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}
.adContainer {
  width: 100%;
  height: 100%;
}
.full {
  width: 100%;
  height: 100%;
}
</style>