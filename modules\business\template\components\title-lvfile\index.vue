<template>
  <view class="title-input clear-float" :class="{'bdt':defaultConfig.bdt,'bdb':defaultConfig.bdb && !child}">
    <view class="l-l" :class='{"bold":bold}' :style="{'color': defaultConfig.titleColor}">
      <!--  -->
      {{defaultConfig.label}}
      <text class="star" v-if="defaultConfig.required">*</text>
    </view>
    <view class="l-r">
    <title-file :disabled="fileDisabled" :cData="attachmentList" :config="{multiSelectCount: defaultConfig.multiSelectCount}" @returnFn="imgReturnFn"></title-file>
  </view>
  </view>
</template>

<script>
  import TitleFile from "@/modules/business/template/components/title-file/index";
  export default {
    name:"titleLvfile",
    components:{
      TitleFile
    },
    data(){
        return {
          fileDisabled:false,
          regForm:{
            attachmentList:[]
          },
          defaultConfig: {
            bdt: false,
            bdb: true,
            titleColor: '#333',
            textColor: '#333',
            label: '单行输入框',
            name: 'input',
            required: false,
            multiSelectCount:1,
          }
        }
    },
    watch:{
      config: {
        handler(val) {
          this.copyConfig()
        },
        deep: true
      },
      attachmentList: {
        handler(val) {
          // debugger
          if (!val || val == ""){
            val = []
          }
          this.regForm.attachmentList = val
        },
        deep: true
      },
    },
    methods:{
      /**
         * 初始化拷贝config对象
         */
      copyConfig() {
        const that = this
        const obj = that.config

        Object.keys(obj).forEach(function(key) {
          that.defaultConfig[key] = obj[key]
        })
        // console.log("that.defaultConfig")
        // console.log(that.defaultConfig)
      },
      /**
       * 上传图片
       * @param v
       */
      imgReturnFn(v) {
        // console.log(v)
        // console.log('v----')
        this.$emit('returnFn',{
          arr:v,
          name:this.config.name
        })
        // this.regForm.attachmentList = v
      },
    },
    props:{
      bold:{
        type:Boolean,
        default:true,
      },
      child:{
        type:Boolean,
        default:false,
      },
      // 参数设置
      config: {
        type: Object,
        required: false,
        default: () => {
          return {}
        }
      },
      // 参数设置
      attachmentList: {
        type: [Array],
        required: true,
        default: () => {
          return []
        }
      },
    },
    mounted(){
      this.copyConfig()

    }
  }
</script>

<style lang="scss" scoped>
  .star{
    color: #F85E4C;
    padding-top: 6upx;
    font-size: 32upx;
    display: inline-block;
        margin-left: 10rpx;
  }
  .title-input{
    background-color:#ffffff;
    .l-l{
      line-height: 88upx;
      color: #333333;
     
      font-size:30upx;
    }
    .l-l.bold{
       font-weight: 600;
    }
    .l-r{
      margin-bottom: 5px;
      display: flex;
      align-items: center;
      input {
        height: 80upx;
        line-height: 80upx;
        color: #333;
        font-size: 32upx;
        text-align: left;
        display: inline-block;
        vertical-align: middle;
        width: calc(100% - 64upx);
        border: 2upx solid #ede9e9;
        border-radius: 10upx;
      }
      .util{
        width: 64upx;
        font-size: 28upx;
            overflow: hidden;
            text-align: center;

      }
    }
  }

</style>
