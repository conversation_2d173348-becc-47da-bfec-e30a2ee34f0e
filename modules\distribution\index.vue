<template>
  <page>
    <view slot="content" class="page-content">
      <m-nav-bar title="分销中心" />
      <view class="content-main">
        <!-- 客户关联模式选择 -->
        <view class="module-item relation-mode">
          <view class="marquee-container">
            <view class="marquee-content">
              <view class="marquee-text">
                客户关联模式：当前为{{distributorModeText}}
              </view>
              <view class="marquee-text">
                客户关联模式：当前为{{distributorModeText}}
              </view>
            </view>
          </view>
          <view class="switch-mode-btn">
            <image src="/static/image/distribution/switch-indicator.png" mode="aspectFit" class="indicator-icon"></image>
          </view>
        </view>

        <!-- 分销员信息 -->
        <view class="module-item distributor-info">
          <view class="distributor-header">
            <view class="distributor-name">{{distributorName || '分销员名称'}}</view>
            <view class="total-amount">
              <text>我的余额：</text>
              <text class="amount">¥{{availableBalance}}</text>
              <view class="cash-out-btn" @tap="navtoGo('DistributionWithdrawal')">前往提现</view>
            </view>
          </view>
        </view>

        <!-- 数据统计 -->
        <view class="module-item statistics-panel">
          <view class="statistics-header">
            <view 
              class="stat-tab" 
              :class="{active: activeTab === 'earnings'}" 
              @tap="switchTab('earnings')"
            >本月获得收益</view>
            <view 
              class="stat-tab" 
              :class="{active: activeTab === 'customers'}" 
              @tap="switchTab('customers')"
            >本月关联客户</view>
            <view 
              class="stat-tab" 
              :class="{active: activeTab === 'orders'}" 
              @tap="switchTab('orders')"
            >本月客户订单</view>
          </view>
          <!-- 收益内容 -->
          <view class="statistics-content" v-if="activeTab === 'earnings'">
            <view class="stat-value">¥{{statisticsData.monthEarnings}}</view>
            <view class="stat-total">总收益：¥{{statisticsData.totalEarnings}}</view>
          </view>
          <!-- 客户内容 -->
          <view class="statistics-content" v-if="activeTab === 'customers'">
            <view class="stat-value">{{statisticsData.monthCustomers}}人</view>
            <view class="stat-total">总客户：{{statisticsData.totalCustomers}}人</view>
          </view>
          <!-- 订单内容 -->
          <view class="statistics-content" v-if="activeTab === 'orders'">
            <view class="stat-value">{{statisticsData.monthOrders}}单</view>
            <view class="stat-total">总订单：{{statisticsData.totalOrders}}单</view>
          </view>
        </view>

        <!-- 功能模块列表 -->
        <view class="module-list">
          <!-- 分销海报 -->
          <view class="module-item function-item" @tap="navtoGo('DistributionPoster')">
            <image :src="distributionPosterUrl" mode="aspectFit" class="function-icon"></image>
            <view class="function-title">分销海报</view>
            <image :src="iconRightArrow" mode="aspectFit" class="right-arrow-icon"></image>
          </view>
          
          <!-- 我的客户 -->
          <view class="module-item function-item" @tap="navtoGo('MyCustomers')">
            <image :src="myCustomersUrl" mode="aspectFit" class="function-icon"></image>
            <view class="function-title">我的客户</view>
            <image :src="iconRightArrow" mode="aspectFit" class="right-arrow-icon"></image>
          </view>
          
          <!-- 分账流水 -->
          <view class="module-item function-item" @tap="navtoGo('AccountStatement')">
            <image :src="accountStatementUrl" mode="aspectFit" class="function-icon"></image>
            <view class="function-title">分账流水</view>
            <image :src="iconRightArrow" mode="aspectFit" class="right-arrow-icon"></image>
          </view>
          
          <!-- 提现记录 -->
          <view class="module-item function-item" @tap="navtoGo('WithdrawalRecord')">
            <image :src="withdrawalRecordUrl" mode="aspectFit" class="function-icon"></image>
            <view class="function-title">提现记录</view>
            <image :src="iconRightArrow" mode="aspectFit" class="right-arrow-icon"></image>
          </view>
        </view>
      </view>
    </view>
  </page>
</template>

<script>
import { mapState } from 'vuex'
import { isDomainUrl } from '@/utils/index'
import { getDistributionType } from '@/utils/enumeration'
import uniIcons from '@/components/uni/uni-icons/uni-icons.vue'
import serverOptions from '@/config/env/options'
const distributionTypeList = getDistributionType()
export default {
  components: {
    uniIcons
  },
  data() {
    return {
      file_ctx: this.file_ctx,
      defaultAvatar: this.file_ctx + 'image/business/moer/icon-default-avatar.png',
      distributionPosterUrl: this.file_ctx + 'static/image/business/hulu-v2/icon-distributionPoster.png',
      myCustomersUrl: this.file_ctx + 'static/image/business/hulu-v2/icon-myCustomers.png',
      accountStatementUrl: this.file_ctx + 'static/image/business/hulu-v2/icon-accountStatement.png',
      withdrawalRecordUrl: this.file_ctx + 'static/image/business/hulu-v2/icon-withdrawalRecord.png',
      iconRightArrow: this.file_ctx + 'static/image/business/hulu-v2/icon-right-arrow.png',
      statisticsData: {
        totalAmount: '0.00', // 总金额
        monthEarnings: '0.00', // 本月收益 
        totalEarnings: '0.00', // 总收益
        monthCustomers: '0', // 本月客户数
        totalCustomers: '0', // 总客户数
        monthOrders: '0', // 本月订单数
        totalOrders: '0' // 总订单数
      },
      distributorName: '', // 分销员名称
      receiverNo: '', // 分销员收款账号
      activeTab: 'earnings',
      providerId: '', // 服务商ID
      availableBalance: '0.00', // 可提现余额
      configData: {          // 配置数据
        appid: '',
        orgCode: ''
      },
      distributorMode: 1, // 分销抢客模式，默认为1
      distributorModeText: '简单模式（客户永久有效不允许抢客）' // 默认模式文本
    }
  },
  options: { styleIsolation: 'shared' },
  computed: {
    ...mapState('user', {
      curSelectUserInfo: state => state.curSelectUserInfo, // 当前选中的用户信息
      recordUserInfo: state => state.recordUserInfo
    }),
    infoObj() {
      const { name, headPath, type } = this.recordUserInfo || {}
      let distributionTypeText = this.$common.getEnumText(type, distributionTypeList)
      return {
        name: name || '',
        avatarUrl: isDomainUrl(headPath || this.defaultAvatar),
        distributionTypeText
      }
    }
  },
  onLoad() {
    // 获取服务商信息及分销模式
    this.getProviderInfo()
    // 加载配置数据
    this.loadConfigData().then(() => {
      // 获取分销员信息
      this.getDistributorInfo()
    })
    // 获取分销中心统计数据
    this.getDistributorStatistics()
  },
  methods: {
    navtoGo(url, obj = {}) {
      this.$navto.push(url, obj)
    },
    // 获取分销员信息
    async getDistributorInfo() {
      try {
        // 从本地存储获取当前用户信息
        const codeUserInfo = this.$common.getKeyVal('user', 'codeUserInfo', true);
        if (!codeUserInfo || !codeUserInfo.id) {
          console.error('未获取到用户ID');
          return;
        }
        
        // 调用接口获取分销员信息
        const res = await this.$api.distribution.accompanydistributorQueryOneByUserId({
          userId: codeUserInfo.id,
          providerId: serverOptions.providerId
        });
        
        // 如果接口返回成功且有分销员信息
        if (res.code === 0 && res.data) {
          console.log('获取到分销员信息:', res.data);
          // 更新分销员名称
          this.distributorName = res.data.name || '';
          
          // 保存分销员ID用于其他接口调用
          this.distributorId = res.data.id;

          this.receiverNo = res.data.receiverNo || ''
          
          // 获取分销中心统计数据
          this.getDistributorStatistics();

          // 获取余额
          this.getAvailableBalance()
        } else {
          console.warn('用户不是分销员或获取分销员信息失败');
        }
      } catch (error) {
        console.error('获取分销员信息异常:', error);
      }
    },
    // 获取分销中心统计数据
    async getDistributorStatistics() {
      try {
        // 检查是否有分销员ID
        if (!this.distributorId) {
          console.error('未获取到分销员ID');
          return;
        }
        
        // 使用分销员ID调用统计接口
        const res = await this.$api.distribution.accompanydistributorStatistic({ 
          distributorId: this.distributorId,
        });
        
        if (res.code === 0 && res.data) {
          // 使用接口返回的数据更新统计数据
          this.statisticsData = {
            monthEarnings: (res.data.monthIncome / 100).toFixed(2) || '0.00', // 转换为元并保留两位小数
            totalEarnings: (res.data.totalIncome / 100).toFixed(2) || '0.00',
            monthCustomers: res.data.monthCustomerNum || '0',
            totalCustomers: res.data.totalCustomerNum || '0',
            monthOrders: res.data.monthOrderNum || '0',
            totalOrders: res.data.totalOrderNum || '0'
          }
        }
      } catch (error) {
        console.error('获取分销中心统计数据失败:', error)
      }
    },
    // 获取余额方法
    async getAvailableBalance() {
      try {
        // 确保有收款账号
        if (!this.receiverNo) {
          console.error('未获取到收款账号');
          return;
        }
        
        // 调用查询账户余额API
        const res = await this.$api.accompanyDoctor.lklaccompanyBalanceQuery({
          orgNo: this.configData.orgCode || '980271',
          payType: '04',
          appid: this.configData.appid || 'OP10000305',
          receiverNo: this.receiverNo,
          providerId: serverOptions.providerId
        });
        
        if (res.data && res.data.respData) {
          // 从接口返回的respData中获取余额数据
          const balanceData = res.data.respData;
          
          // 使用curBalance作为当前余额
          if (balanceData.curBalance) {
            this.availableBalance = balanceData.curBalance;
          } else {
            this.availableBalance = '0.00';
          }
        } else {
          this.availableBalance = '0.00';
        }
      } catch (error) {
        console.error('获取余额失败:', error);
        this.availableBalance = '0.00';
      }
    },
    switchTab(tab) {
      this.activeTab = tab
    },
    // 获取服务商信息及分销模式
    async getProviderInfo() {
      try {
        
        // 调用查询服务商接口
        const res = await this.$api.accompanyDoctor.accompanyproviderQueryOne({
          id: serverOptions.providerId
        });
        
        if (res.code === 0 && res.data) {
          // 从接口返回中获取分销模式
          this.distributorMode = res.data.distributorMode || 1;
          
          // 根据模式值设置对应文本
          switch (this.distributorMode) {
            case 1:
              this.distributorModeText = '简单模式（客户永久有效不允许抢客）';
              break;
            case 2:
              this.distributorModeText = '平衡模式（客户90天有效，90天内不允许抢客）';
              break;
            case 3:
              this.distributorModeText = '竞争模式（客户90天有效，允许抢客）';
              break;
            default:
              this.distributorModeText = '简单模式（客户永久有效不允许抢客）';
          }
          
          console.log('服务商分销模式:', this.distributorMode, this.distributorModeText);
        } else {
          console.error('获取服务商信息失败:', res);
        }
      } catch (error) {
        console.error('获取服务商信息出错:', error);
      }
    },
    
    // 添加加载配置数据方法
    async loadConfigData() {
      try {
        console.log('开始加载配置信息');
        const response = await this.$api.distribution.queryConfig();
        if (response && response.code === 0 && response.data) {
          this.configData.appid = response.data.defaultAppid || "";
          this.configData.orgCode = response.data.defaultOrgCode || "";
          console.log('配置信息加载成功:', this.configData);
        } else {
          console.error('配置信息加载失败:', response);
          // 使用默认值
          this.configData.appid = "OP10000305";
          this.configData.orgCode = "980271";
        }
      } catch (error) {
        console.error('获取配置信息出错:', error);
        // 出错时使用默认值
        this.configData.appid = "OP10000305";
        this.configData.orgCode = "980271";
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.content-main {
  position: relative;
  padding: 0 25rpx;
  flex: 1;
  overflow: auto;
}

/* 客户关联模式 */
.relation-mode {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  margin-top: 25rpx;
}

.marquee-container {
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  padding-right: 20rpx;
  position: relative;
}

.marquee-content {
  display: inline-flex;
  animation: marquee 15s linear infinite;
  width: max-content;
}

.marquee-text {
  display: inline-block;
  font-size: 28rpx;
  color: #333;
  white-space: nowrap;
  padding-right: 50rpx; /* 两段文本之间的间距 */
}

@keyframes marquee {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.relation-title {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  padding-right: 20rpx;
}

.switch-mode-btn {
  display: flex;
  align-items: center;
  justify-content: center;
}

.indicator-icon {
  width: 46rpx;
  height: 46rpx;
}

/* 分销员信息 */
.distributor-info {
  margin-top: 25rpx;
  padding: 30rpx 20rpx;
}

.distributor-header {
  display: flex;
  flex-direction: column;
}

.distributor-name {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.total-amount {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666;
}

.amount {
  font-weight: bold;
  color: #333;
  margin-right: 30rpx;
}

.cash-out-btn {
  color: #2196f3;
  font-size: 26rpx;
  padding: 6rpx 20rpx;
  background-color: rgba(33, 150, 243, 0.1);
  border-radius: 30rpx;
}

/* 数据统计面板 */
.statistics-panel {
  margin-top: 25rpx;
  padding: 0;
  overflow: hidden;
}

.statistics-header {
  display: flex;
  border-bottom: 1px solid #eee;
}

.stat-tab {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  padding: 20rpx 0;
}

.stat-tab.active {
  color: #333;
  font-weight: 500;
  position: relative;
}

.stat-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #2196f3;
}

.statistics-content {
  padding: 30rpx 20rpx;
  text-align: center;
}

.stat-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.stat-total {
  font-size: 24rpx;
  color: #999;
}

.statistics-footer {
  display: flex;
  padding: 15rpx 0;
  border-top: 1px solid #eee;
}

.footer-item {
  flex: 1;
  text-align: center;
  font-size: 24rpx;
  color: #666;
}

/* 功能模块列表 */
.module-list {
  margin-top: 25rpx;
  background-color: #fff;
}

.function-item {
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx; /* 减小上下内边距 */
  margin-top: 12rpx; /* 减小项目间距 */
  position: relative;
  box-shadow: none !important;
  border-bottom: 1px solid #eee;
}

.function-icon {
  width: 40rpx; /* 减小图标宽度 */
  height: 40rpx; /* 减小图标高度 */
  margin-right: 20rpx; /* 增加与文字之间的间距 */
  flex-shrink: 0; /* 防止图标被挤压 */
  object-fit: contain; /* 确保图标正确适应大小 */
}

.function-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #ddd;
  margin-right: 15rpx;
}

.function-title {
  font-size: 28rpx; /* 减小字体大小 */
  color: #333;
  flex: 1; /* 让标题占据中间空间 */
}

.right-arrow-icon {
  width: 30rpx;
  height: 30rpx;
  flex-shrink: 0; /* 防止图标被挤压 */
  object-fit: contain; /* 确保图标正确适应大小 */
}

/* 公共样式 */
.module-item {
  background: #FFFFFF;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
</style>