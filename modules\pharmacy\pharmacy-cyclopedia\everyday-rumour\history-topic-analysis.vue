<template>
  <view class="main">
    <view class="top-head-main"></view>
    <view :style="'height:' + statusBarHeight + 'px;'"></view>
    <view class="top-nav">
      <view class="top-nav-l" @click.stop="handleBack">
        <!-- #ifndef MP-ALIPAY -->
        <image mode="aspectFit" :src="file_ctx + 'static/image/business/hulu-v2/icon-circle-left-arrows.png'" class="header-search-img"/>
        <!-- #endif -->
      </view>
      <view class="top-nav-c">历史题目解析</view>
    </view>
    <view class="participation-rumour">
      <view class="rumour-title" :style="{'background-image':'url('+ file_ctx +'static/image/business/pharmacy-cyclopedia/icon-participation-list-bg.png)','background-size': '100%','background-repeat': 'no-repeat'}">我参与的辟谣</view>
      <view class="participation-list">
        <view class="list-item" v-for="(item,index) in rumourList" :key ="index">
          <view class="value">{{ item.value }}</view>
          <view class="name">{{ item.name }}</view>
        </view>
      </view>
    </view>
    <scroll-refresh class="scroll-refresh-main" :isShowEmptySwitch="true" :fixed="false" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
      <view class="rumour-list">
        <view class="rumour-item" v-for="item in contentList" :key="item.id">
          <view class="rumour-item-content">
            <view class="item-img">
              <image v-if="item.answerState == 1" :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-rumour-correct.png'" mode="aspectFill"></image>
              <image v-else :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-rumour-error.png'" mode="aspectFill"></image>
            </view>
            <view class="item-title" :style="{marginBottom:'0rpx'}">{{item.title}}</view>
            <view class="item-answer">
              <view class="item-answer-title">正确答案：<span>{{item.answerText}}</span></view>
              <view class="item-answer-content" :class="{lineclamp3:item.isAll}">{{item.answerContent}}</view>
              <view v-if="item.isMore">
                <span v-if="item.isAll" @click="handleClickType(item,index)">展开</span>
                <span v-else @click="handleClickType(item,index)">收起</span>
              </view>
              <view class="rumour-item-date">
                <view class="rumour-item-date-l">{{item.answerDateText}}</view>
                <view class="rumour-item-date-r" @click="$navto.push('EverydayMorePoster',{id:item.refuteRumorDetailId})"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-rumour-more-share.png'"></image>分享海报</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-refresh>
    <view class="present-btn" @click="$navto.push('integrationShop')"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-rumour-present-img.png'"></image>积分兑好礼</view>
  </view>
</template>

<script>
  import { mapState } from 'vuex'
  export default {
    data(){
      return{
        statusBarHeight: 0,
        file_ctx: this.file_ctx,
        mescroll: null, // mescroll实例对象
        downOption: {
          auto: false // 不自动加载
        },
        upOption: {
          auto: false, // 不自动加载
          empty: {
            top: 0,
            zIndex: 999,
          }
        },
        contentList:[],
        historyStatisticObj:{},
      }
    },
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId,
      }),
      rumourList(){
        return [
          { name:'本月累计',value:this.historyStatisticObj.monthNum },
          { name:'本周累计',value:this.historyStatisticObj.weekNum },
          { name:'累计答对',value:this.historyStatisticObj.rightNum },
          { name:'累计答错',value:this.historyStatisticObj.wrongNum },
        ]
      }
    },
    onLoad(){
      this.init()
      this.refuterumorweekstatisticsQueryHistoryStatistic()
    },
    mounted(){
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
    },
    methods:{
      async refuterumorweekstatisticsQueryHistoryStatistic(){
        const res = await this.$api.drugBook.refuterumorweekstatisticsQueryHistoryStatistic({accountId:this.accountId})
        this.historyStatisticObj = res.data
      },
      returnFn(obj) {
        const that = this
        setTimeout(function () {
          let params = {
            current: obj.pageNum,
            size: obj.pageSize,
            condition: {
              accountId:that.accountId,
              recordType:1,
            }
          }
          that.$api.drugBook.refuterumorrecordQueryPage(params).then(res => {
            let data = res.data.records.map(item=>{
              if(item.answerContent.length > 80){
                item.isMore = true
                item.isAll = true
              } else {
                item.isMore = false
                item.isAll = false
              }
              return {
                ...item,
                answerDateText:item.createTime?that.$common.formatDate(new Date(item.createTime),'yyyy-MM-dd').replace(/-/g, '/'):'',
                answerText:item.answer == 0 ? '假的' : '真的',
              }
            })
            if (obj.pageNum === 1) {
              that.contentList = []
            }
            that.contentList = [...that.contentList, ...data]
            obj.successCallback && obj.successCallback(data)
          })
        }, that.$constant.noun.scrollRefreshTime)
      },
      handleClickType(item){
        item.isAll = !item.isAll
      },
      scrollInit(scroll) {
        scroll.optUp.page.num = 1
        scroll.optUp.page.size = 10
        this.mescroll = scroll
      },
      init() {
        this.$nextTick(() => {
          this.mescroll.triggerDownScroll()
        })
      },
      handleBack(){
        this.$navto.back(1)
      },
    },
 }
</script>

<style lang='scss' scoped>
  @mixin contentFlex {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .main{
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
    background: #F4F6FA;
  }
  .top-nav{
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 44px;
    line-height: 44px;
    padding: 0 32rpx 0 24rpx;
    .top-nav-l{
      display: flex;
      width: 48rpx;
      height: 48rpx;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .top-nav-c{
      flex: 1;
      text-align: center;
      height: 44rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #2D2F38;
      line-height: 44rpx;
      margin-right: 48rpx;
    }
  }
  .top-head-main{
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    height: 282rpx;
    background-image: url($imgUrl + '/business/pharmacy-cyclopedia/icon-everyday-rumour-header-bg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
  .participation-rumour{
    position: relative;
    min-height: 220rpx;
    background-color: #fff;
    margin: 20rpx 32rpx 0;
    border-radius: 16rpx;
    .rumour-title{
      position: absolute;
      height: 102rpx;
      width: calc(686rpx - 48rpx);
      padding: 32rpx 24rpx 28rpx;
      font-size: 30rpx;
      color: #000000;
      font-weight: 600;    
    }
    .participation-list{
      position: absolute;
      top: 102rpx;
      width: 100%;
      display: flex;
      justify-content: space-around;
      text-align: center;
      .list-item{
        display: flex;
        flex-direction: column;
        .value{
          font-size: 32rpx;
          color: #1D2029;
          font-weight: 600;
        }
        .name{
          font-size: 24rpx;
          color: #4E5569;
        }
      }
    }
  }
  .present-btn{
    position: absolute;
    left: 50%;
    bottom: 40rpx;
    z-index: 999;
    transform: translateX(-50%);
    @include contentFlex;
    width: 290rpx;
    height: 88rpx;
    background: #00B484;
    box-shadow: 0rpx 4rpx 12rpx 0rpx rgba(9,97,73,0.45);
    border-radius: 44rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #FFFFFF;
    image{
      display: flex;
      width: 34rpx;
      height: 32rpx;
      margin-right: 8rpx;
    }
  }

  .scroll-refresh-main{
    flex: 1;
    overflow-x: hidden;
    /deep/ .mescroll-empty-box{
      position: absolute !important;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .rumour-list{
    position: relative;
    height: calc(100% - 30rpx);
    overflow-x: hidden;
    background-color: #f0f2f2;
    .rumour-item{
      margin-bottom: 20rpx;
      padding: 0 32rpx;
      .rumour-item-content{
        position: relative;
        padding: 32rpx 24rpx;
        margin-top: 26rpx;
        border-radius: 16rpx;
        background-color: #fff;
        .item-img{
          position: absolute;
          right: 0;
          top: 10rpx;
          display: flex;
          width: 174rpx;
          height: 136rpx;
          opacity: .3;
          image{
            width: 100%;
            height: 100%;
          }
        }
        .item-title{
          font-size: 30rpx;
          font-weight: 600;
          color:#000;
          width: 480rpx;
        }
        .item-answer{
          margin-top: 32rpx;
          .item-answer-title{
            font-weight: 600;
            font-size: 30rpx;
            color: #4E5569;
            margin-bottom: 24rpx;
            span{
              font-size: 30rpx;
              color: #00B484;
            }
          }
          .item-answer-content{
            word-wrap: break-word;
            font-size: 26rpx;
            color: #4E5569;
            line-height: 44rpx;
          }
          .rumour-item-date{
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 24rpx;
            .rumour-item-date-l{
              color: #868C9C;
            }
            .rumour-item-date-r{
              @include contentFlex;
              width: 192rpx;
              height: 56rpx;
              border-radius: 28rpx;
              border: 1rpx solid #00B484;
              font-size: 26rpx;
              color: #00B484;
              font-weight: 600;
              image{
                display: flex;
                width: 36rpx;
                height: 32rpx;
                margin-right: 6rpx;
              }
            }
          }
          .lineclamp3{
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            white-space: normal;
          }
        }
        .item-bott{
          display: flex;
          justify-content: space-between;
          .item-bott-l,.item-bott-r{
            width: 48%;
            button{
              @include contentFlex;
              height: 88rpx;
              background: #F4F6FA;
              border-radius: 44rpx;
              color: #13b38e;
              color: #1D2029;
              font-size: 30rpx;
              &::after{
                border: none !important;
              }
            }
          }
        }
      }
      &:last-child{
        margin-bottom: 0;
      }
    }
  }
</style>