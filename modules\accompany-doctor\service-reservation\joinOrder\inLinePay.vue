<template>
  <view class="guidanceBox">
    <view class="tapClue" v-if="currentProviderName">
      本次服务由（{{ currentProviderName }}）为您提供
    </view>
    <!-- 锁单文案 -->
    <view class="guidanceTwoTitle" v-if="isLocking && !accompanybookOne.pay">{{lockText}}</view>
    <!-- 已取消保障文案 -->
    <view class="insureSign" v-if="isRefundInsure">
        <view class="">已取消保障，已退款￥{{insuredInfo.price / 100}}</view>
    </view>
    <!-- S 服务名称 -->
    <view class="guidanceCard">
      <view class="serverTitle">
        <image
          class="serverIcon"
          :src="file_ctx + accompanybookOne.serviceDetailImg"
          mode=""
        ></image>
        <view class="serviceName">
          <view class="serverNameT">{{ accompanybookOne.bookName }}</view>
        </view>
      </view>
    </view>
    <!-- E 服务名称 -->

    <!-- S 订单信息 -->
    <view class="payPrompt">当前共{{(accompanybookOne.accompanyOrderDTOList  || []).length}}个订单需要合并支付</view>
    <view class="DTOListItem" :key="key" v-for="(item,key) in accompanybookOne.accompanyOrderDTOList || []">
      <view class="DTOListHeader">
        <view>订单：{{item.id}}</view>
        <view>
          服务费：￥{{item.payPrice / 100}}
          <image @click="awayItem(key)" :class="{awayArrow:joinOrderMap[key].isAway}" class="arrowDown" :src="arrowDown" mode=""></image>
        </view>
      </view>
      <view class="DTOContent" :class="{awayDTOContent:joinOrderMap[key].isAway}" >
        <view class="line">
          <view class="title">就诊时间：</view>
          <view class="lineContnet">{{timestampToDateTime(item.startTime)}}~{{timestampToDateTime(item.endTime)}}</view>
        </view>
        <view class="line">
          <view class="title">就诊医院：</view>
          <view class="lineContnet">{{item.hospitalName}}</view>
        </view>
        <view class="line">
          <view class="title">服务：</view>
          <view class="lineContnet">{{ item.serviceName }}</view>
        </view>
        <view class="line">
          <view class="title">服务费：</view>
          <view class="lineContnet">{{item.payPrice / 100}}</view>
        </view>
      </view>
      <view class="DTOFooter" v-if="showInsuranceContent(item.startTime) && (provinceValue.insureButton === 1 || (provinceValue.insureButton === 2 && item.certfNo)) && !isAgeOver65(item.certfNo, item.age)" :class="{isConfirm:joinOrderMap[key].productCode}" @click="showInsureInfo(isLocking,key,joinOrderMap[key])">
        <view class="title">门诊无忧服务费用</view>
        <view class="lineContnet">费用：￥{{joinOrderMap[key].insuranceNum || '5'}}</view>
      </view>
      <image class="LeafSign" v-if="showInsuranceContent(item.startTime) && (provinceValue.insureButton === 1 || (provinceValue.insureButton === 2 && item.certfNo)) && !isAgeOver65(item.certfNo, item.age)" @click.stop="!isLocking && cancelInsure(key,joinOrderMap[key])" :src="joinOrderMap[key].productCode ? GreenLeaf : Leaf"></image>
    </view>
    <!-- E 订单信息 -->

    <!-- 具体明细 -->
    <view :class="{showDetailsBox:showDetailsBox}" class="DetailsBox">
      <!-- 标题 -->
      <view class="confirmTitle">
        费用明细
        <image class="iconClose" @click="showDetailsBox = false" :src="iconClose" mode=""></image>
      </view>
      <view class="DetailsContent">
        <view class="DetailsLine">
          <view class="lineTitle">总价格</view>
          <view class="">
            <text class="lineSign">￥</text>
            <text class="totalPrice">{{ getPays }}</text>
          </view>
        </view>
        <view class="DetailsLine">
          <text>服务费</text>
          <text>￥{{ getPayPrice }}</text>
        </view>
        <view class="DetailsLine" v-if="getInsuranceNum">
          <text>门诊无忧服务费用</text>
          <text>￥{{getInsuranceNum}}</text>
        </view>
      </view>
    </view>
    <!-- 按钮 -->
    <view class="buttonMap">
      <agreement ref="agreementRef" class="agreement" v-model="isAgreed" :provinceValue="provinceValue" @onLoadDocument="onLoadDocument"></agreement>
      <view class="Detail">
        <view class="pays">￥{{ getPays }}</view>
        <view class="showBtn" :class="{showBtnUpDown:showDetailsBox}" @click="showDetailsBoxFn">
          明细
          <image class="right_arrow" :src="right_arrow" mode=""></image>
        </view>
        <view class="line"></view>
      </view>
      <view class="inLinePayBtn" @click="inLinePay">{{ PayText }}</view>
    </view>
    <selectComboPop
      @change="changeComboPop"
      @selectCombo="selectCombo"
      :comboDataMap="comboDataMap"
      :openFlag="showComboMap"
    ></selectComboPop>
    <view v-if="showDetailsBox" @click="showDetailsBox = false" class="mask"></view>
  </view>
</template>

<script>
import serverOptions from "@/config/env/options";
import common from "@/common/util/main";
import selectComboPop from "../../components/selectComboPop.vue";

export default {
  components: {
    selectComboPop,
  },
  props: {
    accompanybookOne: {
      type: Object,
      default: {
      },
    },
    underInfo: {
      type: Object,
      default:{}
    },
    insuredInfo: {
      type: Object,
      default:{}
    },
    provinceValue: {
      type: Object,
      default:{}
    }
  },
  computed: {
    //判断是否退单 返回true 则是退单 返回false 则是未退单
    isRefundInsure(){
      return this.insuredInfo.refundInsure === 1;
    },
    getPays() {
      let payPrice = 0;
      let insuranceNum = 0;
      if(!this.accompanybookOne.accompanyOrderDTOList) return
      this.accompanybookOne.accompanyOrderDTOList.map(e=>{
        payPrice += e.payPrice  / 100;
      })
      this.joinOrderMap.map(e=>{
        insuranceNum += +e.insuranceNum;
      })
      return payPrice + insuranceNum
    },
    getPayPrice(){
      let payPrice = 0;
      if(!this.accompanybookOne.accompanyOrderDTOList) return

      this.accompanybookOne.accompanyOrderDTOList.map(e=>{
        payPrice += e.payPrice  / 100;
      })
      return payPrice;
    },
    getInsuranceNum(){
      let insuranceNum = 0;
      this.joinOrderMap.map(e=>{
        insuranceNum += +e.insuranceNum;
      })
      return insuranceNum;
    },
    async getAccompanybookOne() {
      // 设置支付按钮文字内容
      this.setPayText();
      let {
        data: {
          records: [serverCurrent],
        },
      } = await this.$api.accompanyDoctor.getAccompanyservicePage({
        condition: { serviceId: this.accompanybookOne.serviceId },
      });
      this.serverCurrent = serverCurrent;
      return this.accompanybookOne;
    },
  },
  watch:{
    underInfo:{
      handler(newVal,oldVal) {

      },
      immediate: true,
      deep:true
    },
    insuredInfo:{
      handler(newVal,oldVal) {
        if(this.insuredInfo.productCode){
          this.insuranceNum = this.insuredInfo.insuranceNum;
        }
      },
      immediate: true,
      deep:true
    },
    accompanybookOne:{
      async  handler(newVal,oldVal) {
        console.log('accompanybookOneWatch',newVal);

        if(!this.accompanybookOne.id) return
        let data = await this.$ext.user.isPayedOrder(this.accompanybookOne.id,4);
        // 此单已被锁定
        if(data){
          this.isLocking = true;
        }
        if(this.accompanybookOne.accompanyOrderDTOList.length > 0){
          let index = 0;
          this.joinOrderMap = Array.from({ length: this.accompanybookOne.accompanyOrderDTOList.length }, () => ({
              insuranceNum:'', // 保险金额
              isFilling:false, // 是否填写
              isAway:false, // 是否收起
              productCode:this.accompanybookOne.accompanyOrderDTOList[index++].productCode // 产品编码
            }));
        }
      },
      immediate: true,
      deep:true
    },
  },
  data() {
    return {
      isAgreed:true,
      lockText:'当前订单已锁单，无法变更订单金额',
      file_ctx: this.file_ctx,
      paid: this.$static_ctx + "image/business/hulu-v2/paid.png",
      round: this.$static_ctx + "image/business/hulu-v2/round.png",
      right_arrow: this.$static_ctx + "image/business/hulu-v2/right-arrow.png",
      Ellipse: this.$static_ctx + "image/business/hulu-v2/Ellipse.png",
      iconPostSucess:
        this.$static_ctx + "image/business/hulu-v2/icon-post-sucess.png",
      Check: this.$static_ctx + "image/business/hulu-v2/Check.png",
      CheckGreen: this.$static_ctx + "image/business/hulu-v2/CheckGreen.png",
      Shield: this.$static_ctx + "image/business/hulu-v2/Shield.png",
      shieldUnchecked: this.$static_ctx + "image/business/hulu-v2/shield-unchecked.png",
      entering: this.$static_ctx + "image/business/hulu-v2/entering.png",
      iconClose: this.$static_ctx + "image/business/hulu-v2/icon-close2.png",
      arrowDown: this.$static_ctx + "image/business/hulu-v2/arrow-down.png",
      GreenLeaf: this.$static_ctx + "image/business/hulu-v2/GreenLeaf.png",
      Leaf: this.$static_ctx + "image/business/hulu-v2/Leaf.png",
      currentServer: {},
      showComboMap: false,
      comboDataMap: [],
      serverCurrent: {},
      PayText: "",
      currentProviderName: "",
      gotoFlag: false,
      isNotice:false,
      showDetailsBox:false,
      directPayment:false,
      insuranceNum:5,
      isShake:false,
      isLocking:false,
      serverOptions,
      joinOrderMap:[]
    };
  },
  async mounted() {
    console.log('this.serverOptions',this.serverOptions);

  },
  methods: {
    onLoadDocument(urlList){
      // 读取buttonMap盒子的高度
      this.$nextTick(()=>{
        const query = uni.createSelectorQuery().in(this);
        query.select('.buttonMap').boundingClientRect();
        query.exec((res) => {
          console.log('buttonMap高度',res);
          this.buttonMapHeight = res[0].height;
        })
      })
    },
    cancelInsure(index,item){
      if(!item.insuranceNum) return this.showInsureInfo(this.isLocking,index,item);
      item.insuranceNum = '';
      item.productCode = '';
    },
    awayItem(key){
      console.log('key',key);

      this.joinOrderMap[key].isAway = !this.joinOrderMap[key].isAway
    },
    showInsuranceContent(timestamp){
      let currentTime = new Date().getTime();
      const date = new Date(timestamp);
      date.setHours(0, 0, 0, 0);
      let endTime = date.getTime();
      if(currentTime < endTime){
        return true
      }
    },
    updateInsurance(insuranceNum){
      this.insuranceNum = insuranceNum;
    },
    showDetailsBoxFn(){
      this.showDetailsBox = !this.showDetailsBox;
    },
    // 判断年龄是否超过65岁
    isAgeOver65(certfNo, age) {
      let isOverFromIdCard = false;
      let isOverFromAge = false;

      // 从身份证号判断年龄
      if (certfNo && certfNo.length === 18) {
        const birthYear = parseInt(certfNo.substring(6, 10));
        const currentYear = new Date().getFullYear();
        const ageFromIdCard = currentYear - birthYear;
        isOverFromIdCard = ageFromIdCard > 65;
      }

      // 从年龄字段判断
      if (age) {
        isOverFromAge = parseInt(age) > 65;
      }

      // 只要有一个条件满足就返回true
      return isOverFromIdCard || isOverFromAge;
    },
    showInsureInfo(isLocking,key,item){
      console.log('item',item);
      this.$emit('ConfirmPurchaseInsurance','insureInfo',isLocking,key,item);
    },
    setPayText() {
      this.gotoFlag = false;
      let currentProviderId = this.accompanybookOne.providerId;
      // 判断当前这条订单的真实服务商是否是当前平台
      if (
        serverOptions.providerId !== currentProviderId ||
        serverOptions.source === 1
      ) {
        this.PayText = "前往支付";
        this.currentProviderName = this.accompanybookOne.providerName;
        this.gotoFlag = true;
        return;
      }
      this.PayText = `在线支付￥${this.getPays}`;
    },
    handleCopyOrder() {
      uni.setClipboardData({
        data: this.accompanybookOne.id,
        success: function () {
          uni.showToast({ title: "复制成功", icon: "success" });
        },
      });
    },
    selectInsurance({productCode},index){
      this.$set(this.joinOrderMap[index],'insuranceNum',serverOptions.productCodeMap.filter(e=>e.productCode === productCode)[0].insuranceNum)
      this.$set(this.joinOrderMap[index],'productCode',productCode)
      console.log('this.joinOrderMap',this.joinOrderMap);

    },
    clearOrder() {
      if (this.accompanybookOne.pay) {
        return this.$emit("clearOrder");
      }
      uni.showModal({
        title: "提示",
        content: "确定要取消订单吗？",
        success: async (res) => {
          if (res.confirm) {
            let {
              data: { records, total },
            } = await this.$api.accompanyDoctor.accompanybookCancel({
              id: this.accompanybookOne.id,
              cancelReason: "用户主动取消",
              refundAmount: this.accompanybookOne.payPrice || 0 // 全额退款，单位分
            });
            uni.navigateBack();
            setTimeout(function () {
              uni.showToast({
                title: "取消成功",
                icon: "none",
              });
            }, 10);
          }
        },
      });
    },
    changeComboPop(flag) {
      this.showComboMap = flag;
    },
    selectCombo(res) {
      console.log("res", res);
      this.$emit("comboPay", res);
    },
    setDirectPayment(flag){
      this.directPayment = flag;
      this.showDetailsBox = false;
    },
    async inLinePay() {
      // 先检查协议状态
      const isAgreed = await this.$refs.agreementRef.checkAgreement();
      if (!isAgreed) return;
      if (this.gotoFlag) {
        let currentProviderId = this.accompanybookOne.providerId;
        let appId = serverOptions.optionsMap.filter(
          (e) => e.providerId === currentProviderId
        )[0]?.appId;
        // 跳转到其他的小程序去进行支付
        uni.navigateToMiniProgram({
          appId,
          path:
            "modules/accompany-doctor/service-reservation/joinOrder/joinOrder?orderId=" +
            this.accompanybookOne.id,
          envVersion: "develop",
          success(res) {
            // 打开成功
            console.log("跳转成功", res);
          },
        });
        return;
      }
      this.$emit("inLinePay");
    },
    getAccompanyCombineInsureList(){
      return this.joinOrderMap.map((e,index)=>{
        return {
          productCode:e.productCode,
          id:this.accompanybookOne.accompanyOrderDTOList[index].id
        }
      })
    },
    // 仅检测自身可枚举属性
    isObjectNotEmpty(obj) {
      return Object.keys(obj).length > 0
    },
    timestampToDateTime(timestamp, flag) {
      if (!timestamp) return "";
      // 创建一个新的Date对象，传入的时间戳是以毫秒为单位的
      var date = new Date(timestamp);
      // 获取年、月、日、时、分、秒
      var year = date.getFullYear();
      var month = ("0" + (date.getMonth() + 1)).slice(-2); // 月份是从0开始的，所以+1，并补零
      var day = ("0" + date.getDate()).slice(-2); // 获取天数，并补零
      var hour = ("0" + date.getHours()).slice(-2); // 获取小时，并补零
      var minute = ("0" + date.getMinutes()).slice(-2); // 获取分钟，并补零
      if (flag) return `${month}.${day} ${hour}:${minute}`;
      // 返回格式化的字符串
      return `${year}.${month}.${day} ${hour}:${minute}`;
    },
  },
};
</script>

<style lang="scss">
  .payPrompt{
    font-weight: 400;
    font-size: 24rpx;
    color: #1687F7;
    margin: 24rpx 0;
  }
  .DTOListItem{
    width: 100%;
    margin-bottom: 24rpx;
    border-radius: 16rpx;
    background: #FFFFFF;
    position: relative;
    .DTOListHeader{
      height: 80rpx;
      background: #FFFFFF;
      padding: 20rpx;
      box-sizing: border-box;
      border-radius: 16rpx 16rpx 0rpx 0rpx;
      font-weight: 500;
      font-size: 28rpx;
      color: #1D2029;
      display: flex;
      justify-content: space-between;
      .arrowDown{
        width: 28rpx;
        height: 28rpx;
        margin-left: 8rpx;
        transition: all 0.3s;
      }
      .awayArrow{
        transform: rotate(180deg);
      }
      view{
        display: flex;
        align-items: center;
      }
    }
    .awayDTOContent{
      height: 0 !important;
      overflow: hidden;
      transform: scaleY(0);
      transform-origin: top;
    }
    .DTOContent{
      transition: all 0.3s;
      height: 216rpx;
      background: #FFFFFF;
      padding: 0 20rpx;
      box-sizing: border-box;
      .line{
        display: flex;
      }
      .title{
        width: 120rpx;
        height: 34rpx;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #4E5569;
        margin-bottom: 20rpx;
      }
      .lineContnet{
        font-weight: 400;
        font-size: 24rpx;
        color: #1D2029;
        margin-bottom: 20rpx;

      }
    }
    .DTOFooter{
      height: 74rpx;
      background: #FFFFFF;
      border-top: 1rpx solid #E6E6E6;
      padding: 0 40rpx 0 20rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;
      .title{
        font-weight: 800;
        font-size: 24rpx;
        color: #AAAAAA;
      }
      .lineContnet{
        font-weight: 500;
        font-size: 24rpx;
        color: #AAAAAA;
      }
    }
    .isConfirm{
      background: #EEFFF5 !important;
      .title{
        color: #00B484 !important;
      }
      .lineContnet{
        color: #00B484 !important;
      }
    }
    .LeafSign{
      width: 40rpx;
      height: 40rpx;
      position: absolute;
      right: 0rpx;
      bottom: 0rpx;
    }
  }
  .insureSign{
    width: 100%;
    height: 60rpx;
    background: #E0F4EF;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    border: 1rpx solid #AAD8CC;
    display: flex;
    justify-content: space-between;
    padding: 0 24rpx;
    align-items: center;
    font-weight: 500;
    font-size: 26rpx;
    color: #00926B;
    margin: 32rpx 0;
    box-sizing: border-box;
    .surrender{

    }
  }
  .guidanceTwoTitle{
    width: 686rpx;
    height: 60rpx;
    line-height: 60rpx;
    background: #FFEEE6;
    border-radius: 8rpx;
    padding: 0 24rpx;
    font-weight: 500;
    font-size: 26rpx;
    color: #FF5500;
    box-sizing: border-box;
    margin-top: 12rpx;
    border: 1rpx solid #F7D4C4;
  }
.mask{
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.6);
  position: fixed;
  left: 0;
  top: 0;
}
.headerTap {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .cencelTitle {
    font-weight: 600;
    font-size: 36rpx;
    color: #1d2029;
    display: flex;
    align-items: center;
    .cencelTitleIcon {
      width: 40rpx;
      height: 40rpx;
      margin-right: 12rpx;
    }
    .surplus{
      font-weight: bold;
      font-size: 32rpx;
      color: #4E5569;
      margin-left: 12rpx;
      margin-right: 6rpx;
    }
    .serplusTime{
      font-weight: 800;
      font-size: 36rpx;
      color: #FF5500;
    }
  }
}
.tapClue {
  width: 100%;
  height: 60rpx;
  background: #e0f4ef;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  border: 1rpx solid #aad8cc;
  line-height: 60rpx;
  font-weight: 500;
  font-size: 26rpx;
  color: #00926b;
  padding-left: 24rpx;
  margin-top: 16rpx;
  box-sizing: border-box;
}
.orderInfo {
  .orderTitle {
    margin-bottom: 16rpx;
    font-weight: 600;
    font-size: 32rpx;
    color: #2d2f38;
  }
  .timeTitle {
    font-weight: 400;
    font-size: 26rpx;
    color: #2d2f38;
  }
  .orderIdTitle {
    font-weight: 400;
    font-size: 26rpx;
    color: #2d2f38;
  }
  .orderValue {
    margin-bottom: 16rpx;
    display: flex;
    justify-content: space-between;
    font-weight: 400;
    font-size: 26rpx;
    color: #6f7281;
    .copy {
      margin-left: 8rpx;
      font-weight: 400;
      font-size: 26rpx;
      color: #316EAB;
    }
  }
}
.orderMap{
    padding-bottom: 32rpx;
    .orderInfo{
      padding-bottom: 32rpx;
      border-bottom: 2rpx solid #EAEBF0;
    }
    .serverNameT{
      margin: 32rpx 0;
      display: flex;
      justify-content:space-between;
    .serverNameTitle{
      font-weight: 600;
      font-size: 32rpx;
      color: #1D2029;
    }
    .payPrice{
      font-weight: 500;
      font-size: 36rpx;
      color: #FF5500;
    }
  }
    .orderItem{
    width: 100%;
    font-weight: 400;
    font-size: 26rpx;
    color: #4E5569;
    display: flex;
    justify-content: space-between;
    margin-bottom: 16rpx;
    .orderTitle{
      font-weight: 400;
      font-size: 26rpx;
      color: #1D2029;
    }
    .payPrice{
      font-weight: 500;
      font-size: 26rpx;
      color: #FF5500;
    }
    .copy{
      font-weight: 400;
      font-size: 26rpx;
      color: #316EAB;
      margin-left: 8rpx;
    }
  }
  }
.serverTitle {
  display: flex;
  .paySign {
    font-weight: 400;
    font-size: 22rpx;
    color: #ff5500;
  }
  .payNums {
    font-weight: 500;
    font-size: 36rpx;
    color: #ff5500;
  }
  .serverIcon {
    width: 80rpx;
    height: 80rpx;
    background: #d8d8d8;
    border-radius: 40rpx;
    border: 1rpx solid #d9dbe0;
    margin-right: 20rpx;
    flex-shrink: 0;
  }
  .serviceName {
    font-weight: 500;
    font-size: 32rpx;
    color: #1d2029;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    .serviceName {
      font-weight: 500;
      font-size: 32rpx;
      color: #1D2029;
    }
    .serverTime {
      font-weight: 400;
      font-size: 24rpx;
      color: #1d2029;
      width: 100%;
      margin-top: 8rpx;
      .tabTitle {
        font-weight: 400;
        font-size: 24rpx;
        color: #4e5569;
      }
    }
    .employee {
      font-weight: 400;
      font-size: 22rpx;
      color: #1d2029;
      display: flex;
      align-items: center;
      .avatar {
        width: 32rpx;
        height: 32rpx;
        margin-left: 20rpx;
        border-radius: 50%;
        margin-right: 8rpx;
      }
    }
    .signal {
      font-weight: 400;
      font-size: 22rpx;
      color: #ff5500;
    }
    .serverNum {
      font-weight: 500;
      font-size: 36rpx;
      color: #ff5500;
    }
    .tag {
      font-weight: 400;
      font-size: 20rpx;
      color: #868c9c;
    }
  }
}
.guidanceBox {
  width: 100vw;
  padding: 0 32rpx;
  box-sizing: border-box;
  height: calc(100vh - 274rpx);
  overflow: scroll;
}
.guidanceCard {
  width: 686rpx;
  background: #ffffff;
  border-radius: 16rpx;
  margin-top: 32rpx;
  padding: 20rpx 24rpx;
  box-sizing: border-box;
  .serviceName {
    font-weight: 500;
    font-size: 32rpx;
    color: #1d2029;
    .signal {
      font-weight: 400;
      font-size: 22rpx;
      color: #ff5500;
    }
    .serverNum {
      font-weight: 500;
      font-size: 36rpx;
      color: #ff5500;
    }
    .tag {
      font-weight: 400;
      font-size: 20rpx;
      color: #868c9c;
    }
  }
  .headerTab {
    width: 686rpx;
    height: 192rpx;
    background: #ffffff;
    border-radius: 16rpx;
    padding: 24rpx;
    box-sizing: border-box;
    display: flex;
    .serverIcon {
      width: 112rpx;
      height: 112rpx;
      background: #d8d8d8;
      border-radius: 12rpx;
      border: 1rpx solid #d9dbe0;
      margin-right: 20rpx;
    }
    .changeServer {
      width: 148rpx;
      height: 52rpx;
      background: #ffffff;
      border-radius: 36rpx;
      border: 1rpx solid #d9dbe0;
      margin-left: auto;
    }
  }
}
.selectInsurance {
  background: linear-gradient(133deg, #ffffff 0%, #e0f4ef 100%) !important;
  .InsuranceInfo {
    border: 2rpx solid #9cebd7 !important;
  }
}
.Insurance {
  width: 100%;
  height: 268rpx;
  background: linear-gradient(133deg, #ffffff 0%, #ffebcb 100%);
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  margin-top: 24rpx;
  padding: 20rpx;
  box-sizing: border-box;
  .shake{
    animation: shake 0.5s;
    animation-iteration-count: infinite;
  }
  // 写的简短一点
  @keyframes shake {
    0% {
      transform: translate(1px, 1px) rotate(0deg);
    }
    50% {
      transform: translate(-1px, -2px) rotate(-1deg);
    }
    100% {
      transform: translate(1px, -1px) rotate(1deg);
    }
  }
  .notice{
    font-weight: 500;
    font-size: 24rpx;
    color: #777777;
    display: flex;
    align-items: center;
    margin-top: 8rpx;
    .selectorButton {
      width: 32rpx;
      height: 32rpx;
      margin-top: auto;
      margin-right: 8rpx;
    }
    .specification{
      color: #1687F7;
    }
  }
  .InsuranceInfo {
    width: 100%;
    height: 192rpx;
    background: #ffffff;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    border: 2rpx solid #ffe3b6;
    padding: 16rpx;
    box-sizing: border-box;
    .InsuranceContent {
      display: flex;
      align-items: flex-end;
      .Group {
        width: 120rpx;
        height: 120rpx;
      }
      .InsuranceInfoMiddle {
        margin-left: 16rpx;
        .InsuranceContentTitle {
          display: flex;
          align-items: center;
          .title {
            font-weight: 800;
            font-size: 32rpx;
            color: #1d2029;
            margin-right: 8rpx;
          }
          .miniTitle {
            height: 28rpx;
            border-radius: 4rpx 4rpx 4rpx 4rpx;
            border: 1rpx solid #7777777a;
            font-weight: 400;
            font-size: 22rpx;
            color: #777777;
            display: flex;
            align-items: center;
            padding: 0 6rpx;
            box-sizing: border-box;
          }
          .right_arrow {
            width: 28rpx;
            height: 28rpx;
          }
        }
        .InsurancePrompt {
          font-weight: 500;
          font-size: 22rpx;
          color: #ff5500;
        }
        .pay {
          font-weight: 500;
          font-size: 24rpx;
          color: #ff5500;
          .dollarSign {
            font-size: 24rpx;
          }
          .payNum {
            font-size: 34rpx;
          }
          .everyone {
            font-size: 32rpx;
          }
          .man {
            font-size: 24rpx;
          }
        }
      }
      .selectBox{
        height: 120rpx;
        flex: 1;
        padding-left: 32rpx;
        display: flex;
        align-items: flex-end;
      }
      .selectorButton {
        width: 32rpx;
        height: 32rpx;
      }
    }
    .describe {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      font-weight: 400;
      font-size: 22rpx;
      color: #4E5569;
      margin-top: 8rpx;
      .Check {
        width: 32rpx;
        height: 32rpx;
        &:first-child {
          margin: 0 12rpx 0 0 !important;
        }
        &:not(:first-child){
          margin: 0 8rpx 0 20rpx;
        }
      }
    }
  }
}
  .DetailsBox{
    z-index: 2;
    position: fixed;
    bottom: -378rpx;
    left: 0;
    width: 750rpx;
    height: 378rpx;
    background: #F4F6FA;
    border-radius: 24rpx 24rpx 0rpx 0rpx;
    transition: all 0.1s ease-out;
    padding: 32rpx 32rpx 48rpx;
    box-sizing: border-box;
    .confirmTitle{
      font-weight: 500;
      font-size: 34rpx;
      color: #1D2029;
      text-align: center;
      position: relative;
      .iconClose{
        width: 32rpx;
        height: 32rpx;
        position: absolute;
        right: 0;
        top: 6rpx;
      }
    }
    .DetailsContent{
      width: 100%;
      background: #FFFFFF;
      border-radius: 16rpx 16rpx 16rpx 16rpx;
      padding: 50rpx 20rpx 16rpx;
      box-sizing: border-box;
      .DetailsLine{
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16rpx;
      }
      .lineTitle{
        font-weight: 500;
        font-size: 34rpx;
        color: #1D2029;
      }
      .lineSign{
        font-weight: 400;
        font-size: 22rpx;
        color: #FF5500;
      }
      .totalPrice{
        font-weight: 500;
        font-size: 36rpx;
        color: #FF5500;
      }
    }
  }
  .showDetailsBox{
    bottom: 270rpx;
  }
  .bottom{
    position: fixed;
    bottom: 0rpx;
    left: 0;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    width: 100%;
  }
  .bottomButtonMap{
      display: flex;
      justify-content: space-between;
      margin: 22rpx 0 22rpx 0;
      padding: 0 32rpx;
      box-sizing: border-box;
      width: 100%;
    .buttonItem{
      width: 334rpx;
      height: 88rpx;
      background: #FFFFFF;
      border-radius: 44rpx 44rpx 44rpx 44rpx;
      border: 2rpx solid #D9DBE0;
      font-weight: 400;
      font-size: 32rpx;
      color: #1D2029;
      text-align: center;
      line-height: 88rpx;
    }
  }
  .bottomClearBtn{
    width: 684rpx;
    height: 88rpx;
    background: #FFFFFF;
    border-radius: 44rpx;
    border: 1rpx solid #D9DBE0;
    font-weight: 400;
    font-size: 32rpx;
    color: #4E5569;
    text-align: center;
    line-height: 88rpx;
    margin: 32rpx auto;
  }
.buttonMap {
  display: flex;
  text-align: center;
  margin-top: 32rpx;
  justify-content: space-between;
  background: white;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 0 32rpx 68rpx;
  box-sizing: border-box;
  flex-wrap: wrap;
  z-index: 3;
  .agreement{
    transform: translateX(-32rpx);
    margin-bottom: 5px;
  }
  .Detail {
    width: 100%;
    height: 90rpx;
    background: #ffffff;
    border-radius: 0rpx 0rpx 0rpx 0rpx;
    box-sizing: border-box;
    align-items: center;
    display: flex;
    justify-content: space-between;
    position: relative;
    margin-bottom: 24rpx;
    .pays {
      font-weight: 500;
      font-size: 36rpx;
      color: #ff5500;
    }
    .showBtnUpDown{
      .right_arrow{
        transform: rotate(270deg) !important;
      }
    }
    .showBtn {
      font-weight: 500;
      font-size: 24rpx;
      color: #777777;
      display: flex;
      align-items: center;
      .right_arrow {
        transition: all 0.1s ease-out;
        width: 32rpx;
        height: 32rpx;
        margin-left: 8rpx;
        transform: rotate(90deg);
      }
    }
    .line {
      background-color: #e6e6e6;
      position: absolute;
      bottom: -1rpx;
      left: -32rpx;
      height: 2rpx;
      width: 100vw;
    }
  }
  .clearBtn {
    width: 196rpx;
    height: 88rpx;
    background: #ffffff;
    border-radius: 44rpx;
    border: 1rpx solid #d9dbe0;
    font-weight: 400;
    font-size: 32rpx;
    line-height: 88rpx;
    color: #1d2029;
  }
  .inLinePayBtn {
    flex: 1;
    height: 88rpx;
    line-height: 88rpx;
    background: #00b484;
    border-radius: 44rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #ffffff;
  }
}
.guidanceTitle {
  font-weight: 500;
  font-size: 36rpx;
  color: #1d2029;
}
.guidanceTwoTitle {
  font-weight: 400;
  font-size: 26rpx;
  color: #868c9c;
}
.guidanceTwoTitle {
  width: 686rpx;
  height: 60rpx;
  line-height: 60rpx;
  background: #ffeee6;
  border-radius: 8rpx;
  padding: 0 24rpx;
  font-weight: 500;
  font-size: 26rpx;
  color: #ff5500;
  box-sizing: border-box;
  margin-top: 12rpx;
  border: 1rpx solid #f7d4c4;
}
.underInfo{
  .underInfoHeader{
    display: flex;
    align-items: center;
    justify-content: space-between;
    .filled{
      display: flex;
      align-items: center;
      font-weight: 400;
      font-size: 26rpx;
      color: #777777;
      .right_arrow{
        width: 32rpx;
        height: 32rpx;
      }
    }
  }
  .underInfoTitle{
    font-weight: 600;
    font-size: 32rpx;
    color: #1D2029;
  }
  .underInfoBox{
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 400;
    font-size: 26rpx;
    color: #1D2029;
    margin-top: 24rpx;
    .changeInfo{
      display: flex;
      align-items: center;
    }
    .entering{
      width: 28rpx;
      height: 28rpx;
      margin-left: 4rpx;
    }
  }
}
</style>
