.date-selector {
  width: 100%;
  font-size: 12px;
  color: #333;
}
.isSelectFormBox{
  width: 100%;
}

.select-date-placeholder,.select-date-value{
  display: inline;
}


.select-date-wrapper {
  margin-bottom: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.select-date {
  padding: 10px;
  flex: 1;
  border-radius: 2px;
  border: 1px solid rgba(6, 7, 46, 0.05);
  font-size: 12px;
}

.isSelectForm{
  height: 80rpx;
  line-height: 80rpx;
  font-size: 30rpx;
  color: #1D2029;
  text-align: left;
  display: inline-block;
  vertical-align: middle;
  width: calc(100% - 64rpx);
  border-bottom: 1rpx solid #f5f5f5;
  border-radius: 10rpx;
  padding: 0 20rpx;
}

.select-date.active {
  border-color: #6a7bff;
}
.isSelectForm.active{
  border-color:#f5f5f5
}

.select-date-placeholder {
  color: rgba(6, 7, 46, 0.3);
}

.btn-group {
  display: flex;
  margin: 48rpx 0;
  justify-content: space-between;
}

.btn-confirm {
      /* width: 180px; */
      height: 80rpx;
      /* line-height: 40px; */
      background: $topicC;
      border-radius: 4px;
      font-size: 14px;
      color: #fff;
      text-align: center;
      flex: 1;
      margin-left: 10rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
}

.btn-cancel {
      /* width: 144px; */
      height: 80rpx;
      /* line-height: 40px; */
      text-align: center;
      background: #fff;
      border-radius: 4px;
      border: 1px solid #979797;
      font-size: 14px;
      color: #06072e;
      flex: 1;
      margin-right: 10rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      align-items: center;
      box-sizing: border-box;
}
