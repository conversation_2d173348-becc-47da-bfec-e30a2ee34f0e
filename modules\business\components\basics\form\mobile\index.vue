<!-- 密码表单组件封装  -->
<template>
  <view>
    <view class="mobile-main">
      <view class="border main-r" :class="inputPhone ? 'bdb-1-ccc' : 'bdb-1-topic-c'">
        <input
          type="text"
          v-model.trim="mobile"
          :placeholder="config.placeholder"
          placeholder-class="f-w-medium"
          @focus="phoneFocus($event, 'inputPhone')"
          @blur="phoneBlur($event, 'inputPhone')"/>
        <view class="icon" v-if="mobile !== ''" @click="deletePwd()">
          <image style="display: block" class="width-height-atuo" :src="$static_ctx + 'image/system/logo/icon-backone.png'"/>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    value: {
      type: String,
      required: false,
      default: ''
    },
    config: {
      type: Object,
      default: function() {
        return {
          name: 'mobile', // 对应的是父组件 data 的密码变量名字
          placeholder: '请输入手机号码'
        }
      }
    }
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $static_ctx: this.$static_ctx,
      mobile: '',
      inputPhone: true
    }
  },
  watch: {
    value: {
      handler(val) {
        this.mobile = val
      },
      deep: true
    },
    /** 监听手机输入 */
    mobile() {
      this.mobile = this.$validate.trim(this.mobile)
      const obj = {}
      obj[this.config.name] = this.mobile
      this.$emit('update', obj)
    }
  },
  created() {},
  methods: {
    /** 失去焦点后触发校验 */
    onBlurPWD() {},

    deletePwd() {
      if (this.mobile) {
        this.mobile = ''
      }
    },
    phoneFocus(e, type) {
      this[type] = !this[type]
    },
    phoneBlur(e, type) {
      this[type] = !this[type]
    }
  }
}
</script>

<style lang="scss" scoped>
  .width-height-atuo{
    width: 100%;
    height: 100%;
  }
  .mobile-main{
    height: 56upx;
    position: relative;
    .icon{
      position: absolute;
      right: 0;
      top: 6upx;
      height: 40upx;
      width: 40upx;
      z-index: 2;
    }

    .main-r{
      width: 100%;
      display: inline-block;
      vertical-align: middle;
      input{
        font-size: 32upx !important;
      }
    }
  }
  .width-height-atuo{
    width: 100%;
    height: 100%;
  }
  .bdb-1-topic-c {
    border-bottom: 2upx solid $topicC;
  }
  .bdb-1-ccc {
    border-bottom: 2upx solid #ccc;
  }
  .f-w-medium{
    color: #bfbfbf;
    font-size: 32upx;
    line-height: 48upx;
    font-weight: Medium;
  }
</style>
