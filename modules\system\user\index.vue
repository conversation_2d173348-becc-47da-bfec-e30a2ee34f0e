<template>
  <page>
    <view slot="content" class="main-body">
      <view class="content-main">
        <!-- <view class="header">
          <text class="l-l">头像</text>
          <yq-avatar
            class="avatar"
            :disabled="!isUpdate"
            :isFullScreen="true"
            selWidth="500upx"
            selHeight="500upx"
            @upload="myUpload"
            :avatarSrc="regForm.headPic?regForm.headPic:this.$static_ctx + 'image/system/avatar/icon-default-avatar.png'"
            avatarStyle="width: 80upx; height: 80upx; border-radius: 100%;">
          </yq-avatar>
        </view>
        <view>
          <title-input title="昵称" type="text" :maxlength="20" :isRequired="true" :disabled="!isUpdate" :initialValue="regForm.nickName" placeholder="请输入" @returnFn="nameFn"></title-input>
          <view class="form-item">
            <title-selector v-model="regForm.sex" :isRequired="true" :disabled="!isUpdate" :config="template.sex" @updateForm="updateForm"></title-selector>
          </view>
        </view> -->
        <view class="user-profile">
          <yq-avatar
            class="avatar"
            :disabled="!isUpdate"
            :isFullScreen="true"
            selWidth="500upx"
            selHeight="500upx"
            @upload="myUpload"
            :avatarSrc="regForm.headPic?regForm.headPic:this.$static_ctx + 'image/system/avatar/icon-default-avatar.png'"
            avatarStyle="width: 144upx; height: 144upx; border-radius: 100%;">
          </yq-avatar>
        </view>
        <view class="user-name">
          <view class="user-l">昵称<span class="user-red">*</span></view>
          <input type="text" v-model="regForm.nickName" placeholder="请输入" :maxlength="20">
        </view>
        <view class="user-gender">
          <picker mode="selector" :value="initialIndex" :range="frequencyOptions" @change="onChange">
            <view class="picker">
                <view class="picker-l">性别<span class="user-red">*</span></view>
                <view class="picker-r">
                  <view>{{ !regForm.sex ? '请选择' : frequencyOptions[initialIndex] }}</view>
                </view>
            </view>
          </picker>
        </view>
        <view class="user-introduce" @click="handleClickUserIntroduce">
          <view class="user-introduce-l">个人介绍</view>
          <view class="user-introduce-r" v-if="regForm.intro">{{ regForm.intro }}</view>
          <view class="user-introduce-r" v-else>点击这里，填写简介</view>
        </view>
      </view>
      <view class="btn-wrapper">
        <view class="btn-bg" @tap="onSubmit()">{{ isUpdate ? '保存':'编辑'}}</view>
      </view>
    </view>
  </page>
</template>
<script>
import {mapState} from 'vuex'
// import TitleInput from '@/components/business/content-title/title-input'
// import TitleSelector from "@/components/business/module/v1/title-selector/index"

import YqAvatar from '@/modules/system/components/yq-avatar/yq-avatar.vue'
import common from '@/common/util/main'

export default {
  components: {
    // TitleInput,
    YqAvatar,
    // TitleSelector,
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      $validate: this.$validate,
      isUpdate: true,
      regForm: {
        birthday: this.$common.formatDate(new Date(), 'yyyy-MM-dd'),
        headPic: '',
        avatar: '',
        sex: ''
      },
      // template: {
      //   sex: {
      //     label: '性别',
      //     name: 'sex',
      //     placeholder: '请选择性别',
      //     required: true,
      //     nextRequest: true,
      //     array: [
      //       { label: '男', value: 1 },
      //       { label: '女', value: 2 }
      //     ],
      //     dicKey: 'sex'
      //   }
      // },
      frequencyOptions:['男','女'],
      initialIndex:0,
      isUserIntroduce:false
    }
  },
  onLoad() {
    this.getDetail()
  },
  onShow(){
    if(this.isUserIntroduce){
      this.$nextTick(()=>{
        this.getDetail()
      })
    }
  },
  computed: {
    ...mapState('user', {
      fansRecord: state => state.fansRecord,
      accountId: state => state.accountId
    })
  },
  methods: {
    handleClickUserIntroduce(){
      this.isUserIntroduce = true
      this.$navto.push('UserIntroduce',{nickName:this.regForm.nickName,headPic: this.regForm.headPath,headPath: this.regForm.headPath,sex:this.regForm.sex,intro:this.regForm.intro})
    },
    // isEncoded(url){
    //   let str = url
    //   if(!(url.startsWith('https://') || url.startsWith('http://'))){
    //     str = this.file_ctx + url
    //   }
    //   return str
    // },

    onChange(e){
      this.initialIndex = Number(e.detail.value)
      this.regForm.sex = Number(e.detail.value) + 1
    },
    // nameFn(v) {
    //   this.regForm.nickName = v
    // },
    // emailFn(v) {
    //   this.regForm.email = v
    // },

    myUpload(rsp) {
      const that = this
      // rsp.path // 更新头像方式一
      // rsp.avatar.imgSrc = rsp.path; //更新头像方式二
      const uploadTask = uni.uploadFile({
        url: this.$constant.noun.uploadInterface,
        filePath: rsp.path,
        name: 'file',
        header: common.getUploadFileHeader(),
        formData: { // HTTP 请求中其他额外的 form data
          groupId: "26000",
          relId: ''
        },
        success: (uploadFileRes) => {
          console.log('uploadFileRes-----------------', uploadFileRes, JSON.parse(uploadFileRes.data))
          const returnData = JSON.parse(uploadFileRes.data).data
          if (JSON.stringify(returnData) !== '[]') {
            this.regForm.headPath = returnData[0].dir
            this.regForm.headPic = this.file_ctx + returnData[0].dir
            this.regForm = Object.assign({}, this.regForm)
          }
        }
      })
      uploadTask.onProgressUpdate((res) => {
        // console.log('上传进度' + res.progress);
        // console.log('已经上传的数据长度' + res.totalBytesSent);
        // console.log('预期需要上传的数据总长度' + res.totalBytesExpectedToSend);
        // console.log(res)
      })
    },
    returnFn(e) {
      this.index = e.target.value
    },
    updateForm(obj) {
      obj.key = obj.key.trim()
      this.regForm[obj.key] = obj.value
    },
    birthdayFn(v) {
      this.regForm.birthday = v
    },
    onSubmit() {
      const that = this
      if (!that.isUpdate) {
        this.$uniPlugin.loading('开启编辑')
        this.isUpdate = true
        setTimeout(() => {
          that.$uniPlugin.hideLoading()
        }, 500)
        return
      }
      const tipArr = {
        nickName: '请输入姓名',
        // email: '请输入邮箱',
        // birthday: '请选择生日'
      }
      const params = {
        nickName: that.regForm.nickName, // 姓名
        // email: that.regForm.email, // 邮箱
        // birthday: that.regForm.birthday // 生日
      }
      // 表单验证
      if (!that.$common.validationForm(tipArr, params)) {
        return
      }
      // const regEmail = /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
      // if (!regEmail.test(that.regForm.email)) {
      //   that.$uniPlugin.toast('邮箱格式不正确')
      //   return
      // }
      that.$uniPlugin.loading('保存中', true)
      // if (that.regForm.headPic.indexOf('icon-default-avatar.png') > -1) {
        //   that.regForm.headPic = ''
      // }
      that.$api.community.fansrecordInfoUpdate({...that.regForm,accountId: this.accountId }).then(res => {
        that.$uniPlugin.hideLoading()
        this.$uniPlugin.toast(res.msg)
        that.$ext.user.getCommunityFansRecord().then(() => {
          setTimeout(() => {
            that.getDetail()
            that.$navto.back(1)
          }, that.$constant.noun.delayedOperationTime)
        })
      }).catch(() => {
        that.$uniPlugin.hideLoading()
      })
    },
    getDetail() {
      const that = this
      // that.$uniPlugin.loading('加载中', true)
      // that.$api.common.sysuserBasicsinfo().then(res => {
      //   that.$uniPlugin.hideLoading()
      //   that.isUpdate = false
      //   that.regForm = res
      //   // that.regForm.headPic = ''
      //   // that.regForm.headPic = res.headPic ? res.headPic : that.$static_ctx + 'image/system/avatar/icon-default-avatar.png'
      // })
      const data = that.$validate.isNull(that.fansRecord) ? {} : JSON.parse(JSON.stringify(that.fansRecord))
      console.log('data-----------------', data)
      that.regForm = data
      this.initialIndex = (data.sex && Number(data.sex) - 1) ?? ''
      that.regForm.headPic = data.headPath ? that.file_ctx + data.headPath : that.$static_ctx + 'image/system/avatar/icon-default-avatar.png'
    }
  }

}
</script>

<style lang="scss" scoped>
.main-body{
  position: relative;
  height: calc(100vh - 28rpx);
  padding: 28rpx 32rpx 0;
}
.content-main{
  padding: 32rpx 24rpx 40rpx;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  background-color: #fff;
  // .header{
  //   background: #fff;
  //   border-bottom: 1px solid #f5f5f5;
  //   height: 80upx;
  //   padding: 20upx 30upx;
  //   margin-top: 10upx;
  //   .l-l{
  //     float: left;
  //     line-height: 80upx;
  //     height: 80upx;
  //     font-size: 32upx;
  //   }
  //   .avatar{
  //     height: 80upx;
  //     width: 80upx;
  //     overflow: hidden;
  //     float: right;
  //     @include rounded(50%);
  //   }
  // }
  .user-red{
    color:#FF5500;
  }
  .user-profile{
    display: flex;
    align-self: center;
    justify-content: center;
  }
  .user-name,.user-gender,.user-introduce{
    display: flex;
    align-items: center;
    font-size: 28rpx;
    color: #777777;
    height: 104rpx;
    border-bottom: 2rpx solid #E6E6E6;
    .user-l,.user-introduce-l{
      display: flex;
      align-items: center;
      width: 174rpx;
    }
    .user-introduce-r{
      width: 400rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .user-gender{
    .picker{
      display: flex;
      .picker-l{
        width: 174rpx;
      }
    }
  }
}
  // .name{
  //   margin-left: 464upx;
  //   color: #999999;
  //   font-size: 30upx;
  // }
  // .form-item {
  //   padding: 0 30rpx;
  //   background-color: #fff;
  // }
  .btn-wrapper{
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 156rpx;
    padding-top: 24rpx;
    background: #FFFFFF;
    box-shadow: 0rpx -1rpx 0rpx 0rpx #EAEBF0;
    border-radius: 0rpx 0rpx 0rpx 0rpx;
    .btn-bg{
      display: flex;
      align-self: center;
      justify-content: center;
      font-weight: 500;
      font-size: 32rpx;
      color: #FFFFFF;
      width: 646rpx;
      height: 88rpx;
      background: #00B484;
      border-radius: 44rpx 44rpx 44rpx 44rpx;
    }
  }
</style>
