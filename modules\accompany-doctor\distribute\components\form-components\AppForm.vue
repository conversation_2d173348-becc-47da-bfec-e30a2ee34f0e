<template>
  <view class="form-component" :class="{'personal-form': value.distributionType === '个人'}">
    <view class="form-fields">
      <template v-for="(field, index) in formFields">
        <!-- 文本输入字段 -->
        <view v-if="field.type === 'input'" :key="index" class="botTabLine">
          <view class="lineTitle"><span v-if="field.required" class="required">*</span>{{field.label}}</view>
          <input
            :type="field.inputType || 'text'"
            :placeholder="field.placeholder"
            class="bookInput"
            :value="value[field.name]"
            @input="handleFormInput"
            :data-field-name="field.name"
            :fixed="true"
            :adjust-position="false"
            :hold-keyboard="true"
          />
        </view>

        <!-- 形象照上传 -->
        <view v-else-if="field.type === 'image'" :key="index" class="botTabLine avatar-line">
          <view class="lineTitle"><span v-if="field.required" class="required">*</span>{{field.label}}</view>
          <view class="upload-box" @click="handleImageUpload" :data-field-name="field.name">
            <view v-if="value[field.name]" class="preview-container">
              <image v-if="findAttachmentByType(getAttTypeByFileKey(field.name))" class="preview-image"
                :src="getImageUrlFromAttachment(findAttachmentByType(getAttTypeByFileKey(field.name)))"
                mode="aspectFill"/>
              <image v-else class="preview-image" :src="getImageUrl(value[field.name])" mode="aspectFill"/>
              <view class="delete-icon" @click.stop="handleImageRemove" :data-field-name="field.name">×</view>
            </view>
            <view v-else class="empty-upload">
              <view class="plus-icon"></view>
            </view>
          </view>
        </view>

        <!-- 营业执照上传 -->
        <view v-else-if="field.type === 'license'" :key="index" class="botTabLine avatar-line">
          <view class="lineTitle"><span v-if="field.required" class="required">*</span>{{field.label}}</view>
          <view class="upload-box" @click="handleImageUpload" :data-field-name="field.name">
            <view v-if="value[field.name]" class="preview-container">
              <image v-if="findAttachmentByType(getAttTypeByFileKey(field.name))" class="preview-image"
                :src="getImageUrlFromAttachment(findAttachmentByType(getAttTypeByFileKey(field.name)))"
                mode="aspectFill"/>
              <image v-else class="preview-image" :src="getImageUrl(value[field.name])" mode="aspectFill"/>
              <view class="delete-icon" @click.stop="handleImageRemove" :data-field-name="field.name">×</view>
            </view>
            <view v-else class="empty-upload">
              <view class="plus-icon"></view>
            </view>
          </view>
        </view>

        <!-- 商户门头照上传 -->
        <view v-else-if="field.type === 'merchantphoto'" :key="index" class="botTabLine avatar-line">
          <view class="lineTitle"><span v-if="field.required" class="required">*</span>{{field.label}}</view>
          <view class="upload-box" @click="handleImageUpload" :data-field-name="field.name">
            <view v-if="value[field.name]" class="preview-container">
              <image v-if="findAttachmentByType(getAttTypeByFileKey(field.name))" class="preview-image"
                :src="getImageUrlFromAttachment(findAttachmentByType(getAttTypeByFileKey(field.name)))"
                mode="aspectFill"/>
              <image v-else class="preview-image" :src="getImageUrl(value[field.name])" mode="aspectFill"/>
              <view class="delete-icon" @click.stop="handleImageRemove" :data-field-name="field.name">×</view>
            </view>
            <view v-else class="empty-upload">
              <view class="plus-icon"></view>
            </view>
          </view>
        </view>

        <!-- 商铺内部照片上传 -->
        <view v-else-if="field.type === 'shopphoto'" :key="index" class="botTabLine avatar-line">
          <view class="lineTitle"><span v-if="field.required" class="required">*</span>{{field.label}}</view>
          <view class="upload-box" @click="handleImageUpload" :data-field-name="field.name">
            <view v-if="value[field.name]" class="preview-container">
              <image v-if="findAttachmentByType(getAttTypeByFileKey(field.name))" class="preview-image"
                :src="getImageUrlFromAttachment(findAttachmentByType(getAttTypeByFileKey(field.name)))"
                mode="aspectFill"/>
              <image v-else class="preview-image" :src="getImageUrl(value[field.name])" mode="aspectFill"/>
              <view class="delete-icon" @click.stop="handleImageRemove" :data-field-name="field.name">×</view>
            </view>
            <view v-else class="empty-upload">
              <view class="plus-icon"></view>
            </view>
          </view>
        </view>

        <!-- 选择器字段 -->
        <picker v-else-if="field.type === 'picker' && field.mode === 'selector'" :key="index" mode="selector" :range="field.range" @change="handleSelectorPickerChange" :data-field-name="field.name" :data-field-range="field.range">
          <view class="botTabLine">
            <view class="lineTitle"><span v-if="field.required" class="required">*</span>{{field.label}}</view>
            <view class="lineValue">{{value[field.name] || field.placeholder}}</view>
            <image class="lineIcon" :src="iconRightArrow || ''" mode="aspectFit"></image>
          </view>
        </picker>

        <!-- 地区选择器 -->
        <picker
          v-else-if="field.type === 'picker' && field.mode === 'region'"
          :key="index"
          mode="region"
          @change="handleRegionPickerChange"
          :data-field="JSON.stringify(field)"
          level="district">
          <view class="botTabLine">
            <view class="lineTitle"><span v-if="field.required" class="required">*</span>{{field.label}}</view>
            <view class="lineValue">
              <text v-if="getRegionDisplayValue(field)">{{getRegionDisplayValue(field)}}</text>
              <text v-else class="placeholder">{{field.placeholder}}</text>
            </view>
            <image class="lineIcon" :src="iconRightArrow || ''" mode="aspectFit"></image>
          </view>
        </picker>

        <!-- 身份证上传区域 -->
        <view v-else-if="field.type === 'idcard'" :key="index" class="additionalContent">
          <view class="lineTitle"><span v-if="field.required" class="required">*</span>{{field.label}}</view>
          <view class="idcard-upload-container">
            <!-- 人像面 -->
            <view class="idcard-item" v-if="value[field.frontField]" @click="handleIdCardImageClick" :data-field-name="field.frontField">
              <view class="idcard-delete" @click.stop="handleIdCardImageRemove" :data-field-name="field.frontField">×</view>
              <!-- 查找表单中对应身份证的fileAttachments，获取attachStorePath -->
              <image v-if="findAttachmentByType(getAttTypeByFileKey(field.frontField))" class="idcard-image"
                :src="getImageUrlFromAttachment(findAttachmentByType(getAttTypeByFileKey(field.frontField)))"></image>
              <image v-else class="idcard-image" :src="getImageUrl(value[field.frontField])"></image>
            </view>
            <view class="idcard-item empty-idcard" v-else @click="handleIdCardImageClick" :data-field-name="field.frontField">
              <image class="idcard-icon" :src="field.frontIcon || IDCardFace" mode="aspectFit"></image>
              <view class="idcard-text">{{field.frontText || '人像面'}}</view>
            </view>

            <!-- 国徽面 -->
            <view class="idcard-item" v-if="value[field.backField]" @click="handleIdCardImageClick" :data-field-name="field.backField">
              <view class="idcard-delete" @click.stop="handleIdCardImageRemove" :data-field-name="field.backField">×</view>
              <!-- 查找表单中对应身份证的fileAttachments，获取attachStorePath -->
              <image v-if="findAttachmentByType(getAttTypeByFileKey(field.backField))" class="idcard-image"
                :src="getImageUrlFromAttachment(findAttachmentByType(getAttTypeByFileKey(field.backField)))"></image>
              <image v-else class="idcard-image" :src="getImageUrl(value[field.backField])"></image>
            </view>
            <view class="idcard-item empty-idcard" v-else @click="handleIdCardImageClick" :data-field-name="field.backField">
              <image class="idcard-icon" :src="field.backIcon || IDCardNationalEmblemFace" mode="aspectFit"></image>
              <view class="idcard-text">{{field.backText || '国徽面'}}</view>
            </view>
          </view>
        </view>

        <!-- 文本域 -->
        <view v-else-if="field.type === 'textarea'" :key="index" class="additionalContent">
          <view class="lineTitle"><span v-if="field.required" class="required">*</span>{{field.label}}</view>
          <textarea
            class="experience-input"
            :placeholder="field.placeholder"
            :value="value[field.name]"
            @input="handleEnhancedTextareaInput"
            :data-field-name="field.name"
            :data-maxlength="field.maxlength"
            :maxlength="10000"
          ></textarea>
          <text v-if="field.showCounter" class="word-counter" :class="{'word-counter-exceed': (value[field.name] || '').length > field.maxlength}">
            {{ (value[field.name] || '').length }}/{{ field.maxlength }}
          </text>
        </view>

        <!-- 开关 -->
        <view v-else-if="field.type === 'switch'" :key="index" class="botTabLine switch-line">
          <view class="lineTitle"><span v-if="field.required" class="required">*</span>{{field.label}}</view>
          <view class="switch-wrapper">
            <switch
              :checked="value[field.name]"
              :color="field.color || '#07C160'"
              @change="handleSwitchInput"
              :data-field-name="field.name"
              class="custom-switch"
            />
          </view>
        </view>

        <!-- 自定义插槽 -->
        <view v-else-if="field.type === 'slot'" :key="index" class="custom-slot-container">
          <view class="lineTitle" v-if="field.label"><span v-if="field.required" class="required">*</span>{{field.label}}</view>
          <!-- 这里直接使用具名插槽，小程序环境下嵌套插槽可能存在兼容性问题 -->
          <slot :name="field.slotName || 'default'"></slot>
        </view>
      </template>
    </view>

    <!-- 底部按钮和协议区域 -->
    <view v-if="showBottomArea" class="bottomBtn">
      <slot name="btnBox">
        <view v-if="agreementText" class="agreement-row" @click="toggleAgreement">
          <view class="custom-checkbox">
            <view
              class="checkbox-icon"
              :class="{ checked: isAgreed }"
            ></view>
          </view>
          <text class="agreement-text">我已阅读并同意</text>
          <text class="agreement-link" @tap.stop="handleAgreementClick">{{agreementText}}</text>
        </view>
      </slot>
      <view class="btn-group">
        <view v-if="showResetButton" class="resetBtn" @click="resetForm">{{resetText}}</view>
        <view class="comBtn" @click="submitForm">{{submitText}}</view>
      </view>
    </view>

    <!-- 全局隐藏上传组件 -->
    <view class="lineHide">
      <title-img :config="{}" ref="avatarUploader" @returnFn="avatarReturnFn" :cData="[]"></title-img>
    </view>
    <view class="lineHide">
      <title-img :config="{}" ref="licenseUploader" @returnFn="licenseReturnFn" :cData="[]"></title-img>
    </view>
    <view class="lineHide">
      <title-img :config="{}" ref="idCardFrontUploader" @returnFn="idCardFrontReturnFn" :cData="[]"></title-img>
    </view>
    <view class="lineHide">
      <title-img :config="{}" ref="idCardBackUploader" @returnFn="idCardBackReturnFn" :cData="[]"></title-img>
    </view>
    <view class="lineHide">
      <title-img :config="{}" ref="merchantPhotoUploader" @returnFn="merchantPhotoReturnFn" :cData="[]"></title-img>
    </view>
    <view class="lineHide">
      <title-img :config="{}" ref="shopPhotoUploader" @returnFn="shopPhotoReturnFn" :cData="[]"></title-img>
    </view>

    <slot></slot>
  </view>
</template>

<script>
import TitleImg from "@/components/business/module/title-img/index.vue";

export default {
  name: 'AppForm',
  components: {
    TitleImg
  },
  props: {
    // 单一字段模式属性
    type: {
      type: String,
      default: 'text', // text, select, idCard, textarea
      validator: (value) => ['text', 'select', 'idCard', 'textarea'].includes(value)
    },
    label: {
      type: String,
      default: ''
    },
    value: {
      type: [String, Object, Number, Array],
      default: () => ({})
    },
    placeholder: {
      type: String,
      default: '请输入'
    },
    required: {
      type: Boolean,
      default: false
    },
    inputType: {
      type: String,
      default: 'text' // text, number等
    },
    // 图片相关属性
    isAvatar: {
      type: Boolean,
      default: false
    },
    // 下拉选择相关
    isSelect: {
      type: Boolean,
      default: false
    },
    // 地区选择器
    isRegionSelect: {
      type: Boolean,
      default: false
    },
    // 选择器相关配置
    options: {
      type: Array,
      default: () => []
    },
    pickerMode: {
      type: String,
      default: 'selector' // selector, multiSelector等
    },
    // 文本域相关
    isTextarea: {
      type: Boolean,
      default: false
    },
    maxlength: {
      type: Number,
      default: 200
    },
    showCounter: {
      type: Boolean,
      default: true
    },
    // 身份证相关
    isIdCard: {
      type: Boolean,
      default: false
    },
    frontValue: {
      type: [String, Object],
      default: ''
    },
    backValue: {
      type: [String, Object],
      default: ''
    },
    // 上传器配置
    uploaderConfig: {
      type: Object,
      default: () => ({})
    },
    // 上传图片数据
    uploaderData: {
      type: Array,
      default: () => []
    },
    // 身份证正面图片数据
    frontUploaderData: {
      type: Array,
      default: () => []
    },
    // 身份证背面图片数据
    backUploaderData: {
      type: Array,
      default: () => []
    },

    // 表单字段配置（新增）
    formFields: {
      type: Array,
      default: () => []
    },
    // 表单验证规则
    validRules: {
      type: Object,
      default: () => ({})
    },
    // 静态资源路径
    staticCtx: {
      type: String,
      default: ''
    },
    // 协议文本
    agreementText: {
      type: String,
      default: ''
    },
    // 提交按钮文本
    submitText: {
      type: String,
      default: '提交'
    },
    // 是否显示重置按钮
    showResetButton: {
      type: Boolean,
      default: false
    },
    // 重置按钮文本
    resetText: {
      type: String,
      default: '重置'
    },
    // 是否显示底部按钮区域
    showBottomArea: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      iconRightArrow: '',
      iconPostMenuClose: '',
      IDCardFace: '',
      IDCardNationalEmblemFace: '',
      isAgreed: false, // 是否同意协议
      configData: {
        appid: "",
        orgCode: ""
      }
    }
  },
  created() {
    // 在created生命周期中安全地加载图片资源
    try {
      this.iconRightArrow = (this.staticCtx || this.$static_ctx) + "image/business/hulu-v2/icon-right-arrow.png";
      this.iconPostMenuClose = (this.staticCtx || this.$static_ctx) + "image/business/hulu-v2/icon-close.png";
      this.IDCardFace = (this.staticCtx || this.$static_ctx) + "image/business/hulu-v2/IDCardFace.png";
      this.IDCardNationalEmblemFace = (this.staticCtx || this.$static_ctx) + "image/business/hulu-v2/IDCardNationalEmblemFace.png";
    } catch (e) {
      console.error('加载图片资源失败:', e);
    }

    // 加载配置信息
    this.loadConfigData();
  },
  computed: {
    countLength() {
      return this.value ? String(this.value).length : 0;
    }
  },
  methods: {
    // 加载配置信息
    async loadConfigData() {
      try {
        const response = await this.$api.distribution.queryConfig();
        if (response && response.code === 0 && response.data) {
          this.configData.appid = response.data.defaultAppid || "";
          this.configData.orgCode = response.data.defaultOrgCode || "";
          console.log('配置信息加载成功:', this.configData);
        } else {
          console.error('配置信息加载失败:', response);
        }
      } catch (error) {
        console.error('获取配置信息出错:', error);
      }
    },
    // 获取文件类型映射
    getAttTypeByFileKey(fileKey) {
      // 根据图片中的对照表映射文件类型
      const fileTypeMap = {
        CardFace: this.value.distributionType === '企业' ? 'FR_ID_CARD_FRONT' : 'ID_CARD_FRONT', // 身份证正面
        CardNationalEmblemFace: this.value.distributionType === '企业' ? 'FR_ID_CARD_BEHIND' : 'ID_CARD_BEHIND', // 身份证反面
        businessLicense: 'BUSINESS_LICENCE', // 营业执照
        merchantPhoto: 'MERCHANT_PHOTO', // 商户门头照
        shopInnerPhoto: 'SHOPINNER', // 商铺内部照片
        openAccountProve: 'OTHERS', // 开户凭证
      };

      return fileTypeMap[fileKey] || 'OTHERS';
    },

    // 根据附件类型获取附件名称
    getAttNameByFileKey(fileKey) {
      const typeNameMap = {
        'businessLicense': '营业执照',
        'CardFace': this.value.distributionType === '企业' ? '法人身份证正面' : '身份证正面',
        'CardNationalEmblemFace': this.value.distributionType === '企业' ? '法人身份证反面' : '身份证反面',
        'merchantPhoto': '商户门头照',
        'shopInnerPhoto': '商铺内部照片',
        'openAccountProve': '开户许可证照片'
      };

      return typeNameMap[fileKey] || '其他附件';
    },

    // 从fileAttachments中删除指定类型的附件
    removeAttachmentByType(attachType) {
      if (!this.value.fileAttachments || !Array.isArray(this.value.fileAttachments)) {
        return;
      }

      console.log('删除前附件列表:', this.value.fileAttachments);

      // 深拷贝附件列表避免引用问题
      const copyAttachments = JSON.parse(JSON.stringify(this.value.fileAttachments));

      // 过滤掉指定类型的附件
      const updatedAttachments = copyAttachments.filter(item => {
        return item.attachType !== attachType;
      });

      // 更新附件列表并确保强制刷新
      this.$nextTick(() => {
        this.emitInputEvent('fileAttachments', updatedAttachments);
        console.log('删除后附件列表:', updatedAttachments);
      });
    },

    // 处理图片点击
    handleImageClick(fieldName) {
      console.log('图片点击:', fieldName);

      // 如果已经有图片，则预览图片
      if (this.value[fieldName]) {
        // 创建临时数组用于预览
        const previewUrls = [this.getImageUrl(this.value[fieldName])];
        uni.previewImage({
          urls: previewUrls,
          current: 0
        });
        return;
      }

      // 否则根据不同的字段类型触发相应的上传器
      if (fieldName === 'businessLicense') {
        this.$refs.licenseUploader.uploadImage();
      } else if (fieldName === 'CardFace') {
        this.$refs.idCardFrontUploader.uploadImage();
      } else if (fieldName === 'CardNationalEmblemFace') {
        this.$refs.idCardBackUploader.uploadImage();
      } else if (fieldName === 'merchantPhoto') {
        this.$refs.merchantPhotoUploader.uploadImage();
      } else if (fieldName === 'shopInnerPhoto') {
        this.$refs.shopPhotoUploader.uploadImage();
      } else {
        // 其他普通图片字段
        this.$refs.avatarUploader.uploadImage();
      }
    },

    // 处理图片删除
    handleImageDelete(fieldName) {
      console.log('删除图片:', fieldName);

      // 确认删除
      uni.showModal({
        title: '提示',
        content: '确定要删除该图片吗？',
        success: (res) => {
          if (res.confirm) {
            // 获取该图片对应的附件类型
            const attachType = this.getAttTypeByFileKey(fieldName);

            // 先清空表单中该字段的值，确保Vue更新
            this.emitInputEvent(fieldName, '');

            // 延迟一帧再删除附件，避免Vue响应式更新冲突
            setTimeout(() => {
              // 从fileAttachments中删除对应类型的附件
              this.removeAttachmentByType(attachType);

              // 强制更新视图
              this.$forceUpdate();

              // 确保清除对应图片的缓存数据
              if (fieldName === 'businessLicense') {
                this.$refs.licenseUploader.imageList = [];
              } else if (fieldName === 'CardFace') {
                this.$refs.idCardFrontUploader.imageList = [];
              } else if (fieldName === 'CardNationalEmblemFace') {
                this.$refs.idCardBackUploader.imageList = [];
              } else if (fieldName === 'merchantPhoto') {
                this.$refs.merchantPhotoUploader.imageList = [];
              } else if (fieldName === 'shopInnerPhoto') {
                this.$refs.shopPhotoUploader.imageList = [];
              } else {
                this.$refs.avatarUploader.imageList = [];
              }

              uni.showToast({
                title: '删除成功',
                icon: 'success'
              });
            }, 0);
          }
        }
      });
    },

    // 上传文件到服务器
    async uploadFileToServer(fileInfo, fileType) {
      try {
        console.log('开始处理文件上传:', fileInfo, fileType);

        // 获取附件类型
        const attType = this.getAttTypeByFileKey(fileType);

        // 先获取文件的原始路径 - 这是本地上传返回的路径（第一次上传）
        const localFilePath = fileInfo.filePath || '';
        console.log('本地文件路径(originalFilePath):', localFilePath);

        // 获取文件URL
        const fileUrl = fileInfo.url || '';
        if (!fileUrl) {
          throw new Error('无效的文件URL');
        }

        // 下载文件获取临时路径
        console.log('正在下载文件:', fileUrl);
        const downloadResult = await new Promise((resolve, reject) => {
          uni.downloadFile({
            url: fileUrl,
            success: res => resolve(res),
            fail: err => reject(err)
          });
        });

        if (downloadResult.statusCode !== 200) {
          throw new Error('下载文件失败');
        }

        // 读取文件为base64
        console.log('正在读取文件为base64');
        const base64Result = await new Promise((resolve, reject) => {
          uni.getFileSystemManager().readFile({
            filePath: downloadResult.tempFilePath,
            encoding: 'base64',
            success: res => resolve(res.data),
            fail: err => reject(err)
          });
        });

        // 获取文件扩展名
        const fileExtName = fileInfo.extName || fileUrl.split('.').pop() || 'jpg';

        console.log(`准备上传文件 ${fileType}，类型: ${attType}`);

        // 上传文件
        const requestData = {
          appid: this.configData.appid,
          orgCode: this.configData.orgCode,
          attType: attType,
          attExtName: fileExtName,
          attContext: base64Result
        };

        console.log('使用的配置信息:', {
          appid: this.configData.appid,
          orgCode: this.configData.orgCode
        });

        // 如果配置还未加载完成，使用默认值
        if (!requestData.appid) {
          console.warn('使用默认appid，因为配置未加载完成');
          requestData.appid = "OP10000305";
        }

        if (!requestData.orgCode) {
          console.warn('使用默认orgCode，因为配置未加载完成');
          requestData.orgCode = "980271";
        }

        // 调用上传接口 - 这是第二次上传（到服务器）
        const response = await this.$api.distribution.uploadFile(requestData);

        if (response && response.code === 0) {
          console.log('文件上传成功:', response);

          // 获取服务器返回的文件路径信息 - 这应该存储到attachStorePath
          const serverFilePath = response.data?.respData?.attFileId;
          if (!serverFilePath) {
            throw new Error('未获取到上传文件路径');
          }
          console.log('服务器文件路径(attachStorePath):', serverFilePath);

          // 构建附件信息：区分本地路径和服务器路径
          const attachmentInfo = {
            attachType: attType,
            attachName: this.getAttNameByFileKey(fileType),
            attachStorePath: serverFilePath, // 服务器路径（第二次上传）
            originalFilePath: localFilePath  // 本地路径（第一次上传）
          };

          console.log('构建的附件信息:', attachmentInfo);

          // 从当前附件列表中删除相同类型的附件
          this.removeAttachmentByType(attType);

          // 等待一帧后添加新附件
          await new Promise(resolve => setTimeout(resolve, 0));

          // 确保fileAttachments是数组
          let currentAttachments = this.value.fileAttachments || [];
          if (!Array.isArray(currentAttachments)) {
            currentAttachments = [];
          }

          // 创建一个深拷贝的数组，避免引用问题
          let newAttachments = JSON.parse(JSON.stringify(currentAttachments));

          // 添加新附件
          newAttachments.push(attachmentInfo);
          console.log('更新后的附件列表:', newAttachments);

          // 更新附件列表
          this.emitInputEvent('fileAttachments', newAttachments);

          return attachmentInfo;
        } else {
          console.error('文件上传API响应错误:', response);
          throw new Error(response.msg || '上传失败');
        }
      } catch (error) {
        console.error('文件上传处理错误:', error);
        uni.showToast({
          title: '文件上传失败',
          icon: 'none'
        });
        return null;
      }
    },

    // 获取地区显示值
    getRegionDisplayValue(field) {
      // 优先使用displayField中存储的完整显示文本
      if (field.displayField && this.value[field.displayField]) {
        console.log('使用displayField显示:', this.value[field.displayField]);
        return this.value[field.displayField];
      }

      // 如果是直接保存了地区数组
      const regionArray = this.value[field.name];
      if (Array.isArray(regionArray) && regionArray.length > 0) {
        console.log('获取地区显示值-数组模式:', regionArray);
        return regionArray.join(' ');
      }

      // 如果是通过单独的字段保存省市区
      const province = this.value[field.provinceField];
      const city = this.value[field.cityField];
      const district = this.value[field.districtField];

      console.log('获取地区显示值-字段模式:', { province, city, district });

      if (province || city || district) {
        const regions = [];
        if (province) regions.push(province);
        if (city) regions.push(city);
        if (district) regions.push(district);
        console.log('构建的显示值:', regions.join(' '));
        return regions.join(' ');
      }

      console.log('没有找到地区数据');
      return '';
    },

    // 选择器变化
    handleSelectorPickerChange(e) {
      try {
        const fieldName = e.currentTarget.dataset.fieldName;
        const range = e.currentTarget.dataset.fieldRange || [];
        const index = e.detail.value;
        const selectedValue = range[index];

        console.log('选择器变化:', {
          fieldName,
          index,
          selectedValue,
          range
        });

        this.$emit('change', e);
        this.emitInputEvent(fieldName, selectedValue);
      } catch (error) {
        console.error('处理选择器数据失败:', error);
      }
    },

    // 地区选择器变化
    handleRegionPickerChange(e) {
      this.$emit('change', e);

      try {
        // 从dataset中获取field对象
        const field = JSON.parse(e.currentTarget.dataset.field);
        console.log('获取到的field对象:', field);

        console.log('地区选择原始数据:', e.detail);

        // uni-app的picker返回的数据结构可能不同，这里做兼容处理
        let provinceValue = '';
        let cityValue = '';
        let districtValue = '';

        // 尝试不同的数据结构解析
        if (e.detail && e.detail.value) {
          // 方式1：数组格式 ['广东省', '深圳市', '南山区']
          if (Array.isArray(e.detail.value)) {
            const values = e.detail.value;
            if (values.length >= 1) provinceValue = values[0];
            if (values.length >= 2) cityValue = values[1];
            if (values.length >= 3) districtValue = values[2];
          }
          // 方式2：可能是对象，如 {province:'广东省', city:'深圳市', area:'南山区'}
          else if (typeof e.detail.value === 'object') {
            provinceValue = e.detail.value.province || '';
            cityValue = e.detail.value.city || '';
            districtValue = e.detail.value.area || e.detail.value.district || '';
          }
          // 方式3：可能是逗号分隔的字符串 '广东省,深圳市,南山区'
          else if (typeof e.detail.value === 'string' && e.detail.value.includes(',')) {
            const parts = e.detail.value.split(',');
            if (parts.length >= 1) provinceValue = parts[0];
            if (parts.length >= 2) cityValue = parts[1];
            if (parts.length >= 3) districtValue = parts[2];
          }
        }

        console.log('解析后的地区数据:', {
          province: provinceValue,
          city: cityValue,
          district: districtValue
        });

        // 创建完整的显示文本
        const displayText = [provinceValue, cityValue, districtValue].filter(Boolean).join(' ');

        // 构建完整的地区数组
        const regionArray = [];
        if (provinceValue) regionArray.push(provinceValue);
        if (cityValue) regionArray.push(cityValue);
        if (districtValue) regionArray.push(districtValue);

        // 确保至少有省份信息
        if (regionArray.length > 0) {
          // 保存完整的地区数组
          this.emitInputEvent(field.name, regionArray);

          // 保存分开的省市区字段
          if (field.provinceField && provinceValue) {
            this.emitInputEvent(field.provinceField, provinceValue);
          }
          if (field.cityField && cityValue) {
            this.emitInputEvent(field.cityField, cityValue);
          }
          if (field.districtField && districtValue) {
            this.emitInputEvent(field.districtField, districtValue);
          }

          // 直接设置完整的显示文本到一个专用字段
          if (field.displayField) {
            this.emitInputEvent(field.displayField, displayText);
          }
        }
      } catch (error) {
        console.error('处理地区选择器数据失败:', error);
      }
    },

    // 开关变化
    handleSwitchChange(fieldName, e) {
      this.emitInputEvent(fieldName, e.detail.value);
    },

    // 安全获取图片URL
    getImageUrl(value) {
      if (!value) return '';
      try {
        // 获取文件服务器前缀
        let fileCtx = '';
        try {
          fileCtx = uni.getStorageSync('file_ctx') || this.file_ctx;
        } catch (e) {
          console.error('获取file_ctx失败:', e);
          fileCtx = this.file_ctx;
        }

        console.log('处理图片URL:', value);

        if (typeof value === 'object') {

          if (value.originalFilePath) {
            const fullUrl = fileCtx + value.originalFilePath;
            console.log('使用attachStorePath显示图片:', fullUrl);
            return fullUrl;
          }

          // 最后尝试使用filePath字段
          if (value.filePath) {
            const fullUrl = fileCtx + value.filePath;
            console.log('使用filePath显示图片:', fullUrl);
            return fullUrl;
          }

          return '';
        }

        // 如果是字符串，检查是否已包含http前缀
        if (typeof value === 'string') {
          if (value.startsWith('http')) {
            console.log('使用http路径显示图片:', value);
            return value;
          } else {
            // 添加服务器前缀
            const fullUrl = fileCtx + value;
            console.log('给字符串路径添加前缀:', fullUrl);
            return fullUrl;
          }
        }

        return value || '';
      } catch (e) {
        console.error('处理图片URL错误:', e);
        return '';
      }
    },

    // 输入处理
    handleInput(e) {
      this.$emit('input', e.detail.value);
    },

    // 字段输入处理
    handleInputChange(fieldName, e) {
      this.emitInputEvent(fieldName, e.detail.value);
    },

    // 统一的值更新事件
    emitInputEvent(fieldName, value) {
      const updatedValue = {...this.value, [fieldName]: value};
      this.$emit('input', updatedValue);

      // 如果是银行卡号字段，并且字段值有变化，调用查询接口
      if (fieldName === 'accountNumber' && value && value.length >= 16) {
        this.queryCardBinInfo(value);
      }
    },

    // 银行卡bin信息查询
    async queryCardBinInfo(cardNo) {
      try {
        const params = {
          appid: this.configData.appid,
          orgCode: this.configData.orgCode,
          cardNo: cardNo
        };

        console.log('开始查询银行卡信息:', params);

        uni.showLoading({
          title: '查询中...'
        });

        const result = await this.$api.distribution.queryCardBinInfo(params);

        console.log('银行卡信息查询结果:', result);
        uni.hideLoading();
        if (result && result.code === 0 && result.data && result.data.respData) {
          const bankInfo = result.data.respData;
          console.log('获取到的银行卡信息:', bankInfo);

          // 创建表单数据的副本以进行批量更新
          const updatedFormData = {...this.value};

          if (bankInfo.bankName) {
            // 填充收款账户银行
            if (this.value.distributionType === '个人') {
              // 个人表单只填充开户行名称
              updatedFormData.accountBranchName = bankInfo.bankName;
            } else {
              // 企业表单同时填充银行和开户行名称
              updatedFormData.accountBank = bankInfo.bankName;
              updatedFormData.accountBranchName = bankInfo.bankName;
            }
            console.log('已填充银行名称:', bankInfo.bankName);
          }

          // 开户行号
          if (bankInfo.bankCode) {
            updatedFormData.accountBankBranch = bankInfo.bankCode;
            console.log('已填充开户行号:', bankInfo.bankCode);
          }

          // 清算行号
          if (bankInfo.clearingBankCode) {
            updatedFormData.accountBankCode = bankInfo.clearingBankCode;
            console.log('已填充清算行号:', bankInfo.clearingBankCode);
          }
          // 一次性更新所有字段，避免多次触发v-model更新
          this.$emit('input', updatedFormData);

          // 强制更新视图
          this.$forceUpdate();

          // 提示用户银行卡查询成功
          uni.showToast({
            title: '银行卡信息识别成功',
            icon: 'success',
            duration: 1500
          });
        } else {
          console.warn('银行卡信息查询结果无效:', result);
          uni.showToast({
            title: '未获取到银行卡信息',
            icon: 'none',
            duration: 1500
          });
        }
      } catch (error) {
        // 确保发生错误时也关闭加载提示
        uni.hideLoading();
        console.error('查询银行卡信息失败:', error);
        uni.showToast({
          title: '查询银行卡信息失败',
          icon: 'none',
          duration: 1500
        });
      }finally{
        uni.hideLoading();
      }
    },

    // 切换协议选中状态
    toggleAgreement() {
      this.isAgreed = !this.isAgreed;
    },

    // 协议点击事件
    handleAgreementClick() {
      this.$emit('agreement-click');
    },

    // 验证表单
    validateForm() {
      const errors = [];

      // 遍历验证规则进行验证
      Object.entries(this.validRules).forEach(([field, rule]) => {
        const value = this.value[field];

        // 必填项验证
        if (rule.required) {
          // 处理函数类型的required
          const isRequired = typeof rule.required === 'function'
            ? rule.required(value, this.value)
            : rule.required;

          if (isRequired && !value) {
            errors.push(rule.message);
            return;
          }
        }

        // 自定义验证
        if (rule.validator && !rule.validator(value)) {
          errors.push(rule.message);
          return;
        }

        // 图片类型验证
        if (rule.type === 'image' && rule.required) {
          if (!value || (typeof value === 'object' && !value.url && !value.filePath)) {
            errors.push(rule.message);
          }
        }
      });

      return errors;
    },

    // 提交表单
    submitForm() {
      // 验证协议
      if (this.agreementText && !this.isAgreed) {
        uni.showToast({
          title: '请先同意协议',
          icon: 'none'
        });
        return;
      }

      // 表单验证
      const errors = this.validateForm();
      if (errors.length > 0) {
        uni.showToast({
          title: errors[0],
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 处理表单数据，提取图片的 filePath
      const processedData = {...this.value};

      // 遍历表单字段，处理图片类型
      this.formFields.forEach(field => {
        // 处理普通图片字段
        if (field.type === 'image' && processedData[field.name]) {
          if (typeof processedData[field.name] === 'object') {
            processedData[field.name] = processedData[field.name].filePath || processedData[field.name].url || '';
          }
        }

        // 处理身份证图片字段
        if (field.type === 'idcard') {
          if (field.frontField && processedData[field.frontField]) {
            if (typeof processedData[field.frontField] === 'object') {
              processedData[field.frontField] = processedData[field.frontField].filePath || processedData[field.frontField].url || '';
            }
          }

          if (field.backField && processedData[field.backField]) {
            if (typeof processedData[field.backField] === 'object') {
              processedData[field.backField] = processedData[field.backField].filePath || processedData[field.backField].url || '';
            }
          }
        }
      });

      // 提交表单数据
      this.$emit('submit', processedData);
    },

    // 重置表单
    resetForm() {
      // 创建一个新的空白表单数据
      const emptyForm = {};

      // 遍历表单字段，将每个字段重置为空
      this.formFields.forEach(field => {
        if (field.type === 'switch') {
          // 开关类型默认为true
          emptyForm[field.name] = true;
        } else if (field.type === 'idcard') {
          // 身份证字段
          emptyForm[field.frontField] = '';
          emptyForm[field.backField] = '';
        } else {
          // 其他字段设为空
          emptyForm[field.name] = '';
        }
      });

      // 触发重置事件
      this.$emit('input', emptyForm);
      this.$emit('reset');
    },

    // 头像上传返回处理
    async avatarReturnFn(imageList) {
      console.log('头像上传返回:', imageList);

      if (imageList && imageList.length > 0) {
        try {
          // 获取图片字段列表
          const imageFields = this.formFields.filter(field => field.type === 'image');

          if (imageFields.length > 0) {
            // 优先使用开户许可证字段，通常是openAccountProve
            const targetField = imageFields.find(field => field.name === 'openAccountProve') || imageFields[0];
            const fieldName = targetField.name;

            console.log('准备上传图片，字段名:', fieldName);

            // 显示上传中提示
            uni.showLoading({
              title: '正在上传...',
              mask: true
            });

            // 上传文件到服务器
            const attachmentInfo = await this.uploadFileToServer(imageList[0], fieldName);

            if (attachmentInfo) {
              // 获取文件服务器前缀
              let fileCtx = '';
              try {
                fileCtx = uni.getStorageSync('file_ctx') || this.file_ctx;
              } catch (e) {
                console.error('获取file_ctx失败:', e);
                fileCtx = this.file_ctx; // 使用默认值
              }

              // 构建图片对象，使用服务器路径用于显示，保留原始上传路径
              const imageObj = {
                ...imageList[0],
                url: fileCtx + attachmentInfo.attachStorePath, // 添加服务器前缀用于显示
                filePath: attachmentInfo.originalFilePath // 原始上传的路径
              };

              console.log('设置图片表单字段:', fieldName, imageObj);

              // 更新表单字段
              this.emitInputEvent(fieldName, imageObj);

              // 提示上传成功
              uni.hideLoading();
              uni.showToast({
                title: '上传成功',
                icon: 'success'
              });
            } else {
              uni.hideLoading();
              throw new Error('附件信息获取失败');
            }
          } else {
            console.error('未找到图片字段');
            uni.showToast({
              title: '未找到图片字段',
              icon: 'none'
            });
          }
        } catch (error) {
          uni.hideLoading();
          console.error('图片上传处理错误:', error);
          uni.showToast({
            title: '上传处理失败',
            icon: 'none'
          });
        }
      }
    },

    // 营业执照上传返回处理
    async licenseReturnFn(imageList) {
      console.log('营业执照上传返回:', imageList);

      if (imageList && imageList.length > 0) {
        try {
          // 获取营业执照字段
          const licenseFields = this.formFields.filter(field => field.type === 'license');

          if (licenseFields.length > 0) {
            const fieldName = licenseFields[0].name; // 通常是 'businessLicense'
            console.log('准备上传营业执照，字段名:', fieldName);

            // 显示上传中提示
            uni.showLoading({
              title: '正在上传...',
              mask: true
            });

            // 上传文件到服务器
            const attachmentInfo = await this.uploadFileToServer(imageList[0], fieldName);

            if (attachmentInfo) {
              // 获取文件服务器前缀
              let fileCtx = '';
              try {
                fileCtx = uni.getStorageSync('file_ctx') || this.file_ctx;
              } catch (e) {
                console.error('获取file_ctx失败:', e);
                fileCtx = this.file_ctx; // 使用默认值
              }

              // 构建图片对象，使用服务器路径用于显示，保留原始上传路径
              const imageObj = {
                ...imageList[0],
                url: fileCtx + attachmentInfo.attachStorePath, // 添加服务器前缀用于显示
                filePath: attachmentInfo.originalFilePath // 原始上传的路径
              };

              console.log('设置营业执照表单字段:', fieldName, imageObj);

              // 更新表单字段
              this.emitInputEvent(fieldName, imageObj);

              // 提示上传成功
              uni.hideLoading();
              uni.showToast({
                title: '上传成功',
                icon: 'success'
              });
            } else {
              uni.hideLoading();
              throw new Error('附件信息获取失败');
            }
          } else {
            console.error('未找到营业执照字段');
            uni.showToast({
              title: '未找到营业执照字段',
              icon: 'none'
            });
          }
        } catch (error) {
          uni.hideLoading();
          console.error('营业执照上传处理错误:', error);
          uni.showToast({
            title: '上传处理失败',
            icon: 'none'
          });
        }
      }
    },

    // 身份证正面上传返回
    async idCardFrontReturnFn(imageList) {
      if (imageList && imageList.length > 0) {
        try {
          // 获取表单中的身份证字段
          const idcardFields = this.formFields.filter(field => field.type === 'idcard');

          if (idcardFields.length > 0 && idcardFields[0].frontField) {
            const fieldName = idcardFields[0].frontField;
            console.log('准备上传身份证正面，字段名:', fieldName);

            // 显示上传中提示
            uni.showLoading({
              title: '正在上传...',
              mask: true
            });

            // 上传文件到服务器
            const attachmentInfo = await this.uploadFileToServer(imageList[0], fieldName);

            if (attachmentInfo) {
              // 获取文件服务器前缀
              let fileCtx = '';
              try {
                fileCtx = uni.getStorageSync('file_ctx') || this.file_ctx;
              } catch (e) {
                console.error('获取file_ctx失败:', e);
                fileCtx =  this.file_ctx; // 使用默认值
              }

              // 构建图片对象，使用服务器路径用于显示，保留原始上传路径
              const imageObj = {
                ...imageList[0],
                url: fileCtx + attachmentInfo.attachStorePath, // 添加服务器前缀用于显示
                filePath: attachmentInfo.originalFilePath // 原始上传的路径
              };

              console.log('设置身份证正面表单字段:', fieldName, imageObj);

              // 更新表单字段
              this.emitInputEvent(fieldName, imageObj);

              // 提示上传成功
              uni.hideLoading();
              uni.showToast({
                title: '上传成功',
                icon: 'success'
              });
            } else {
              uni.hideLoading();
              throw new Error('附件信息获取失败');
            }
          } else {
            console.error('未找到身份证正面字段');
            uni.showToast({
              title: '未找到身份证正面字段',
              icon: 'none'
            });
          }
        } catch (error) {
          uni.hideLoading();
          console.error('身份证正面上传处理错误:', error);
          uni.showToast({
            title: '上传处理失败',
            icon: 'none'
          });
        }
      }
    },

    // 身份证背面上传返回
    async idCardBackReturnFn(imageList) {
      if (imageList && imageList.length > 0) {
        try {
          // 获取表单中的身份证字段
          const idcardFields = this.formFields.filter(field => field.type === 'idcard');

          if (idcardFields.length > 0 && idcardFields[0].backField) {
            const fieldName = idcardFields[0].backField;
            console.log('准备上传身份证背面，字段名:', fieldName);

            // 显示上传中提示
            uni.showLoading({
              title: '正在上传...',
              mask: true
            });

            // 上传文件到服务器
            const attachmentInfo = await this.uploadFileToServer(imageList[0], fieldName);

            if (attachmentInfo) {
              // 获取文件服务器前缀
              let fileCtx = '';
              try {
                fileCtx = uni.getStorageSync('file_ctx') || this.file_ctx;
              } catch (e) {
                console.error('获取file_ctx失败:', e);
                fileCtx = this.file_ctx; // 使用默认值
              }

              // 构建图片对象，使用服务器路径用于显示，保留原始上传路径
              const imageObj = {
                ...imageList[0],
                url: fileCtx + attachmentInfo.attachStorePath, // 添加服务器前缀用于显示
                filePath: attachmentInfo.originalFilePath // 原始上传的路径
              };

              console.log('设置身份证背面表单字段:', fieldName, imageObj);

              // 更新表单字段
              this.emitInputEvent(fieldName, imageObj);

              // 提示上传成功
              uni.hideLoading();
              uni.showToast({
                title: '上传成功',
                icon: 'success'
              });
            } else {
              uni.hideLoading();
              throw new Error('附件信息获取失败');
            }
          } else {
            console.error('未找到身份证背面字段');
            uni.showToast({
              title: '未找到身份证背面字段',
              icon: 'none'
            });
          }
        } catch (error) {
          uni.hideLoading();
          console.error('身份证背面上传处理错误:', error);
          uni.showToast({
            title: '上传处理失败',
            icon: 'none'
          });
        }
      }
    },

    // 商户门头照上传返回处理
    async merchantPhotoReturnFn(imageList) {
      console.log('商户门头照上传返回:', imageList);

      if (imageList && imageList.length > 0) {
        try {
          const fieldName = 'merchantPhoto';
          console.log('准备上传商户门头照，字段名:', fieldName);

          // 显示上传中提示
          uni.showLoading({
            title: '正在上传...',
            mask: true
          });

          // 上传文件到服务器
          const attachmentInfo = await this.uploadFileToServer(imageList[0], fieldName);

          if (attachmentInfo) {
            // 获取文件服务器前缀
            let fileCtx = '';
            try {
              fileCtx = uni.getStorageSync('file_ctx') || this.file_ctx;
            } catch (e) {
              console.error('获取file_ctx失败:', e);
              fileCtx = this.file_ctx; // 使用默认值
            }

            // 构建图片对象，使用服务器路径用于显示，保留原始上传路径
            const imageObj = {
              ...imageList[0],
              url: fileCtx + attachmentInfo.attachStorePath, // 添加服务器前缀用于显示
              filePath: attachmentInfo.originalFilePath // 原始上传的路径
            };

            console.log('设置商户门头照表单字段:', fieldName, imageObj);

            // 更新表单字段
            this.emitInputEvent(fieldName, imageObj);

            // 提示上传成功
            uni.hideLoading();
            uni.showToast({
              title: '上传成功',
              icon: 'success'
            });
          } else {
            uni.hideLoading();
            throw new Error('附件信息获取失败');
          }
        } catch (error) {
          uni.hideLoading();
          console.error('商户门头照上传处理错误:', error);
          uni.showToast({
            title: '上传处理失败',
            icon: 'none'
          });
        }
      }
    },

    // 商铺内部照片上传返回处理
    async shopPhotoReturnFn(imageList) {
      console.log('商铺内部照片上传返回:', imageList);

      if (imageList && imageList.length > 0) {
        try {
          const fieldName = 'shopInnerPhoto';
          console.log('准备上传商铺内部照片，字段名:', fieldName);

          // 显示上传中提示
          uni.showLoading({
            title: '正在上传...',
            mask: true
          });

          // 上传文件到服务器
          const attachmentInfo = await this.uploadFileToServer(imageList[0], fieldName);

          if (attachmentInfo) {
            // 获取文件服务器前缀
            let fileCtx = '';
            try {
              fileCtx = uni.getStorageSync('file_ctx') || this.file_ctx;
            } catch (e) {
              console.error('获取file_ctx失败:', e);
              fileCtx = this.file_ctx; // 使用默认值
            }

            // 构建图片对象，使用服务器路径用于显示，保留原始上传路径
            const imageObj = {
              ...imageList[0],
              url: fileCtx + attachmentInfo.attachStorePath, // 添加服务器前缀用于显示
              filePath: attachmentInfo.originalFilePath // 原始上传的路径
            };

            console.log('设置商铺内部照片表单字段:', fieldName, imageObj);

            // 更新表单字段
            this.emitInputEvent(fieldName, imageObj);

            // 提示上传成功
            uni.hideLoading();
            uni.showToast({
              title: '上传成功',
              icon: 'success'
            });
          } else {
            uni.hideLoading();
            throw new Error('附件信息获取失败');
          }
        } catch (error) {
          uni.hideLoading();
          console.error('商铺内部照片上传处理错误:', error);
          uni.showToast({
            title: '上传处理失败',
            icon: 'none'
          });
        }
      }
    },

    // 处理表单输入
    handleFormInput(e) {
      const fieldName = e.currentTarget.dataset.fieldName;
      this.handleInputChange(fieldName, e);
    },

    // 新增的handleImageUpload方法
    handleImageUpload(e) {
      const fieldName = e.currentTarget.dataset.fieldName;
      this.handleImageClick(fieldName);
    },

    // 新增的handleImageRemove方法
    handleImageRemove(e) {
      const fieldName = e.currentTarget.dataset.fieldName;
      this.handleImageDelete(fieldName);
    },

    // 处理身份证图片点击
    handleIdCardImageClick(e) {
      const fieldName = e.currentTarget.dataset.fieldName;
      this.handleImageClick(fieldName);
    },

    // 处理身份证图片删除
    handleIdCardImageRemove(e) {
      const fieldName = e.currentTarget.dataset.fieldName;
      this.handleImageDelete(fieldName);
    },

    // 简化的文本域输入处理方法
    handleEnhancedTextareaInput(e) {
      const fieldName = e.currentTarget.dataset.fieldName;
      const maxlength = parseInt(e.currentTarget.dataset.maxlength || 200);

      // 获取当前输入的值
      const newValue = e.detail.value;
      console.log('newValue输入',newValue);

      // 更新表单字段值（不截断，允许超过字符限制）
      this.emitInputEvent(fieldName, newValue);

      // 如果超过字符限制，只提示一次
      if (newValue.length > maxlength && newValue.length > (this.value[fieldName] || '').length) {
        uni.showToast({
          title: `已超过${maxlength}个字符限制，提交时将无法通过验证`,
          icon: 'none',
          duration: 2000
        });
      }
    },

    // 新增的handleSwitchInput方法
    handleSwitchInput(e) {
      const fieldName = e.currentTarget.dataset.fieldName;
      this.handleSwitchChange(fieldName, e);
    },

    // 新增的findAttachmentByType方法
    findAttachmentByType(attachType) {
      if (!this.value.fileAttachments || !Array.isArray(this.value.fileAttachments)) {
        return null;
      }

      return this.value.fileAttachments.find(item => item.attachType === attachType);
    },

    // 新增的getImageUrlFromAttachment方法
    getImageUrlFromAttachment(attachment) {
      if (!attachment) return '';
      return this.getImageUrl(attachment);
    }
  }
}
</script>

<style lang="scss" scoped>
.form-component {
  width: 100%;
  padding-bottom: 200rpx; /* 企业模式下的底部间距 */
  margin-bottom: 50rpx; /* 表单底部额外间距 */
  /* 使用calc(100vh - 1px)解决安卓兼容问题 */
  height: auto;
  overflow-y: visible;
}

/* 个人模式下的底部间距 */
.form-component.personal-form {
  padding-bottom: 36rpx;
}

.botTabLine {
  width: 100%;
  height: 104rpx;
  line-height: 104rpx;
  border-bottom: 2rpx solid #EAEBF0;
  display: flex;
  align-items: center;

  &.avatar-line {
    height: 150rpx; /* 形象照行的高度设置为150rpx */
    line-height: 150rpx;
  }

  .lineValue {
    flex: 1;
    color: #333;
    font-size: 28rpx;

    .placeholder {
      color: #999;
    }
  }

  .lineIcon {
    width: 32rpx;
    height: 32rpx;
  }

  .bookInput {
    width: 438rpx;
    height: 40rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #1D2029;
  }
}

.lineTitle {
  font-weight: 500;
  font-size: 28rpx;
  color: #1D2029;
  width: 212rpx;

  .required {
    font-weight: 500;
    font-size: 14px;
    color: #FF5500;
  }
}

.upload-box {
  width: 140rpx;
  height: 140rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;

  .preview-image {
    width: 100%;
    height: 100%;
    border-radius: 8rpx;
  }

  .empty-upload {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f4f6fa;
    border-radius: 8rpx;
  }

  .plus-icon {
    position: relative;
    width: 40rpx;
    height: 40rpx;

    &:before, &:after {
      content: '';
      position: absolute;
      background-color: #aaa;
    }

    &:before {
      width: 40rpx;
      height: 4rpx;
      top: 18rpx;
      left: 0;
    }

    &:after {
      width: 4rpx;
      height: 40rpx;
      left: 18rpx;
      top: 0;
    }
  }
}

.preview-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.preview-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.delete-icon {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 36rpx;
  height: 36rpx;
  background-color: #ff5500;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  z-index: 2;
  font-weight: bold;
}

.idcard-delete {
  width: 36rpx;
  height: 36rpx;
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background: #ff5500;
  color: #ff5500;
  border-radius: 50%;
  z-index: 9;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
}

// 文本域相关样式
.experience-input {
  width: 100%;
  height: 240rpx;
  padding: 24rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  border: 1rpx solid #D9DBE0;
}

.word-counter {
  font-size: 24rpx;
  color: #999;
  text-align: right;
  display: block;
  margin-top: 16rpx;
  margin-bottom: 24rpx;
}

.word-counter-exceed {
  color: #FF5500;
  font-weight: bold;
}

// 额外内容区域样式
.additionalContent {
  margin-top: 32rpx;
}

// 身份证上传区域样式
.idcard-upload-container {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
  width: 100%;
}

.idcard-item {
  width: 48%;
  height: 200rpx;
  background: #F4F6FA;
  border-radius: 16rpx;
  position: relative;
  overflow: hidden;
}

.empty-idcard {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.idcard-icon {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 10rpx;
}

.idcard-text {
  font-size: 28rpx;
  color: #666;
}

.idcard-image {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
}

.idcard-delete {
  width: 30rpx;
  height: 30rpx;
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background: white;
  border-radius: 50%;
  z-index: 9;
}

// 隐藏上传组件样式
.lineHide {
  width: 0;
  overflow: hidden;
  height: 0;
}

// 新增样式
.form-fields {
  width: 100%;
}

.btn-group {
  display: flex;
  justify-content: space-between;
}

.bottomBtn {
  width: 750rpx;
  height: 180rpx;
  background: #FFFFFF;
  box-shadow: 0rpx -1rpx 0rpx 0rpx #EAEBF0;
  position: fixed;
  bottom: 30rpx;
  left: 0;
  padding: 24rpx 32rpx;
  box-sizing: border-box;
  z-index: 99; /* 确保按钮始终在最上层 */
}

.agreement-row {
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 24rpx;
}

.agreement-text {
  font-size: 14px;
  color: #666;
}

.agreement-link {
  font-size: 14px;
  color: #1687F7;
}

.custom-checkbox {
  display: inline-block;
  vertical-align: middle;
  margin-right: 8px;
  margin-left: 32rpx;
}

.checkbox-icon {
  width: 28rpx;
  height: 28rpx;
  border: 2rpx solid #ccc;
  border-radius: 50%;
  position: relative;
  transition: all 0.3s;
}

.checkbox-icon.checked {
  background: #00B578;
  border-color: #00B578;
}

.checkbox-icon.checked::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  width: 16rpx;
  height: 8rpx;
  border: 4rpx solid #fff;
  border-top: none;
  border-right: none;
  transform: translate(-50%, -60%) rotate(-45deg);
}

.comBtn {
  flex: 1;
  height: 88rpx;
  background: #00B484;
  border-radius: 44rpx;
  text-align: center;
  line-height: 88rpx;
  font-weight: 500;
  font-size: 32rpx;
  color: #FFFFFF;
}

.resetBtn {
  width: 220rpx;
  height: 88rpx;
  background: #F5F7FA;
  border-radius: 44rpx;
  text-align: center;
  line-height: 88rpx;
  font-weight: 500;
  font-size: 32rpx;
  color: #666666;
  margin-right: 24rpx;
}

.custom-slot-container {
  width: 100%;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #EAEBF0;
}

.switch-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 0;
  height: 88rpx;
  line-height: 88rpx;
}

.switch-wrapper {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}

.custom-switch {
  transform: scale(0.8);
}
</style>
