<template>
  <view class='ai-doctor' v-if="search && isShowAiDoctor">
    <view class="header">
      <view class="doctor-head-l">
        <image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-ai-doctor-img.png'"></image>
        <view class="text">{{ isShowLoading ? '深度思考中...' : '已完成深度思考'}}</view>
      </view>
      <view class="content-head" v-if="!innerHtml">
        <view class="head-l"></view>
        <image class="head-loading" :src="file_ctx + 'static/image/business/hulu-v2/loading.gif'"></image>
      </view>

      <!-- 深度思考内容 -->
      <image class="content-wrapper-loading" v-if="innerHtmlLoading" :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/ai-skeleton-screen-loading.gif'"></image>
      <view class="content-wrapper" v-if="!innerHtmlLoading && innerHtml">  
        <view class="text">{{ innerHtml }}</view>
        <view class="text-prompt" v-if="!isShowLoading">此内容由AI生成，请仔细甄别</view>
      </view>
    </view>

    <!-- 医生评论 -->
    <view class="ai-doctor-list-loading" v-if="commentListLoading"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/ai-doctor-skeleton-screen-loading.gif'"></image></view>
    <template v-if="!commentListLoading && commentList.length">
      <view class="ai-doctor-list">
        <view class="ai-doctor-item ai-doctor-wrap" v-for="(item, index) in commentList" :key="item.id">
          <view class="doctor-item-head" @click="hanldeClickDoctor(item)">
            <view class="head-img"><image @error="imgError(item)" :src="item.expertPic || defaultImage"></image></view>
            <view class="head-info">
              <view class="info-name" v-if="item.name">{{ item.name }}<span v-if="item.post">{{ item.post }}</span></view>
              <view class="info-desc" v-if="item.hospitalName">{{ item.hospitalName }}<span v-if="item.deptType">-{{ item.deptType }}</span></view>
            </view>
          </view>
          <view class="doctor-info">{{ item.content }}</view>
          <view class="doctor-item-bott" v-if="item.postMessageId" @click="handleClickJump(item)">
            <view class="item-bott-l" v-if="item.title">来源于帖子：{{ item.title }}</view>
            <view class="item-bott-r" v-if="item.title"><image :src="file_ctx + 'static/image/business/hulu-v2/icon-ai-doctor-right-img.png'"></image></view>
          </view>
        </view>
      </view>
    </template>
  </view>
</template>

<script>
  import { isDomainUrl } from '@/utils/index.js'
  import { streamRequest } from './api/business-ai.js'
  import env from '@/config/env'
  export default {
    props: {
      search:{
        type:String,
        default:''
      }
    },
    data(){
      return{
        file_ctx: this.file_ctx,
        reflectText:'深度思考中...',
        isShowLoading:false,
        contentObj:{},
        commentList:[], //评论内容
        isShowAiDoctor:false,
        defaultImage: this.file_ctx + 'static/image/business/pharmacy-cyclopedia/ai-doctor-default-profile-photo.png', // 默认图片路径
        innerHtml:'',
        commentListLoading:true,
        innerHtmlLoading:true,
      }
    },
    watch:{
      search(){
        if(!this.search){
          this.contentObj = {}
          this.commentList = []
          this.isShowAiDoctor = false
          // this.resetDisplay()
        }
      }
    },
    onLoad(){
      this.$set(this,'commentList',[])
      this.innerHtml = ''
    },
    onShow(){
      this.$set(this,'commentList',[])
      // this.innerHtml = ''
    },
    onHide(){
      // this.stopTypingEffect()
      // this.innerHtml = ''
    },
    mounted(){
      this.$set(this,'commentList',[])
      this.innerHtml = ''
    },
    methods:{
      replaceSurroundingQuotes(str) {
        if (str.startsWith('"') && str.endsWith('"')) {
          // 移除首尾双引号，再用单引号包裹
          return `${str.slice(1, -1)}`;
        }
        return str; // 不满足条件则原样返回
      },
      getStreamRequest(){
        streamRequest(
          env.ctx + 'dm/api/v1/postmessage/wenxin/ask/steam',
          {
            method: 'POST',
            header: {},
            data: {
              message:this.search,
            }
          },
          async(chunk) => {
            // console.log('收到数据块:', chunk);
            try {
              let str = chunk.replace(/\\"/g, '"').replace(/\\\\/g, '\\').replace(/^data:"data: \s*/, '"').trim()
              let data = this.replaceSurroundingQuotes(str)
              if(typeof data === 'string'){
                data = JSON.parse(data)
                this.innerHtml += data.result
                this.innerHtmlLoading = false
              }
            } catch (error) {
              console.error('JSON 解析失败:', error);
              this.innerHtmlLoading = false
            }
          },
          (error) => {
            console.error('流式请求错误:', error);
            this.innerHtmlLoading = false
            // uni.showToast({
            //   title: '请求失败: ' + error,
            //   icon: 'none'
            // });
          },
          (complete) => {
            this.isShowLoading = false
            this.innerHtmlLoading = false
            console.log('流式请求完成');
            // uni.showToast({
            //   title: '内容生成完成',
            //   icon: 'success'
            // });
          }
        );
      },
      imgError(item){
        item.expertPic = this.defaultImage
      },
      hanldeClickDoctor(item){
        if(item.doctorId){
          this.$navto.push('DoctorDetail', {id:item.doctorId})
        }
      },
      handleClickJump(item){
        if(item.postMessageId){
          this.$navto.push('PostsDetail', {id:item.postMessageId,isShowBtn:true})
        }
      },
      handleSecondRound(milliseconds){
        const secondsRound = Math.round(milliseconds / 1000)
        return secondsRound
      },
      init(){
        if(this.search){
          this.innerHtml = ''
          this.isShowAiDoctor = true
          this.isShowLoading = true
          try{
            this.getStreamRequest()
            this.postCommentQuerySearchContentPage() //医生评论
          } catch (err){
            console.log(err,'err')
          }
        }
      },

      async postCommentQuerySearchContentPage(){
        this.commentListLoading = true
        const res = await this.$api.postmessage.postCommentQuerySearchContentPage({size:10,current:1,condition:{content:this.search}})
        if(res?.data?.records?.length){
          this.commentList = res.data?.records.map(item=>({...item,expertPic:isDomainUrl(item.expertPic)}))
          console.log(this.commentList,'commentList22222')
          this.commentListLoading = false
        } else {
          this.commentListLoading = false
        }
      },
    },
 }
</script>

<style lang='scss' scoped>

.ai-doctor{
  display: flex;
  flex-direction: column;
  // height: calc(100vh - 40rpx);
  height: 100%;
  padding: 20rpx 32rpx;
  // background-color: #fff;
  overflow-y: auto;
  overflow-x: hidden;
  background: #F4F6FA;
  .header{
    display: flex;
    // align-items: center;
    flex-direction: column;
    // justify-content: space-between;
    // margin-bottom: 20rpx;
    padding: 20rpx 0;
    background: #fff;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    .doctor-head-l{
      display: flex;
      align-items: center;
      padding: 0 20rpx;
      image{
        display: flex;
        width: 48rpx;
        height: 48rpx;
        margin-right: 20rpx;
      }
      .text{
        font-size: 28rpx;
        color: #777777;
      }
    }
    // .doctor-head-r{
    //   image{
    //     display: flex;
    //     width: 32rpx;
    //     height: 32rpx;
    //   }
    // }
  }
  // .bottom-scroll{
  //   position: relative;
  //   flex: 1;
  //   overflow: hidden;
  // }
  .content-head{
    display: flex;
    margin: 20rpx 0 24rpx 40rpx;
    .head-l{
      border-left: 2rpx solid #E6E6E6; 
      margin-right: 34rpx;
    }
    .head-loading{
      display: flex;
      width: 24rpx;
      height: 24rpx;
    }
  }
  .content-wrapper{
    background: #fff;
    padding: 20rpx;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    display: flex;
    flex-direction: column;
    .text{
      word-break: break-all;
      white-space: pre-wrap;
      font-size: 32rpx;
    }
    .text-prompt{
      text-align: center;
      // margin-top: 20rpx;
      font-size: 24rpx;
      color: #777777;
      padding: 20rpx 0;
    }
  }
  .ai-doctor-wrap{ 
    background-color: #fff;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
  }
  .ai-doctor-head{
    display: flex;
    flex-direction: column;
    height: 100vh;
    padding: 20rpx;
    // margin-bottom: 24rpx;
  }
  // .ai-doctor-content{
  //   padding: 20rpx;
  //   margin-bottom: 24rpx;
  //   .content-item{
  //     font-size: 28rpx;
  //     color: #333333;
  //     .prompt-steps{
  //     }
  //     .serial{
  //       margin: 10rpx 0;
  //       font-weight: bold;
  //     }
  //     .prompt-steps-content{}
  //   }
  // }
  .ai-doctor-list{
    padding-bottom: 50px;
    .ai-doctor-item{
      padding: 40rpx 20rpx 20rpx;
      margin-top: 24rpx;
      .doctor-item-head{
        margin-bottom: 14rpx;
        display: flex;
        .head-img{
          display: flex;
          width: 96rpx;
          height: 96rpx;
          border-radius: 50%;
          overflow: hidden;
          margin-right: 12rpx;
          image{
            width: 100%;
            height: 100%;
          }
        }
        .head-info{
          .info-name{
            font-weight: bold;
            font-size: 32rpx;
            color: #333333;
            span{
              display: inline-block;
              height: 34rpx;
              background: #DFFBF4;
              border-radius: 8rpx 8rpx 8rpx 8rpx;
              padding: 0 8rpx;
              margin-left: 8rpx;
              font-size: 24rpx;
              color: #00B484;
            }
          }
          .info-desc{
            margin-top: 8rpx;
            font-size: 28rpx;
            color: #777777;
          }
        }
      }
      .doctor-info{
        display: flex;
        margin: 14rpx 0 20rpx;
        padding-bottom: 20rpx;
        border-bottom: 1rpx solid #E6E6E6;
      }
      .doctor-item-bott{
        display: flex;
        align-items: center;
        justify-content: space-between;
        .item-bott-l{
          font-size: 28rpx;
          color: #777777;
        }
        .item-bott-r{
          margin-left: 26px;
          image{
            display: flex;
            width: 32rpx;
            height: 32rpx;
          }
        }
      }
    }
  }
  .content-wrapper-loading{
    display: flex;
    width: 626rpx;
    height: 364rpx;
    margin-left: 40rpx;
  }
  .ai-doctor-list-loading{
    width: 686rpx;
    height: 524rpx;
    background: #FFFFFF;
    padding: 40rpx 20rpx;
    box-sizing: border-box;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    margin-top: 20rpx;
    image{
      width: 646rpx;
      height: 444rpx;
    }
  }
}
</style>