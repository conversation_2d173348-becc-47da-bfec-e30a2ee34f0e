<template>
  <view class="main">
    <!-- #ifndef H5 -->
    <sa-hover-menu />
    <!-- #endif -->
    <web-view :src="src"></web-view>
  </view>
</template>

<script>
import saHoverMenu from '@/components/basics/sa-hover-menu/sa-hover-menu'
export default {
  components: {
    saHoverMenu
  },
  data() {
    return {
      src: ''
    }
  },
  onLoad(paramsObj = {}) {
    const query = this.$Route.query
    if (!this.$validate.isNull(query)) {
      this.src = this.fullyDecodeURI(query.src)
      console.log('query-------', query)
      uni.setNavigationBarTitle({
        title: this.fullyDecodeURI(query.title)
      })
    }
  },
  methods: {
    fullyDecodeURI (url) {
      function isEncoded(url) {
        if (url.startsWith('https://') || url.startsWith('http://')) return false
        url = url || '';
        return url !== decodeURIComponent(url);
      }
      while (isEncoded(url)){
        url = decodeURIComponent(url);
      }
      return url;
    }
  }
}
</script>

<style lang="scss" scoped>
  .main{
    height: 100%;
    position: relative;
  }
</style>
