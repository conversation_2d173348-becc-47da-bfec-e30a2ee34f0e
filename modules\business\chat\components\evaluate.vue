<template>
    <view class="evaluate">
        <uni-rate :margin="8" :size="36" :value="5" readonly />
        <view class="evaluate-satisfy">非常满意</view>
        <view class="evaluate-form" v-for="(item, index) in evaluateForm" :key="index">
            <template v-if="item.type === 'rate'">
                <view class="evaluate-key">{{ item.label }}</view>
                <view class="evaluate-value">
                    <uni-rate :margin="6" :readonly="disabled" :size="28" v-model="item.value" @change="changeRate($event, index)" />
                </view>
            </template>
            <template v-if="item.type === 'text'">
              <!-- 0000 -->

                <title-textarea style="width: 100%;" v-model="item.value" :disabled="disabled" :config="item.config" :placeholder="item.placeholder" />
            <image  :src='$static_ctx + "image/business/im/icon-im-edit.png"'  class='editico'></image>
            </template>
			<!-- src="http://localhost:3000/images/edit.png" -->
        </view>

        <button class="btn" type="primary" size="mini" @click="confirm" v-if="!disabled">匿名评价</button>

        <view class="tipcontent">
          你的咨询服务已完成，如需继续咨询，可点击下方按钮继续咨询
        </view>
        <view class="jzbtn" @click="handleConsult">
          继续咨询
        </view>
    </view>
</template>

<script>
import TitleTextarea from "@/components/business/module/v1/title-textarea/index"
import uniRate from '@/modules/business/components/uni/uni-rate/uni-rate'

export default {
    components: {
        TitleTextarea,
        uniRate
    },
    props: {
        disabled: Boolean,
        form: {
            type: Array,
            default: () => {
                return []
            }
        },
    },
    data () {
        return {
            evaluateForm: [],
             $static_ctx: this.$static_ctx,
        }
    },
    mounted () {
        this.$common.setKeyVal('chat', 'bottomBoxHeight', 0, false)
    },
    methods: {
      handleConsult(){
        this.$emit('handle')
      },
        changeRate ({value},index) {
            this.$set(this.evaluateForm, index, {...this.evaluateForm[index], value})
        },
        confirm () {
            this.$emit('confirm', this.evaluateForm)
        }
    },
    watch: {
        form: {
            handler () {
                this.evaluateForm = JSON.parse(JSON.stringify(this.form))
            },
            deep: true,
            immediate: true
        }
    }
}
</script>

<style lang="scss" scoped>
.evaluate {
    display: flex;
    flex-direction: column;
    align-items: center;

    &-satisfy {
        font-weight: 550;
        font-size: 32upx;
        padding: 12upx 0 32upx;
    }
    &-form {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-bottom: 12upx;
        // border-top:2upx dotted #a2a2a2;
        padding:5upx 0;
        position: relative;

    }
    &-key {
        // color: #666;
        color: #1a1a1a;
        width: 150upx;
    }
    &-value{
      display:flex;
      justify-content: flex-end;
      flex:1;
    }

    .editico{
      width:40upx;
      height: 40upx;
      position: absolute;


      left:15upx;
      top:18upx
    }
    .tipcontent{
      font-size: 28upx;
      margin-bottom: 20upx;
      margin-top: 43upx;
    }
    .jzbtn{
          height: 80upx;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1upx solid #00d29d;
          padding: 0 40upx;
          border-radius: 50upx;
          font-size: 36upx;
          margin-right: auto;
          box-sizing: border-box;
          color: #00d29d;
    }

}
.btn {
    width: 100%;
    background-color: $topicC;
    border: none;
    height: 80upx;
    // line-height: 74upx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    @include rounded(32upx);
    border-radius: 50upx;
}
</style>
