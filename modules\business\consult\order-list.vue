<template>
    <scroll-refresh :fixed="false" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit" :noPage='true'>
        <view class="main-content">
            <view class="item" :index="index" v-for="(item, index) in orderList" :key="index" hover-class="message-hover-class"
                    @tap="navtoGo('CaseDetail',{id: item.id})">
                
                <view class="item-content">
                    <image mode="aspectFill" :src="item.chatList.chatUserHeadPath ? file_ctx + item.chatList.chatUserHeadPath : defaultAvatar"></image>
                    <!-- <view class="un-read" v-if="item.chatList.unMsgCount>0">{{ item.chatList.unMsgCount>99?'ⵈ': item.chatList.unMsgCount}}</view> -->
                    <view class="right u-border-bottom title-wrap">
                        <view class="right_top">
                            <view class="right_top_name u-line-1">
                                <view class="right_top_name_content">
                                    <!-- chatList.chatUserName -->
                                    {{ item.receiveUserName }}
                                    <view class="right_top_name_desc">图文咨询</view>
                                </view>
                                <view class="right_top_time " v-if="item.chatList">{{ item.chatList.lastMsgTime | getTimeStringAutoShort2 }}</view>
                                <view class="right_top_time " v-else>-</view>
                            </view>
                            <view class="right_center">
                                <view class="right_center_name u-line-1">
                                    {{ `患者-${item.patientInfoVo.name || '-'} | ${item.patientInfoVo.gender === 1 ? '男' : item.patientInfoVo.gender === 2 ? '女' : '未知'} | ${item.patientInfoVo.age || '-'}岁` }}
                                </view>
                            </view>
                            <view class="right_btm ">
                                <view class="u-line-1" v-if="item.chatList.lastMsgContent" v-html="item.chatList.lastMsgContent"></view>
                                <view class="u-line-1" v-else>-</view>
                                <!-- <view class="item-bottom-status">{{ item.consultStatus === 1 ? '待接入' : item.consultStatus === 2 ? '接入中' : '已结束' }}</view> -->
                            </view>
                        </view>
                    </view>
                </view>
                <view class="item-bottom">
                    <view class="item-bottom-status">{{ item.consultStatus === 1 ? '待接入' : item.consultStatus === 2 ? '接入中' : '已结束' }}</view>
                    <!-- <view class="item-bottom-desc">完成患者咨询-收费</view> -->
                    <view class="item-bottom-time" v-if="item.endTime">{{  item.endTime | getTimeStringAutoShort2 }}</view>
                    <view class="item-bottom-time" v-else>-</view>
                </view>
            </view>
        </view>
    </scroll-refresh>
</template>

<script>
import { mapState } from 'vuex'
import TimeUtils from '@/common/util/websocket/timeUtils.js'
import env from '@/config/env'

export default {
    props: {
        params: {
            type: Object,
            default: () => {
                return {}
            }
        },
        index: [Number, String],
        i: [Number, String]
    },
    data () {
        return {
            // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
            $constant: this.$constant,
            $common: this.$common,
            $accurateConversion: this.$accurateConversion,
            file_ctx: this.file_ctx,
            $static_ctx: this.$static_ctx,
            $timePlugin: this.$timePlugin,
            $validate: this.$validate,
            mescroll: null, // mescroll实例对象
            downOption: {
                auto: false // 不自动加载
            },
            upOption: {
                auto: false // 不自动加载
            },
            isInit: false, // 列表是否已经初始化
            scrollY: 0,
            regForm: {
                search: ''
            },
            itemList:[
            ],
            orderList: [],
            defaultAvatar: this.file_ctx + 'static/image/system/avatar/icon-default-avatar.png',
        }
    },
    computed: {
        ...mapState('chat', {
            ws: state => state.ws,
            chatlist: state => state.chatlist
        }),
        ...mapState('user', {
            curSelectUserInfo: state => state.curSelectUserInfo // 当前登录用户信息
        })
    },
    methods: {
        init(val) {
            this.$nextTick(() => {
                this.mescroll.triggerDownScroll()
            })
        },
        scrollInit(scroll) {
            scroll.optUp.page.num = 1
            scroll.optUp.page.size = 7
            this.mescroll = scroll
        },
        returnFn(obj) {
            const that = this
            // function queryPage(pageNum, pageSize, fn) {
            //   const param = {
            //     current: pageNum,
            //     size: pageSize,
            //     condition:{
            //       businessType: 1
            //     }
            //   }
            //   if (that.regForm.search) {
            //     param.condition.title = that.regForm.search
            //   }
            //   that.$ext.chat.chatlistQueryPage(param).then(res => {
            //     fn(res)
            //   })
            // }
            setTimeout(function() {
                // 拉取聊天列表
                const { centerUserId = '' } = that.curSelectUserInfo || {}
                const tenantId = that.$common.getKeyVal('user', 'curSelectStoreId', true) || env.tenantId
                // const data = {
                //   cmd: that.$constant.chat.GET_CHATLIST_CMD,
                //   data: {
                //     userId: centerUserId
                //   }
                // }
                // that.$ext.webSocket.webSocketSend(that.$constant.chat.GET_CHATLIST_CMD, data)

                let params = {}
                Object.keys(that.params).forEach(key => {
                    if (!['name'].includes(key)) {
                        params[key] = that.params[key]
                    }
                })

                that.$api.chat.orderQueryConsultList({ userId: centerUserId, tenantId, userType: that.$userType, ...params }).then(res => {
                    // that.$common.setKeyVal('chat', 'chatlist', res.data, false)
                    that.orderList = res.data || []
                    // if (that.index === that.i) {
                    //     that.$common.setKeyVal('chat', 'chatlist', that.orderList, false)
                    // }
                    obj.successCallback && obj.successCallback(res.data || [])
                })
                // queryPage(obj.pageNum, obj.pageSize, (data) => {
                //   if (obj.pageNum === 1) {
                //     that.itemList = []
                //   }
                //   that.itemList = that.itemList.concat(data.filter((v)=>{
                //     return !(that.itemList.indexOf(v) > -1)
                //   }))
                //   that.$common.setKeyVal('chat', 'chatlist', that.itemList, false)
                //   obj.successCallback && obj.successCallback(data || [])
                // })
                // obj.successCallback && obj.successCallback([])
            }, that.$constant.noun.scrollRefreshTime)
        },
        navtoGo(url, obj = {}) {
            this.$navto.push(url, obj)
        }
    },
    filters: {
        getTimeStringAutoShort2(timestamp){
        try {
            timestamp = Number(timestamp) ? Number(timestamp) : timestamp
        } catch (err) {
            
        }
        return TimeUtils.getTimeStringAutoShort2(new Date(timestamp).getTime(),true);
    }
    }
}
</script>

<style lang="scss" scoped>
.main-content{
  padding:0upx 16upx 0upx 16upx;
  background: white;
}
.un-read{
  background-color: red;
  font-size: 20upx;
  border-radius: 20upx;
  text-align: center;
  padding: 0 10upx 0 10upx;
  margin: auto;
  position: absolute;
  left: 15%;
  top: 10%;
  color: white;
}
.item {
  // padding: 20rpx;
  border-bottom: 1px solid #f4f6f8;
  &-content {
    position: relative;
    width: 100%;
    height: 172upx;
    display: flex;
    align-items: center;
  }
  &-bottom {
    display: flex;
    flex-direction: row;
    font-size: 24upx;
    align-items: center;
    padding: 20upx;
    color: #888;

    &-status {
        color: #555;
        font-size: inherit;
    }
    &-desc {
        font-size: inherit;
    }
    &-time {
        flex: 1;
        text-align: right;
        font-size: inherit;
    }
  }
  image {
    width: 108rpx;
    height: 108rpx;
    margin: 20rpx;
    border-radius: 12rpx;
    //flex: 0 0 76rpx;
  }
  .right {
    overflow: hidden;
    flex: 1 0;
    padding: 20rpx 20rpx 20rpx 0;
    &_top {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    //   height: 108rpx;
      &_name {
        display: flex;
        justify-content: space-between;
        font-size: 28rpx;
        color: #303133;
        font-weight: 550;
        //flex: 0 0 450rpx;
        overflow: hidden;
        &_content {
            display: flex;
        }
        &_desc {
          font-size: 24upx;
          color: #ff9800;
          margin-left: 8upx;
          overflow: hidden;
          border: 2upx solid #ff9800;
          border-radius: 6upx;
          padding: 0 4upx 0 4upx;
          left: 0;
        }
      }
      &_info {
        font-weight: 400;
        color: #444;
        font-size: 26rpx;
        margin-left: 24rpx;
      }
      &_time {
        font-size: 22rpx;
        color: #909399;
      }
    }
    &_center {
      display: flex;
      justify-content: space-between;
      &_name {
        font-size: 24rpx;
        color: #2196f3;
        flex: 0 0 450rpx;
        overflow: hidden;
        em{
          color: #e5e5e5;
          margin: 0 10upx 0 10upx;
        }
      }
    }
    &_btm {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 22rpx;
      color: #909399;
      padding-top: 10rpx;

      .u-line-1 {
        @include ellipsis(1);
      }
    }
  }
}
.slot-wrap {
  display: flex;
  align-items: center;
  padding: 0 30rpx;
}
.right_center_name {
  display: flex;
  flex-direction: row;
}
</style>