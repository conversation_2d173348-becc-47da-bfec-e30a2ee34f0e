<!-- 老赠险 -->
<template>
  <page>
    <view slot="content" class="content" :style="{ maxhHeight: `${scrollHeight}px`, backgroundImage: `url(${imgList.bg})` }">

      <view class="content-cell">
        <view id="ADS16486228330403862" data-v-fae5bece="" class="banner image">
          <image :src="imgList.banner" style="width: 750upx;height: 300upx;" mode="aspectFill"/>
        </view>
        <view class="content-center">
          <view
            :style="{ backgroundImage: `url(${imgList.whiteTop})` }"
            class="whiteTop"
          ></view>

          <!-- 故障图文 -->
            <view
              :style="{ backgroundImage: `url(${imgList.repair})` }"
              v-if="showError && hidenChooseGender"
              class="repair"
            ></view>
          <view v-if="hidenChooseGender">
            <view class="content-code centerC">
              <view class="tips" v-if="showError">
                {{ tips }}
              </view>
              <template v-else>
                <view>
                  <button
                    style="background: #fd830a"
                    class="button"
                    @click="receiveInsurance"
                    v-if="showInsuranceButton"
                  >
                    <span>{{ activityPutPlan.insuranceDesc }}</span>
                  </button>
                  <button
                    style="background: #409eff"
                    class="button"
                    type="primary"
                    @click="freeBtnClick"
                    v-if="showFreeButton"
                  >
                    <span>{{ activityPutPlan.abandonInsuranceDesc }}</span>
                  </button>
                </view>
                <view class="centerC" v-if="!showFreeButton && !showInsuranceButton">
                  <image :src="imgList.xiaobao" style="width: 300upx;height: 370upx;"></image>
                </view>
              </template>
            </view>
          </view>

        </view>
        <!-- 底部 -->
        <view class="content-bottom">
          <image style="height: 48upx;" :src="imgList.logo"></image>
          <view class="tips-text" @click="handleTel('************')">
            <text>遇到出袋问题请拨打客服电话：</text>
            <text>************</text>
          </view>
        </view>
      </view>

    </view>
  </page>

</template>

<script>
import env from '@/config/env'
import common from '@/common/util/main'
export default {
  components: {
  },
  name: 'Home',
  data() {
    return {
      height: '0px',
      qrCodeUrl: null,
      deviceId: null,
      showReload: false,
      scanH5QrCodeText: null,
      reloadText: '请重新扫码进入',
      accountId: null,
      appId: null,
      hidenChooseGender: true,
      scanH5QrCodeData: {},
      imgList: {
        bg: env.file_ctx + 'h5/home/<USER>',
        white: env.file_ctx + 'h5/home/<USER>',
        code: env.file_ctx + 'h5/home/<USER>',
        logo: env.file_ctx + 'h5/home/<USER>',
        xiaobao: env.file_ctx + 'h5/home/<USER>',
        repair: env.file_ctx + 'h5/home/<USER>',
        banner: env.file_ctx + 'h5/home/<USER>',
        close: env.file_ctx + 'h5/home/<USER>',
        guide: env.file_ctx + 'h5/home/<USER>',
        hand: env.file_ctx + 'h5/home/<USER>',
        tips: env.file_ctx + 'h5/home/<USER>',
        whiteTop: env.file_ctx + 'h5/home/<USER>',
      },
      showError: false,
      showHand: false,
      scrollHeight: uni.getSystemInfoSync().screenHeight,
      tips: '',
      showFreeButton: false,
      activityPutPlan: {},
      showInsuranceButton: false,
      urlParams: {},
      alipayInsuranceSwitchStatus: 2 // 是否开启按钮配置 1-是 2-否
    }
  },
  async onShow() {
    const _this = this
    await this.getUseridIsFree()
    this.checkUserFreePacketBtn()
    this.$nextTick(() => {
      // this.createJsapiSignature()
    })
  },
  async onLoad(params) {
    let launchParams = common.getKeyVal('system', 'launchParams')
    const that = this
    let deviceId = ''
    let businessInfoId = ''
    let type = 1
    if (!this.$validate.isNull(launchParams)) {
      deviceId = launchParams.deviceId
      businessInfoId = launchParams.businessInfoId || ''
      type = launchParams.type || 1
    }
    if (!this.$validate.isNull(this.$Route.query.deviceId)) {
      deviceId = this.$Route.query.deviceId
    }
    if (!this.$validate.isNull(this.$Route.query.businessInfoId)) {
      businessInfoId = this.$Route.query.businessInfoId
    }

    that.$uniPlugin.loading()

    that.deviceId = deviceId
    if (!deviceId) {
      that.showError = true
      that.tips = '设备不存在，请重新扫码！'
      that.$uniPlugin.hideLoading()
      return
    }
    // #ifdef MP-ALIPAY
      my.getAuthCode({
        scopes: 'auth_base',
        success: async (authInfo) => {
          const resp = await that.$api.common.alipayGetUserInfo({code: authInfo.authCode, deviceId, type, businessInfoId}).catch(err => {that.$uniPlugin.hideLoading()})
          const { h5Page,userId,inlinePage,isShowInsuranceButton,activityPutPlan, alipayInsuranceSwitchStatus } = resp.data
          that.urlParams = {
            ...resp.data,
            deviceId,
            freeTicket: '',
            gbUserId: userId
          }
          that.accountId = userId
          that.activityPutPlan = activityPutPlan
          that.alipayInsuranceSwitchStatus = alipayInsuranceSwitchStatus

          // 记录
          const promise1 = that.$api.common.aggregatecodeuserScanCode({scanType: 2, deviceId, accountId: that.accountId, type})
          const promise2 = that.$api.common.alipayInsertExposure({ deviceId, userId: that.accountId})
          Promise.all([promise1,promise2]).then(async res => {
            // 是否开启按钮配置
            if (that.alipayInsuranceSwitchStatus == 1) {
              // 是否显示领取保险按钮
              if (isShowInsuranceButton == 1) {
                that.showInsuranceButton = true
              } else {
                that.showInsuranceButton = false
              }

              // 是否显示免费按钮
              await that.getUseridIsFree().catch(() => {that.$uniPlugin.hideLoading()})
              this.checkUserFreePacketBtn()
              that.$uniPlugin.hideLoading()
            } else {
              that.$uniPlugin.hideLoading()
              // 领取保险
              this.receiveInsurance()
            }
          }).catch(err => {
            that.$uniPlugin.hideLoading()
          })
        },
        fail: () => {
          that.$uniPlugin.hideLoading()
          that.$uniPlugin.toast('用户授权失败')
        }
      })
    // #endif

  },
  methods: {
    checkUserFreePacketBtn(){
      if (this.alipayInsuranceSwitchStatus == 1 && this.showInsuranceButton === false && this.showFreeButton === false) {
        this.showError = true
        this.tips = '该账号今日权益已领完，可切换其他账号重新扫码！'
      }
    },
    // 领取保障
    receiveInsurance () {
      const that = this
      const {inlinePage,h5Page} = that.urlParams
      if(inlinePage === 1) {
        that.$navto.push('WebHtmlView', { src: h5Page, title: '赠险' })
      }else if (inlinePage === 2) {
        // #ifdef MP-ALIPAY
        my.ap.navigateToAlipayPage({
          path: encodeURIComponent(h5Page), // 注意只支持特定前缀的 URL，且需要整体冗余编码
          success:() => {
            console.log(h5Page)
          },
          fail:() => {
            // my.alert({ title: 'navigateToAlipayPage fail', content: JSON.stringify(res) });
          }
        });
        // #endif
      }
    },
    // 免费取袋
    async freeBtnClick () {
      const that = this
      that.$uniPlugin.loading()
      const { deviceId,freeTicket,gbUserId } = this.urlParams
      const param = {
        deviceId,
        freeTicket,
        userId:gbUserId,
        abandonPacketStatus: that.showInsuranceButton ? 1 : 2 // 1 放弃赠险领袋 2 无赠险领袋
      }
      const res = await this.$api.common.alipayFreeStartPacket(param).catch(() => {that.$uniPlugin.hideLoading()})
      that.$uniPlugin.hideLoading()
      const { packetId } = res.data
      this.$navto.push('ResultLoading', {...this.urlParams,packetId})
    },
    // 是否显示免费出袋
    async getUseridIsFree () {
      const that = this
      if (that.$validate.isNull(that.urlParams)) return
      // 是否开启按钮配置
      if (that.alipayInsuranceSwitchStatus != 1) {
        that.showFreeButton = false
      } else {
        const { deviceId,userId } = that.urlParams
        if (!userId) {
          this.$uniPlugin.toast('用户ID异常，请重新扫码！')
          return
        }
        if (!deviceId) {
          this.$uniPlugin.toast('设备ID异常，请重新扫码！')
          return
        }
        const res = await that.$api.common.alipayGetUseridIsFree({userId, deviceId})
        const {isFree, freeTicket} = res.data
        that.urlParams.freeTicket = freeTicket
        if(isFree == 1) {
          that.showFreeButton = true
        } else {
          that.showFreeButton = false
        }
      }
    },
    // 手势开始
    gtouchstart() {
      const that = this;
      this.timeOutEvent = setTimeout(function () {
        that.longPress()
      }, 500);//这里设置定时器，定义长按500毫秒触发长按事件，时间可以自己改，个人感觉500毫秒非常合适
      return false;
    },
    //手释放，如果在500毫秒内就释放，则取消长按事件，此时可以执行onclick应该执行的事件
    gtouchend() {
      clearTimeout(this.timeOutEvent);//清除定时器
      if (this.timeOutEvent != 0) {
        //这里写要执行的内容（尤如onclick事件）
        // vm.goChat(item);
      }
      return false;
    },
    //如果手指有移动，则取消所有事件，此时说明用户只是要移动而不是长按
    gtouchmove() {
      clearTimeout(timeOutEvent);//清除定时器
      this.timeOutEvent = 0;

    },
    //真正长按后应该执行的内容
    longPress() {
      this.timeOutEvent = 0;
      if (this.getAdsKey().length === 0) {
        return false
      }
      updateAccountRecommendWxIdCache({ accountId: this.accountId, wxId: this.wxId }).then(res => {
        console.log('updateAccountRecommendWxIdCache', res)
      }).catch((err) => {
        console.log("updateAccountRecommendWxIdCache error:", err)
      })
    },
    loadImg(val) {
      console.log('====val')
      this.height = val.path[0].height + 'px'

    },
    handleTel (val) {
      uni.makePhoneCall({
        phoneNumber: val
      })
    }
  }
}
</script>
<style>
</style>
<style lang="scss" scoped>
.button{
  border-radius: 1.3rem;
  padding: 0 32upx 0 32upx;
  color: #fff;
  &+.button {
    margin-top: 12upx;
  }
}
.content {
  width: 100vw;
  // min-height: 100vh;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  // display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  .content-cell {
    max-width: 750upx;
    height: 100%;
    padding: 40upx;
    box-sizing: border-box;
    .banner {
      max-width: 750upx;
      // height: 20vh;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      border-radius: 20upx;
      overflow: hidden;
      image {
        width: 750upx;
        border-radius: 20upx;
      }
    }
    .content-center {
      border-radius: 20upx;
      margin-top: 2vh;
      height: 60vh;
      background-color: #fff;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      position: relative;
      // overflow: hidden;
      .whiteTop {
        background-size: contain;
        background-repeat: no-repeat;
        background-position: top;
        width: 60vw;
        height: 3vh;
        position: absolute;
        top: -1vh;
      }
      .cell {
        box-sizing: border-box;
        margin-top: 40upx;
        background: #f7f1f1;
        padding: 5vw;
        border-radius: 20upx;
        // width: 95vw;
        .top {
          font-size: 36upx;
          view {
            margin-bottom: 20upx;
          }
        }
        .centerCell {
          display: flex;
          align-items: center;
          justify-content: center;
          background: #fff;
          border-radius: 10px;
          box-sizing: border-box;
          padding: 5vh 2vh;
          .imgCell {
            width: 25vw;
            position: relative;
            .choose {
              width: 40upx;
              height: 40upx;
              position: absolute;
              right: 0;
              bottom: 0;
              border: 4upx solid #3783fb;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
      }

      .centerC {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
      }
      .repair {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 60%;
        height: 60%;
        background-size: 100%;
        background-repeat: no-repeat;
        background-position: bottom;
        z-index: 10;
        border-radius: 20upx;
      }
      .scanH5QrCodeText {
        font-size: 36upx;
        margin-bottom: 25vh;
        word-break: break-all;
        line-height: 48upx;
        padding: 0 40pux;
      }

      .content-code {
        position: relative;
        .tips {
          font-size: 40upx;
          color: #383838;
          font-weight: 800;
          padding: 0 24px 20vh;
        }

        .code {
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center;
          position: relative;

          .code-img {
            position: absolute;
            .hand {
              width: 50%;
              position: absolute;
              right: -60%;
              bottom: -43%;
            }
          }
        }
      }

      .button-list {
        width: 85%;
        position: relative;
        min-height: calc(140upx + 4vh);
        .button {
          width: 85%;
          background-color: #00c08f;
          border-radius: 50upx;
          border: none;
          font-size: 32upx;
          font-weight: 800;
          height: 90upx;
          margin-top: 3vh;
          border: 1px solid #f9f9f9;
        }
        .hand {
          width: 30%;
          position: absolute;
          right: -20%;
          top: 25%;
        }
      }
    }
    .content-bottom {
      text-align: center;
      margin-top: 3vh;
      // height: 10vh;
      .tips-text {
        font-size: 28upx;
        color: #fff;
        line-height: 40upx;
        font-size: 28upx;
      }
    }
  }
}
.canttouch {
  -webkit-touch-callout: none; /* iOS Safari */

  -webkit-user-select: none; /* Chrome/Safari/Opera */

  -khtml-user-select: none; /* Konqueror */

  -moz-user-select: none; /* Firefox */

  -ms-user-select: none; /* Internet Explorer/Edge */

  user-select: none; /* Non-prefixed version, currently*/
}
</style>
