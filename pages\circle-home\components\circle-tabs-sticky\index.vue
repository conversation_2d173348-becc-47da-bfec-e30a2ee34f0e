<!-- 悬浮菜单 <tabs-sticky v-model="index" :fixed="false" @change="changeTab"></tabs-sticky> -->
<template>
  <view class="tabs-sticky">
    <view class="tab-top" @click="$emit('clickMore')" v-if="isShow">
      <text class="attention">我关注的</text>
      <view class="more"><view class="img"><image :src="file_ctx + 'static/image/business/hulu-v2/icon-circle-more-add.png'"></image></view>关注更多</view>
    </view>
    <scroll-view :style="'background:' + background" :scroll-x="overflowX" :class="{'tabs-fixed': fixed,'top':top, 'display-flex': !overflowX, 'overflow-x': overflowX, 'bdb': bdb}">
      <view class="tabs-sticky-body" :class="{'white-space': overflowX}">
        <view class="tabs-sticky-body-main">
          <view class="tab" v-for="(tab, i) in tabs" :key="i" :class="{'active': value==i, 'font-bigger': value==i&&fontBigger&&overflowX}" @click="changeTab(i)">
            <view class="img">
              <image mode="aspectFill" :src="file_ctx + tab.logoPath"/>
            </view>
            <text class="tab-name">{{tab.name}}</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  props: {
    // 数组下标
    dIndex: {
      type: [String, Number],
      default() {
        return ''
      }
    },
    background: {
      type: String,
      default() {
        // return '#fff'
        return ''
      }
    },
    fontBigger: {
      type: Boolean,
      default() {
        return false
      }
    },
    overflowX: {
      type: Boolean,
      default() {
        return false
      }
    },
    bdb: {
      type: Boolean,
      default() {
        return true
      }
    },
    tabs: {
      type: Array,
      default() {
        return []
      }
    },
    value: {
      type: Number,
      default() {
        return 0
      }
    },
    fixed: {
      type: Boolean,
      default() {
        return false
      }
    },
    top: {
      type: [Number, String],
      default() {
        return 0
      }
    },
    isShow:{
      type:Boolean,
      default:true
    },

  },
  data() {
    return {
      file_ctx: this.file_ctx
    }
  },
  methods: {
    // 切换tab
    changeTab(i) {
      if (this.value != i) {
        if (!this.$validate.isNull(this.dIndex)) {
          const obj = {
            value: i,
            index: this.dIndex
          }
          this.$emit('changeObj', obj)
          return
        }
        this.$emit('input', i)
        this.$emit('change', i)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .tab-top {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #606266;
    padding: 26rpx 0 28rpx;
    .attention{
      height: 50rpx;
      line-height: 50rpx;
      font-size: 36rpx;
      color: #1D2029;
      font-weight: 600;
      font-family: '思源黑体';
    }
    .more{
      display: flex;
      align-items: center;
      font-family: '思源黑体';
      justify-content: center;
      background: #FFFFFF;
      border-radius: 28rpx;
      font-size: 24rpx;
      color: #1D2029;
      border: 2rpx solid #D9DBE0;
      padding:10rpx 20rpx;
      .img{
        display: flex;
        width: 24rpx;
        height: 24rpx;
        margin-right: 4rpx;
        image{
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .white-space{
    white-space: nowrap;
    display: table;
  }
  .display-flex{
    display: flex;
    view{
      display: flex;
      width: 100%;
      .tab{
        text{
          flex: 1;
        }
      }
    }
  }
  .overflow-x{
    .tab{
      text{
        color: #1D2029;
        font-size: 26upx;
      }
    }
    .font-bigger{
      color: #333 !important;
      font-weight: bold !important;
      font-size: 36upx !important;
      text{
        color: 28upx;
      }
    }
  }
  .tabs-sticky{
    width: 100%;
    font-size: 26upx;
    text-align: center;
    z-index: 99;
    padding: 0 32upx;
    box-sizing: border-box;
    .tabs-sticky-body{
      &-main {
        display: flex;
        &::-webkit-scrollbar {
          display: none;
        }
      }

      .active{
        color: $topicC;
        background: linear-gradient( 90deg, #FFFFFF 0%, #D7FAF1 100%) !important;
        border-radius: 16rpx;
        border: 2rpx solid #00B484 !important;
        text {
          color: $topicC;
        }
        .img{
          filter: grayscale(0) !important;
        }
      }
      .tab{
        display: flex;
        position: relative;
        align-items: center;
        justify-content: center;
        padding: 22rpx 24rpx;
        background: #f3fcfa;
        border-radius: 16rpx;
        margin-right: 16rpx;
        border: 2rpx solid #D2E5E0;
        .img{
          display: flex;
          width: 40rpx;
          height: 40rpx;
          margin-right: 5rpx;
          filter: grayscale(1);
          image{
            width: 100%;
            height: 100%;
          }
        }
        &:last-child{
          margin-right: 0;
        }
      }
    }
  }
  /*悬浮样式*/
  .tabs-sticky.tabs-fixed{
    z-index: 2;
    position: fixed;
    top: 88upx;
    left: 0;
    width: 100%;
  }

  .tab-name {
    white-space: nowrap;
  }
</style>
