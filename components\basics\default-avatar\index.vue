<!--展示默认头像组件-->
<template>
  <view
    class="default-avatar-main"
    :style="config.itemClass"
    :class="[defaultConfig.widthHeightAuto?'w-h-auto':'w-h-90']">
    <view
      v-if="isShowName"
      class="default-name"
      :class="{'bd-e5e5e5':bdSolid}">
      <text class="text" :style="{'font-size':fontS}">
        {{form.data.name}}
      </text>
    </view>
    <image
      :mode="config.mode"
      :lazy-load="config.lazyLoad"
      :src="form.data.src"
      @error="imageError"
      class="role-image-init"
      :class="{'bd-e5e5e5':bdSolid}"
      v-else />
  </view>

</template>

<script>
  export default {
    data() {
      return {
        // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
        $common: this.$common,
        $static_ctx: this.$static_ctx,
        $validate: this.$validate,
        form: {
          data: {
            src: '',
            name: ''
          }
        },
        defaultConfig: {
          bd: true, // 圆圈线
          widthHeightAuto: false, // 宽高是否开启100%
          label: '默认',
          mode: 'aspectFill', // 图片裁剪、缩放的模式
          lazyLoad: false, // 图片懒加载
          fadeShow: true, // 图片显示动画效果
          itemClass: {},
          defaultAvatar: this.$static_ctx + 'image/system/avatar/icon-default-avatar.png'
        },
        isShowName: false // 判断没有图片地址，有cName，获取最后两位显示
      }
    },
    props: {
      config: {
        type: Object,
        required: false,
        default: () => {
          return {}
        }
      },
      cData: {
        type: String,
        required: false,
        default: ''
      },
      cName: {
        type: String,
        required: false,
        default: ''
      },
      // 文字大小
      fontS: {
        type: String,
        required: false,
        default: '28upx'
      },
      bdSolid: {
        type: Boolean,
        required: false,
        default: true
      }
    },
    watch: {
      cData: {
        handler(val) {
          this.watchDataMain()
        },
        immediate: true,
        deep: true
      },
      cName: {
        handler(val) {
          this.form.data.name = val
          this.watchDataMain()
        },
        immediate: true,
        deep: true
      },
      config: {
        handler(val) {
          this.copyConfig()
        },
        immediate: true,
        deep: true
      }
    },
    mounted() {
      this.watchDataMain()
    },
    methods: {
      watchDataMain() {
        this.isShowName = false
        this.copyConfig()
        if (this.$validate.isNull(this.cData)) {
          if (this.$validate.isNull(this.cName)) {
            this.form.data.src = this.defaultConfig.defaultAvatar
          } else {
            this.isShowName = true
            if (this.cName.length > 2) {
              this.form.data.name = this.cName.substring(this.cName.length - 2)
            } else {
              this.form.data.name = this.cName
            }
          }
        } else {
          this.form.data.src = this.$common.getHeadImage(this.cData)
        }
      },
      /**
       * 初始化拷贝config对象
       */
      copyConfig() {
        const that = this
        const obj = JSON.parse(JSON.stringify(that.config))
        Object.keys(obj).forEach(function(key) {
          that.defaultConfig[key] = obj[key]
        })
        that.defaultConfig = Object.assign({}, that.defaultConfig)
      },
      /**
       * 异常登陆
       */
      imageError(e) {
        this.form.data.src = this.defaultConfig.defaultAvatar
      }
    }
  }
</script>
<style lang="scss" scoped>
  .w-h-90{
    width: 90upx;
    height: 90upx;
    line-height: 90upx;
  }
  .w-h-auto{
    width: 100%;
    height: 100%;
  }
  .bd-e5e5e5{
    border: 2upx solid #e5e5e5;
  }
  .default-name{
    position: relative;
    @include rounded(50%);
    vertical-align:middle;
    display: inline-block;
    overflow: hidden;
    box-sizing: border-box;
    text-align: center;
    background-color: #ffffff;
    height: 100%;
    width: 100%;
    .text{
      font-weight: bold;
      color: $topicC;
      position: absolute;
      top: 50%;
      left: 0;
      width: 100%;
      text-align: center;
      -webkit-transform: translateY(-50%);
      transform: translateY(-50%);
    }
  }
  .role-image-init{
    @include rounded(50%);
    vertical-align:middle;
    display: inline-block;
    overflow: hidden;
    box-sizing: border-box;
    height: 100%;
    width: 100%;
  }

</style>
