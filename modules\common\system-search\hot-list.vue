<template>
  <view class="list">
    <view class="list-item" v-for="(item,index) in hotList" :key="index" @tap="clickItem(item)">
      <view v-if="index === 0" class="item-prefix one"></view>
      <view v-else-if="index === 1" class="item-prefix two"></view>
      <view v-else-if="index === 2" class="item-prefix three"></view>
      <view v-else class="item-prefix"></view>
      <text class="item-title">{{ item.title }}</text>
      <text class="item-read-num">{{ item.readNumberText }}</text>
      <text class="item-hot-trent">-</text>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
export default {
  data() {
    return {
      $common: this.$common,
      hotList: []
    }
  },
  computed: {
    ...mapState('user', {
      accountId: state => state.accountId
    })
  },
  methods: {
    clickItem(data) {
      this.navtoGo('PostsDetail', {id: data.id})
    },
    init() {
      this.postmessageQueryEssencePage()
    },
    async postmessageQueryEssencePage() {
      let params = {
        current: 1,
        size: 10,
        condition: {
          accountId: this.accountId
        }
      }
      const res = await this.$ext.community.postmessageQueryEssencePage(params)
      this.hotList = res.data.records.map(item => {
        return {
          ...item,
          readNumberText: this.$common.bigNumberTransform(+item.virtualReadNumber + item.readNumber)
        }
      })
    },
    navtoGo(url, obj = {}) {
			this.$navto.push(url, obj)
		},
  }
}
</script>

<style lang="scss" scoped>
.list {
  padding: 0 30upx 10upx 30upx;
  &-item {
    display: flex;
    align-items: center;
    padding: 24rpx 0;
    border-bottom: 1px solid #e4e4e4;
    .item-prefix {
      width: 12rpx;
      height: 12rpx;
      background-color: #ccc;
      border-radius: 50%;
      margin: 0 24rpx;
      &.one {
        background-color: #f17462;
      }
      &.two {
        background-color: #fcb257;
      }
      &.three {
        background-color: #fddc4c;
      }
    }
    .item-title {
      flex: 1;
      font-size: 28rpx;
      color: #333;
      vertical-align: middle;
      @include ellipsis(1);
    }
    .item-read-num {
      color: #cbcbcb;
      padding: 0 24rpx;
    }
    .item-hot-trent {
      color: #cbcbcb;
    }
  }
}
</style>