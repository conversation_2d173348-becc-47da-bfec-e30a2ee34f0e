<template>
  <page>
    <!-- 基础信息、历史病况、诊断疾病、其他报告 -->
    <view slot="content" class="main-body">

      <view class="content-t">
        <view class="content-title">
          基础信息
        </view>
        <view class="content-tip">
          ( 带
          <text class="red">*</text>
           内容必填 )
        </view>
      </view>

      <view class="form-content">
        <title-radio v-model="form.memberType" :config="config.memberType" />
        <title-input v-model="form.name" horizontal :config="config.name" :disabled="config.name.disabled"></title-input>
        <title-input v-model="form.phone" horizontal :config="config.phone" :disabled="config.phone.disabled"></title-input>
        <title-input v-model="form.idNumber" horizontal :config="config.idNumber" :disabled="config.idNumber.disabled"></title-input>
        <title-jump-date v-model="form.birthDate"  title="出生日期" :endDate="$timePlugin.parseTime(new Date(), '{y}-{m}-{d}')"></title-jump-date>
        <title-input v-model="form.origo" horizontal :config="config.origo"></title-input>
        <title-input v-model="form.address" horizontal :config="config.address"></title-input>
        <title-radio v-model="form.gender" :config="config.gender" />
        <title-selector v-model="form.familyTies" :config="config.familyTies" />

        <view class="img-title">
            个人照：
        </view>
        <title-img
          :config="config.headPath"
          @returnFn="(obj) => {imgReturnFn(obj,'headPath')}"
          :cData="cDataHeadPath"
        ></title-img>
      </view>




      <view class="content-t">
        <view class="content-title">
          历史病况
        </view>
        <view class="content-tip">
          ( 带
          <text class="red">*</text>
           内容必填 )
        </view>
      </view>


      <view class="form-content">
        <title-textarea v-model="form.previousHistory" :config="config.previousHistory" />
        <title-textarea v-model="form.familyHistory" :config="config.familyHistory" />
        <title-textarea v-model="form.allergicHistory" :config="config.allergicHistory" />
      </view>

      <view class="content-t">
        <view class="content-title">
          诊断疾病
        </view>
        <view class="content-tip">
          ( 带
          <text class="red">*</text>
           内容必填 )
        </view>
      </view>

      <view class="form-content">
        <title-textarea v-model="form.principalDisease" :config="config.principalDisease" />
        <title-input horizontal v-model="form.concomitantDisease" :config="config.concomitantDisease" />
        <title-input horizontal v-model="form.disease_time" :config="config.disease_time" />
        <title-textarea v-model="form.mainSuit" :config="config.mainSuit" />
       <!-- concomitantDisease:'',
        disease_time:'',
        mainSuit:'' -->
        <!-- <title-textarea v-model="form.familyHistory" :config="config.familyHistory" /> -->
        <!-- <title-textarea v-model="form.allergicHistory" :config="config.allergicHistory" /> -->
      </view>



      <view class="content-t">
        <view class="content-title">
          处方单/发票信息
        </view>
        <view class="content-tip">
          ( 带
          <text class="red">*</text>
           内容必填 )
        </view>
      </view>

      <view class="form-content">
       <!-- <view class="img-title">
           处方单/发票信息：
        </view> -->
        <title-img
          :config="config.prescriptionPath"
          @returnFn="(obj) => {imgReturnFn(obj,'prescriptionPath')}"
          :cData="cDataprescriptionPath"
        ></title-img>
       <!-- <view class="img-title">
           检查报告：
        </view> -->

      </view>

      <view class="content-t">
        <view class="content-title">
          检查报告
        </view>
        <view class="content-tip">
          ( 带
          <text class="red">*</text>
           内容必填 )
        </view>
      </view>

      <view class="form-content">
        <title-img
          :config="config.inspectionReport"
          @returnFn="(obj) => {imgReturnFn(obj,'inspectionReport')}"
          :cData="cDatainspectionReport"
        ></title-img>
      </view>





     <!-- <view class="form-content">
        <title-radio v-model="form.memberType" :config="config.memberType" />
      </view>
      <view class="head">
        <view class="l">
        </view>
        <view class="r">
          个人身份信息
        </view>
      </view>
      <view class="form-content">
        <view class="img-title">
            个人照：
        </view>
        <title-img
          :config="config.headPath"
          @returnFn="(obj) => {imgReturnFn(obj,'headPath')}"
          :cData="cDataHeadPath"
        ></title-img>
        <title-input v-model="form.name" horizontal :config="config.name" :disabled="config.name.disabled"></title-input>
        <title-input v-model="form.phone" horizontal :config="config.phone" :disabled="config.phone.disabled"></title-input>
        <title-input v-model="form.idNumber" horizontal :config="config.idNumber" :disabled="config.idNumber.disabled"></title-input>
        <title-jump-date v-model="form.birthDate"  title="出生日期" :endDate="$timePlugin.parseTime(new Date(), '{y}-{m}-{d}')"></title-jump-date>
        <title-input v-model="form.origo" horizontal :config="config.origo"></title-input>
        <title-input v-model="form.address" horizontal :config="config.address"></title-input>
        <title-radio v-model="form.gender" :config="config.gender" />
        <title-selector v-model="form.familyTies" :config="config.familyTies" />
      </view>
      <view class="head">
        <view class="l">
        </view>
        <view class="r">
          病史情况
        </view>
      </view> -->


      <view class="btn-view-main">
        <view class="btn-view-li">
          <view class="btn-view-li-view" @tap="save()">保存档案</view>
        </view>
      </view>
    </view>
  </page>
</template>

<script>
import TitleRadio from '@/components/business/module/v1/title-radio/index.vue'
import TitleInput from "@/components/business/module/v2/title-input/index.vue"
import TitleTextarea from "@/components/business/module/v2/title-textarea/index.vue"
import TitleJumpDate from "@/components/business/module/v1/title-jump-date/index.vue"
import TitleSelectAddress from "@/modules/business/components/title-select-address/index"
import TitleImg from "@/components/business/module/title-img/index.vue"
import TitleSelector from "@/components/business/module/v1/title-selector/index.vue"
import {mapState} from "vuex"
export default {
  name: "PatientsAddTep",
  components: {
    TitleInput,
    TitleRadio,
    TitleJumpDate,
    TitleSelectAddress,
    TitleImg,
    TitleSelector,
    TitleTextarea
  },
  data () {
    return {
      query: {},
      cDataHeadPath: [],
      cDataprescriptionPath:[],
      cDatainspectionReport:[],

      form: {
        tenantId: '', // 租户id
        memberType: '', // 成员类型
        name: '', // 姓名
        phone: '', // 手机号码
        idNumber: '' ,// 身份证
        birthDate: '', // 出生日期
        origo: '', // 籍贯
        address: '', // 详细地址
        gender: '', // 性别
        familyTies: '',
        headPath: '', // 个人照
        previousHistory: '',
        familyHistory: '',
        allergicHistory: '',
        registerSource: 1, // 注册来源
        physicianUserId: '', // 邀请人
        // 是否为主账号 暂时无用
        isPrimaryAccount: 2, // 是否为主账号 1-是 2-否
        masterId: '', // 主账号id
        principalDisease:"",// 主要疾病

        concomitantDisease:'', // 伴发疾病
        disease_time:'', // 现病时长
        mainSuit:'',// 主诉
        prescriptionPath:"", // 处方单/发票信息
        inspectionReport:"",// 检查报告

      },
      $timePlugin: this.$timePlugin,
      $constant: this.$constant,
      file_ctx: this.file_ctx,
      config: {
        memberType: {label: '成员类型', required: true, array: [{ label: '普通成员', value: 1 },{ label: '新生儿', value: 2 }],nextRequest:true},
        name: {label: '真实姓名', required: true,nextRequest:true},
        phone: {label: '手机号码', required: true,nextRequest:true},
        idNumber: {label: '身份证',nextRequest:true},
        origo: {label: '籍贯',nextRequest:true},
        address: {label: '详细地址',nextRequest:true},
        gender: {label: '性别', required: true, array: [{label: '男',value: 1},{label: '女',value: 2},{label: '未知',value: 3}],nextRequest:true},
        familyTies: {label: '家庭关系', required: true, array: [
          { label: '配偶', value: 1 },
          { label: '子女', value: 2 },
          { label: '父母', value: 3 },
          { label: '兄弟', value: 4 },
          { label: '姐妹', value: 5 },
          { label: '其他', value: 6 }],
          nextRequest:true
        },
        previousHistory: {label: '既往史',inputStyle:'background:#fff'},
        familyHistory: {label: '家族史',inputStyle:'background:#fff'},
        allergicHistory: {label: '过敏史',inputStyle:'background:#fff'},
        headPath: {count: 1, multiSelectCount: 1, theCluesText: '最多只能上传1张图片'},
        principalDisease:{
          label:"主要疾病",
          required:true,
          maxlength:512,
          nextRequest:true,
          inputStyle:'background:#fff'
        },
        concomitantDisease:{
          label:"伴发疾病",
          // required:true,
          maxlength:512,
          nextRequest:true,
          inputStyle:'background:#fff'
        },
        disease_time:{
          label: '现病时长',
          // required: true,
          nextRequest:true,type:'number'
        },
        mainSuit:{
          label:"主诉",
          // required:true,
          maxlength:512,
          nextRequest:true,
          inputStyle:'background:#fff'
        },
        prescriptionPath:{label: '处方单/发票信息',nextRequest:true}, // 处方单/发票信息
        inspectionReport:{label: '检查报告',nextRequest:true},// 检查报告


      }
    }
  },
  onLoad() {
    this.query = this.$Route.query
    if(!this.$validate.isNull(this.query)) {
      this.getDetail()
    }
  },
  mounted () {
    this.getSmallprogramqrcodeGetCodeByScene()
    if(!this.$validate.isNull(this.curSelectUserInfo)) {
      this.form.phone = this.curSelectUserInfo.phone
      // 默认为主账号
      this.form.isPrimaryAccount = 1
    }
    if (!this.$validate.isNull(this.recordUserInfo)) this.form.masterId = this.recordUserInfo.id
  },
  computed: {
    ...mapState('user', {
      recordUserInfo: state => state.recordUserInfo,
      curSelectUserInfo: state => state.curSelectUserInfo
    }),
    storeList () {
      return this.$common.getKeyVal('user', 'storeList', true)
    },
    scene () {
      return this.$common.getKeyVal('system', 'scene', true)
    },
    shareUserId () {
      return this.$common.getKeyVal('system', 'shareUserId',true)
    }
  },
  methods: {
    async save () {
      const that = this
      // 表单校验
      for(const key in this.form) {
        if(!this.$validate.isNull(this.config[key])) {
          if(this.config[key].required && !this.form[key]) {
            this.$uniPlugin.toast(`${this.config[key].label}不得为空！`)
            return
          }
        }
      }

      const isPhone = that.$validate.isMobile(that.form.phone)
      if (!isPhone.flag) {
        this.$uniPlugin.toast(isPhone.msg)
        return
      }

      let url = ''
      if(!this.$validate.isNull(this.query)) {
        url = this.$api.patientinfo.patientinfoUpdate
        this.form.id = this.query.id
      } else {
        url = this.$api.patientinfo.patientinfoInsert
      }

      that.$uniPlugin.loading('正在提交', true)
      const res = await url(this.form).catch(err => {
        that.$uniPlugin.hideToast()
      })

      // 没有档案的情况下新增，重新获取档案
      if(this.$validate.isNull(this.recordUserInfo)) {
        await this.$ext.user.getRecordUserInfo()
      }

      setTimeout(() => {
        that.$uniPlugin.hideToast()
        that.$navto.back(1)
      }, that.$constant.noun.delayedOperationTime)
    },
    getSmallprogramqrcodeGetCodeByScene () {
      if(!this.$validate.isNull(this.scene)) {
        this.$api.common.smallprogramqrcodeGetCodeByScene({scene: this.scene.sceneCode}).then(res => {
          if(!this.$validate.isNull(res.data)) {
            const { userId,tenantId } = res.data
            // 邀约注册
            this.form = {
              ...this.form,
              registerSource: 2,
              physicianUserId: userId,
              tenantId: tenantId
            }
          }
        })
      }
    },
    async getDetail () {
      const {id} = this.query
      const res = await this.$api.patientinfo.patientinfoQueryOne({id})
      if(!this.$validate.isNull(res.data)) {
        for(const key in this.form) {
          if(key === 'birthDate') {
            this.form[key] = res.data[key] ? this.$timePlugin.parseTime(new Date(res.data[key]), '{y}-{m}-{d}') : ''
          } else if (key === 'headPath') {
            this.form[key] = res.data[key]
            if(!this.$validate.isNull(this.form.headPath)) {
              this.cDataHeadPath = [{url: this.file_ctx + this.form.headPath}]
              this.$forceUpdate()
            }
          } else if (['prescriptionPath'].includes(key)) {
            if (!this.$validate.isNull(res.data[key])) {
              this.cDataprescriptionPath = res.data[key].split(',').map(item => {
                return {
                  dir: item,
                  url: this.file_ctx + item
                }
              })
            }
            this.form[key] = res.data[key]
          } else if (['inspectionReport'].includes(key)) {
            if (!this.$validate.isNull(res.data[key])) {
              this.cDatainspectionReport = res.data[key].split(',').map(item => {
                return {
                  dir: item,
                  url: this.file_ctx + item
                }
              })
            }
            this.form[key] = res.data[key]
          } else {
            this.form[key] = res.data[key]
          }
        }
        this.$set(this.config, 'phone', {...this.config.phone, disabled: true})
      }
    },
    imgReturnFn (obj,key) {
      this.form[key] = this.$validate.isNull(obj) ? '' : obj[0].dir
    }
  }
}
</script>

<style lang="scss" scoped>


  .main-body{
    // padding:0 20upx
  }
  .content-t{
    height: 100upx;
    display: flex;
    align-items: center;
    background-color: #fff;
    border-bottom: 1upx solid $borderColor;
    // margin: 0 20upx;
    padding: 0 20upx;
      .content-title{
        font-size: 32upx;
        font-weight: 550;
        padding-left: 20upx;
        border-left: 5upx solid $topicC;
        line-height: 1.5;
        // height: 80%;
      }
      .content-tip{
        font-size: 24upx;
        margin-left: 10upx;
      }
      .red{
        color: red;
      }
  }


  .head {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    display: -webkit-flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    -webkit-justify-content: flex-start;
    justify-content: flex-start;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
    padding: 24upx 24upx 16upx;
    .l{
      width: 10upx;
      height: 32upx;
      background: #00D29D;
      border-radius: 1px;
    }
    .r{
      margin-left: 10upx;
    }
  }
  .form-content {
    padding: 0 24upx;
    background-color: #fff;
    .img-title {
      padding-top: 24upx;
    }
  }
  .btn-view-main{
    bottom: 0;
    padding: 20upx 0 64upx;
    height: 164upx;
    box-sizing: border-box;
    text-align: center;
    z-index: 2;
    .btn-view-li{
      display: inline-block;
      vertical-align: middle;
      box-sizing: border-box;
      overflow: hidden;
      width: 100%;
      padding: 0 30upx;
      margin: 0 auto;
      .btn-view-li-view{
        height: 64upx;
        line-height: 64upx;
        font-size: 32upx;
        text-align: center;
        @include rounded(32upx);
        //background:linear-gradient(90deg,rgba(255,185,36,1),rgba(254,219,55,1));
        background: $topicC;
        color: #fff;
      }
      .border-color-but{
        border: 2upx solid $topicC;
        color: $topicC;
        background: none;
      }
    }
  }
</style>
