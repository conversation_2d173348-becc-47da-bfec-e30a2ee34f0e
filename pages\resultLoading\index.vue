<template>
  <view class="content">
    <image :src="background" style="width: 100%;" mo></image>
    <view class="progress" v-if="showProgress">
      <progress
        :percent="percent"
        :stroke-width="40"
        border-radius="40upx"
        activeColor="#ee9e2f"
      />
    </view>
    <view class="tips">
      {{ tips }}
    </view>
    <view class="call">
      <text class="call-item">呼叫小葆:</text>
      <text class="call-item" @click="handleTel('************')">************</text>
    </view>
  </view>
</template>
<script>
import env from '@/config/env'
import { getQueryStr } from '@/utils/index'

// import { alipayGetPacketRecordReceiveStatus, attentionOutPacketByIpInfo } from '@/utils/api/index'


export default {
  data() {
    return {
      background: env.file_ctx + 'h5/background.jpg',
      percent: 0,
      tips: '出袋中...',
      showProgress: false,
      wxInfo: null,
      packetId: null,
      requestNum: 0,
      requestTime: 5000,
      time1: null,
      timer: null
    }
  },
  beforeDestroy() {
    this.percent = 0
    clearInterval(this.time1)
    this.time1 = null
    clearInterval(this.timer)
    this.timer = null
  },
  methods: {
    handleTel (val) {
      uni.makePhoneCall({
        phoneNumber: val
      })
    },
    start() {
      this.alipayGetPacketRecordReceiveStatus()
      this.showProgress = true
      clearInterval(this.time1)
      this.time1 = setInterval(() => {
        if (this.percent < 90) {
          this.percent += 1
        } else {
          clearInterval(this.time1)
        }
      }, 100)

    },
    // getWxInfo() {
    //   const authAssociateId = getQueryStr('authAssociateId')
    //   const authOpenId = getQueryStr('authOpenId')
    //   const wxInfo = {
    //     code: getQueryStr('code'),
    //     openId: getQueryStr('openId'),
    //     wxId: getQueryStr('wxId'),
    //     authAssociateId,
    //     authOpenId
    //   }
    //   this.wxInfo = wxInfo
    //   this.attentionOutPacketByIpInfo()
    // },
    // attentionOutPacketByIpInfo() {
    //   const wxInfo = this.wxInfo
    //   this.$uniPugin
    //   this.$uniPlugin.loading({
    //     message: '加载中...',
    //     forbidClick: true,
    //     overlay: true,
    //     duration: 0
    //   })
    //   this.$api.common.attentionOutPacketByIpInfo(wxInfo).then(res => {
    //     this.$uniPlugin.hideLoading()
    //     if (res.data.packetId) {
    //       this.tips = '出袋中'
    //       this.packetId = res.data.packetId
    //       this.start()
    //     } else {
    //       if (res.data.errorMsg) {
    //         this.$uniPlugin.model
    //         this.$uniPlugin.modal('',res.data.errorMsg)
    //       } else {
    //         this.tips = '请扫设备上二维码取袋！'
    //       }

    //     }
    //   }).catch(err => {
    //     this.tips = '请扫设备上二维码取袋！'
    //     console.log(err)
    //     this.$uniPlugin.modal('',err.data.msg)
    //     this.$uniPlugin.hideLoading()
    //   })
    // },
    alipayGetPacketRecordReceiveStatus() {
      const packetId = this.packetId
      if (this.requestNum >= 22) {
        this.tips = '对不起，出袋失败，给您造成不便，非常抱歉！如需重新取袋，请重新扫码！'
        return
      } else {
        this.requestNum += 1
      }
      this.$api.common.alipayGetPacketRecordReceiveStatus({ packetId }).then(res => {
        // if(this.$validate.isNull(res.data)) {
        //   this.tips = '出袋中'
        //   setTimeout(() => {
        //     this.alipayGetPacketRecordReceiveStatus()
        //   }, this.requestTime)
        //   return
        // }
        const {receiveStatus = 3} = res.data
        switch (receiveStatus) {
          case 1:
            this.requestNum = 0
            this.percent = 100
            this.tips = '出袋完成，请轻拉取袋，感谢对环保的支持'
            break;
          case 2:
            this.requestNum = 0
            this.percent = 100
            this.tips = '对不起，出袋失败，给您造成不便，非常抱歉！如需重新取袋，请重新扫码！！'
            break;
          case 3:
            this.tips = '出袋中'
            this.timer = setTimeout(() => {
              this.alipayGetPacketRecordReceiveStatus()
            }, this.requestTime)
            break;
          case 4:
            this.requestNum = 0
            this.percent = 100
            this.tips = '对不起，出袋失败，给您造成不便，非常抱歉！如需重新取袋，请重新扫码！'

            break;
          case 5:
            this.requestNum = 0
            this.percent = 100
            this.tips = '对不起，出袋失败，给您造成不便，非常抱歉！如需重新取袋，请重新扫码！'

            break;

          default:
            break;
        }

      })
    }
  },
  async onLoad() {
    // this.getWxInfo()
    const { deviceId,freeTicket,gbUserId,packetId } = this.$Route.query
    this.packetId = packetId
    if(this.packetId == 0) {
      this.requestNum = 0
      this.percent = 100
      this.tips = '对不起，出袋失败，给您造成不便，非常抱歉！如需重新取袋，请重新扫码！！'
      return
    }

    this.start()
  }
}
</script>
<style lang="scss" scoped>
.content {
    background: #5CD7AC;
    height: 100vh;
    .tips {
        padding: 20px;
        font-size: 25px;
        color: #fff;
    }
    .progress {
        padding: 40px 20px;
        .a-progress {
          border-radius: 32upx;
          overflow: hidden;
        }
    }
    .call {
        position: fixed;
        bottom: calc(5vh + env(safe-area-inset-bottom));
        width: 100vw;
        display: flex;
        align-items: center;
        justify-content: center;

        .call-item {
          color: #fff;
          line-height: 20px;
          font-size: 14px;
        }
    }
}
</style>
