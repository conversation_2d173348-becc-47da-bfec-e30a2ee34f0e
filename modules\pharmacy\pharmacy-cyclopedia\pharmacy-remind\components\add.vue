<template>
  <view class="add">
    <view class="add-content">
      <view class="add-title">
        <view class="label">{{labelValue ? '添加产品' : '添加药品'}}</view>
        <view class="sky" v-if="disabled">
          <view class="sky-text">{{queryParams.productName}}</view>
        </view>
        <view class="sky" v-else><input class="my-input" v-model="queryParams.productName" :maxlength="18" placeholder-style="color:#b3b3b3" placeholder="请输入" /></view>
      </view>
      <view class="drug-time">
        <my-picker mode="selector" :label="labelValue ? '服用频次' : '用药频次'" :range="frequencyOptions" v-model="queryParams.times"></my-picker>
        <template v-if="queryParams.times !='请选择'">
          <view  v-for="(item,index) in queryParams.times" :key="index">
            <view class="line"></view>
            <template v-if="queryParams.times > 1">
              <my-picker v-if="detailObj.id" mode="time" :label="labelValue ? '服用时间'+(index+1) : '用药时间'+(index+1)" start="08:00" v-model="remindTimeArr[index].remindTime"></my-picker>
              <my-picker v-else mode="time" :label="labelValue ? '服用时间'+(index+1) : '用药时间'+(index+1)" start="08:00" v-model="remindTimeArr[index]"></my-picker>
            </template>
            <template v-else>
              <my-picker v-if="detailObj.id" mode="time" :label="labelValue ? '服用时间' : '用药时间'" start="08:00" v-model="remindTimeArr[index].remindTime"></my-picker>
              <my-picker v-else mode="time" :label="labelValue ? '服用时间' : '用药时间'" start="08:00" v-model="remindTimeArr[index]"></my-picker>
            </template>
          </view>
        </template>
      </view>
      <view class="options">
        <view class="title">可选项</view>
        <view class="options-content">  
          <view class="input-container">
            <view class="label">单次用量</view>
            <view class="sky"><input class="my-input" v-model="queryParams.numOfTake" :maxlength="10" placeholder-style="color:#b3b3b3" placeholder="输入用量" /></view>
          </view>
          <view class="line"></view>
          <!-- <my-picker mode="date" label="开始日期" :start="startDate" v-model="queryParams.beginTime"></my-picker> -->
          <my-picker mode="date" label="开始日期" :start="startDate" :end="endDate" v-model="queryParams.beginTime"></my-picker>
          <view class="line"></view>
          <view class="input-container">
            <view class="label">{{labelValue ? '服用天数' : '用药天数'}}</view>
            <view class="sky"><input class="my-input" v-model="queryParams.medicationDays" type="number" placeholder-style="color:#b3b3b3" placeholder="输入天数" /><span>天</span></view>
          </view>        
        </view>
      </view>
      <view class="drug-user">
        <my-picker mode="selector" :label="labelValue ? '服用人' : '用药人'" :range="frequencyOptions2" v-model="queryParams.takeMedicineUser"></my-picker>
      </view>
      <view class="remark">
        <textarea rows="2" v-model="queryParams.remark" placeholder-style="color:#b3b3b3" placeholder="备注"/>
      </view>
    </view>
    <view class="botton-btn">
      <button @click="confirm">确认</button>
    </view>
  </view>

</template>

<script>
  import { mapState } from 'vuex'
  import myPicker from "@/components/business/module/v1/my-picker/index"
  export default {
    components: {
      myPicker,
    },
    // #ifdef MP-ALIPAY
    options: {
      virtualHost: true, // 确保支付宝正确处理组件
      multipleSlots: true
    },
    // #endif
    data(){
      return{
        // 用药频次选项  
        frequencyOptions: [1,2,3],  
        frequencyOptions2:['自己','他人'],  
        queryParams:{
          productId:'',
          productName:'',
          remindType:1, // 用药频次
          times:'请选择', // 每天次数-用药频次
          remindTime:'08:00', // 用药时间
          numOfTake:'', // 	单次用量
          beginTime:this.startDate, // 	开始日期
          medicationDays:'', // 	用药天数
          takeMedicineUser:'自己', // 用药人
          remark:'', //	备注
        },
        remindTimeArr:[],
        submitLoading: false,
        unionid:null,
        detailObj:null,
        timesTemp:null,
        disabled:false,
      }
    },
    mounted(options){
      if (this.$Route.name !== 'PharmacyAdd') return
      const query = this.$validate.isNull(this.$Route.query) ? options : this.$Route.query
      this.detailObj = query
      this.clearData(query.productId,query.productName)
      if(query.productName){
        this.disabled = true
      }
      if(this.detailObj.id){
        uni.setNavigationBarTitle({
          title: '更新提醒'
        });

        this.pharmacyRemindQueryOne()
      }
    },

    computed: {
      ...mapState('user', {
        // publishInfo:state => state.publishInfo,
        accountId: state => state.accountId,
        recordUserInfo: state => state.recordUserInfo, // 当前登录用户信息
        curSelectUserInfo: state => state.curSelectUserInfo
      }),
      startDate() {
        return this.getCurrentformatDate(new Date())
      },
      endDate() {
        return this.getCurrentformatDate(new Date(),10)
      },
      labelValue() {
        let flag = false
        // if(this.detailObj?.productId == '2031448999104024578' || this.detailObj?.productId == '2031353194431111174'){
        if(this.detailObj?.drugType == 2){
          flag = true
        }
        return flag 
      },
    },
    watch: {
      'queryParams.times':{
        handler(){
          if(this.queryParams.times != '请选择'){
            // if(this.timesTemp != this.queryParams.times){
            //   this.remindTimeArr = []
            // }
            if(this.queryParams.times == 1){
              this.remindTimeArr = this.remindTimeArr[0] && [this.remindTimeArr[0]] || ['08:00']
            } else if(this.queryParams.times == 2){
              if(this.detailObj?.id){
                if(this.remindTimeArr.length == 1){
                  this.remindTimeArr = [this.remindTimeArr[0],{id:2,remindTime:'12:00'},{id:3,remindTime:'19:00'}]  
                } else {
                  this.remindTimeArr = [{id:2,remindTime:'08:00'},{id:3,remindTime:'12:00'}]
                }
              } else {
                this.remindTimeArr = this.remindTimeArr[0] && [this.remindTimeArr[0],this.remindTimeArr[1] || '19:00'] || ['08:00','19:00']
              }
            } else if(this.queryParams.times == 3){
              if(this.detailObj?.id){
                if(this.remindTimeArr.length == 1){
                  this.remindTimeArr = [this.remindTimeArr[0],{id:2,remindTime:'12:00'},{id:3,remindTime:'19:00'}]  
                } else if(this.remindTimeArr.length == 2){
                  this.remindTimeArr = [this.remindTimeArr[0],this.remindTimeArr[1],{id:3,remindTime:'19:00'}]
                } else if(this.remindTimeArr.length == 3){
                  this.remindTimeArr = [this.remindTimeArr[0],this.remindTimeArr[1],this.remindTimeArr[2]]
                } else {
                  this.remindTimeArr = ['08:00','12:00','19:00']
                }
              } else {
                this.remindTimeArr = this.remindTimeArr[0] && [this.remindTimeArr[0],this.remindTimeArr[1] || '12:00',this.remindTimeArr[2] || '19:00'] || ['08:00','12:00','19:00']
              }
            }
          }else{
            this.remindTimeArr = []
          }
        },
        deep:true
      }
    },
    methods:{
      clearData(id,name){
        Object.assign(this.queryParams,{
          productId:id || null,
          productName:name || '',
          remindType:1, // 用药频次
          times:'请选择', // 每天次数-用药频次
          remindTime:'08:00', // 用药时间
          numOfTake:'', // 	单次用量
          beginTime:this.startDate, // 	开始日期
          medicationDays:'', // 	用药天数
          takeMedicineUser:'自己', // 用药人
          remark:'', //	备注
        });
      },

      async confirm(){
        if (this.submitLoading) return
        if(this.queryParams.times == '请选择'){
          return uni.showToast({
          	title: '请添加用药频次',
            icon:'none'
          });
        } else if(this.queryParams.remindTime == ''){
          return uni.showToast({
          	title: '请添加用药时间',
          	icon:'none'
          });
        } else if(this.queryParams.numOfTake == ''){
          return uni.showToast({
          	title: '请添加单次用量',
          	icon:'none'
          });
        } else if(this.queryParams.beginTime == ''){
          return uni.showToast({
          	title: '请添加开始日期',
          	icon:'none'
          });
        } else if(this.queryParams.medicationDays == ''){
          return uni.showToast({
          	title: '请添加用药天数',
          	icon:'none'
          });
        } else if(this.queryParams.takeMedicineUser == ''){
          return uni.showToast({
          	title: '请添加用药人',
          	icon:'none'
          });
        }
        this.submitLoading = true
        uni.showLoading({
          title: ''
        });
        
        const unionid = await this.$ext.wechat.getUnionId()
        const { centerUserId = '' } = this.curSelectUserInfo || {}
        
        let params = {
          ...this.queryParams,
          accountId:this.accountId,
          unionId:unionid,
          userId:centerUserId,
          remindTime:this.detailObj?.id ? this.remindTimeArr : this.remindTimeArr.join('-'),
          takeMedicineUser:this.queryParams.takeMedicineUser == '自己' ? 1 : 2,
          medicationDays:Number(this.queryParams.medicationDays),
          numOfTake:this.queryParams.numOfTake,

        }
        let res = null
        if(this.detailObj?.id){
          params = {...params,id:this.detailObj?.id,executeStatus:2}
          res = await this.$api.drugBook.pharmacyRemindUpdate(params)
          uni.hideLoading();
          this.$navto.push('PharmacyDetail',this.detailObj)
          this.submitLoading = false
        } else {
          res = await this.$api.drugBook.pharmacyRemindInsert(params)
          let takeMedicineUser = params.takeMedicineUser == 1 ? '自己' : '他人'
          // #ifdef MP-WEIXIN
          getApp().globalData.sensors.track("MedicationReminder",
            {
              'add_medicine' : params.productName,
              'medicine_id' : params.productId,
              'dosage_frequency' : params.times,
              'dosage_time' : params.remindTime?.split('-')?.join(' '),
              'dosage_amount' : params.numOfTake,
              'start_date' : params.beginTime,
              'medicine_days' : params.medicationDays,
              'medicine_user' : takeMedicineUser,
              'notes' : params.remark,
            }
          ) 
          // #endif
          uni.hideLoading();
          // this.close('query')
          this.$navto.push('PharmacyRemind',this.detailObj)
          this.submitLoading = false
        }
      },

      close(type){
        this.$emit('handleHide',type)
      },


      // 数据回显
      pharmacyRemindQueryOne(){
        this.$api.drugBook.pharmacyRemindQueryOne({id:this.detailObj.id}).then(res=>{
          res.data = {...res.data,beginTime:this.formatDate(res.data.beginTime)}
          Object.keys(res.data).forEach(key => {
            if (key in this.queryParams) {
              switch (key) {
                case 'times':
                  this.queryParams[key] = res.data[key]
                  this.timesTemp = res.data[key]
                  this.remindTimeArr = JSON.parse(res.data['remindTime'])
                  break;
                case 'takeMedicineUser':
                  this.queryParams[key] = this.frequencyOptions2[res.data[key]-1]
                  break;
                default:
                  // 直接赋值其他字段
                  this.queryParams[key] = res.data[key]
              }
            }
          });
        })
      },
      
      //获取当前的时间
      getCurrentformatDate(date,num = 0) {  
        const year = date.getFullYear() + num;  
        const month = this.padZero(date.getMonth() + 1); // 月份是从0开始的，所以要+1  
        const day = this.padZero(date.getDate());  
        return `${year}-${month}-${day}`;  
      },  

      padZero(num) {  
        return num < 10 ? '0' + num : num;  
      },  

      // 时间戳转换为日期格式
      formatDate(timestamp) {  
        // 创建一个Date对象  
        let date = new Date(timestamp); // 注意：时间戳通常是毫秒为单位的
        // 获取年份  
        const year = date.getFullYear();
        // 获取月份（注意：月份是从0开始的，所以需要+1）  
        let month = date.getMonth() + 1;  
        month = month < 10 ? '0' + month : month; // 如果月份是个位数，前面补0  
          
        // 获取日期  
        let day = date.getDate();  
        day = day < 10 ? '0' + day : day; // 如果日期是个位数，前面补0  
          
        // 返回格式化的字符串  
        return year + '-' + month + '-' + day;  
      },

      navtoGo(url, obj = {}) {
        this.$navto.push(url, obj)
      },
    },
 }
</script>

<style lang='scss' scoped>
.line{
  height: 1rpx;
  margin:30upx 0;
  background-color: #e5e5e5;
}
.add{
  position: relative;
  height: calc(100vh - 30rpx);
  padding-top: 30rpx;
  background-color: #f0f2f2;
  .add-content{
    padding-bottom: 100rpx;
    padding: 0 30rpx;
    height: calc(100% - 196rpx);
    overflow-y: scroll;
    .add-drug,.drug-time,.add-title{
        padding: 30rpx 0 30rpx 30rpx;  
        background-color: #fff;  
        border-radius: 20rpx;
        /deep/.picker_one{
          background-color: red;
        }
    }
    .add-title{
      display: flex;
      justify-content: space-between;
      padding:30rpx;
      margin-bottom:20rpx;
      .sky{
        margin-left: auto;
        text-align: right;
        flex: 1;
        .sky-text{
          color:#000;
        }
      }
    }
    .add-drug{
      margin-bottom: 20rpx;
    }
    .options{
      .title{
        margin: 40upx 0 20upx 0;
        color:#787979;
      }
      .options-content{
        padding: 30rpx 0 30rpx 30rpx;  
        background-color: #fff;  
        border-radius: 20rpx;
        .input-container {
          display: flex;
          align-items: center;
          .sky{
            display: flex;
            padding-right: 30rpx;
            margin-left: auto;
            .my-input{
              text-align: right;
            }
            span{
              margin-left: 8rpx;
            }
          }
        }
      }
    }
    .drug-user,.remark{
      padding: 30rpx 0 30rpx 30rpx;  
      background-color: #fff;  
      border-radius: 20rpx;
      textarea{
        height: 120rpx;
      }
    }
    .drug-user{
      margin:20upx 0;
    }
  }
  .botton-btn{
    position: absolute;
    bottom: 0;
    left: 0;
    width: calc(100% - 40rpx);
    background-color: #fff;
    padding: 20rpx 20rpx 40rpx 20rpx;
    font-weight: 700;
    font-size: 36rpx;
    button{
      border-radius: 46rpx;
      color: #fff;
      background: #13b38e;
    }
  }
}
</style>