<template>
  <view class="main">
    <view class="top">
      <view class="top-nav">
        <view class="top-nav-body">
          <view class="city" @tap="$navto.push('CommonPositioning')">
            <view class="white">{{ regionInfo.city || '定位失败' }}</view>
            <em class="icon-positioning-down"></em>
          </view>
          <view class="input-view">
            <i class="icon-positioning-search"></i>
            <input confirm-type="search" placeholder="搜索地址" placeholder-style="color: #BFBFBF" class="input" type="text" :value="search" @input="searchInputFn" @confirm="searchFn">
          </view>
          <view class="click" @tap="searchFn">
            搜索
          </view>
        </view>
      </view>
    </view>
    <view class="title">
      当前位置
    </view>
    <view class="location" @tap="$navto.back(1)">
      <view class="location-list">
        <view class="top">
          <view class="name current">
            {{cityInfo.name || '暂无'}}
          </view>
          <view class="tag" @tap.stop="repositionFn()">
            {{repositionText}}
          </view>
        </view>
        <view class="bottom" v-if="cityInfo">
          {{cityInfo.pname || ''}}{{cityInfo.pname || ''}}{{cityInfo.cityname || ''}}{{cityInfo.adname || ''}}{{cityInfo.address || ''}}
        </view>
      </view>
    </view>
    <view class="title">
      最近访问
    </view>
    <view class="location" v-for="(item, index) in pdList" :key="index" @tap="clickFn(item)">
      <view class="location-list bdb">
        <view class="top">
          <view class="name">
            {{item.name || ''}}
          </view>
        </view>
        <view class="bottom">
          {{item.pname || ''}}{{item.pname || ''}}{{item.cityname || ''}}{{item.adname || ''}}{{item.address || ''}}
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'

export default {
  components: {

  },
  data() {
    return {
      repositionText: '重新定位',
      repositionState: false,
      search: '',
      cityInfo: {},
      regionInfo: {},
      pdList: []
    }
  },
  onLoad(paramsObj) {
    const that = this
    const query = this.$Route.query
    if (!this.$validate.isNull(query)) {}
    const cityInfo = that.$common.getKeyVal('system', 'cityInfoStorage', true)
    if (!that.$validate.isNull(cityInfo)) {

    } else {
      that.$ext.utility.getLocation().then((res) => {
        that.$ext.common.getPosition(res).then((data) => {
          that.init()
        }).catch(() => {

        })
      }).catch(() => {

      })
    }
  },
  onShow() {
    this.init()
  },
  methods: {
    init() {
      this.$nextTick(() => {
        this.search = ''
        const cityInfo = this.$common.getKeyVal('system', 'cityInfoStorage', true)
        let pdList = this.$common.getKeyVal('system', 'cityInfoArrStorage', true)
        const regionInfo = this.$common.getKeyVal('system', 'longitudeAndLatitude', true)
        if (!this.$validate.isNull(regionInfo)) {
          this.regionInfo = regionInfo
        } else {
          this.regionInfo = {}
        }
        if (!this.$validate.isNull(cityInfo)) {
          this.cityInfo = cityInfo
        } else {
          this.cityInfo = {}
        }
        if (!this.$validate.isNull(pdList)) {

        } else {
          pdList = []
        }
        this.pdList = pdList
      })
    },
    repositionFn() {
      const that = this
      if (that.repositionState) {
        return
      }
      that.repositionText = '定位中...'
      that.repositionState = true
      that.$ext.utility.getLocation().then((res) => {
        that.$ext.common.getPosition(res).then((data) => {
          clearTimeout(timer)
          that.repositionText = '重新定位'
          that.repositionState = false
          // that.init()
          that.$navto.back(1)
          that.$common.setKeyVal('system', 'isOnShow', true)
        }).catch(() => {
          clearTimeout(timer)
          that.repositionText = '重新定位'
          that.repositionState = false
        })
      }).catch(() => {
        clearTimeout(timer)
        that.repositionText = '重新定位'
        that.repositionState = false
      })
      const timer = setTimeout(() => {
        that.repositionText = '重新定位'
        that.repositionState = false
        that.$uniPlugin.toast('定位失败')
      }, 5000)
    },
    clickFn(item) {
      this.$common.setKeyVal('system', 'cityInfoStorage', item, true)
      let arr = this.$common.getKeyVal('system', 'cityInfoArrStorage', true)
      if (!this.$validate.isNull(arr)) {
        if (typeof arr === 'object' && arr.length > 0) {
          let state = false
          let index = ''
          for (let i = 0; i < arr.length; i++) {
            if (item.id === arr[i].id) {
              state = true
              index = i
              break
            }
          }
          if (state) {
            if (typeof index === 'number') {
              arr.splice(index, 1)
            }
          }
        }
        arr.unshift(item)
        this.$common.setKeyVal('system', 'cityInfoArrStorage', arr, true)
      } else {
        this.$common.setKeyVal('system', 'cityInfoArrStorage', [], true)
      }
      this.$navto.back(1)
      this.$common.setKeyVal('system', 'isOnShow', true)
      // this.init()
    },
    searchInputFn(e) {
      this.search = e.target.value
    },
    searchFn() {
      if (this.search) {
        const regionInfo = this.$common.getKeyVal('system', 'longitudeAndLatitude', true)
        if (!this.$validate.isNull(regionInfo)) {
          this.$navto.push('CommonPositioningSearch', { search: this.search })
        } else {
          this.$uniPlugin.toast('请选择地区')
        }
      } else {
        this.$uniPlugin.toast('请输入搜索地址')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .m-t-40{
    margin-top:40upx;
  }
  .m-b-20{
    margin-bottom: 20upx;
  }
  .main{
    height: calc(100vh - 88upx);
    .top-nav{
      padding: 10upx 30upx;
      background-color: #fff;
      .top-nav-body{
        .city {
          display: inline-block;
          vertical-align: middle;
          width: 160upx;
          margin-right: 16upx;
          .white{
            display: inline-block;
            vertical-align: middle;
            max-width: calc(100% - 28upx);
            font-weight: bold;
            color: #333;
            font-size: 28upx;
            line-height: 42upx;
            @include ellipsis(1);
          }
          .icon-positioning-down{
            display: inline-block;
            vertical-align: middle;
            @include iconImg(28, 28, '/system/icon-positioning-down.png');
          }
        }
        .input-view {
          display: inline-block;
          vertical-align: middle;
          width: calc(100% - 316upx);
          @include rounded(38upx);
          line-height: 64upx;
          height: 64upx;
          padding: 0 20upx;
          background: #F7F7F7;
          .icon-positioning-search{
            display: inline-block;
            vertical-align: middle;
            margin-right: 6upx;
            @include iconImg(32, 32, '/system/icon-positioning-search.png');
          }
          .input {
            width: calc(100% - 48upx);
            display: inline-block;
            vertical-align: middle;
            font-size: 28upx;
            line-height: 42upx;
            color: #333;
          }
        }
        .click{
          display: inline-block;
          vertical-align: middle;
          text-align: right;
          width: 100upx;
        }
      }
    }
    .title{
      padding: 30upx 30upx 16upx 30upx;
      color: #999;
      font-size: 26upx;
      line-height: 40upx;
    }
    .location{
      padding: 0 30upx;
      background-color: #fff;
      .location-list{
        padding: 30upx 0;
        .top{
          margin-bottom: 20upx;
          .name{
            width: 100%;
            display: inline-block;
            vertical-align: middle;
            font-size: 32upx;
            line-height: 48upx;
            font-weight: 500;
          }
          .tag{
            display: inline-block;
            vertical-align: middle;
            width: 160upx;
            text-align: right;
            margin-left: 20upx;
            color: #277DFF;
          }
          .name.current{
            width: calc(100% - 180upx);
            @include ellipsis(1);
          }
        }
        .bottom{
          color: #666;
          font-size: 24upx;
          line-height: 36upx;
        }
      }
      .location-list.bdb{
        border-bottom: 2upx solid $contentDdt;
      }
    }
    .location:last-of-type{
      .location-list.bdb{
        border-bottom: none;
      }
    }
  }
</style>
