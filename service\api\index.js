import common from '@/modules/common/api/common.js'
import lights from '@/modules/common/api/lights.js'
import user from '@/modules/common/api/user'
import sys from '@/modules/common/api/sys'
import pay from '@/modules/common/api/pay'
import wechat from '@/modules/common/api/wechat'
import information from '@/modules/common/api/information'
import record from '@/modules/common/api/record'
import patientinfo from '@/modules/common/api/patientinfo'
import chat from '@/modules/common/api/chat'
import order from '@/modules/common/api/order'
import community from '@/modules/common/api/community.js'
import postmessage from '@/modules/common/api/postmessage'
import circleclassify from '@/modules/common/api/circleclassify'
import wishingwell from '@/modules/common/api/wishingwell'
import packet from '@/modules/common/api/packet'
import hospital from '@/modules/common/api/hospital'
import drugBook from '@/modules/common/api/drugBook'

import cloudClassroom from '@/modules/common/api/cloudClassroom.js'
import activity from '@/modules/activity/api/activity.js'
import medicineclassify from '@/modules/common/api/medicineclassify'
import redpacket from '@/modules/common/api/redpacket'
import dll from '@/modules/common/api/dll'
import accompanyDoctor from '@/modules/accompany-doctor/api/accompanyDoctor.js'
import providerManagement from '@/modules/provider-management/api/providerManagement.js'
import alipay from '@/modules/common/api/alipay.js'
import distribution from '@/modules/common/api/distribution'
const api = {
  common,
  lights,
  pay,
  user,
  sys,
  wechat,
  information,
  record,
  patientinfo,
  chat,
  order,
  community,
  postmessage,
  circleclassify,
  wishingwell,
  packet,
  cloudClassroom,
  activity,
  hospital,
  medicineclassify,
  drugBook,
  redpacket,
  dll,
  accompanyDoctor,
  providerManagement,
  alipay,
  distribution
}
export default api
