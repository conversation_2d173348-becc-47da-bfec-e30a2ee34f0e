/**
 * 系统全局
 */
import validate from '@/common/util/validate'
const sys = {
    namespaced: true,
    state: {
        historyCount: 0,
        bannerList: [], // 首页banner
        routerNameObj: {
            Home: 0
        },
        prevRouterName: 'Index', // 上一次路由
        prevRouterObj: {}, // 上一次路由
        // direction: shouldUseTransition ? 'forward' : '', // 页面切换专场效果记录
        timerChangeToken: null, // 定时切换token
        isWechat: false, // JSSDK验证成功状态
        dictionaryData: {}, // 数据字典数据
        scrollGetPosTimer: null, // 获取scroller定位的定时器id
        isLoading: true, // 正在加载中,
        isOnShow: true, // 控制onShow是否执行
        isOnShowTwo: true, // 控制onShowTwo是否执行
        readOnlyStorage: {}, // 数据处理暂存区（使用完及时清除）---只读存储，只作为只读
        temporaryStorage: {}, // 数据处理暂存区（使用完及时清除）---只作为暂时操作存储，操作完及时清除
        twoTemporaryStorage: {}, // 数据处理暂存区（使用完及时清除）---只作为暂时操作存储，操作完及时清除（处理上一个被暂用情况）
        draftStorage: {}, // 草稿保存暂时数据（班级通知、班级相册、亲子任务新增保存草稿功能）
        cityInfoStorage: {}, // 存储定位相关信息，永久存储
        longitudeAndLatitude: {}, // 存储经纬度，永久存储
        cityInfoArrStorage: [], // 存储历史定位数组信息，永久存储
        searchHistoryArrStorage: [], // 搜索历史数组信息，永久存储
        systemMenuList: {}, // 系统菜单列表存储
        selectedMenu: {}, // 已选菜单权限,
        scene: {}, // 场景值对象存储
        extraData: {}, // 从另外一个小程序跳转所带过来的参数的临时存储
        shareUserId: null, // 主动分享的用户主键（分享二维码）
        isShowSubscribePanel: false, // 是否显示微信接收消息订阅面板
        teacherClassArrStorage: [], // 教师班级历史数组信息，永久存储
        teacherClassDefaultObj: {}, // 教师班级默认数据，永久存储
        networkStatus: true, // 网络状态是否正常
        backlogData: '', // 待办的存储数量
        launchParams: null, // 应用初始化参数
        gbScene: '', // 渠道链入口标识
        backTaskFlag:false, //判定是否出现返回福币任务按钮
    },
    mutations: {
        UPDATE_GBSCENE(state, gbScene) {
            state.gbScene = gbScene
        },
        UPDATE_LAUNCHPARAMS(state, launchParams) {
            state.launchParams = launchParams
        },
        /**
         * 案例模板
         * @param state
         * @param data
         * @constructor
         */
        UPDATE_DEMO: (state, data) => {},
        /**
         * 网络状态是否正常
         */
        UPDATE_NETWORKSTATUS(state, networkStatus) {
            state.networkStatus = networkStatus
        },
        /**
         * 待办的存储数量
         */
        UPDATE_BACKLOGDATA(state, backlogData) {
            state.backlogData = backlogData
        },

        /**
         * 是否显示微信接收消息订阅面板
         */
        UPDATE_ISSHOWSUBSCRIBEPANEL(state, isShowSubscribePanel) {
            state.isShowSubscribePanel = isShowSubscribePanel
        },
        /**
         * 记录场景值对象存储
         */
        UPDATE_SCENE(state, scene) {
            state.scene = scene
        },
        /**
         * 从另外一个小程序跳转所带过来的参数的临时存储
         */
        UPDATE_EXTRADATA(state, extraData) {
            state.extraData = extraData
        },
        /**
         * 主动分享的用户主键（分享二维码）
         */
        UPDATE_SHAREUSERID(state, shareUserId) {
            state.shareUserId = shareUserId
        },
        /**
         * 记录路由跳转数量
         */
        UPDATE_HISTORYCOUNT(state, historyCount) {
            state.historyCount = historyCount
        },
        /**
         * 记录路由跳转数量
         */
        UPDATE_BANNERLIST(state, bannerList) {
            state.bannerList = bannerList
        },
        /**
         * 上一次路由名字
         */
        UPDATE_PREVROUTEROBJ(state, prevRouterObj) {
            state.prevRouterObj = prevRouterObj
        },
        /**
         * 上一次路由名字
         */
        UPDATE_PREVROUTERNAME(state, prevRouterName) {
            state.prevRouterName = prevRouterName
        },
        /**
         * 上一路由链接 对象
         */
        UPDATE_ROUTERNAMEOBJ(state, routerNameObj) {
            state.routerNameObj = routerNameObj
        },
        /**
         * 页面切换专场效果记录
         */
        // UPDATE_DIRECTION(state, direction) {
        //   state.direction = direction
        // },
        /**
         * 定时刷新token 定时器id
         */
        UPDATE_TIMERCHANGETOKEN(state, timerChangeToken) {
            if (validate.isNull(timerChangeToken)) { // 传递为空则表示清除定时器
                clearInterval(this.state.sys.timerChangeToken)
            }
            state.timerChangeToken = timerChangeToken
        },
        /**
         * 更新是否是微信坏境
         */
        UPDATE_ISWECHAT(state, isWechat) {
            state.isWechat = isWechat
        },
        /**
         * 更新数据字典数据
         */
        UPDATE_DICTIONARYDATA(state, dictionaryData) {
            state.dictionaryData = dictionaryData
        },
        /**
         * 更新scroller定位的定时器id
         */
        UPDATE_SCROLLGETPOSTIMER(state, scrollGetPosTimer) {
            state.scrollGetPosTimer = scrollGetPosTimer
        },
        /**
         * 更新是否加载中
         */
        UPDATE_ISLOADING(state, isLoading) {
            state.isLoading = isLoading
        },
        /**
         * 更新是否数据处理暂存区
         */
        UPDATE_TEMPORARYSTORAGE(state, temporaryStorage) {
            state.temporaryStorage = temporaryStorage
        },
        /**
         * 控制onShow是否执行
         */
        UPDATE_ISONSHOW(state, isOnShow) {
            state.isOnShow = isOnShow
        },
        /**
         * 控制onShowTwo是否执行
         */
        UPDATE_ISONSHOWTWO(state, isOnShowTwo) {
            state.isOnShowTwo = isOnShowTwo
        },
        /**
         * 更新是否数据处理暂存区
         */
        UPDATE_TWOTEMPORARYSTORAGE(state, twoTemporaryStorage) {
            state.twoTemporaryStorage = twoTemporaryStorage
        },
        /**
         * 更新是否数据处理暂存区-只读
         */
        UPDATE_READONLYSTORAGE(state, readOnlyStorage) {
            state.twoTemporaryStorage = readOnlyStorage
        },
        /**
         * 更新定位信息存储区
         */
        UPDATE_CITYINFOSTORAGE(state, cityInfoStorage) {
            state.cityInfoStorage = cityInfoStorage
        },
        /**
         * 更新历史定位数组信息存储区
         */
        UPDATE_CITYINFOARRSTORAGE(state, cityInfoArrStorage) {
            state.cityInfoArrStorage = cityInfoArrStorage
        },
        /**
         * 搜索历史数组信息
         */
        UPDATE_SEARCHHISTORYARRSTORAGE(state, searchHistoryArrStorage) {
            state.searchHistoryArrStorage = searchHistoryArrStorage
        },
        /**
         * 教师班级历史数组信息
         */
        UPDATE_TEACHERCLASSARRSTORAGE(state, teacherClassArrStorage) {
            state.teacherClassArrStorage = teacherClassArrStorage
        },
        /**
         * 教师班级默认数据信息
         */
        UPDATE_TEACHERCLASSDEFAULTOBJ(state, teacherClassDefaultObj) {
            state.teacherClassDefaultObj = teacherClassDefaultObj
        },
        /**
         * 系统菜单列表
         */
        UPDATE_SYSTEMMENULIST(state, systemMenuList) {
            state.systemMenuList = systemMenuList
        },
        /**
         * 已选菜单权限
         */
        UPDATE_SELECTEDMENU(state, selectedMenu) {
            state.selectedMenu = selectedMenu
        },
        /**
         * 更新经纬度信息存储区
         */
        UPDATE_LONGITUDEANDLATITUDE(state, longitudeAndLatitude) {
            state.longitudeAndLatitude = longitudeAndLatitude
        },
        /**
         * 草稿保存暂时数据
         */
        UPDATE_DRAFTSTORAGE(state, draftStorage) {
            state.draftStorage = draftStorage
        },
        /**
         * 更新返回福币任务按钮显示状态
         */
        UPDATE_BACKTASKFLAG(state, backTaskFlag) {
            state.backTaskFlag = backTaskFlag
        }
    },
    actions: {
        UpdateGbScene(context, gbScene) {
            context.commit('UPDATE_GBSCENE', gbScene)
        },
        UpdateLaunchParams(context, launchParams) {
            context.commit('UPDATE_LAUNCHPARAMS', launchParams)
        },
        /**
         * 是否显示微信接收消息订阅面板
         */
        UpdateIsShowSubscribePanel(context, isShowSubscribePanel) {
            context.commit('UPDATE_ISSHOWSUBSCRIBEPANEL', isShowSubscribePanel)
        },
        /**
         * 案例模板
         * @param commit
         * @param data
         * @constructor
         */
        UpdateDemo({ commit }, data) {
            commit('UPDATE_DEMO', data)
        },
        /**
         * 网络状态是否正常
         */
        UpdateNetworkStatus(context, networkStatus) {
            context.commit('UPDATE_NETWORKSTATUS', networkStatus)
        },
        /**
         * 待办的存储数量
         */
        UpdateBacklogData(context, backlogData) {
            context.commit('UPDATE_BACKLOGDATA', backlogData)
        },
        /**
         * 记录场景值对象存储
         */
        UpdateScene(context, scene) {
            context.commit('UPDATE_SCENE', scene)
        },
        /**
         * 从另外一个小程序跳转所带过来的参数的临时存储
         */
        UpdateExtraData(context, extraData) {
            context.commit('UPDATE_EXTRADATA', extraData)
        },
        /**
         * 主动分享的用户主键（分享二维码）
         */
        UpdateShareUserId(context, shareUserId) {
            context.commit('UPDATE_SHAREUSERID', shareUserId)
        },
        /**
         * 记录路由跳转数量
         */
        UpdateHistoryCount(context, historyCount) {
            context.commit('UPDATE_HISTORYCOUNT', historyCount)
        },
        /**
         * 记录路由跳转数量
         */
        UpdateBannerList(context, bannerList) {
            context.commit('UPDATE_BANNERLIST', bannerList)
        },
        /**
         * 上一次路由名字
         */
        UpdatePrevRouterName(context, prevRouterName) {
            context.commit('UPDATE_PREVROUTERNAME', prevRouterName)
        },
        /**
         * 上一路由链接 对象
         */
        UpdatePrevRouterObj(context, prevRouterObj) {
            context.commit('UPDATE_PREVROUTEROBJ', prevRouterObj)
        },
        /**
         * 记录路由跳转 对象
         */
        UpdateRouterNameObj(context, routerNameObj) {
            context.commit('UPDATE_ROUTERNAMEOBJ', routerNameObj)
        },
        /**
         * 页面切换专场效果记录
         */
        // UpdateDirection(context, direction) {
        //   context.commit('UPDATE_DIRECTION', direction)
        // },
        /**
         * 更新票据
         */
        UpdateTimerChangeToken(context, timerChangeToken) {
            context.commit('UPDATE_TIMERCHANGETOKEN', timerChangeToken)
        },
        /**
         * 更新是否是微信公众号坏境JSSDK验证成功状态
         */
        UpdateIsWechat(context, isWechat) {
            context.commit('UPDATE_ISWECHAT', isWechat)
        },
        /**
         * 更新数据字典数据
         */
        UpdateDictionaryData(context, dictionaryData) {
            context.commit('UPDATE_DICTIONARYDATA', dictionaryData)
        },
        /**
         * 更新scroller定位的定时器id
         */
        UpdateScrollGetPosTimer(context, scrollGetPosTimer) {
            context.commit('UPDATE_SCROLLGETPOSTIMER', scrollGetPosTimer)
        },
        /**
         * 更新是否加载中
         */
        UpdateIsLoading(context, isLoading) {
            context.commit('UPDATE_ISLOADING', isLoading)
        },
        /**
         * 更新数据处理暂存区
         */
        UpdateTemporaryStorage(context, temporaryStorage) {
            context.commit('UPDATE_TEMPORARYSTORAGE', temporaryStorage)
        },
        /**
         * 控制onShow是否执行
         */
        UpdateIsOnShow(context, isOnShow) {
            context.commit('UPDATE_ISONSHOW', isOnShow)
        },
        /**
         * 控制onShow是否执行
         */
        UpdateIsOnShowTwo(context, isOnShowTwo) {
            context.commit('UPDATE_ISONSHOWTWO', isOnShowTwo)
        },
        /**
         * 更新数据处理暂存区
         */
        UpdateTwoTemporaryStorage(context, twoTemporaryStorage) {
            context.commit('UPDATE_TWOTEMPORARYSTORAGE', twoTemporaryStorage)
        },
        /**
         * 更新数据处理暂存区-只读
         */
        UpdateReadOnlyStorage(context, readOnlyStorage) {
            context.commit('UPDATE_READONLYSTORAGE', readOnlyStorage)
        },
        /**
         * 更新定位信息存储区
         */
        UpdateCityInfoStorage(context, cityInfoStorage) {
            context.commit('UPDATE_CITYINFOSTORAGE', cityInfoStorage)
        },
        /**
         * 更新历史定位数组信息存储区
         */
        UpdateCityInfoArrStorage(context, cityInfoArrStorage) {
            context.commit('UPDATE_CITYINFOARRSTORAGE', cityInfoArrStorage)
        },
        /**
         * 搜索历史数组信息
         */
        UpdateSearchHistoryArrStorage(context, searchHistoryArrStorage) {
            context.commit('UPDATE_SEARCHHISTORYARRSTORAGE', searchHistoryArrStorage)
        },
        /**
         * 搜索历史数组信息
         */
        UpdateTeacherClassArrStorage(context, teacherClassArrStorage) {
            context.commit('UPDATE_TEACHERCLASSARRSTORAGE', teacherClassArrStorage)
        },
        /**
         * 搜索历史数组信息
         */
        UpdateTeacherClassDefaultObj(context, teacherClassDefaultObj) {
            context.commit('UPDATE_TEACHERCLASSDEFAULTOBJ', teacherClassDefaultObj)
        },
        /**
         * 系统菜单列表
         */
        UpdateSystemMenuList(context, systemMenuList) {
            context.commit('UPDATE_SYSTEMMENULIST', systemMenuList)
        },
        /**
         * 已选菜单权限
         */
        UpdateSelectedMenu(context, selectedMenu) {
            context.commit('UPDATE_SELECTEDMENU', selectedMenu)
        },
        /**
         * 更新经纬度信息存储区
         */
        UpdateLongitudeAndLatitude(context, longitudeAndLatitude) {
            context.commit('UPDATE_LONGITUDEANDLATITUDE', longitudeAndLatitude)
        },
        /**
         * 草稿保存暂时数据
         */
        UpdateDraftStorage(context, draftStorage) {
            context.commit('UPDATE_DRAFTSTORAGE', draftStorage)
        },
        /**
         * 更新返回福币任务按钮显示状态
         */
        UpdateBackTaskFlag(context, backTaskFlag) {
            context.commit('UPDATE_BACKTASKFLAG', backTaskFlag)
        }
    }
}

export default sys
