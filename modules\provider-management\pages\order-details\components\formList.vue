<template>
  <view class="server">
    <template v-for="(item,index) in serverMap">

      <view v-if="showItem(item)" :key="index" @click="$emit('emitChange',item.textType)" class="serverItem" :class="{ 'disabled-item': item.disabled }">
        <view class="serverTitle">
          <text class="sign" v-if="item.required">*</text>
          {{item.title}}
        </view>
        <view class="serverContent">{{item.text || item.noValue}}</view>
        <image class="iconRightArrow" :src="iconRightArrow" mode="aspectFill"></image>
      </view>
      <view
          v-if="item.type === 'price-input' && !item.hidden"
          :key="index"
          class="serverItem price-input"
        >
          <view class="serverTitle" v-if="item.required">
            <text class="sign">*</text>{{item.title}}
          </view>
          <view class="price-input-container">
            <input
              class="price-input-field"
              :type="item.inputType || 'text'"
              :value="item.value"
              @input="res => inputChange(item.textType, res)"
              placeholder="请输入价格"
            />
            <text class="price-unit">{{item.unit}}</text>
          </view>
        </view>

      <view v-if="item.type === 'textarea' && !item.hidden" :key="index" @click="$emit('emitChange',item.textType)" class="serverItem">
        <view class="serverTitle" :style="{width:item.miniTitle ? '100%' : '172rpx'}">
          <text class="sign" v-if="item.required">*</text>
          {{item.title}}
          <text class="miniTitle" v-if="item.miniTitle">({{item.miniTitle}})</text>
        </view>
        <textarea class="serverContent" @input="res=>inputChange(item.textType,res)"  :value="item.value" ></textarea>
      </view>

      <view v-if="item.type === 'input' && !item.hidden" :key="index" @click="$emit('emitChange',item.textType)" class="serverItem">
        <view class="serverTitle"><text class="sign">*</text>{{item.title}}</view>
        <input class="serverContent" :value="item.value" @input="res=>inputChange(item.textType,res)" />
      </view>

      <view v-if="item.type === 'select' && !item.hidden" :key="index" @click="$emit('emitChange',item.textType)" class="serverItem">
        <view class="serverTitle">
          <text class="sign" v-if="item.required">*</text>
          {{item.title}}
        </view>
        <view class="serverContent">{{item.text || item.noValue}}</view>
        <image class="iconRightArrow" :src="iconRightArrow" mode=""></image>
      </view>

      <view class="serverItem" v-if="item.type === 'image' && !item.hidden" :key="index">
        <view class="serverTitle">{{item.title}}</view>
        <view class="additionalContentImageUpMap">
          <view
          class="additionalContentImageUp"
          v-for="(itemImage,index) in item.value"
          :key="index"
          @click="$emit('previewImage',file_ctx + itemImage)"
          >
            <image class="iconPostMenuClose" @click.stop="$emit('closeImgage',index)" :src="iconPostMenuClose" mode="aspectFill"></image>
            <image class="imageContent" :src="file_ctx + itemImage"></image>
          </view>
          <view class="additionalContentImageUp" @click="$emit('emitChange',item.textType)">
            <image class="imageUpImage" :src="iconPostUpload" mode="aspectFill"></image>
            <view class="imageUpText">上传图片</view>
          </view>
        </view>

      </view>
      <view class="serverItem" :key="index" v-if="item.type === 'switch'  && !item.hidden">
        <view class="serverTitle">{{item.title}}</view>
        <switch @change="res=>switchChange(res,item.textType)" />
      </view>
      <view class='line' :key="index" v-if="index < serverMap.length - 1 && !item.hidden"></view>
    </template>
  </view>
</template>

<script>
  import serverOptions from '@/config/env/options'
  export default{
    props:{
      serverMap:{
        type:Array,
        default:[]
      }
    },
    data(){
      return {
        file_ctx: this.file_ctx,
        iconRightArrow: this.$static_ctx + "image/business/hulu-v2/icon-right-arrow.png",
        iconPostUpload: this.$static_ctx + "image/business/hulu-v2/icon-post-upload.png",
        iconPostMenuClose: this.$static_ctx + "image/business/hulu-v2/icon-post-menu-close.png"
      }
    },
    methods:{
      switchChange(res,type){
        res.detail.value = +res.detail.value;
        this.$emit('emitInput',{type,res})
      },
      inputChange(type,res){
        this.$emit('emitInput',{type,res})
      },
      showItem(item){
        if(item.showSource) return !item.type && !item.hidden && item.showSource === serverOptions.source
        return !item.type && !item.hidden
      }
    }
  }
</script>

<style lang="scss">
  .line{
    width:100%;
    height: 2rpx;
    background: #EAEBF0;
  }
  .iconRightArrow{
    width: 32rpx;
    height: 32rpx;
  }
  .server{
    width: 686rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    margin-top: 40rpx;
    padding: 0 24rpx;
    box-sizing: border-box;
    .serverItem{
      width: 100%;
      display: flex;
      padding: 32rpx 8rpx;
      box-sizing: border-box;
      align-items: center;
      flex-wrap: wrap;
      .serverTitle{
        width: 172rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #1D2029;
        .sign{
          font-weight: 500;
          font-size: 28rpx;
          color: #FF5500;
        }
      }
      .miniTitle{
        font-weight: 400;
        font-size: 24rpx;
        color: #4E5569;
      }
      .serverArea{
        width: 638rpx;
        height: 288rpx;
        background: #FFFFFF;
        border-radius: 16rpx;
        border: 1rpx solid #D9DBE0;
        margin-top: 16rpx;
        padding: 24rpx;
        font-weight: 400;
        font-size: 28rpx;
      }
      .serverContent{
        width: 400rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 28rpx;
        color: #A5AAB8;
      }
      .iconRightArrow{
        margin-left: auto;
      }
    }
  }
  .additionalContentImageUpMap{
    display: flex;
    flex-wrap: wrap;
    column-gap: 20rpx;
  }
  .additionalContentImageUp{
    margin-top: 12rpx;
    width: 144rpx;
    height: 144rpx;
    background: #F4F6FA;
    border-radius: 16rpx;
    text-align: center;
    position: relative;
    &:nth-of-type(3n){
      margin-right: 0;
    }
    .imageUpImage{
      width: 64rpx;
      height: 54rpx;
      margin-top: 46rpx;
    }
    .imageUpText{
      font-weight: 400;
      font-size: 24rpx;
      color: #4E5569;
    }
    .imageContent{
      width: 100%;
      height: 100%;
    }
    .iconPostMenuClose{
      width: 30rpx;
      height: 30rpx;
      position: absolute;
      top: 0;
      right: 0;
      background: white;
    }
  }
  // 添加禁用样式
  .disabled-item {
    opacity: 0.7;
    pointer-events: none;

    .serverContent {
      color: #A5AAB8 !important;
    }
  }
</style>
