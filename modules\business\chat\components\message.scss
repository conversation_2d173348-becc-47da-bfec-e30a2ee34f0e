.btn{
  // min-width: 157upx;
  min-height: 52upx;
  background-color: #00d29d;
  // padding: 0;
  border-radius: 30upx;
  margin: 0 auto;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 30upx;
}
.zxbtn{
  margin-top: 30upx;
  margin-bottom: 30upx;
  height: 80rpx;
}

.read-status-panel{
    margin: 50upx 8upx 0 8upx;
  }
  .icon-yijianfankui-d{
    @include iconImg(20, 20, '/system/icon-yijianfankui-d.png');
  }
  .icon-yijianfankui-d-ok{
    @include iconImg(20, 20, '/system/icon-yijianfankui-d-ok.png');
  }
  .icon-loading{
    @include iconImg(45,45,'/system/icon-loading.gif');
  }
  .content-box{
    background-color: #f7f7f7;
    /* #ifdef APP-PLUS */
    margin-bottom: 0rpx;
    margin-bottom: constant(safe-area-inset-bottom);
    margin-bottom: env(safe-area-inset-bottom);
    /* #endif */
    /* #ifdef MP-WEIXIN || MP-ALIPAY */
    padding-bottom: 0rpx;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    /* #endif */


  }
  .msg-loading{
    width: 26upx;
    height: 26upx;
    margin: 30upx 10upx 0 10upx;
  }
  .ws-warn{
    position: fixed;
    text-align: center;
    background: #f44336;
    width: 100%;
    height: 70upx;
    line-height: 70upx;
    z-index: 999;
    color: white;
  }

  .content {
    // background-color: #fff;
    &-box {
      width: 100%;
      height: auto;
      // min-height: calc(100vh - env(safe-area-inset-top) - 200rpx);
      box-sizing: content-box;
      position: relative;
      // padding-bottom: 120rpx;

      /* #ifdef APP-PLUS  */
      margin-bottom: 0rpx;
      margin-bottom: constant(safe-area-inset-bottom);
      margin-bottom: env(safe-area-inset-bottom);
      /* #endif */
      /* #ifdef MP-WEIXIN */
      padding-bottom: 0rpx;
      // padding-bottom: calc(120rpx + constant(safe-area-inset-bottom));
      // padding-bottom: calc(120rpx + env(safe-area-inset-bottom) );
      /* #endif */

      &-bg {
        width: 100%;
        position: fixed;
        /* #ifdef MP-WEIXIN */
        bottom: 0;
        bottom: constant(safe-area-inset-bottom);
        bottom: env(safe-area-inset-bottom);
        /* #endif */
        /* #ifndef MP-WEIXIN */
        top: 0;
        left: 0;
        /* #endif */
      }

      &-loading {
        text-align: center;
        padding: 20rpx 0;
      }

      .message {
        padding: 13rpx 20rpx;

        .edit {
          text-align: right;
          margin-right: 108upx;
          color: $topicC;
        }
      }

      .message-item {
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        align-content: flex-start;
        flex-wrap: nowrap;
        flex-direction: row;

        .img {
          width: 80rpx;
          height: 80rpx;
          &.avatar {
            border-radius: 50%;
          }
        }

        .content {
          padding: 20rpx;
          max-width: 500rpx;
          border-radius: 10rpx;
          font-size: 28rpx;
        }
        //图片
        .msgType2{
          padding: 0;
          border-radius: 2rpx;
          background-color: transparent !important;
          .img{
            width: 200rpx;
            height: auto;
            max-width: 300rpx;
            max-height: 400rpx;
          }
        }
        .msgType2::after{
          border: none !important;
          display: none !important;
        }
        .content-type-right {
          flex-direction: row-reverse;
        }

        &.right {
          flex-direction: row-reverse;

          .content {
            background-color: #4cd964;
            margin-right: 28rpx;
            word-break: break-all;
            line-height: 36rpx;
            position: relative;

            &::after {
              content: '';
              display: block;
              width: 0;
              height: 0;
              border-top: 10rpx solid transparent;
              border-bottom: 10rpx solid transparent;
              border-left: 16rpx solid #4cd964;
              position: absolute;
              right: -16rpx;
              top: 30rpx;
            }
          }

          .bgf{
            background-color: #fff;
            min-width: 507upx;
            padding: 42upx;
            box-sizing: border-box;

          }
          .bgf::after{
            border-right-color:#fff!important;
            border-left-color: #fff!important;
          }
          .bgf {
            .content-title{
              font-size: 32upx;
              font-weight: 550;
              border-bottom: none;
              margin-bottom:10upx
            }
            .zxlicon{
              width: 50upx;
              height: 50upx;
              margin-right: 20upx;
              vertical-align: middle;
            }
            .content-card-key{
              font-size: 28upx;
              color: #19d5a4;
              margin-bottom: 35upx;
            }
          }
        }

        &.left {
          .content {
            background-color: #fff;
            margin-left: 28rpx;
            word-break: break-all;
            line-height: 36rpx;
            position: relative;

            &::after {
              content: '';
              display: block;
              width: 0;
              height: 0;
              border-top: 10rpx solid transparent;
              border-bottom: 10rpx solid transparent;
              border-right: 16rpx solid #fff;
              position: absolute;
              left: -16rpx;
              top: 30rpx;
            }
          }
        }
      }
    }

    .input-box {
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      box-sizing: content-box;
      z-index: 999;
      background-color: #eaeaea;

      /* #ifdef APP-PLUS */
      margin-bottom: 0rpx;
      margin-bottom: constant(safe-area-inset-bottom);
      margin-bottom: env(safe-area-inset-bottom);
      /* #endif */
      /* #ifdef MP-WEIXIN */
      padding-bottom: 0rpx;
      padding-bottom: constant(safe-area-inset-bottom);
      padding-bottom: env(safe-area-inset-bottom);
      /* #endif */

      &-flex {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-wrap: nowrap;
        flex-direction: row;
        padding: 20rpx;
        box-sizing: border-box;
        image{
          width: 56rpx;
          height: 56rpx;
        }
        .icon_img {
          margin-right: 20rpx;
        }
        .icon_btn_add{
          margin-right: 11px;
          @include iconImg(52, 52, '/system/icon-add.png');
          //margin-left: 20rpx;
        }
        &-grow {
          flex-grow: 1;

          .content {
            box-sizing: border-box;
            background-color: #fff;
            //height: 70rpx;
            height: 70rpx;
            line-height: 70upx;
            width: 100%!important;

            padding: 0 20rpx;
            border-radius: 12rpx;
            font-size: 28rpx;
            caret-color: #4cd964;
          }

          .voice_title {
            text-align: center;
            background-color: #ffffff;
            height: 80rpx;
            line-height: 80rpx;
            border-radius: 12rpx;
          }
        }

        .btn {
          margin-left: 20rpx;
          background-color: #4cd964;
          border: none;
          height: 74upx;
          line-height: 74upx;
        }
      }

      .fun-box{
        opacity: 0;
        transition: all 0.1s ease-in-out;
        height: 0;
        .grid-text{
          padding-top: 10rpx;
          color: #999;
        }

      }
      .show-fun-box{
        opacity: 1;
        height: 300rpx;
      }
    }

    .input-box-mpInputMargin {
      /* #ifdef MP-WEIXIN */
      padding-bottom: 0rpx;
      /* #endif */
    }
    .voice_an{
      width: 300rpx;
      height: 300rpx;
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%,-55%);
      background-color: rgba(41,41,41,0.7);
      color: white;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      border-radius: 10rpx;
      .text{
        padding-top: 30rpx;
      }
      @keyframes runVoice{
        0%{
          height: 10%;
        }
        20%{
          height: 50%;
        }
        50%{
          height: 100%;
        }
        80%{
          height: 50%;
        }
        100%{
          height: 0%;
        }
      }
      .wave{
        width:6rpx;
        height: 100%;
        margin-left: 10rpx;
        border-radius: 50rpx;
        background-color: #999;
        vertical-align: middle;
        display: inline-block;
      }
    }

    &-title {
      border-bottom: 1px solid #ccc;
      padding-bottom: 12upx;
    }

    &-card {
      padding-top: 12upx;

      &-item {
        display: flex;
        flex-direction: row;
        // align-items: center;

        image {
          max-height: 200upx;
        }
      }

      &-key {
        // width: 200upx;
      }
    }

  }

  .unread-fixed-box {
    position: fixed;
    bottom: calc(140upx + constant(safe-area-inset-bottom));
    bottom: calc(140upx + env(safe-area-inset-bottom));
    right: 0;
    background-color: #fff;
    border-top-left-radius: 32upx;
    border-bottom-left-radius: 32upx;
    padding: 14upx 20upx 14upx 20upx;

    .unread-fixed-text {
      color: $topicC;
      font-size: 28rpx;
    }
    &-top {
      position: fixed;
      top: 52upx;
      right: 0;
      background-color: #fff;
      border-top-left-radius: 32upx;
      border-bottom-left-radius: 32upx;
      padding: 14upx 20upx 14upx 20upx;

      .unread-fixed-text {
        color: $topicC;
        font-size: 28rpx;
      }
    }
  }

  .order-end {
    margin: 0 24upx;
    padding-bottom: 20upx;

    &-time {
      font-size: 26upx;
      color: #666;
      text-align: center;
    }

    &-tips {
      display: inline-block;
      padding: 8upx 24upx;
      background-color: #eaedf1;
      color: #333;
      font-size: 26upx;
      margin: 32upx 0;
      @include rounded(4px);
    }

    &-evaluate {
      display: flex;
      flex-direction: column;
      align-items: center;
      background-color: #fff;
      @include rounded(12px);
      border-radius: 10upx;
      padding: 32upx;

      &-title {
        font-size: 34upx;
        font-weight: 600;
      }

      &-desc {

      }
    }
  }

  .time {
    width: 100%;
    font-size: 24upx;
    margin: 12upx auto;
    text-align: center;

    &-text {
      display: inline-block;
      padding: 8upx 12upx;
      color: #fff;
      border-radius: 6upx;
      background-color: #dcdcdc;
    }
  }

  .subscribe {
      display: flex;
      flex-direction: column;
      padding: 36upx;
      border: 1px solid #16d6a5;
      position: relative;
      background-color: #fff;
      .subscribe-desc{
        line-height: 2;
      }

      &-close-icon {
        position: absolute;
        right: 0;
        top: 0;
        padding: 4rpx 24rpx;
      }
      &-title {
          text-align: center;
          font-weight: 550;
          margin-bottom: 36upx;
          font-size: 32upx;
      }
      &-desc {
          font-size: 28upx;
          line-height: 48upx;
      }

      &-steps + &-steps {
        margin-top: 24upx;
      }

      &-steps {
        display: flex;
        flex-direction: row;
        align-items: center;
        &-num {
          width: 36upx;
          height: 36upx;
          @include rounded(50%);
          text-align: center;
          line-height: 36upx;
          margin-right: 24upx;
        }
        &-desc {
          font-size: 26upx;
        }
        &.active {
          .subscribe-steps-desc {
            // color: #22daad;
            color: #ffb206;
          }
          .subscribe-steps-num {
            // background-color: #22daad;
            background-color: #ffb206;
            color: #fff;
          }
        }
      }
  }

  .card {
      display: flex;
      flex-direction: row;
      padding: 8upx 16upx;
      &-img {
          width: 100upx;
          height: 100upx;
          margin-left: 48px;
      }
      &-content {

          &-title {
              font-size: 32upx;
          }
          &-desc {
              font-size: 22upx;
              color: #aaa;
          }
      }
  }
