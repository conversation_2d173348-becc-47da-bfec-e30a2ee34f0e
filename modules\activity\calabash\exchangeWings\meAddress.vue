<template>
  <view class="pages">
    <!-- 手动获取微信收货地址 -->
<!--    <view class="getWXaddress" @click="getWXaddress">
      <image class="iconRightArrow" :src="iconRightArrow" mode="aspectFill"></image>
    </view> -->
    <!-- 地址列表 -->
    <scroll-view class="goodsMap" v-if="addressMap.length" scroll-y="true" @scrolltolower="lower">
      <view class="addressContent" :class="{currentAddress:currentAddressIndex === index}" @click="selectAddress(index)" v-for="(item,index) in addressMap">
        <view class="addressTitle">
          <view>
            <image class="selectIcon" v-if="currentAddressIndex === index" :src="selected" mode="aspectFill"></image>
            收货人：{{item.username}}
          </view>
          <text>{{item.phone}}</text>
        </view>
        <!-- 地址 -->
        <view class="addressIn">{{item.province}} {{item.city}} {{item.county}} {{item.address}}</view>
        <!-- 操作按钮 -->
        <view class="emitBtns">
          <view class="defaultAddress"  @click.stop="selectIcon(index)" :class="{selectAddress:item.isPrimary}">
            <image class="selectIcon" :src="item.isPrimary ? selected : unselected" mode="aspectFill"></image>
            默认地址
          </view>
          <view class="editor">
            <view class="editorBox" @click.stop="deleteItem(index)"><image class='editorIcon' :src="deleteIcon" mode="aspectFill"></image>删除</view>
            <view class="editorBox" @click.stop="gotoAddAddress(item)"><image class='editorIcon' :src="editorIcon" mode="aspectFill"></image>编辑</view>
          </view>
          </view>
      </view>
    </scroll-view>
    <!-- 暂无地址 -->
    <view class="addressPitera" v-else>
      <image class="addressPiteraIcon" :src="addressPitera" mode="aspectFill"></image>
      <view class="addressTitle">暂无收获地址</view>
    </view>
    <!-- 新增按钮 -->
    <view class="addAddress" @click="gotoAddAddress(false)">新增地址</view>
  </view>
</template>

<script>
  import calabashApis from "@/modules/common/api/calabash.js"
  import { mapState } from "vuex";
  export default{
    data(){
      return {
        iconRightArrow: this.$static_ctx + "image/business/hulu-v2/icon-right-arrow.png",
        weChat: this.$static_ctx + "image/business/hulu-v2/weChat.png",
        unselected: this.$static_ctx + "image/business/hulu-v2/unselected.png",
        selected: this.$static_ctx + "image/business/hulu-v2/selected.png",
        deleteIcon: this.$static_ctx + "image/business/hulu-v2/delete.png",
        editorIcon: this.$static_ctx + "image/business/hulu-v2/editor.png",
        addressPitera: this.$static_ctx + "image/business/hulu-v2/addressPitera.png",
        current:0,
        loadEnd:false,
        addressMap:[],
        currentAddressIndex:null
      }
    },
    computed: {
      ...mapState("user", {
        accountId: (state) => state.accountId,
        fansRecord: (state) => state.fansRecord,
      }),
    },
    onShow() {
      this.current = 0;
      this.addressMap.length = 0;
      this.loadEnd = false;
      this.getAddress()
    },
    methods:{
      selectAddress(index){
        this.currentAddressIndex = index;
        console.log('this.addressMap',this.addressMap[index]);
        uni.setStorageSync('currentAddressId',this.addressMap[index].id)
        uni.navigateBack()
      },
      gotoAddAddress(item){
        if(item){
          uni.navigateTo({
            url:'/modules/activity/calabash/exchangeWings/addAddress?id=' + item.id,
          })
        }
        uni.navigateTo({
          url:'/modules/activity/calabash/exchangeWings/addAddress',
        })
      },
      async getAddress(){
        if(this.loadEnd) return
        this.current+=1
        // 获取商品列表
        let {data:{records,total}} = await calabashApis.pointaddressQueryPage({
          current:this.current,
          size:10,
          condition:{accountId:this.accountId},
        })
        if(total <= this.addressMap.length){
           this.loadEnd = true
           return
        }
        console.log('records',records);
        let currentAddressId = uni.getStorageSync('currentAddressId');
        records.map((e,index)=>{
          if(currentAddressId){
            if(e.id === currentAddressId) this.currentAddressIndex = index
            return
          }
          if(e.isPrimary) this.currentAddressIndex = index
        })
        this.addressMap.push(...records)
      },
      async selectIcon(key){
        let {data} = await calabashApis.pointaddressUpdate({...this.addressMap[key],isPrimary:1})
        this.addressMap.map((e,index)=>{
          this.$set(this.addressMap[index],'isPrimary',0)
        })
        this.addressMap[key].isPrimary = 1;
      },
      deleteItem(index){
        uni.showModal({
        title: '提示',
        content: '确定要删除该地址吗？',
        success: async (res)=> {
            if (res.confirm) {
              let {data} = await calabashApis.pointaddressDeleteOne(this.addressMap[index].id)
              this.addressMap.splice(index,1)
            }
          }
        });
      },
      lower(){
        this.getAddress()
      },
      getWXaddress(){
        wx.chooseAddress({
          success(res){
            console.log('res',res);
          },
          fail(err){
            console.log('请求失败',err);
          }
        })
      }
    }
  }
</script>

<style lang="scss">
  .selectIcon{
    width: 28rpx;
    height: 28rpx;
    margin-right: 10rpx;
  }
  .pages{
    width: 100vw;
    height: 100vh;
    background: #F4F6FA;
    overflow: scroll;
  }
  .goodsMap{
    width: 100vw;
    height:calc(100vh - 88rpx - 80rpx);
    padding: 20rpx 32rpx;
    box-sizing: border-box;
  }
  .getWXaddress{
    width: 686rpx;
    height: 104rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    position: relative;
    .iconRightArrow{
      width: 32rpx;
      height: 32rpx;
      position: absolute;
      top:50%;
      transform: translateY(-50%);
      right: 14rpx;
    }
  }
  .currentAddress{
    border: 1px solid #00B484;
  }
  .addressContent{
    width: 686rpx;
    height: 204rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    padding: 24rpx;
    box-sizing: border-box;
    margin-bottom: 20rpx;
    .addressTitle{
      font-weight: 400;
      font-size: 28rpx;
      color: #1D2029;
      display: flex;
      justify-content: space-between;
    }
    .addressIn{
      font-weight: 400;
      font-size: 24rpx;
      color: #4E5569;
      margin: 16rpx 0 32rpx 0;
    }
    .emitBtns{
      display: flex;
      justify-content: space-between;
      .defaultAddress{
        font-weight: 400;
        font-size: 24rpx;
        color: #4E5569;
        display: flex;
        align-items: center;
        .selectIcon{
          width: 28rpx;
          height: 28rpx;
          margin-right: 10rpx;
        }
      }
      .editor{
        display: flex;
        font-weight: 400;
        font-size: 24rpx;
        color: #4E5569;
        .editorIcon{
          width: 32rpx;
          height: 32rpx;
          margin-right: 2rpx;
        }
        .editorBox{
          display: flex;
          align-items: center;
          margin-right: 40rpx;
        }
      }
      .selectAddress{
        color: #00B484;
      }
    }

  }
  .addAddress{
    width: 686rpx;
    height: 88rpx;
    line-height: 88rpx;
    background: #00B484;
    border-radius: 44rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #FFFFFF;
    text-align: center;
    position: fixed;
    bottom: 80rpx;
    left: 50%;
    transform: translateX(-50%);
  }
  .addressPitera{
    width: 686rpx;
    height: 652rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    margin: 20rpx auto;
    display: flex;
    flex-wrap: wrap;
    text-align: center;
    font-weight: 400;
    font-size: 28rpx;
    color: #4E5569;
    .addressPiteraIcon{
      width: 238rpx;
      height: 190rpx;
      margin: 198rpx auto 0;
    }
    .addressTitle{
      width: 100%;
    }
  }
</style>
