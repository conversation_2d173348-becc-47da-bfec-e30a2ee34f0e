<template>
  <page>
    <view slot="content" class="bf">
      <swiper class="swiper" :style="{
        height:tabBar.length > 1 ? 'calc(100vh - 48px)' : '100vh'
      }" circular :current="current" :duration="duration" @change="changeCurrent">
        <swiper-item>
          <fillincase
            :submit-params="submitParams"
            :disabled="!!caseCollectSubmitLogId"
            :updatecount="updatecount2"
            :ids="id"
            :height="tabBar.length > 1 ? 'calc(100vh - 48px)' : '100vh'"
            :tenantId="tenantId"
            :isAccuratePromotion='collectionType == 3'
            :collectionType='collectionType'
            ref="fillincaseRef"
          ></fillincase>
          <!-- 888 -->
        </swiper-item>
        <swiper-item>
          <introduce-view
            :updatecount="updatecount1"
            :row="rowData"
            :height="tabBar.length > 1 ? 'calc(100vh - 48px)' : '100vh'"
            :pageid="id"
            :tenantId="tenantId"
          ></introduce-view>
          <!-- <view class="swiper-item uni-bg-green">B</view> -->
        </swiper-item>

      </swiper>
      <template v-if="tabBar.length != 1">
        <tabBar :list="tabBar" :activeindex="activeindex" @change="changePage"></tabBar>
      </template>
    </view>
  </page>
</template>

<script>
import tabBar from '@/modules/activity/components/tabBar/index.vue';
import introduceView from './components/introduce.vue';
import fillincase from './components/fillin.vue';
import caselist from './components/list.vue';
// import returnvisit from './components/returnvisit/index.vue';
import { isDomainUrl, getQueryObject } from '@/utils/index.js';
// const static_ctx = 'http://localhost:3000'
export default {
  name: 'questionnaireIndex',
  components: {
    tabBar,
    introduceView,
    fillincase,
    caselist,
    // returnvisit
  },
  data() {
    return {
      collectionType:null,
      updatecount1: 0,
      updatecount2: 0,
      updatecount3: 0,
      updatecount4: 0,
      rowData: {},
      activeindex: 0,
      duration: 500,
      current: 0,
      file_ctx: this.file_ctx,
      // $static_ctx: this.$static_ctx,
      $static_ctx: 'http://localhost:3000',
      scrollintotargetview: '',
      tabBar: [
        {
          pagePath: '/index/index2',
          iconPath: this.$static_ctx + 'image/business/case-collect/icon-im-edit.png',
          selectedIconPath: this.$static_ctx + 'image/business/case-collect/icon-im-edit_a.png',
          text: '填写问卷'
        },
        {
          pagePath: '/index/index',
          iconPath: this.$static_ctx + 'image/business/case-collect/icon-im-seek.png',
          selectedIconPath: this.$static_ctx + 'image/business/case-collect/icon-im-seek_a.png',
          text: '项目说明'
        }
      ],
      updatecount: 0,
      nowTimer: 0,
      id: '',
      caseCollectSubmitLogId: false, // 填写过的表单记录id
      submitParams: {}, // 表单提交参数
      taskId: '', // 问卷任务id
      tenantId: ''
    };
  },
  async onLoad(options) {
    let current = 0;
    if (options && options.current) {
      current = options.current - 0;
    }

    this.current = current;
    this.activeindex = this.current;

    let date = new Date();
    this.nowTimer = date.getTime();

    const query = this.$Route.query;
    if(this.$validate.isNull(query?.id)){
      let params = decodeURIComponent(query.scene)
      console.log("params:====",params)
      query.id = getQueryObject(params).id
    }
    if(!this.$validate.isNull(query?.i)) query.eId = query.i
    if(this.$validate.isNull(query?.eId)){
      let params = decodeURIComponent(query.scene)
      console.log("params:====",params)
      query.eId = getQueryObject(params).eId || getQueryObject(params).i
    }
    if (!this.$validate.isNull(query)) {
      this.id = query.id;
      this.tenantId = query.tenantId || '1'
    }

    // 问卷地推扫码进来
    if (!this.$validate.isNull(query) && !this.$validate.isNull(query.eId)) {
      this.taskId = query.eId
      await this.dmactivityexpandconfigQueryOneTaskId(query.eId)
    }

    if (!this.$validate.isNull(query) && !this.$validate.isNull(query.id) && !this.$validate.isNull(query.caseCollectSubmitLogId)) {
      this.caseCollectSubmitLogId = query.caseCollectSubmitLogId
      this.$nextTick(() => {
        this.$refs.fillincaseRef.setData({ id: this.id, mainId: query.caseCollectSubmitLogId })
      })
    }

    // 项目说明
    if (current == 0) {
      this.getDetail(this.id);
    }
    this.$nextTick(() => {
      this.updatecount1 += 1;
    });

    // if (res.data.title && res.data.title != '') {
    uni.setNavigationBarTitle({
      title: this.tabBar[this.activeindex].text
    });
    // }
  },
  methods: {
    // 根据问卷扫码的id 获取活动详情
    async dmactivityexpandconfigQueryOneTaskId(eId) {
      const params = {
        taskId: eId,
        accountId: this.$common.getKeyVal('user', 'accountId', true)
      }
      const res = await this.$api.activity.dmactivityexpandconfigQueryOneTaskId(params)
      // const data = res.data
      if (this.$validate.isNull(res.data)) {
        this.$uniPlugin.toast('参数异常')
        return Promise.reject()
      }
      const { activityId, caseCollectSubmitLogId, extendUserId, taskId, userName, tenantId = '1' } = res.data
      this.id = activityId
      this.caseCollectSubmitLogId = caseCollectSubmitLogId
      this.tenantId = tenantId
      console.log('tenantId---------', tenantId)
      this.$refs.fillincaseRef.setData({ id: activityId, mainId: caseCollectSubmitLogId, userName })
      this.submitParams = {
        inviterUserId: extendUserId,
        taskId: taskId
      }
    },
    // 病例征集状态
    caseStatus(now, start, end) {
      let obj = {};
      start = +start;
      // start = +end;
      end = +end;
      // end = +start;
      now = +now;
      if (now < start) {
        obj = {
          color: '#7c7c7c',
          tip: '未开始'
        };
      }
      if (now > start) {
        obj = {
          // color: 'red',
          tip: '进行中'
        };
      }
      if (now > end) {
        obj = {
          color: 'red',
          tip: '已结束'
        };
      }
      console.log(obj);

      return obj;
    },
    // 获取项目详情
    getDetail(id) {
      const that = this;
      that.$api.activity.researchQueryOne({ id: id }, { 'gb-part-tenant-id': that.tenantId }).then(res => {
        console.log('res', res);
        if (res.data instanceof Object) {
         res.data.pushTimeText = this.$common.formatDate(new Date(res.data.createTime), 'yyyy-MM-dd')

          // that.regForm = res.data
          // res.data.coverPath = '0/msg-reply/797072774330322949.jpeg';

          // console.log(isDomainUrl)
          res.data.coverPath = isDomainUrl(res.data.coverPath);
          this.collectionType = res.data.collectionType

          // res.data.statusobj = this.caseStatus(this.nowTimer, res.data.startTime, res.data.endTime);



          this.rowData = res.data;
          console.log('this.rowData', this.rowData);
        }
        // that.$api.activity.queryBusinessTemplateDTOList({ templateIds: that.regForm.imagesTemplateIds }).then(res => {
        //   that.templateData = res.data
        //   if (that.templateData[0]) {//至少获取一个
        //       if (that.templateData[0].businessTemplate.type === 2) {
        //         that.openTemplate = true
        //       }
        //   }
        //
        // })
        // that.$api.activity.activityAuditDetail({ id: id }).then(res => {
        //   that.auditLog = res.data
        //   that.templateData = res.data.templateVos
        //   if (that.templateData[0]) {//至少获取一个
        //       if (that.templateData[0].businessTemplate.type === 2) {
        //         that.openTemplate = true
        //       }
        //   }
        // })
      });
    },

    changePage(e) {
      console.log(e)
      let current = e.current;
      this.current = current;
    },
    changeCurrent(e) {
      let current = e.detail.current;
      this.activeindex = current;
      this.current = current;
      if (current == 0) {
        this.$nextTick(() => {
          this.updatecount1 += 1;
        });
      } else if (current == 1) {
        if (!this.taskId && !this.caseCollectSubmitLogId) {
          this.$nextTick(() => {
            this.updatecount2 += 1;
          });
        }
      } else if (current == 2) {
        this.$nextTick(() => {
          this.updatecount3 += 1;
        });
      } else if (current == 3) {
        this.$nextTick(() => {
          this.updatecount4 += 1;
        });
      }
      uni.setNavigationBarTitle({
        title: this.tabBar[this.activeindex].text
      });

      // this.getTemplateStep()
      // 填写病例
      // if(current == 1){
      // this.getTemplateStep();
      // }
      // console.log('val',val)

      // this.$emit()
    },
    toTarget() {
      console.log('kkk');
      this.scrollintotargetview = 'i2d';
      console.log(this.scrollintotargetview);
    }
  }
};
</script>

<style scoped lang="scss">
.bf {
  background-color: #fff;
}
.swiper {
  height: calc(100vh - 48px);
}
// .page{
//   height: 100vh;
// }
// .frame-page-box {
//   height: 100vh;
//   padding-bottom: 48px;
//   box-sizing: border-box;
// }
</style>
