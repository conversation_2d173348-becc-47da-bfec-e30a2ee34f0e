<template>
  <page>
    <view slot="content" class="advice">
      <view class="li">
        <view class="info">
          <view class="img">
            <default-img :config="config.avatar"/>
          </view>
          <text class="name">{{regForm.author? regForm.author : '管理员'}}</text>
          <text class="time">{{$timePlugin.formatDate(new Date().valueOf(), regForm.createTime)}}</text>
        </view>
        <view class="main-content">
          <view class="title">
            <text class="name">{{regForm.title}}</text>
            <text class="label topicC" v-if="regForm.receiveType==1">全部</text>
            <text class="label topicC" v-if="regForm.receiveType==2">定向</text>
            <text class="label blue" v-if="regForm.businessType==1">普通</text>
            <text class="red-point" v-if="regForm.readStatus==2"></text>
          </view>
          <rich-text v-if="regForm.contentHtml" :nodes="regForm.contentHtml"></rich-text>
          <view class="article" v-else>
            <text>{{regForm.content}}</text>
          </view>
        </view>
      </view>
    </view>
  </page>
</template>

<script>
export default {
  name: 'NoticeAdviceDetail',
  components: {
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $timePlugin: this.$timePlugin,
      regForm: {},
      id: undefined,
      index: undefined,
      config: {
        avatar: {
          widthHeightAuto: true
        }
      }
    }
  },
  onLoad(paramsObj) {
    const query = this.$Route.query
    if (!this.$validate.isNull(query)) {
      this.id = query.id
      this.index = query.index
    }
    this.id && this.getDetail(this.id)
  },
  methods: {
    getDetail(id) {
      const that = this
      that.$api.common.noticelogQueryAppOne({ id: id }).then(res => {
        that.regForm = res.data
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.advice{
  width: 100%;
  height: 100%;
  overflow-y: auto;
  background: #f7f7f7;
  .li{
    background: #fff;
    width: 100%;
    margin-top: 20upx;
    overflow: hidden;
    .main-content{
      width: 100%;
      padding: 16upx;
      box-sizing: border-box;
      overflow: hidden;
      .title{
        width: 100%;
        padding-bottom: 20upx;
        .name{
          font-size: 28upx;
          color: #333333;
          display: block;
          margin:0 14upx 10upx 0;
        }
        .label{
          padding: 6upx;
          margin:10upx 14upx 0 0;
          display: inline-block;
          border: 1px solid #BFBFBF;
          color: #BFBFBF;
          font-size: 20upx;
          @include rounded(6upx);
          /*vertical-align: top;*/
        }
        .blue {
          color: #187FFF;
          border: 1px solid #187FFF;
        }
        .red-point{
          width: 10upx;
          height: 10upx;
          @include rounded(50%);
          margin-left: 10upx;
          display: inline-block;
          background-color: red;
        }
      }
      .article{
        line-height: 1.5;
        font-size: 28upx;
        color: #666666;
        padding-bottom: 20upx;
      }
    }
    .receive-reading{
      border-top: 20upx solid #f7f7f7;
      .title{
        padding: 30upx;
        font-size: 32upx;
        color: #333333;
        font-weight: bold;
      }
      .content{}
      .reminder{
        padding: 30upx;
        .num{
          display: inline-block;
          font-size: 32upx;
          color: #333333;
          margin-right: 8upx;
          vertical-align: middle;
          float: left;
        }
        .btn{
          display: inline-block;
          color: #FFFFFF;
          background-color: $topicC;
          padding: 12upx 24upx;
          font-size: 28upx;
          vertical-align: middle;
          @include rounded(24upx);
          float: right;
        }
      }
    }
    .info{
      width: 100%;
      padding:26upx 30upx 30upx;
      box-sizing: border-box;
      overflow: hidden;
      .img{
        float: left;
        width: 66upx;
        height: 66upx;
        margin-right: 10upx;
        @include rounded(50%);
        overflow: hidden;
        image{
          width: 100%;
          height: 100%;
        }
      }
      .name,.time{
        float: left;
        margin-top: 20upx;
        color: #333333;
        font-size: 28upx;
      }
      .time{
        float: right;
        color: #999999;
        font-size: 24upx;
      }
    }
  }
}
.preview-image{
  /*margin-left: 30upx;*/
  overflow: hidden;
  margin-bottom: 20upx;
  padding-bottom: 20upx;
  .img-li{
    margin-top: 20upx;
    margin-right: 20upx;
    width: 216upx;
    height: 216upx;
    overflow: hidden;
    @include rounded(8upx);
    position: relative;
    .role-image{
      width: 100%;
      height: 100%;
    }
    .img-li-num{
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      text{
        text-align: center;
        width: 100%;
        color: #fff;
        font-size: 72upx;
        position: absolute;
        left: 0;
        top: 50%;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
        font-weight: 600;
      }
    }
  }
  .img-li:nth-child(3n){
    margin-right: 0;
  }

}
.topicC{
  color: $topicC!important;
  border: 1px solid $topicC!important;
}
.color-border-red{
  color:red!important;
  border:1px solid red!important;
}
</style>
