import user from '@/modules/common/ext/user'
import wechat from '@/modules/common/ext/wechat'
import pay from '@/modules/common/ext/pay'
import utility from '@/modules/common/ext/utility'
import common from '@/modules/common/ext/common'
import VueWebSocket from './modules/websocket/index'
import chat from '@/modules/common/ext/chat'
import systemMenuList from '@/modules/common/ext/system-menu-list'
import community from '@/modules/common/ext/community'
import circleclassify from '@/modules/common/ext/circleclassify'
import alipay from '@/modules/common/ext/alipay'

/**
 * 框架扩展目录，主要放置 数据接口扩展逻辑，针对接口做ETL或者封装，隔离接口
 * @type {{etl: {}}}
 */
const ext = {
    wechat,
    user,
    pay,
    utility,
    common,
    systemMenuList,
    // webSocket: VueWebSocket,
    webSocket: new VueWebSocket(),
    chat,
    community,
    circleclassify,
    alipay
}
export default ext
