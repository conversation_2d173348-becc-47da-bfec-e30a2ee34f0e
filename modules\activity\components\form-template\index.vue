<template>
  <scroll-view
    :style="{
      height: height
    }"
    :scroll-y="true"
    scroll-with-animation
    :scroll-into-view="scrollintotargetview"
  >
    <!-- 选择表单类型 -->
    <titleLoading v-if="steps.length == 0 && !initstep"></titleLoading>
    <template v-if="steps.length > 1">
      <title-step :updatecount="updateinit" :list="steps" @change="changeStep" :step="step" :update="updatestep"></title-step>
    </template>

    <view class="template-content">
      <!-- 表单类型 -->
      <template v-if="updateforminit && submittype == 2 && healthType !== 4">
        <template v-for="(item, index) in config">
          <!--<slot v-if="item.slot" :name="item.name"></slot>-->
          <view :id="'target' + index" :key="item.id">
            <!--类型：1-单选题(radio)，2-多选题(checkbox)，3-文本题，4-数值题(slider)，5-文件上传(file)，6-共用题干题（嵌套题），7-手机号码，8-邮寄地址-->
            <!--文本题类型：1-单行输入框(input)，2-日期，3-时间，4-日期时间，5-多行输入框(textarea)-->
            <template v-if="item.type === 'radio'">
              <title-radio :disabled="item.disabled" :config="item" :cData="regForm[item.name]" @updateForm="updateForm"></title-radio>
            </template>
            <template v-if="item.type === 'checkbox'">
              <title-checkbox :disabled="item.disabled" :config="item" :cData="regForm[item.name]" @updateForm="updateForm"></title-checkbox>
            </template>
            <template v-if="item.type === 'slider'">
              <title-slider :disabled="item.disabled" :config="item" :cData="regForm[item.name]" @updateForm="updateForm"></title-slider>
            </template>
            <template v-if="item.type === 'file'">
              <title-lvfile :config="item" :attachmentList="regForm[item.name]" @updateForm="updateForm" @returnFn="imgReturnFn"></title-lvfile>
              <!-- <title-file :config="{padding: 0, background: 'none'}" :disabled="true" :cData="regForm.attachmentList"></title-file> -->
            </template>
            <template v-if="item.type === 'input'">
              <title-input :disabled="item.disabled" :config="item" :cData="regForm[item.name]" @updateForm="updateForm"></title-input>
            </template>
            <template v-if="item.type === 'textarea'">
              <title-textarea :child="true" :disabled="item.disabled" :config="item" :cData="regForm[item.name]" @updateForm="updateForm"></title-textarea>
            </template>
            <template v-if="item.type === 'phone'">
              <title-phone :disabled="item.disabled" :config="item" :cData="regForm[item.name]" @updateForm="updateForm"></title-phone>
            </template>
            <template v-if="item.type === 'address'">
              <title-address :disabled="item.disabled" :config="item" :cData="regForm[item.name]" @updateForm="updateForm"></title-address>
            </template>
            <template v-if="item.type === 'lvSlider'">
              <!-- lvSlider -->
              <title-lvsliver :config="item" :cdata="regForm[item.name]" :onlykey="item.name" @update="updateValue"></title-lvsliver>
            </template>
            <!-- 我是公干题 -->
            <template v-if="item.type === 'lvQuestion'">
              <!-- :cdata="regForm[item.name]" -->
              <titleLvquestion @returnFn="imgReturnFn" :config="item" @updateForm="updateForm" @updateValue="updateValue2" :onlykey="item.name"></titleLvquestion>
            </template>

            <template v-if="item.type === 'date'">
              <title-date :config="item" :cdata="regForm[item.name]" @update="updateDate"></title-date>
              <!-- :cdata="regForm[item.name]" -->
              <!-- <titleLvquestion @returnFn="imgReturnFn" :config="item" @updateForm="updateForm" @updateValue="updateValue2" :onlykey="item.name"></titleLvquestion> -->
            </template>

            <template v-if="item.type === 'rate'">
                 <!-- 667 -->
                 <!-- <title-input ></title-input> -->
                 <title-rate :disabled="item.disabled" :config="item" :cData="regForm[item.name]" @updateForm="updateForm"></title-rate>
                <!-- <uni-rate :margin="8" :size="36" :value="5" :readonly='item.readonly' /> -->
            </template>

            <!-- 特定功能组件 -->
            <template v-if="item.type === 'pointFunctionSex'">
              <title-sex :isAccuratePromotion='isAccuratePromotion' :disabled="item.disabled" :config="item" :cData="regForm[item.name]" @updateForm="updateForm"></title-sex>
            </template>
            <template v-if='item.type === "pointFunctionWxNick"'>
              <title-wx-nick :isAccuratePromotion='isAccuratePromotion' :disabled="item.disabled" :config="item" :cData="regForm[item.name]" @updateForm="updateForm"></title-wx-nick>
            </template>
            <template v-if='item.type === "pointFunctionAge"'>
              <title-age :isAccuratePromotion='isAccuratePromotion' :disabled="item.disabled" :config="item" :cData="regForm[item.name]" @updateForm="updateForm"></title-age>
            </template>
            <template v-if='item.type === "pointFunctionDepartment"'>
              <title-department :isAccuratePromotion='isAccuratePromotion' :disabled="item.disabled" :config="item" :cData="regForm[item.name]" @updateForm="updateForm"></title-department>
            </template>
            <template v-if='item.type === "pointFunctionProvinceCity"'>
              <title-province :isAccuratePromotion='isAccuratePromotion' :disabled="item.disabled" :config="item" :cData="regForm[item.name]" @updateForm="updateForm"></title-province>
            </template>
            <template v-if='item.type === "pointFunctionDiseaseIndex"'>
              <title-disease :isAccuratePromotion='isAccuratePromotion' :disabled="item.disabled" :config="item" :cData="regForm[item.name]" @updateForm="updateForm"></title-disease>
            </template>
            <template v-if='item.type === "pointFunctionName"'>
              <title-name :isAccuratePromotion='isAccuratePromotion' :disabled="item.disabled" :config="item" :cData="regForm[item.name]" @updateForm="updateForm"></title-name>
            </template>
            <template v-if="item.type === 'dateAll'">
              <titleDateAll
                :config="item"
                :disabled="item.disabled"
                :cData="regForm[item.name]"
                :isAccuratePromotion='isAccuratePromotion'
                @updateForm="updateForm"

              ></titleDateAll>
              <!-- :cdata="regForm[item.name]" -->
              <!-- <titleLvquestion @returnFn="imgReturnFn" :config="item" @updateForm="updateForm" @updateValue="updateValue2" :onlykey="item.name"></titleLvquestion> -->
            </template>
            <!--  -->

            <!-- date -->
          </view>
        </template>
      </template>

      <!-- 文档类型 -->
      <template v-if="updateforminit && submittype == 1 && healthType !== 4">
        <doc-template :arr="config" :value="filelist" :businessId="businessid" @returnFn="returnFnDom"></doc-template>
        <!-- <web-view :src="docsrc"></web-view> -->
        <!-- <doc-template v-if="!openTemplate" :businessId="regForm.id" :auditLog="auditLog" :attachmentList="auditLog.attachmentList===''?[]:auditLog.attachmentList" :templateData="templateData" @returnFn="returnDoc"></doc-template> -->
      </template>

      <!-- 患者登记 -->
      <template v-if="updateforminit && submittype == 3 && healthType !== 4">

        <view class="content-t">
          <view class="content-title">基础信息</view>
          <view class="content-tip">
            ( 带
            <text class="red">*</text>
            内容必填 )
          </view>
        </view>
        <!-- 快捷输入 -->
         <title-selector v-model="tepId" @updateForm='updateChange' :config="tepconfig" />


        <template v-for="(item, index) in config">
          <template v-if="item.belong == 'base'">
          <view :id="'target' + index" :key="index">
              <template v-if="item.type === 'radio'">
                <title-radio horizontal :disabled="item.disabled" :config="item" :cData="regForm[item.name]" @updateForm="updateForm"></title-radio>
              </template>

              <template v-else-if="item.type === 'input'">
                <title-input-two horizontal :disabled="item.disabled" :config="item" v-model="regForm[item.name]" @updateForm="updateForm"></title-input-two>
              </template>
              <template v-else-if="item.type === 'textarea'">
                <title-textarea-two :child="true" :disabled="item.disabled" :config="item" v-model="regForm[item.name]" @updateForm="updateForm"></title-textarea-two>
              </template>
              <template v-else-if="item.type === 'phone'">
                <title-phone :disabled="item.disabled" :config="item" :cData="regForm[item.name]" @updateForm="updateForm"></title-phone>
              </template>
              <template v-else-if="item.type === 'address'">
                <title-address :disabled="item.disabled" :config="item" :cData="regForm[item.name]" @updateForm="updateForm"></title-address>
              </template>


           <!--   <template v-else-if="item.type === 'address'">
                <title-address :disabled="item.disabled" :config="item" :cData="regForm[item.name]" @updateForm="updateForm"></title-address>
              </template> -->
              <template v-else-if="item.type == 'select'">
                <title-selector v-model="regForm[item.name]" :config="item" />
              </template>

              <template v-else-if="item.type === 'date'">
                <title-jump-date :isRequired='item.required' :nextRequest='item.required'  v-model="regForm[item.name]" :title="item.label" :endDate="$timePlugin.parseTime(new Date(), '{y}-{m}-{d}')"></title-jump-date>
              </template>


              <template v-if="item.type === 'file'">
                <title-lvfile :bold='false' :config="item" :attachmentList="regForm[item.name]" @updateForm="updateForm" @returnFn="imgReturnFn"></title-lvfile>
                <!-- <title-file :config="{padding: 0, background: 'none'}" :disabled="true" :cData="regForm.attachmentList"></title-file> -->
              </template>
          </view>
          </template>
        </template>

        <view class="content-t">
          <view class="content-title">历史病况</view>
          <view class="content-tip">
            ( 带
            <text class="red">*</text>
            内容必填 )
          </view>
        </view>
        <template v-for="(item, index) in config">
          <template v-if="item.belong == 'history'">
          <view :id="'target' + index" :key="index">
              <template v-if="item.type === 'radio'">
                <title-radio horizontal :disabled="item.disabled" :config="item" :cData="regForm[item.name]" @updateForm="updateForm"></title-radio>
              </template>

              <template v-else-if="item.type === 'input'">
                <title-input-two horizontal :disabled="item.disabled" :config="item" v-model="regForm[item.name]" @updateForm="updateForm"></title-input-two>
              </template>
              <template v-else-if="item.type === 'textarea'">
                <title-textarea-two :child="true" :disabled="item.disabled" :config="item" v-model="regForm[item.name]" @updateForm="updateForm"></title-textarea-two>
              </template>
              <template v-else-if="item.type === 'phone'">
                <title-phone :disabled="item.disabled" :config="item" :cData="regForm[item.name]" @updateForm="updateForm"></title-phone>
              </template>
              <template v-else-if="item.type === 'address'">
                <title-address :disabled="item.disabled" :config="item" :cData="regForm[item.name]" @updateForm="updateForm"></title-address>
              </template>

              <template v-else-if="item.type == 'select'">
                <title-selector v-model="regForm[item.name]" :config="item" />
              </template>

              <template v-else-if="item.type === 'date'">
                <title-jump-date :isRequired='item.required' :nextRequest='item.required' v-model="regForm[item.name]" :title="item.label" :endDate="$timePlugin.parseTime(new Date(), '{y}-{m}-{d}')"></title-jump-date>
              </template>

              <template v-if="item.type === 'file'">
                <title-lvfile :bold='false' :config="item" :attachmentList="regForm[item.name]" @updateForm="updateForm" @returnFn="imgReturnFn"></title-lvfile>
                <!-- <title-file :config="{padding: 0, background: 'none'}" :disabled="true" :cData="regForm.attachmentList"></title-file> -->
              </template>
          </view>
          </template>
        </template>

        <view class="content-t">
          <view class="content-title">诊断疾病</view>
          <view class="content-tip">
            ( 带
            <text class="red">*</text>
            内容必填 )
          </view>
        </view>

        <template v-for="(item, index) in config">
          <template v-if="item.belong == 'diagnose'">
          <view :id="'target' + index" :key="index">

              <template v-if="item.type === 'radio'">
                <title-radio horizontal :disabled="item.disabled" :config="item" :cData="regForm[item.name]" @updateForm="updateForm"></title-radio>
              </template>

              <template v-else-if="item.type === 'input'">
                <title-input-two horizontal :disabled="item.disabled" :config="item" v-model="regForm[item.name]" @updateForm="updateForm"></title-input-two>
              </template>
              <template v-else-if="item.type === 'textarea'">
                <title-textarea-two :child="true" :disabled="item.disabled" :config="item" v-model="regForm[item.name]" @updateForm="updateForm"></title-textarea-two>
              </template>
              <template v-else-if="item.type === 'phone'">
                <title-phone :disabled="item.disabled" :config="item" :cData="regForm[item.name]" @updateForm="updateForm"></title-phone>
              </template>
              <template v-else-if="item.type === 'address'">
                <title-address :disabled="item.disabled" :config="item" :cData="regForm[item.name]" @updateForm="updateForm"></title-address>
              </template>

              <template v-else-if="item.type == 'select'">
                <title-selector v-model="regForm[item.name]" :config="item" />
              </template>

              <template v-else-if="item.type === 'date'">
                <title-jump-date :isRequired='item.required' :nextRequest='item.required' v-model="regForm[item.name]" :title="item.label" :endDate="$timePlugin.parseTime(new Date(), '{y}-{m}-{d}')"></title-jump-date>
              </template>

              <template v-if="item.type === 'file'">
                <title-lvfile :bold='false' :config="item" :attachmentList="regForm[item.name]" @updateForm="updateForm" @returnFn="imgReturnFn"></title-lvfile>
                <!-- <title-file :config="{padding: 0, background: 'none'}" :disabled="true" :cData="regForm.attachmentList"></title-file> -->
              </template>
          </view>
          </template>
        </template>

        <view class="content-t">
          <view class="content-title">其他报告</view>
          <view class="content-tip">
            ( 带
            <text class="red">*</text>
            内容必填 )
          </view>
        </view>

        <template v-for="(item, index) in config">
          <template v-if="item.belong == 'other'">
            <view :id="'target' + index" :key="index">
              <template v-if="item.type === 'radio'">
                <title-radio horizontal :disabled="item.disabled" :config="item" :cData="regForm[item.name]" @updateForm="updateForm"></title-radio>
              </template>

              <template v-else-if="item.type === 'input'">
                <title-input-two horizontal :disabled="item.disabled" :config="item" v-model="regForm[item.name]" @updateForm="updateForm"></title-input-two>
              </template>
              <template v-else-if="item.type === 'textarea'">
                <title-textarea-two :child="true" :disabled="item.disabled" :config="item" v-model="regForm[item.name]" @updateForm="updateForm"></title-textarea-two>
              </template>
              <template v-else-if="item.type === 'phone'">
                <title-phone :disabled="item.disabled" :config="item" :cData="regForm[item.name]" @updateForm="updateForm"></title-phone>
              </template>
              <template v-else-if="item.type === 'address'">
                <title-address :disabled="item.disabled" :config="item" :cData="regForm[item.name]" @updateForm="updateForm"></title-address>
              </template>

              <template v-else-if="item.type == 'select'">
                <title-selector v-model="regForm[item.name]" :config="item" />
              </template>

              <template v-else-if="item.type === 'date'">
                <title-jump-date :isRequired='item.required' :nextRequest='item.required'  v-model="regForm[item.name]" :title="item.label" :endDate="$timePlugin.parseTime(new Date(), '{y}-{m}-{d}')"></title-jump-date>
              </template>

              <template v-if="item.type === 'file'">
                <title-lvfile :bold='false' :config="item" :attachmentList="regForm[item.name]" @updateForm="updateForm" @returnFn="imgReturnFn"></title-lvfile>
                <!-- <title-file :config="{padding: 0, background: 'none'}" :disabled="true" :cData="regForm.attachmentList"></title-file> -->
              </template>
            </view>
          </template>
        </template>

        <!-- 输入框 -->

        <!-- 文本框 -->

        <!-- 输入框 -->

        <!-- 文件选择框 -->
      </template>

      <!-- 健康自测 -->
      <template v-if="updateforminit && healthType == 4">
        <healthTemp class="health-temp" :configList="formTempObj.formTemplates" :businessId="businessid" :currentNum="currentNum" @updateForm="updateForm2" @handleCommit="handleEmit" :cmainid="cmainid" :healthTesting="healthTesting"></healthTemp>
      </template>

      <!-- <template v-if="updateforminit && ">
        <template v-for="item in businessConfig">
          <view :id="'target' + index" :key="item.id">

          </view>
        </template>
      </template> -->


      <!-- 诊后点评反馈意见 -->
      <template v-if="additionalArr && additionalArr.length != 0 && extraVisible && pharmacyType == 1">
        <template v-for="item in additionalArr">
          <template v-if="item.type === 'input'">
            <title-input :disabled="item.disabled" :config="item" :cData="additionalForm[item.name]" @updateForm="updateExtrForm"></title-input>
          </template>
          <template v-if="item.type === 'textarea'">
            <title-textarea :child="true" :disabled="item.disabled" :config="item" :cData="additionalForm[item.name]" @updateForm="updateExtrForm"></title-textarea>
          </template>
        </template>
      </template>
    </view>
    <!-- <view class="loadingbox">
      <image src="/static/image/business/loading.svg"></image>
    </view> -->
    <view class="template-footer" v-if="config.length > 0 && !nosubmit">
      <!-- :class="{
          'b-btn-color': isSubmit,
        }" -->
      <template v-if="steps.length == 1 && isone">
        <!-- {{ !isSubmit ? "确认提交" : "已提交" }} -->
        <!--  <view class="btn-bg m-tb-20-auto" @tap="submit()" :class="[submittype == 1 ? 'disabled' : '']">

          保存并下一步
        </view> -->
        <button type="default" :loading="loading" :disabled='cDisabled' class="btn-bg m-tb-20-auto" @tap="submit()">{{submitText ? submitText : '保存并下一步'}}</button>
      </template>
      <template v-else>
        <!-- :class="[submittype == 1 ? 'disabled' : '']" -->
        <button v-if="healthTypeFlag" type="default" :disabled='cDisabled' :loading="loading" class="querybtn" @click="submit()">{{submitBtnText}}</button>
        <!-- <view >保存</view> -->
      </template>
      <view class="nextbtn" @click="toggleStep" v-if="steps.length != 1 && ptype == 1">
        <template v-if="step != steps.length">
          下一步
        </template>
        <template v-else>
          上一步
        </template>
      </view>
    </view>
    <nomore type="2" v-else-if="submittype == 2 && config.length == 0 && initstep && healthTypeFlag"></nomore>

    <view class="o-space" v-if="healthTypeFlag"></view>
  </scroll-view>
</template>

<script>
import TitleRadio from '@/modules/activity/components/title-radio/index';
import TitleCheckbox from '@/modules/activity/components/title-checkbox/index';
import TitleInput from '@/modules/activity/components/title-input/index';
import TitleTextarea from '@/modules/activity/components/title-textarea/index';
import TitleSlider from '@/modules/activity/components/title-slider/index';
import TitleFile from '@/modules/activity/components/title-file/index';
import TitlePhone from '@/modules/activity/components/title-phone/index';
import TitleAddress from '@/modules/activity/components/title-address/index';
import titleLvsliver from '@/modules/activity/components/title-lvsliver/index.vue';
import titleLvquestion from '@/modules/activity/components/title-lvquestion/index.vue';
import titleLvfile from '@/modules/activity/components/title-lvfile/index.vue';
import titleRate from '@/modules/activity/components/title-rate/index.vue';
import titleDate from '@/modules/activity/components/title-date/index.vue';
import titleStep from '../title-steps/index.vue';
import titleLoading from '../title-loading/index.vue';
import docTemplate from '@/modules/activity/components/doc-template/index.vue';

import TitleInputTwo from '@/components/business/module/v2/title-input/index';
import TitleTextareaTwo from '@/components/business/module/v2/title-textarea/index';
import healthTemp from '@/modules/activity/health-testing/components/add/index';
// import TitleTextareaTwo from '@/components/business/module/v2/title-textarea/index';

import nomore from '../nomore/index.vue';

import { defaultObject, link, valueidx, labelidx } from '@/modules/activity/constant/modules/formTemplate.js';

// import titleLvfile from '@/components/business/module/v2/title-lvfile/index.vue';
import TitleJumpDate from '@/components/business/module/v1/title-jump-date/index.vue';
import TitleSelector from '@/components/business/module/v1/title-selector/index.vue';
import titleDateAll from "../title-datetime-picker/index.vue";

// 特定功能控件
import titleSex from '@/modules/activity/components/title-sex/index.vue'
import titleWxNick from '@/modules/activity/components/title-wx-nick/index.vue'
import titleAge from '@/modules/activity/components/title-age/index.vue'
import titleDepartment from '@/modules/activity/components/title-department/index.vue'
import titleDisease from '@/modules/activity/components/title-province/diseaseIndex.vue'
import titleProvince from '@/modules/activity/components/title-province/index.vue'
import titleName from '@/modules/activity/components/title-name/index.vue'

export default {
  name: 'form-template',
  components: {
    titleSex,
    titleWxNick,
    titleAge,
    titleDepartment,
    titleDisease,
    titleProvince,
    titleName,


    TitleInput,
    TitleRadio,
    TitleCheckbox,
    TitleTextarea,
    TitleSlider,
    TitleFile,
    TitlePhone,
    TitleAddress,
    titleLvsliver,
    titleLvquestion,
    titleLvfile,
    titleStep,
    nomore,
    titleDate,
    titleLoading,
    docTemplate,
    TitleInputTwo,
    TitleTextareaTwo,

    TitleJumpDate,
    TitleSelector,
    titleRate,
    healthTemp,
    titleDateAll

  },
  props: {
    // 提交按钮文案
    submitText:{
      type:String,
      default:null
    },
    healthTypeFlag:{
      type:Boolean,
      default:true
    },
    currentNum:{
      type:Number,
      default:0
    },
    // 是否是精准地推
    isAccuratePromotion:{
      type:Boolean,
      default:false,
    },

    tenantId: {
      type: String,
      default: '1'
    },
    // 禁用
    cDisabled:{
      type:Boolean,
      default:false,
    },
    submitBtnText:{
      type:String,
      default:'保存'
    },
    userDisabled:{
      type:Boolean,
      default:false,
    },
    extraForm:{
      type:Object,
      default:function () {
        return {}
      }
    },
    extraUpdate:{
      type:Number,
      default:0
    },
    extra:{
      type:Array,
      default:function () {
        return []
      }
    },
    isone:{
      type:Boolean,
      default:true,
    },
    // 参数设置
    // templateData: {
    //   type: Array,
    //   required: true,
    //   default: () => {
    //     return [];
    //   }
    // },
    // 业务主键
    businessid: {
      type: [String, Number],
      required: true,
      default() {
        return '';
      }
    },
    updatecount: {
      type: Number,
      default: 0
    },
    height: {
      type: String,
      default: ''
    },
    updatepage: {
      type: Number,
      default: 0
    },
    cmainid: {
      type: [String, Number],
      default: null
    },
    ptype: {
      type: [String, Number],
      default: 1
    },
    stepid: {
      type: [String, Number],
      default: ''
    },
    visitid: {
      type: [String, Number],
      default: ''
    },
    parentbusinessid: {
      type: [String, Number],
      default: ''
    },
    noloading: {
      type: Boolean,
      default: false
    },
    emainId: {
      type: [String, Number],
      default: null
    },
    submititemlogid: {
      type: [String, Number],
      default: null
    },
    nosubmit: {
      type: Boolean,
      default: false
    },
    pharmacyType: {
      type: [String, Number],
      default: ''
    },

    // 相关接口附带类型 activitytype 活动类型1病例征集2学术调研
    activitytype: {
      type: [Number, String],
      default: 1
    },
    healthType: {
      type: [Number, String],
      default: 1
    },
    healthTesting:{
      type: Object,
      default:function () {
        return {}
      }
    },
    collectionType: {
      type: Number,
      default: null,
    },
    // 省市 用于默认填充省市组件
    provinceCity: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // ID 记录 对应类型
      typeLogObject:{},
      // 姓名组件最大汉字个数
      maxChineseCount:4,
      // 问卷地推
      userName:"",

      additionalForm:{},
      additionalArr:[],
      extraVisible:true,

      extendValueForm:{},
      loading: false,
      updateforminit: false,
      updateinit: 0,
      scrollintotargetview: null,
      templateData: null,
      step: 0,
      updatestep: 0,
      steps: [],
      initstep: false,
      barHeight: 3,
      blockSize: 26,
      backgroundColor: '#EEEEF6',
      slider1: { min: 50, max: 200, step: 10, rangeValue: [50, 150] },
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $timePlugin: this.$timePlugin,

      isSubmit: false, //是否提交
      // id:'834610603728510977',
      regForm: {
        // id:''
        // isNotTo: 1,
        // isNotTo1: '',
        // isNotTo2: '',
        // isNotTo3: '',
        // isNotTo4: ''
      },
      config: [
        // {
        //   label: '1、为什么天空是蓝色，请回答！！',
        //   name: 'isNotTo',
        //   required: true,
        //   dicKey: 'deductType',
        //   array: [
        //     {
        //       key: 1,
        //       value: '不扣'
        //     },
        //     {
        //       key: 2,
        //       value: '扣'
        //     }
        //   ],
        //   type: 'radio'
        // },
        // {
        //   label: '1、为什么天空是蓝色，请回答！！',
        //   name: 'isNotTo1',
        //   required: true,
        //   dicKey: 'deductType',
        //   array: [
        //     {
        //       key: 1,
        //       value: '不扣'
        //     },
        //     {
        //       key: 2,
        //       value: '扣'
        //     }
        //   ],
        //   type: 'checkbox'
        // },
        // {
        //   label: '1、为什么天空是蓝色，请回答！！',
        //   name: 'isNotTo2',
        //   required: true,
        //   type: 'slider'
        // },
        // {
        //   label: '1、为什么天空是蓝色，请回答！！',
        //   name: 'isNotTo3',
        //   required: true,
        //   type: 'input'
        // },
        // {
        //   label: '1、为什么天空是蓝色，请回答！！',
        //   name: 'isNotTo4',
        //   required: true,
        //   type: 'textarea'
        // },
      ],
      formItem: {},

      // 当前节点id
      flowId: null,
      // 历史填写节点
      mainId: null,
      currentbusinessId: null,
      docsrc: null,
      submittype: 2,
      // 患者登记
      businessConfig: {},

      filelist: [],

      tepId:null,
      tepconfig:{
        label: '快捷选择患者', array: [],nextRequest:true
      },
      noEdit:false,
      formTempObj:{},
    };
  },
  mounted() {
    // this.getDetail('835483840192643077');
    // this.initi();
  },
  watch: {
    extraUpdate(n){
      console.log('jinlail',this.extraUpdate,this.extraForm)
      for(let key in this.extraForm){
        this.additionalForm[key] = this.extraForm[key]
      }
      this.extraVisible = false;
      this.$nextTick(() => {
        this.extraVisible = true
      })
    },
    noloading(n) {
      // console.log('chul');
      if (n) {
        this.loading = false;
      }
    },
    cmainid(n) {
      if (n && n != this.mainId) {
        this.mainId = this.cmainid;
      }
    },
    updatepage(n) {
      this.initstep = false;
      // 编辑其他条目
      if (this.emainId) {
        this.mainId = this.emainId;

        // 获取历史填写过的表单节点

        this.getTemplateStep();
      } else if (this.ptype == 1) {
        this.getHistory();
      } else if (this.ptype == 2) {
        // 回访记录另外一套

        this.getDetail(this.stepid, 'casecollectvisitconfigqueryone', this.submititemlogid).then(ret => {
          this.initstep = true;
        });

        this.$nextTick(() => {
          this.updateinit += 1;
        });
      } else if (this.ptype == 3){
        this.additionalArr = []
        // 拓展表单
        for(let i=0;i < this.extra.length;i++){
          this.additionalArr.push(this.extra[i])
        }
        this.gethospitalserviceevaluatequeryone()
      }
    },
    // templateData: {
    //   handler(val) {
    //     this.templateData = val
    //   },
    //   deep: true
    // },
    updatecount(n) {
      this.initstep = false;
      // // console.log('变了');
      // // console.log(this.templateData);
      // this.initi();
      // console.log('this.defaultObject', defaultObject);
      this.getHistory();
    }
  },
  methods: {
    handleEmit(){
      // console.log(555555)
      this.submit()
    },
    updateForm2(obj = {}){
      // console.log(obj,'obj22')
      obj.key = obj.key.trim();
      this.regForm[obj.key] = obj.value;
      this.extendValueForm[obj.key] = obj
    },
    // 校验输入汉字个数
    countChineseCharacters(str) {
        const regex = /[\u4e00-\u9fa5]/g;
        let count = 0;
        let match;
        while ((match = regex.exec(str)) !== null) {
            count++;
        }
        return count;
    },
    updateExtrForm(obj) {
      const that = this;
      // // console.log(obj);
      // if (obj.lvchild) {
      //   obj.key = obj.key.trim();
      //   if (that.regForm[obj.onlykey].child) {
      //     that.regForm[obj.onlykey].child[obj.key] = obj.value;
      //     that.extendValueForm[obj.key] = obj
      //   }
      //   // console.log(obj.key);
      //   // console.log(that.regForm[obj.onlykey]);
      // } else {
      obj.key = obj.key.trim();
      that.additionalForm[obj.key] = obj.value;
      // }
    },
    // 获取诊后点评详情
    gethospitalserviceevaluatequeryone(){
      const that = this
      that.$api.activity.hospitalserviceevaluatequeryone({ id: this.visitid }).then(res => {

        // res.data.pushTimeText = this.$common.formatDate(new Date(res.data.createTime), 'yyyy-MM-dd')
        // that.regForm = res.data
        that.templateId = res.data.templateId
        if(res.data == ''){
          return   that.$uniPlugin.toast('活动不存在');
        }
        // 活动已关闭
        if(res.data.openStatus === 2){
          this.noEdit = true
          this.regForm = {};
          this.config = [];
          this.steps = [{}]
          this.initstep = true

          return that.$uniPlugin.toast('活动已结束');
        }
        if(!res.data.templateId || res.data.templateId == ''){
          this.noEdit = true
          this.regForm = {};
          this.config = [];
          this.steps = [{}]
          this.initstep = true
          return that.$uniPlugin.toast('活动未绑定模板');
        }
        const cparams = {
          templateIds: res.data.templateId,
          isSelectWriteData:false
        }
        if(this.submititemlogid){
          cparams.formWriteBusinessId = this.submititemlogid
          cparams.isSelectWriteData = true
          cparams.accountId = this.$common.getKeyVal('user', 'accountId', true)
          this.noEdit = false;
        }else{
          this.noEdit = true
        }

        that.$api.activity.queryBusinessTemplateDTOList(cparams).then(res => {
          // this.templateData = res.data
          // that.openTemplate = true
          this.templateData = res.data[0];



          // this.businessConfig = [];

          this.regForm = {};
          this.config = [];
          this.initi();
          console.log("this.config",this.config)

          this.steps = [{}]
          this.initstep = true
        })
      })
    },
    // 获取快捷输入模板数据列表
    getQuickList(){
      console.log('getQuickList')
      const curSelectStore = this.$common.getKeyVal('user', 'curSelectStore', true)
      let recordId = curSelectStore.userTenantRecordList[0] ? curSelectStore.userTenantRecordList[0].recordId : null;

      if(!recordId){
        this.$uniPlugin.toast('recordId为空');
        return;
      }
      let param = {
        // current: pageNum,
        // size: pageSize,
        physicianId:recordId,
      }
      this.$api.activity.getpatientinfophysicianid(param, { 'gb-part-tenant-id': this.tenantId }).then(res => {
        // for (const a in res.data) {
        //   const data = res.data[a]
        //   data.label = data.name;
        //   data.value =
        // }
        for(let i=0;i<res.data.length;i++){
          res.data[i].label = res.data[i].name;
          res.data[i].value = i;
        }
        this.tepconfig.array = res.data
      })
    },
    updateChange(){
      setTimeout(() => {
        console.log('this.updateChange',this.tepId)

        console.log(this.tepconfig.array[this.tepId])
        if(this.tepconfig.array[this.tepId]){
          this.updateNode(this.tepconfig.array[this.tepId])
          this.updateforminit = false;
          this.$nextTick(() => {
            this.updateforminit = true;
          })
        }

      },600)

    },
    updateNode(patientInfo){
      const that = this;
      for(let i=0;i<this.config.length;i++){
        const ff = this.config[i];
        if (ff.type == 'input' || ff.type == "textarea") {
          // console.log('businessConfig[i].codeId',businessConfig[i].codeId)
          // console.log('ff', that.regForm[businessConfig[i].codeId]);
          if(ff.name == 'phone' && that.regForm[ff.name] == ''){
            that.regForm[ff.name] = patientInfo[ff.name] ? patientInfo[ff.name] : '';
          }else if(ff.name != 'phone'){
            that.regForm[ff.name] = patientInfo[ff.name] ? patientInfo[ff.name] : '';
          }
        } else if (ff.type == 'radio') {
          that.regForm[ff.name] = [
            {
              id: patientInfo[ff.name] + '',
              value: patientInfo[ff.name] + '',
              label: '其他',
              openStatus: 1
            }
          ];
        } else if (ff.type == 'file') {
          let arr = [];
          if (patientInfo[ff.name]) {
            arr = patientInfo[ff.name] ? patientInfo[ff.name].split(',') : [];
          }

          // if(defaultObject[businessConfig[i].codeId].multiple){
          //   ff.multiSelectCount = 9;
          // }




          let temp = [];
          // console.log(arr);
          for (let i = 0; i < arr.length; i++) {
            temp.push({
              dir: arr[i],
              url: that.file_ctx + arr[i]
            });
          }
          // console.log('arr', temp);
          that.regForm[ff.name] = temp;
        } else if (ff.type == 'date') {
          // console.log('jjjk');
          // console.log(patientInfo[businessConfig[i].codeId]);
          that.regForm[ff.name] = this.getTime(patientInfo[ff.name]);
        }
      }
    },
    returnFnDom(arr) {
      this.filelist = arr;
    },
    updateDate(obj) {
      // console.log(obj)
      this.regForm[obj.key] = obj.value.selectValue;
      // console.log(this.regForm)
      // let str = obj.key + ''
      this.$forceUpdate();
      // this.$set(this.regForm, str, 30)
      // this.$set(this,regForm,.selectValue)
      // // console.log('lll')

      // this.$set(this)
    },
    toggleStep() {
      if (this.step != this.steps.length) {
        if (this.step == 0) {
          this.step += 2;
        } else {
          this.step += 1;
        }
      } else {
        this.step -= 1;
      }

      let step = this.step - 1;
      this.updatestep = this.step;

      this.currentbusinessId = this.steps[step].id;
      this.flowId = this.steps[step].id;

      this.getDetail(this.currentbusinessId);
    },
    // 获取节点数
    getTemplateStep(id) {
      if (!id) {
        id = this.businessid;
      }
      this.$api.activity
        .casecollectflowconfigsteplist({
          mainId: id,
          activityType: this.activitytype
        }, { 'gb-part-tenant-id': this.tenantId })
        .then(ret => {
          this.initstep = true;
          // console.log('ret', ret);

          if (ret.data instanceof Object && ret.data.length != 0) {
            this.steps = ret.data;
            this.flowId = ret.data[0].id;
            this.currentbusinessId = ret.data[0].id;
            this.step = 0;

            this.getDetail(this.currentbusinessId);

            this.$nextTick(() => {
              this.updateinit += 1;
            });
          } else {
            this.steps = [];
          }
          // this.
        });
    },
    // 获取历史填写过的表单节点
    casecollectsubmititemlogbyflowid() {
      // flowId
      return new Promise((resolve, reject) => {
        this.$api.activity
          .casecollectsubmititemlogbyflowid({
            flowId: this.currentbusinessId,
            submitLogId: this.mainId,
            activityType: this.activitytype
          }, { 'gb-part-tenant-id': this.tenantId })
          .then(res => {
            if (res.data instanceof Object) {
              resolve(res.data.id);
            } else {
              resolve(res.data);
            }
          });
      });
    },

    // 获取历史填写数据
    getHistory() {
      // casecollectsubmitlogsubmit
      this.$api.activity
        .casecollectsubmitlogsubmit({
          activityId: this.businessid,
          type: this.ptype,
          activityType: this.activitytype
        }, { 'gb-part-tenant-id': this.tenantId })
        .then(ret => {
          console.log('100000000000', ret);

          if (ret.data instanceof Object) {
            this.mainId = ret.data.id;

            // 获取历史填写过的表单节点

            this.getTemplateStep();
          } else {
            this.mainId = null;
            this.getTemplateStep();
          }

          // this.steps = ret.data;
          // this.flowId = ret.data[0].id;
          // this.step = 0;

          // this.$nextTick(() => {
          //   this.updateinit += 1;
          // });
          // this.
        });
    },
    getTime(timer) {
      if (!timer) {
        return null;
      }
      let date = new Date(timer);

      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let days = date.getDate();

      return year + '-' + month + '-' + days;
    },
    async getDetail(id, reqstr = 'casecollectflowconfigqueryone', submititemlogid) {
      const that = this;
      this.tepId = null;
      uni.showLoading({
        title: '加载中'
      });
      let submitLogId;
      if (this.mainId) {
        submitLogId = await this.casecollectsubmititemlogbyflowid();
      }
      let reqparams = {
        id: id,
        activityType: this.activitytype,
        accountId: this.$common.getKeyVal('user', 'accountId', true)
      };
      if (submitLogId) {
        reqparams.submitItemId = submitLogId;
      }
      if (submititemlogid) {
        reqparams.submitItemId = submititemlogid;
      }
      if (this.mainId) {
        reqparams.submitLogId = this.mainId;
      }
      // if()
      await that.$api.activity[reqstr](reqparams, { 'gb-part-tenant-id': this.tenantId }).then(async res => {
        // console.log('ret', res);
        this.formTempObj.formTemplates = res.data.formTemplates
        // console.log(this.formTempObj,'this.formTempObj')
        that.$emit('getSubmitItemlog', res.data)
        if (res.data instanceof Object) {
          // res.data.pushTimeText = this.$common.formatDate(new Date(res.data.createTime), 'yyyy-MM-dd');
          // that.regForm = res.data;
          // that.$api.activity.casecollectsubmitlogqueryone({ id: id }).then(res => {
          if (res.data.caseFlowType == 2) {
            this.templateData = res.data;
            // this.businessConfig = [];

            this.regForm = {};
            this.config = [];
            //   that.openTemplate = true;
            //   // this.$forceUpdate()

            //   this.$nextTick(() => {
            //     // console.log('进来');
            //     // console.log(this.templateData);
            //     this.updatecount += 1;
            this.initi();
          } else if (res.data.caseFlowType == 1) {
            let attachId = null;
            let name = '';
            if (res.data.attachment instanceof Object) {
              attachId = res.data.attachment.dir;
              name = res.data.attachment.name;
            }
            // this.docsrc = this.file_ctx + res.data.attachId;

            this.templateData = [];
            this.config = [
              {
                attachId: attachId,
                name: name
              }
            ];

            let filelist = [];
            let arr = res.data.docTemplateValue.split(',');

            if (arr[0] != '') {
              for (let i = 0; i < arr.length; i++) {
                filelist.push({
                  dir: arr[i],
                  name: arr[i].split('.')[0],
                  suffix: arr[i].split('.')[1] || ''
                });
              }
            }

            this.filelist = filelist;

            // this.businessConfig = [];

            this.updateforminit = false;
            this.$nextTick(() => {
              this.updateforminit = true;
            });
          } else {
            const apires = await this.$api.activity.codefindByParentId({
              parentId: 28000,
              activityType: this.activitytype
            }, { 'gb-part-tenant-id': this.tenantId });

            // 获取快捷录入模板
            this.getQuickList();

            let templateid = res.data.id;

            for (let i = 0; i < apires.data.length; i++) {
              let temp = apires.data[i].codeName.split(link);
              let value = temp[valueidx];
              let label = temp[labelidx];
              this.businessConfig[value] = {
                label: label,
                require: false,
                templateId: templateid
              };
              // console.log('temp', temp);
            }
            let businessConfig = [];
            const that = this;
            if (res.data.businessConfig && res.data.businessConfig != '') {
              businessConfig = JSON.parse(res.data.businessConfig);
            }
            let patientInfo = res.data.patientInfo != '' ? res.data.patientInfo : {};
            this.regForm = {};
            this.config = [];
            console.log('patientInfo', patientInfo);

            if (patientInfo.id) {
              this.regForm.id = patientInfo.id;
            }
            for (let i = 0; i < businessConfig.length; i++) {
              if (!businessConfig[i].codeId) {
                businessConfig[i].codeId = businessConfig[i].key;
              }
              this.regForm[businessConfig[i].codeId] = null;
              if (this.businessConfig[businessConfig[i].codeId]) {
                this.businessConfig[businessConfig[i].codeId].type = defaultObject[businessConfig[i].codeId] ? defaultObject[businessConfig[i].codeId].type : 'input';
              }
              let lab = this.businessConfig[businessConfig[i].codeId] ? this.businessConfig[businessConfig[i].codeId].label : '';
              let templab = lab.split('-');

              // if(lab != '')
              if (templab.length == 2) {
                lab = templab[1];
              }
              let ff = {
                label: lab,
                name: businessConfig[i].codeId,
                required: businessConfig[i].require,
                templateId: businessConfig[i].codeId,
                type: defaultObject[businessConfig[i].codeId] ? defaultObject[businessConfig[i].codeId].type : 'input',
                belong: defaultObject[businessConfig[i].codeId] ? defaultObject[businessConfig[i].codeId].belong : 'base',
                inputStyle: defaultObject[businessConfig[i].codeId] ? defaultObject[businessConfig[i].codeId].inputStyle : '',
                nextRequest: true
                // type:businessConfig[i].type,
                // disabled: that.formItem.businessTemplate.filled,
                // openStatus: item.openStatus,
                // unitDesc: item.unitDesc
              };

              // // console.log(businessConfig[i])
              console.log('ff', ff);

              if (this.businessConfig[businessConfig[i].codeId]) {
                this.businessConfig[businessConfig[i].codeId].require = businessConfig[i].require;
                this.businessConfig[businessConfig[i].codeId].idx = i;
              }
              // defaultObject
              if (ff.type == 'input' || ff.type == "textarea") {
                // console.log('businessConfig[i].codeId',businessConfig[i].codeId)
                that.regForm[businessConfig[i].codeId] = patientInfo[businessConfig[i].codeId] ? patientInfo[businessConfig[i].codeId] : '';
                console.log('ff', that.regForm[businessConfig[i].codeId]);

                if (businessConfig[i].codeId == 'phone') {
                  console.log(that.regForm[businessConfig[i].codeId] && that.regForm[businessConfig[i].codeId] != '');
                  // ff.disabled = ;

                  if(that.regForm[businessConfig[i].codeId] && that.regForm[businessConfig[i].codeId] != ''){
                    ff.disabled = true;
                  }else{
                    ff.disabled = false;
                  }
                }
              } else if (ff.type == 'radio') {
                if (defaultObject[businessConfig[i].codeId]) {
                  ff['array'] = defaultObject[businessConfig[i].codeId] ? defaultObject[businessConfig[i].codeId].options : [];
                  // if (patientInfo[businessConfig[i].codeId] && patientInfo[businessConfig[i].codeId] instanceof Object) {
                  if (patientInfo[businessConfig[i].codeId]) {
                    that.regForm[businessConfig[i].codeId] = [
                      {
                        id: patientInfo[businessConfig[i].codeId] + '',
                        value: patientInfo[businessConfig[i].codeId] + '',
                        label: '其他',
                        openStatus: 1
                      }
                    ];
                  }
                } else {
                  ff['array'] = [];
                }
              } else if (ff.type == 'file') {
                let arr = [];
                if (patientInfo[businessConfig[i].codeId]) {
                  arr = patientInfo[businessConfig[i].codeId] ? patientInfo[businessConfig[i].codeId].split(',') : [];
                }

                if(defaultObject[businessConfig[i].codeId].multiple){
                  ff.multiSelectCount = 9;
                }




                let temp = [];
                // console.log(arr);
                for (let i = 0; i < arr.length; i++) {
                  temp.push({
                    dir: arr[i],
                    url: that.file_ctx + arr[i]
                  });
                }
                // console.log('arr', temp);
                that.regForm[businessConfig[i].codeId] = temp;
              } else if (ff.type == 'date') {
                console.log('jjjk');
                console.log(patientInfo[businessConfig[i].codeId]);
                that.regForm[businessConfig[i].codeId] = this.getTime(patientInfo[businessConfig[i].codeId]);
              }

              // console.log('this.cDisabled',this.cDisabled)
              if(this.cDisabled){
                ff.disabled = this.cDisabled
              }
              that.config.push(ff);
            }

            // this.templateData = [];
            // this.businessConfig = businessConfig;

            // console.log('this.config', this.config);

            // if(res.data.patientInfo instanceof Object){
            //   for(let key in res.data.patientInfo){
            //     if(this.businessConfig[businessConfig[i].codeId].type == 'radio'){
            //       this.regForm[key] = res.data.patientInfo[key]
            //     }else {
            //       this.regForm[key] = res.data.patientInfo[key]
            //     }
            //   }
            // }
            console.log(this.regForm);

            this.updateforminit = false;
            this.$nextTick(() => {
              this.updateforminit = true;
            });
          }
          this.submittype = res.data.caseFlowType;

          console.log('this.caseFlowType', this.submittype);
          console.log(this.config);

          //   });
          // });
        }
      });
      uni.hideLoading();
    },
    changeStep(step) {
      // console.log('obj', step);
      this.step = step + 1;
      // this.updatecount += 1;

      this.currentbusinessId = this.steps[step].id;
      this.flowId = this.steps[step].id;

      this.getDetail(this.currentbusinessId);

      // // console.log(this.id == '835483840192643077');
      // this.id = this.id == '835483840192643077' ? '834610603728510977' : '836217505293946886';
      // // console.log('this.regForm.id',id)
      // // console.log(this.regForm.id == '835483840192643077')
      // this.getDetail(this.id);
    },
    updateValue2(obj) {
      this.regForm[obj.onlykey2].child[obj.onlykey] = obj.value;
    },
    updateValue(obj = {}) {
      // // console.log("kval")
      // // console.log(obj)

      // if(obj.lvchild){

      //   if(that.regForm[obj.onlykey].child){

      //   }
      //   // console.log(obj.key)
      //   // console.log(that.regForm[obj.onlykey])
      // }else{

      // }
      this.regForm[obj.onlykey] = obj.value;
    },
    format1(val) {
      return val;
    },
    // format2(val) {      //   return `${val}%`      // }
    handleRangeChange(e) {
      this.rangeValue = e;
    },

    initi(formTemplates = []) {
      // console.log(this.templateData);
      const that = this;
      // debugger
      that.config = [];
      that.regForm = {};
      let formItem = that.templateData;
      // if (this.templateData.length > 0) {
      // for (let item1 of that.templateData) {
      //模板类型:1-文档模板，2-表单模板  3-患者登记
      // if (item1.businessTemplate.type === 2) {
      // if (that.templateData.businessTemplate.type === 2) {
      //   that.formItem = that.templateData;
      //   // break;
      // }
      // }
      // }
      console.log('formItem',formItem)
      // 空数据
      if(!formItem){
        // this.initstep = true
        this.updateforminit = false;
        this.$nextTick(() => {
          this.updateforminit = true;
        });
        return
      }
      if (!that.$validate.isNull(formItem.formTemplates)) {
        // console.log('kkk001');
        for (let item of formItem.formTemplates) {
          // debugger
          let ff = {
            // label: item.order + '.' + item.title,
            label: (that.isAccuratePromotion ? '' : (item.order + '.'))  + item.title,
            name: item.id,
            required: item.mandatoryStatus === 1,
            templateId: item.id,
            disabled:that.userDisabled,
            // disabled: that.formItem.businessTemplate.filled,
            openStatus: item.openStatus,
            unitDesc: item.unitDesc
          };
          // that.isSubmit = that.formItem.businessTemplate.filled;
          // debugger

          ff['type'] = '';
          // ID 记录 对应类型
          this.typeLogObject[item.id] = {
            type:item.type,
            title:ff.label,
            child:{}
          }
          // console.log(item.type);
          //   <!--类型：1-单选题(radio)，2-多选题(checkbox)，3-文本题，4-数值题(slider)，5-文件上传(file)，6-共用题干题（嵌套题）9 滑块选择-->
          //   <!--文本题类型：1-单行输入框(input)，2-日期，3-时间，4-日期时间，5-多行输入框(textarea)-->
          switch (item.type) {
            case 1:
              ff['type'] = 'radio';
              ff['array'] = that.getFormTemplateOptions(item);
              if (item.optionValue.length > 0 && !that.noEdit) {
                that.regForm[item.id] = [
                  {
                    id: item.optionIds,
                    value: item.optionValue,
                    label: '其他',
                    openStatus: 1
                  }
                ];
              }
              break;
            case 2:
              // debugger
              ff['type'] = 'checkbox';
              ff['array'] = that.getFormTemplateOptions(item);

              let optionIds = [];
              let optionValue = [];
              if (item.optionIds.length > 0 && !that.noEdit) {
                optionIds = item.optionIds.split(',');
                optionValue = item.optionValue.split(',');
              }

              // debugger
              let optionList = [];
              // debugger
              for (let i = 0; i < optionIds.length; i++) {
                const id = ['null', 'undefined'].includes(optionIds[i]) ? '' : optionIds[i];
                if (item.openStatus === 1 && !id) {
                  optionList.push({
                    id,
                    value: optionValue[i],
                    label: '其他',
                    openStatus: 1
                  });
                } else {
                  optionList.push({
                    id,
                    value: optionValue[i]
                  });
                }
              }
              // debugger
              that.regForm[item.id] = optionList;
              break;
            case 3:
              if (item.textType === 1) {
                ff['type'] = 'input';
              } else if (item.textType === 5) {
                ff['type'] = 'textarea';
              }
              if(!that.noEdit){
                that.regForm[item.id] = '';
                that.regForm[item.id] = item.optionValue;
              }

              break;
            case 4:
              ff['type'] = 'slider';
              ff['numberScope'] = item.numberScope.split('-');

              if(!that.noEdit){
                that.regForm[item.id] = '';
                that.regForm[item.id] = item.optionValue;
              }
              // that.regForm[item.id] = '';
              // that.regForm[item.id] = item.optionValue;
              break;
            case 5:
              ff['type'] = 'file';
              // if(item.op)
              // console.log('item.optionValue', item.optionValue);
              let arr = item.optionValue && !that.noEdit ? item.optionValue.split(',') : [];

              let temp = [];
              // console.log(arr);
              for (let i = 0; i < arr.length; i++) {
                temp.push({
                  dir: arr[i],
                  url: that.file_ctx + arr[i]
                });
              }
              // console.log('arr', temp);
              that.regForm[item.id] = temp;
              break;
            case 7:
              ff['type'] = 'phone';
              if(!that.noEdit){
                that.regForm[item.id] = item.optionValue;
              }

              break;
            case 8:
              ff['type'] = 'address';
              if(!that.noEdit){
                that.regForm[item.id] = { value: item.optionValue, extendValue: item.extendValue };
              }

              break;
            case 9:
              ff['type'] = 'lvSlider';
              ff['config'] = item.numberScope && item.numberScope != '' ? JSON.parse(item.numberScope) : {};
              if(!that.noEdit){
                this.regForm[item.id] = item.optionValue;
              }

              break;
            case 6:
              ff['type'] = 'lvQuestion';
              this.regForm[item.id] = {
                value: item.optionValue,
                child: {}
              };

              ff['config'] = this.initform(item.childFormTemplates, item.order, item.id);

              break;
            case 10:
              ff['type'] = 'rate'
              ff['config'] = item.numberScope && item.numberScope != '' ? JSON.parse(item.numberScope) : {};
              if(!that.noEdit){
               this.regForm[item.id] = item.optionValue;
               this.extendValueForm[item.id] = {
                 extendValue:item.extendValue,
               }
              }else{

              }

              break;
            case 14:
              // 微信昵称
              ff['type'] = 'pointFunctionWxNick';
              that.regForm[item.id] = ''
              that.regForm[item.id] = item.optionValue;
              break;
            case 15:
              // 性别
              ff['type'] = 'pointFunctionSex';
              that.regForm[item.id] = ''
              that.regForm[item.id] = item.optionValue;
              break;
            case 16:
              // 年龄
              ff['type'] = 'pointFunctionAge';
              that.regForm[item.id] = ''
              that.regForm[item.id] = item.optionValue;
              break;
            case 17:
              // 科室
              ff['type'] = 'pointFunctionDepartment';
              that.regForm[item.id] = ''
              that.regForm[item.id] = item.optionValue;
              break;
            case 18:
              // 省市
              if (!that.mainId && !that.$validate.isNull(that.provinceCity)) {
                setTimeout(() => {
                  that.config.forEach((e, eIndex) => {
                    if (e.name === item.id) {
                      that.$set(that.config, eIndex, {...that.config[eIndex], disabled: true})
                    }
                  })
                })
              }
              ff['type'] = 'pointFunctionProvinceCity';
              that.regForm[item.id] = ''
              that.regForm[item.id] = (!that.mainId && !that.$validate.isNull(that.provinceCity)) ? that.provinceCity.join('/') : item.optionValue;
              break;
            case 19:
              // 疾病
              ff['type'] = 'pointFunctionDiseaseIndex';
              that.regForm[item.id] = '';
              that.regForm[item.id] = item.optionValue
              break;
            case 20:
              // 姓名
              ff['type'] = 'pointFunctionName';
              that.regForm[item.id] = '';
              that.regForm[item.id] = item.optionValue;
              break;
            case 21:
              // 日期日期（YYYY-MM-DD）
              ff['type'] = 'date';
              that.regForm[item.id] = '';
              that.regForm[item.id] = item.optionValue;
              break;
            case 22:
              // 日期日期（YYYY-MM-DD）
              ff['type'] = 'dateAll';
              that.regForm[item.id] = '';
              that.regForm[item.id] = item.optionValue;
              break;
            case 23:
              // 疾病和科室的集合
              ff['type'] = 'pointFunctionDiseaseIndex';
              that.regForm[item.id] = '';
              that.regForm[item.id] = item.optionValue
              break;
          }
          that.config.push(ff);
        }
        console.log('=========');
        console.log(that.config);
        // console.log(that.regForm);

        this.updateforminit = false;
        this.$nextTick(() => {
          this.updateforminit = true;
        });
        // this.updateforminit += 1;
        // this.$forceUpdate()
      }
    },
    initform(arr = [], order, idd) {
      const regForm = {};
      const config = [];
      const that = this;
      for (let item of arr) {
        // debugger
        let ff = {
          label: order + '.' + (item.order + 1) + '.' + item.title,
          name: item.id,
          required: item.mandatoryStatus === 1,
          templateId: item.id,
          disabled:that.userDisabled,
          // disabled: that.formItem.businessTemplate.filled,
          openStatus: item.openStatus,
          unitDesc: item.unitDesc
        };
        // that.isSubmit = that.formItem.businessTemplate.filled
        // debugger

        ff['type'] = '';
        // ID 记录 对应类型
        if(this.typeLogObject[idd]){
          this.typeLogObject[idd].child[item.id] = {
            type:item.type,
            title:ff.label,
          }
        }
        // console.log(item.type);
        //   <!--类型：1-单选题(radio)，2-多选题(checkbox)，3-文本题，4-数值题(slider)，5-文件上传(file)，6-共用题干题（嵌套题）9 滑块选择-->
        //   <!--文本题类型：1-单行输入框(input)，2-日期，3-时间，4-日期时间，5-多行输入框(textarea)-->
        switch (item.type) {
          case 1:
            ff['type'] = 'radio';
            ff['array'] = that.getFormTemplateOptions(item);

            if (item.optionValue.length > 0 && !that.noEdit) {
              regForm[item.id] = [
                {
                  id: item.optionIds,
                  value: item.optionValue,
                  label: '其他',
                  openStatus: 1
                }
              ];
            }
            break;
          case 2:
            // debugger
            ff['type'] = 'checkbox';
            ff['array'] = that.getFormTemplateOptions(item);

            let optionIds = [];
            let optionValue = [];
            if (item.optionIds.length > 0 && !that.noEdit) {
              optionIds = item.optionIds.split(',');
              optionValue = item.optionValue.split(',');
            }

            // debugger
            let optionList = [];
            // debugger
            for (let i = 0; i < optionIds.length; i++) {
              const id = ['null', 'undefined'].includes(optionIds[i]) ? '' : optionIds[i];
              if (item.openStatus === 1 && !id) {
                optionList.push({
                  id,
                  value: optionValue[i],
                  label: '其他',
                  openStatus: 1
                });
              } else {
                optionList.push({
                  id,
                  value: optionValue[i]
                });
              }
            }
            // debugger
            regForm[item.id] = optionList;
            break;
          case 3:
            if (item.textType === 1) {
              ff['type'] = 'input';
            } else if (item.textType === 5) {
              ff['type'] = 'textarea';
            }
            if(!that.noEdit){
              regForm[item.id] = item.optionValue;
            }else{
              regForm[item.id] = '';
            }

            break;
          case 4:
            ff['type'] = 'slider';
            ff['numberScope'] = item.numberScope.split('-');
            if(!that.noEdit){
              regForm[item.id] = item.optionValue;
            }else{
              regForm[item.id] = '';
            }
            break;
          case 5:
            ff['type'] = 'file';
            let arr = item.optionValue && !that.noEdit ? item.optionValue.split(',') : [];

            let temp = [];
            // console.log(arr);
            for (let i = 0; i < arr.length; i++) {
              temp.push({
                dir: arr[i],
                url: that.file_ctx + arr[i]
              });
            }
            // console.log('arr', temp);
            regForm[item.id] = temp;
            that.regForm[idd].child[item.id] = temp;
            // regForm[item.id] = '模拟值'
            // that.regForm[idd].child[item.id] = '模拟值'
            break;
          case 7:
            ff['type'] = 'phone';
            if(!that.noEdit){
              regForm[item.id] = item.optionValue;
            }else{
              regForm[item.id] = null
            }
            break;
          case 8:
            ff['type'] = 'address';
            if(!that.noEdit){
              regForm[item.id] = { value: item.optionValue, extendValue: item.extendValue };
            }else{
              regForm[item.id] = null
            }
            break;
          case 9:
            ff['type'] = 'lvSlider';
            ff['config'] = item.numberScope && item.numberScope != '' ? JSON.parse(item.numberScope) : {};
            if(!that.noEdit){
              regForm[item.id] = item.optionValue;
            }else{
              regForm[item.id] = null
            }
            break;
          case 10:
            ff['type'] = 'rate'
            ff['config'] = item.numberScope && item.numberScope != '' ? JSON.parse(item.numberScope) : {};
            if(!that.noEdit){
              regForm[item.id] = item.optionValue;
              that.extendValueForm[item.id] = {
                extendValue:item.extendValue,
              }
            }else{
              regForm[item.id] = null
            }

            break;
          case 20:
            // 姓名
            ff['type'] = 'pointFunctionName';
            regForm[item.id] = '';
            regForm[item.id] = item.optionValue;
            break;
          case 21:
            // 日期日期（YYYY-MM-DD）
            ff['type'] = 'date';
            regForm[item.id] = '';
            regForm[item.id] = item.optionValue;
            break;
          case 22:
            // 日期日期（YYYY-MM-DD）
            ff['type'] = 'dateAll';
            regForm[item.id] = '';
            regForm[item.id] = item.optionValue;
            break;
          case 23:
            // 疾病和科室的集合
            ff['type'] = 'pointFunctionDiseaseIndex';
            regForm[item.id] = '';
            regForm[item.id] = item.optionValue
            break;
        }
        config.push(ff);
      }

      this.regForm[idd].child = regForm;
      return {
        regForm,
        config
      };
    },
    /**
     * 解析选项数组
     */
    getFormTemplateOptions(item) {
      const { formTemplateOptions, openStatus } = item;
      let array = [];
      if (formTemplateOptions.length > 0) {
        for (let itemOption of formTemplateOptions) {
          let o = {
            id: itemOption.id,
            value: itemOption.value
          };
          array.push(o);
        }

        // 预览时 选项其他在模板选项中 编辑时不存在模板选项中
        if (openStatus === 1) {
          array.push({
            id: '',
            label: '其他',
            value: '',
            openStatus: 1
          });
        }
      }
      return array;
    },
    imgReturnFn(obj) {
      // console.log(obj);
      if (obj.child) {
        // this.regForm[obj.name] = obj.arr;
        if (this.regForm[obj.onlykey].child) {
          this.regForm[obj.onlykey].child[obj.name] = obj.arr;
        }
      } else {
        this.regForm[obj.name] = obj.arr;
      }
      // console.log(this.regForm);
      // this.regForm.attachmentList = v;
    },
    updateForm(obj) {
      const that = this;
      // console.log(obj);
      // 问卷精准地推类型昵称获取
      if(obj.userName){
        this.userName = obj.userName
      }
      if (obj.lvchild) {
        obj.key = obj.key.trim();
        if (that.regForm[obj.onlykey].child) {
          that.regForm[obj.onlykey].child[obj.key] = obj.value;
          that.extendValueForm[obj.key] = obj
        }
        // console.log(obj.key);
        // console.log(that.regForm[obj.onlykey]);
      } else {
        obj.key = obj.key.trim();
        that.regForm[obj.key] = obj.value;
        that.extendValueForm[obj.key] = obj
      }
    },
    // 验证公干题
    validPublic(that, order, param, onlykey) {
      // console.log(this.regForm);
      // console.log(this.regForm[onlykey].child);

      const tipArr = {};
      for (let j = 0; j < that.config.length; j++) {
        let item = that.config[j];

        if (item.required) {
          tipArr[item.name] = '请填写序号为' + (order + 1) + '.' + (j + 1) + '的题目';
        }
      }
      // console.log(that.config);
      // console.log(that.regForm);
      if (!this.$common.validationForm(tipArr, this.regForm[onlykey].child)) {
        return 'novalid';
      }

      // console.log(this.regForm[onlykey])

      for (let i = 0; i < that.config.length; i++) {
        // debugger
        let item = that.config[i];
        // const rf = that.regForm[item.name]
        const rf = this.regForm[onlykey].child[item.name];
        if (this.$validate.judgeTypeOf(rf) === 'Array') {
          if (that.config[i].type == 'file') {
            let val = '';
            for (let i = 0; i < rf.length; i++) {
              let r = rf[i];
              if (val == '') {
                val = rf[i].dir;
              } else {
                val += ',' + rf[i].dir;
              }
            }
            let fv = {
              templateId: item.templateId,
              templateOptionId: '',
              value: val
              // extendValue: r.extendValue ? JSON.stringify(r.extendValue) : ''
            };
            param.formWriteValueDTOS.push(fv);

            continue;
          }
          for (let i = 0; i < rf.length; i++) {
            let r = rf[i];
            let fv = {
              templateId: item.templateId,
              templateOptionId: r.id,
              value: r.value,
              extendValue: r.extendValue ? JSON.stringify(r.extendValue) : ''
            };
            param.formWriteValueDTOS.push(fv);
          }
        } else {
          // 非
          let fv = {
            templateId: item.templateId,
            templateOptionId: '',
            value: rf
          };
          if(item.type === 'rate'){
            fv.extendValue = that.extendValueForm[item.name] && that.extendValueForm[item.name] instanceof Object ? that.extendValueForm[item.name].extendValue : ''
          }


          param.formWriteValueDTOS.push(fv);
        }
        // // console.log(param.formWriteValueDTOS)

        // // console.log("that.regForm[item.name]",that.regForm[item.name])
      }

      // console.log(param);
      return param;
    },
    // query 验证通过  用于验证不通过返回对应索引
    validationForm(tipArr, data) {
      // console.log(data);
      for (const k in tipArr) {
        if (k && tipArr.hasOwnProperty(k)) {
          let arr = [];
          let key;
          let val;
          let type;
          if (k.indexOf('.') > -1) {
            // 为这类设置 tipArr['noticeSign.endTime'] = '请选择截止时间！'
            arr = k.split('.');
            key = arr[0];
            val = data[arr[0]][arr[1]];
          } else {
            arr = k.split('-');
            key = arr[0];
            type = arr[1];
            val = data[key];
          }
          if (typeof val === 'string') {
            val = val.replace(/\s+/g, '');
          }
          if (typeof val === 'undefined' || val === null || val === '' || val.length <= 0) {
            // 为空
            // debugger
            // let text = '请输入'
            // if (tipArr[k].indexOf('请') > -1) text = ''
            // uniPlugin.toast(text + tipArr[k])
            this.$uniPlugin.toast(tipArr[k].msg);
            return tipArr[k].idx;
          } else {
            // 不为空
            if (type) {
              tipArr[k].msg = tipArr[k].msg.replace('请输入', '');
              tipArr[k].msg = tipArr[k].msg.replace('请选择', '');
              if (type === 'phone') {
                // 手机验证
                const reg = /^1[0-9]{10}$/;
                if (!reg.test(val)) {
                  this.$uniPlugin.toast(tipArr[k].msg + '有误');
                  return tipArr[k].idx;
                }
              } else if (type === 'mail') {
                // 邮箱验证
                const reg = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/;
                if (!reg.test(val)) {
                  this.$uniPlugin.toast(tipArr[k].msg + '有误');
                  return tipArr[k].idx;
                }
              } else if (type === 'IDCard') {
                // 身份证验证
                const obj = this.$validate.isCardid(val);
                if (!obj.status) {
                  this.$uniPlugin.toast(obj.text);
                  // return obj.status
                  return tipArr[k].idx;
                }
              }
            }
          }
        }
      }
      return 'query';
    },
    scrollto(idx) {
      this.scrollintotargetview = 'target' + idx;
      this.debounce(function() {
        this.scrollintotargetview = '';
      });
    },
    debounce(fn, delay = 500) {
      if (this.timer) {
        clearTimeout(this.timer);
      }
      this.timer = setTimeout(() => {
        fn.apply(this, arguments);
        this.timer = null;
      }, delay);
    },
    // 回访提交
    visitsubmit() {
      // console.log('回访提交');
    },
    // 提交前校验
    submitValidType(){
      let is = true;
      let regForm = this.regForm;
      for(let ky in this.typeLogObject){
        if(this.typeLogObject[ky].type === 20){
          // 姓名组件
          let count = this.countChineseCharacters(regForm[ky]);
          let name = this.typeLogObject[ky].title;
          if(count > this.maxChineseCount){
            this.$uniPlugin.toast('题目'+ name + '汉字个数不能超过'+this.maxChineseCount);
            is = false;
          }
        }else if(this.typeLogObject[ky].type === 6) {
          // 公干题目
          let childObject = this.typeLogObject[ky].child;
          if(childObject instanceof Object) {
            for(let kk in childObject){
              if(childObject[kk].type === 20) {
                // 姓名组件
                // console.log('regForm[ky]',regForm[ky])
                let val = regForm[ky].child[kk]
                let count = this.countChineseCharacters(val);
                let name = childObject[kk].title;
                if(count > this.maxChineseCount){
                  this.$uniPlugin.toast('题目'+ name + '汉字个数不能超过'+this.maxChineseCount);
                  is = false;
                }
              }
            }
          }
        }
      }
      return is;
    },
    submit() {
      // 回访保存提交
      // if(this.ptype == 2){
      //   this.visitsubmit();
      //   return
      // }

      if (this.loading) {
        return;
      }
      console.log('this.submittype',this.submittype)
      if (this.submittype == 3) {
        // 针对输入框和文本框
        // for(let key in this.regForm){
        //   if()
        // }
        // console.log(this.regForm);

        var param = {
          activityId: this.businessid,
          // "formWriteValueDTOS": [
          //   {
          //     "templateId": 0,
          //     "templateOptionId": 0,
          //     "value": ""
          //   }
          // ],
          flowId: this.flowId,
          mainId: this.mainId //
        };
        let tipArr = {};
        // console.log('this.businessConfig',this.businessConfig)
        for (let key in this.businessConfig) {
          if (this.businessConfig[key].require) {
            tipArr[key] = {
              // msg: '请填写序号为' + (this.businessConfig[key].idx + 1) + '的题目',
              msg: '请填写'+this.businessConfig[key].label+'',
              idx: this.businessConfig[key].idx
            };
          }
        }

        let result = this.validationForm(tipArr, this.regForm);

        if (result != 'query') {
          this.scrollto(result);
          return;
        }
        let regForm = {};

        // console.log(this.businessConfig)
        for (let key in this.regForm) {
          const rf = this.regForm[key];
          // console.log(rf)
          // console.log(this.businessConfig[key])
          if (this.$validate.judgeTypeOf(rf) === 'Array') {
            if (this.businessConfig[key].type == 'file') {
              let val = '';
              for (let i = 0; i < rf.length; i++) {
                let r = rf[i];
                if (val == '') {
                  val = rf[i].dir;
                } else {
                  val += ',' + rf[i].dir;
                }
              }
              // let fv = {
              //   // templateId: item.templateId,
              //   templateOptionId: '',
              //   value: val
              //   // extendValue: r.extendValue ? JSON.stringify(r.extendValue) : ''
              // };
              regForm[key] = val;
              // param.formWriteValueDTOS.push(fv);

              continue;
            }
            for (let i = 0; i < rf.length; i++) {
              let r = rf[i];
              // let fv = {
              //   // templateId: item.templateId,
              //   templateId: this.businessConfig[key].templateId,
              //   templateOptionId: r.id,
              //   value: r.value,
              //   extendValue: r.extendValue ? JSON.stringify(r.extendValue) : ''
              // };
              regForm[key] = r.value;
              // param.formWriteValueDTOS.push(fv);
            }
          } else {
            // 非
            // let fv = {
            //   templateId: this.businessConfig[key].templateId,
            //   templateOptionId: '',
            //   value: rf
            // };
            regForm[key] = rf;
            // param.formWriteValueDTOS.push(fv);
          }
        }

        if (this.ptype == 2) {
          param.flowId = this.stepid;
          param.bindSubmitLogId = this.visitid;
          if (this.parentbusinessid && this.parentbusinessid != '') {
            param.mainId = this.parentbusinessid;
          }
        }
        param.patientInfo = regForm;

        // console.log(param)
        param.caseCollectSubmitLogType = 1;

        this.loading = true;
        this.$emit('returnFn', {
          param: param,
          next: this.step == this.steps.length || this.steps.length == 1
        });

        // param.formWriteValueDTOS = [];
      } else if (this.submittype == 2) {
        const that = this;
        const tipArr = {};
        for (let j = 0; j < that.config.length; j++) {
          let item = that.config[j];
          if (item.required) {
            tipArr[item.name] = {
              msg: '请填写序号为' + (j + 1) + '的题目',
              idx: j
            };
          }
        }
        // debugger
        // 表单验证
        // debugger
        let result = this.validationForm(tipArr, that.regForm);

        // console.log('444',tipArr,result,that.regForm)


        // return
        if (result != 'query') {
          this.scrollto(result);
          return;
        }

        // 特定校验，指定类型组件校验
        let yyIs = this.submitValidType();
        if(!yyIs){
          return;
        }

        var param = {
          activityId: that.businessid,
          // "formWriteValueDTOS": [
          //   {
          //     "templateId": 0,
          //     "templateOptionId": 0,
          //     "value": ""
          //   }
          // ],
          flowId: this.flowId,
          mainId: this.mainId //
        };
        console.log("param=====",param)
        if(this.collectionType === 4 && !param.mainId) {
          param.auditStatus = 1;
        }
        param.formWriteValueDTOS = [];
        // console.log(that.regForm);

        // return;
        // debugger
        for (let i = 0; i < that.config.length; i++) {
          // debugger
          let item = that.config[i];
          // console.log('item',item)
          // console.log(that.extendValueForm[item.name])

          if (item.type == 'lvQuestion') {
            // let fv = {
            //   templateId: item.templateId,
            //   templateOptionId: '',
            //   value: '我是集合题',
            // }
            // param.formWriteValueDTOS.push(fv)
            // item.config.ruleForm =
            param = this.validPublic(item.config, i, param, item.name);
            if (param == 'novalid') {
              this.scrollto(i);
              return;
            }

            continue;
          }
          const rf = that.regForm[item.name];
          if (this.$validate.judgeTypeOf(rf) === 'Array') {
            if (that.config[i].type == 'file') {
              let val = '';
              for (let i = 0; i < rf.length; i++) {
                let r = rf[i];
                if (val == '') {
                  val = rf[i].dir;
                } else {
                  val += ',' + rf[i].dir;
                }
              }
              let fv = {
                templateId: item.templateId,
                templateOptionId: '',
                value: val
                // extendValue: r.extendValue ? JSON.stringify(r.extendValue) : ''
              };
              param.formWriteValueDTOS.push(fv);

              continue;
            }
            for (let i = 0; i < rf.length; i++) {
              let r = rf[i];
              let fv = {
                templateId: item.templateId,
                templateOptionId: r.id,
                value: r.value,
                extendValue: r.extendValue ? JSON.stringify(r.extendValue) : ''
              };
              param.formWriteValueDTOS.push(fv);
            }
          } else {
            // 非
            let fv = {
              templateId: item.templateId,
              templateOptionId: '',
              value: rf,
            };
            if(item.type === 'rate'){
              fv.extendValue = that.extendValueForm[item.name] && that.extendValueForm[item.name] instanceof Object ? that.extendValueForm[item.name].extendValue : ''
            }
            param.formWriteValueDTOS.push(fv);
          }
          // console.log(param.formWriteValueDTOS);

          // // console.log("that.regForm[item.name]",that.regForm[item.name])
        }
        // ，Value ="提交类型1病例征集2回访"，

        if (this.ptype == 2) {
          param.flowId = this.stepid;
          param.bindSubmitLogId = this.visitid;
          if (this.parentbusinessid && this.parentbusinessid != '') {
            param.mainId = this.parentbusinessid;
          }
        }
        param.caseCollectSubmitLogType = this.ptype;

        // return

        // 病例登记
        // if (this.submittype == 3) {
        //   param.patientInfo = {};

        //   param.formWriteValueDTOS = [];
        // }
        // console.log(param);
        // return
        // return
        // param.nextstep =
        this.loading = true;
        that.$emit('returnFn', {
          param: param,
          templateId:this.templateId,
          additionalForm:this.additionalForm,
          userName:this.userName,
          // extendValueForm:this.extendValueForm,
          next: this.step == this.steps.length || this.steps.length == 1
        });
        // console.log(param);
      } else if (this.submittype == 1) {
        // console.log('this.filelist', this.filelist);

        if (this.filelist.length == 0) {
          that.$uniPlugin.toast('请选择上传文件');
          return;
        }
        let filestr = '';
        for (let i = 0; i < this.filelist.length; i++) {
          if (filestr == '') {
            filestr += this.filelist[i].dir;
          } else {
            filestr += ',' + this.filelist[i].dir;
          }
        }

        var param = {
          activityId: this.businessid,
          caseCollectSubmitLogType: this.ptype,

          // "formWriteValueDTOS": [
          //   {
          //     "templateId": 0,
          //     "templateOptionId": 0,
          //     "value": ""
          //   }
          // ],
          docTemplateValue: filestr,
          flowId: this.flowId,
          mainId: this.mainId //
        };

        if (this.ptype == 2) {
          param.flowId = this.stepid;
          param.bindSubmitLogId = this.visitid;
          if (this.parentbusinessid && this.parentbusinessid != '') {
            param.mainId = this.parentbusinessid;
          }
        }

        this.loading = true;

        this.$emit('returnFn', {
          param: param,
          next: this.step == this.steps.length || this.steps.length == 1
        });

        // console.log('filestr', filestr);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.content-t {
  height: 100upx;
  display: flex;
  align-items: center;
  background-color: #fff;
  border-bottom: 1upx solid $borderColor;
  // margin: 0 20upx;
  padding: 0 20upx;
  .content-title {
    font-size: 32upx;
    font-weight: 550;
    padding-left: 20upx;
    border-left: 5upx solid $topicC;
    line-height: 1.5;
    // height: 80%;
  }
  .content-tip {
    font-size: 24upx;
    margin-left: 10upx;
  }
  .red {
    color: red;
  }
}
.o-space {
  height: 100px;
  width: 100%;
}
.querybtn {
  background-color: $topicC;
  flex: 2;
  margin-left: 20upx;
  margin-right:20upx;
  height: 80upx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  border-radius: 10upx;
}

.nextbtn {
  border: 1upx solid $borderColor;
  background-color: #fafafa;
  flex: 1;
  margin-left: 20upx;
  margin-right: 20upx;
  height: 80upx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10upx;
}
.m-tb-20-auto {
  margin: 40upx 0 70upx 0;
}

.template-content {
  padding: 20upx;
  min-height: 200upx;

  // height: 100%;
}
.b-btn-color {
  background: #f56c6c;
}

.template-footer {
  display: flex;
  justify-content: center;
  // padding-bottom: 100rpx;
}

.disabled.btn-bg {
  opacity: 0.5;

  // background-color: ;
}
.disabled.querybtn {
  opacity: 0.5;
}
/deep/.uni-popup__wrapper.uni-custom.bottom .uni-popup__wrapper-box{
  max-height: none !important;
  overflow-y: none !important;
}
</style>
