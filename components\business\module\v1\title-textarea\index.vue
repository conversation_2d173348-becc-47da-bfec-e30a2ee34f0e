
<template>
  <view class="title-textarea clear-float" @click="toFocus()" style="border-bottom: none;border-top: none;" :class="{'bdt':defaultConfig.bdt,'bdb':defaultConfig.bdb}" :style="defaultConfig.style">
    <view class="l-l" :style="{'color': defaultConfig.titleColor}" v-if="defaultConfig.showLabel">
      <text class="star" v-if="defaultConfig.required && !defaultConfig.iconurl">*</text>
      <image  :src='defaultConfig.iconurl' class="zxicon" mode="" v-if="defaultConfig.iconurl"></image>
      {{defaultConfig.label}}
    </view>
    <view class="l-r">
      <textarea :placeholder="placeholder ? placeholder : `请输入${defaultConfig.label}`" :disabled="disabled" :placeholderStyle="placeholderStyle"
        v-model="form.data.val" @blur="returnFn" :focus="autofocus" @focus="handleFocus"  auto-height :style="defaultConfig.inputStyle" :maxlength="defaultConfig.maxlength"
      />
    </view>
  </view>
</template>

<script>

export default {
  data() {
    return {
      autofocus:false,
      form: {
        data: {
          val: ''
        }
      },
      file_ctx:this.file_ctx,
      array: [],
      index: 0,
      defaultConfig: {
        style: {},
        bdt: true,
        bdb: true,
        titleColor: '#333',
        textColor: '#333',
        label: '多行输入框',
        name: 'input',
        required: false,
        showLabel: true,
        inputStyle: {},
        maxlength: 140
      },
      keyboardHeight:0,
    }
  },
  watch: {
    cData: {
      handler(val) {
        this.watchDataMain(val)
      },
      deep: true
    },
    config: {
      handler(val) {
        this.copyConfig()
      },
      deep: true
    },
    value: {
      handler () {
        this.form.data.val = this.value
      },
      immediate: true
    },
    'form.data.val': {
      handler () {
        this.$emit('input', this.form.data.val)
      },
      deep: true
    }
  },
  props: {
    value: [String,Number],
    placeholder: String,
    placeholderStyle:{
      type:String,
      defautl:()=>''
    },
    // 是否禁用disable
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 初始值传值，用于回显
    cData: {
      type: [String, Number],
      required: false,
      default() {
        return ''
      }
    },
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    },
    showConfirmBar:{
      type:Boolean,
      default:true
    },
    list:{
      type:Array,
      default:[]
    },
    formObj:{
      type:Object,
      default:{}
    },
    path:{
      type:String,
      default:'PostMessage'
    }
  },
  computed: {

  },
  mounted() {
      this.copyConfig()
      if (!this.$validate.isNull(this.cData)) {
        this.watchDataMain(this.cData)
      }
  },
  methods: {
    // 隐藏键盘
    hideKeyboard() {
      this.keyboardHeight = 0
      uni.hideKeyboard();
    },
    handleAddGambit(){
      this.$navto.push('postGambitIndex',{path:this.path,list:this.list,form:this.formObj})
    },
    // 监听输入框聚焦
    handleFocus(e) {
      // 获取键盘高度（微信小程序通过 e.detail.height 获取）
      this.keyboardHeight = e.detail.height || 300; // H5需模拟高度
    },
    toFocus(){
      this.autofocus = true;
    },
    /**
       * 初始化拷贝config对象
       */
    copyConfig() {
      const that = this
      const obj = that.config
      console.log('obj',obj)
      Object.keys(obj).forEach(function(key) {
        that.defaultConfig[key] = obj[key]
      })
    },
    /**
       * 监听Data主逻辑方法
       */
    watchDataMain(val) {
      this.form.data.val = val
    },
    /**
       * picker触发选中事件
       * @param e
       */
    returnFn(e) {
      const that = this
      this.keyboardHeight = 0
      if (that.disabled) return
      that.$emit('updateForm', { key: '' + that.config.name, value: that.form.data.val })
    }
  }
}
</script>

<style lang="scss" scoped>
  .zxicon{
    width: 44upx;
    height: 39upx;
        vertical-align: middle;
        margin-left: 6upx;
        margin-right: 12upx;
  }
  .title-textarea{
    background-color:#ffffff;
    .l-l{
      line-height: 88upx;
      color: #333333;

      font-weight: 500;
      font-size:30upx;
    }
    .l-r{
      margin-bottom: 5px;
      textarea {
        min-height: 162upx;
        width: 665upx;
        padding: 18upx 17upx;
        box-sizing: border-box;
        color:#1D2029;
        font-size: 28upx;
        text-align: left;
        display: inline-block;
        vertical-align: middle;
        background: #fafafa;
        border-radius: 0;
      }
    }
  }
  .star{
    color: #F85E4C;
    padding-top: 6upx;
    font-size: 32upx;
    display: inline-block;
  }
</style>
