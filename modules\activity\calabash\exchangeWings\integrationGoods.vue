<template>
  <view class="pages">
    <!-- 返回按钮 -->
    <image class="backBtn" @click="back" :style="{top:capsuleTop}" :src="backBtn" mode="aspectFill"></image>
    <!-- 顶部轮播图 -->
    <view class="swiperBox">
      <swiper class="swiper" @change="e=>current = e.detail.current" circular v-model="current" :autoplay="true">
      	<swiper-item v-for="(item,index) in pageData.productCarouselImageList" :key="index">
          <image class="swiperImage" :src="file_ctx + item" mode="aspectFill"></image>
      	</swiper-item>
      </swiper>
      <view class="swiperPoint">{{current + 1}}/{{pageData.productCarouselImageList.length}}</view>
    </view>
    <!-- 商品内容 -->
    <view class="goodsContent">
      <view class="">
        <text class="goodsNums">{{pageData.needPoint}}</text><text class="goodsSings">福币</text>
      </view>
      <view class="display-flex">
        <view class="goodsTab">限兑{{pageData.exchangeNum}}次</view><view class="goodsTab">兑后不换</view>
      </view>
      <view class="goodsName">{{pageData.productName}}</view>
      <view class="productDesc">{{pageData.productDesc}}</view>
    </view>
    <!-- 商品内容分类tab -->
    <view class="ClassCommodityContent">
      <view class="CommodityContent" :class="{select:currentTab === 1}" @click="currentTab = 1">
        好礼详情
        <image class="borderBottom" v-if="currentTab === 1" :src="borderBottom" mode="aspectFill"></image>
      </view>
      <view class="CommodityContent" :class="{select:currentTab === 2}" @click="currentTab = 2">
        权益说明
        <image class="borderBottom" v-if="currentTab === 2" :src="borderBottom" mode="aspectFill"></image>
      </view>
    </view>
    <!-- 内容分类内容 -->
    <view class="ClassContent">
      <view class="" v-if="currentTab === 1" v-html="pageData.productDetail" @click ="itemclick(pageData.productDetail)"></view>
      <view class="" v-else v-html="pageData.rightDesc" @click ="itemclick(pageData.rightDesc)"></view>
    </view>
    <!-- 底部tab -->
    <!-- 占位元素 -->
    <view class="bottomGap"></view>
    <view class="bottomTab">
      <view class="tabGap" v-if="initGoldNum >= pageData.needPoint"></view>
      <view class="tabGapLack" @click="gotoLuckyCoinaTask" v-else>
        <image class="luckyoinIcon" :src="luckyoinIcon2" mode="aspectFill"></image>
        <view class="">福币不足无法兑换，立即前往攒福币</view>
        <image class="rightRed" :src="rightRed" mode="aspectFill"></image>
      </view>
      <view class="bottomContent">
        <view class="bottomL">
          <view class="goodsNumsMin">{{pageData.needPoint}}<text class="goodsSings">福币</text></view>
          <view class="currentGoodsNums">当前福币：{{initGoldNum}}</view>
        </view>
        <view class="bottomR" @click='openConfirm' v-if="initGoldNum >= pageData.needPoint">去兑换</view>
        <view class="bottomR bottomRGray" @click='deficiency' v-else>福币不足</view>
      </view>
    </view>
    <confirmExchange :pageData='pageData' :openFlag='openFlag' @change='e=>openFlag = e'></confirmExchange>
  </view>
</template>

<script>
  import calabashApis from "@/modules/common/api/calabash.js"
  import { mapState } from "vuex";
  import confirmExchange from './components/confirmExchange'
  export default{
    components: {
        confirmExchange
    },
    onShareAppMessage(res) {
      return {
        path: `modules/activity/calabash/exchangeWings/integrationGoods?id=${this.queryOptions.id}`
      }
    },
    data(){
      return {
        backBtn: this.$static_ctx + "image/business/hulu-v2/backBtn.png",
        borderBottom: this.$static_ctx + "image/business/hulu-v2/border-bottom.png",
        luckyoinIcon2: this.$static_ctx + "image/business/hulu-v2/luckyoinIcon2.png",
        rightRed: this.$static_ctx + "image/business/hulu-v2/right-red.png",
        capsuleTop:0,
        file_ctx: this.file_ctx,
        pageData:{productCarouselImageList:[]},
        current:0,
        currentTab:1,
        initGoldNum:0,
        openFlag:false,
        monthMum:0,//每月限兑数量
      }
    },
    computed: {
      ...mapState("user", {
        accountId: (state) => state.accountId,
        fansRecord: (state) => state.fansRecord,
      }),
    },
    destroyed() {
      console.log('页面销毁');
      uni.removeStorageSync('currentAddressId')
    },
    onLoad(res){
      this.queryOptions = res;
      this.loadData()
      this.pointgiftexchangeQueryDefault()
    },
    methods:{
      itemclick(item){
        // 判断含有图片
        if (item.indexOf("src") >= 0) {
          const imgs = [];
          item.replace(/]*src=['"]([^'"]+)[^>]*>/gi, function (match, capture) {
            imgs.push(capture);
          })
          if(imgs.length){
            uni.previewImage({
              current: imgs[0], // 当前显示图片的http链接
              urls: imgs
            })
          }
        }
      },
      async loadData(res){
        let {data} = await calabashApis.pointgiftQueryOne(this.queryOptions)
        data.productCarouselImageList = data.productCarouselImageList
        this.pageData = data;
        // 获取初始化金币数量
        let {data:{totalPoint}} = await calabashApis.pointuserQueryUser({accountId:this.accountId})
        this.initGoldNum = totalPoint
        let ClientRect = uni.getMenuButtonBoundingClientRect()
        this.capsuleTop = ClientRect.top +　'px'
      },
      gotoLuckyCoinaTask(){
        uni.navigateTo({
          url:'modules/activity/calabash/luckyCoinaTask'
        })
      },
      back(){
        try{
          uni.navigateBack()
        }catch(e){
          uni.switchTab({url:'/pages/index/index'})
        }
      },
      async pointgiftexchangeQueryDefault(){
        const res = await calabashApis.pointgiftexchangeQueryDefault({})
        if(res.data !==""){
          this.monthMum = res.data.num
        }
      },
      async openConfirm(){
        const res = await calabashApis.pointgiftexchangeQueryMonthNum({accountId:this.accountId})
        if(res.data){
          if(res.data >= this.monthMum)return uni.showToast({icon:'none',title:'本月兑奖次数已达上限，请下个月再兑换'})
          this.openFlag = true
        } else {
          this.openFlag = true
        }
      },
      deficiency(){
        uni.showToast({icon:'none',title:'福币不足'})
      }
    }
  }
</script>

<style lang="scss">
  .pages{
    width: 100vw;
    height: 100vh;
    background: #F4F6FA;
    overflow: scroll;
  }
  .display-flex{
    display: flex;
  }
  .backBtn{
    width: 64rpx;
    height: 64rpx;
    position: absolute;
    left: 24rpx;
    z-index: 999;
  }
  .swiperBox{
    width: 750rpx;
    height: 750rpx;
    position: relative;
    .swiper{
      width: 750rpx;
      height: 750rpx;
      .swiperImage{
        width: 750rpx;
        height: 750rpx;
      }
    }
    .swiperPoint{
      position: absolute;
      bottom: 24rpx;
      right: 24rpx;
      width: 78rpx;
      height: 38rpx;
      line-height: 38rpx;
      text-align: center;
      color:white;
      background: rgba(0,0,0,0.4);
      border-radius: 20rpx;
    }
  }
  .goodsSings{
    font-weight: 500;
    font-size: 24rpx;
    color: #FF4100;
    margin-left: 4rpx;
  }
  .goodsContent{
    width: 100vw;

    padding: 24rpx 32rpx 32rpx 32rpx;
    box-sizing: border-box;
    background-color: white;
    .goodsNums{
      font-weight: 500;
      font-size: 56rpx;
      color: #FF5500;
    }
    .goodsTab{
      width: 104rpx;
      height: 40rpx;
      background: #DEF2ED;
      border-radius: 8rpx;
      margin-right: 16rpx;
      font-weight: 400;
      font-size: 20rpx;
      color: #00664B;
      text-align: center;
      line-height: 40rpx;
    }
    .goodsName{
      font-weight: 500;
      font-size: 30rpx;
      color: #1D2029;
      margin-top: 30rpx;

    }
    .productDesc{
      font-weight: 400;
      font-size: 24rpx;
      color: #4E5569;
      margin-top: 12rpx;
      word-wrap: break-word;
      margin-bottom: 8rpx;
    }
  }
  .ClassCommodityContent{
    width: 750rpx;
    height: 88rpx;
    background: #FFFFFF;
    margin-top: 20rpx;
    display: flex;
    .CommodityContent{
      width: 50%;
      height: 88rpx;
      line-height: 88rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #4E5569;
      text-align: center;
      position: relative;
      .borderBottom{
        width: 38rpx;
        height: 10rpx;
        position: absolute;
        left: 50%;
        bottom: 12rpx;
        transform: translateX(-50%);
      }
    }
    .select{
      font-weight: 500;
      font-size: 28rpx;
      color: #00B484;
    }
  }
  .ClassContent{
    width: 100vw;
    min-height: 504rpx;
    box-sizing: border-box;
    padding: 24rpx 32rpx 32rpx 32rpx;
  }
  .bottomGap{
    width: 100vw;
    height: 236rpx;
  }
  .bottomTab{
    width: 100vw;
    height: 236rpx;
    position: fixed;
    bottom: 30rpx;
    background: white;
    .tabGap{
      width: 100vw;
      height: 88rpx;
    }
    .tabGapLack{
      width: 750rpx;
      height: 64rpx;
      background: #FFECE2;
      padding: 0 24rpx 0 104rpx;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      position: relative;
      font-weight: 400;
      font-size: 24rpx;
      color: #FF4100;
      .luckyoinIcon{
        position: absolute;
        bottom: 8rpx;
        left: 24rpx;
        width: 76rpx;
        height: 74rpx;
      }
      .rightRed{
        width: 32rpx;
        height: 32rpx;
        margin-left: auto;
      }
    }
    .bottomContent{
      width: 750rpx;
      height: 172rpx;
      background: #FFFFFF;
      box-shadow: 0rpx -4rpx 8rpx 0rpx rgba(0,0,0,0.04);
      padding: 18rpx 32rpx 50rpx 30rpx;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      .bottomL{
        width: 298rpx;
        .goodsNumsMin{
          font-weight: 500;
          font-size: 48rpx;
          color: #FF5500;
        }
        .currentGoodsNums{
          font-weight: 500;
          font-size: 24rpx;
          color: #1D2029;
        }
      }
      .bottomR{
        width: 420rpx;
        height: 88rpx;
        line-height: 88rpx;
        background: #00B484;
        border-radius: 44rpx;
        text-align: center;
        font-weight: 500;
        font-size: 32rpx;
        color: #FFFFFF;
        margin-top: auto;
      }
      .bottomRGray{
        background: #C9CCD4;
      }
    }
  }
</style>
