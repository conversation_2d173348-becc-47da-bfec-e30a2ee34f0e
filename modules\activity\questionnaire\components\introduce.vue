<template>
  <scroll-view scroll-y="true" :style="{
    height:height,
  }">
  <view class="main-content">
    <view class="title">
      <text class="name">{{row.title}}</text>
    </view>
    <view class="secenter">
      <view class="l">
        来源：{{ row.author || '运营' }}
      </view>
      <view class="l">
        {{ row.pushTimeText }}
      </view>
    </view>
    <view class="cover-path">
      <image mode="widthFix"  :src="row.coverPath" @click="previewImg" class="slideimg"/>
    </view>
    <view class="video-content">
      <view class="introduction">
        <view class="head">
          <view class="l">
          </view>
          <view class="r">
            简介
          </view>
        </view>
      </view>

      <view class="introduct-input">
        <!-- <u-parse :content="row.desc" @preview="preview" @navigate="navigate"></u-parse> -->
        <rich-text v-if="row.desc" :nodes="row.desc"></rich-text>
        <view class="o-space">

        </view>
      </view>

      <!-- regForm.desc -->

    </view>
  </view>  
  </scroll-view>

</template>

<script>
  // import uParse from '@/components/uni/u-parse/u-parse.vue'
  export default {
    name:'questionnaireIntroduce',
    components: {
      // TitleTextarea,
      // uParse
    },
    props:{
      height:{
        type:String,
        default:'100vh'
      },
      row:{
        type:[Object,Array],
        default:function (){
          return {}
        }
      }
    },
    // data(){
    //   return {
    //     regForm:{

    //     }
    //   }
    // },
    methods:{
    previewImg(){
      uni.previewImage({
        current: this.row.coverPath, // 当前显示图片的http链接
        urls: [this.row.coverPath] // 需要预览的图片http链接列表
      })
    },
    // preview(src, e) {
    // 	// do something
    // },
    // navigate(href, e) {
    // 	// do something
    // },
    }
  }
</script>

<style lang="scss" scoped>
  .o-space{
    height: 100px;
    width: 100%;
  }
  .introduct-input {
    padding: 20upx;

  }

  .main-content{
    width: 100%;
    padding: 30upx;
    box-sizing: border-box;
    //overflow: hidden;
    .title{
      width: 100%;
      .name{
        font-size: 40upx;
        display: block;
        margin:0 14upx 10upx 0;
        font-weight: 600;
        color: #121822;
      }

    }
    .secenter{
      padding: 30upx 0 30upx 0;
      position: relative;
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      display: -webkit-flex;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      -webkit-justify-content: flex-start;
      justify-content: flex-start;
      -webkit-box-align: center;
      -ms-flex-align: center;
      -webkit-align-items: center;
      align-items: center;
      .l {
        font-size: 25upx;
        font-weight: 400;
        color: #A5AEBB;
        line-height: 20upx;
        margin-right: 40upx;
      }
    }
    .cover-path{
      image{
        width: 100%;
        //height: 100%;
      }
    }
    .video-content{
      width: 100%;
      .introduction{
        padding-top: 10upx;
        position: relative;
        .content{
          color: #726e6e;
        }
      }
    }
    .article{
      line-height: 1.5;
      font-size: 28upx;
      color: #666666;
      padding-bottom: 20upx;
    }
    }
</style>
