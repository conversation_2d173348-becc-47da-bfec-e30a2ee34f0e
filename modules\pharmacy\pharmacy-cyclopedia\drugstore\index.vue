<template>
  <page>
    <view slot="content" class="body-main">
      <view class="drugstore">
        <!-- 分页加在这里  -->
        <view class="m-main-body" v-if="permFlag">
          <scroll-refresh class="scroll-refresh-main" :isShowEmptySwitch="true" :fixed="false" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
          <view class="header">
            <view class="title">
              <view class="img">
                <image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/headLocation2.png'"></image>
              </view>
              <span @tap="linzqCitySelectState" v-if="permFlag">{{ cityInfo.name || '定位失败' }}</span>
              <span @tap="initLocationPerm" v-else>选择地址</span>
            </view>
          </view>
          <view class="drugstore-content">
            <view class="drugstore-item" :style="{backgroundImage:'url(' + file_ctx + 'static/image/business/pharmacy-cyclopedia/drugstoreList.png)'}" v-for="(item,index) in indexlist" :key="item.id">
              <view class="item-head">
                <view class="head-l">
                  <view class="img">
                    <image :src="item.logoPath.includes(file_ctx) ? item.logoPath : file_ctx + item.logoPath" @error="handleError(item)"></image>
                  </view>
                  <view class="title">{{ item.name }}</view>
                </view>
              </view>
              <view class="item-bott">
                <view class="item-info">
                  <text class="info-l" @click="handleAddress(item)">{{ item.address }}</text>
                  <view class="info-r">
                    <view class="line" v-if="item.pharmacyDistance"></view>
                    <view class="info-r-item">
                      <view class="phone" @click="callPhoneNumber(item.contactNumber)" v-if="!!item.contactNumber">
                        <view class="img">
                          <image :src="file_ctx +'static/image/business/pharmacy-cyclopedia/smallPhone2.png'"></image>
                        </view>
                        <span>联系门店</span>
                      </view>
                      <view class="location" @click="handleAddress(item)" v-if="item.pharmacyDistance">
                        <view class="img">
                          <image :src="file_ctx +'static/image/business/pharmacy-cyclopedia/smallLocation2.png'"></image>
                        </view>
                        <span>{{ formattedDistance(item.pharmacyDistance) }}</span>
                      </view>
                    </view>
                  </view>
                </view>
                <view class="item-time" v-if="item.businessHoursText">
                  <span class="time">营业时间:{{ item.businessHoursText }}</span>
                </view>
              </view>
            </view>
          </view>
          <!-- <view class="empty-box" slot="empty">
            <image
              class="empty-img"
              mode="aspectFill"
              :src="file_ctx + 'static/image/business/hulu-v2/icon-circle-empty.png'"
            ></image>
            <text class="empty-text">暂无更多圈子</text>
          </view> -->          
          </scroll-refresh>
        </view>
        <view class="empty" v-else>
          <view class="img">
            <image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/blankpage.png'"></image>
          </view>
          <view class="text">需要获取您当前的地理位置权限</view>
          <button class="empty-btn" @click="initLocationPerm">点此授权</button>
        </view>
        <view class="small-cart" v-if="appletSwitch == 1" @click="handleClickYellowCart"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-nearby-drugstore-samll-card.png'"></image></view>
      </view>      
    </view>
  </page>
</template>

<script>
  import { mapState } from 'vuex'
  import { isDomainUrl } from '@/utils/index.js'
  export default {
    data(){
      return{
        constant: this.$constant,
        file_ctx: this.file_ctx,
        mescroll: null, // mescroll实例对象
        downOption: {
          auto: false // 不自动加载
        },
        upOption: {
          auto: false, // 不自动加载
          empty: {
            top: 0,
            zIndex: 999,
          },
          onScroll: true
        },
        indexlist:[],
        currentIndex:0,
        cityInfo: {},
        isOnLoadStatus: false,// 第一次预先加载状态
        permFlag:true,
        bindingId:null,
        brandId:null, //企业Id
        system:null, //系统信息
        appletSwitch:null, //是否开启小黄车
        path:null, //小黄车路径
        appId:null, //小黄车appid
        labelValue:null,
        channelCode:null,
        pharmacyBindType:null,
      }
    },
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId,
        publishInfo:state => state.publishInfo,
        curSelectUserInfo:state => state.curSelectUserInfo
      }),
    },
    onLoad(options){
      const query = this.$validate.isNull(this.$Route.query) ? options : this.$Route.query
      this.bindingId = query.bindingId
      this.brandId = query.brandId
      this.appletSwitch = query.appletSwitch
      this.labelValue = query?.labelValue
      this.channelCode = query?.gs
      if(query?.extProfiles){
        this.pharmacyBindType = query?.extProfiles?.pharmacyBindType
      }
      if(query?.path){
        this.path = query.path
      }
      if(query?.appId){
        this.appId = query.appId
      }
      this.system = uni.getSystemInfoSync() //获取系统信息
      this.initLocationPerm()

    },
    onShow(){
      if (this.isOnLoadStatus) {
        this.initiData()
      }
    },
    onShareAppMessage: function (res) {  
      if (res.from === 'share') {  
        // 来自页面内转发按钮  
        console.log(res.target);  
      }
      this.queryAndCreate(3)
      return {
        title: `${this.publishInfo.name}${this.labelValue}`, //分享的名称
        path: 'modules/pharmacy/pharmacy-cyclopedia/index?gs='+ encodeURIComponent(this.channelCode),
        mpId:this.$appId, //此处配置微信小程序的AppId
      }
    },
    mounted(){},
    methods:{
      async queryAndCreate(type){
        let { phone = '' } =  this.curSelectUserInfo || {}
        let params = {
          accountId:this.accountId,
          phone: phone,
          productId:this.bindingId,
          shareType:type,  //	分享类型：1分享按钮2保存二维码3微信好友分享按钮
        }
        await this.$api.drugBook.queryAndCreate(params)
      },
      // 小黄车跳转
      handleClickYellowCart() {
        this.$uniPlugin.navigateToMiniProgram({
          appId:this.appId,
          path:this.path,
          envVersion: 'release',
          extraData: {}
        }, (res) => {
          resolve(true)
          console.log(res)
        }, (err) => {
          resolve(false)
        })
      },

      // 转换后的距离
      formattedDistance(val) {  
        const distanceMeters = parseInt(val)  
        let formatted;  
    
        if (distanceMeters >= 1000) {  
          // 转换为千米  
          const distanceKm = distanceMeters / 1000;  
          formatted = distanceKm.toFixed(1) + 'km'; // 保留一位小数  
        } else if (distanceMeters >= 100) {  
          // 显示为百米  
          formatted = distanceMeters + 'm';  
        } else if (distanceMeters < 100) {  
          // 小于百米 
          formatted = '<100m';  
        } else {  
          // 无
          formatted = '';  
        }  
    
        return formatted;  
      },  
      
      // 获取定理位置权限
      initLocationPerm(){
        let that = this
        // 手机系统定位
        if (!this.system.locationEnabled) {
          uni.showModal({
            title: '提示',
            content: '手机系统定位未打开！',
            showCancel: false,
            success: function(res) {
              if (res.confirm) {
              } 
            }
          });
          return false
        } else {
          uni.getSetting({
            success: function (res) {
              if (!res.authSetting['scope.userLocation']) {
                // 如果未获取定位权限，则请求授权
                // console.log('未获取定位权限')
                uni.authorize({
                  scope: 'scope.userLocation',
                  success() {
                    // 用户已经同意授权定位权限
                    that.getLocationOperate()
                  },
                  fail(error) {
                    that.permFlag = false
                    // 没有权限则提示弹窗
                    uni.showModal({
                      title: '提示',
                      content: '需要获取地理位置权限，请到小程序设置页面打开授权',
                      success (res) {
                        console.log(res)
                        if (res.confirm) {
                          // 选择弹框内授权
                          uni.openSetting({
                            success (res) {
                              that.getLocationOperate()
                            },
                          })
                        } else if (res.cancel) {
                          // 选择弹框内 不授权
                        }
                      }
                    })
                    }
                });
              } else {
                // 如果已经获取定位权限，直接进行定位操作
                that.getLocationOperate()
              }
            },
            fail(error) {
            }
          });
        }

      },
      
      getLocationOperate(){
        this.permFlag = true
        this.$nextTick(()=>{
          this.$ext.utility.getLocation().then((res) => {
            this.$ext.common.getPosition(res).then((data) => {
            // this.$ext.common.getPosition({...res,longitude:"116.43375399999991",latitude:"39.921479000000005",}).then((data) => {
              this.initCityInfoStorage()
              this.isOnLoadStatus = true
              this.init()
              this.initiData()
            }).catch(() => {

            })
          }).catch(() => {

          })
        })
      },

      initCityInfoStorage() {
        this.$nextTick(() => {
          const cityInfo = this.$common.getKeyVal('system', 'cityInfoStorage', true)
          if (!this.$validate.isNull(cityInfo)) {
            this.cityInfo = cityInfo
          } else {
            this.cityInfo = {}
          }
        })
      },

      initiData() {
        const that = this
        that.$nextTick(() => {
          let cityInfoStorage = that.$common.getKeyVal('system', 'cityInfoStorage', true)
          if (typeof cityInfoStorage === 'string' && that.$validate.isNull(cityInfoStorage)) {
            cityInfoStorage = {}
          } else if (typeof cityInfoStorage === 'string' && !that.$validate.isNull(cityInfoStorage)) {
            cityInfoStorage = JSON.parse(cityInfoStorage)
          }
          if (!that.$validate.isNull(cityInfoStorage)) {
            if (typeof cityInfoStorage === 'object' && !that.$validate.isNull(cityInfoStorage)) {
              that.cityInfo = cityInfoStorage
            } else {
              that.cityInfo = {}
            }
          } else {
            that.cityInfo = {}
          }
        })
      },

      handleAddress(item){
        uni.openLocation({
          latitude: Number(item.latitude),
          longitude: Number(item.longitude),
          name:item.address,
          address:item.address,
          success: function () {
            // console.log('success');
          }
        });
      },

      linzqCitySelectState() {
        this.$navto.push('CommonPositioningPoi')
      },

      handleError(item){
        item.logoPath = this.file_ctx + 'static/image/business/pharmacy-cyclopedia/drugLogo.png'
      },

      callPhoneNumber(phone){
        const phoneNumber = phone; // 替换为实际的电话号码  
        uni.makePhoneCall({
          phoneNumber: phoneNumber,
          success: () => {
            // console.log('拨打电话成功！');
          },
          fail: () => {
            // console.error('拨打电话失败！');
          }
        });
      },

      handleClickTab(index){
        this.currentIndex = index
      },

      returnFn(obj) {
        this.system = uni.getSystemInfoSync() //获取系统信息
        const that = this
        if (!this.system.locationEnabled) {
          uni.showModal({
            title: '提示',
            content: '手机系统定位未打开！',
            showCancel: false,
            success: function(res) {
              if (res.confirm) {
              } 
            }
          });
          obj.successCallback && obj.successCallback([])
          return false
        }
        
        setTimeout(function () {
          let params = {
            current: obj.pageNum,
            size: obj.pageSize,
            condition:{
              bindingId:that.bindingId,
              bindType:1, //默认已关联
              latitude:that.cityInfo.lng,
              longitude:that.cityInfo.lat,
              range:'5000' //默认5公里
            }
          }
          // 企业Id列表默认显示医院数据
          if(that.pharmacyBindType === 2){
            // let brandList = ['2117512918354862083','2006014821225963523','2083438491020312581','2093684894593409027','2091582773609873410','2109655889818025985','2137162016691310597','2148044127087587333','2148011574305714178','2148010541462134786','2148006452183506947','2184891185638113283','2205277838635655174']
            // if(brandList.includes(that.brandId)){
            //   delete params?.condition?.bindingId 
            // } else if(that.brandId == '2022748678633525253' || that.brandId == '2052444454004228103'){
            //   params.condition.range = '6000000'
            // }
            delete params?.condition?.bindingId
            params.condition.range = '6000000'
          } else {
            params.condition.bindType = 1
          }
          that.$api.drugBook.getPharmacyQueryPage(params).then(res => {
            let data = res.data.records.map(item=>{
              return{
                ...item,
                logoPath:isDomainUrl(item.logoPath),
                businessHoursText:item.startBusinessHours + (item.endBusinessHours ? '-' : '') + item.endBusinessHours
              }
            }) || []
            if (obj.pageNum === 1) {
              that.indexlist = []
            }
            that.indexlist = [...that.indexlist, ...data]
            obj.successCallback && obj.successCallback(data)
          })
        }, that.$constant.noun.scrollRefreshTime)
      },

      scrollInit(scroll) {
        scroll.optUp.page.num = 1
        scroll.optUp.page.size = 10
        this.mescroll = scroll
      },
      init() {
        this.$nextTick(() => {
          this.mescroll.triggerDownScroll()
        })
      },
    },
 }
</script>

<style lang='scss' scoped>
.body-main{
  height: 100%;
  .drugstore{
    position: relative;
    height: 100%;
  .header{
    display: flex;
    flex-direction: column;
    padding:24upx;
    background-color: #fff;
    .title{
      display: flex;
      align-items: center;
      .img{
        width: 25.45rpx;
        height: 33.08rpx;
        margin-right: 6rpx;
        image{
          width: 100%;
          height: 100%;
        }
      }
      span{
        color: #252525;
        font-size: 31.81rpx;
        margin-top: -1rpx;
      }
    }
  }
  .m-main-body{
    // height: calc(100% - 144rpx);
    height: 100%;
    .scroll-refresh-main{
      height: 100%;
      .drugstore-content{
          padding:0 24upx;
          .drugstore-item{
            margin-top: 24upx;
            // background-color: #fff;
            border-radius: 13upx;
            overflow: hidden;
            background-size: 100%;
            background-repeat: no-repeat;
            .item-head,.item-bott{
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding:30upx 24upx;
              .head-l{
                display: flex;
                align-items: center;
                .img{
                  width: 47.71rpx;
                  height: 47.71rpx;
                  border-radius: 50%;
                  overflow: hidden;
                  image{
                    width: 100%;
                    height: 100%;
                  }
                }
                .title{
                  font-weight: 500;
                  font-size: 31.81rpx;
                  color: #252525;
                  margin-left: 10upx;
                  margin-top: -1rpx;
                }
              }
              .head-r{
                display: flex;
                align-items: center;
                justify-content: center;
                width: 132.32rpx;
                height: 42.62rpx;
                border-radius: 21.31rpx;
                color:#fff;
                font-size: 25.45rpx;
                background-color: #1CB8C1;
              }
            }
            .item-bott{
              // padding:30upx 20upx;
              flex-direction: column;
              background-color: #fff;
              .item-info{
                display: flex;
                justify-content: space-between;
                width: 100%;
                .info-l{
                  width: 400upx;
                  font-size: 28.63rpx;
                  color:#252525;
                }
                .info-r{
                  display: flex;
                  flex: 1;
                  // justify-content: space-around;
                  margin-left: 20rpx;
                  .line{
                    width: 1rpx;
                    height: 100%;
                    background-color: #ccc;
                  }
                  .info-r-item{
                    display: flex;
                    flex: 1;
                    justify-content: space-evenly;
                    .phone,.location{
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      flex-direction: column;
                      .img{
                        width: 29.9rpx;
                        height: 35.62rpx;
                        image{
                          width: 100%;
                          height: 100%;
                        }
                      }
                      span{
                        margin-top: 13rpx;
                        color:#8F8E94;
                        font-size: 24.17rpx;
                      }
                    }
                    .location{
                      .img{ 
                        width: 30.53rpx;
                        height: 38.17rpx;
                      }
                    }
                  }
                }
              }
              .item-time{
                display: flex;
                align-items: center;
                width: 100%;
                margin-top: 20upx;
                color:#8F8E94;
                font-size: 24upx;
                .tag{
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  border-radius: 3.18rpx;
                  padding: 2rpx 7rpx 3rpx;
                  font-weight: 500;
                  color:#FA5F35;
                  background-color: #FBEEDE;
                  margin-right: 12upx;
                }
                .time{
                  // margin:0 12upx;
                  margin-right: 12upx;
                }
                .line{
                  width: 1upx;
                  height: 100%;
                  background-color: #8F8E94;
                  margin-right: 5upx;
                }
              }
            }
          }
      } 
    }
  }
  .empty{
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    height: 40vh;
    .img{
      width: 200upx;
      height: 200upx;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .text{
      margin: 30upx 0;
    }
    .empty-btn{
      font-size: 30rpx;
      padding: 0 40rpx;
    }
  }
  .small-cart{
    position: absolute;
    bottom: 132rpx;
    right: 0;
    z-index: 999;
    width: 144rpx;
    height: 144rpx;
    image{
      width: 100%;
      height: 100%;
    }
  }
  }
}
</style>