<template>
  <page>
    <view slot="content" class="content" :style="{ maxhHeight: `${scrollHeight}px`, backgroundImage: `url(${imgList.bg})` }">

      <view class="content-cell">
        <view class="banner image">
          <image :src="imgList.banner" style="width: 750upx;height: 300upx;" mode="aspectFill"/>
        </view>
        <view class="content-center">
          <view
            :style="{ backgroundImage: `url(${imgList.whiteTop})` }"
            class="whiteTop"
          ></view>
          <text class="btn-receive" @click="handleReceive" v-if="urlParams.isFree === 1">点我领袋</text>
          <text class="not-receive" v-else-if="urlParams.isFree === 2">该账号今日权益已领完，可切换其他账号重新扫码</text>
        </view>
        <!-- 底部 -->
        <view class="content-bottom">
          <image style="height: 48upx;" :src="imgList.logo"></image>
          <view class="tips-text" @click="handleTel('************')">
            <text>遇到出袋问题请拨打客服电话：</text>
            <text>************</text>
          </view>
        </view>
      </view>

    </view>
  </page>

</template>

<script>
import { getQueryStr, checkHttp ,sitecheckInit, param2Obj} from '@/utils/index'
import env from '@/config/env'
export default {
  components: {

  },
  name: 'Home',
  data() {
    return {
      height: '0px',
      qrCodeUrl: null,
      deviceId: null,
      showReload: false,
      scanH5QrCodeText: null,
      reloadText: '请重新扫码进入',
      accountId: null,
      appId: null,
      hidenChooseGender: true,
      scanH5QrCodeData: {},
      imgList: {
        bg: env.file_ctx + 'h5/home/<USER>',
        white: env.file_ctx + 'h5/home/<USER>',
        code: env.file_ctx + 'h5/home/<USER>',
        logo: env.file_ctx + 'h5/home/<USER>',
        xiaobao: env.file_ctx + 'h5/home/<USER>',
        repair: env.file_ctx + 'h5/home/<USER>',
        banner: env.file_ctx + 'h5/background.jpg',
        // banner: env.file_ctx + 'h5/home/<USER>',
        close: env.file_ctx + 'h5/home/<USER>',
        guide: env.file_ctx + 'h5/home/<USER>',
        hand: env.file_ctx + 'h5/home/<USER>',
        tips: env.file_ctx + 'h5/home/<USER>',
        whiteTop: env.file_ctx + 'h5/home/<USER>',
      },
      showError: false,
      showHand: false,
      scrollHeight: uni.getSystemInfoSync().screenHeight,
      isFree: '',
      freeTicket: '',
      urlParams: {}
    }
  },
  onShow() {
    const _this = this
    this.$nextTick(() => {
      // this.createJsapiSignature()
    })
  },
  async onLoad(data) {
    const that = this
    that.urlParams = that.$Route.query
    const { deviceId,gbUserId } = that.$Route.query
    that.deviceId = deviceId
    const param = {
      userId: gbUserId,
      deviceId
    }
    if (!gbUserId) {
      this.$uniPlugin.toast('用户ID异常，请重新扫码！')
      return
    }
    if (!deviceId) {
      this.$uniPlugin.toast('设备ID异常，请重新扫码！')
      return
    }

    const res = await that.$api.common.alipayGetUseridIsFree(param)
    const {isFree,freeTicket} = res.data
    that.urlParams = {
      ...that.urlParams,
      freeTicket,
      isFree
    }
    if(that.urlParams.isFree === 2) {
      that.$uniPlugin.toast('该账号今日权益已领完，可切换其他账号重新扫码')
    }
  },
  methods: {
    async handleReceive () {
      const { deviceId,freeTicket,gbUserId } = this.urlParams
      const res = await this.$api.common.alipayFreeStartPacket({ deviceId,freeTicket,userId:gbUserId })
      const { packetId } = res.data
      this.$navto.push('ResultLoading', {...this.urlParams,packetId})
    },
    handleTel (val) {
      uni.makePhoneCall({
        phoneNumber: val
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  width: 100vw;
  // min-height: 100vh;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  // display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  .content-cell {
    max-width: 750upx;
    height: 100%;
    padding: 40upx;
    box-sizing: border-box;
    .banner {
      max-width: 750upx;
      // height: 20vh;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      border-radius: 20upx;
      overflow: hidden;
      img {
        width: 750upx;
        border-radius: 20upx;
      }
    }
    .content-center {
      border-radius: 20upx;
      margin-top: 2vh;
      height: 60vh;
      background-color: #fff;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      position: relative;
      // overflow: hidden;
      .whiteTop {
        background-size: contain;
        background-repeat: no-repeat;
        background-position: top;
        width: 60vw;
        height: 3vh;
        position: absolute;
        top: -1vh;
      }
      .btn-receive {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 80%;
        background-color: $warningColor;
        border-radius: 50upx;
        border: none;
        font-size: 32upx;
        font-weight: 800;
        height: 90upx;
        padding: 0 30upx;
        color: #fff;

        &:active {
          opacity: .8;
        }
      }
      .not-receive {
        font-size: 36upx;
      }
      .cell {
        box-sizing: border-box;
        margin-top: 40upx;
        background: #f7f1f1;
        padding: 5vw;
        border-radius: 20upx;
        // width: 95vw;
        .top {
          font-size: 36upx;
          view {
            margin-bottom: 20upx;
          }
        }
      }

      .centerC {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
      }
      .repair {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 60%;
        height: 60%;
        background-size: 100%;
        background-repeat: no-repeat;
        background-position: bottom;
        z-index: 10;
        border-radius: 20upx;
      }
      .scanH5QrCodeText {
        font-size: 36upx;
        margin-bottom: 25vh;
        word-break: break-all;
        line-height: 48upx;
        padding: 0 40pux;
      }

    }
    .content-bottom {
      text-align: center;
      margin-top: 3vh;
      // height: 10vh;
      .tips-text {
        font-size: 28upx;
        color: #fff;
        line-height: 40upx;
        font-size: 28upx;
      }
    }
  }
}
</style>
