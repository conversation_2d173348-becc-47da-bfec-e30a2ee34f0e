<!--横幅广告-->
<template>
  <view class="banner-main" :style="{'--height': height, '--width': width}">
    <!--<loading-layer :config="{zIndex: 10}" :cData="isLoadingLayer" :position="'absolute'"></loading-layer>-->
    <swiper circular :indicator-dots="true" :autoplay="true" indicator-active-color="#00B484"
        class="swiper-box" :style="'height: ' + height + ' !important;'" @change="change" v-if="bannerList.length > 0">
      <swiper-item v-for="(item, index) in bannerList" :key="index" class="swiper-item">
        <!-- #ifdef MP-WEIXIN -->
        <view class="info-li-img" v-show="item.gdtAdSwitch === 1 && adCustomUnitId && (adCustomErrorObj[item.id] !== false)">
          <ad-custom :unit-id="adCustomUnitId" @load="adLoad" @error="adError($event, item)" @close="adClose($event, item)" ad-intervals="30"></ad-custom>
        </view>
        <view class="info-li-img" v-show="!(item.gdtAdSwitch === 1 && adCustomUnitId && (adCustomErrorObj[item.id] !== false))">
          <image class='banner-info' :src="item.url" mode="scaleToFill" @tap="navigateTo(item,index)" />
        </view>
        <!-- #endif -->

        <!-- #ifndef MP-WEIXIN -->
        <view class="info-li-img">
          <image class='banner-info' :src="item.url" mode="scaleToFill" @tap="navigateTo(item,index)" />
        </view>
        <!-- #endif -->
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import { getQueryObject, isDomainUrl } from '@/utils/index'
const launchOptions = uni.getLaunchOptionsSync()
export default {
  props: {
    queryParams: {
      type: Object,
      default: () => ({})
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '208rpx'
    }
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $common: this.$common,
      bannerList: [

      ],
      current:0,
      adCustomErrorObj: {},
      // isLoadingLayer: true
    }
  },
  computed: {
    ...mapState('user', {
      curSelectUserInfo: state => state.curSelectUserInfo, // 当前选中的用户信息
      curSelectUserInfoId: state => state.curSelectUserInfoId, // 当前选中的档案的ID
    }),
    // 广告位id
    adCustomUnitId() {
      const { useType } = this.queryParams || {}
      switch(useType) {
        // 首页
        case 1:
          return 'adunit-2ac94b3fdda432fa'
        // 个人中心横幅
        case 5:
          return 'adunit-fa05c22fae56a1aa'
        // 交流页横幅
        case 7:
          return 'adunit-0f5bafd6401150b2'
        // 文章底部
        case 9:
          return 'adunit-e8cd3fdfb4fcb8d6'
        default:
          return
      }
    }
  },

  methods: {
    // #ifdef MP-WEIXIN
    adLoad(e) {
    },
    adError(e, item) {
      this.$set(this.adCustomErrorObj, item.id, false)
    },
    adClose(e, item) {
      this.$set(this.adCustomErrorObj, item.id, false)
    },
    handleClickTrack(data){
      getApp().globalData.sensors.track("OperationClick",
        {
          'page_name':'首页',
          'first_operation_name' : data.desc,
          'operation_floor' : '4',
          'operation_id' : data.id+'',
          'operation_type':'banner位',
          'is_link_third' : data?.is_link_third || '否',
          'target_url' : data.skipUrl,
          'position_rank' : '11',
        }
      ) 
    },
    // #endif
    change(e) {
      this.current = e.detail.current
      this.$nextTick(()=>{
        this.trackExposure({...this.bannerList[this.current],type:17},this.current)
      })
    },
    async init() {
      await this.bannerQueryList()
    },
    recordClick (data,index) {
      this.trackExposure({...data,clickType:2},index)
    },

    // 曝光方法
    async trackExposure(data,index){
      if (!this.bannerList[index]) return
      const param = {
        accountId: this.$common.getKeyVal('user', 'accountId'),
        businessType: 1, // 业务类型:1-横幅，2-外部服务应用，3-企业微信群，4-圈子，5-帖子，6-帖子评论
        businessId: data.id,
        source: 1, // 来源：1-真实用户，2-马甲
        type: data.type || 2, // 类型：1-访问，2-点击，3-长按，4-关注，5-取关，6-点赞，7-收藏，8-删除评论，9-取消点赞，10-取消收藏，11-阅读
      }
      await this.$api.community.applicationoperateV2logInsert(param)
    },
    // 轮播图页面跳转
    navigateTo(e,index) {
      if(launchOptions.scene === 1154) return this.$uniPlugin.toast('请前往小程序使用完整服务') 
      // 跳转类型:1-静态，2-跳内部页面，3-跳Web地址,4-跳小程序
      e.skipType = typeof(e.skipType) === 'number' ? JSON.stringify(e.skipType) : e.skipType
      this.recordClick(e,index)
      switch (e.skipType) {
        case '1':
          break
        case '2':
          if (e.skipUrl) {
            const arr = e.skipUrl.split('?')
            let query = ''
            if (arr.length > 0){
               query = getQueryObject(decodeURIComponent(arr[1]))
            }
            if (arr[0].indexOf('/') === -1) {
              this.$navto.pushTab(arr[0], query)
            } else {
              this.$navto.pushPath(arr[0], query)
            }
          // #ifdef MP-WEIXIN
          this.handleClickTrack(e)
          // #endif
          }
          break
        case '3':
          this.$navto.push('WebHtmlView', { src: e.skipUrl, title: '' })
          // #ifdef MP-WEIXIN
          this.handleClickTrack(e)
          // #endif
          break
        case '4':
          uni.navigateToMiniProgram({
            appId: e.minAppId,
            path: e.skipUrl, // 打开的页面路径
            extraData: {}, // 需要传递的参数
            success: res => {
            },
            fail: err => {
              // console.log('打开失败------', err)
            }
          })
          // #ifdef MP-WEIXIN
          this.handleClickTrack({...e,is_link_third:'是'})
          // #endif
          break
        default:
      }
    },

    bannerQueryList() {
      const that = this
      let param = {
        ...this.queryParams
      }
      this.bannerList = []
      that.$api.common.bannerQueryList(param).then(async(res) => {
        for (let i = 0; i < res.data.length; i++) {
          let data = res.data[i]
          this.bannerList.push({
            ...data,
            url: isDomainUrl(res.data[i].image),
          })
          if([10,11].includes(this.queryParams?.useType)){
            this.bannerList = this.bannerList.slice(0,5)
          }
          // 手动触发第一张图的曝光
          if(this.bannerList.length == 1){
            this.trackExposure({...this.bannerList[this.current],type:17},this.current)
          }
        }
        // 通知父组件banner数据
        this.$emit('informBanner',this.bannerList.length ? true : false)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  /* #ifdef H5 */
  .banner-main{
    padding:0 32rpx;
  }
  /* #endif */
.banner-main{
  margin-bottom: 32rpx;
  .swiper-box{
    height: var(--height);
    @include rounded(20rpx);
    .swiper-item{
      transform: translateZ(0); /* 触发 GPU 加速 */
      .info-li-img{
        height: 100%;
        width: 100%;
        .banner-info{
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}
</style>
