import common from '@/common/util/main'
import api from '@/service/api'
import env from '@/config/env'

/**
 * 系统请求工具类，
 * 注意：API接口只做请求数据和格式化数据的事情，其他事务一概不处理
 */
export default {
  /**
   * 消息聊天列表--分页列表查询
   * @param resolve
   * @param reject
   */
  chatlistQueryPage(param) {
    const that = this
    return new Promise((resolve, reject) => {
      api.chat.chatlistQueryPage(param).then((res) => {
        if (res && res.data.records) {
          for (const a in res.data.records) {
            const data = res.data.records[a]
            res.data.records[a] = that.formatChatlist(data)
          }
          resolve(res.data.records)
        }
      }).catch(error => {

      })
    })
  },
  /**
   * 格式化消息列表对象
   * @param data
   */
  formatChatlist(data){
    if (data.headPath.length !=0){
      if (data.headPath.indexOf("http")!=-1){
      }else {
        data.headPath = env.file_ctx + (data.headPath.length==0?'image/system/avatar/icon-default-avatar.png':data.headPath)
      }
    }
    data.lastMsgTimeText = common.formatDate(new Date(data.lastMsgTime), 'yyyy-MM-dd')
    data.lastMsgContent = data.lastMsgContent.length > 23?data.lastMsgContent.substring(0,23) + '...': data.lastMsgContent
    return data
  },
}
