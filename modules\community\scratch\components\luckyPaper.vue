<template>
  <view class="scratch-container">
    <!-- 底层奖品内容 -->
    <view class="prize-content" v-if="showPrize">
      <text class="prize-text">{{showText}}</text>
    </view>
    <!-- 刮刮乐遮罩层 -->
    <canvas 
      class="luckyPaper" 
      canvas-id="luckyPaper"
      @touchstart="onTouchStart"
      @touchmove="onTouchMove"
      @touchend="onTouchEnd"
      disable-scroll="true"
    ></canvas>
  </view>
</template>

<script>
  export default {
    name: '',
    data() {
      return {
        canvasCTX: null,
        file_ctx: this.file_ctx,
        luckyPaperIcon: this.file_ctx + 'static/image/business/hulu-v2/luckyPaper.png',
        luckyPaperStyle: {
          width: 584,
          height: 296
        },
        // 刮刮乐相关状态
        isScratching: false,
        scratchedArea: 0, // 已刮除的面积百分比
        scratchThreshold: 30, // 刮除阈值，超过30%显示完整奖品
        canvasWidth: 0,
        canvasHeight: 0,
        showPrize: false, // 是否显示底层图片
        isComplete: false, // 是否刮奖完成
        prizeTextOptions: {
          noPrizeText: '🎯 很遗憾，这次与大奖擦肩而过…',
          prizeText: '💡 别揉了，眼睛没花！您真的中奖了',
        },
        showText:''

      }
    },
    methods: {
      // 绘制图片到canvas
      drawImageToCanvas({path, x, y, width, height, canvas, isCircle = false}) {
        let resFn;
        let pro = new Promise((res) => resFn = res);
        // 绘制图片到canvas
        canvas.drawImage(path, x, y, width, height);
        canvas.draw(isCircle, async () => resFn())
        return pro
      },
      
      // 获取图片信息
      getImageInfo({imgSrc}) {
        let resFn;
        let pro = new Promise((res) => resFn = res);
        uni.getImageInfo({
          src: imgSrc,
          success: (res) => {
            resFn(res)
          },
          fail: (err) => {
            console.error('获取图片信息失败:', err);
            resFn(err)
          }
        })
        return pro
      },
      
      // 初始化刮刮乐遮罩
      async initScratchMask() {
        const luckyPaperIconInfo = await this.getImageInfo({imgSrc:this.luckyPaperIcon})
        // 绘制图片到canvas
        await this.drawImageToCanvas({path:luckyPaperIconInfo.path,x:0,y:0,width:this.canvasWidth,height:this.canvasHeight,canvas:this.canvasCTX})  
        // 加载完后才渲染礼品
        this.showPrize = true;
        this.showText = this.prizeTextOptions.noPrizeText;
      },
      
      // 触摸开始
      onTouchStart(e) {
        if (this.isComplete) return;
        this.isScratching = true;
        const touch = e.touches[0];
        this.scratch(touch.x, touch.y);
      },
      
      // 触摸移动
      onTouchMove(e) {
        if (!this.isScratching || this.isComplete) return;
        const touch = e.touches[0];
        this.scratch(touch.x, touch.y);
      },
      
      // 触摸结束
      onTouchEnd(e) {
        this.isScratching = false;
        this.checkScratchProgress();
      },
      
      // 刮除效果
      scratch(x, y) {
        // 设置合成模式为destination-out，实现擦除效果
        this.canvasCTX.globalCompositeOperation = 'destination-out';
        this.canvasCTX.beginPath();
        this.canvasCTX.arc(x, y, 20, 0, 2 * Math.PI); // 刮除半径为20px
        this.canvasCTX.fill();
        this.canvasCTX.draw(true); // 保留之前的绘制内容
        
        // 恢复合成模式
        this.canvasCTX.globalCompositeOperation = 'source-over';
      },
      
      // 检查刮除进度
      checkScratchProgress() {
        // 获取canvas图像数据来计算刮除面积
        uni.canvasGetImageData({
          canvasId: 'luckyPaper',
          x: 0,
          y: 0,
          width: this.canvasWidth,
          height: this.canvasHeight,
          success: (res) => {
            const data = res.data;
            let transparentPixels = 0;
            
            // 计算透明像素数量
            for (let i = 3; i < data.length; i += 4) {
              if (data[i] < 128) { // alpha值小于128认为是透明
                transparentPixels++;
              }
            }
            
            const totalPixels = this.canvasWidth * this.canvasHeight;
            this.scratchedArea = (transparentPixels / totalPixels) * 100;
            // 如果刮除面积超过阈值，显示完整奖品
            if (this.scratchedArea >= this.scratchThreshold) {
              this.revealPrize();
            }
          },
          fail: (err) => {
            console.error('获取canvas数据失败:', err);
          }
        }, this);
      },
      
      // 显示完整奖品
      revealPrize() {
        this.isComplete = true;
        // 清除整个canvas，显示底层奖品
        this.canvasCTX.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
        this.canvasCTX.draw();
        // 触发刮奖完成事件
        this.$emit('scratchComplete', {
          scratchedArea: this.scratchedArea,
          prize: '100元优惠券'
        });
      },
      
      // 重置刮刮乐
      resetScratch() {
        this.isComplete = false;
        this.scratchedArea = 0;
        this.isScratching = false;
        this.initScratchMask();
      }
    },
    async created() {
      this.canvasCTX = uni.createCanvasContext('luckyPaper', this);
      // 通过计算算出584rpx在当前设备等于多少px
      const systemInfo = uni.getSystemInfoSync();
      const screenWidth = systemInfo.screenWidth;
      this.canvasWidth = (this.luckyPaperStyle.width / 750) * screenWidth; // 584rpx转px
      this.canvasHeight = (this.luckyPaperStyle.height / 750) * screenWidth; // 296rpx转px
      
      // 初始化刮刮乐遮罩
      this.initScratchMask();
    }
  }
</script>

<style scoped lang="scss">
.scratch-container {
  position: relative;
  width: 584rpx;
  height: 296rpx;
  border-radius: 12rpx;
  overflow: hidden;
  .luckyPaperIcon{
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9999999999;
  }
}

.prize-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  z-index: 1;
}

.prize-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #D2691E;
  margin-bottom: 20rpx;
}

.prize-desc {
  font-size: 28rpx;
  color: #8B4513;
  font-weight: 500;
}

.luckyPaper {
  position: absolute;
  top: 0;
  left: 0;
  width: 584rpx;
  height: 296rpx;
  z-index: 2;
}
</style>