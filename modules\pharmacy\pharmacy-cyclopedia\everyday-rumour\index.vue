<template>
  <view class="everyday-rumour-wrapper">
    <integration-time-er :backTaskFlag='IntegrationData.backTaskFlag'></integration-time-er>
    <!-- #ifdef H5 -->
    <view :style="'height:' + statusBarHeight + 'px;'"></view>
    <view class="top-nav" style="background-color: #fff;">
      <view class="top-nav-l" @click.stop="handleBack"><image mode="aspectFit" :src="file_ctx + 'static/image/business/hulu-v2/icon-circle-left-arrows.png'" class="header-search-img"/></view>
      <view class="top-nav-c">每日辟谣</view>
    </view>
    <view class="poster-content">
      <wx-open-launch-weapp
        id="launch-btn"
        :appid="$appId"
        @launch="handleClickBtn(1)"
        :path="`modules/pharmacy/pharmacy-cyclopedia/everyday-rumour/index?gs=QNZNFz`"
        style="width: 750rpx;height: 1292rpx;position:absolute;"
      >
        <script type="text/wxtag-template">
          <div style="width: 750rpx;height: 1292rpx;position: relative;">
            <img style="width: 100%;height: 100%;" src="https://file.greenboniot.cn/static/image/business/pharmacy-cyclopedia/rumour-poster.png"></img>
          </div>

        </script>
      </wx-open-launch-weapp>
      <wx-open-launch-weapp
        id="launch-btn"
        :appid="$appId"
        @launch="handleClickBtn(2)"
        :path="`modules/pharmacy/pharmacy-cyclopedia/everyday-rumour/index?gs=QNZNFz`"
        style="width: 507.91rpx;height: 98.97rpx;position:absolute;bottom:30rpx;left:50%;transform: translate(-50%, -50%);"
      >
        <script type="text/wxtag-template">
          <div class="testing-btn" style="
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            width: 512.91rpx;
            height: 98.97rpx;
          ">
            <img style="width: 100%;height: 100%;" src="https://file.greenboniot.cn/static/image/business/pharmacy-cyclopedia/rumour-poster-btn.png"></img>
            <div style="width:314.02rpx;38.62rpx;position:absolute;">
              <img src="https://file.greenboniot.cn/static/image/business/pharmacy-cyclopedia/rumour-poster-btn-text.png" style="position:absolute;top:50%;left:50%;transform: translate(-50%, -50%);width:157.01px;height:19.31px;">
            </div>
          </div>
        </script>
      </wx-open-launch-weapp>
    </view>
    <!-- #endif -->
    <!-- #ifdef MP-WEIXIN || MP-ALIPAY -->
    <view class="main">
      <view class="my-data" :style="{'background-image':'url('+ file_ctx +'static/image/business/pharmacy-cyclopedia/icon-everyday-ranklist-new-bg.png)','background-size': '100%','background-position': 'center'}">
        <view :style="'height:' + statusBarHeight + 'px;'"></view>
        <view class="top-nav">
          <view class="top-nav-l" @click.stop="handleBack">
            <!-- #ifndef MP-ALIPAY -->
            <image mode="aspectFit" :src="file_ctx + 'static/image/business/hulu-v2/icon-circle-left-arrows.png'" class="header-search-img"/>
            <!-- #endif -->
            <!-- #ifdef MP-ALIPAY -->
            <view class="header-search-img"></view>
            <!-- #endif -->
            <view class="rule" @click.stop="handleClickListRule">辟谣规则</view>
          </view>
          <view class="top-nav-c"></view>
        </view>
      </view>
      <view class="rumour-box">
        <view class="rumour-list" v-if="loading">
          <view class="rumour-empty">加载中...</view>
        </view>
        <view v-else>
          <view class="upper-limit-rumour-list" v-if="limitReached">
            <view class="rumour-item">
              <view class="rumour-item-content">
                <view class="common-answer"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-everyday-common-answer.png'"></image></view>
                <view class="common-answer-title">共答对<span>{{ answerRightNum }}</span>题</view>
                <view class="upper-limit">今日辟谣已达上限，请明天再来</view>
                <button class="look-my-rumour-btn" open-type="share">邀请好友一起玩</button>
              </view>
              <view class="rumour-item-bg"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-everyday-rumour-content-bg.png'"></image></view>
              <view class="more-list">
                <view class="more-list-item" v-for="moreItem in moreList" :key="moreItem.id" @click="handleClickMoreJump(moreItem)">
                  <image class="item-img" :src="file_ctx + moreItem.url"></image>
                  <view class="text">{{ moreItem.name }}</view>
                </view>
                <view class="divider"></view>
              </view>
            </view>
          </view>
          <view class="rumour-list" v-else-if="rumourDetailList.length > 0">
            <view class="rumour-item">
              <view class="rumour-item-content">
                <view class="rumour-quantity"><span class="rumour-current-num">{{ currentIndex+1 }}</span><span class="rumour-sum">/{{ rumourDetailList.length }}</span></view>
                <view class="rumour-answer-time" v-if="currentQuestion.answered == 1 && !isLastQuestion">
                  <view class="count-down-answer">
                    <view class="topic">下一题</view>
                    <view class="num">{{ currentTime }}</view>
                  </view>
                </view>
                <view class="item-img" v-if="currentQuestion.answered == 1">
                  <image v-if="currentQuestion.answer == currentQuestion.userAnswer || currentQuestion.answerState == 1" :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-rumour-correct.png'" mode="aspectFill"></image>
                  <image v-else :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-rumour-error.png'" mode="aspectFill"></image>
                </view>
                <view class="item-title">{{currentQuestion.title}}</view>
                <view class="item-answer" v-if="currentQuestion.answered == 1">
                  <view class="item-answer-title">正确答案：<span>{{currentQuestion.answerText}}</span></view>
                  <view class="item-answer-content" :class="{lineclamp3:currentQuestion.isAll}">{{currentQuestion.answerContent}}</view> 
                  <view v-if="currentQuestion.isMore">
                    <span v-if="currentQuestion.isAll" @click="handleClickType">展开</span>
                    <span v-else @click="handleClickType">收起</span>
                  </view>
                  <view class="rumour-item-date" v-if="userAnswerTime">{{ userAnswerTime }}</view>
                </view>
                <view class="item-bott" v-else>
                  <view class="item-bott-l"><button class="real-btn-l" @click="handleLookAnswer(1,currentQuestion)">真的</button></view>
                  <view class="item-bott-r"><button class="real-btn-r" @click="handleLookAnswer(0,currentQuestion)">假的</button></view>
                </view>
              </view>
              <view class="rumour-item-bg"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-everyday-rumour-content-bg.png'"></image></view>
              <view class="more-list">
                <view class="more-list-item" v-for="moreItem in moreList" :key="moreItem.id" @click="handleClickMoreJump(moreItem)">
                  <image class="item-img" :src="file_ctx + moreItem.url"></image>
                  <view class="text">{{ moreItem.name }}</view>
                </view>
                <view class="divider"></view>
              </view>
            </view>
          </view>
          <view class="rumour-empty" style="margin:0 32rpx;" v-else>
            <image :src="'http://file.greenboniot.cn/static/image/system/invalid/icon-no-data.png'" class="empty-img"></image>
            <view>今日暂无辟谣</view>
          </view>
        </view>
      </view>
      <view 
        class="look-rumour-ranking" 
        v-if="myDataHeight" 
        :style="{top:(rumourDetailList[currentIndex] && rumourDetailList[currentIndex].answerContent.length) > 80 && rumourDetailList[currentIndex].answered == 1 ? myDataHeight + defaultHeight + 'px' : myDataHeight + (rumourDetailList[currentIndex].answered == 1 ? answerHeight : 204) + 'px'}" 
        @click="handleLookRankingList"
      >
        <view class="text">查看辟谣排行榜<image class="text-right" :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-rumour-right-new.png'"></image></view>
      </view>

      <!-- #ifdef MP-WEIXIN -->
      <view class="rumour-subscription" @click="handleClickSubscribe">
        <image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-everyday-rumour-subscription.png'"></image>订阅
      </view>
      <!-- #endif -->
      <view class="detail-bottom">
        <view class="detail-bottom-l" @click="handleClickAnswer">
          <view class="bottom-l-img" v-if="answerNum !== 0"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-everyday-rumour-share-active.png'"></image></view>
          <view class="bottom-l-img" v-else><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-everyday-rumour-share.png'"></image></view>
          <view class="text">海报分享</view>
        </view>
        <view class="detail-bottom-c"></view>
        <view class="detail-bottom-r">
          <view class="bottom-r-img"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-everyday-rumour-transpond.png'"></image></view>
          <!-- #ifdef MP-WEIXIN -->
          <button class="text" :plain="true" open-type="share">邀好友参与</button>
          <!-- #endif -->

          <!-- #ifdef H5 -->
          <button class="text" :plain="true" @click="handleClickShare">邀好友参与</button>
          <!-- #endif --> 
        </view>
      </view>
    </view>
    <!-- #endif -->
    <uni-popup class="sharePopup" ref="sharePopup" type="top">
      <view class="share-box" @click="handleClickShareClose">
        <image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-everyday-share-box.png'"></image>
      </view>
    </uni-popup>
    <uni-popup class="answerPopup" ref="answerPopup" type="center">
      <view class="answer-box-content">
        <view class="answer-box" v-for="(item,index) in popupParams" :key="item.id" :style="{'background-image':'url('+ file_ctx +`${item.imageUrl})`}">
          <view class="answer-header">
            <view class="answer-topic">共答对 <span> {{ answerRightNum || 0 }} </span> 题</view>
            <view class="answer-ranking">当前排名：第 {{ selfWeekObj.rank || 0 }} 名</view>
          </view>
          <view class="answer-btn" :style="{padding:index == 1 ? '0 32rpx' : '0 64rpx',width:index == 1 ? `calc(100% - ${64}rpx)` : `calc(100% - ${128}rpx)`}">
            <view 
              class="answer-btn-item" 
              v-for="(managementItem,itemIndex) in item.managementItemList" 
              :key="managementItem.id"
              :style="{width:index == 1 ? '256rpx' : '470rpx',marginLeft:itemIndex == 1 ? '22rpx' : ''}"
            >
              <image class="answer-img-btn" :src="managementItem.imageUrl"></image>
              <button @click="handleClickMoreJump(managementItem)"></button>
            </view>
          </view>
        </view>
        <view class="answer-popup-img" @click="handleClickClose"><image :src="file_ctx + 'static/image/business/hulu-v2/icon-home-popup-error-fault.png'"></image></view>
      </view>
    </uni-popup>
    
    <!-- 订阅消息弹窗 -->
    <uniPopup type="center" ref='subscriptionPopup' id="subscriptionPopup">
      <view class="my-subscription">
        <view class="header">
          <view class="title">已提交申请，扫码关注公众号</view>
          <view class="info">留意审核通知及发货提醒</view>
        </view>
        <view class="content"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/integral-subscribe-messages-bg.jpg'"></image></view>
        <view class="bottom-text">长按识别或者保存图片扫一扫</view>
      </view>
    </uniPopup>
  </view>
</template>

<script>
  import uniPopup from '@/components/uni/uni-popup'
  import { mapState } from 'vuex'
  export default {
    components: {
      uniPopup,
    },
    data(){
      return{
        $appId: this.$appId,
        file_ctx: this.file_ctx,
        $constant:this.$constant,
        rumourDetailList:[],
        $static_ctx: this.$static_ctx,
        isAll:false,
        isMore:false,
        statusBarHeight: 0,
        typeId:null,
        businessType:null,
        popupParams:[],
        moreList:[
          // {id:1,name:'领取好礼',url:'static/image/business/pharmacy-cyclopedia/icon-get-gift-new-two.png',jumpType:4,jumpUrl:'https://s.greenbonnet.cn/i/6bq2Er'},
          {id:2,name:'积分兑换',url:'static/image/business/pharmacy-cyclopedia/icon-integral-new.png',jumpType:2,jumpUrl:'integrationShop'},
          {id:3,name:'题目解析',url:'static/image/business/pharmacy-cyclopedia/history-topic-new.png',jumpType:2,jumpUrl:'HistoryTopicAnalysis'},
        ],
        // answerBtnList:[{id:1,name:'领取好礼',jumpType:2},{id:2,name:'查看排名',jumpType:1,jumpUrl:'RumourRankingList'}],
        myDataHeight:0,
        defaultHeight:225,
        answerHeight:209,
        ruleObj:null,
        currentTime:10, //倒计时下一题
        topicStartTime:0,//计时答题的时长 秒
        currentIndex: 0,
        isActive:false,//是否正在计时
        timer:null,
        timer2:null,
        answerRightNum:0,//每天答对正确数
        limitReached:false, // 是否达到题目上限（核心状态）
        loading:true,
        selfWeekObj:{},
        answerNum:0, //每天答题数
        userAnswerTime:'',//用户答题时间
        isShowAnswer:false, //是否点击过答题
      }
    },
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId,
        fansRecord: state => state.fansRecord,
        recordUserInfo: state => state.recordUserInfo, // 当前登录用户信息
        curSelectUserInfo: state => state.curSelectUserInfo,
        isLogin: state => state.isLogin
      }),
      currentQuestion() {
        return this.rumourDetailList[this.currentIndex]
      },
      isLastQuestion() {
        return this.currentIndex === this.rumourDetailList.length - 1
      }
    },
    onLoad(options){
      const query = this.$validate.isNull(this.$Route.query) ? options : this.$Route.query
      if(query?.afterPopId){
        this.typeId = query.afterPopId
      }
      this.refuterumorrecordAnswerNum() //每天答题数
      this.refuterumordetailDayDetail(true) //获取辟谣题目
      this.refuterumorrecordAnswerRightNum() //每天答题正确数
      this.refuterumorweekstatisticsQueryDefault()//辟谣规则

      // #ifdef H5
      this.wxh5ShareInit()
      this.pageexposurerecordInsert()
      // #endif

      const savedTime = uni.getStorageSync('examDuration');
      if (savedTime) {
        this.topicStartTime = parseInt(savedTime) || 0;
      }
    },
    onShow(){
      if(this.rumourDetailList.length && !this.timer && this.isShowAnswer){
        this.handleTopicCountDown()
      }
      // if(!this.isActive && this.rumourDetailList.length){
      //   console.log('进来了9999')
      //   // this.handleTopicStartTime()
      // }
    },
    onHide(){
      uni.removeStorageSync('examDuration')
      this.handleTopicPauseTime()
    },
    onUnload(){
      uni.removeStorageSync('examDuration')
      // #ifdef MP-WEIXIN
      this.handleClickTrack('EndOperationDetailsPageView')
      // #endif
      this.handleTopicPauseTime()
    },
    destroyed () {
      uni.removeStorageSync('examDuration')
      this.handleTopicPauseTime()
    },
    onShareAppMessage (res) {
      if (res.from === 'button') {
        // 来自页面内转发按钮
      }

      let params = {
        accountId:this.accountId, //账户id
        username:this.fansRecord.nickName, //用户昵称
        userType:1, //用户类型 1-活跃用户2-用户(马甲)
        refuteRumorDetailId:this.rumourDetailList[this.currentIndex]?.id || '', //辟谣详情主键id
        recordType:2, //操作类型 1-答题2-分享3-订阅
        answer:1, //答题 1真的 0假的
        title:this.rumourDetailList[this.currentIndex]?.title || '',
      }
      let title = `每日辟谣：${this.rumourDetailList[this.currentIndex]?.title || ''}`
      let imgUrl = this.rumourDetailList[this.currentIndex]?.shareImg && this.file_ctx + this.rumourDetailList[this.currentIndex].shareImg
      this.$api.drugBook.refuterumorrecordInsert(params,{'recordType':'2'})

      
      if(this.limitReached){
        title = '每日辟谣'
        imgUrl = ''
      }
      return {
        title: title || '每日辟谣', //分享的名称
        path: 'modules/pharmacy/pharmacy-cyclopedia/everyday-rumour/index',
        mpId:this.$appId, //此处配置微信小程序的AppId
        // imageUrl: this.rumourDetailList?.shareImg ? this.file_ctx + this.rumourDetailList.shareImg : this.getRandomImage(),
        imageUrl: imgUrl,
      }
    },
    onShareTimeline(){
      return {
        title: '家人朋友群里的消息总是真假难辨？只需1分钟，轻松辨别!',
        path: 'modules/pharmacy/pharmacy-cyclopedia/everyday-rumour/index',
      }
    },
    mounted(){
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度

      // #ifdef MP-WEIXIN
      this.handleClickTrack('OperationDetailsPageView')
      // #endif
    },
    methods:{
      //每天答题数
      async refuterumorrecordAnswerNum(){
        const res = await this.$api.drugBook.refuterumorrecordAnswerNum({accountId:this.accountId})
        this.answerNum = res.data
        if(res.data >= 3){
          this.limitReached = true
        } else {
          this.limitReached = false
        }
      },

      // 本人所在排行榜
      async refuterumorweekstatisticsQueryRank(){
        let params = {
          accountId:this.accountId,
          thisWeek:true, // true本周 false 上周
        }
        const res = await this.$api.drugBook.refuterumorweekstatisticsQueryRank(params)
        this.selfWeekObj = res.data
      },

      // 题目答题后的倒计时 默认10秒
      handleTopicCountDown(time = 10){
        this.currentTime = time
        this.timer = setInterval(()=>{
          if(this.currentTime >= 0){
            this.currentTime == 0 ? this.nextQuestion() : this.currentTime--          
          }
        },1000)
      },

      // 倒计时时间到了切换下一题
      nextQuestion(){
        if(!this.isLastQuestion){
          this.handleTopicPauseTime() //清除当前的定时器、计时答题的时长
          // this.currentTime = 10
          this.currentIndex++
          this.$nextTick(()=>{
            this.getElementHeight()
          })
          this.handleTopicStartTime() //切到下一题 再次开启答题时长
          this.isShowAnswer = false
        } else {
          this.handleTopicPauseTime()
          uni.removeStorageSync('examDuration')
        }
      },

      // 题目刚开始计时答题的时长
      handleTopicStartTime(){
        this.isActive = true
        this.timer2 = setInterval(()=>{
          this.topicStartTime++
          uni.setStorageSync('examDuration', this.topicStartTime)
          // console.log(this.topicStartTime,'this.topicStartTime0000')
        },1000)
      },

      // 清除定时器
      handleTopicPauseTime(){
        this.isActive = false
        if (this.timer) {
          clearInterval(this.timer)
          this.currentTime = 10
          this.timer = null
        }
        if (this.timer2) {
          clearInterval(this.timer2)
          this.topicStartTime = 0
          this.timer2 = null
        }
      },

      // 关闭答题后的答对弹窗
      async handleClickClose(){
        await this.$api.drugBook.advertisementmanagementrecordInsert({accountId:this.accountId,advertisementId:this.popupParams[0].id,businessType: 3}) //businessType: 3,  //默认传3(关闭)
        this.$refs.answerPopup.close()
      },

      async refuterumorweekstatisticsQueryDefault(){
        const res = await this.$api.drugBook.refuterumorweekstatisticsQueryDefault({})
        this.ruleObj = res.data
      },
      handleClickListRule(){
        this.$navto.push('RumourRule')
      },
      // 查看更多排行榜单
      handleLookRankingList(){
        if(!this.isLogin){
          this.$navto.push('Login',{formPage: 'RumourRankingList'})
        } else {
          this.$navto.push('RumourRankingList')
        }
      },
      
      handleClickMoreJump(item){
        switch (item.jumpType) {
          case 2:
            if((item.id == 2 || item.id == 3) && !this.isLogin){
              this.$navto.push('Login',{formPage: item.jumpUrl})
            } else {
              this.$navto.push(item.jumpUrl)
            }
            break;
          case 3:
            this.$navto.push('WebHtmlView', { src: item?.jumpUrl, title: '' })
            break;
          case 4:
            if(this.ruleObj?.jumpType == 2){
              this.$navto.push(this.ruleObj?.jumpUrl)
            } else{
              this.$navto.push('WebHtmlView', { src: this.ruleObj?.jumpUrl, title: '' })
            }
            break;
        }
      },
      getElementHeight() {
        let query = uni.createSelectorQuery().in(this);
        query.select('.rumour-list').boundingClientRect(data => {
          if (data) {
            this.myDataHeight = data.height
          }
        }).exec();

        query.select('.upper-limit-rumour-list').boundingClientRect(data => {
          if (data) {
            this.myDataHeight = data.height
          }
        }).exec();
        
      },
      // 按钮点击
      async handleClickBtn(type){
        this.handleClickCommonPort(type)
      },
      async handleClickCommonPort(type){
        let parmas = {
          imageUrl:this.file_ctx + 'static/image/business/pharmacy-cyclopedia/rumour-poster.png',
          accountId:this.accountId,
          businessType:this.$constant.drugBook.businessTypeObj.h5RumourTypePage,
        }
        if(type == 1){
          await this.$api.drugBook.visitpagerecordInsert(parmas)
        } else {
          await this.$api.drugBook.imageoperationrecordInsert(parmas)
        }
      },
      // #ifdef H5
      async pageexposurerecordInsert(){
        let parmas = {
          imageUrl:this.file_ctx + 'static/image/business/pharmacy-cyclopedia/rumour-poster.png',
          accountId:this.accountId,
          businessType:this.$constant.drugBook.businessTypeObj.h5RumourTypePage,
        }
        await this.$api.drugBook.pageexposurerecordInsert(parmas)
      },
      // #endif

      async advertisementmanagementValidPopAccount(id){
        const res = await this.$api.drugBook.advertisementmanagementListValidPopAccount({accountId:this.accountId,useType:2,adsId:id || ''})
        this.popupParams = res.data
        if(this.popupParams?.length){
          this.$nextTick(()=>{
            this.$refs.answerPopup?.open()
            this.advertisementmanagementrecordInsert()
          })

          //神策埋点
          // #ifdef MP-WEIXIN
          this.handleClickTrackPopup(1)
          // #endif
        }
      },

      // #ifdef MP-WEIXIN
      handleClickTrack(type){
        let pages = getCurrentPages()
        let current = pages[pages.length - 1] // 获取到当前页面的信息
        let pageInfo = __wxConfig.page && __wxConfig.page[`${current.route}.html`] || ''; // 内部属性
        getApp().globalData.sensors.track(type,
          {
            'page_name' : pageInfo?.window?.navigationBarTitleText || '',
            'first_operation_name' : pageInfo?.window?.navigationBarTitleText || '',
            'second_operation_name' : '',
          }
        )
      },
      // #endif

      // #ifdef MP-WEIXIN
      handleClickTrackPopup(type){
        getApp().globalData.sensors.track("PopupClick",
          {
            'page_name' : '每日辟谣',
            'popup_id' : 'homePopup',
            'popup_name' : '答完题后弹窗',
            'click_type' : type == 1 ? '进入弹窗' : '取消弹窗',
          }
        )
      },
      // #endif

      handleHomePopupClose(){
        this.$refs.homePopup.$refs.carouselPopup.close()
        // #ifdef MP-WEIXIN
        this.handleClickTrack(2)
        // #endif
      },
      handleClickShareClose(){
        this.$refs.sharePopup.close()
      },
      handleClickShare(){
        this.$refs.sharePopup.open()
      },
      handleClickAnswer(){
        if(this.answerNum !==0){
          this.$navto.push('HistoryTopicAnalysis')
        } else {
          // this.$navto.push('EverydayMorePoster',{id:this.rumourDetailList[0].id})
          this.$uniPlugin.toast('请先答题再分享哦！')
        }
      },
      handleBack(){
        this.$navto.back(1)
      },

      handleClickType(){
        this.currentQuestion.isAll = !this.currentQuestion.isAll
        this.$nextTick(()=>{
          this.getElementHeight()
        })
      },
      // 订阅辟谣
      async handleClickSubscribe(){
        let that = this
        const { centerUserId = '' } = this.curSelectUserInfo || {}
        // 获取openid
        let openId = await this.$ext.wechat.getOpenId()
        await this.$uniPlugin.subscribeMessage(['MBqanA4deXXeb6hhTnpuuS0iaKN0Hkr6USG8DNSPOkg'])
        const tmplId = 'MBqanA4deXXeb6hhTnpuuS0iaKN0Hkr6USG8DNSPOkg'
        uni.requestSubscribeMessage({
          tmplIds: [tmplId],
          success:async(res)=>{
            if (res[tmplId] === 'accept') {
              // 执行订阅后的逻辑
              let logParamList = {
                appId: that.$appId,
                templateId: tmplId,
                openId: openId,
                subscribeStatus: res[tmplId],
                businessType : 8,
                accountId: that.accountId,
                userId: centerUserId
              }
              await that.$api.common.wxsubscribemessagelogInsertBatch({wxSubscribeMessageLogList:logParamList})
              let params = {
                accountId:that.accountId, //账户id
                username:that.fansRecord.nickName, //用户昵称
                userType:1, //用户类型 1-活跃用户2-用户(马甲)
                recordType:3, //操作类型 1-答题2-分享3-订阅
                answer:1, //答题 1真的 0假的
              }
              await that.$api.drugBook.refuterumorrecordInsert(params,{'recordType':'3'})
              await that.$api.drugBook.refuterumorsubscribeInsert({businessType:1,accountId:that.accountId})
              // #ifdef MP-WEIXIN
              getApp().globalData.sensors.track("Subscription",
                {
                  'content_belong_circle' : '',
                  'function_name' : '每日答题活动通知',
                  'subscription_type':'一次性订阅'
                }
              )
              // #endif
              this.$uniPlugin.toast('消息订阅成功')
            } else {
              this.$uniPlugin.toast('消息订阅失败')
              // 处理用户拒绝的情况
            }
          },
          fail(err) {
            console.error('请求订阅消息失败：', err);
          }
        });
      },

      // 辟谣弹窗曝光量
      async advertisementmanagementrecordInsert(){
        await this.$api.drugBook.advertisementmanagementrecordInsert({accountId:this.accountId,advertisementId:this.popupParams[0].id,businessType: 1}) //businessType: 3,  //默认传3(关闭)
      },

      async handleLookAnswer(type,item){
        let that = this
        let typeText = item.answer == type ? '恭喜你，答对了' : '很遗憾，答错了'
        this.isShowAnswer = true //是否点击过答题
        item.userAnswer = type
        that.$uniPlugin.modal('提示', typeText, {
          showCancel: false, // 是否显示取消按钮，默认为 true
          cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
          cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
          confirmText: '我知道了', //  确定按钮的文字，默认为"确定"，最多 4 个字符
          confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
          fn: (n) => {
            if (n) {
              let params = {
                accountId:that.accountId, //账户id
                username:that.fansRecord.nickName, //用户昵称
                userType:1, //用户类型 1-活跃用户2-用户(马甲)
                refuteRumorDetailId:item.id, //辟谣详情主键id
                recordType:1, //操作类型 1-答题2-分享3-订阅
                answer:type, //答题 1真的 0假的
                answerTime:that.topicStartTime, //答题时间，秒
                businessType:1, // 业务类型：1-每日辟谣 2-辟谣签到
              }
              // const res = await that.$api.drugBook.refuterumorrecordInsert(params)
              that.$api.drugBook.refuterumorrecordInsert(params,{'recordType':'1'}).then(res=>{
                if(res.data !== ""){
                  item.answered = 1
                  that.userAnswerTime = res.data.createTime ? that.$common.formatDate(new Date(res.data.createTime),'yyyy-MM-dd').replace(/-/g, '/'):''
                  if(that.isLastQuestion){ //最后一题显示 答对数和弹窗
                    that.nextQuestion()
                    that.refuterumorrecordAnswerRightNum() //查询答对题数
                    that.refuterumorweekstatisticsQueryRank() //查询当前排名
                    that.refuterumordetailDayDetail(false) //查询是否还有题目
  
  
  
                    if(!!that.typeId){
                      that.advertisementmanagementValidPopAccount(that.typeId)
  
                      // #ifdef MP-WEIXIN
                      that.handleClickTrack(1)
                      // #endif
                    } else {
                      that.advertisementmanagementValidPopAccount()
  
                      // #ifdef MP-WEIXIN
                      that.handleClickTrack(1)
                      // #endif
                    }
  
  
                  } else {
                    that.refuterumorrecordAnswerNum() //更新答题数
                    that.handleTopicCountDown(that.currentTime) //答题完显示倒计时
                    this.$nextTick(()=>{
                      this.getElementHeight()
                    })
                  }
                }
              })
            }
          }
        })

      },
      // 每天答对正确数
      async refuterumorrecordAnswerRightNum(){
        const res = await this.$api.drugBook.refuterumorrecordAnswerRightNum({accountId:this.accountId})
        if(res.data !==0){
          this.answerRightNum = res.data
        }
      },
      // 获取每日辟谣题目
      async refuterumordetailDayDetail(loading = false){
        let that = this
        let params = {
          accountId:that.accountId,
        }
        that.loading = loading
        const res = await that.$api.drugBook.refuterumordetailDayDetail(params)
        if(res.data?.length){
          that.limitReached = false
          that.loading = false
          that.rumourDetailList = res.data.map(item=>{
            return {
              ...item,
              isMore:item.answerContent.length > 80 ?  true : false,
              isAll:item.answerContent.length > 80 ?  true : false,
              answerDateText:item.answerDate?that.$common.formatDate(new Date(item.answerDate),'yyyy-MM-dd').replace(/-/g, '/'):'',
              answerText:item.answer == 0 ? '假的' : '真的',
              userAnswer:null
            }
          })
          that.handleTopicStartTime() //有题目开始计时答题时长
        } else {
          that.loading = false
          that.refuterumorrecordAnswerNum()
          that.$set(that,'rumourDetailList',res.data)
        }

        if(loading){
          that.$nextTick(()=>{
            that.getElementHeight() //有题目了显示排行榜
          })
        }
      },
    },
 }
</script>

<style lang='scss' scoped>
  @mixin contentFlex {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .my-subscription{
    width: 680rpx;
    padding: 60rpx 20rpx 10rpx;
    background-color: #fff;
    border-radius: 15rpx;
    .header{
      display: flex;
      flex-direction: column;
      align-items: center;
     .title{
      
     } 
     .info{

     }
    }
    .content{
      display: flex;
      width: 258rpx;
      height: 258rpx;
      margin: 30rpx auto 20rpx;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .bottom-text{
      font-size: 12px;
      color:darkgray;
      text-align: center;
    }
  }
  .main{
    position: relative;
    height: 100vh;
    overflow-y: auto;
    background: #BFEADE;
  }
  /* #ifdef H5 */
  .poster-content{
    position: relative;
    width: 750rpx;
    height: 1292rpx;
    overflow: hidden;
  }
  /* #endif */
  .my-data{
    height: 732rpx;
    width: 100%;
  }
  .top-nav{
    position: relative;
    @include contentFlex;
    width: calc(100% - 56rpx);
    height: 40px;
    line-height: 40px;
    padding: 0 32rpx 0 24rpx;
    .top-nav-l{
      @include contentFlex;
      position: absolute;
      height: 40px;
      left: 24rpx;
      top: 0;
      .header-search-img{
        display: flex;
        width: 48rpx;
        height: 48rpx;
        margin-right: 24rpx;
      }
      .rule{
        font-size: 28rpx;
        color: #1D2029;
        line-height: 40rpx;
        background: rgba(255,255,255,0.3);
        border-radius: 32rpx;
        padding: 12rpx 24rpx;
      }
    }
    .top-nav-c{
      @include contentFlex;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      top: 0;
      height: 32px;
      font-weight: 500;
      font-size: 32rpx;
      color: #1D2029;
      margin-right: 48rpx;
    }
  }
  .rumour-box{
    position: absolute;
    // top: 388rpx;
    top:194px;
    left: 0;
    width: 100%;
    .rumour-list,.upper-limit-rumour-list{
      position: relative;
      width: calc(100% - 64rpx);
      margin: 0 32rpx;
      background: #FFFFFF;
      box-shadow: 0rpx 4rpx 16rpx 0rpx #B8E2D6;
      border-radius: 32rpx;
      padding: 72rpx 0rpx 32rpx;
      .rumour-item{
        // position: relative;
        .rumour-item-date{
          color:#000;
        }
        .rumour-item-content{
          // position: relative;
          padding: 0rpx 32rpx;
          border-radius: 16rpx;
          background-color: #fff;
          .rumour-quantity{
            position: absolute;
            top: 10rpx;
            left: 20rpx;
            display: flex;
            align-items: center;
            .rumour-current-num{
              font-weight: 600;
              font-size: 30rpx;
              color: #1D2029;
              line-height: 42rpx;
              margin-right: 4rpx; 
            }
            .rumour-sum{
              font-size: 20rpx;
              color: #868C9C;
            }
          }
          .rumour-answer-time{
            position: absolute;
            top: -36rpx;
            left: 50%;
            width: 184rpx;
            height: 122rpx;
            transform: translateX(-50%);
            background-image: url($imgUrl + '/business/pharmacy-cyclopedia/icon-rumour-answer-time.png');
            background-size: 100%;
            .count-down-answer{
              position: absolute;
              left: 50%;
              top: 12rpx;
              transform: translateX(-50%);
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              width: 96rpx;
              height: 96rpx;
              border: 6rpx solid #BFEADE;
              border-radius: 50%;
              .topic{
                font-size: 20rpx;
                color: #4E5569;
              }
              .num{
                font-size: 40rpx;
                color: #1D2029;
                line-height: 56rpx;
              }
            }
          }
          .item-img{
            position: absolute;
            right: -26rpx;
            top: -42rpx;
            display: flex;
            width: 174rpx;
            height: 136rpx;
            image{
              width: 100%;
              height: 100%;
            }
          }
          .item-title{
            @include contentFlex;
            font-size: 36rpx;
            font-weight: 600;
            color:#000;
            margin: 30rpx 0 32rpx;
            line-height: 50rpx;
          }
          .item-answer{
            .item-answer-title{
              display: flex;
              justify-content: center;
              font-weight: 600;
              font-size: 30rpx;
              color: #4E5569;
              margin-bottom: 28rpx;
              span{
                font-size: 30rpx;
                color: #00B484;
              }
            }
            .item-answer-content{
              line-height: 50rpx;
              word-wrap: break-word;
              max-height: 240rpx;
              overflow-y:auto;
            }
            .rumour-item-date{
              color:#999;
              margin: 24rpx 0 30rpx;
            }
            .lineclamp3{
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 3;
              -webkit-box-orient: vertical;
              white-space: normal;
            }
          }
          .item-bott{
            .item-bott-l,.item-bott-r{
              button{
                @include contentFlex;
                height: 88rpx;
                background: #F4F6FA;
                border-radius: 44rpx;
                font-size: 30rpx;
                color: #1D2029;
                &::after{
                  border: none !important;
                }
              }
            }
            .item-bott-r{
              margin: 32rpx 0 64rpx;
            }
          }
          .common-answer{
            display: flex;
            width: 112rpx;
            height: 132rpx;
            margin: 40rpx 0 22rpx;
            image{
              width: 100%;
              height: 100%;
            }
          }
          .common-answer-title{
            font-weight: 600;
            font-size: 32rpx;
            color: #1D2029;
            span{
              font-size: 40rpx;
              color: #00B484;
            }
          }
          .upper-limit{
            font-size: 26rpx;
            color: #4E5569;
            line-height: 36rpx;
          }
          .look-my-rumour-btn{
            width: 502rpx;
            height: 80rpx;
            background: #EBF7F4;
            border-radius: 48rpx;
            font-size: 30rpx;
            color: #00B484;           
            &::after{
              border: none !important;
            }
          }
        }
        .rumour-item-bg{
          display: flex;
          width: 100%;
          height: 40rpx;
          margin-bottom: 15rpx;
          image{
            width: 100%;
            height: 100%;
          }
        }
        .more-list{
          position: relative;
          display: flex;
          height: 56rpx;
          align-items: center;
          justify-content: space-around;
          .more-list-item{
            display: flex;
            align-items: center;
            flex: 1;
            justify-content: center;
            // flex-direction: column;
            // border-right: 2rpx solid #EAEBF0;
            // padding: 0 34rpx 0 32rpx;
            // &:last-child{
            //   border-right: none;
            // }
            .item-img{
              width: 40rpx;
              height: 40rpx;
            }
            .text{
              font-size: 28rpx;
              color: #1D2029;
              margin-left: 4rpx;
            }
          }
          .divider{
            position: absolute;
            left: 50%; /* 定位到正中间 */
            top: 50%;
            transform: translate(-50%, -50%); /* 精确居中 */
            width: 2rpx;
            height: 56rpx; /* 竖线高度 */
            background-color: #EAEBF0;
          }
        }
      }
    }
    .upper-limit-rumour-list{
      position: relative;
      .rumour-item-content{
        margin-top: -30rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        .common-answer{
          margin: 0 !important;
        }
        .common-answer-title{
          line-height: 56rpx;
          margin: 16rpx 0 8rpx;
        }
        .look-my-rumour-btn{
          margin: 32rpx 0 38rpx;
        }
      }
      .rumour-item-bg{
        margin-bottom: 24rpx;
      }
    }
    .rumour-empty{
      @include contentFlex;
      flex-direction: column;
      height: 500rpx;
      font-size: 30rpx;
      background-color: #fff;
      border-radius: 16rpx;
      image{
        height: 200rpx;
        width: 300rpx;
        margin-bottom: 20rpx;
      }
    }
    .rumour-get-gift{
      @include contentFlex;
      width: calc(100% - 64rpx);
      margin: 30rpx 32rpx 0;
      height: 88rpx;
      background: #FFFFFF;
      border-radius: 32rpx;
      .gift-l{
        display: flex;
        width: 42rpx;
        height: 42rpx;
        margin-right: 12rpx;
      }
      span{
        font-size: 28rpx;
        color: #1D2029;
      }
      .gift-r{
        display: flex;
        width: 32rpx;
        height: 32rpx;
      }
    }
  }
  .look-rumour-ranking{
    display: flex;
    align-items: center;
    position: absolute;
    left: 32rpx;
    width: 686rpx;
    height: 104rpx;
    background-image: url($imgUrl + '/business/pharmacy-cyclopedia/icon-look-rankinglist-new.png');
    background-size: 100%;
    .text{
      display: flex;
      align-items: center;
      padding-left: 32rpx;
      font-size: 32rpx;
      color: #1D2029;
      font-weight: 600;
      .text-right{
        display: flex;
        width: 16rpx;
        height: 28rpx;
        margin: 4rpx 0 0 10rpx;
      }
    }
  }
  .rumour-subscription{
    @include contentFlex;
    position: absolute;
    bottom: 190rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 290rpx;
    height: 96rpx;
    background: #00B484;
    border-radius: 48rpx;
    font-size: 32rpx;
    color: #FFFFFF;
    image{
      width: 48rpx;
      height: 48rpx;
    }
  }
  .detail-bottom{
    display: flex;
    justify-content: space-evenly;
    position: fixed;
    width: 100%;
    bottom: 0;
    left: 0;
    height: 156rpx;
    background-color: #fff;
    border-radius: 32rpx 32rpx 0rpx 0rpx;
    .detail-bottom-l,.detail-bottom-r{
      display: flex;
      padding-top: 28rpx;
      font-size: 28rpx;
      color: #1D2029;
      .bottom-l-img,.bottom-r-img{
        display: flex;
        width: 40rpx;
        height: 40rpx;
        margin-right: 8rpx;
        image{
          width: 100%;
          height: 100%;
        }
      }
      .bottom-r{
        width: 42rpx;
      }
    }
    .detail-bottom-r{
      button{
        display: flex;
        font-size: 28rpx;
        color: #1D2029;
        padding: 0;
        border: none;
        margin: 0;
        margin-top: -13rpx;
        &::after{
          border: none !important;
        }
      }
    }
    .detail-bottom-c{
      width: 2rpx;
      height: 56rpx;
      background: #EAEBF0;
      margin-top: 16rpx;
    }
  }
  .share-box{
    display: flex;
    width: 670rpx;
    height: 252rpx;
    margin-left: auto;
    margin-right: 20rpx;
    image{
      width: 100%;
      height: 100%;
    }
  }
  .answer-box-content{
    .answer-box{
      position: relative;
      width: 598rpx;
      height: 520rpx;
      background-size: 100%;
      .answer-header{
        position: absolute;
        top: 208rpx;
        left: 0;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        .answer-topic{
          display: flex;
          align-items: center;
          font-size: 32rpx;
          font-weight: 600;
          color: #1D2029;
          line-height: 44rpx;
          span{
            font-weight: 600;
            font-size: 40rpx;
            color: #00B484;
            line-height: 56rpx;
          }
        }
        .answer-ranking{
          @include contentFlex;
          width: 250rpx;
          height: 52rpx;
          background: #F4F6FA;
          border-radius: 8rpx;
          font-size: 26rpx;
          color: #4E5569;
          line-height: 36rpx;
          margin-top: 12px;
        }
      }
      .answer-btn{
        position: absolute;
        bottom: 56rpx;
        display: flex;
        padding: 0 64rpx;
        .answer-btn-item{
          position: relative;
          height: 88rpx;
          .answer-img-btn{
            height: 100%;
            width: 100%;
          }
          button{
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            border-radius: 45rpx;
            opacity: 0;
          }
        }
      }
    }
    .answer-popup-img{
      display: flex;
      width: 40rpx;
      height: 40rpx;
      margin: 32rpx auto 0;
      image{
        width: 100%;
        height: 100%;
      }
    }
  }
</style>
