<template>
  <view class="winRecordBox">
    <view :style="{transform: `translateY(-${TotalRunStep}px)`}">
      <view class="winRecordScroll" :style="{transform: `translateY(${runStepList[0]}px)`}">
        <view class="winRecordItem" v-for="(item, index) in winRecordList" :key="index">
          <view class="winRecordTitle">{{item.name}}</view>
          <view class="winRecordContent">{{item.content}}</view>
        </view>
      </view>
      <view class="winRecordScroll" :style="{transform: `translateY(${runStepList[1]}px)`}">
        <view class="winRecordItem" v-for="(item, index) in winRecordList" :key="index">
          <view class="winRecordTitle">{{item.name}}</view>
          <view class="winRecordContent">{{item.content}}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    name: '',
    props:{
      winRecordList:{
        type:Array,
        default:()=>[]
      }
    },
    data() {
      return {
        runStep:0,
        TotalRunStep:0,
        runStepList:[0,0],
        scrollHeight:0,
        currentItem:0,
        isFirstRun:0,
        timeEr:null,
      }
    },
    methods: {
      async getWinRecordScrollHeight(){
        let resFn;
        let pro = new Promise(res=>resFn = res)
        // 先获取winRecordScroll盒子的高度
        uni.createSelectorQuery().in(this).select('.winRecordScroll').boundingClientRect().exec(([winRecordScroll])=>{
          // 向上取整避免小数干扰
          this.scrollHeight = Math.ceil(winRecordScroll.height)
          resFn()
        })
        return pro;
      },
      runAnimation(){
        let step = 1;
        this.TotalRunStep+=step;
        if(this.TotalRunStep && this.TotalRunStep % this.scrollHeight < step){
          this.runStepList[this.currentItem] += this.scrollHeight * 2;
          this.isFirstRun++;
          this.currentItem = this.currentItem ? 0 : 1;
        }
      },
    },
    async created() {
      // 先获取winRecordScroll盒子的高度在uniapp环境下
      await this.getWinRecordScrollHeight()
      if(this.timeEr) clearInterval(this.timeEr)
      this.timeEr = null;
      // 再运行动画
      this.timeEr = setInterval(()=>{
        this.runAnimation()
      },38)
    },
    destroyed(){
      clearInterval(this.timeEr)
    },
  }
</script>

<style scoped lang="scss">
.winRecordBox{
  width: 100%;
  height: 100%;
  overflow: hidden;
    /* 硬件加速优化 */
  transform: translateZ(0);
  will-change: transform;
  .winRecordScroll{
      /* 硬件加速优化 */
    transform: translateZ(0);
    will-change: transform;
    overflow: hidden;
    &:nth-of-type(1){
      &:first-child{
        margin-top: 15rpx;
      }
    }
    &:nth-of-type(2){
      &:last-child{
        margin-top: 0rpx;
      }
    }
  }
  .winRecordItem{
    display: flex;
    justify-content: space-between;
    font-weight: 500;
    font-size: 28rpx;
    color: #777777;
    margin-bottom: 15rpx;
    
  }
}
</style>