<template>
  <view style="height: 100vh; display: flex; flex-direction: column;">


    <!-- 导航栏 - 只在接单和服务页面显示 -->
    <uni-nav-bar
      v-if="navCurrent == 0 || navCurrent == 1"
      color="#1d2029"
      :border="false"
      :showBtnsRight='false'
      :fixed="false"
      statusBar
      @clickLeft="back"
      left-icon="left"
      left-width="48rpx"
      right-width="100px"
      backgroundColor="#00B484"
    >
    <view class="avatarBox">
      <image class="avatarHeader" :src="file_ctx + userInfo.avatar" mode="aspectFill"></image>
      <view class="avatarTitle">{{userInfo.username}}</view>
    </view>
    </uni-nav-bar>

    <!-- 内容区域 -->
    <view style="flex: 1; overflow: hidden;">
      <accompanyOrderList :userInfo='userInfo' v-if="navCurrent == 0"></accompanyOrderList>
      <accompanyServerList :userInfo='userInfo' v-if="navCurrent === 1"></accompanyServerList>

    </view>
    <view class="bottomTab">
      <!-- <view class="tabIcon" :class="{selectTab:tabIndex === 0}" @click="tabIndex = 0">
        <image class="tabIconImg" :src="returnsTabImage('order',tabIndex)" mode="aspectFill"></image>
        <view class="">接单</view>
      </view>
      <view class="tabIcon" :class="{selectTab:tabIndex === 1}" @click="tabIndex = 1">
        <image class="tabIconImg" :src="returnsTabImage('server',tabIndex)" mode="aspectFill"></image>
        <view class="">服务</view>
      </view> -->
      <view class="bottom-item" v-for="(item,index) in navList" :key="index" @tap="handletapJump(item,index)">
        <view class="bottom-item-img" v-if="navCurrent == index"><image class="img" :src="file_ctx + item.activeUrl"></image></view>
        <view class="bottom-item-img" v-else><image class="img" :src="file_ctx + item.url"></image></view>
        <view :class="navCurrent == index ? 'bottom-item-name active' : 'bottom-item-name'">{{item.name}}</view>
      </view>
    </view>
  </view>
</template>

<script>
  import common from '@/common/util/main'
  import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'
  import accompanyOrderList from '../../components/accompanyOrderList.vue'
  import accompanyServerList from '../../components/accompanyServerList.vue'


  export default{
    components: {
      uniNavBar,
      accompanyOrderList,
      accompanyServerList,
    },
    data(){
      return {
        file_ctx: this.file_ctx,
        serverNoSelect: this.$static_ctx + "image/business/hulu-v2/serverNoSelect.png",
        serverSelect: this.$static_ctx + "image/business/hulu-v2/serverSelect.png",
        orderNoSelect: this.$static_ctx + "image/business/hulu-v2/orderNoSelect.png",
        orderSelect: this.$static_ctx + "image/business/hulu-v2/orderSelect.png",
        userInfo:{},
        tabIndex:0,
        beforTabIndex:0,
        navList:[
          {name:'接单',url:'static/image/business/accompany-doctor/icon-order-receiving.png',activeUrl:'static/image/business/accompany-doctor/icon-order-receiving-active.png'},
          {name:'服务',url:'static/image/business/accompany-doctor/icon-accompany-bottom-service.png',activeUrl:'static/image/business/accompany-doctor/icon-accompany-bottom-service-active.png'},
          // {name:'社区',routerName:'accompanyOrder',url:'static/image/business/accompany-doctor/icon-community.png',activeUrl:'static/image/business/accompany-doctor/icon-community-active.png'},
          {name:'课程',url:'static/image/business/accompany-doctor/icon-course.png',activeUrl:'static/image/business/accompany-doctor/icon-course-active.png'},
          {name:'我的',url:'static/image/business/accompany-doctor/icon-my.png',activeUrl:'static/image/business/accompany-doctor/icon-my-active.png'}
        ],
        navCurrent:0,
      }
    },
    onHide() {
      this.beforTabIndex = this.tabIndex;
      this.tabIndex = 2;
    },
    onShow() {
      this.tabIndex = this.beforTabIndex;
    },
    onLoad() {
      this.getAccompanyInfo();
      console.log('触发加载');
    },
    methods:{
      handletapJump(item,index){
        // 如果是课程或我的，直接跳转到对应页面
        if(index === 2) {
          // 跳转到课程页面
          this.$navto.push('CourseIndex')
          return
        }
        if(index === 3) {
          // 跳转到我的课程页面
          this.$navto.push('CourseMy')
          return
        }
        // 其他情况正常切换
        this.navCurrent = index
      },

      returnsTabImage(tabType,index){
        if(tabType === 'order'){
          return index === 0 ? this.orderSelect : this.orderNoSelect
        }
        if(tabType === 'server'){
          return index === 1 ? this.serverSelect : this.serverNoSelect
        }
      },
      back() {
        let pages = getCurrentPages() // 获取栈实例
        if (pages.length > 1) {
          this.$navto.back()
        } else {
          this.$navto.replaceAll('Index')
        }
      },
      async getAccompanyInfo(){
        const codeUserInfo = common.getKeyVal('user', 'codeUserInfo',true);
        let queryOptions = {userId:codeUserInfo.id};
        let {data} = await this.$api.accompanyDoctor.getAccompanyemployeeOneByUserId(queryOptions)
        this.userInfo = data;
        console.log('data',data);
        if(!data){
          setTimeout(()=>{
            uni.navigateBack()
          }, 2000);
        }
      }
    }
  }
</script>

<style lang="scss">
  .avatarBox{
    display: flex;
    align-items: center;
    .avatarHeader{
      width: 64rpx;
      height: 64rpx;
      border-radius: 50%;
    }
    .avatarTitle{
      font-weight: 500;
      font-size: 32rpx;
      color: #FFFFFF;
      margin-left: 12rpx;
    }
  }
  .img{
    width: 100%;
    height: 100%;
  }
  .bottomTab{
    height: 140rpx;
    width: 100vw;
    position: fixed;
    bottom: 0;
    left: 0;
    background-color: white;
    display: flex;
    justify-content: space-around;
    .bottom-item{
      display: flex;
      flex-direction: column;
      align-items: center;
      // justify-content: center;
      padding-top: 8rpx;
      .bottom-item-img{
        width: 56rpx;
        height: 56rpx;
        margin-bottom: 4rpx;
      }
      .bottom-item-name{
        font-size: 20rpx;
        color: #868C9C;
      }
      .active{
        color: #00B484;
      }
    }
    // .tabIcon{
    //   font-weight: 400;
    //   font-size: 20rpx;
    //   color: #868C9C;
    //   .tabIconImg{
    //     width: 56rpx;
    //     height: 56rpx;
    //   }
    // }
    // .selectTab{
    //   color: #00B484;
    // }
  }

</style>
