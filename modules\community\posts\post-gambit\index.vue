<template>
  <view class='post-gambit'>
    <view class="text-list" v-if="gambitList.length">
      <view class="list-item" v-for="(item,index) in gambitList" :key="item.id" @click="handleItemDelete(index)">
        # {{ item.name }}<uni-icons class="my-icon" type="closeempty" size="12"></uni-icons>
      </view>
    </view>
    <textarea
      :style="{'height':gambitList.length ? '150rpx' : '350rpx'}"
      class="textarea"
      v-model="gambitText"
      placeholder="选择或输入话题，最多添加5个"
      :focus="autofocus"
      :show-confirm-bar="false"
      @blur="handleBlur"
      @input="handleInput"
    ></textarea>
    <view class="line"></view>
    <view class="gambit-list">
      <view class="title">话题列表</view>
        <scroll-view 
          class="my-scroll-view"
          scroll-y
        >
        <view class="keyboard-content">
          <template v-if="keyList.length">
            <view class="keyboard-list" v-for="item in keyList" :key="item.id" @tap="handleClickItem(item)">
              <view class="list-l"># {{ item.name }}</view>
              <!-- <view class="list-r">{{ item.preview }}浏览</view> -->
            </view>
          </template>
          <view v-else class="keyboard-list" @tap="handleClickAddNew">
            <view class="list-l" ># {{ lastVal.length && lastVal[lastVal.length-1] || '' }}</view>
            <view class="new-list-r">+新建话题</view>
          </view>
        </view>
        </scroll-view>
    </view>
    <view class="gambit-btn">
      <button @click="handleClickConfirm">确定</button>
    </view>
  </view>
</template>

<script>
  import uniIcons from '@/components/uni/uni-icons/uni-icons.vue'
  export default {
    components: {
      uniIcons,
    },
    props: {
      value: [String,Number],
      topicIds:{
        type:Array,
        default:[]
      },
      isShowJumpGambit:{
        type:Boolean,
        default:false,
      }
    },
    data(){
      return{
        autofocus:false,
        keyList:[],
        gambitText:'',
        targetVal:'',
        lastVal:[],
        timer:null,
        gambitList:[],
        showCustomConfirm:false,
        showPop: false,
        popPosition: {}, // 弹窗定位坐标
        path:'',
        form:{},
        // isShowGambit:false
      }
    },
    watch: {

    },
    onLoad(options){
      const query = this.$validate.isNull(this.$Route.query) ? options : this.$Route.query
      console.log(query,'query90999')
      if(query?.path){
        this.path = query.path
      }
      if(query?.list){
        this.gambitList = query?.list || []
        console.log(this.gambitList,'this.gambitList99888')
      }
      if(query?.form){
        this.form = query?.form
      }
      this.postmessagetopicQueryByName('')
    },
    mounted(){},
    methods:{
      handleDelete(index){
        this.gambitList.splice(index,1)
      },

      // 确定按钮
      handleClickConfirm(){
        console.log(this.gambitList,'this.gambitList2222')
        let newArr = ['Index', 'CircleHome', 'News', 'PostMessage', 'Personal']
        if(newArr.includes(this.path)){
          console.log(encodeURIComponent(JSON.stringify(this.gambitList)),'encodeURIComponent(JSON.stringify(this.gambitList))')
          uni.reLaunch({
            url: `/pages/post-message/index?params=${encodeURIComponent(JSON.stringify(this.gambitList))}&form=${encodeURIComponent(JSON.stringify(this.form))}`
          })
        } else {
          this.$navto.replaceAll('postMessageIndex',{params:encodeURIComponent(JSON.stringify(this.gambitList)),form:encodeURIComponent(JSON.stringify(this.form))})
        }
      },

      handleItemDelete(index){
        uni.showModal({
          title: '提示',
          content: '是否确定删除？',
          success:(res)=> {
            if (res.confirm) {
              console.log('确定')
              this.handleDelete(index)
            }
          }
        })
        // this.showCustomConfirm = true
      },
      // 用户输入
      handleInput(e){
        this.lastVal = e.target.value && e.target.value.split(' ') || ''
        console.log(this.lastVal,'this.lastVal')
        this.targetVal = e.target.value //输入的值
        console.log(this.targetVal,'this.targetVal000988')
        clearTimeout(this.timer)
        this.timer = setTimeout(()=>{
          // if(this.targetVal && this.lastVal[this.lastVal.length-1]){
            // this.checkTrigger(this.lastVal[this.lastVal.length-1])
          // }
          this.checkTrigger(this.targetVal)
        },500)
      },

      // 点击列表话题
      handleClickItem(item){
        console.log(item,'item2222')
        console.log(this.gambitList,'this.gambitList090998898')
        if(this.gambitList.length >= 5) return uni.showToast({title:'最多可选择5个话题',icon:'none'})
        this.gambitList.push({id:item.id,name:item.name.replace(/[#\s]/g, '')})
        this.gambitText = ''
        this.checkTrigger(this.gambitText)
      },

      // 查询不到话题 添加新的话题
      handleClickAddNew(){
        if(this.targetVal && this.lastVal[this.lastVal.length-1]){
          this.postmessagetopicInsert(this.lastVal[this.lastVal.length-1])
          this.gambitText = ''
          this.checkTrigger(this.gambitText)
        }
      },

      async postmessagetopicDeleteOne(id){
        await this.$api.postmessage.postmessagetopicDeleteOne({id})
        uni.showToast({title:'删除成功'})
      },

      async postmessagetopicInsert(name){
        const res = await this.$api.postmessage.postmessagetopicInsert({name:name.replace(/[#\s]/g, '')})
        this.gambitList.push({id:res.data.id,name:res.data.name}) //(/^#+/, '')
      },

      // 检测是否需要触发请求
      checkTrigger(content) {
        this.postmessagetopicQueryByName(content)
      },

      // 查询话题
      async postmessagetopicQueryByName(name){
        const res = await this.$api.postmessage.postmessagetopicQueryByName({name})
        if(res.data?.length){
          this.keyList = res.data
          // this.isShowGambit = isShowGambit
        } else {
          this.keyList = []
        }
      },
      
      handleBlur() {
        this.autofocus = false
      },
    },
 }
</script>

<style lang='scss' scoped>
.text-list{
  display: flex;
  flex-wrap: wrap;
  height: 150rpx;
  .list-item{
    display: flex;
    align-items: center;
    justify-content: center;
    height: fit-content;
    padding: 8rpx 20rpx;
    background: #F5F5F5;
    font-size: 24rpx;
    color: #133667;
    border-radius: 124rpx 124rpx 124rpx 124rpx;
    margin-right: 20rpx;
    margin-bottom: 20rpx;
    .my-icon{
      margin-left: 10rpx;
    }
    &:last-child{
      margin-right: 0;
    }
  }
}
.line{
  height: 2rpx;
  background-color: #E6E6E6;
  margin: 10rpx 0 40rpx;
}
.post-gambit{
  display: flex;
  flex-direction: column;
  height: calc(100vh - 32rpx);
  padding:16rpx 32rpx;
  background-color: #fff;
}

.gambit-list{
  .title{
    padding: 16rpx 0;
    font-weight: bold;
    font-size: 30rpx;
    color: #333333;
  }
  .my-scroll-view{
    overflow: hidden;
    .keyboard-content{
      max-height: 750rpx;
      .keyboard-list{
        display: flex;
        justify-content: space-between;
        padding: 16rpx 0;
        border-bottom: 1rpx solid #E6E6E6;
        .list-l,.list-r,.new-list-r{
          font-size: 28rpx;
          color: #000000;
        }
        .list-r{
          color: #777777;
        }
        .new-list-r{
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 8rpx 12rpx;
          width: fit-content;
          background: #F5F5F5;
          border-radius: 96rpx 96rpx 96rpx 96rpx;
          color: #777777;
        }
        &:last-child{
          border-bottom: 0;
        }
      }
    }
  }
}
.gambit-btn{
  position: fixed;
  width: calc(100% - 64rpx);
  bottom: 72rpx;
  button{
    width: 100%;
    font-size: 32upx;
    font-weight: 500;
    color: #ffffff;
    height: 88rpx;
    line-height: 88upx;
    background: #00B484;
    border-radius: 44rpx 44rpx 44rpx 44rpx;
  }
}
.textarea {
  width: 100%;
  padding: 18upx 17upx;
  box-sizing: border-box;
  color:#1D2029;
  font-size: 28upx;
  text-align: left;
  display: inline-block;
  vertical-align: middle;
  background: #fff;
  border-radius: 0;
}
</style>