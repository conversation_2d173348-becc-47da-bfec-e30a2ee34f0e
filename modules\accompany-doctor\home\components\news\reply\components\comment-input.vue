<template>
    <view class="comment-submit-box" v-if="submit" @click="closeInput">
		<!-- 下边的click.stop.prevent用于让上边的click不传下去，以防点到下边的空白处触发closeInput方法 -->
		<view class="comment-add" @click.stop.prevent="stopPrevent" :style="'bottom:' + KeyboardHeight + 'px'">
			<textarea class="textarea" v-model="commentReq.content" :placeholder="placeholder" :adjust-position="false" :show-confirm-bar="false"
				@blur="blur" @focus="focusOn" :focus="focus" maxlength="250"></textarea>
			<template v-if="!$validate.isNull(sendImgages)">
				<view v-for="(e, eIndex) in sendImgages.map(item => item.dir)" :key="eIndex" class="send-img-box">
					<view class="send-img-remove" @click.stop="sendImgages = []"></view>
					<image @click="previewImage(sendImgages, eIndex)"
						:src="file_ctx + e" mode="aspectFill" class="send-img"
					></image>
				</view>
			</template>
			<view class="comment-add-bottom">
				<uni-icons type="image-filled" :size="24" @tap="clickSendImg" />
				<title-img disabled ref="sendImgRef" :config="config.img" @uploadFinish="sendImg"></title-img>
				<view class="comment-add-bottom-right">
					<!-- <checkbox-group style="width: auto;" @change="changeAnonymous">
						<label>
							<checkbox value="anonymous" :checked="isAnonymous" />匿名
						</label>
					</checkbox-group> -->
					<button @click="handleRelease" class="release-btn" type="primary" size="mini" :disabled="!commentReq.content && $validate.isNull(sendImgages)">发布</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import UniIcons from '@/components/uni/uni-icons/uni-icons.vue';
export default {
    props: {
        show: {
            type: Boolean,
            default: false
        },
        placeholder: {
            type: String,
            default: '请输入评论'
        }
    },
    components: {
        UniIcons
    },
    data () {
        return {
            KeyboardHeight: 0,
            submit: false,
            focus: false,
            commentReq: {
                content: ''
            },
            isAnonymous: false,
						sendImgages: []
        }
    },
    mounted: function() {
		uni.onKeyboardHeightChange(res => {
			this.KeyboardHeight = res.height;
		})
	},
    watch: {
        show: {
            handler () {
                this.submit = this.show
            },
            immediate: true
        }
    },
    methods: {
		clickSendImg () {
			this.$refs.sendImgRef.uploadImage()
		},
		sendImg (list) {
			this.sendImgages = list
			// this.$emit('comment', {target: this.replyTarget, imagePath: list, type: 2})
		},
    handleRelease () {
      this.$emit('confirm', { content: this.commentReq.content, imagePath: this.sendImgages })
		},
    changeAnonymous (e) {
			this.isAnonymous = e.detail.value.includes('anonymous')
		},
    clearInput () {
			this.commentReq = {
				content: ''
      }
			this.sendImgages = []
    },
    // 关闭输入评论
		closeInput() {
			this.focus = false;
      this.$emit('update:show', false)
		},
    // 输入框失去焦点
		blur() {
			this.focus = false;
		},
		// 输入框聚焦
		focusOn() {
			this.$emit('focusOn');
		},
		stopPrevent() {},
    }
}
</script>

<style scoped lang="scss">
.comment-submit-box {
	position: fixed;
	display: flex;
	align-items: flex-end;
	z-index: 9900;
	left: 0;
	top: var(--window-top);
	bottom: 0;
	background-color: rgba($color: #000000, $alpha: 0.5);
	width: 100%;
}
.comment-add {
	position: absolute;
	background-color: #FFFFFF;
	width: 100%;
	border: 1px solid #ddd;
	transition: .3s;
	-webkit-transition: .3s;
	padding: 27upx 34upx;
	padding-bottom: constant(safe-area-inset-bottom);
	padding-bottom: env(safe-area-inset-bottom);
	box-sizing: border-box;
	&-bottom {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding-top: 18upx;
		&-right {
			display: flex;
			align-items: center;
			justify-content: flex-end;
		}
	}
}
.textarea {
	height: 135upx;
	padding: 18upx 20upx;
	width: 100%;
	background: #F6F6F6;
	border-radius: 14upx;
	box-sizing: border-box;
}
.release-btn {
	margin-left: 24upx;
}
</style>