// import serverOptions from './options'
// console.log('serverOptions',serverOptions);
const packagingEnvironment = 'pro' // 打包环境(test-测试/pro-投产)
const isOpenAccountSubscribe = false // 是否开启公众号订阅
// #ifdef H5
const isOpenMsgSubscribe = false // 是否开启小程序消息订阅
// #endif
// #ifndef H5
const isOpenMsgSubscribe = true // 是否开启小程序消息订阅
// #endif
// 操作人类型：1-员工，2-医师，3-专员，4-患者 -userType
const environmentConfiguration = {
    'test': {
        environmental: 'test',
        ctx: 'http://192.168.3.103:7002/',
        ws_ctx: 'tws.greenboniot.cn/',
        file_ctx: 'https://test-oss.greenboniot.cn/',
        domain_ctx: 'http://tapp.greenboniot.cn/',
        systemId: '1',
        userType: 4 ,
        isDebug: true,
        tenantId:1,
        envVersion: 'trial', // 打开另一个小程序配置参数；develop（开发版），trial（体验版），release（正式版：默认）
        static_ctx: 'http://test-file.greenboniot.cn/static/', // 静态资源库（editor组件的css文件与uni.scss都用用到这个路径，如有变动请同步修改）
        version_ctx: '1.0.0.220927_Release', // 版本号
        appId: 'wx436b8e65632f880f',
        subscribeUrl: 'https://mp.weixin.qq.com/s/0nUv8L0mcnAgMwQToMFJTA', // 公众号消息订阅
        isOpenAccountSubscribe,
        isOpenMsgSubscribe
    },
    'pro': {
        environmental: 'pro',
        ctx: 'https://api.greenboniot.cn/',
        ws_ctx: 'wss://ws.greenboniot.cn/ws',
        file_ctx: 'https://file.greenboniot.cn/',
        domain_ctx: 'https://saas.greenboniot.cn/ps/',
        systemId: '1',
        userType: 4 ,
        isDebug: false,
        tenantId:1,
        envVersion: 'release', // 打开另一个小程序配置参数；develop（开发版），trial（体验版），release（正式版：默认）
		static_ctx: 'http://file.greenboniot.cn/static/', // 静态资源库（editor组件的css文件与uni.scss都用用到这个路径，如有变动请同步修改）
        version_ctx: '1.0.0.220927_Release', // 版本号
        appId: 'wx436b8e65632f880f',
        subscribeUrl: 'https://mp.weixin.qq.com/s/0nUv8L0mcnAgMwQToMFJTA', // 公众号消息订阅
        isOpenAccountSubscribe,
        isOpenMsgSubscribe
    }
}

const parameters = {
    // 开发环境配置
    development: {
        // environmental: 'test',
          // ctx: 'https://api.greenboniot.cn/',
        //   ws_ctx: 'ws.greenboniot.cn/',
          // file_ctx: 'https://file.greenboniot.cn/',
        ctx: 'http://192.168.3.53:7000/',
        // // ctx: 'http://test.ngrok.greenboniot.cn/',
        // ws_ctx: 'ws://192.168.3.110:9001/ws',
        file_ctx: 'http://test-file.greenboniot.cn/',
        // domain_ctx: 'http://192.168.3.18:8080/ps/',
        // systemId: '1',
        // userType: 4 ,
        // isDebug: true,
        // tenantId:1,
        // envVersion: 'release', // 打开另一个小程序配置参数；develop（开发版），trial（体验版），release（正式版：默认）
        // static_ctx: 'http://file.greenboniot.cn/static/', // 静态资源库（editor组件的css文件与uni.scss都用用到这个路径，如有变动请同步修改）
        // version_ctx: '1.0.0.220927_Release', // 版本号
        // appId: 'wx436b8e65632f880f',
        // subscribeUrl: 'https://mp.weixin.qq.com/s/0nUv8L0mcnAgMwQToMFJTA', // 公众号消息订阅
        // isOpenAccountSubscribe,
        // isOpenMsgSubscribe

        environmental: 'pro',
        // ctx: 'https://api.greenboniot.cn/',
        ws_ctx: 'wss://ws.greenboniot.cn/ws',
        // file_ctx: 'https://file.greenboniot.cn/',
        domain_ctx: 'https://saas.greenboniot.cn/ps/',
        systemId: '1',
        userType: 4 ,
        isDebug: true,
        tenantId:1,
        envVersion: 'release', // 打开另一个小程序配置参数；develop（开发版），trial（体验版），release（正式版：默认）
        static_ctx: 'http://file.greenboniot.cn/static/', // 静态资源库（editor组件的css文件与uni.scss都用用到这个路径，如有变动请同步修改）
        version_ctx: '1.0.0.220927_Release', // 版本号
        appId: 'wx436b8e65632f880f',
        subscribeUrl: 'https://mp.weixin.qq.com/s/0nUv8L0mcnAgMwQToMFJTA', // 公众号消息订阅
        isOpenAccountSubscribe,
        isOpenMsgSubscribe
    },
    production: environmentConfiguration[packagingEnvironment]
}

export default parameters[process.env.NODE_ENV]
