<template>
    <page>
        <view slot="content" class="body-main">
            <view class="header">
                <view class="header-l">
                    <view class="name">{{ detailObj.name }}</view>
                    <view class="title">
                        <p class="hospital">{{ detailObj.hospitalName }}<uni-icons :size="10" color="#666" type="right" /></p>
                        <p>{{ detailObj.deptName }}</p>
                    </view>
                </view>
                <view class="header-r">
                    <image mode="aspectFit" :src="detailObj.expertPic"></image>
                </view>
            </view>
            <view class="doctor-content">
                <view class="doctor-adept">
                    <view class="info">
                        <view class="adept-img">
                            <image src="https://lvbao-saas-test.oss-cn-shenzhen.aliyuncs.com/hospitalRanking/goodAt.png"></image>
                        </view>
                        {{ detailObj.goodAt }}
                    </view>
                </view>
                <view class="doctor-brief">
                    <view class="info">
                        <view class="brief-img">
                            <image src="https://lvbao-saas-test.oss-cn-shenzhen.aliyuncs.com/hospitalRanking/intro.png"></image>
                        </view>
                        {{ detailObj.introduction }}
                    </view>
                </view>
            </view>
        </view>
    </page>
</template>

<script>
  import UniIcons from '@/components/uni/uni-icons/uni-icons'
  import { isDomainUrl } from '@/utils/index.js'
  export default {
  components: {
    UniIcons,
  },
    data(){
      return{
        detailObj:{}
      }
    },
    onLoad(option){
        // 医院详情接口
        this.$api.hospital.crawlershospitaldoctorQueryOne({id:option.id}).then(res=>{
            console.log(res,'res111')
            if(res.code == 0){
                this.detailObj = {...res.data,expertPic:isDomainUrl(res.data.expertPic)}
            }
        })
    },
    mounted(){},
    methods:{},
 }
</script>

<style lang='scss' scoped>
.body-main{
  background-color: #f5f5f5;
    .header{
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding:20upx 40upx;
        border-radius:20upx 20upx 0 0;
        margin:0 20upx;
        background-color: #fff;
     .header-l{
        .name{
            color:#000;
            font-size: 32upx;
            font-weight: bold;
        }
        .title{
            margin-top: 40upx;
            font-size: 29upx;
            .hospital{
                color:#000;
                margin-bottom: 10upx;
            }
        }
     }   
     .header-r{
        width: 150upx;
        height: 150upx;
        image{
            width: 100%;
            height: 100%;
        }
     }   
    }
    .doctor-content{
        background-color: #fff;
        padding:20upx 40upx;
        border-radius:0 0 20upx 20upx;
        margin:0 20upx; 
        .doctor-adept,.doctor-brief{
            display: flex;
            align-items: center;
            width: 100%;
            .info{
                font-size: 30upx;
                .adept-img,.brief-img{
                    width: 175upx;
                    height: 42upx;
                    margin-right: 10upx;
                    margin-bottom: 10upx;
                    image,.image{
                        width: 100%;    
                        height: 100%;
                    }
                }
            }
        }
        .doctor-brief{
            margin-top: 30upx;
        }
    }
}
</style>