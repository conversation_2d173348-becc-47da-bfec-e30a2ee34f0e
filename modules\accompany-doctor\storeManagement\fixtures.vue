<template>
    <view class="page">
        <view class="titleBox">
            <view class="title" @click="current = index" :class="{current:current === index}" v-for="(item, index) in skinList" :key="index">{{item.title}}</view>
        </view>
        <swiper :current="current" class="swiper" interval="5000" duration="1000" circular="true" @change="changeSwiper">
            <swiper-item class="swiper-item" v-for="pageItem in skinList" :key="pageItem.pageIcon">
                <image :src="file_ctx + pageItem.pageIcon" class="icon"></image>
            </swiper-item>
        </swiper>
        <view class="bottomBox">
            <view class="bottomBtn" @click="apply">一键应用</view>
        </view>
    </view>
</template>
<script>
  import serverOptions from '@/config/env/options'

export default {
    data() {
        return {
            file_ctx: this.file_ctx,
            skinList:[
                {title:'清新蓝',decoration:1,pageIcon:"static/image/business/accompany-doctor/blueSkin.png"},
                {title:'环保绿',decoration:0,pageIcon:"static/image/business/accompany-doctor/greenSkin.png"},
            ],
            current:0,
        }
    },
    methods: {
        // 跳转订单详情页
        gotoPage(routerName) {
            
        },
        changeSwiper(e){
            this.current = e.detail.current;
        },
        async apply(){
            uni.showLoading({title:'正在应用中...'})
            let currentSkin = this.skinList[this.current];
            let {data} = await this.$api.accompanyDoctor.accompanyproviderQueryOne({id:serverOptions.providerId});
            data.decoration = currentSkin.decoration;
            await this.$api.accompanyDoctor.accompanyproviderUpdate(data);
            uni.hideLoading();
            this.$navto.replaceAll('AccompanyHome',{providerId:serverOptions.providerId})
            uni.showToast({title:'应用成功',icon:'none'});
        }
    }
}
</script>
<style lang="scss" scoped>
.page{
    width: 100vw;
    height: 100vh;
    background-color: #F4F6FA;
    padding: 32rpx;
    box-sizing: border-box;
}
.titleBox{
    width: 100vw;
    height: 80rpx;
    display: flex;
    column-gap: 40rpx;
    .title{
        font-weight: 500;
        font-size: 28rpx;
        color: #777777;
    }
    .current{
        font-weight: bold;
        font-size: 28rpx;
        color: #333333;
    }
}
.swiper{
    width: 100%;
    height: 1486rpx;
    border-radius: 16rpx;
    padding-bottom: 180rpx;
}
.icon{
    width: 100%;
    height: 1486rpx;
    border-radius: 16rpx;
}
.bottomBox{
    position: fixed;
    bottom: 0;
    left: 0;
    width: 750rpx;
    height: 180rpx;
    background: #FFFFFF;
    box-shadow: 0rpx -1rpx 0rpx 0rpx #EAEBF0;
    border-radius: 0rpx 0rpx 0rpx 0rpx;
    .bottomBtn{
        width: 646rpx;
        height: 88rpx;
        line-height: 88rpx;
        text-align: center;
        background: #00B484;
        border-radius: 44rpx 44rpx 44rpx 44rpx;
        margin: 24rpx auto;
        font-weight: 500;
        font-size: 32rpx;
        color: #FFFFFF;
    }
}
</style>