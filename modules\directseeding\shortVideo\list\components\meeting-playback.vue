<template>
  <video-playback-list :is-jump="false" @clickItem="$emit('clickItem', $event)" :list="pdList" />
</template>

<script>
import videoPlaybackList from './video-playback.vue';
export default {
  components: {
    videoPlaybackList
  },
  props: {
    mainId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      pdList: []
    };
  },
  watch: {
    mainId: {
      handler() {
        this.meetingplaybackGetPlaybackByMeetingid();
      },
      immediate: true
    }
  },
  methods: {
    meetingplaybackGetPlaybackByMeetingid() {
      this.$api.cloudClassroom.meetingplaybackGetPlaybackByMeetingid({ meetingId: this.mainId }).then(res => {
        for (let i = 0; i < res.data.length; i++) {
          res.data[i].burningTimeText = this.$common.getDifferenceTimeTypeFormat(
            this.$common.formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss'),
            this.$common.formatDate(new Date(new Date().getTime() + res.data[i].burningTime * 1000), 'yyyy-MM-dd HH:mm:ss')
          );
        }

        this.pdList = res.data;
      });
    }
  }
};
</script>

<style></style>
