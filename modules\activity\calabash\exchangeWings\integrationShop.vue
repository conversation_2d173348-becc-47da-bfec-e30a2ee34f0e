<template>
  <view class="page">
    <view class="header">
      <!-- 头像&用户名 -->
      <view class="tabTopBox">
        <image class="headimg" :src="file_ctx+avatarUrl" mode="aspectFill"></image>
        <view class="headTitle">
          <view>{{fansRecord.nickName}}</view>
          <view>当前福币：<text class="integrationNum">{{initGoldNum}}</text> </view>
        </view>
      </view>
      <view class="header-r" @click="gotoPages('modules/activity/calabash/exchangeWings/exchangeRecords')">
        兑换记录
        <image class="gotoRight" :src="gotoRight" mode="aspectFill"></image>
      </view>
    </view>
    <scroll-view class="goodsMap" scroll-y="true" @scrolltolower="lower">
      <view class="goods" @click="gotoPages('modules/activity/calabash/exchangeWings/integrationGoods',item.id)" v-for="(item,index) in goodMaps" :key="index">
        <view class="goodsIcon">
          <image class="icon" :src="file_ctx + item.productImage" mode="aspectFill"></image>
        </view>
        <view class="goodsContent">
          <view class="goodsName">{{item.productName}}</view>
          <view class="goodsCon">
            <view class="goodsNums">
              <view>{{item.needPoint}}</view>
              <view class="goodsSinge">福币</view>
            </view>
            <view class="goodsBtn" v-if="item.needPoint<=initGoldNum">去兑换</view>
            <view v-else class="goodsNoBtn goodsBtn">福币不足</view>
          </view>
        </view>
      </view>
    </scroll-view>
    <!-- 跳转 -->
    <view class="gotoTask" @click='gotoTask'>
      立即赚取更多福币
      <image class="rightWhite" :src="rightWhite" mode="aspectFill"></image>
    </view>
  </view>
</template>

<script>
  import { mapState } from 'vuex'
  import calabashApis from "@/modules/common/api/calabash.js"
  export default{
    data(){
      return {
        file_ctx: this.file_ctx,
        initGoldNum:0,
        current:0,
        loadEnd:false,
        goodMaps:[],
        gotoRight: this.$static_ctx + "image/business/hulu-v2/goto-right.png",
        rightWhite: this.$static_ctx + "image/business/hulu-v2/right-white.png",
      }
    },
    computed: {
      ...mapState('user', {
        fansRecord: state => state.fansRecord,
        accountId: (state) => state.accountId,
      }),
      avatarUrl(){
        return this.fansRecord.headPath ? this.fansRecord.headPath : this.defaultAvatar
      },
    },
    onLoad() {
      this.loadData()
    },
    methods:{
      gotoPages(url,item){
        uni.navigateTo({url:`${url}${item ? `?id=${item}` : ``}`})
      },
      gotoTask(){
        uni.navigateTo({url:`modules/activity/calabash/luckyCoinaTask`})
      },
      async loadData(){
        // 获取初始化金币数量
        let {data:{totalPoint}} = await calabashApis.pointuserQueryUser({accountId:this.accountId})
        this.initGoldNum = totalPoint
        console.log('this.initGoldNum',this.initGoldNum);
        this.getPointgiftQueryPage()
      },
      lower(){
        this.getPointgiftQueryPage()
      },
      async getPointgiftQueryPage(){
        if(this.loadEnd) return
        this.current+=1
        // 获取商品列表
        let {data:{records,total}} = await calabashApis.pointgiftQueryPage({
          current:this.current,
          size:10,
          condition:{state:1}
        })
        if(total <= this.goodMaps.length){
           this.loadEnd = true
           return
        }
        this.goodMaps.push(...records)
      }
    }
  }
</script>

<style lang="scss">
  .page{
    width: 750rpx;
    height: 100vh;
    background: #F4F6FA;
  }
  .gotoTask{
    width: 382rpx;
    height: 88rpx;
    background: #00B484;
    box-shadow: 0rpx 4rpx 12rpx 0rpx rgba(9,97,73,0.45);
    border-radius: 44rpx;
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
    line-height: 88rpx;
    position: fixed;
    left: 50%;
    transform: translateX(-50%);
    bottom: 60rpx;
    color: white;
    .rightWhite{
      width: 28rpx;
      height: 24rpx;
      margin-left: 2rpx;
    }
  }
  .header{
    width: 750rpx;
    height: 104rpx;
    background: #FFFFFF;
    padding: 0 32rpx;
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    align-items: center;

    .tabTopBox{
      display: flex;
      font-size: 26rpx;
      color: #1D2029;
      .headimg{
        width:72rpx;
        height: 72rpx;
        background: #C8F4E8;
        border: 1rpx solid #EAEBF0;
        border-radius: 50%;
        margin-right: 12rpx;
      }
      .headTitle{
        transform:translateY(-8rpx);
      }
      .integrationNum{
        font-weight: 500;
        font-size: 32rpx;
        color: #1D2029;
      }
    }
    .header-r{
      font-weight: 400;
      font-size: 24rpx;
      color: #4E5569;
      display: flex;
      align-items: center;
      .gotoRight{
        width: 28rpx;
        height: 28rpx;
      }
    }
  }
  .goodsMap{
    width: 100vw;
    height: calc(100vh - 104rpx);
    display: flex;
    padding: 22rpx calc((100vw - 664rpx) / 3);
    box-sizing: border-box;
    .goods{
      width: 332rpx;
      display: inline-block;
      margin-bottom: 22rpx;
      &:nth-of-type(odd){
        margin-right: calc((100vw - 664rpx) / 3);
      }
      .goodsIcon{
        width: 332rpx;
        height: 332rpx;
        background: #FFFFFF;
        overflow: hidden;
        border-radius: 16rpx 16rpx 0rpx 0rpx;
        .icon{
          width: 100%;
          height: 100%;
        }
      }
      .goodsContent{
        width: 332rpx;
        height: 172rpx;
        background: #FFFFFF;
        padding: 20rpx;
        box-sizing: border-box;
        border-radius: 0rpx 0rpx 16rpx 16rpx;
        .goodsName{
          width: 292rpx;
          height: 72rpx;
          font-weight: 600;
          font-size: 26rpx;
          color: #1D2029;
          line-height: 36rpx;
          text-align: left;
          font-style: normal;
          word-wrap: break-word;
          overflow: hidden;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-bottom: 8rpx;
        }
        .goodsCon{
          display: flex;
          justify-content: space-between;
          align-items: center;
          .goodsNums{
            font-weight: 600;
            font-size: 40rpx;
            color: #FF5500;
            display: flex;
            .goodsSinge{
              font-size: 20rpx;
              color: #FF4100;
              padding-top: 8rpx;
              margin-left: 4rpx;
            }
          }
          .goodsBtn{
            width: 90rpx;
            height: 48rpx;
            line-height: 48rpx;
            background: #00B484;
            border-radius: 200rpx;
            font-size: 22rpx;
            color: #FFFFFF;
            text-align: center;
          }
          .goodsNoBtn{
            width: 112rpx;
            height: 48rpx;
            background: #C9CCD4;
            border-radius: 200rpx;
          }
        }
      }
    }
  }
</style>
