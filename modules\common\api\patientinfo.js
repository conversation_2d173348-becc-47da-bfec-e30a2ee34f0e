import request from '@/common/util/request'
import env from '@/config/env'

/**
 * 患者档案
 */
export default {
  /**
   * 根据主账户id查询家庭关系列表
   * @param param
   * @param {*}
   */
  patientinfoQueryListByMasterId(param) {
    const url = env.ctx + 'dm/api/v1/patientinfo/queryListByMasterId'
    return request.get(url, param)
  },
  /**
   * 保存数据
   * @param param
   * @param {*}
   */
  patientinfoInsert(param) {
    const url = env.ctx + 'dm/api/v1/patientinfo/insert'
    return request.postJson(url, param)
  },
  /**
   * 更新数据
   * @param param
   * @param {*}
   */
  patientinfoUpdate(param) {
    const url = env.ctx + 'dm/api/v1/patientinfo/update'
    return request.putJson(url, param)
  },
  /**
   * 根据主键单一查询
   * @param param
   * @param {*}
   */
  patientinfoQueryOne(param) {
    const url = env.ctx + 'dm/api/v1/patientinfo/query/one'
    return request.get(url, param)
  },
}
