<template>
  <view class="main">
    <view class="top">
      <view class="top-nav">
        <view class="top-nav-body">
          <view class="city" @tap="$navto.replace('CommonPositioning')">
            <view class="white">{{ regionInfo.city || '定位失败' }}</view>
            <em class="icon-positioning-down"></em>
          </view>
          <view class="input-view">
            <i class="icon-positioning-search"></i>
            <input confirm-type="search" placeholder="搜索地址" placeholder-style="color: #BFBFBF" class="input" type="text" :value="search" @input="searchInputFn" @confirm="searchFn">
          </view>
          <view class="click" @tap="searchFn">
            搜索
          </view>
        </view>
      </view>
    </view>
    <scroll-refresh :up="upOption" :fixed='false' :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
      <view class="location" v-for="(item, index) in pdList" :key="index" @tap="clickFn(item)">
        <view class="location-list bdb">
          <view class="top">
            <view class="name">
              {{item.name || ''}}
            </view>
          </view>
          <view class="bottom">
            {{item.pname || ''}}{{item.pname || ''}}{{item.cityname || ''}}{{item.adname || ''}}{{item.address || ''}}
          </view>
        </view>
      </view>
    </scroll-refresh>
  </view>
</template>

<script>
import { mapState } from 'vuex'

export default {
  components: {

  },
  computed: {
    ...mapState('system', {
      cityInfoStorage: state => state.cityInfoStorage, // 当前定位信息集合
      longitudeAndLatitude: state => state.longitudeAndLatitude, // 当前经纬度集合
      cityInfoArrStorage: state => state.cityInfoArrStorage // 历史定位数组信息
    })
  },
  data() {
    return {
      mescroll: null, // mescroll实例对象
      downOption: {
        auto: false // 不自动加载
      },
      upOption: {
        auto: false // 不自动加载
      },
      pdList: [], // 列表数据
      search: '',
      regionInfo: {},
      cityInfo: {}
    }
  },
  onLoad(paramsObj) {
    const query = this.$Route.query
    if (!this.$validate.isNull(query)) {
      this.search = query.search
    }
    if (this.search) {
      this.init()
    }
  },
  methods: {
    init() {
      this.$nextTick(() => {
        const regionInfo = this.$common.getKeyVal('system', 'longitudeAndLatitude', true)
        if (regionInfo) {
          if (typeof regionInfo === 'object' && JSON.stringify(regionInfo) !== '{}') {
            this.regionInfo = regionInfo
          } else {
            this.regionInfo = {}
          }
        } else {
          this.regionInfo = {}
        }
        this.searchFn()
      })
    },
    clickFn(item) {
      this.$common.setKeyVal('system', 'cityInfoStorage', item, true)
      let arr = this.$common.getKeyVal('system', 'cityInfoArrStorage', true)
      if (typeof arr === 'string' && arr === '') {
        arr = []
      } else if (typeof arr === 'string' && arr !== '') {
        arr = JSON.parse(arr)
      }
      if (arr) {
        if (typeof arr === 'object' && arr.length > 0) {
          let state = false
          let index = ''
          for (let i = 0; i < arr.length; i++) {
            if (item.id === arr[i].id) {
              state = true
              index = i
              break
            }
          }
          if (state) {
            if (typeof index === 'number') {
              arr.splice(index, 1)
            }
          }
        }
        arr.unshift(item)
        this.$common.setKeyVal('system', 'cityInfoArrStorage', arr, true)
      } else {
        this.$common.setKeyVal('system', 'cityInfoArrStorage', [], true)
      }
      // this.$navto.back(1)
      this.$navto.back(2)
      this.$common.setKeyVal('system', 'isOnShow', true)
    },
    searchInputFn(e) {
      this.search = e.target.value
    },
    scrollInit(scroll) {
      scroll.optUp.page.num = 1
      scroll.optUp.page.size = 10
      this.mescroll = scroll
    },
    searchFn() {
      this.mescroll.optUp.page.num = 1
      this.mescroll.optUp.page.size = 10
      this.mescroll.triggerDownScroll()
    },
    returnFn(obj) {
      const that = this
      function queryPage(pageNum, pageSize, fn) {
        const param = {
          keywords: that.search,
          offset: pageSize,
          page: pageNum
        }
        if (that.regionInfo.city) {
          param.city = that.regionInfo.city
        }
        that.$api.common.homepageQueryPoi(param).then(res => {
          if (res && res.pois) {
            fn(res.pois)
          } else {
            fn([])
          }
        })
      }
      setTimeout(function() {
        queryPage(obj.pageNum, obj.pageSize, (data) => {
          if (obj.pageNum === 1) {
            that.pdList = []
          }
          that.pdList = that.pdList.concat(data)
          obj.successCallback && obj.successCallback(data || [])
        })
      }, that.$constant.noun.scrollRefreshTime)
    }
  }
}
</script>

<style lang="scss" scoped>
  .m-t-40{
    margin-top:40upx;
  }
  .m-b-20{
    margin-bottom: 20upx;
  }
  .main{
    height: calc(100vh - 88upx);
    .top-nav{
      padding: 10upx 30upx;
      background-color: #fff;
      .top-nav-body{
        .city {
          display: inline-block;
          vertical-align: middle;
          width: 160upx;
          margin-right: 16upx;
          .white{
            display: inline-block;
            vertical-align: middle;
            max-width: calc(100% - 28upx);
            font-weight: bold;
            color: #333;
            font-size: 28upx;
            line-height: 42upx;
            @include ellipsis(1);
          }
          .icon-positioning-down{
            display: inline-block;
            vertical-align: middle;
            @include iconImg(28, 28, '/system/icon-positioning-down.png');
          }
        }
        .input-view {
          display: inline-block;
          vertical-align: middle;
          width: calc(100% - 316upx);
          @include rounded(38upx);
          line-height: 64upx;
          height: 64upx;
          padding: 0 20upx;
          background: #F7F7F7;
          .icon-positioning-search{
            display: inline-block;
            vertical-align: middle;
            margin-right: 6upx;
            @include iconImg(32, 32, '/system/icon-positioning-search.png');
          }
          .input {
            width: calc(100% - 48upx);
            display: inline-block;
            vertical-align: middle;
            font-size: 28upx;
            line-height: 42upx;
            color: #333;
          }
        }
        .click{
          display: inline-block;
          vertical-align: middle;
          text-align: right;
          width: 100upx;
        }
      }
    }
    .location{
      padding: 0 30upx;
      background-color: #fff;
      .location-list{
        padding: 30upx 0;
        .top{
          margin-bottom: 20upx;
          .name{
            width: 100%;
            display: inline-block;
            vertical-align: middle;
            font-size: 32upx;
            line-height: 48upx;
            font-weight: 500;
          }
          .tag{
            display: inline-block;
            vertical-align: middle;
            width: 120upx;
            text-align: right;
            margin-left: 20upx;
            color: #277DFF;
          }
          .name.current{
            width: calc(100% - 140upx);
            @include ellipsis(1);
          }
        }
        .bottom{
          color: #666;
          font-size: 24upx;
          line-height: 36upx;
        }
      }
      .location-list.bdb{
        border-bottom: 2upx solid $contentDdt;
      }
    }
    .location:last-of-type{
      .location-list.bdb{
        border-bottom: none;
      }
    }
  }
</style>
