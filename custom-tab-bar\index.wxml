<view class="tab-bar">
  <view class="tab-bar-border"></view>
  <view wx:for="{{list}}" wx:key="index" class="tab-bar-item" data-path="{{item.pagePath}}" data-index="{{index}}" data-text="{{item.text}}" bindtap="switchTab">
    <view class="badge" wx:if="{{ item.badge }}">{{ item.badge }}</view>
    <image
      src="{{selected === index ? item.selectedIconPath : item.iconPath}}"
      class="{{ item.pagePath === '/pages/post-message/index' ? 'big-icon' : '' }}"
    ></image>
    <view
      style="color: {{selected === index ? selectedColor : color}}"
      wx:if="{{ item.pagePath !== '/pages/post-message/index' }}"
    >{{item.text}}</view>
  </view>
</view>
