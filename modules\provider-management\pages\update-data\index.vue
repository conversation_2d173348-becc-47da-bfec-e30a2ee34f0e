<template>
  <view class='update-data'>
    <view class="botTab">
      <view class="additionalContent">
        <view class="must">*</view>
        <view class="lineTitle">服务商logo</view>
        <!-- <view class="additionalContentImageUpMap">
          <view
          class="additionalContentImageUp"
          v-for="(item,index) in queryOptions.imageObj"
          :key="index"
          @click="$refs.upDataImage.previewImage(index)"
          >
            <image class="iconPostMenuClose" @click.stop="$refs.upDataImage.del(index)" :src="iconPostMenuClose" mode="aspectFill"></image>
            <image class="imageContent" :src="item.url"></image>
          </view>
          <view class="additionalContentImageUp" @click="$refs.upDataImage.uploadImage()">
            <image class="imageUpImage" :src="iconPostUpload" mode="aspectFill"></image>
            <view class="imageUpText">上传图片</view>
          </view>
        </view> -->
        <!-- <view class="lineHide"> -->
          <title-img
            :config="{
              padding: 0,
              background: 'none',
              margin: '10rpx',
              count: 1,
              multiSelectCount: 1,
            }"
            ref="upDataImage"
            @returnFn="(obj) => {imgReturnFn(obj)}"
            :cData="imageList"
          >
          </title-img>
        <!-- </view> -->
      </view>
      <view class="botTabLine">
        <view class="must">*</view>
        <view class="lineTitle">服务商名称</view>
        <input type="text" placeholder="请输入" class="bookInput" v-model="queryOptions.providerName" />
      </view>
      <!-- <view class="botTabLine" @click="$refs.selectCity.show()" >
        <view class="must">*</view>
        <view class="lineTitle">服务地区</view>
        <view class="lineValue">{{getSelectCity}}</view>
        <image class="lineIcon" :src="iconRightArrow" mode="aspectFill"></image>
        <image class="lineIcon" :src="iconRightArrow" mode=""></image>
      </view> -->
      <view class="lineHide">
        <dataPicker ref="selectCity" :localdata="provinceMap" popup-title="请选择" @change="onchangeCity" @nodeclick="onnodeclick"></dataPicker>
      </view>
      <view class="botTabLine">
        <view class="must">*</view>
        <view class="lineTitle">负责人姓名</view>
        <input type="text" placeholder="请输入" class="bookInput" v-model="queryOptions.username" />
      </view>
      <view class="botTabLine">
        <view class="must">*</view>
        <view class="lineTitle">负责人手机</view>
        <input type="text" placeholder="请输入" class="bookInput" v-model="queryOptions.phone"/>
      </view>
      <view class="intro">
        <view class="title">简介</view>
        <view class="outline-bott">
          <scroll-view style="height:100%" class="text_field_outer" scroll-y>
            <textarea  v-model="queryOptions.introduction" maxlength="-1" placeholder-style="color:#A5AAB8" placeholder="请输入"/>
          </scroll-view>
        </view>
      </view>
    </view>
    <!-- 底部 -->
    <view class="bottomBtn">
      <view class="comBtn" @click="handleSave">修改资料</view>
    </view>
  </view>
</template>

<script>
  import dataPicker from '../../components/uni-data-picker/uni-data-picker.vue'
  import TitleImg from "@/components/business/module/title-img/index.vue"
  export default {
    components:{
      dataPicker,
      TitleImg
    },
    data(){
      return{
        file_ctx: this.file_ctx,
        iconRightArrow: this.$static_ctx + "image/business/hulu-v2/icon-right-arrow.png",
        iconPostUpload: this.$static_ctx + "image/business/accompany-doctor/icon-accompany-post-upload.png",
        provinceMap:[],
        // queryOptions:{
        //   provinceValue:[],
        //   imageObj:[],
        //   range:[],
        //   bookName:'',
        //   bookPhone:'',
        //   intro:'',
        // },
        queryOptions:{
        },
        imageList:[],
      }
    },
    computed: {
      getSelectCity(){
        return this.queryOptions?.provinceValue?.join()
      },
    },
    onLoad(option){
      const query = this.$validate.isNull(this.$Route.query) ? option : this.$Route.query
      console.log(query,'query009888')
      this.queryOptions = {...query}
      this.$set(this.queryOptions,'provinceValue',[query.province,query.city,query.county])
      this.imageList = [{url:this.file_ctx + query.logo,dir:this.file_ctx + query.logo,filePath:query.logo}]
    },
    async mounted(){
      let {data} = await this.$api.providerManagement.getAccompanyproviderAll();
      // let {pname,cityname,adname} = await this.getPosition();
      // this.queryOptions.provinceValue = [pname,cityname,adname]
      // this.$set(this.queryOptions,'provinceValue',[pname,cityname,adname])
      let provinceMap = {}
      data.map(e=>{
        let {province,city,county} = e;
        if(!provinceMap[province]){
          provinceMap[province] = {
              text:province,
              value:province,
              children:{
                [city]:{text:city,value:city,children:{[county]:{text:county,value:county}}}
              }
          }
        }else{
          if(provinceMap[province].children[city]){
            // 当不存在该县级时
            if(!provinceMap[province].children[city].children[county]){
              provinceMap[province].children[city].children[county] = {text:county,value:county}
            }
          }else{
            provinceMap[province].children[city] = {text:city,value:city,children:{[county]:{text:county,value:county}}}
          }
        }
      })
      provinceMap = Object.values(provinceMap);
      provinceMap.map(e=>{
        e.children = Object.values(e.children);
        e.children.map(e1=>{
          e1.children = Object.values(e1.children);
        })
      })
      this.provinceMap = provinceMap
      console.log(this.provinceMap,'this.provinceMap009888')
    },
    methods:{
      // getPosition(){
      //   let resFn;
      //   let promise = new Promise(res=>resFn = res);
      //   uni.getLocation({
      //     type: 'wgs84',
      //     geocode:true,
      //     	success: async (res)=> {
      //         console.log('res',res);
      //         let Position = await this.$ext.common.getPosition(res);
      //         console.log('Position',Position);
      //         resFn(Position)
      //     	}
      //   });
      //   return promise
      // },
      imgReturnFn(list) {
        this.imageList = list
        console.log(this.imageList,'this.imageList0099')
      },
      handleSave(){
        if(!this.imageList.length){
          return this.$uniPlugin.toast('服务商logo不能为空')
        }
        if(!this.queryOptions.providerName){
          return this.$uniPlugin.toast('服务商名称不能为空')
        }
        if(!this.queryOptions.username){
          return this.$uniPlugin.toast('负责人姓名不能为空')
        }
        if(!this.queryOptions.phone){
          return this.$uniPlugin.toast('负责人手机不能为空')
        }
        if(!this.queryOptions.province){
          return this.$uniPlugin.toast('省市区不能为空')
        }
        let params = {
          logo:this.imageList.length && this.imageList[0].filePath || null,
          providerName:this.queryOptions.providerName,
          rate:this.queryOptions.rate,
          province:this.queryOptions.province,
          city:this.queryOptions.city,
          county:this.queryOptions.county,
          username:this.queryOptions.username,
          phone:this.queryOptions.phone,
          introduction:this.queryOptions.introduction,
          tlAccount:this.queryOptions.tlAccount,
          id:this.queryOptions.id
        }
        this.$api.providerManagement.accompanyproviderUpdate(params).then(res=>{
          this.$uniPlugin.toast('保存成功')
          this.$navto.push('ProviderWorkBench',{isShow:true})
        })
      },
      onchangeCity({detail:{value}}){
        this.queryOptions.provinceValue = value.map(e=>e.value);
        let [province,city,county] = this.queryOptions.provinceValue;
        this.queryOptions.province = province
        this.queryOptions.city = city
        this.queryOptions.county = county
        // this.getHospital({province,city})
      },
      onnodeclick(){},
    },
 }
</script>

<style lang='scss' scoped>
  .update-data{
    margin: 0 32rpx;
    background: #F4F6FA;
  }
  .botTab{
    box-sizing: border-box;
    padding: 32rpx;
    width: 686rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    margin-top: 20rpx;
    margin-bottom: 200rpx;
    .lineHide{
      width: 0;
      overflow: hidden;
      height: 0;
    }
    .additionalContent{
      display: flex;
      // margin-top: 32rpx;
      .must{
        font-size: 28rpx;
        color: #FF5500;
      }
      .additionalContentImageUpMap{
        display: flex;
        flex-wrap: wrap;
      }
      .additionalContentImageUp{
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 144rpx;
        height: 144rpx;
        background: #F4F6FA;
        border-radius: 16rpx;
        text-align: center;
        position: relative;
        margin-right: calc((100% - (144rpx * 3)) / 2);
        &:nth-of-type(3n){
          margin-right: 0;
        }
        .imageUpImage{
          width: 40rpx;
          height: 48rpx;
        }
        .imageUpText{
          font-weight: 400;
          font-size: 24rpx;
          color: #4E5569;
        }
        .imageContent{
          width: 100%;
          height: 100%;
        }
        .iconPostMenuClose{
          width: 30rpx;
          height: 30rpx;
          position: absolute;
          top: 0;
          right: 0;
          background: white;
        }
      }
    }
    .lineTitle{
      font-weight: 500;
      font-size: 28rpx;
      color: #1D2029;
      width: 212rpx;
    }
    .botTabLine{
      width: 100%;
      height: 104rpx;
      line-height: 104rpx;
      border-bottom: 2rpx solid #EAEBF0;
      display: flex;
      align-items: center;
      .must{
        font-size: 28rpx;
        color: #FF5500;
      }
      .lineValue{
        flex: 1;
        min-width: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .lineIcon{
        width: 32rpx;
        height: 32rpx;
      }
      .bookInput{
        width: 438rpx;
      }
      .botTabLine{
        width: 438rpx;
        height: 40rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 28rpx;
        color: #A5AAB8;
      }
    }
  }
  .bottomBtn{
    width: 750rpx;
    height: 180rpx;
    background: #FFFFFF;
    box-shadow: 0rpx -1rpx 0rpx 0rpx #EAEBF0;
    position: fixed;
    bottom: 0;
    left: 0;
    .comBtn{
      width: 686rpx;
      height: 88rpx;
      background: #00B484;
      border-radius: 44rpx;
      text-align: center;
      line-height: 88rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #FFFFFF;
      margin: 24rpx auto;
    }
  }
  .intro{
    display: flex;
    flex-direction: column;
    margin-top: 32rpx;
    .outline-bott{
      border-radius: 16rpx;
      border: 1rpx solid #D9DBE0;
      padding: 24rpx;
      margin-top: 24rpx;
      textarea{
        width: 100%;
        height: 160rpx;
        overflow-y: scroll;
      }
    }
  }
</style>
