import request from '@/common/util/request'
import env from '@/config/env'

/**
 * 系统请求工具类，
 * 注意：API接口只做请求数据和格式化数据的事情，其他事务一概不处理
 */
export default {
  getPhone(param) {
    const url = env.ctx + 'manage/api/v1/alipay/get/phone'
    return request.get(url, param)
  },
  getPhoneV2(param) {
    const url = env.ctx + 'manage/api/v2/alipay/get/phone'
    return request.get(url, param)
  },
  getPhoneV3(param) {
    const url = env.ctx + 'manage/api/v3/alipay/get/phone'
    return request.postJson(url, param)
  }
}