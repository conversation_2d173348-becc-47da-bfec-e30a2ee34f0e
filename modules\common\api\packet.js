import request from '@/common/util/request'
import env from '@/config/env'

/**
 * 系统请求工具类，
 * 注意：API接口只做请求数据和格式化数据的事情，其他事务一概不处理
 */
export default {
  /**
   * 获取H5换
   * @param param
   * @param resolve
   * @param reject
   * @returns {*}
   */
  getH5Link(param) {
    const url = env.ctx + 'manage/api/wx/getAppH5PacketUrl'
    return request.postForm(url, param)
  },
  // 获取设备信息info 参数: deviceId
  getDeviceInfoByDeviceId(data) {
    const url = env.ctx + 'manage/api/device/getPacketAuthTypeByDeviceId'
    return request.get(url, data)
  },
  // 根据设备id获取出袋链接 参数: deviceId
  getAppScreenPacketUrl(data) {
    const url = env.ctx + 'manage/api/wx/getAppScreenPacketUrl'
    return request.postForm(url, data)
  },
  // 根据出袋场景值获取h5出袋页面和设备id
  getSourceH5PacketUrl(param) {
    const url = env.ctx + 'manage/api/wx/getSourceH5PacketUrl'
    return request.postForm(url, param)
  },
  // 根据设备Id获取h5出袋页面
  getAppSubscribeScreenPacketUrl(param) {
    const url = env.ctx + 'manage/api/wx/getAppSubscribeScreenPacketUrl'
    return request.postForm(url, param)
  },
  // 添加推荐缓存
  updateAccountRecommendWxIdCache(param) {
    const url = env.ctx + 'manage/api/wx/updateAccountRecommendWxIdCache'
    return request.putJson(url, param)
  },
  // 小程序增粉事件
  uaInsertEvent(param) {
    const url = env.ctx + 'manage/api/wx/applet/insert/event'
    return request.postJson(url, param)
  },
  // 获取投放计划
  getAdvertisePlan(param) {
    const url = env.ctx + 'manage/api/wx/getAdvertisePlan'
    return request.postForm(url, param)
  },
  // 广告投放计划 - 接口链接获取
  parseTargetUrl(param) {
    const url = env.ctx + 'manage/api/wx/parse/targetUrl'
    return request.get(url, param)
  },
  // 领袋授权手机号弹窗记录
  packetoperationrecordInsert(param) {
    const url = env.ctx + 'manage/api/v1/packetoperationrecord/insert'
    return request.postJson(url, param)
  }
}
