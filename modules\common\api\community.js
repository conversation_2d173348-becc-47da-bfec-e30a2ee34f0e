import request from '@/common/util/request'
import env from '@/config/env'
import serverOptions from '@/config/env/options'
// 捕获request传递给serverOptions
serverOptions.catchRequest(request)
/**
 * 社区请求工具类，
 * 注意：API接口只做请求数据和格式化数据的事情，其他事务一概不处理
 */
export default {
    /**
     * 获取地址
     * @param param
     * @returns {*}
     */
    arealistArea(param) {
      const url = env.ctx + 'manage/api/area/listArea'
      return request.get(url, param)
    },
    // 获取粉丝根据userId（用于判断是否为社区粉丝）
    fansrecordGetFansrecordByUserid (param) {
        const url = env.ctx + 'dm/api/v1/fansrecord/get/fansrecord/by/userid'
        return request.get(url, param)
    },
    // 添加小程序粉丝
    fansrecordAddMinappSource (param) {
        const url = env.ctx + 'dm/api/v1/fansrecord/add/minapp/source'
        return request.postJson(url, param)
    },
    // 我的回复
    commoncollectlikesQueryMyCommentPage (param) {
        const url = env.ctx + 'dm/api/v1/commoncollectlikes/query/my/comment/page'
        return serverOptions.accompanyDoctorPost(url, param)
    },
    // 小葫芦我的回复
    commoncollectlikesQueryMyCommentGourdPage (param) {
        const url = env.ctx + 'dm/api/v1/commoncollectlikes/query/my/comment/page'
        return request.postJson(url, param)
    },
    // 企业微信群分页查询
    enterprisewechatgroupQueryPage (param) {
        const url = env.ctx + 'dm/api/v1/enterprisewechatgroup/query/page'
        return request.postJson(url, param)
    },
    // 企业微信群根据主键单一查询
    enterprisewechatgroupQueryOne (param) {
        const url = env.ctx + 'dm/api/v1/enterprisewechatgroup/query/one'
        return request.get(url, param)
    },
    // 社区小程序消息-评论,点赞
    noticelogQueryCommentPage (param) {
        const url = env.ctx + 'dm/api/v1/noticelog/query/comment/page'
        return serverOptions.accompanyDoctorPost(url, param)
    },
    // 通用记录接口
    applicationoperatelogInsert (param) {
        const url = env.ctx + 'dm/api/v1/applicationoperatelog/insert'
        return request.postJson(url, param)
    },
    // 通用记录接口
    applicationoperatelogInsert (param) {
        const url = env.ctx + 'dm/api/v1/applicationoperatelog/insert'
        return request.postJson(url, param)
    },
    // 新的banner接口
      applicationoperateV2logInsert (param) {
        const url = env.ctx + 'dm/api/v1/applicationoperatelogv2/insert'
        return request.postJson(url, param)
    },
    // 分享接口
    applicationoperatelogShare (param) {
        const url = env.ctx + 'dm/api/v1/applicationoperatelog/share'
        return request.postJson(url, param)
    },
    // 企业微信群或圈子-保存登记
    matterregisterInsert (param) {
        const url = env.ctx + 'dm/api/v1/matterregister/insert'
        return request.postJson(url, param)
    },
    // 获取圈子应用服务
    applicationserviceGetCircleApplication (param) {
        const url = env.ctx + 'dm/api/v1/applicationservice/get/circle/application'
        return request.get(url, param)
    },
    // 获取首页应用服务
    applicationserviceGetIndexApplication (param) {
        const url = env.ctx + 'dm/api/v1/applicationservice/get/index/application'
        return request.get(url, param)
    },
    // 我的收藏
    commoncollectlikesQueryMyCollectionPage (param) {
        const url = env.ctx + 'dm/api/v1/commoncollectlikes/query/my/collection/page'
        return serverOptions.accompanyDoctorPost(url, param)
    },
    // 小葫芦的收藏
    commoncollectlikesQueryMyCollectionGourdPage (param) {
        const url = env.ctx + 'dm/api/v1/commoncollectlikes/query/my/collection/page'
        return request.postJson(url, param)
    },
    // 获取粉丝根据accountId
    fansrecordGetFansrecordByAccountid (param) {
        const url = env.ctx + 'dm/api/v1/fansrecord/get/fansrecord/by/accountid'
        return request.get(url, param)
    },
    // 社区未读统计-别人回复消息
    noticelogGetCommunityUnreadStatistics (param) {
        const url = env.ctx + 'dm/api/v1/noticelog/get/community/unread/statistics'
        return request.get(url, param)
    },
    // 社区消息未读统计 批量修改为已读
    noticelogBatchUpdateRead (param) {
        const url = env.ctx + 'dm/api/v1/noticelog/batch/update/read'
        return request.postForm(url, param)
    },
    // 社区消息未读统计 全部修改为已读
    noticelogUpdateAllNoticeLog (param) {
      const url = env.ctx + 'dm/api/v1/noticelog/update/all/notice/log'
      return request.postForm(url, param)
    },
    // 社区小程序-我的-上方的统计
    commoncollectlikesGetMyStatistics (param) {
        const url = env.ctx + 'dm/api/v1/commoncollectlikes/get/my/statistics'
        return serverOptions.accompanyDoctorGet(url, param)
    },
    // 社区小程序-我的-上方的统计-小葫芦的
    commoncollectlikesGetMyGourdStatistics (param) {
        const url = env.ctx + 'dm/api/v1/commoncollectlikes/get/my/statistics'
        return request.get(url, param)
    },
    // 更新粉丝信息
    fansrecordInfoUpdate (param) {
        const url = env.ctx + 'dm/api/v1/fansrecord/info/update'
        return request.putJson(url, param)
    },
    // 绑定病友圈用户
    usertenantrecordBindFans(param) {
        const url = env.ctx + 'auth/api/v1/usertenantrecord/bind/fans'
        return request.postForm(url, param)
    },
    // 更新粉丝注册状态
    fansrecordUpdateRegisterStatus(param) {
        const url = env.ctx + 'dm/api/v1/fansrecord/update/register/status'
        return request.putForm(url, param)
    },
    // 帖子没有被邀请过的医生（随机获取list）
    fansrecordNotInviteDoctorList(param) {
        const url = env.ctx + 'dm/api/v1/fansrecord/notInvite/doctor/list'
        return request.get(url, param)
    },
    // 已邀请评论记录
    cfpostinvitelogQueryPage(param) {
        const url = env.ctx + 'dm/api/v1/cfpostinvitelog/query/page'
        return request.postJson(url, param)
    },
    // 已邀请评论 未回复数量
    cfpostinvitelogBeInviteCount(param) {
        const url = env.ctx + 'dm/api/v1/cfpostinvitelog/beInvite/count'
        return request.get(url, param)
    },
    // 我的点赞
    commoncollectlikesQueryMyLikePage (param) {
        const url = env.ctx + 'dm/api/v1/commoncollectlikes/query/my/like/page'
        return serverOptions.accompanyDoctorPost(url, param)
    },
    // 小葫芦我的点赞
    commoncollectlikesQueryMyLikeGourdPage (param) {
        const url = env.ctx + 'dm/api/v1/commoncollectlikes/query/my/like/page'
        return request.postJson(url, param)
    },
    // 发起提现
    accompanypayoutInsert (param) {
        const url = env.ctx + 'dm/api/v1/accompanypayout/insert'
        return request.postJson(url, param)
    },
    // 获取新的点赞和收藏
    postmessageStatisticsAccountId (param) {
      const url = env.ctx + 'dm/api/v1/postmessage/statistics/accountId'
      return request.get(url, param)
    },
    // 根据被关注者的accountId绑定关注
    userFansrecordfollowerBinding (param) {
      const url = env.ctx + 'dm/api/v1/fansrecordfollower/binding/' + param
      return request.postForm(url)
    },
    // 根据被关注者的accountId解除关注
    userFansrecordfollowerUnbinding (param) {
      const url = env.ctx + 'dm/api/v1/fansrecordfollower/unbinding/' + param.accountId
      return request.postForm(url)
    },
    // 删除帖子
    userPostmessageDeleteBatch (param) {
      const url = env.ctx + 'dm/api/v2/postmessage/delete/batch/'+param.id
      return request.delete(url)
    },
    // 我发的帖子
    userPostmessageMyPostPage (param) {
      const url = env.ctx + 'dm/api/v1/postmessage/my/post/page'
      return request.postJson(url, param)
    },

    // 查询是否关注他人
    userFansrecordfollowerQueryFollow (param) {
      const url = env.ctx + 'dm/api/v1/fansrecordfollower/query/follow/'+ param.accountId
      return request.postForm(url)
    },
    // 帖子更新可见范围
    userPostmessageUpdateScope (param) {
      const url = env.ctx + 'dm/api/v1/postmessage/update/scope'
      return request.postForm(url,param)
    },
    // 个人主页通用二维码
    userCommonqrcodeInsert (param) {
      const url = env.ctx + 'dm/api/v1/commonqrcode/insert'
      return request.postJson(url,param)
    },
    // 个人主页通用二维码 - 根据主键单一查询
    userCommonqrcodeQueryOne (param) {
      const url = env.ctx + 'dm/api/v1/commonqrcode/query/one'
      return request.get(url,param)
    },
}
