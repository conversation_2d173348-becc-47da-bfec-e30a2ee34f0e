<template>
  <view class='pharmacy-detail' :style="{
    paddingTop:headerTop + 'px'
  }">
    <title-header  :headerobj='headerobj' @init='initHeader' @getlist="handleGetlist"></title-header>
    <view class="add-content" v-if="pharmacyDetail">
      <view class="add-drug">
        <view class="title">{{ pharmacyDetail.productName }}</view>
        <view :class="pharmacyDetail.executeStatus == 2 ?'close active' : 'close'">{{ pharmacyDetail.executeStatus == 2 ? '进行中' : '已关闭' }}</view>
        <view class="drug-item">
          <view class="name">{{labelValue ? '服用频次' : '用药频次'}}</view>
          <view class="value">每日 {{ pharmacyDetail.times }} 次</view>
        </view>
        <view class="drug-item">
          <view class="name">{{labelValue ? '服用时间' : '用药时间'}}</view>
          <!-- <view class="value">{{ pharmacyDetail.remindTime.replaceAll('-','，') }}</view> -->
          <view class="value">{{ filterRemindTime }}</view>
        </view>
        <view class="drug-item">
          <view class="name">单次用量</view>
          <view class="value">{{ pharmacyDetail.numOfTake }}</view>
        </view>
        <view class="drug-item">
          <view class="name">提醒天数</view>
          <view class="value">{{ pharmacyDetail.medicationDays }} 天</view>
        </view>
        <view class="drug-item">
          <view class="name">备注</view>
          <view class="value">{{ pharmacyDetail.remark || '暂无' }}</view>
        </view>
        <view class="send-inform" v-if="pharmacyDetail.executeStatus == 2">
          <view class="send-inform-l">发送消息通知</view>
          <view class="send-inform-r"><switch color="#2aba99" :checked="pharmacyDetail.notify == 1" @change="handleChage(pharmacyDetail,$event)" /></view>
        </view>
      </view>
      <view class="plan-content" v-if="pharmacyDetail.executeStatus == 2">
        <button  
          :class="currentIndex == index ? 'active plan-btn' : 'plan-btn'"
          v-for="(item,index) in btnList" 
          :key="index" 
          @click="handleUpdatePlan(index)"
        >
          {{ item.name }}
        </button>
      </view>
      <button class="open-btn" @click="handleClickOpen" v-else>再次开启</button>
    </view>
    <uni-popup ref="swiperPopup" id="swiperPopup" type="center">
      <view class="datail">
        <view class="title">确定要关闭计划吗?</view>
        <view class="detail-content">
          <button class="cancel" @click="handleCancel">取消</button>
          <button class="confirm" @click="handleClose">关闭</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import uniPopup from '@/components/uni/uni-popup' 
  import titleHeader from '@/modules/pharmacy/pharmacy-cyclopedia/components/title-header/index.vue'
  export default {
    components:{
      uniPopup,
      titleHeader,
    },
    data(){
      return{
        // detailData:{},
        btnList:[
          {id:1,name:'修改计划'},
          {id:2,name:'关闭计划'},
        ],
        currentIndex:0,
        pharmacyDetail:null,  
        drugDetial:null,
        headerobj:{
          headBgColor:'#fff',
          titleType:"txt",
          titleTxt:"用药详情",
          currentIndex:0,
          contentColor:"#000",
          borderColor:"#000"
        },
        headerTop:55,
      }
    },
    computed: {
      filterRemindTime(){
        if(this.pharmacyDetail?.remindTimeList.length){
          return this.pharmacyDetail.remindTimeList.map(item=>(item.remindTime)).join('，')
        }
      },
      labelValue() {
        let flag = false
        // if(this.drugDetial?.productId == '2031448999104024578' || this.drugDetial?.productId == '2031353194431111174'){
        if(this.drugDetial?.drugType == 2){
          flag = true
        }
        return flag 
      },
    },
    mounted(options){
      if (this.$Route.name !== 'PharmacyDetail') return
      const query = this.$validate.isNull(this.$Route.query) ? options : this.$Route.query
      this.drugDetial = query 
      // if(this.drugDetial?.productId == '2031353194431111174' || this.drugDetial?.productId == '2031448999104024578'){
      if(this.drugDetial?.drugType == 2){
        this.headerobj.titleTxt = '服用详情'
      }
      if(this.drugDetial.id){
        this.pharmacyRemindQueryOne()
      }
    },
    methods:{
      // #ifdef MP-WEIXIN
      handleClickTrack(type){
        getApp().globalData.sensors.track("PopupClick",
          {
            'page_name' : '用药提醒详情页面',
            'popup_id' : 'swiperPopup',
            'popup_name' : '确定关闭计划弹窗',
            'click_type' : type == 1 ? '进入弹窗' : '取消弹窗',
          }
        ) 
      },
      // #endif
      handleGetlist(){
        uni.$emit("backPage",{}) //返回上一页刷新列表数据
      },

      initHeader(height){
        this.headerTop = height
      },
      // 数据回显
      pharmacyRemindQueryOne(){
        this.$api.drugBook.pharmacyRemindQueryOne({id:this.drugDetial.id}).then(res=>{
          this.pharmacyDetail = {...res.data,remindTimeList:JSON.parse(res.data.remindTime)}
        })
      },

      async handleClose(){
        let params = {
          id:this.drugDetial.id,
          executeStatus:4
        }
        const res = await this.$api.drugBook.pharmacyRemindDelete(params)  
        if(res.data){
          uni.showToast({
          	title: '已关闭计划',
          	icon:'none'
          });
          this.pharmacyRemindQueryOne()
          this.$emit('handleclose')
          this.$refs.swiperPopup.close()
          // #ifdef MP-WEIXIN
          this.handleClickTrack(2)
          // #endif
        }
      },

      async handleChage(item,e){
        await this.$api.drugBook.pharmacyRemindSendMessage({
          id:item.id,
          notify:e.detail.value ? 1 : 2
        })
      },

      handleClickOpen(){
        // this.$emit('handleDetailData',this.drugDetial,1)
        console.log(this.drugDetial,'this.drugDetial0000')
        this.$navto.push('PharmacyAdd',this.drugDetial)
      },

      handleUpdatePlan(index){
        // this.currentIndex = index
        if(index == 1){
          this.$refs.swiperPopup.open()
          // #ifdef MP-WEIXIN
          this.handleClickTrack(1)
          // #endif
        } else {
          this.handleClickOpen()
        }
      },

      handleCancel(){
        this.$refs.swiperPopup.close()
      }

    },
 }
</script>

<style lang='scss' scoped>
.pharmacy-detail{
  height: calc(100vh - 30rpx);
  // padding: 30rpx;
  background-color: #f0f2f2;
  .add-content{
    padding: 30rpx;
    .add-drug{
      padding: 50rpx 0 40rpx 40rpx;  
      background-color: #fff;  
      border-radius: 20rpx;
      .title{
        color:#333;
        font-size: 36rpx;
        font-weight: 700;
      }
      .close{
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #ccc;
        padding: 4rpx 10rpx;
        width: 100rpx;
        border-radius: 13rpx;
        margin: 20rpx 0 40rpx;
        font-weight: 700;
        color:#999;
      }
      .active{
        color: #2aba99;
        border-color: #2aba99;
      }
      .drug-item{
        display: flex;
        margin-top: 20rpx;
        .name{
          width: 120rpx;
          margin-right: 30rpx;
        }
        .value{
          color:#999;
        }
      }
      .send-inform{
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-top: 30rpx;
        margin-top: 30rpx;
        border-top: 1rpx solid #eee;
        .send-inform-r{
          padding-right: 20rpx;
        }
      }
    }
    .open-btn{
      height: 100rpx;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 100rpx;
      border-radius: 100rpx;
      background-color: #2aba99;
      color:#fff;
      font-weight: 700;
      font-size: 30rpx;
      &::after{
        border: none !important;
      }
    }
    .plan-content{
      display: flex;
      width: 100%;
      .plan-btn{
        height: 100rpx;
        // width: 100%;
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 100rpx;
        border-radius: 100rpx;
        background-color: #fff;
        color:#000;
        font-weight: 600;
        font-size: 30rpx;
        margin-right: 20rpx;
        &::after{
          border: none !important;
        }
      }
      :last-child{
        margin-right: 0;
      }
      .active{
        background-color: #2aba99;
        color:#fff;
      }
    }
  }

  .datail{
    width: 65vw;
    padding: 50rpx;
    background-color: #fff;
    border-radius: 25rpx;
    .title{
      text-align: center;
    }
    .detail-content{
      display: flex;
      margin-top: 50rpx;
      .cancel{
        flex: 1;
        background-color: #f5f5f5;
        color:#000;
        margin-right: 30rpx;
        border-radius: 20rpx;
        &::after{
          border: none !important;
        }
      }
      .confirm{
        flex: 1;
        background-color: #39cd80;
        color:#fff;
        border-radius: 20rpx;
        &::after{
          border: none !important;
        }
      }
    }
  }
}
</style>