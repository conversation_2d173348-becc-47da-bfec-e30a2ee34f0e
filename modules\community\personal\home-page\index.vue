<template>
  <page>
    <view slot="content" class="body-main">
      <view class="my-data" :style="{height:myDataHeight+'px'}">
      <!-- <view class="my-data"> -->
        <uni-nav-bar
          class="header-nav-bar"
          @clickLeft="back"
          color="#1d2029"
          :border="false"
          :left-icon="isAliPay ? '' : 'left'"
          fixed
          :backgroundColor="'rgba(0,0,0,0)'"
          statusBar
        >
          <!-- :backgroundColor="scrollTop > 50 ? '#fff' : 'rgba(0,0,0,0)'" -->
        <!-- <view v-if="scrollTop > 50" class="header-search-box header-title">
          个人主页
        </view> -->
        </uni-nav-bar>
        <view
          class="my-bg-img"
          :style="{
            backgroundImage:'url(' + file_ctx + 'static/image/business/hulu-v2/icon-personal-home-new-bg.png)',
            height:myDataHeight+'px'
          }"
        >
          <view class="my-user" id="user-info-id">
            <view class="user-head">
              <view class="user-img-l">
                <image class="my-img" :src="infoObj.headPathNew"></image>
                <!-- <image class="my-addv" :src="file_ctx +"></image> -->
                <template v-if="infoObj.isAddV == 1">
                  <image
                    v-if="infoObj.vType == 2"
                    class="head-v-icon"
                    :src="
                      file_ctx +
                      'static/image/system/avatar/icon-user-v-e-new.png'
                    "
                    mode="aspectFill"
                  ></image>
                  <image
                    v-else-if="infoObj.vType == 1"
                    class="head-v-icon"
                    :src="
                      file_ctx + 'static/image/system/avatar/icon-user-v-new.png'
                    "
                    mode="aspectFill"
                  ></image>
                </template>
              </view>
              <view class="user-img-info">
                <view class="title">{{ infoObj ? infoObj.nickName : '' }}</view>
                <view class="info" v-if="infoObj.jobName">{{ infoObj.jobName }}</view>
                <image class="user-code" @click="handleCreateCode" :src="file_ctx + 'static/image/business/hulu-v2/icon-user-home-code-new-bg.png'"></image>
              </view>
            </view>
            <view class="user-bott">
              <moreLinesDivide v-if="infoObj.intro" class="good-at" :setUpdateCount="setUpdateCount" :isShowEdit="isShowUserVisible" @handleSendEdit="handleSendEdit" @clickIsHide="handleClickIsHide" :foldShowStyle="false" :line="3" :dt="infoObj.intro"></moreLinesDivide>
              <view class="text-empty" v-else-if="!infoObj.intro && isShowUserVisible" @click="handleSendEdit">点击这里，填写简介</view>
              <view class="my-all-attention">
                <view class="attention-item">
                  <view class="attention-l">
                    <view class="item-l" v-for="item in userDetailList" :key="item.id">
                      <view class="item-num">{{ item.value }}</view>
                      <view class="item-name">{{ item.name }}</view>
                    </view>
                  </view>
                  <view class="item-r user-edit" v-if="isShowUserVisible" @click="handleClickJumpUser">编辑资料</view>
                  <view class="item-r user-attention" v-else-if="isUserAttention" @click="$refs.carouselPopup.open()">已关注</view>
                  <view class="item-r" v-else @click="handleClickBindingAttention"><image :src="file_ctx + 'static/image/business/hulu-v2/icon-user-attention-add-new.png'"></image> 关注</view>
                </view>
              </view>
              <view class="list-title">{{ isShowUserVisible ? '我' : 'Ta'}}的动态</view>
            </view>
          </view>
        </view>
      </view>
      <scroll-refresh
        class="my-scroll-refresh"
        :isShowEmptySwitch="true"
        :fixed="false"
        :isAbsolute="false"
        :up="upOption"
        :down="downOption"
        @returnFn="returnFn"
        @scrollInit="scrollInit"
        @scroll="scroll"
      >
        <view class="scroll-view-main">
          <view class="main-list">
            <view class="list-content">
              <nui-list
                class="nui-list"
                :indexlist="indexlist"
                @cateClick="cateClick"
                :posts-params="postsParams"
                :isShowBtn="isShowBtn"
              >
                <!-- #ifdef MP-WEIXIN -->
                <view class="list-head-suffix" v-for="(data,index) in indexlist" :key="index" slot="head-suffix{{index}}" @click="handleShowPermPopup(data)">
                <!-- #endif -->
                <!-- #ifndef MP-WEIXIN -->
                <view class="list-head-suffix" slot="head-suffix" slot-scope="{ data }" @click="handleShowPermPopup(data)">
                <!-- #endif -->
                  <image class="user-perm-myself" :src="file_ctx + 'static/image/business/hulu-v2/icon-user-perm-myself.png'"></image>
                  <view class="text">{{ data.scope == 2 ? '仅自己可见' : data.putawayStatus == 2 ? '已下架' : '' }}</view>
                </view>
              </nui-list>
            </view>
          </view>
        </view>
      </scroll-refresh>
    <!-- 提示用户关注弹窗 -->
    <uni-popup ref="carouselPopup" type="center">
      <view class="carousel-content">
        <view class="title">提示</view>
        <view class="info">确定不再关注</view>
        <view class="carousel-btn">
          <view class="btn btn-l" @click="handleClickConfirm(2)">取消</view>
          <view class="btn btn-r" @click="handleClickConfirm(1)">确定</view>
        </view>
      </view>
    </uni-popup>

    <!-- 设置帖子权限弹窗 -->
    <uni-popup ref="userPermPopup" type="bottom">
      <view class="user-perm-content">
        <view class="user-perm-item" v-for="item in userPermList" :key="item.id" @click="handleClickPerm(item)">
          <image class="perm-item-l" :src="file_ctx + item.url"></image>
          <view class="perm-item-name">{{ item.name }}</view>
        </view>
        <view class="user-perm-btn" @click="handleClickPermCancel">取消</view>
      </view>
    </uni-popup>
    
    <!-- 用户个人主页二维码 -->
    <uni-popup ref="userHomePopup" type="center">
      <view class="user-home-popup">
        <view class="header">
          <view class="header-l">
            <image class="user-profile" :src="infoObj.headPathNew" mode="aspectFill"></image>
            <template v-if="infoObj.isAddV == 1">
              <image v-if="infoObj.vType == 2" class="head-v-icon" :src="file_ctx +'static/image/system/avatar/icon-user-v-e-new.png'" mode="aspectFill"></image>
              <image v-else-if="infoObj.vType == 1" class="head-v-icon" :src="file_ctx + 'static/image/system/avatar/icon-user-v-new.png'" mode="aspectFill"></image>
            </template>
          </view>
          <view class="header-r">
            <view class="nickname">{{ infoObj ? infoObj.nickName : '' }}</view>
            <view class="info" v-if="infoObj.jobName">{{ infoObj.jobName }}</view>
          </view>
        </view>
        <view class="content"><image :src="commonQrcodeObj.qrcodePath"></image></view>
        <view class="content-title">扫码了解我的个人主页吧</view>
      </view>
      <view class="download-picture">
        <button @click="handleDownloadPicture">下载图片</button>
      </view>
    </uni-popup>

    <!-- 个人二维码绘制开始 -->
    <view class="my-user-home-popup" id="answer-canvas" v-show="isShowHomeQrcode">
      <view class="header draw_canvas">
        <view class="header-l draw_canvas">
          <image 
            class="user-profile draw_canvas" 
            mode="aspectFill"
            data-type="radius-image" 
            data-delay="1"
            :data-url="infoObj.headPathNew"
            :src="infoObj.headPathNew" 
          >
          </image>
          <template v-if="infoObj.isAddV == 1">
            <image 
              v-if="infoObj.vType == 2"
              class="head-v-icon draw_canvas"
              mode="aspectFill"
              data-type="image" 
              data-delay="1"
              :data-url="file_ctx +'static/image/system/avatar/icon-user-v-e-new.png'"
              :src="file_ctx +'static/image/system/avatar/icon-user-v-e-new.png'"
            >
            </image>
            <image 
              v-else-if="infoObj.vType == 1"
              class="head-v-icon draw_canvas"
              mode="aspectFill"
              data-type="image" 
              data-delay="1"
              :data-url="file_ctx +'static/image/system/avatar/icon-user-v-e-new.png'"
              :src="file_ctx + 'static/image/system/avatar/icon-user-v-new.png'"
            >
            </image>
          </template>
        </view>
        <view class="header-r draw_canvas">
          <view class="nickname draw_canvas" data-delay="1" data-type="text" :data-text="infoObj ? infoObj.nickName : ''">{{ infoObj ? infoObj.nickName : '' }}</view>
          <view class="info draw_canvas" data-delay="1" data-type="text" :data-text="infoObj.jobName" v-if="infoObj.jobName">{{ infoObj.jobName }}</view>
        </view>
      </view>
      <view class="content draw_canvas"><image class="draw_canvas" data-type="image" data-delay="1" :data-url="commonQrcodeObj.qrcodePath" :src="commonQrcodeObj.qrcodePath"></image></view>
      <view class="content-title draw_canvas" data-delay="1" data-type="text" data-text="扫码了解我的个人主页吧">扫码了解我的个人主页吧</view>
    </view>

    <!-- 绘制分享码canvas -->
    <canvas canvas-id="answerCanvas" class="answerCanvas" :style="{'position':'absolute','top':'-99999px','width':`${canvasWidth}`+'px','height':`${canvasHeight}`+'px'}"></canvas>

    </view>
  </page>
</template>

<script>

import defaultImg from '@/components/basics/default-avatar/index'
import StatusBarHeight from '@/components/business/module/status-bar-height/index'
import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'
import nuiList from '@/components/community/nui-list/nui-list.vue'
import uniIcons from '@/components/uni/uni-icons/uni-icons.vue'
import search from '@/components/basics/form/search'
import moreLinesDivide from '@/components/business/module/more-lines-divide/more-lines-divide'
import uniPopup from '@/components/uni/uni-popup'
import Wxml2Canvas from 'wxml2canvas';
import { mapState } from 'vuex'
export default {
  components: {
    StatusBarHeight,
    defaultImg,
    uniNavBar,
    nuiList,
    uniIcons,
    search,
    moreLinesDivide,
    uniPopup
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $timePlugin: this.$timePlugin,
      $static_ctx: this.$static_ctx,
      homeAccountId: '',
      infoObj: {},
      config: {
        avatar: {
          widthHeightAuto: true,
          itemClass: {
            width: '158rpx',
            height: '158rpx',
            display: 'inline-block',
          }
        }
      },
      mescroll: null, // mescroll实例对象
      downOption: {
        auto: false // 不自动加载
      },
      upOption: {
        auto: false, // 不自动加载
        empty: {
          top: 0,
          zIndex: 999,
        }
      },
      isInit: false, // 列表是否已经初始化
      indexlist: [],
      defaultAvatar: this.$static_ctx + 'image/system/avatar/icon-default-avatar.png',
      scrollTop: 0,
      isShowBtn:true,
      likeNumObj:{},//点赞和收藏数据
      userDetailList:[
        {id:1,name:'关注',value:0,},
        {id:2,name:'粉丝',value:0},
        {id:3,name:'获赞',value:0},
        {id:4,name:'收藏',value:0},
      ],
      menuButtonInfo: uni.getMenuButtonBoundingClientRect(),
      myDataHeight:252,
      userPermList:[
        {id:1,name:'公开可见',url:'static/image/business/hulu-v2/icon-user-public-visible.png'},
        {id:2,name:'仅自己可见',url:'static/image/business/hulu-v2/icon-user-myself-visible.png'},
        {id:3,name:'重新编辑',url:'static/image/business/hulu-v2/icon-user-perm-edit.png'},
        {id:4,name:'删除',url:'static/image/business/hulu-v2/icon-user-perm-delete.png'}
      ],
      postDetailId:'',
      isShowPostEdit:false,
      userIntroduceFlag:false,
      isUserAttention:false,
      setUpdateCount:0,
      commonQrcodeObj:{},
      isShowHomeQrcode:false,
      canvasWidth:null,
      canvasHeight:null,
    }
  },
  computed: {
    ...mapState('user', {
      accountId: state => state.accountId,
    }),
    postsParams() {
      return {
        mode: 'personal',
        accountId: this.homeAccountId
      }
    },
    isShowEdit(){
      return this.$common.getKeyVal('user', 'accountId') === this.accountId
    },
    isShowUserVisible(){
      return this.$common.getKeyVal('user', 'accountId') === this.homeAccountId
    },
  },
  onLoad(options) {
    const query = this.$validate.isNull(this.$Route.query) ? options : this.$Route.query
    if(query?.isShowBtn){
      this.isShowBtn = JSON.parse(query.isShowBtn)
    } 

    this.homeAccountId = query?.homePageAccountId

    if(query?.scene){
      this.homeAccountId = query?.scene?.split('=')[1] || ''
    }


    this.$nextTick(() => {
      this.userFansrecordfollowerQueryFollow()
      this.getFansrecordGetFansrecordByAccountid()  
      this.getLikeNumAndCollectNum()
      this.init()
    })
  },
  onShow(){
    if(this.isShowPostEdit){
      this.handleClickIsHide()
      setTimeout(() => { 
        this.init()
      }, 500);
    }
    this.setUpdateCount += 1
    if(this.userIntroduceFlag){
      this.getFansrecordGetFansrecordByAccountid() 
      setTimeout(() => { 
        this.init()
      }, 200)
    }
  },
  onShareAppMessage (res) {
    return {
      title: this.infoObj.nickName ? `分享${this.infoObj.nickName}的个人主页` : '个人主页', //分享的名称
      path: `modules/community/personal/home-page/index?homePageAccountId=${this.homeAccountId || this.accountId}`,
      imageUrl: this.infoObj.headPathNew || '',
    }
  },
  onShareTimeline(){
    return {
      title: this.infoObj.nickName ? `分享${this.infoObj.nickName}的个人主页` : '个人主页',
      path: `modules/community/personal/home-page/index`,
      query:`homePageAccountId=${this.homeAccountId || this.accountId}`,
      imageUrl: this.infoObj.headPathNew || '',
    }
  },
  methods: {
    // 下载二维码图片
    handleDownloadPicture(){
      let that = this
      // 首先检查权限
      uni.getSetting({
        success(res) {
          if (!res.authSetting['scope.writePhotosAlbum']) { 
            // 请求授权
            uni.authorize({
              scope: 'scope.writePhotosAlbum',
              success() {
                that.saveImage()
              },
              fail() {
                // 用户拒绝授权，引导用户去设置页手动打开权限
                uni.showModal({
                  title: '授权提示',
                  content: '需要获取添加图片权限，请到小程序设置页面打开授权',
                  success(modalRes) {
                    if (modalRes.confirm) {
                      uni.openSetting();
                    }
                  }
                });
              }
            });
          } else {
            that.saveImage()
          }
        }
      });
    },

    // 下载图片
    saveImage() {
      this.isShowHomeQrcode = true
      this.$nextTick(()=>{
        try {
          uni.createSelectorQuery().select('#answer-canvas').boundingClientRect().exec((res)=>{
            let { width,height } = res[0]
            this.canvasWidth = width
            this.canvasHeight = height
            this.draw(width,height)
          })
        } catch(error){
          console.log(error)
        }
      })
    },

    draw(width,height){
      let that = this
      uni.showLoading({
        title:"加载中...",
        mask:true
      })
      let wxcanvas = new Wxml2Canvas({
        element: 'answerCanvas', // canvas节点的id,
        obj: that,
        width: width, // 宽 自定义
        height: height, // 高 自定义
        progress(percent) {},
        finish(url) {
          uni.hideLoading()
          setTimeout(()=>{
            that.isShowHomeQrcode = false
            uni.saveImageToPhotosAlbum({
              filePath: url,
              success: (res) => {
                uni.showToast({
                  title: '下载成功',
                  icon: 'success'
                })
              },
            })
            that.$refs.userHomePopup.close()
          },300)
        },
        error(res) {
          console.log(res);
          that.isShowHomeQrcode = false
          uni.hideLoading()
          // 画失败的原因
        }
      })
      let data = {
        //直接获取wxml数据
        list: [{
          type: 'wxml',
          class: '.draw_canvas',  // answer_canvas这边为要绘制的wxml元素跟元素类名， answer_draw_canvas要绘制的元素的类名（所有要绘制的元素都要添加该类名）
          limit: '.my-user-home-popup', // 这边为要绘制的wxml元素跟元素类名,最外面的元素
          x: 0,
          y: 0
        }]
      }
        //传入数据，画制canvas图片
      wxcanvas.draw(data);
    },

    // 点击个人生成二维码
    async handleCreateCode(){
      // 如果已有二维码数据，直接打开弹窗，不再调用接口
      if(this.commonQrcodeObj && Object.keys(this.commonQrcodeObj).length > 0){
        this.$refs.userHomePopup.open()
        return
      }
      let params = {
        name:this.infoObj?.nickName || '',
        businessType:1,
        businessId:this.homeAccountId,
        path:'modules/community/personal/home-page/index',
      }
      const res = await this.$api.community.userCommonqrcodeInsert(params)
      if(res.data !==""){
        this.userCommonqrcodeQueryOne(res.data.id)
      }
      this.$refs.userHomePopup.open()
    },

    async userCommonqrcodeQueryOne(id){
      const res = await this.$api.community.userCommonqrcodeQueryOne({id})
      if(res.data !==""){
        this.commonQrcodeObj = res.data
      }
    },

    // 点击关注
    handleClickBindingAttention(){
      this.userFansrecordGetFansrecordByAccountid()
    },
    handleClickJumpUser(){
      this.userIntroduceFlag = true
      this.navtoGo('User')
    },
    handleShowPermPopup(data){
      if(!this.isShowUserVisible)return
      this.postDetailId = data.id
      this.$refs.userPermPopup.open()
    },
    // 弹窗子项点击
    async handleClickPerm(item){
      if(item.id == 1){
        await this.userPostmessageUpdateScope(1)
        setTimeout(() => { 
          this.init()
        }, 1000)
      } else if(item.id == 2){
        await this.userPostmessageUpdateScope(2)
        setTimeout(() => { 
          this.init()
        }, 1000)
      } else if(item.id == 3){
        this.isShowPostEdit = true
        this.navtoGo('PostsEdit',{id:this.postDetailId})
      } else if(item.id == 4){
        await this.$api.community.userPostmessageDeleteBatch({id:this.postDetailId})
        uni.showToast({title:'删除成功',icon:'none'})
        this.init()
      }
      this.handleClickPermCancel()
    },
    handleClickPermCancel(){
      this.$refs.userPermPopup.close()
    },

    //绑定关注
    async userFansrecordGetFansrecordByAccountid(){
      await this.$api.community.userFansrecordfollowerBinding(this.homeAccountId == 'undefined' ? this.accountId : this.homeAccountId)
      uni.showToast({title:'关注成功',icon:'none'})
      this.userFansrecordfollowerQueryFollow()
      this.getLikeNumAndCollectNum()
    },

    async handleClickConfirm(type){
      if(type == 1){
        await this.$api.community.userFansrecordfollowerUnbinding({accountId:this.homeAccountId == 'undefined' ? this.accountId : this.homeAccountId})
        uni.showToast({title:'取消关注成功',icon:'none'})
        this.userFansrecordfollowerQueryFollow()
        this.getLikeNumAndCollectNum()
      }
      this.$refs.carouselPopup.close()
    },
    handleSendEdit(){
      this.userIntroduceFlag = true
      let params = {
        nickName:this.infoObj.nickName,
        headPic: this.infoObj.headPath,
        headPath: this.infoObj.headPath,
        sex: this.infoObj.sex,
        intro:this.infoObj.intro
      }
      this.navtoGo('UserIntroduce',params)
    },
    handleClickIsHide(){
      this.$nextTick(()=>{
        this.getElementHeight()
      })
    },
    getElementHeight() {
      let query = uni.createSelectorQuery().in(this);
      query.select('.my-user').boundingClientRect(data => {
        if (data) {
          this.myDataHeight = data.height + data.top
        }
      }).exec();
    },
    scroll(e) {
      this.scrollTop = e.detail.scrollTop
    },
    searcFn() {
      this.$navto.push('CommonSystemSearch', { accountId: this.homeAccountId })
    },
    cateClick(data) {
      this.navtoGo('Circle', { cid: data.circleClassifyId })
    },
    navtoGo(url, obj = {}) {
      console.log('navtoGo', url, obj)
      this.$navto.push(url, obj)
    },
    back() {
      this.$navto.back(1)
    },

    //查询是否关注
    async userFansrecordfollowerQueryFollow(){
      const res = await this.$api.community.userFansrecordfollowerQueryFollow({accountId:this.homeAccountId == 'undefined' ? this.accountId : this.homeAccountId})
      this.isUserAttention = res.data
    },
    
    // 范围公开还是可见
    async userPostmessageUpdateScope(scope){
      const res = await this.$api.community.userPostmessageUpdateScope({id:this.postDetailId,scope}) //scope 范围：1公开 2仅自己可见
    },

    async getFansrecordGetFansrecordByAccountid() {
      const param = {
        accountId: this.homeAccountId == 'undefined' ? this.accountId : this.homeAccountId
      }
      const res = await this.$api.community.fansrecordGetFansrecordByAccountid(param)
      this.infoObj = {
        ...res.data,
        beLikeNumberText: this.$common.bigNumberTransform(res.data.beLikeNumber || 0),
        beCollectNumberText: this.$common.bigNumberTransform(res.data.beCollectNumber || 0),
        headPathNew:res.data.headPath ? this.file_ctx + res.data.headPath : this.$static_ctx + 'image/system/avatar/icon-default-avatar.png'
      }
      setTimeout(()=>{
        this.getElementHeight(true)
      },500)
    },
    async getLikeNumAndCollectNum(){
      const param = {
        accountId: this.homeAccountId == 'undefined' ? this.accountId : this.homeAccountId
      }
      const res = await this.$api.community.postmessageStatisticsAccountId(param)
      const nameToDataKey = {
        '关注': 'followNum',
        '粉丝': 'fansNum',
        '获赞': 'likeNum',
        '收藏': 'collectNum'
      }

      this.userDetailList.forEach(item => {
        const dataKey = nameToDataKey[item.name]
        if (dataKey && res.data[dataKey] !== undefined) {
          item.value = this.$common.bigNumberTransform(res.data[dataKey])
        }
      })
      // this.likeNumObj = {
      //   ...res.data,
      //   beLikeNumberText: this.$common.bigNumberTransform(res.data.likeNum || 0),
      //   beCollectNumberText: this.$common.bigNumberTransform(res.data.collectNum || 0)
      // }
    },

    init() {
      this.$nextTick(() => {
        this.mescroll.triggerDownScroll()
      })
    },
    scrollInit(scroll) {
      scroll.optUp.page.num = 1
      scroll.optUp.page.size = 10
      this.mescroll = scroll
    },
    returnFn(obj) {
      const that = this
      setTimeout(function () {
        let params = {
          current: obj.pageNum,
          size: obj.pageSize,
          condition: {
            accountId: that.homeAccountId == 'undefined' ? that.accountId : that.homeAccountId,
            // orderByPutawayTime: 1
          }
        }
        // that.$ext.community.postmessageQueryAppPage(params).then(res => {
        that.$api.community.userPostmessageMyPostPage(params).then(res => {
          let data = res.data.records.map(item=>({...item,intro:item.intro ? item.intro : item.content,content:item.content ? item.content : item.intro,headPath:item.headPath ? item.headPath : 'image/system/avatar/icon-default-avatar.png'})) || []
          // data = data.map(item => {
          //     return {
          //       ...item,
          //     }
          // })
          if (obj.pageNum === 1) {
            that.indexlist = []
          }
          that.indexlist = [...that.indexlist, ...data]
          obj.successCallback && obj.successCallback(data)
        })
      }, that.$constant.noun.scrollRefreshTime)

    },
  }
}
</script>

<style lang="scss" scoped>
.main-list {
  padding: 0rpx 40rpx 0;
  background: #fff;
}
.user-home-popup,.my-user-home-popup{
  padding: 48rpx 40rpx 0;
  background-color: #fff;
  width: 654rpx;
  height: 738rpx;
  background: #FFFFFF;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  box-sizing: border-box;
  .header{
    display: flex;
    align-items: center;
    .header-l{
      position: relative;
      .user-profile{
        width: 96rpx;
        height: 96rpx;
        border-radius: 50%;
      }
      .head-v-icon {
        position: absolute;
        width: 32rpx;
        height: 32rpx;
        right: -6px;
        bottom: 8px;
      }
    }
    .header-r{
      flex: 1;
      margin-left: 32rpx;
      .nickname{
        font-size: 32rpx;
        color: #333333;
      }
      .info{
        margin-top: 12rpx;
        font-size: 24rpx;
        color: #777777;
      }
    }
  }
  .content{
    width: 388rpx;
    height: 388rpx;
    margin: 40rpx auto;
    image{
      width: 100%;
      height: 100%;
    }
  }
  .content-title{
    text-align: center;
    font-size: 32rpx;
    color: #333333;
  }
}
.download-picture{
  margin-top: 32rpx;
  button{
    display: flex;
    align-items: center;
    justify-content: center;
    width: 544rpx;
    height: 88rpx;
    background: #00B484;
    font-size: 32rpx;
    color: #FFFFFF;
    border-radius: 44rpx 44rpx 44rpx 44rpx;
  }
}
.list-head-suffix{
  height: 50rpx;
  flex: 1;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  flex-direction: column;
  .user-perm-myself{
    display: flex;
    width: 36rpx;
    height: 20rpx;
  }
  .text{
    font-size: 20rpx;
    color: #4BADE9;
  }
}
.carousel-content{
  padding: 48rpx 20rpx;
  width: 654rpx;
  height: 332rpx;
  background: #FFFFFF;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  box-sizing: border-box;
  .title{
    font-size: 32rpx;
    color: #1D2029;
    font-weight: 600;
    text-align: center;
  }
  .info{
    font-size: 28rpx;
    color: #777777;
    text-align: center;
    margin: 32rpx 0;
  }
  .carousel-btn{
    display: flex;
    justify-content: space-between;
    .btn{
      display: flex;
      align-items: center;
      justify-content: center;
      width: 292rpx;
      height: 88rpx;
      background: #FFFFFF;
      border-radius: 44rpx 44rpx 44rpx 44rpx;
      border: 2rpx solid #D9DBE0;
      font-size: 32rpx;
    }
    .btn-l{
      color: #1D2029;
    }
    .btn-r{
      color: #FFFFFF;
      background: #00B484;
    }
  }
}
.user-perm-content{
  padding: 8rpx 32rpx 68rpx;
  border-radius: 24rpx 24rpx 0rpx 0rpx;
  background-color: #fff;
  .user-perm-item{
    display: flex;
    align-items: center;
    padding: 32rpx 0 34rpx;
    border-bottom: 2rpx solid #EAEBF0;
    .perm-item-l{
      display: flex;
      width: 40rpx;
      height: 40rpx;
      margin-right: 40rpx;
    }
    .perm-item-name{
      font-size: 28rpx;
      color: #333333;
    }
  }
  .user-perm-btn{
    display: flex;
    align-items: center;
    justify-content: center;
    width: 598rpx;
    height: 88rpx;
    font-size: 32rpx;
    color: #777777;
    background: #F4F6FA;
    border-radius: 44rpx 44rpx 44rpx 44rpx;
    margin: 60rpx auto 0;
  }
}
.list-content {
  overflow: hidden;
  /deep/.nui-list{
    .lists{
      .list-wrapper{
        .list{
          padding: 24rpx 0 0;
        }
      }
    }
  }
}
.list-title {
  font-weight: 500;
  font-size: 36rpx;
  color: #00B484;
  line-height: 50rpx;
  padding: 20rpx 0;
}
.body-main {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f4f5f8;
  .scroll-view-main {
    height: 100%;
    background-color: #fff;
  }
}
.my-bg-img {
  height: 504rpx;
  width: 100%;
  background-size: 100%;
  background-repeat: no-repeat;
  background-color: #fff;
}
.my-data {
  position: relative;
  .my-user{
    position: absolute;
    top: 252rpx;
    width: calc(100% - 64rpx);
    left: 0;
    z-index: 999;
    padding: 0 32rpx;
    .user-head{
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;
      .user-img-l{
        position: relative;
        .my-img{
          width: 144rpx;
          height: 144rpx;
          border-radius: 50%;
        }
        .head-v-icon {
          position: absolute;
          width: 50.54rpx;
          height: 50.54rpx;
          right: 0;
          bottom: 0;
        }
      }
      .user-img-info{
        display: flex;
        align-items: center;
        margin-left: 24rpx;
        .title{
          font-size: 32rpx;
          color: #333333; 
          max-width: 260rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .info{
          font-size: 24rpx;
          color: #777777;
          margin-top: 12rpx;
        }
        .user-code{
          display: flex;
          width: 48rpx;
          height: 48rpx;
          margin-left: 20rpx;
        }
      }
    }
    .user-bott{
      /deep/.good-at{
        .button-show{
          width: initial !important;
          .fold-bottom-arrow{
            // right: -22rpx;
            // bottom: 0rpx;
          }
        }
      }
      .my-all-attention{
        margin-top: 20rpx;
        .attention-item{
          display: flex;
          justify-content: space-between;
          align-items: center;
          .attention-l{
            display: flex;
            flex: 1;
            justify-content: space-between;
            .item-l{
              display: flex;
              align-items: center;
              flex-direction: column;
              .item-num{
                text-align: center;
                font-size: 32rpx;
                color: #333333;
              }
              .item-name{
                font-size: 24rpx;
                color: #777777;
              }
            }
          }
          .user-attention{
            border: 2rpx solid #00B484 !important;
            border-radius: 56rpx 56rpx 56rpx 56rpx !important;
            font-size: 28rpx !important;
            color: #00B484 !important;
            background:#fff !important;
          }
          .user-edit{
            border-radius: 52rpx 52rpx 52rpx 52rpx !important;
            border: 2rpx solid #777777 !important;
            background:#fff !important;
            font-size: 26rpx !important;
            color: #777777 !important;
          }
          .item-r{
            display: flex;
            align-items: center;
            justify-content: center;
            height: 64rpx;
            width: 154rpx;
            background: #00B484;
            border-radius: 56rpx 56rpx 56rpx 56rpx;
            font-size: 28rpx;
            color: #FFFFFF;
            // padding: 0 24rpx 0 26rpx;
            margin-left: 30rpx;
            image{
              display: flex;
              width: 24rpx;
              height: 24rpx;
              margin-right: 6rpx;
            }
          }
        }
      }
    }
  }
  .header-nav-bar{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
  }
}
.my-scroll-refresh{
  flex: 1;
  /deep/ .mescroll-empty-box{
    min-height: 0 !important;
    top: -50% !important;
  }
}

.user-head-pic {
  position: relative;
  .head-v-icon {
    position: absolute;
    width: 42rpx;
    height: 42rpx;
    right: 0;
    bottom: 0;
  }
}
.headimg-wrapper {
  position: absolute;
  height: 74px;
  width: 74px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.header-search-box {
  width: 100%;
  display: flex;
  align-items: center;
}
.header-search-box-right {
  // #ifdef MP-WEIXIN
  justify-content: flex-end;
  padding-right: 95px;
  // #endif
}
.header-title {
  font-weight: 500;
  font-size: 36rpx;
  color: #1D2029;
  line-height: 50rpx;
}
.header-search-img {
  width: 32px;
  height: 32px;
}
/deep/ .uni-navbar__header-btns-left {
  width: 48rpx !important;
}
/deep/ .uni-navbar__header-container {
  padding-left: 24rpx;
}
/deep/ .uni-navbar__header-btns-right {
  width: 200px !important;
}
</style>
