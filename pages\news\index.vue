<template>
    <page>
      <view slot="content">
        <scroll-refresh bgColor='#fff' :isShowEmptySwitch="false" :fixed="true" top="0" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit" :isShowOptUpSwitch="false">
          <view class="main-content">
            <view class="top-msg-content">
                <view class="msg-item left" @tap="handleClickJump(newsUnreadObj,1)">
                  <view class="img">
                    <img class="img" :src="$static_ctx+'image/business/hulu-v2/icon-news-reply.png'">
                    <view class="badge wz" :class="{
                      'bgdge-one':newsUnreadObj.communityUnread.commitCount < 10,
                      'bgdge-two':newsUnreadObj.communityUnread.commitCount >= 10
                    }" v-if="newsUnreadObj.communityUnread.commitCount > 0">
                      {{ newsUnreadObj.communityUnread.commitCount > 99 ? (99 + '+') : newsUnreadObj.communityUnread.commitCount }}
                    </view>
                  </view>
                  <view class="img-text">
                    <view class="w-104 position">
                      我的回复
                    </view>
                    占位
                  </view>
                </view>
                <view class="msg-item center" @tap="handleClickJump(newsUnreadObj,2)">
                  <view class="img">
                    <img class="img" :src="$static_ctx+'image/business/hulu-v2/icon-news-like-and-bookmark.png'">
                    <view class="badge wz" v-if="newsUnreadObj.communityUnread.collectionCount > 0">
                      {{ newsUnreadObj.communityUnread.collectionCount > 99 ? (99 + '+') : newsUnreadObj.communityUnread.collectionCount }}
                    </view>
                  </view>
                  <view class="img-text">
                    <view class="w-130 position">
                      点赞与收藏
                    </view>
                    占位
                  </view>
                </view>
                <view class="msg-item right" @tap="handleClickJump(newsUnreadObj,3)">
                  <view class="img">
                    <img class="img" :src="$static_ctx+'image/business/hulu-v2/icon-news-inviting-you-to-answer.png'">
                    <view class="badge wz" v-if="newsUnreadObj.beInviteCount > 0">
                      {{ newsUnreadObj.beInviteCount > 99 ? (99 + '+') : newsUnreadObj.beInviteCount }}
                    </view>
                  </view>
                  <view class="img-text">
                    <view class="w-104 position">
                      邀你回答
                    </view>
                    占位
                  </view>
                </view>
            </view>
            <!-- 邀请评论 -->
           <!-- <view class="invite" @tap="navtoGo('PostsReply')">
              <image mode="aspectFill" class="invite-icon" :src="file_ctx + 'static/image/business/icon-email.png'"></image>
              <text class="invite-text">邀请你回答</text>
              <view class="badge">{{ newsUnreadObj.beInviteCount }}</view>
            </view> -->
            <view class="item" :index="index" v-for="(item, index) in chatlist" :key="index" hover-class="message-hover-class"
                    @tap="navtoGo('Chat',item)">
                <image class="img" :src="item.chatUserHeadPath ? file_ctx + item.chatUserHeadPath : defaultAvatar"></image>
                <!-- <view class="un-read" v-if="item.extendUnMsgCount>0">{{ item.extendUnMsgCount>99 ? 'ⵈ': item.extendUnMsgCount}}</view> -->
                <view class="right u-border-bottom title-wrap">
                    <view class="right_top">
                      <view class="right_top_name u-line-1">
                        <!-- <view> -->
                          {{ item.chatUserName }}
                        <!-- </view> -->
                        <template v-if="item.patientInfoVo">
                          <text class="top-active">( {{`患者-${item.patientInfoVo.name}`}} | {{ item.patientInfoVo.gender === 1 ? '男' : item.patientInfoVo.gender === 2 ? '女' : '未知' }}{{item.patientInfoVo.age ? ' | ' + item.patientInfoVo.age + '岁' : ''}} )</text>
                        </template>
                      </view>

                      <view class="right_top_time ">{{  item.lastMsgTime | getTimeStringAutoShort2 }}</view>
                    </view>
                    <!-- <view class="right_center">
                      <template v-if="item.patientInfoVo">
                        <view class="right_center_name u-line-1">
                          {{ `患者-${item.patientInfoVo.name} | ${item.patientInfoVo.gender === 1 ? '男' : item.patientInfoVo.gender === 2 ? '女' : '未知'} | ${item.patientInfoVo.age}岁` }}
                        </view>
                      </template>
                    </view> -->
                    <view class="right-center">
                      {{item.lastMsgContent}}
                      <view class="badge wz2" v-if="item.extendUnMsgCount>0" :class="{
                        'bgdge-one':item.extendUnMsgCount < 10,
                        'bgdge-two':item.extendUnMsgCount >= 10
                      }">
                        {{ item.extendUnMsgCount > 99 ? (99 + '+') : item.extendUnMsgCount }}
                      </view>
                    </view>
                    <!-- <view class="right_btm ">
                      <view class="u-line-1" v-html="item.lastMsgContent && item.lastMsgContent.length > 0?item.lastMsgContent:'-'"></view>
                      <view class=""></view>
                    </view> -->
                  </view>

            </view>
          </view>
          <view class="empty-box" v-if="chatlist.length === 0">
            <image
              class="empty-img"
              mode="widthFix"
              :src="$static_ctx + 'image/business/hulu-v2/icon-circle-empty.png'"
            ></image>
            <text class="empty-text">暂无咨询</text>
          </view>
        </scroll-refresh>
        <customerService v-if="isShowConfig" ref="customerService"></customerService>
      </view>
    </page>

</template>

<script>
// import MescrollUni from '@/components/uni/mescroll-uni'
import search from '@/components/basics/form/search'
import {mapState} from 'vuex'
import TimeUtils from '@/common/util/websocket/timeUtils.js'
import customerService from '@/components/basics/customerService/index.vue'
export default {
  components: {
    // MescrollUni,
    search,
    customerService,
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      $validate: this.$validate,
      mescroll: null, // mescroll实例对象
      downOption: {
        auto: false // 不自动加载
      },
      upOption: {
        auto: false ,// 不自动加载
        empty:{
          icon:this.$static_ctx + '/image/business/hulu-v2/icon-circle-empty.png'
        }
      },
      isInit: false, // 列表是否已经初始化
      scrollY: 0,
      regForm: {
        search: ''
      },
      itemList:[
      ],
      isListEmpty: false,
      defaultAvatar: this.file_ctx + 'static/image/system/avatar/icon-default-avatar.png',
      isShowConfig:false,
    }
  },
  watch:{
    chatlist: {
      handler:function(newVal,oldVal){
        if (this.$validate.isNull(newVal)) {
          this.isListEmpty = true
        } else {
          this.isListEmpty = false
        }

        this.handlerUnReadNum()
      },
      deep: true
    },

  },
  onShow(){
    // #ifdef MP-WEIXIN
    this.setTabBarIndex(3);
    // #endif
    this.$ext.common.queryNewsUnread().then((data) => {
      // #ifdef MP-WEIXIN
      Object.keys(data).forEach(index => {
        this.setTabBarBadge(index, data[index])
      })
      // #endif
    })
  },
  computed: {
    ...mapState('chat', {
      ws: state => state.ws,
      chatlist: state => state.chatlist
    }),
    ...mapState('user', {
      curSelectUserInfo: state => state.curSelectUserInfo, // 当前登录用户信息
      codeUserInfo: state => state.codeUserInfo,
      isLogin: state => state.isLogin,
      accountId: state => state.accountId,
      newsUnreadObj: state => state.newsUnreadObj
    }),
  },
  onLoad(paramsObj) {
    const query = this.$Route.query
    if (!this.$validate.isNull(query)) {
    }
    this.getCustomerserviceprofilesQueryNoParametersOne()
    // #ifdef MP-WEIXIN
    // wx.showShareMenu({
    //   withShareTicket:true,
    //   //设置下方的Menus菜单，才能够让发送给朋友与分享到朋友圈两个按钮可以点击
    //   menus:["shareAppMessage","shareTimeline"]
    // })
    // #endif
  },
  mounted() {
    // 是否登录
    if (!this.isLogin) {
      this.$navto.replace('Login')
    } else {
      this.init()
    }
  },
  methods: {
    // 在线客服
    async getCustomerserviceprofilesQueryNoParametersOne(){
      const res = await this.$api.drugBook.getCustomerserviceprofilesQueryNoParametersOne()
      if(res.data !==""){
        res.data.enableStatus == 1 && res.data.types.includes('3') ? this.isShowConfig = true : this.isShowConfig = false
      }
    },
    handleClickJump(item,type){
      // #ifdef MP-WEIXIN
      this.handleClickTrack(type)
      // #endif
      if(type == 1){
        this.navtoGo('NewsReply')
      } else if(type == 2){
        this.navtoGo('NewsLikeCollect')
      } else {
        this.navtoGo('PostsReply')
      }
    },
    // #ifdef MP-WEIXIN
    handleClickTrack(type){
      getApp().globalData.sensors.track("OperationClick",
        {
          'page_name':'消息',
          'first_operation_name' : type == 1 ? '我的回复' : type == 2 ? '点赞与收藏' : '邀你回答',
          'second_operation_name' : '',
          'operation_floor' : '1',
          'operation_id' : '',
          'operation_type':'功能组件',
          'is_link_third' : '否',
          'target_url' : type == 1 ? '/modules/community/news/reply/index' : type == 2 ? '/modules/community/news/like-collect/index' : '/modules/community/posts/reply/index',
          'position_rank' : type == 1 ? '1' : type == 2 ? '2' : '3',
        }
      ) 
    },
    // #endif
    handlerUnReadNum () {
        let newsUnreadObj = this.$common.getKeyVal('user', 'newsUnreadObj')
        newsUnreadObj.consultUnread = this.$ext.common.getConsultUnread()
        this.$common.setKeyVal('user', 'newsUnreadObj', newsUnreadObj)
    },
    navtoGo(url = '', obj = {}) {
      this.$navto.push(url, obj)
    },
    init(val) {
      this.$nextTick(() => {
        this.isInit = true // 标记为true
        this.regForm.search = ''
        this.mescroll.triggerDownScroll()
      })
    },
    changeSearch(obj) {
      this.mescroll.optUp.page.num = 1
      this.mescroll.optUp.page.size = 7
      this.regForm.search = obj.name
      this.mescroll.triggerDownScroll()
    },
    scrollInit(scroll) {
      scroll.optUp.page.num = 1
      scroll.optUp.page.size = 7
      this.mescroll = scroll
    },
    returnFn(obj) {
      const that = this
      setTimeout(function() {
        // 拉取聊天列表
        const { centerUserId = '' } = that.curSelectUserInfo || {}
        that.$api.chat.chatlistPatientQueryList({ userId: centerUserId }).then(res => {
          that.$common.setKeyVal('chat', 'chatlist', res.data, false)
          obj.successCallback && obj.successCallback(res.data || [])
        })
      }, that.$constant.noun.scrollRefreshTime)
    }

  },
  filters: {
    getTimeStringAutoShort2(timestamp){
        try {
            timestamp = Number(timestamp) ? Number(timestamp) : timestamp
        } catch (err) {

        }
        return TimeUtils.getTimeStringAutoShort2(new Date(timestamp).getTime(),true);
    }
  }
}
</script>
<style lang="scss" scoped>
.empty-box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items:center;
  height: calc(100vh - 256upx);
  .empty-img {
    width:250upx;
    // height: 250upx;
  }
  .empty-text {
    height: 70rpx;
    display: flex;
    align-items: center;
    align-items: center;
    color: #7c7c7c;
  }
}
.top-active {
  font-weight: 400;
  font-size: 24rpx;
  color: #1062B2;
  line-height: 34rpx;
  text-align: left;
  font-style: normal;
  margin-left: 20rpx;
}
.right-center {
  width: 504rpx;
  height: 34rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 24rpx;
  color: #868C9C;
  line-height: 34rpx;
  text-align: left;
  font-style: normal;
  position: relative;
  padding-right: 30px;
  margin-top: 12rpx;
}
.wz2{
  position: absolute;
  right: 0;
  top: 0;
  text-align: center;
}
.right_top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.right_top_name{
  overflow-wrap: break-word;
  color: rgba(29, 32, 41, 1);
  font-size: 32rpx;
  font-weight: 550;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
  margin-top: 8rpx;
}
.right_top_time {
  overflow-wrap: break-word;
  color: rgba(134, 140, 156, 1);
  font-size: 20rpx;
  font-weight: normal;
  text-align: right;
  white-space: nowrap;
  line-height: 28rpx;
  margin-top: 8rpx;
}

.top-msg-content{
  display: flex;
  text-align: center;
  width: 100%;
  height: 150upx;
  padding-bottom: 36upx;
  //background: red;
  //padding: 20upx;
  padding-top: 20upx;
  .msg-item{
    display: inline-block;
    position: relative;
    flex: 1;
    // margin: 20upx auto;
    .img{
      width: 96upx;
      height: 96upx;
      position: relative
    }
  }
  .msg-item.left {
    margin-left: 62upx;
  }
  .msg-item.right {
    margin-right: 60upx;
  }
  .msg-item.center {
    margin:0  154upx;
  }
  .img-text{
    font-size: 26upx;
    margin-top: 24upx;
    color: #fff;
    position: relative;
  }
  .position {
    position: absolute;
    white-space: nowrap;
    font-weight: 500;
    font-size: 26upx;
    color: #000;
    left: 0;
    top: 0upx;
  }
  .w-104 {
    width: 104upx;
    left: -7rpx;
  }
  .w-130 {
    width: 130upx;
    left: -20rpx;
  }
}
.badge{
  width: 54rpx;
  height: 28rpx;
  background: #FF5500;
  border-radius: 16rpx;
  border: 2rpx solid #FFFFFF;
  font-size: 20rpx;
  color: #fff;
  line-height: 28rpx;
}
.badge.wz {
  position: absolute;
  right: 0;
  top: 0;
  transform: translate(50%,-50%);
}
.bgdge-one {
  width: 28upx;
  // height: 28upx;
}
.bgdge-two {
  width: 54rpx;
}
.un-read{
  background-color: red;
  font-size: 20upx;
  border-radius: 20upx;
  text-align: center;
  padding: 0 10upx 0 10upx;
  margin: auto;
  position: absolute;
  left: 15%;
  top: 10%;
  color: white;
}
.main-container{
  background-color:#f7f7f7;
  height: 100%;
}
.main-search{
  background-color:#FFFFFF;
}
.main-content{
  padding:0upx 16upx 0upx 16upx;
  // #ifdef MP-WEIXIN
  padding-bottom: calc(56px + env(safe-area-inset-bottom));
  // #endif
  background: white;
}
.item {
  position: relative;
  width: 100%;
  // height: 172upx;
  display: flex;
  align-items: center;
  margin-bottom: 10upx;
  // border-bottom: 1px solid #f4f6f8;
  // padding: 20rpx;
  image {
    width: 106rpx;
    height: 106rpx;
    margin: 20rpx;
    border-radius: 12rpx;
    margin-left: 16upx;
    border: 1upx solid #dbdbdb;
    //flex: 0 0 76rpx;
    &.img {
      border-radius: 50%;
    }
  }
  .right {
    overflow: hidden;
    flex: 1 0;
    padding-right: 8upx;
    // padding: 20rpx 20rpx 20rpx 0;
    &_btm {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 22rpx;
      color: #909399;
      padding-top: 10rpx;

      .u-line-1 {
        @include ellipsis(1);
      }
    }
  }
}
.code{
  padding: 16upx 16upx 16upx 16upx;
  overflow: hidden;
  background: #FFFFFF;
  /*margin-bottom: 20upx;*/
  height: 40upx;
  line-height: 40upx;
  position: relative;
  .code-num{
    display:inline-block;
    vertical-align: middle;
    margin-right: 8upx;
    @include iconImg(45,45,'/system/icon-sys-new.png');
  }
  text{
    width: calc(100% - 100upx);
    font-size: 28upx;
    color: #333333;
    line-height: 48upx;
    display: inline-block;
    vertical-align: middle;
  }

  .jump{
    position: absolute;
    right: 20upx;
    display: inline-block;
    vertical-align: middle;
    margin-left: 12upx;
    @include iconImg(45, 45, '/business/icon-more.png');
  }
  .jump-text{
    position: absolute;
    right: 70upx;
    font-size: 32upx;
    color: #999999;
    line-height: 48upx;
    display: inline-block;
    vertical-align: middle;
  }
}
.slot-wrap {
  display: flex;
  align-items: center;
  padding: 0 30rpx;
}
.right_center_name {
  display: flex;
  flex-direction: row;
}
.invite {
  position: relative;
  width: 100%;
  height: 172upx;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f4f6f8;
  box-sizing: border-box;
  .invite-icon {
    width: 108rpx;
    height: 108rpx;
    margin: 20rpx;
  }
  .invite-text {
    font-size: 28rpx;
    color: #303133;
    font-weight: 550;
  }
  .badge {
    right: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}
</style>
