<template>
  <view class="panel-wrapper">
    <!-- 区域-1 start -->
    <view class="panel-one">
      <view class="panel-one-item" v-for="(item,index) in panelOne" :key="item.id" @tap="clickApp(item)">
        <view class="one-item-l">
          <image mode="aspectFit" :src="item.logoPath" class="panel-one-icon"></image>
          <view class="panel-one-content">
            <text class="panel-one-title">{{ item.name }}</text>
            <text class="panel-one-subtitle">{{ item.subTitle }}</text>
          </view>
        </view>
        <view class="one-item-r" v-if="index !== panelOne.length - 1"></view>
      </view>
    </view>
    <!-- 区域-1 end -->

    <!-- 区域-2 start -->
    <view class="panel-two">
      <view class="panel-two-item" v-for="item in panelTwo" :key="item.id" @tap="clickApp(item)">
        <image mode="aspectFit" :src="item.logoPath" class="panel-two-icon"></image>
        <text class="panel-two-title">{{ item.name }}</text>
      </view>
    </view>
    <!-- 区域-2 end -->

    <!-- 区域-3 start -->
    <view class="panel-three">
      <!-- 左侧默认健康保护神 -->
      <swiper class="panel-four-swiper" circular autoplay :duration="500" :interval="3000">
        <swiper-item class="panel-four-swiper-item" v-for="(item, index) in panelFour" :key="index">
          <view
            class="panel-three-left"
            :style="'background-image: url(' + item.logoPath + ')'"
            @tap="clickApp(item)"
          >
            <view class="panel-three-content flex-between">
              <view>
                <view class="panel-three-title">{{ item.name }}</view>
                <view class="panel-three-subtitle">
                  <template v-if="item.prayEnable === 1">
                    <text style="color: #4E5569;">{{ $common.bigNumberTransform(wishingwellData.lightSumCount || 0, 0) }}</text>
                    人已祈福
                  </template>
                  <template>{{ item.subTitle }}</template>
                </view>
              </view>
              <!-- <image v-if="item.prayEnable === 1" mode="aspectFit" :src="file_ctx + 'static/image/business/hulu-v2/pray-btn.gif'" class="pray-btn"></image> -->
              <!-- <view class="panel-three-btn">立即祈福</view> -->
            </view>
          </view>
        </swiper-item>
      </swiper>
      <view class="panel-three-right">
        <view class="panel-three-item" v-for="(item, index) in panelThree" :key="item.id" :style="'background-image: url(' + item.logoPath + ')'" @tap="clickApp(item)">
          <view class="panel-three-content flex-between">
            <view>
              <view class="panel-three-title flex-align-center-box">
                {{ item.name }}
                <text class="tag-new" v-if="index === 0">NEW</text>
              </view>
              <view class="panel-three-subtitle">{{ item.subTitle }}</view>
            </view>
            <!-- <view
              class="panel-three-btn"
              :class="{
                blue: index === 0,
                green: index === 1
              }"
            >{{ index === 0 ? '看直播' : '去选购' }}</view> -->
          </view>
        </view>
      </view>
    </view>
    <!-- 区域-3 end -->
  </view>
</template>

<script>
import { isDomainUrl } from '@/utils/index'
import { mapState } from 'vuex'
import env from '@/config/env'
const launchOptions = uni.getLaunchOptionsSync()

export default {
  data() {
    return {
      file_ctx: this.file_ctx,
      panelOne: [],
      panelTwo: [],
      panelThree: [],
      panelFour: [],
      wishingwellData: {}
    }
  },
  computed: {
    ...mapState('user', {
      accountId: state => state.accountId,
      curSelectUserInfo: state => state.curSelectUserInfo,
      isLogin: state => state.isLogin,
    })
  },
  methods: {
    handleClickJump(){
      // #ifdef MP-WEIXIN
      getApp().globalData.sensors.track("OperationClick",
        {
          'page_name':'首页',
          'first_operation_name' : '祈福保平安',
          'second_operation_name':'',
          'operation_floor' : '3',
          'operation_id':'',
          'operation_type':'功能组件',
          'target_url' : 'calabashWebview',
          'is_link_third' : '否',
          'position_rank' : '8',
        }
      ) 
      // #endif
      this.navtoGo('calabashWebview')
    },
    // #ifdef MP-WEIXIN
    handleClickTrack(data){
      getApp().globalData.sensors.track("OperationClick",
        {
          'page_name':'首页',
          'first_operation_name' : data?.name,
          'operation_floor' : data?.regionNum,
          'operation_type':'功能组件',
          'operation_id' : data?.id,
          'target_url' : data?.path,
          'is_link_third' : data?.is_link_third || '否',
          'position_rank' : data?.order,
        }
      ) 
    },
    // #endif

    // 获取健康保护神首页数据
    async getWishingwellGetIndexData () {
      const res = await this.$api.wishingwell.wishingwellGetIndexData({})
      this.wishingwellData = res.data
    },
    navtoGo(url = '', obj = {}) {
      if (url == 'zixun'){
        const { centerUserId = '' } = this.curSelectUserInfo || {}
        if (!centerUserId) {
          // this.$uniPlugin.toast('用户档案不存在！')
          this.$navto.push('Login')
          return
        }
        this.$uniPlugin.loading('加载中', true)
        this.$api.order.orderInitiatorUserCheck({ userId: centerUserId, tenantId: this.$common.getKeyVal('user', 'curSelectStoreId', true) || env.tenantId }).then(res => {
          this.$uniPlugin.hideLoading()
          this.$navto.push('Chat', res.data)
        }).catch(() => {
          this.$uniPlugin.hideLoading()
        })

        // this.$uniPlugin.toast('敬请期待')
        return
      }
      this.$navto.push(url, obj)
    },
    async clickApp(data) {
      if(data.path ==='/modules/activity/calabash/webview') data.path = '/modules/activity/calabash/calabashWebview'
      if(launchOptions.scene === 1154) return this.$uniPlugin.toast('请前往小程序使用完整服务')
      this.recordClick(data)
      if (data.prayEnable === 1) return this.handleClickJump()
      const isJump = await this.jumpApp(data)
      console.myLog('isJump',isJump);
      // if (!isJump) return this.$uniPlugin.toast('敬请期待')
    },
    jumpApp(data) {
      return new Promise((resolve, reject) => {
        const { type, path, appId, name, miniType } = data
        if (path === 'zixun') {
          // if(this.isLogin){
            this.navtoGo(path)
            // #ifdef MP-WEIXIN
            this.handleClickTrack(data)
            // #endif
          // } else {
          //   this.$navto.push('Login',{formPage: 'Chat'})
          // }
          return resolve(true)
        }
        switch (type) {
          // 小程序
          case 1:
            // miniType 1-自营 2-第三方
            if (miniType === 1) {
              if(['/pages/index/index', '/pages/circle-home/index', '/pages/news/index', '/pages/post-message/index', '/pages/personal/index'].includes(path)) {
                uni.switchTab({
                  url: path
                })
              } else {
                // #ifdef MP-WEIXIN
                this.handleClickTrack(data)
                // #endif
                // 云商跳转
                if(path === '/modules/accompany-doctor/home/<USER>'){
                  let oldCloudProviderId = this.$common.getKeyVal('system','cloudProviderId', true);
                  if(oldCloudProviderId){
                    this.$navto.pushPath('/modules/accompany-doctor/home/<USER>')
                    return resolve(true)
                  }
                }
                this.$navto.pushPath(path)
              }
              resolve(true)
            } else {
              if (!appId) {
                return resolve(false)
              }
              // #ifdef MP-WEIXIN
              this.handleClickTrack({...data,is_link_third:'是'})
              // #endif
              this.$uniPlugin.navigateToMiniProgram({
                appId,
                path,
                envVersion: 'release',
                extraData: {}
              }, (res) => {
                resolve(true)
                console.log(res)
              }, (err) => {
                resolve(false)
              })
            }
            break
          // H5
          case 2:
            this.$navto.push('WebHtmlView', { src: path, title: name })
            // #ifdef MP-WEIXIN
            this.handleClickTrack(data)
            // #endif
            resolve(true)
            break
          default:
        }
      })
    },
    recordClick(data) {
      const param = {
        accountId: this.accountId,
        businessType: 2, // 业务类型:1-横幅，2-外部服务应用，3-企业微信群，4-圈子，5-帖子，6-帖子评论
        businessId: data.id,
        source: 1, // 来源：1-真实用户，2-马甲
        type: 2, // 类型：1-访问，2-点击，3-长按，4-关注，5-取关，6-点赞，7-收藏，8-删除评论，9-取消点赞，10-取消收藏，11-阅读
        indexPath: 1 // 操作位置：1-首页，2-圈子
      }
      this.$api.community.applicationoperatelogInsert(param)
    },
    async init() {
      this.getWishingwellGetIndexData()
      await this.getApplicationserviceGetIndexApplication()
    },
    // 获取圈子应用服务
    async getApplicationserviceGetIndexApplication() {
      const res = await this.$api.community.applicationserviceGetIndexApplication({})
      if (this.$validate.isNull(res.data)) return
      this.panelOne = []
      this.panelTwo = []
      this.panelThree = []
      this.panelFour = []
      res.data.filter(item => {
        // #ifdef MP-ALIPAY
        if (item.type === 1 && item.miniType === 2) return false
        // #endif
        return true
      }).forEach(item => {
        item.logoPath = isDomainUrl(item.logoPath)
        if (item.regionNum === 1) this.panelOne.push(item)
        if (item.regionNum === 2) this.panelTwo.push(item)
        if (item.regionNum === 3) this.panelThree.push(item)
        if (item.regionNum === 4) this.panelFour.push(item)
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.panel-wrapper {
  padding: 0 32rpx;
}
.panel-one {
  display: flex;
  align-items: center;
  margin-bottom: 48rpx;
}
.panel-one-item {
  display: flex;
  justify-content: center;
  flex: 1;
  position: relative;
  // padding-left: 28rpx;
  .one-item-l{
    display: flex;
    flex-direction: column;
    align-items: center;
    .panel-one-icon {
      display: block;
      width: 88rpx;
      height: 88rpx;
    }

  }
  .one-item-r{
    position: absolute;
    right: 0;
    top: 8rpx;
    width: 2rpx;
    height: 88rpx;
    background: #DBDDE0;
  }
}
.panel-one-content {
  // padding-left: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  // justify-content: space-between;
}
.panel-one-title {
  font-weight: 500;
  font-size: 32rpx;
  color: #1D2029;
  line-height: 44rpx;
  margin-top: 8rpx;
}
.panel-one-subtitle {
  font-size: 22rpx;
  color: #868C9C;
  line-height: 32rpx;
}

.panel-two {
  display: flex;
  align-items: center;
  margin-bottom: 56rpx;
}
.panel-two-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.panel-two-icon {
  display: block;
  width: 64rpx;
  height: 64rpx;
}
.panel-two-title {
  display: inline-block;
  font-size: 24rpx;
  color: #1D2029;
  line-height: 34rpx;
  padding-top: 12rpx;
}

.panel-three {
  display: flex;
  margin-bottom: 20rpx;
}
.panel-three-left {
  width: 332rpx;
  height: 324rpx;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  .panel-three-content {
    padding: 32rpx 32rpx 24rpx;
  }
  .panel-three-title {
    margin-bottom: 6rpx;
  }
  .panel-three-btn {
    width: 144rpx;
    height: 56rpx;
    background: linear-gradient( 90deg, #FF9224 0%, #FF5500 100%);
    box-shadow: 0rpx 0rpx 8rpx 0rpx #FFFFFF;
    border-radius: 28rpx;
    font-weight: 600;
    font-size: 24rpx;
    text-align: center;
    color: #FFFFFF;
    line-height: 56rpx;
  }
}
.panel-three-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-left: 20rpx;
  .panel-three-content {
    padding: 18rpx 24rpx 16rpx;
  }
}
.panel-three-item {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  // 暂时兼容下支付宝小程序
  // #ifndef MP-ALIPAY
  flex: 1;
  // #endif
  // #ifdef MP-ALIPAY
  height: 50%;
  // #endif
  &+.panel-three-item {
    margin-top: 20rpx;
  }
}
.panel-three-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}
.panel-three-title {
  font-weight: 500;
  font-size: 30rpx;
  color: #1D2029;
  line-height: 42rpx;
  margin-bottom: 2rpx;
}
.panel-three-subtitle {
  font-size: 22rpx;
  color: #A5AAB8;
  line-height: 32rpx;
}
.panel-three-btn {
  width: 98rpx;
  height: 36rpx;
  border-radius: 18rpx;
  font-size: 22rpx;
  color: #FFFFFF;
  line-height: 36rpx;
  text-align: center;
  &.blue {
    background-color: #72ADE8;
  }
  &.green {
    background-color: #4BC8AF;
  }
}
.flex-align-center-box {
  display: flex;
  align-items: center;
}
.flex-between {
  justify-content: space-between;
}
.tag-new {
  position: relative;
  display: inline-block;
  font-weight: 600;
  font-size: 20rpx;
  color: #FFFFFF;
  line-height: 28rpx;
  background: linear-gradient( 90deg, #FF9224 0%, #FF5500 100%);
  border-radius: 8rpx;
  margin-left: 14rpx;
  padding: 0 6rpx;
  &::before {
    content: "";
    position: absolute;
    top: 4rpx;
    left: -8rpx;
    width: 0;
    height: 0;
    border-right: 14rpx solid #FF9023;
    border-top: 10rpx solid transparent;
    border-bottom: 10rpx solid transparent;
  }
}
.pray-btn {
  width: 160rpx;
  height: 60rpx;
  margin-left: -12rpx;
}
.panel-four-swiper,
.panel-four-swiper-item {
  width: 332rpx;
  height: 324rpx;
}
</style>
