<template>
  <page>
    <view slot="content" class="body-main">
      <tabs-sticky :fontBigger="true" :bdb="false" :overflowX="true" v-model="curIndex" :tabs="tabs" @change="changeTab"></tabs-sticky>
      <view class="search-main">
        <swiper class="swiper" :current="curIndex" @change="swiperChange">
          <!-- 帖子 -->
          <swiper-item>
            <scroll-refresh style="height: 100%;" :isShowEmptySwitch="true" :fixed="false" :isAbsolute="false" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
              <view class="content">
                <view class="content-main">
                  <nui-list class="nui-list" :indexlist="indexlist" :isShowGambit="true" @cateClick="cateClick" mode="collect" @collect="collect"></nui-list>
                </view>
              </view>
            </scroll-refresh>
          </swiper-item>
          <!-- 话题 -->
          <swiper-item>
            <scroll-refresh style="height: 100%;" :isShowEmptySwitch="true" :fixed="false" :isAbsolute="false" :up="upOption" :down="downOption" @returnFn="gambitReturnFn" @scrollInit="gambitScrollInit">
              <view class="gambit-content">
                <view class="gambit-item" v-for="item in gambitList" :key="item.id">
                  <view class="gambit-l">
                    <view class="title"># {{ item.name }}</view>
                    <view class="info-box">
                      <!-- <view class="browse">{{ item.preview }}浏览</view> -->
                      <view class="discussion">{{ item.postUseCount }}讨论</view>
                    </view>
                  </view>
                  <view class="gambit-r" @click="handleClickDiscuss(item)">去讨论</view>
                </view>
              </view>
            </scroll-refresh>
          </swiper-item>
        </swiper>
      </view>
    </view>
  </page>
</template>

<script>

import { mapState } from 'vuex'
import nuiList from '@/components/community/nui-list/nui-list.vue'
import TabsSticky from '@/components/basics/tabs-sticky-v3'
export default {
  components: {
    nuiList,
    TabsSticky
  },
  data() {
    return {
      file_ctx: this.file_ctx,
      mescroll: null, // mescroll实例对象
      gambitMescroll: null, // liveMescroll实例对象
      downOption: {
          auto: false // 不自动加载
      },
      upOption: {
          auto: false, // 不自动加载
          empty: {
            top: 0,
            zIndex: 999,
          }
      },
      isInit: false, // 列表是否已经初始化
      indexlist: [],
      pageStartDate: null,
      curIndex: 0,
      tabs: [{ name: '帖子' },{name:'话题'},],
      gambitList:[],
    }
  },
  computed: {
    ...mapState('user', {
      codeUserInfo: state => state.codeUserInfo, // 当前登录用户信息
      curSelectUserInfo: state => state.curSelectUserInfo, // 当前选中的用户信息
      curSelectUserInfoId: state => state.curSelectUserInfoId, // 当前选中的档案的ID
      recordUserInfo: state => state.recordUserInfo,
      accountId: state => state.accountId
    })
  },
  watch: {
    // 监听下标的变化
    curIndex(val) {
      this.init()
    }
  },
  onLoad(paramsObj) {
    const that = this
    const query = that.$Route.query
    this.pageStartDate = that.$common.formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss')
    if (!that.$validate.isNull(query)) {

    }
    this.$nextTick(() => {
      that.init()
    })
    // #ifdef MP-WEIXIN
    this.handleClickTrack('OperationDetailsPageView')
    // #endif
  },
  onUnload(){
    // #ifdef MP-WEIXIN
    this.handleClickTrack('EndOperationDetailsPageView')
    // #endif
  },
  methods: {
    handleClickDiscuss(item){
      this.$navto.push('GambitDetail',{id:item.id})
    },
    gambitReturnFn(obj){
      const that = this
      setTimeout(function() {
        let params = {
          current: obj.pageNum,
          size: obj.pageSize,
          condition: {
            accountId: that.accountId
          }
        }
        that.$api.postmessage.postmessagetopicQueryMyCollection(params).then(res => {
          let data = res.data.records || []
          if (obj.pageNum === 1) {
            that.gambitList = []
          }
          that.gambitList = [...that.gambitList, ...data]
          obj.successCallback && obj.successCallback(data)
        })
      }, that.$constant.noun.scrollRefreshTime)
    },
    gambitScrollInit(scroll) {
      scroll.optUp.page.num = 1
      scroll.optUp.page.size = 7
      this.gambitMescroll = scroll
    },
    changeTab(index) {
      this.curIndex = index
    },
    swiperChange(e) {
      this.changeTab(e.detail.current)
    },
    // #ifdef MP-WEIXIN
    handleClickTrack(type){
      let pages = getCurrentPages()
      let current = pages[pages.length - 1]; // 获取到当前页面的信息
      let pageInfo = __wxConfig.page && __wxConfig.page[`${current.route}.html`] || ''; // 内部属性
      getApp().globalData.sensors.track(type,
        {
          'page_name' : pageInfo?.window?.navigationBarTitleText || '',
          'first_operation_name' : '我的信息',
          'second_operation_name' : '我的收藏',
        }
      ) 
    },
    // #endif
    collect (data) {
      this.$uniPlugin.modal('','确认取消收藏该帖子？', {
        showCancel: true, // 是否显示取消按钮，默认为 true
        cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
        cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
        confirmText: '确定', //  确定按钮的文字，默认为"确定"，最多 4 个字符
        confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
        fn: async (n) => {
          if(n) {
            const res = await this.$api.postmessage.postmessageCancelCollection({id: data.id, accountId: this.accountId})
            this.$uniPlugin.toast(res.msg)
            this.init()
          }
        }
      })
    },
    cateClick (data) {
      this.navtoGo('Circle', {cid: data.circleClassifyId})
    },
    navtoGo(url, obj = {}) {
      this.$navto.push(url, obj)
    },
    init() {
      if(this.curIndex === 0){
        this.$nextTick(() => {
          this.mescroll.triggerDownScroll()
        })
      } else {
        this.$nextTick(() => {
          this.gambitMescroll.triggerDownScroll()
        })
      }
    },
    scrollInit(scroll) {
      scroll.optUp.page.num = 1
      scroll.optUp.page.size = 10
      this.mescroll = scroll
    },
    returnFn(obj) {
      const that = this
      setTimeout(function() {
        let params = {
          current: obj.pageNum,
          size: obj.pageSize,
          condition: {
            accountId: that.accountId,
            // createTime: that.pageStartDate
          }
        }
        that.$api.community.commoncollectlikesQueryMyCollectionGourdPage(params).then(res => {
          let data = res.data.records.map(item=>({...item,id:item.businessId,topicIdsArr:item.topicIds && JSON.parse(item.topicIds) || []})) || []
          if (obj.pageNum === 1) {
            that.indexlist = []
          }
          that.indexlist = [...that.indexlist, ...data]
          obj.successCallback && obj.successCallback(data)
        })
      }, that.$constant.noun.scrollRefreshTime)
    },
  }
}
</script>

<style lang="scss" scoped>
  /deep/.tabs-sticky{
    padding:22rpx 32rpx 22rpx;
    border-bottom: 4rpx solid #F5F5F5;
    .tabs-sticky-body{
      padding: 0;
      .tab{
        text{
          padding: 0;
        }
      }
    }
  }
  .search-main{
    flex: 1;
    .swiper{
      height: 100%;
    }
  }
  .body-main {
    display: flex;
    height: 100%;
    flex-direction: column;
    background-color: #fff;
    .content {
      padding-bottom: env(safe-area-inset-bottom);
      padding-bottom: constant(safe-area-inset-bottom);
      box-sizing: border-box;
      &-main {
        box-sizing: border-box;
      }
    }
  }
  .gambit-content{
    padding: 32rpx;
    background: #F4F6FA;
    .gambit-item{
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 30rpx 24rpx 28rpx;
      background: #FFFFFF;
      border-radius: 16rpx 16rpx 16rpx 16rpx;
      margin-bottom: 24rpx;
      .gambit-l{
        display: flex;
        flex-direction: column;
        .title{
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 400rpx; /* 设置元素宽度 */
          font-size: 32rpx;
          color: #1D2029;
          line-height: 38rpx;
          margin-bottom: 8rpx;
        }
        .info-box{
          display: flex;
          color: #777777;
          .browse{
            font-size: 24rpx;
            margin-right: 12rpx;
          }
          .discussion{
            font-size: 24rpx;
          }
        }
      }
      .gambit-r{
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
        color: #00B484;
        padding: 6rpx 16rpx;
        height: fit-content;
        border-radius: 66rpx 66rpx 66rpx 66rpx;
        border: 2rpx solid #00B484;
      }
      &:last-child{
        margin-bottom: 0;
      }
    }
  }
</style>
