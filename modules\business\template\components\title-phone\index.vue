
<template>
  <view class="title-input clear-float" :class="{'bdt':defaultConfig.bdt,'bdb':defaultConfig.bdb && !child}">
    <view class="l-l" :style="{'color': defaultConfig.titleColor}">
      {{defaultConfig.label}}
      <text class="star" v-if="defaultConfig.required">*</text>
    </view>
    <view class="l-r">
      <input :disabled="disabled"
        v-model="form.data.val"
        :placeholder="defaultConfig.placeholder"
        @input="returnFn"
      />
    </view>
    <view class="l-r" v-if="!disabled">
      <input :disabled="disabled"
        type="number"
        placeholder="请输入验证码"
        v-model="form.data.code"
        @input="returnFn"
        style="margin-right: 40upx;"
      />
      <view :class="{'get-code-btn': true, 'is-get-code': !isAllowGetNum}" class="get-code-btn" @tap="getCode()">{{ getVerificationTxt }}</view>
    </view>
  </view>
</template>

<script>

export default {
  data() {
    return {
      form: {
        data: {
          val: '',
          code: ''
        }
      },
      array: [],
      index: 0,
      defaultConfig: {
        bdt: false,
        bdb: true,
        titleColor: '#333',
        textColor: '#333',
        label: '单行输入框',
        name: 'input',
        required: false,
        placeholder: '请输入手机号码'
      },
      isAllowGetNum: true,
      getVerificationTxt: '获取验证码',
      uuid: ''
    }
  },
  watch: {
    cData: {
      handler(val) {
        this.watchDataMain(val)
      },
      deep: true
    },
    config: {
      handler(val) {
        this.copyConfig()
      },
      deep: true
    }
  },
  props: {
    child:{
      type:Boolean,
      default:false,
    },
    // 是否禁用disable
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 初始值传值，用于回显
    cData: {
      type: [String, Number],
      required: false,
      default() {
        return ''
      }
    },
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    }
  },
  computed: {

  },
  mounted() {
      this.copyConfig()
      if (!this.$validate.isNull(this.cData)) {
        this.watchDataMain(this.cData)
      }
  },
  methods: {
    getCode () {
      if(!this.isAllowGetNum) return
      // 获取验证码
      const {flag,msg} = this.$validate.isMobile(this.form.data.val)
      if(!flag) {
        this.$uniPlugin.toast(msg)
        return
      } else {
        this.uuid = this.$common.getTokenUuid()
        const params = {
          phone: this.form.data.val, // 用户填写的手机号
          uuid: this.uuid, // 随机编码
        }
        this.$uniPlugin.toast('发送中')
        this.$api.sys.sendSms(params, res => {
          // debugger
          this.resetTime(60)
          this.$uniPlugin.toast('发送成功')
        })
      }
    },
    /**
     * 倒计时
     * @param time
     */
    resetTime(time) {
      this.isAllowGetNum = false
      const countdownMinute = time || 30 // 1分钟倒计时

      const startTimes = new Date() // 开始时间 new Date('2016-11-16 15:21');
      const endTimes = new Date(startTimes.setSeconds(startTimes.getSeconds() + countdownMinute)) // 结束时间
      const curTimes = new Date() // 当前时间
      let surplusTimes = endTimes.getTime() / 1000 - curTimes.getTime() / 1000 // 结束毫秒-开始毫秒=剩余倒计时间

      // 进入倒计时
      let countdowns = setInterval(() => {
        surplusTimes--
        // eslint-disable-next-line no-unused-vars
        let minu = '' + Math.floor(surplusTimes / 60)
        let secd = '' + Math.round(surplusTimes % 60)
        // console.log(minu+':'+secd);
        minu = minu.length === 1 ? '0' + minu : minu
        secd = secd.length === 1 ? '0' + secd : secd
        this.getVerificationTxt = secd + '秒后重试'
        if (surplusTimes <= 0) {
          // console.log('时间到！');

          this.getVerificationTxt = '获取验证码'
          clearInterval(countdowns)
          countdowns = true
          this.isAllowGetNum = true
        }
      }, 1000)
    },
    /**
       * 初始化拷贝config对象
       */
    copyConfig() {
      const that = this
      const obj = that.config
      Object.keys(obj).forEach(function(key) {
        that.defaultConfig[key] = obj[key]
      })
    },
    /**
       * 监听Data主逻辑方法
       */
    watchDataMain(val) {
      this.form.data.val = val
    },
    /**
       * picker触发选中事件
       * @param e
       */
    returnFn(e) {
      const that = this
      if (that.disabled) return
      that.$emit('updateForm', { key: '' + that.config.name, value: [{extendValue: {code: that.form.data.code, uuid: that.uuid},value: that.form.data.val}]})
    }
  }
}
</script>

<style lang="scss" scoped>
  .color-topicC{
    color: $topicC !important;
  }
  .title-input{
    background-color:#ffffff;
    .l-l{
      line-height: 88upx;
      color: #333333;
      font-weight: 600;
      font-size:30upx;
    }
    .l-r{
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-bottom: 5px;
      &+.l-r {
        margin-top: 24upx;
      }
      input {
        height: 80upx;
        line-height: 80upx;
        color: #333;
        font-size: 32upx;
        text-align: left;
        display: inline-block;
        vertical-align: middle;
        width: calc(100% - 64upx);
        border: 2upx solid #ede9e9;
        border-radius: 10upx;
        padding: 0 20rpx;
      }
    }
  }
  .star{
    color: #F85E4C;
    padding-top: 6upx;
    font-size: 32upx;
    display: inline-block;
    margin-left: 10rpx;
  }
  .get-code-btn {
    width: 320upx;
    height: 68upx;
    line-height: 68upx;
    font-size: 30upx;
    text-align: center;
    border: 2upx solid $topicC;
    color: #fff;
    background: $topicC;
    @include rounded(32upx);
  }
  .is-get-code{
    @include opacity(0.5);
  }
</style>
