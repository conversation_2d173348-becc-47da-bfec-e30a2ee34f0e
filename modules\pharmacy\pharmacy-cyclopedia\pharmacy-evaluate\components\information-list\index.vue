<!--最新消息-->
<template>
  <view class="upcoming-remind">
    <view class="content clear-float">
      <view class="wait-to-be-done-list">
        <nui-list :indexlist="list" :entryType="entryType" @cateClick="cateClick" :isShowBtn="false"></nui-list>
      </view>
      <view class="img-wrapper" @click="handlePublish" v-if="entryType == 2">
        <view class="img-item">
          <image :src="file_ctx + 'static/image/business/hulu-v2/icon-publish-experience.png'"></image>
        </view>
        <view class="text">发布心得</view>
      </view>
    </view>
  </view>
</template>

<script>
import nuiList from '@/components/community/nui-list/nui-list.vue'
export default {
  name: 'Index',
  components: {
    nuiList
  },  
  props: {
    index: Number,
    entryType:{
      type:[String,Number],
      default:''
    },
    shareImg:{
      type:String,
      default:''
    },
    list: { // 数据列表
      type: Array,
      default() {
        return []
      }
    },
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      file_ctx: this.file_ctx,
    }
  },
  mounted() {
  },
  methods: {
    handlePublish(){
      this.$navto.push('PublishPost',{shareImg:this.shareImg})
    },
    cateClick (data) {
      this.navtoGo('Circle', {cid: data.circleClassifyId})
    },
    navtoGo(url = '', obj = {}) {
      this.$navto.push(url, obj)
    }
  }
}
</script>

<style lang="scss" scoped>
.upcoming-remind{
  //margin: 0 30upx 30upx 30upx;
  .content{
    position: relative;
    // padding:0upx 0 30upx;
    //@include rounded(20upx);
    background: #fff;
    border-radius:13upx;
    .wait-to-be-done-list{
      // min-height: 400upx;
      position: relative;
      // padding: 31rpx 44rpx;
      .li{
        background: #fff;
        margin-bottom: 20upx;
        border-bottom: 2upx solid $contentDdt;
        &:last-of-type{
          margin-bottom: 0;
          border-bottom: none;
        }
        .li-view {
          .t{
            margin-bottom: 16upx;
            .l{
              display: inline-block;
              vertical-align: middle;
              width: calc(100% - 160upx);
              font-size: 32upx;
              line-height: 48upx;
              .t-l-l{
                display: inline-block;
                vertical-align: middle;
                margin-right: 16upx;
              }
              .t-l-r{
                display: inline-block;
                vertical-align: middle;
                font-size: 24upx;
                line-height: 36upx;
                padding: 0 16upx;
                @include rounded(6upx);
              }
            }
            .r{
              display: inline-block;
              vertical-align: middle;
              text-align: right;
              width: 160upx;
            }
          }
          .time-text{
            font-size: 24upx;
            line-height: 36upx;
            color: #666;
            margin-bottom: 16upx;
          }
          .text-999-24 {
            font-size: 24upx;
            line-height: 36upx;
            color: #999;
          }
        }
      }
    }
    .img-wrapper{
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: fixed;
      right: 20rpx;
      bottom: 304rpx;
      width: 112rpx;
      height: 112rpx;
      background: #00B484;
      border-radius: 50%;
      box-shadow: 0rpx 8rpx 16rpx 0rpx rgba(0,180,132,0.2);
      .img-item{
        display: flex;
        width: 32rpx;
        height: 32rpx;
        image{
          width: 100%;
          height: 100%;
        }
      }
      .text{
        margin-top: 4rpx; 
        height: 28rpx;
        line-height: 28rpx;
        font-size: 20rpx;
        color:#fff;
      }
    }
  }
}
</style>
