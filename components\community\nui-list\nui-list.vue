<template>
	<view class="lists">
		<block v-for="(item, index) in indexlist" :key="index">
			<view class="list-wrapper">
				<view class="list">
					<view class="list-head">
						<!-- 匿名 -->
						<template v-if="item.isAnonymity == 1">
              <view class="heardimg-box-wrapper">
                <view class="heardimg-box">
                  <image class="head-inside" :src="file_ctx + 'static/image/system/icon-default-users.png'" mode="aspectFill" @click="userClick(item)">
                  </image>
                </view>
                <view class="info">
                  <text class="name" @click="userClick(item)">匿名用户</text>
                </view>
              </view>
						</template>

						<template v-else>
              <view class="heardimg-box-wrapper">
                <view class="heardimg-box" :style="item.lightLogoPath ? 'padding: 0 8rpx;' : ''">
                  <image class="head-pendant" v-if="item.lightLogoPath" :src="file_ctx + item.lightLogoPath" @click="userClick(item)"></image>
                  <image class="head-inside" :src="item.headPath ? file_ctx + item.headPath : defaultAvatar" mode="aspectFill" @error="imgErr($event, 'take')" @click="userClick(item)" ></image>
                </view>
                <!-- 头饰挂件 -->
                <view class="info">
                  <rich-text class="name" @click="userClick(item)" :nodes="item.nickName || '<span>昵称</span>'"></rich-text>
                </view>
                <template v-if="item.isAddV == 1">
                  <view class="heardimg-addv-icon">
                    <image v-if="item.vType == 2" :src="file_ctx + 'static/image/system/avatar/icon-v-e.png'" mode="aspectFill" @error="imgErr($event,'take')"></image>
                    <image v-else-if="item.vType == 1" :src="file_ctx + 'static/image/business/hulu-v2/icon-vType.png'" mode="aspectFill" @error="imgErr($event,'take')"></image>
                  </view>
                </template>
              </view>
						</template>
            <!-- #ifdef MP-WEIXIN -->
            <!-- #ifdef VUE2 -->
            <slot name="head-suffix{{index}}"></slot>
            <!-- #endif -->
            <!-- #ifdef VUE3 -->
            <slot :name="`head-suffix${index}`"></slot>
            <!-- #endif -->
            <!-- #endif -->
            <!-- #ifndef MP-WEIXIN -->
            <slot name="head-suffix" v-bind="item"></slot>
            <!-- #endif -->
					</view>
					<view class="list-body">
						<view class="list-content" v-if="item.isDel" @click="itemClick(item)">
							抱歉，此内容已被删除
						</view>
						<template v-else>
							<view class="list-content" @click="itemClick(item)">
                  <rich-text class="title" :nodes="item.title"></rich-text>
                  <rich-text class="dec" :nodes="item.materialType === 2 ? item.intro : item.content"></rich-text>
							</view>

							<!-- 图片 -->
							<view class="list-images" v-if="item.imagesPath && item.materialType !== 2" @click.stop.prevent="itemClick(item)">
                <view v-for="(img, index2) in item.imagesPath.split(',').slice(0, 6)" :key="index2">
                  <image @error="imgErr($event,'take')" class="item" :src="img.indexOf('http') !== -1 ? img : file_ctx + img" mode="widthFix" v-if="item.imagesPath.split(',').length == 1">
                  </image>

                  <view class="item" v-else>
                    <image @error="imgErr($event,'take')" class="itemimage" :src="img.indexOf('http') !== -1 ? img : file_ctx + img" mode="aspectFill"></image>
                  </view>
                </view>
							</view>

							<!-- 语音 -->
							<template v-if="item.audioPath">
								<view class="list-audio">
									<nui-audio :src="file_ctx + item.audioPath" :poster="file_ctx + item.audioPath"></nui-audio>
								</view>
							</template>

							<!-- 视频 -->
							<view class="list-video" v-if="item.videosPath && item.materialType === 2">
								<view
                  class="item video"
                  :style="{
                    width: videoInfo[item.id] ? videoInfo[item.id].width + 'rpx' : '300px',
                    height: videoInfo[item.id] ? videoInfo[item.id].height + 'rpx' : '225px'
                  }"
                  @click="itemClick(item)"
                >
                  <image
                    v-if="item.imagesPath"
                    mode="aspectFit"
                    class="video-poster"
                    :src="isDomainUrl(item.imagesPath)"
                    @load="videoLoadedmetadata($event, item)"
                  />
                  <video
                    v-else
                    class="itemvideo"
                    :poster="isDomainUrl(item.imagesPath)"
                    :src="isDomainUrl(item.videosPath)"
                    :controls="false"
                    :show-center-play-btn="false"
                    @loadedmetadata="videoLoadedmetadata($event, item)"
                  ></video>
                  <view class="rounded-play-btn-wrapper">
                    <view class="rounded-play-btn"></view>
                  </view>
								</view>
							</view>

							<!-- 产品卡片 -->
							<template v-if="item.products">
								<nui-card-product v-for="(e, eIndex) in item.products" :product="e" :key="eIndex"></nui-card-product>
							</template>

							<template v-if="item.filesPath">
								<view v-for="(item, index3) in item.filesPath" :key="index3">

								</view>
							</template>
						</template>

						<!-- 底部数据展示 -->
						<view class="list-footer" @click="itemClick(item)">
              <view class="list-footer-l" v-if="isShowBtn">
                <view class="cate" @click="cateClick(item)">

									#{{ item.circleClassifyName }}
								</view>
                <view class="cate" v-if="isShowGambit && item.topicIdsArr.length" @click="handleGambitDetail(item.topicIdsArr)">
                  #{{ topicIdsName(item.topicIdsArr) }}
                </view>
              </view>
              <view class="list-footer-r" v-if="isShowStatistics">
                <view class="list-item">
                  <view class="item" v-if="item.accountId === accountId">
                    <view class="num">
                      <uni-icons type="eye" :size="18" />
                      <text>{{ $common.bigNumberTransform((item.virtualReadNumber + item.readNumber) || 0 )}}</text>
                    </view>
                  </view>
                  <view class="item">
                    <view class="num">
                      <view class="img" style="display:flex;width:26rpx;height:24rpx;">
                        <image v-if="item.likeSubscribeStatus == 1" :src="file_ctx + 'static/image/business/hulu-v2/icon-like-active.png'"></image>
                        <image v-else :src="file_ctx + 'static/image/business/hulu-v2/icon-like.png'"></image>
                      </view>
                      <text>{{$common.bigNumberTransform( (item.virtualLikeNumber + item.likeNumber) || 0) }}</text>
                    </view>
                  </view>
                  <view class="item">
                    <view class="num">
                      <view class="img" style="display:flex;width:28rpx;height:28rpx;">
                        <image v-if="item.collectSubscribeStatus == 1" :src="file_ctx + 'static/image/business/hulu-v2/icon-like-active.png'"></image>
                        <image v-else :src="file_ctx + 'static/image/business/hulu-v2/icon-star.png'"></image>
                      </view>
                      <text>{{ $common.bigNumberTransform((item.virtualCollectNumber + item.collectNumber) || 0) }}</text>
                    </view>
                  </view>
                  <view class="item">
                    <view class="num">
                      <view class="img" style="display:flex;width:26rpx;height:24rpx;"><image :src="file_ctx + 'static/image/business/hulu-v2/icon-comment.png'"></image></view>
                      <text>{{ $common.bigNumberTransform((item.virtualCommentNumber + item.commentNumber) || 0) }}</text>
                    </view>
                  </view>
                </view>

                <view class="item2-footer-btn">
                  <template v-if="owner && !item.isDel">
                    <view class="item2" @click.stop.prevent="stopPrevent">
                      <view class="num">
                        <button class="flex-box btn" type="warn" @tap="$emit('del', item)">
                          <uni-icons type="trash" color="#fff" :size="16" />
                        </button>
                      </view>
                    </view>
                    <view class="item2" @click.stop.prevent="stopPrevent" v-if="item.processStatus !== 1">
                      <view class="num">
                        <button class="flex-box btn" type="primary" style="margin:0 10rpx;" @tap="$emit('edit', item)">
                          <uni-icons type="compose" color="#fff" :size="16" />
                        </button>
                      </view>
                    </view>
                  </template>
                  <template v-if="mode === 'collect' && !item.isDel">
                    <view class="item2" @click.stop.prevent="stopPrevent">
                      <view class="num">
                        <button class="flex-box btn" type="warn" @tap="$emit('collect', item)">
                          <uni-icons type="trash" color="#fff" :size="16" />
                        </button>
                      </view>
                    </view>
                  </template>
                </view>
              </view>
						</view>
            <!-- #ifdef MP-WEIXIN -->
            <!-- #ifdef VUE2 -->
            <slot name="item-footer{{index}}"></slot>
            <!-- #endif -->
            <!-- #ifdef VUE3 -->
            <slot :name="`item-footer${index}`"></slot>
            <!-- #endif -->
            <!-- #endif -->
            <!-- #ifndef MP-WEIXIN -->
            <slot name="item-footer" v-bind="item"></slot>
            <!-- #endif -->
					</view>

				</view>

			</view>

		</block>


	</view>
</template>

<script>
	import UniIcons from '@/components/uni/uni-icons/uni-icons.vue'
  import { isDomainUrl } from '@/utils/index'
  import { mapState } from 'vuex'
	export default {
		name: 'nui-list',
		components: {
			UniIcons
		},
		data() {
			return {
        $common: this.$common,
				file_ctx: this.file_ctx,
				defaultAvatar: this.file_ctx + 'static/image/system/avatar/icon-default-avatar.png',
        isDomainUrl,
        videoInfo: {},
        isShowBtnVal:null, //是否显示圈子
			};
		},
		props: {
      postsParams: {
        type: Object,
        default: () => {
          return {}
        }
      },
			owner: {
				type: Boolean,
				default: false
			},
			mode: {
				type: String,
				default: '',
				validator: function (value) {
					return ['','collect'].includes(value)
				}
			},
			indexlist: {
				type: [Object, Array],
			},
      isShowBtn:{
        type:Boolean,
        default:true
      },
      entryType:{
        type:String,
        default:''
      },
      isShowGambit:{
        type:Boolean,
        default:false
      },
      // 展示底部数据统计
      isShowStatistics: {
        type: Boolean,
        default: true
      },
      // 点击是否跳转到详情
      isjumpDetail: {
        type: Boolean,
        default: true
      }
		},
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId
      })
    },

		methods: {
      topicIdsName(list){
        let obj = list.find(item=>item.id == this.postsParams.topicIds) || list[0]
        return obj.name || '' 
      },
      handleGambitDetail(list){
        let obj = list.find(item=>item.id == this.postsParams.topicIds) || list[0] 
        this.$navto.push('GambitDetail',{id:obj?.id})
      },      
      videoLoadedmetadata(e, item) {
        let { width, height } = e.detail
        // 竖屏
        if (width < height) {
          width = width/(height/600)
          height = 600
        } else {
          height = height/(width/600)
          width = 600
        }
        this.$set(this.videoInfo, item.id, {width, height})
      },
			imgErr(that,dat){
				console.log(that, dat)
				that[dat] = this.file_ctx + '/static/image/system/icon-image-error.png'
			},
			stopPrevent () {},
			navtoGo(url, obj = {}) {
				this.$navto.push(url, obj)
			},
			itemClick(data) {
				if (data.isDel) {
					this.$uniPlugin.toast('抱歉，此内容已被删除')
					return
				}
        this.$emit('item-click', data)
        if (!this.isjumpDetail) return
				// 跳转到内部窗口并且传递链接
        if (data.materialType === 2) {
          this.navtoGo('postVideoList', {id: data.id, params: JSON.stringify(this.postsParams),isShowBtn:this.isShowBtn,entryType:this.entryType})
        } else {
          this.navtoGo('PostsDetail', {id: data.id,isShowBtn:this.isShowBtn})
        }

			},
			cateClick(data) {
				// 跳转到内部窗口并且传递链接
				this.$emit('cateClick', data)
			},
			userClick(data) {
				if (data.isAnonymity == 1) {
					this.$uniPlugin.toast('该帖子为匿名发表，无法查看该用户')
					return
				}
				const accountId = data.accountId
				// 是否是自己
				if (this.$common.getKeyVal('user', 'accountId') === accountId) {
					this.$navto.pushTab('Personal', {})
				} else {
					this.navtoGo('PersonalHomePage', { homePageAccountId:accountId,isShowBtn:this.isShowBtn })
				}
				// 跳转到内部窗口并且传递链接

			},
		}
	};
</script>

<style scoped lang="scss">
	/deep/ uni-swiper .uni-swiper-wrapper {
		overflow-y: auto !important;
	}

	/deep/ uni-swiper-item {
		overflow-y: auto !important;
	}

	.list {
		background: #ffffff;
		overflow: hidden;
		position: relative;
    padding: 24rpx 32rpx 0;
	}

	.list-body {
		margin-top: 10px;
    padding-bottom: 24rpx;
	}

	.list-head {
		display: flex;
    align-items: center;
    .heardimg-box-wrapper{
      display: flex;
      align-items: center;
      .heardimg-box{
        display: flex;
        width: 48rpx;
        height: 48rpx;
        position: relative;
        .head-pendant{
          position: absolute;
          top: -10rpx;
          left: 0;
          width: 70rpx;
          height: 70rpx;
        }
        .head-inside{
          width: 100%;
          height: 100%;
          border-radius: 50%;
          overflow: hidden;
        }
      }
      .info{
        margin:0 4rpx 0 12rpx;
        .name{
          font-family: '思源黑体';
          font-weight: 400;
          font-size: 26rpx;
          color: #1D2029;
        }
      }
      .heardimg-addv-icon{
        display: flex;
        width: 24rpx;
        height: 24rpx;
        margin-top: 2rpx;
        image{
          width: 100%;
          height: 100%;
        }
      }
    }
	}

	.list-content {
		.title  {
      font-family: '思源黑体';
      font-weight: 600;
      font-size: 32rpx;
      color: #1D2029;
      line-height: 44rpx;
		}

		.dec {
			word-break: break-all;
			text-overflow: ellipsis;
			-webkit-box-orient: vertical;
      -webkit-line-clamp: 3 !important;
      display: -webkit-box !important;
      overflow: hidden !important;
      font-family: '思源黑体';
      font-weight: 400;
      font-size: 28rpx;
      color: #4E5569;
      line-height: 40rpx;
			// max-height: 500rpx;
      max-height: calc(3 * 40rpx); /* 用行高计算最大高度 */
      margin: 12rpx 0 20rpx;

		}
	}

	.list-images {
		font-size: 0;
		overflow: hidden;
		position: relative;

		.item:nth-child(3n) {
			margin-right: 0;
		}

		.itemimage1 {
			width: 200px;

			object-fit: cover;
			position: absolute;
		}

		.item {
			float: left;
			width: 32.2%;
			height: 103px;
			max-height: 225px;
			margin-top: 1.1%;
			margin-right: 1.1%;
			box-sizing: border-box;
			position: relative;
			overflow: hidden;
			background: #f8f8f8;
      border-radius: 8rpx;
      border: 1rpx solid #EAEBF0;

			.itemimage {
				width: 100%;
				height: 100%;
				object-fit: cover;
				position: absolute;
			}
		}
	}

	.list-video {
		.item {
			box-sizing: border-box;
			border-radius: 12px;
			.itemvideo {
        width: 100%;
        height: 100%;
			}
		}
    .video {
      position: relative;
      .rounded-play-btn-wrapper {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 2;
      }
      .rounded-play-btn {
        opacity: .8;
        width: 50rpx;
        height: 50rpx;
        border-bottom-left-radius: 20rpx;
        background-color: #fff;
        transform: rotate(90deg) skewX(-30deg) scale(1, .866);
        &::before, &::after {
          content: "";
          position: absolute;
          background-color: inherit;
          width: 50rpx;
          height: 50rpx;
          border-bottom-left-radius: 20rpx;
        }
        &::before {
          transform: rotate(135deg) skewY(-45deg) scale(.707, 1.414) translate(-50%, 0);
        }
        &::after {
          transform: rotate(-135deg) skewX(-45deg) scale(1.414, .707) translate(0%, 50%);
        }
      }
    }
	}

	.list-footer {
    display: flex;
    justify-content: space-between;
    margin-top: 24rpx;
    .list-footer-l{
      display: flex;
      .cate {
        display: flex;
        align-items: center;
        justify-content: center;
        background: #EAEBF0;
        border-radius: 40rpx;
        font-family: '思源黑体';
        font-weight: 400;
        font-size: 20rpx;
        // color: #4E5569;
        color: #143666;
        padding:6rpx 16rpx;
        margin-right: 12rpx;
        height: fit-content;
        &:last-child{
          margin-right: 0;
        }
			}
    }
    .list-footer-r{
      display: flex;
      flex: 1;
      flex-direction: column;
      .list-item{
        display: flex;
        justify-content: flex-end;
        .item{
          margin-right: 44rpx;
          .num{
            display: flex;
            align-items: center;
            height: 40rpx;
            line-height: 40rpx;
            image{
              width: 100%;
              height: 100%;
            }
            text{
              font-family: '思源黑体';
              font-weight: 400;
              font-size: 28rpx;
              color: #4E5569;
              margin-left: 6rpx;
            }
          }
          &:last-child{
            margin-right: 0;
          }
        }
      }
      .item2-footer-btn{
        display: flex;
        align-items: center;
        margin-top: 15rpx;
        justify-content: flex-end;
      }
    }
	}
	.flex-box {
        display: flex;
        align-items: center;
        flex-direction: row;
    }
    .btn {
        padding: 0 24upx;
        margin: 0;
        height: 42upx;
        line-height: 42upx;
    }
	.list-wrapper {
		border-bottom: 1upx solid $borderColor;
	}
  .video-poster {
    max-width: 600rpx;
    max-height: 600rpx;
    width: 100%;
    height: 100%;
  }
</style>
