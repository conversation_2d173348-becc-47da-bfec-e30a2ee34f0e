
<template>
  <view class="title-input clear-float" :class="{'bdt':defaultConfig.bdt,'bdb':defaultConfig.bdb && !child}">
    <view class="l-l" :style="{'color': defaultConfig.titleColor}">
      {{defaultConfig.label}}
      <text class="star" v-if="defaultConfig.required">*</text>
    </view>
    <view class="l-r" @click="riqidayClick">
      <view class="inputtxt">
        <template v-if="inputvalue && inputvalue != ''">
          <view class="value">
            {{inputvalue}}
          </view>
        </template>
        <template v-else>
          请选择日期
        </template>
      </view>
    <!--  <input :disabled="disabled"
        :value="inputvalue"
        @input="returnFn"
        placeholder="请输入"
      /> -->
   <!--   <view class="util" v-if="config.unitDesc">
        {{config.unitDesc}}
      </view> -->
    </view>

    <title-picket :show='riqidayVisible' :value='timervalue' type='date' @cancel='riqidayClick' @query='queryRiqiday' :column='3'>
    </title-picket>


  </view>


</template>

<script>
import titlePicket from '../title-picket/index.vue';
export default {
  components:{
    titlePicket
  },
  data() {
    return {
      riqidayVisible:false,
      inputvalue:"",
      timervalue:"now",
      // form: {
      //   data: {
      //     val: ''
      //   }
      // },
      array: [],
      index: 0,
      defaultConfig: {
        bdt: false,
        bdb: true,
        titleColor: '#333',
        textColor: '#333',
        label: '单行输入框',
        name: 'input',
        required: false,
      }
    }
  },
  watch: {

    cdata(n){
      console.log('jk',n)
      this.inputvalue = n
      if(n && n != ''){
        this.timervalue = n
      }
    },
    config: {
      handler(val) {
        this.copyConfig()
      },
      deep: true
    }
  },
  props: {
    child:{
      type:Boolean,
      default:false,
    },
    // 是否禁用disable
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 初始值传值，用于回显
    cdata: {
      type: [String, Number],
      required: false,
      default() {
        return ''
      }
    },
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    }
  },
  computed: {

  },
  mounted() {
    console.log(this.config)
      this.copyConfig()
      if (this.cdata && this.cdata != '') {
        // this.watchDataMain(this.cData)
        console.log('thiw',this.cdata)
        this.inputvalue = this.cdata
        this.timervalue = this.cdata
      }
  },
  methods: {
    riqidayClick() {
    	this.riqidayVisible = !this.riqidayVisible

    },
    queryRiqiday(obj) {
    	console.log(obj)
    	// uni.showToast({
    	// 	title: e.detail.selectValue,
    	// 	icon: 'none'
    	// })
      if (this.disabled) return
    	this.riqidayVisible = !this.riqidayVisible;
      if(!this.riqidayVisible){
        this.$emit('update', { key: '' + this.config.name, value: obj })
      }
    	// this.setData({
    	// 	riqidayVisible: false
    	// })
    },
    /**
       * 初始化拷贝config对象
       */
    copyConfig() {
      const that = this
      const obj = that.config
      Object.keys(obj).forEach(function(key) {
        that.defaultConfig[key] = obj[key]
      })
    },
    /**
       * 监听Data主逻辑方法
       */
    watchDataMain(val) {
      this.form.data.val = val
    },
    /**
       * picker触发选中事件
       * @param e
       */
    // returnFn(e) {
    //   const that = this
    //   if (that.disabled) return
    //   that.$emit('updateForm', { key: '' + that.config.name, value: that.form.data.val })
    // }
  }
}
</script>

<style lang="scss" scoped>
  .color-topicC{
    color: $topicC !important;
  }
  .title-input{
    background-color:#ffffff;
    .l-l{
      line-height: 88upx;
      color: #333333;
      font-weight: 600;
      font-size:30upx;
    }
    .l-r{
      margin-bottom: 5px;
      display: flex;
      align-items: center;
          padding-bottom: 20rpx;
      .inputtxt {
        height: 80upx;
        line-height: 80upx;
        color: #333;
        font-size: 32upx;
        text-align: left;
        display: inline-block;
        vertical-align: middle;
        width: calc(100% - 64upx);
        border-bottom: 1upx solid $borderColor;
        border-radius: 10upx;
        padding: 0 20rpx;
        color: #a3a3a3;

      }
      .value{
        color: #000;
        height: 100%;
        display: flex;
        align-items: center;
         font-size: 32upx;
      }
      .util{
        width: 64upx;
        font-size: 28upx;
            overflow: hidden;
            text-align: center;

      }
    }
  }
  .star{
    color: #F85E4C;
    padding-top: 6upx;
    font-size: 32upx;
    display: inline-block;
        margin-left: 10rpx;
  }
</style>
