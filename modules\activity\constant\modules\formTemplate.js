// 患者登记表单模板字段对应输入类型 input 普通输入框 date 日期时间选择框 radio image[multiple 属性是否可以多选] 上传图片类型 select[multiple]下拉框

// belong 所属 说明  base 基础端  history 历史病况  diagnose 诊断病况  其他报告 other

const defaultObject = {
  name:{
    type:'input',
    belong:'base',
  },
  address:{
    type:'input',
    belong:'base',
  },
  allergicHistory:{
    type:'input',
    belong:'base',
  },
  birthDate:{
    type:'date',
    belong:'base',
  },
  createTime:{
    type:'date',
    belong:'base',
  },
  familyHistory:{
    type:"input",
    belong:'base',
  },
  familyTies:{
    type:'select',
    belong:'base',
    options:[
      {
        label:"配偶",
        value:1,
        id:1,
        noinput:true,
        openStatus:1,
      },{
        label:"子女",
        value:2,
        id:2,
        noinput:true,
        openStatus:1,
      },{
        label:'父母',
        value:3,
        id:3,
        noinput:true,
        openStatus:1,
      },{
        label:"兄弟",
        value:4,
        id:4,
        noinput:true,
        openStatus:1,
      },{
        label:"姐妹",
        value:5,
        id:5,
        noinput:true,
        openStatus:1,
      },{
        label:"其他",
        value:6,
        id:6,
        noinput:true,
        openStatus:1,
      }
    ]
  },
  gender:{
    type:"radio",
    belong:'base',
    options:[
      {
        label:"男",
        value:1,
        id:1,
        noinput:true,
        openStatus:1,
      },{
        label:"女",
        value:2,
        id:2,
        noinput:true,
        openStatus:1,
      },{
        label:"未知",
        value:3,
        id:3,
        noinput:true,
        openStatus:1,
      }
    ]
  },
  headPath:{
    type:"file",
    belong:'base',
    // multiple:true, //开启多选图片
  },

  idNumber:{
    type:"input",

  },
  isPrimaryAccount:{
    type:'radio',
    belong:'base',
    options:[
      {
        label:"是",
        value:1,
        id:1,
        noinput:true,
        openStatus:1,
      },{
        label:"否",
        value:2,
        id:2,
        noinput:true,
        openStatus:1,
      }
    ],
  },
  memberType:{
    type:"radio",
    belong:'base',
    options:[
      {
        label:"普通成员",
        value:1,
        id:1,
        noinput:true,
        openStatus:1,
      },{
        label:"新生儿",
        value:2,
        id:2,
        noinput:true,
        openStatus:1,
      }
    ]
  },
  // name:{
  //   type:"input",
  // },
  nation:{
    type:"input",
    belong:'base',
  },
  origo:{
    type:"input",
    belong:'base',
  },
  phone:{
    // type:"phone",
    type:"input",
    belong:'base',
  },
  // previousHistory:{
  //   type:"input",

  // },
  registerSource:{
    type:"select",
    belong:"base",
    options:[{
      label:"自主注册",
      value:1,
      id:2,
      noinput:true,
      openStatus:1,
    },{
      label:"邀约注册",
      value:2,
      id:2,
      noinput:true,
      openStatus:1,
    },{
      label:"管理员添加",
      value:3,
      id:3,
      noinput:true,
      openStatus:1,
    }]
  },

  userName:{
    type:"input",
  },
  // prescriptionPath:{
  //    type:"file",
  //    multiple:true,
  // },
  // inspectionReport:{
  //   type:"file",
  //   belong:""
  // },
  // 诊断疾病
  principalDisease:{
    type:'textarea',
    belong:"diagnose",
    inputStyle:"background:#fff",
  },
  diseaseTime:{
    type:"input",
    belong:"diagnose",
  },
  mainSuit:{
    type:'textarea',
    belong:"diagnose",
    inputStyle:"background:#fff",
  },

  // 其他报告
  prescriptionPath:{
    type:"file",
    belong:"other",
    inputStyle:"background:#fff",
    multiple:true,
  },
  inspectionReport:{
     type:"file",
    belong:"other",
    inputStyle:"background:#fff",
    multiple:true,
    // mul
  },

  previousHistory:{
    type:'textarea',
    belong:"history",
    inputStyle:"background:#fff",
  },
  familyHistory:{
    type:'textarea',
    belong:"history",
    inputStyle:"background:#fff",
  },
  allergicHistory:{
    type:'textarea',
    belong:"history",
    inputStyle:"background:#fff",
  },
  idNumber:{
    type:"input",
    belong:'base',
  },

  idNumber:{
    type:"input",
    belong:'base',
  },
  isPrimaryAccount:{
    type:"radio",
    belong:'base',
    options:[
      {
        label:"是",
        value:1,
        id:1,
        noinput:true,
        openStatus:1,
      },{
        label:"否",
        value:2,
        id:2,
        noinput:true,
        openStatus:1,
      }
    ]
  }














}




// 对应字典连接符号
const link = '_';
// 截取之后对应数组对应值
const valueidx = 0;
const labelidx = 1;


// 患者档案库新增模板字段位置就是表单位置
const templateArr = [
  'name',
  'phone',
  'nation',
  'origo',
  'address',
  'allergicHistory',
  'birthDate',
  'concomitantDisease',
  'diseaseTime',
  'familyHistory',
  'familyTies',
  'gender',
  'idNumber',
  'inspectionReport',
  'isPrimaryAccount',
  'mainSuit',
  'memberType',
  'prescriptionPath',
  'previousHistory',
  'principalDisease',
  // 'registerSource',
  'headPath',

]


// 患者档案库默认描述值
const templateInfo = {
  'name':"姓名",
  'phone':"手机号",
  'nation':"民族",
  'origo':"籍贯",
  'address':"详细地址",
  'allergicHistory':"过敏史",
  'birthDate':"个人身份信息-出生日期",
  'concomitantDisease':"诊断疾病-伴发疾病",
  'diseaseTime':"诊断疾病-现病时长",
  'familyHistory':"家庭史",
  'familyTies':"家庭关系",
  'gender':"个人身份信息-性别",
  'headPath':"头像",
  'idNumber':"个人身份信息-证件号码",
  'inspectionReport':"检查报告（可多张图片）",
  'isPrimaryAccount':"是否主账号",
  'mainSuit':"诊断疾病-主诉",
  'memberType':"成员类型",
  'prescriptionPath':"处方单/发票信息（可多张图片）",
  'previousHistory':"历史病况-既往史",
  'principalDisease':"诊断疾病-主要疾病",
  'registerSource':'注册来源',
}

export {
  defaultObject,
  link,
  valueidx,
  labelidx,
  templateArr,
  templateInfo
};
