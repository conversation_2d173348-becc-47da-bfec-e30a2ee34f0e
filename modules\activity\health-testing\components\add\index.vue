<template>
  <view class='formsize'>
    <swiper class="swiper" v-if="currentIndex !== null" :current="currentIndex" :duration="durationNum" :interval="intervalNum" @change="onSwiperChange">
      <swiper-item class="detail-item" @touchmove.stop="stopTouchMove">
        <view class="detail-content" >
          <view class="content">
            <view class="detail-head">
              <view class="problems-num">共 {{ swiperList.length }} 题</view>
              <h3 class="problems-title">{{ defatilObj.title }}</h3>
              <view class="problems-text">{{ defatilObj.describe }}</view>
            </view>
            <view class="detail-info">
              <rich-text :nodes="defatilObj.background"></rich-text>
            </view>
            <view class="detail-reference" @click="handleClickShowDetail" v-if="defatilObj.methodSample">
              <view class="reference-l">参考文献</view>
              <view class="reference-r"><uni-icons :size="10" color="#666" type="right" /></view>
            </view>
          </view>
        </view>
        <view class="detail-bottom">
          <!-- #ifdef MP-WEIXIN -->
          <button class="recommend" open-type="share">推荐给朋友</button>
          <!-- #endif -->

          <!-- #ifdef H5 -->
          <button class="recommend" @click="handleClickShare">推荐给朋友</button>
          <!-- #endif -->

          <button class="free" @click="scrollSwiper('add','free')">免费测试</button>
        </view>
      </swiper-item>
      <swiper-item class="free-item" @touchmove.stop="stopTouchMove" v-if="defatilObj.resultAnalysis">
        <view class="free-content">
          <view class="free-bor">
            <view class="free-title">自评方法</view>
            <view class="free-info">
              <rich-text :nodes="defatilObj.resultAnalysis"></rich-text>
            </view>
          </view>
        </view>
        <view class="free-bottom" @click="scrollSwiper('add')">
          开始答题
        </view>
      </swiper-item>
      <swiper-item class="topic-item" @touchmove.stop="stopTouchMove" v-for="(item,index) in swiperList" :key="item.id">
        <view class="topic-content">
          <view class="topic-num">第 {{ index +1 }} 题，共 {{ swiperList.length }} 题</view>
          <view class="topic-title">{{ item.title }}</view>
          <view 
            v-for="(option,index2) in item.formTemplateOptions" 
            :class="option.currentIndex == index2 ? 'topic-option active' :'topic-option'" 
            :key="option.id"
            @click.stop="handleClickOption(index,index2)"
          >
            {{ option.value }}
          </view>
        </view>
        <view class="topic-bottom-body">
          <view class="topic-bottom bottom-l" @click.stop="scrollSwiper('subtract',index)">
            <uni-icons :size="14" color="#666" type="left" style="margin-top:5rpx;" />上一题
          </view>
          <view class="topic-bottom bottom-r" @click.stop="scrollSwiper('add',index)" v-if="currentIndex < currentRecord">
            下一题<uni-icons :size="14" color="#fff" type="right" style="margin-top:5rpx;" />
          </view>
          <view class="topic-bottom bottom-r" @click="handleSubmit(index)" v-if="tempIndex == currentIndex && optionFlag">
            {{ submitText }}
          </view>
        </view>
      </swiper-item>
      <swiper-item class="evaluate-item" @touchmove.stop="stopTouchMove">
        <scroll-view :scroll-y="true" style="height:100%;">
          <view class="evaluate-content">
            <view class="content">
              <view class="evaluate-head">
                <view class="problems-num">本次测试结果</view>
                <h3 class="problems-title">{{ rulesOneObj.conclusionTitle }}</h3>
                <view class="problems-text">此结果不代表诊疗意见,请以医生建议为准</view>
                <view class="problems-btn" @click="handleClickProblem">
                  <view class="text-btn">分值：{{ scoreVal || 0 }}<span></span></view>
                </view>
              </view>
              <template v-if="defatilObj.resultConclusion">
                <view class="evaluate-suggest">解读及建议</view>
                <view class="evaluate-info">
                  <rich-text :nodes="defatilObj.resultConclusion"></rich-text>
                </view>
              </template>
              <view class="evaluate-info" style="margin:20rpx 0;">
                <rich-text :nodes="rulesOneObj.conclusionDesc"></rich-text>
              </view>
              <view class="evaluate-info">测试结果会随着身体状况和生活坏境的改变而发生变化，建议定期自测，持续评估。</view>
              <!-- #ifdef MP-WEIXIN -->
              <view class="evaluate-remind">
                <template v-if="expectedObj && expectedObj.expectedTime">
                  <view class="remind-l">将在 {{expectedObj.expectedTime}} 提醒你再次测试</view>
                  <view class="cancel" @click="handleRemindCancel">取消</view>
                </template>
                <template v-else>
                  <view class="remind-l">提醒我4周后再次测试</view>
                  <view class="remind-r" @click="handleSetRemind">设置提醒</view>
                </template>
              </view>
              <!-- #endif -->
            </view>
            <view class="evaluate-bottom">
              <!-- #ifdef MP-WEIXIN -->
              <button class="recommend" open-type="share">推荐给朋友</button>
              <!-- #endif -->

              <!-- #ifdef H5 -->
              <button class="recommend" @click="handleClickShare">推荐给朋友</button>
              <!-- #endif -->
              <button class="free" v-if="cmainid || businessId" @click="handleClickJump">再次测试</button>
              <button class="free" v-else>免费测试</button>
            </view>
            <view class="evaluate-recommend" v-if="recommendList.length > 0">
              <view class="title">推荐测试</view>
              <view class="content-item" v-for="item in recommendList" :key="item.id" @click="handelRecommendClick(item)">
                <view class="content-l">
                  <view class="content-title">{{ item.title }}</view>
                  <view class="content-topic">共 {{ item.formTemplateNum }} 题</view>
                </view>
                <view class="content-r"><uni-icons :size="10" color="#666" type="right" /></view>
              </view>
            </view>
          </view>
        </scroll-view>
      </swiper-item>
    </swiper>

    <uni-popup class="swiperPopup" ref="swiperPopup" id="swiperPopup" type="bottom" v-if="defatilObj.methodSample">
      <view class="detail">
        <view class="header-btn" @click="handleCancel"><uni-icons :size="16" style="font-weight: bold;" color="#999" type="bottom" /></view>
        <view class="title">参考文献</view>
        <!-- <view class="content">{{ defatilObj.methodSample }}</view> -->
        <view class="content">
          <rich-text :nodes="defatilObj.methodSample" :style="{whiteSpace: 'pre-wrap'}"></rich-text>
        </view>
      </view>
    </uni-popup>

    <uni-popup class="scorePopup" ref="scorePopup" id="scorePopup" type="bottom">
      <view class="scoreDetail">
        <view class="header-btn" @click="handleScoreCancel"><uni-icons :size="16" style="font-weight: bold;" color="#999" type="bottom" /></view>
        <view class="title">测试得分：{{ scoreVal }}</view>
        <!-- <view class="content">{{ defatilObj.methodSample }}</view> -->
        <view class="info">测试得分根据你此次的回答选项计算得出，不代表诊疗意见，请以医生建议为准。</view>
        <view class="info" style="margin:30rpx 0;">分值区间及对应结论参考：</view>
        <view class="content">
          <view class="content-item" v-for="item in rulesObj.scoreItemRulesList" :key="item.id">
            <view class="content-item-l">
              <span></span>
              <view class="text">{{ item.conclusionTitle }}</view>
            </view>
            <view class="content-item-r">{{ item.minScore }}-{{ item.maxScore }}分</view>
          </view>
        </view>
      </view>
    </uni-popup>

    <uni-popup class="sharePopup" ref="sharePopup" id="sharePopup" type="top">
    <view class="share-box" @click="handleClickShareClose">
      <image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-everyday-share-box.png'"></image>
    </view>
    </uni-popup>
    <!-- 健康自测完成答题弹窗 -->
    <prayPopup class="healthPopup" :popupParams="popupParams" ref="healthPopup" id="healthPopup" @handlePrayPopupClose="handlePrayPopupClose"></prayPopup> 
  </view>
</template>

<script>
  import { mapState } from 'vuex'
  import UniIcons from '@/components/uni/uni-icons/uni-icons'
  import titleHeader from '@/modules/activity/components/title-header/index.vue'
  import prayPopup from '@/components/basics/pray-popup/index.vue'
  import uniPopup from '@/components/uni/uni-popup'
  // import { getQueryObject } from '@/utils/index.js';
  export default {
    props: {
      configList:{
        type:Array,
        default:()=>[]
      },
      businessId:{
        type:String,
        default:'',
      },
      currentNum:{
        type:Number,
        default:0,
      },
      cmainid:{
        type: [String, Number],
        default: null 
      },
      healthTesting:{
        type:Object,
        default:()=>{}
      }
    },
    components: {
      UniIcons,
      titleHeader,
      uniPopup,
      prayPopup,
    },
    data(){
      return{
        file_ctx: this.file_ctx,
        headerTop:55,
        headerobj:{
          headBgColor:'#fff',
          titleType:"txt",
          titleTxt:"",
          currentIndex:0,
          contentColor:"#000",
          borderColor:"#000",
        },
        duration:1000,
        currentIndex: null, // 当前索引  
        swiperList:[],
        optionScore:0,
        currentRecord:null,
        optionFlag:false,
        timer: null,
        timer2: null,
        defatilObj:{},
        index2:null, //最后一题的下标
        scoreVal:null,
        rulesObj:{}, //评分规则对象
        rulesOneObj:{}, //匹配到的一条规则对象
        tempIndex:null,
        recommendList:[], //推荐列表的数据
        expectedObj:null, //预计推送时间对象
        popupParams:null,
        timer3:null, //开始自测到结束自测时长/退出健康自测，单位为s
        answerDuration:0, 
        submitText:'提交',
        durationNum:500,
        intervalNum:5000,
        currentChangeIndex:0,
        saveId:'',
        saveValue:'',
      }
    },
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId,
        curSelectUserInfo: state => state.curSelectUserInfo,
        isLogin: state => state.isLogin,
      }),
      // 计算swiper-item的总数
      totalItems() {
        return 2 + this.swiperList.length
      },
    },
    watch: {
      configList:{
        handler(list) {
          list.forEach(item=>{
            item.formTemplateOptions.forEach(item2=>{
              item2.radio = item2.radio / 100
              item2.currentIndex = null
            })
          })
          this.swiperList = list
        },
        immediate: true
      },
      isLogin:{
        handler(){
          if(!this.isLogin){
            this.submitText = '登录获取结果'
          }
        },
        immediate: true
      },
      currentNum(val){
        if(!this.healthTesting?.currentChangeIndex){
          this.currentIndex += val
        }
      },
      businessId:{
        async handler(){
          await this.researchScorerulesQueryOneBusinessId()
          this.researchQueryOne()
          if(!this.healthTesting?.myEvaluating){
            this.scoreresultlogBrowseLog()
            this.getSubscribeGetOneByBusiness()
          }
          this.researchQueryPage()

        },
        immediate:true
      },
      healthTesting:{
        handler(obj){
          if(obj?.currentChangeIndex){
            this.index2 = obj.topicCurrentIndex
            this.currentIndex = obj?.currentChangeIndex + 1
            this.swiperList[obj.topicIndex].formTemplateOptions[obj.topicCurrentIndex].currentIndex = obj.topicCurrentIndex
            obj?.swiperListRadio.length && obj?.swiperListRadio.forEach((item,index)=>{
              this.swiperList[index].radioVal = item
            }) 
          }
        },
        immediate:true
      },
      
    },
    onLoad(options){
      const query = this.$validate.isNull(this.$Route.query) ? options : this.$Route.query
      this.headerobj.titleTxt = query.title
    },

    mounted(){
      console.log(this.currentIndex,'this.currentIndex12335566')
    },
    methods:{
      onSwiperChange(e) {
        this.currentChangeIndex = e.detail.current
      },
      advertisementmanagementListValidPopAccount(){
        this.$api.drugBook.advertisementmanagementListValidPopAccount({accountId:this.accountId,useType:4}).then((res)=>{
          if(res.data?.length){
            this.popupParams = res.data
            this.$nextTick(()=>{
              this.$refs.healthPopup.$refs.carouselPopups.open()
              this.advertisementmanagementrecordInsert()
            })
            //神策埋点
            // #ifdef MP-WEIXIN
            this.handleClickTrackPopup(1)
            // #endif
          }
        })     
      },
      // 自测弹窗曝光量
      async advertisementmanagementrecordInsert(){
        await this.$api.drugBook.advertisementmanagementrecordInsert({accountId:this.accountId,advertisementId:this.popupParams[0].id,businessType: 1}) //businessType: 3,  //默认传3(关闭)
      },
      // 每次进首页都会弹窗
      handlePrayPopupClose(){
        this.$refs.healthPopup.$refs.carouselPopups.close()
        // #ifdef MP-WEIXIN
        this.handleClickTrackPopup(2)
        // #endif
      },
      // #ifdef MP-WEIXIN
      handleClickTrackPopup(btnType){
        getApp().globalData.sensors.track("PopupClick",
          {
            'page_name' : '健康自测-完成答题',
            'popup_id' : 'healthPopup',
            'popup_name' : '健康自测-完成答题',
            'click_type' : btnType == 1 ? '进入弹窗' : '取消弹窗',
          }
        ) 
      },
      // #endif
      // #ifdef MP-WEIXIN
      handleClickTrack(type){
        getApp().globalData.sensors.track("PopupClick",
          {
            'page_name' : '健康自测',
            'popup_id' : type == 1 ? 'scorePopup' : 'swiperPopup',
            'popup_name' : type == 1 ? '测试得分' : '参考文献',
            'click_type' : type == 1 || type == 2 ? '进入弹窗' : '取消弹窗',
          }
        ) 
      },
      // #endif
      handleClickShare(){
        this.$refs.sharePopup.open()
      },
      handleClickShareClose(){
        this.$refs.sharePopup.close()
      },
      handleClickProblem(){
        this.$refs.scorePopup.open()
        // #ifdef MP-WEIXIN
        this.handleClickTrack(1)
        // #endif
      },
      handleClickShowDetail(){
        this.$refs.swiperPopup.open()
        // #ifdef MP-WEIXIN
        this.handleClickTrack(2)
        // #endif
      },
      // 查询是否有订阅
      getSubscribeGetOneByBusiness(){
        let params = {
          // businessId:this.defatilObj.id,
          businessId:this.businessId,
          accountId:this.accountId
        }
        this.$api.activity.getSubscribeGetOneByBusiness(params).then(res=>{
          // console.log(res,'res00----=')
          if(res.data !==""){
            this.expectedObj = {...res.data,expectedTime:this.$common.formatDate(new Date(res.data.expectedTime), 'MM-dd')}
          } else {
            this.expectedObj = null
          }
        })
      },

      // 取消提醒
      handleRemindCancel(){
        this.$api.activity.healthSubscribeCancel({id:this.expectedObj.id}).then(res=>{
          // console.log(res,'res-13-55')
          if(res.data == true){
            this.$uniPlugin.toast('取消提醒成功')
            this.getSubscribeGetOneByBusiness()
          }
        })
      },

      // 设置提醒
      async handleSetRemind(){
        let that = this
        const { centerUserId = '' } = this.curSelectUserInfo || {}
        // 获取openid
        let openId = await this.$ext.wechat.getOpenId()
        await this.$uniPlugin.subscribeMessage(['Mcy5OI_4JX9w5U2ojFoIAdsmcFnrk2XHjUuPl8jKaao'])
        const tmplId = 'Mcy5OI_4JX9w5U2ojFoIAdsmcFnrk2XHjUuPl8jKaao'
        uni.requestSubscribeMessage({
          tmplIds: [tmplId],
          success:async(res)=>{
            // console.log(res,'res0123----')
            if (res[tmplId] === 'accept') {
              // console.log('用户同意订阅消息');
              // 执行订阅后的逻辑
              let logParamList = {
                appId: that.$appId,
                templateId: tmplId,
                openId: openId,
                subscribeStatus: res[tmplId],
                businessType : 7,
                businessId: that.defatilObj.id,
                accountId: that.accountId,
                userId: centerUserId
              }
              await that.$api.common.wxsubscribemessagelogInsertBatch({wxSubscribeMessageLogList:logParamList})
              let params = {
                businessId:that.defatilObj.id,
                accountId:that.accountId,
                userId:centerUserId,
                businessType:1,
                channelCode:that.healthTesting?.gs
              }
              await that.$api.activity.subscribeInsert(params)
              that.getSubscribeGetOneByBusiness()
              this.$uniPlugin.toast('消息订阅成功')

              // #ifdef MP-WEIXIN
              getApp().globalData.sensors.track("Subscription",
                {
                  'content_belong_circle' : '',
                  'function_name' : '健康自测',
                  'subscription_type':'一次性订阅'
                }
              )
              // #endif
            } else {
              // console.log('用户拒绝订阅消息');
              this.$uniPlugin.toast('消息订阅失败')
              // 处理用户拒绝的情况
            }
          },
          fail(err) {
            console.error('请求订阅消息失败：', err);
          }
        });
        this.handleHealthSelfTestClick('设置提醒')
      },
      
      handleClickJump(){
        // modules/activity/health-testing/testing-detail
        this.handleHealthSelfTestClick('重新测试')
        this.$navto.replaceAll('TestingDetail',{ id:this.businessId })
      },

      handleHealthSelfTestClick(type){
        getApp().globalData.sensors.track("HealthSelfTestClick",{'click_type':type})
      },

      // 推荐列表跳转
      handelRecommendClick(item){
        this.$navto.replaceAll('TestingDetail', item)
      },

      // 推荐列表
      researchQueryPage(){
        let params = {
          current:1,
          size:5,
          condition:{
            collectionType:5,
            excludeId:this.businessId,
          }
        }
        this.$api.activity.researchQueryPage(params).then(res => {
          this.recommendList = res.data.records 
        })
      },
      
      researchQueryOne(){
        this.$api.activity.researchQueryOne({ id: this.businessId }).then(res => {
          this.defatilObj = res.data
          this.$common.setKeyVal('user','healthInfo',this.defatilObj)
          this.$nextTick(()=>{
            if(this.healthTesting?.saveId){
              this.$emit('updateForm',{key:this.healthTesting?.saveId,value:this.healthTesting?.saveValue})
            }
            if(this.healthTesting?.myEvaluating){
              let num = this.defatilObj?.resultAnalysis && 1 || 0
              this.durationNum = 0 
              this.intervalNum = 0
              this.currentIndex = (this.swiperList.length + 1) + num
              this.scoreVal = Math.ceil(this.healthTesting?.resultScore)
              this.rulesObj.scoreItemRulesList.forEach(item=>{
                if(this.scoreVal >= item.minScore && this.scoreVal <= item.maxScore){
                  this.rulesOneObj = item
                }
              })
            } else {
              !this.healthTesting?.currentChangeIndex && (this.currentIndex = 0)
            }
          })
        })
      },
      
      
      scoreresultlogBrowseLog(){
        console.log('进来浏览了1111')
        // if(!this.healthTesting?.myEvaluating){
          let params = {
            accountId : this.$common.getKeyVal('user', 'accountId', true),
            researchId : this.businessId, //问卷id
            channelCode : this.healthTesting?.gs || ''
          }
          this.$api.activity.scoreresultlogBrowseLog(params)
        // }
      },
      
      // 获取评分规则设置
      async researchScorerulesQueryOneBusinessId(){
        const res = await this.$api.activity.researchScorerulesQueryOneBusinessId({ businessId: this.businessId })
        if(res.data !== '' && res.data.maxScore > 100){
          res.data.maxScore = res.data.maxScore / 100
          res.data.minScore = res.data.minScore / 100
          res.data.scoreItemRulesList.forEach(item=>{
            item.maxScore = item.maxScore / 100 
            item.minScore = item.minScore / 100 
          })
          this.rulesObj = res.data
          if(this.healthTesting?.currentChangeIndex){
            this.handleLoginGetResults(this.healthTesting.topicIndex)
            this.durationNum = 0 
            this.intervalNum = 0
          }
        }
      },

      // 禁止用户滑动
      stopTouchMove(){
        return true
      },

      handleCancel(){
        this.$refs.swiperPopup.close()
        // #ifdef MP-WEIXIN
        this.handleClickTrack(4)
        // #endif
      },

      handleScoreCancel(){
        this.$refs.scorePopup.close()
        // #ifdef MP-WEIXIN
        this.handleClickTrack(3)
        // #endif
      },

      initHeader(height){
        this.headerTop = height
      },

      scrollSwiper(type,index) {
        if(this.timer2){
          clearTimeout(this.timer2)
        }
        if(index == 'free'){ //判断是按钮免费测试，开始计时答题时长
          this.startTimer()
        }
        this.timer2 = setTimeout(() => {
        this.timer2 = null
        // this.startTimer()
          if(type == 'add'){
            this.currentIndex += 1
          } else {
            this.currentIndex -= 1
            if(this.currentIndex == 0){
              this.answerDuration = 0
              clearInterval(this.timer3)
            }
          }
        },500)
      },

      startTimer(){
        this.timer3 = setInterval(() => {
          this.answerDuration++
        }, 1000);
      },
      
      handleClickOption(index,index2){
        if (this.timer) {
          clearTimeout(this.timer)
          this.timer = null 
        }
        this.swiperList[index].formTemplateOptions.forEach(item=>{
          item.currentIndex = null
        })
        this.swiperList[index].formTemplateOptions[index2].currentIndex = index2

        if(this.defatilObj.resultAnalysis){ //判断有无自评方法
          this.tempIndex = (this.swiperList.length-1)+2
        } else {
          this.tempIndex = (this.swiperList.length-1)+1 
        }

        this.timer = setTimeout(() => {
          this.swiperList[index].radioVal = this.swiperList[index].formTemplateOptions[index2].radio
          if(this.tempIndex == this.currentIndex){
            this.optionFlag = true
          } else {
            this.currentIndex += 1
            this.currentRecord = this.currentIndex
          }
          this.index2 = index2
        }, 500)
        this.saveId = this.swiperList[index].id
        this.saveValue = this.swiperList[index].formTemplateOptions[index2].value
        // #ifdef MP-WEIXIN
        getApp().globalData.sensors.track("ConductingHealthSelfTest",
          {
            'self_test_type' : this.healthTesting.title,
            'self_test_questions' : this.swiperList[index].title,
            'self_test_answer_choices' : this.saveValue,
          }
        ) 
        // #endif
        this.$emit('updateForm',{key:this.saveId,value:this.saveValue})
      },

      handleLoginGetResults(index){
        clearInterval(this.timer3) //清空答题倒计时
        this.timer3 = null 
        this.swiperList[index].radioVal = this.swiperList[index].formTemplateOptions[this.index2].radio
        let score = this.swiperList.reduce((sum,item)=>{
          sum = sum + (item.radioVal ?? 0)
          return sum
        },0)
        this.scoreVal = Math.ceil(score)
        this.rulesObj.scoreItemRulesList.forEach(item=>{
          if(this.scoreVal >= item.minScore && this.scoreVal <= item.maxScore){
            this.rulesOneObj = item
          }
        })
        // #ifdef MP-WEIXIN
        getApp().globalData.sensors.track("CompletedHealthSelfTest",
          {
            'self_test_type' : this.healthTesting.title,
            'self_test_result' : this.rulesOneObj.conclusionTitle || '',
            'duration' : this.answerDuration, // 时长
          }
        ) 
        // #endif
        this.$emit('handleCommit')
        this.advertisementmanagementListValidPopAccount()
      },
      // 提交
      handleSubmit(index){
        if(this.isLogin){
          this.handleLoginGetResults(index)
        } else {
          this.$navto.push('Login',{formPage: 'TestingDetail',formPageParams:encodeURIComponent(
            JSON.stringify({
              businessId: this.businessId,
              topicIndex:index,
              topicCurrentIndex:this.index2,
              currentChangeIndex:this.currentChangeIndex,
              saveId:this.saveId,
              saveValue:this.saveValue,
              swiperListRadio:this.swiperList.length && this.swiperList.map(item=>(item.radioVal)) || [],
            })
          )})
        }
      },

      returnFn(obj){
        // console.log(obj,'obj666')
      }
    },
    destroyed () {
      clearTimeout(this.timer)
      clearTimeout(this.timer2)
      clearInterval(this.timer3)
    }
 }
</script>

<style lang='scss' scoped>

  .swiper{
    height: 100vh;
    background-color: #ebeef2;
    .detail-item,.evaluate-item{
      height: 100%;
      .detail-content,.evaluate-content{
        // height: 100%;
        padding:20upx 20rpx 100rpx 20rpx;
        .content{
          border-radius: 20upx;
          padding:30upx 20upx;
          background-color: #fff;
          .detail-head,.evaluate-head{
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .problems-num{
              margin-bottom: 80upx;
            }
            .problems-title{
              font-size: 40upx;
              font-weight: bold;
              color:#000;
              margin-bottom: 20upx;
              width: 480rpx;
              text-align: center;
            }
            .problems-text{
              margin-bottom: 30upx;
              width: 480rpx;
              text-align: center;
            }
            .problems-btn{
              display: flex;
              .text-btn{
                font-size: 24upx;
                padding:10upx 15upx;
                border-radius: 15upx;
                border:1upx solid #d2d6db;
                margin-right: 10upx;
              }
              &:last-child{
                margin-right: 0;
              }
            }
          }
          .detail-info{
            margin:50upx 0 20upx 0;
            border-radius: 20upx;
            padding:40upx 30upx;
            background-color: #f5f7f7;
          }
          .detail-reference{
            display: flex;
            justify-content: space-between;
            padding:0 20upx;
          }
        }
      }
      .detail-bottom,.evaluate-bottom{
        display: flex;
        position: fixed;
        width: calc(100% - 40upx);
        bottom: 0;
        left: 0;
        padding:20upx 20upx 80upx;
        background-color: #fff;
        /deep/.recommend,.free{
          display: flex;
          justify-content: center;
          align-items: center;
          // padding:20upx 0 !important;
          padding:5rpx 0;
          width: 50%;
          border-radius: 40upx;
          font-weight: 600;
          font-size: 32upx;
          border:1upx solid #d2d6db !important;
        &::after{
          border: none !important;
        }
        }
        .free{
          width:calc(50% - 20upx);
          margin-left: 20upx;
          background-color: #00b3a0;
          color:#fff;
          border: none;
        }
      }
    }
    .free-item,.topic-item{
      height: calc(100% - 20upx) !important;
      width: calc(100% - 40upx) !important;
      padding:20upx 20upx 0;
      .free-content,.topic-content{
        padding:25upx;
        border-radius: 20upx;
        background-color: #fff;
        .free-bor{
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          border: 1upx solid #f0f3f7;
          border-radius: 20upx;
          padding:0 50upx;
          .free-title{
            padding:120upx 0 60upx;
            font-size: 40upx;
            font-weight: bold;
            color:#000;
          }
          .free-info{
            padding-bottom: 150upx;
          }
        }
      }
      .free-bottom{
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 30upx;
        width: 100%;
        padding:20upx 0;
        color:#fff;
        font-size: 32upx;
        font-weight: 600;
        border-radius: 40upx;
        background-color: #00b3a0;
      }
    }
    .topic-item{
      overflow-y: scroll;
      .topic-content{
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        border-radius: 20upx;
        padding:30upx 20upx;
        .topic-num{
          margin-bottom: 80upx;
        }
        .topic-title{
          font-size: 40upx;
          font-weight: bold;
          color:#000;
          margin-bottom: 80upx;
          width: 480rpx;
          text-align: center;
        }
        .topic-option{
          display: flex;
          align-items: center;
          justify-content: center;
          width: calc(100% - 40upx);
          padding:20upx;
          color:#000;
          font-size: 32upx;
          border-radius: 20upx;
          text-align: center;
          background-color:#f5f7f9;
          margin-bottom: 20upx;
        }
        .active{
          background-color: #00ae9b;
          color:#fff;
          font-weight: 600;
        }
      }
      .topic-bottom-body{
        display: flex;
        justify-content: space-between;
        .topic-bottom{
          // width: 120upx;
          padding:20upx 30upx;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 30upx;
          background-color: #ebeef2;
          color:#6d7483;
          border: 1upx solid #d3d6dc;
          font-weight: bold;
          border-radius: 40upx;
        }
        .bottom-r{
          background-color: #51545c;
          color:#fff;
        }
      }
    }
    .evaluate-item{
      // position: relative !important;
      .evaluate-content{
        // padding-bottom: 150rpx !important;
        .content{
          padding:40rpx;
          .evaluate-head{
            padding:40upx;
            background-color: #f5f7f7;
            border-radius: 30upx;
            .problems-num{
              margin-bottom: 60upx;
            }
            .problems-title{
              color:#009b8a;
              font-size: 45rpx;
            }
            .problems-text{
              font-size: 24rpx;
              color:#959aa4;
            }
            .problems-btn{
              .text-btn{
                padding:10upx 30upx;
                border-radius: 75rpx;
                span{
                  width: 0;
                  height: 0;
                  display: inline-block;
                  border-left: 8rpx solid #999;
                  border-right: 8rpx solid transparent;
                  border-top: 8rpx solid transparent;
                  border-bottom: 8rpx solid transparent;
                  margin-left: 10rpx;
                }
              } 
            }
          }
          .evaluate-suggest{
            margin:30upx 0 20upx;
          }
          .evaluate-info{
            font-size: 30upx;
          }
          .evaluate-remind{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30upx;
            border-top: 1px solid #f0f0f0;
            font-size: 30upx;
            padding-top: 20upx;
            .remind-r{
              padding:10upx 20upx;
              border-radius: 30upx;
              border:1upx solid #529b8a;
              color:#529b8a;
              font-size: 30upx;
            }
            .cancel{
              padding: 10rpx 35rpx;
              border-radius: 30rpx;
              border: 1rpx solid #c6c7c7;
              font-size: 28rpx;
            }
          }
        }
        .evaluate-bottom{
          position:static !important;
          width: 100%;
          padding:20upx 0 30upx;
          background-color: transparent;
          .recommend{
            background-color: #00b3a0;
            color:#fff;
            border: none;
          }
          .free{
            background-color: #fff;
            color:#6d7483;
          }
        }
        .evaluate-recommend{
          border-radius: 20rpx;
          overflow: hidden;
          padding-left: 40rpx;
          background-color: #fff;
          .title{
            padding:40rpx 0;
            border-bottom: 1px solid #eee;
          }
          .content-item{
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding:30rpx 0;
            border-bottom: 1px solid #eee;
            .content-l{
              .content-title{
                color:#000;
                margin-bottom: 10rpx;
              }
              .content-topic{
                
              }
            }
            .content-r{
              margin-right: 20rpx;
            }
            &:last-child{
              border-bottom: 0;
            }
          }
        }
      }
    }
  }
  
  .detail{
    height: 90vh;
    position: relative;
    background-color: #f5f7f7;
    padding:20rpx 30rpx;
    border-radius: 13rpx 13rpx 0 0;
    .header-btn{
      display: flex;
      align-items: center;
      justify-content: center;
      width: 120rpx;
      height: 60rpx;
      background: #ebeded;
      border-radius: 30rpx;
      margin: 0 auto;
    }
    .title{
      color:#000;
      font-weight: 600;
      font-size: 34rpx;
      margin: 60rpx 0 30rpx;
    }
    .content,.content-bott{
      font-size: 32rpx;
      line-height: 60rpx;
      color:#000;
    }
    .content-bott{
      margin-top: 40rpx;
    }
  }
  .scoreDetail{
    height: 90vh;
    position: relative;
    background-color: #f5f7f7;
    padding:20rpx 30rpx;
    border-radius: 13rpx 13rpx 0 0;
    .header-btn{
      display: flex;
      align-items: center;
      justify-content: center;
      width: 120rpx;
      height: 60rpx;
      background: #ebeded;
      border-radius: 30rpx;
      margin: 0 auto;
    }
    .title{
      color:#000;
      font-weight: 600;
      font-size: 34rpx;
      margin: 60rpx 0 30rpx;
    }
    .content{
      .content-item{
        display: flex;
        justify-content: space-between;
        padding:30rpx 20rpx;
        border-top:1rpx solid #eee;
        .content-item-l{
          display: flex;
          align-items: center;
          span{
            display: inline-block;
            width: 10rpx;
            height: 10rpx;
            border-radius: 50%;
            background: #009b8a;
            margin-right: 15rpx;
          }
          .text{
            text-overflow: ellipsis;
            width: 480rpx;
            overflow: hidden;
            white-space: nowrap;
          }
        }
        .content-item-r{

        }
      }
    }
  }
  .share-box{
    display: flex;
    width: 670rpx;
    height: 252rpx;
    margin-left: auto;
    margin-right: 20rpx;
    image{
      width: 100%;
      height: 100%;
    }
  }
</style>