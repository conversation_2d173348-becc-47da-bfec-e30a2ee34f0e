import request from '@/common/util/request'
import env from '@/config/env'

/**
 * 社区许愿池请求工具类，
 * 注意：API接口只做请求数据和格式化数据的事情，其他事务一概不处理
 */
export default {
    // 点亮
    wishingwellAddLight (param) {
        const url = env.ctx + 'dm/api/v1/wishingwell/add/light'
        return request.postForm(url, param)
    },
    // 获取点亮配置
    wishingwellGetLightSet (param) {
        const url = env.ctx + 'dm/api/v1/wishingwell/get/light/set'
        return request.get(url, param)
    },
    // 新增许愿
    wishingwellAddWish (param) {
        const url = env.ctx + 'dm/api/v1/wishingwell/add/wish'
        return request.postForm(url, param)
    },
    // 获取许愿
    wishingwellGetWish (param) {
        const url = env.ctx + 'dm/api/v1/wishingwell/get/wish'
        return request.get(url, param)
    },
    // 获取首页统计
    wishingwellGetIndexData (param) {
        const url = env.ctx + 'dm/api/v1/wishingwell/get/index/data'
        return request.get(url, param)
    }
}
