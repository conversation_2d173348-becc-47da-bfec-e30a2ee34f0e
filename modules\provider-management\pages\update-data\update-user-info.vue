<template>
  <view class='update-data'>
    <view class="botTab">
      <view class="additionalContent">
        <view class="must">*</view>
        <view class="lineTitle">头像</view>
        <!-- <image class="avatar-url" :src="file_ctx + infoObj.avatarUrl"></image> -->
          <title-img
            :config="{
              padding: 0,
              background: 'none',
              margin: '10rpx',
              count: 1,
              multiSelectCount: 1,
            }"
            ref="upDataImage"
            @returnFn="(obj) => {imgReturnFn(obj)}"
            :cData="imageList"
          >
          </title-img>
      </view>
      <view class="botTabLine">
        <view class="must">*</view>
        <view class="lineTitle">昵称</view>
        <input type="text" placeholder="请输入" class="bookInput" v-model="queryOptions.nickName" />
      </view>
    </view>
    <!-- 底部 -->
    <view class="bottomBtn">
      <view class="comBtn" @click="handleSave">保存</view>
    </view>
  </view>
</template>

<script>
  import { mapState } from 'vuex'
  import TitleImg from "@/components/business/module/title-img/index.vue"
  export default {
    components:{
      TitleImg
    },
    data(){
      return{
        appId: this.$appId,
        file_ctx: this.file_ctx,
        queryOptions:{
        },
        imageList:[],
      }
    },
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId,
        fansRecord: state => state.fansRecord,
      }),
      infoObj() {
        return {
          name: this.fansRecord.nickName,
          avatarUrl: this.fansRecord.headPath ? this.fansRecord.headPath : this.defaultAvatar,
        }
      }
    },
    watch: {
      infoObj:{
        handler(val) {
          if(val.name){
            this.queryOptions.nickName = val.name
            this.imageList = [{url:this.file_ctx + val.avatarUrl,dir:this.file_ctx + val.avatarUrl,filePath:val.avatarUrl}]
          }
        },
        deep: true,
        immediate: true
      },
    },
    onLoad(option){
      const query = this.$validate.isNull(this.$Route.query) ? option : this.$Route.query
    },
    async mounted(){
    },
    methods:{
      imgReturnFn(list) {
        this.imageList = list
      },
      handleSave(){
        const codeUserInfo = this.$common.getKeyVal('user', 'codeUserInfo',true);
        if(!this.imageList.length){
          return this.$uniPlugin.toast('头像不能为空')
        }
        if(!this.queryOptions.nickName){
          return this.$uniPlugin.toast('昵称不能为空')
        }
        let params = {
          userId:codeUserInfo?.id || null,
          accountId:this.accountId,
          headPath:this.imageList.length && this.imageList[0].filePath || null,
          nickName:this.queryOptions.nickName,
          appId:this.appId,
        }
        this.$api.providerManagement.accompanyfansrecordUpdate(params).then(res=>{
          this.$uniPlugin.toast('保存成功')
          if(res.data?.id !==""){
            this.$common.setKeyVal('user', 'fansRecord', res.data)
            uni.switchTab({
              url: 'pages/my/index', // 这里替换成你的 tabBar 页面的路径
            });
          }
        })
      },
    },
 }
</script>

<style lang='scss' scoped>
  .update-data{
    margin: 0 32rpx;
    background: #F4F6FA;
  }
  .botTab{
    box-sizing: border-box;
    padding: 32rpx;
    width: 686rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    margin-top: 20rpx;
    margin-bottom: 200rpx;
    .lineHide{
      width: 0;
      overflow: hidden;
      height: 0;
    }
    .additionalContent{
      display: flex;
      align-items: center;
      border-bottom: 2rpx solid #EAEBF0;
      padding-bottom: 20rpx;
      // margin-top: 32rpx;
      .must{
        font-size: 28rpx;
        color: #FF5500;
      }
    }
    .lineTitle{
      font-weight: 500;
      font-size: 28rpx;
      color: #1D2029;
      width: 100rpx;
    }
    // .avatar-url{
    //   display: flex;
    //   width: 112rpx;
    //   height: 112rpx;
    //   border-radius: 50%;
    // }
    .botTabLine{
      width: 100%;
      height: 80rpx;
      line-height: 80rpx;
      // border-bottom: 2rpx solid #EAEBF0;
      display: flex;
      align-items: center;
      .must{
        font-size: 28rpx;
        color: #FF5500;
      }
      .lineValue{
        flex: 1;
      }
      .lineIcon{
        width: 32rpx;
        height: 32rpx;
      }
      .bookInput{
        width: 438rpx;
      }
      .botTabLine{
        width: 438rpx;
        height: 40rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 28rpx;
        color: #A5AAB8;
      }
    }
  }
  .bottomBtn{
    width: 750rpx;
    height: 180rpx;
    background: #FFFFFF;
    box-shadow: 0rpx -1rpx 0rpx 0rpx #EAEBF0;
    position: fixed;
    bottom: 0;
    left: 0;
    .comBtn{
      width: 686rpx;
      height: 88rpx;
      background: #00B484;
      border-radius: 44rpx;
      text-align: center;
      line-height: 88rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #FFFFFF;
      margin: 24rpx auto;
    }
  }
  .intro{
    display: flex;
    flex-direction: column;
    margin-top: 32rpx;
    .outline-bott{
      border-radius: 16rpx;
      border: 1rpx solid #D9DBE0;
      padding: 24rpx;
      margin-top: 24rpx;
      textarea{
        width: 100%;
        height: 160rpx;
        overflow-y: scroll;
      }
    }
  }
</style>
