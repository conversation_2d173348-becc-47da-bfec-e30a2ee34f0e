import Vue from 'vue';
import optionsMap from "./optionsMap.js"
import api from '@/service/api'
import common from '@/common/util/main'
import { getQueryObject } from '@/utils/index'
let currentId = 2;
// 保险套餐id集合
let productCodeMap = [{productCode:"0585089787",insuranceNum:5,name:"基础版"},{productCode:"6122857820",insuranceNum:10,name:'升级版'}]; // 测试
if(process.env.NODE_ENV == 'production'){
  productCodeMap = [{productCode:"1406320187",insuranceNum:5,name:"基础版"},{productCode:"9380172782",insuranceNum:10,name:'升级版'}]; // 生产
}
function getoptions(){
  return JSON.parse(JSON.stringify(optionsMap.filter(e=>e.id === currentId)[0]))
}
// 处理数据变为响应式数据
let currentOptions = Vue.observable(getoptions());
console.log('currentOptions',currentOptions);

currentOptions.getUserId = function (that) {
  const codeUserInfo = that.$common.getKeyVal('user', 'codeUserInfo',true);
  if(codeUserInfo.id) return codeUserInfo.id;
  const curSelectUserInfo = that.$common.getKeyVal('user', 'curSelectUserInfo',true);
  if(curSelectUserInfo.centerUserId) return curSelectUserInfo.centerUserId;
  const mpWxUserInfo = that.$common.getKeyVal('user', 'mpWxUserInfo',true);
  if(mpWxUserInfo.loginId) return mpWxUserInfo.loginId;
}
// 根据传递进来的云陪诊服务商id修改当前的陪诊参数
currentOptions.setCurrentOptions = function(options) {
  Object.keys(options).forEach(key => {
    if (!this.hasOwnProperty(key)) {
      Vue.set(this, key, options[key]) // 确保新增属性响应式
    } else {
      this[key] = options[key] // 已有属性直接赋值
    }
  })
}
// 根据当前路由参数处理云陪诊参数
currentOptions.handlingCloudProviderId = async function(pageOptions) {
  let {path,query,params} = pageOptions || {};
  let pages = getCurrentPages();
  if (!path){
    // 获取当前页面路由
    let currentPage = pages[pages.length - 1];
    let {route,options} = currentPage;
    path = route;
    query = options;
    params = {};
  }
  // 获取当前页面栈
  console.log('当前路由',path);

  // 判断当前路径是否属于陪诊模块 路由中是否含有 accompany-doctor字段
  if(path.indexOf('accompany-doctor') === -1) {
    return;
  }
  // 判断当前页面栈是否存在modules/accompany-doctor/home/<USER>
  let isExist = pages.some(e=>e.route === 'modules/accompany-doctor/home/<USER>');
  // 如果存在该页面则直接返回

  // 不存在则判断当前路由参数是否存在cloudProviderId
  let queryOptions = {...query,...params};
  if(queryOptions.scene){
    let {gbScene,gs,providerId} = getQueryObject(decodeURIComponent(decodeURIComponent(decodeURIComponent(queryOptions.scene))));
    let scCode = gbScene || gs;
    console.log('scCode',scCode,isExist,providerId,isExist && providerId);
    if(isExist && providerId) return;
    let data;
    if(!scCode) return;
    try {
      data = (await api.common.minichannellinkQueryOne({code:scCode})).data
    } catch (error) {
      console.log('error',error);
      return;
    }
    // 如果当前路由参数存在providerId则修改当前的云陪诊参数
    if(data && data.customParameters && getQueryObject(data.customParameters).providerId){
      queryOptions.providerId = getQueryObject(data.customParameters).providerId;
    }
  }
  let {providerId} = queryOptions;
  let cloudProviderId = providerId;
  if(cloudProviderId){
    await currentOptions.loadCloudProvider(cloudProviderId);
  }
}
// 根据传递进来的id修改当前的陪诊参数
currentOptions.loadCloudProvider = async function(providerId){
  uni.showLoading({title:'加载中...'})
  // 存储当前云服务商id到本地缓存中
  common.setKeyVal('system','cloudProviderId', providerId, true);

  // 将当前服务商ID添加到服务商ID列表中
  let providerRecords = common.getKeyVal('system', 'cloudProviderRecords', true);
  // 确保是数组
  if (!providerRecords || !Array.isArray(providerRecords)) {
    providerRecords = [];
  }

  // 添加当前ID到记录列表，包含时间戳
  const timestamp = new Date().getTime();
  const newRecord = {
    providerId,
    timestamp
  };

  // 检查是否已存在相同providerId的记录
  const existingIndex = providerRecords.findIndex(record => record.providerId === providerId);
  if (existingIndex !== -1) {
    // 更新已有记录的时间戳
    providerRecords[existingIndex] = newRecord;
  } else {
    // 添加新记录
    providerRecords.push(newRecord);
  }

  // 保存更新后的记录列表
  common.setKeyVal('system', 'cloudProviderRecords', providerRecords, true);

  // 同时保持原来的列表兼容
  let providerIdList = providerRecords.map(record => record.providerId);
  common.setKeyVal('system', 'cloudProviderIdList', providerIdList, true);

  let {data} = await api.accompanyDoctor.accompanyproviderQueryOne({id:providerId});
  if(!data) {
    uni.hideLoading();
    uni.switchTab({url:'/pages/index/index'})
    setTimeout(()=>{
      uni.showToast({title:'不存在该服务商',icon:'none'})
    },1000)
    return
  }
  // 获取平台服务商id
  let platformProviderId = currentOptions.optionsMap.filter(e=>e.id === 1)[0].providerId
  let {providerName:registerTitle,appid:appId,appName:title} = data;
  let newOptions = {title,appId,providerId,registerTitle,source:providerId === platformProviderId ? 1 : 2};
  console.log('当前云服务商信息',newOptions);
  currentOptions.setCurrentOptions(newOptions);
  uni.hideLoading();
  uni.showToast({title:'进入云陪诊门店',icon:'none'});
  return data;
},

currentOptions.optionsMap = JSON.parse(JSON.stringify(optionsMap));
currentOptions.productCodeMap = productCodeMap;
currentOptions.getoptions = getoptions;
console.log('currentOptions',currentOptions);
// 捕获request
currentOptions.catchRequest = request=>currentOptions.request = request;
// 因为数据分割 所以重写请求方法
// 重写get方法
currentOptions.accompanyDoctorGet = function (url, param){
  console.log('请求中读取',url,currentOptions.providerId);

  let providerId = currentOptions.providerId
  if(param?.providerId) return currentOptions.request.get(url, param)
  param = {...param,providerId}
  return currentOptions.request.get(url, param)
}
// 重写post方法
currentOptions.accompanyDoctorPost = function (url, param){
  // 创建参数的深拷贝以避免副作用，如果param为null/undefined则使用空对象
  let originalParamCopy = param ? JSON.parse(JSON.stringify(param)) : {};

  // 检查 omitProviderId 标记
  if (originalParamCopy.omitProviderId === true) {
    delete originalParamCopy.omitProviderId; // 移除标记
    if (originalParamCopy.condition) {
      delete originalParamCopy.condition.providerId; // 从 condition 中移除 providerId
    }
    delete originalParamCopy.providerId; // 也从顶层移除 providerId，以确保完全不传递
    return currentOptions.request.postJson(url, originalParamCopy);
  }

  // 如果没有 omitProviderId 标记，则执行原有逻辑
  const globalProviderId = currentOptions.providerId;

  if(param && param.condition && param.condition.providerId) {
      return currentOptions.request.postJson(url, param);
  }
  let paramToFill = originalParamCopy;

  paramToFill.providerId = globalProviderId; // 在顶层添加/覆盖 providerId
  if(paramToFill.condition){
    paramToFill.condition.providerId = globalProviderId;
  }else{
    paramToFill.condition = {providerId: globalProviderId};
  }
  return currentOptions.request.postJson(url, paramToFill);
}
// 重写putJSON方法
currentOptions.accompanyDoctorPutJson = function (url, param){
  let providerId = currentOptions.providerId
  if(param?.providerId) return currentOptions.request.putJson(url, param)
  param = {...param,providerId}
  return currentOptions.request.putJson(url, param)
}

// 重写delete方法
currentOptions.accompanyDoctorDelete = function (url, param = {}) {
  let providerId = currentOptions.providerId
  if(param?.providerId) return currentOptions.request.delete(url, param)
  param = {...param, providerId}
  return currentOptions.request.delete(url, param)
}
console.log('currentOptions',currentOptions);

export default currentOptions
