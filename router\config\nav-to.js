import router from '@/router'
import validate from '@/common/util/validate'
/* 1. push
跳转到普通页面，新开保留历史记录

2. replace
动态的导航到一个新 URL 关闭当前页面，跳转到的某个页面。

3. replaceAll
动态的导航到一个新 URL 关闭所有页面，打开到应用内的某个页面

4. pushTab
动态的导航到一个新 url 关闭所有页面，打开到应用内的某个tab */
export default {
  /**
   * 1. push
   * 跳转到普通页面，新开保留历史记录
   * @param {Object} name
   * @param {Object} params
   */
  push(name, params) {
    if (validate.isNull(params)) {
      params = {}
    }

    router.push({
      name: name,
      params: params
    })
  },
  /**
   * 路径路由格式   /pages/router/router1
   * @param name
   * @param params
   */
  pushPath(name, params) {
    if (validate.isNull(params)) {
      params = {}
    }
    console.log('name, params',name, params)
    router.push({
      path: name,
      query: params
    })
  },
  /**
   * 2.replace
   * 动态的导航到一个新 URL 关闭当前页面，跳转到的某个页面。
   * @param name
   * @param params
   */
  replace(name, params) {
    if (validate.isNull(params)) {
      params = {}
    }
    router.replace({
      name: name,
      params: params
    })
  },
  /**
   * 3.replaceAll
   * 动态的导航到一个新 URL 关闭所有页面，打开到应用内的某个页面
   * @param name
   * @param params
   */
  replaceAll(name, params) {
    if (validate.isNull(params)) {
      params = {}
    }
    router.replaceAll({
      name: name,
      params: params
    })
  },
  /**
   * 4. pushTab
   * 动态的导航到一个新 url 关闭所有页面，打开到应用内的某个tab
   * @param name
   * @param params
   */
  pushTab(name, params) {
    if (validate.isNull(params)) {
      params = {}
    }
    router.pushTab({
      name: name,
      params: params
    })
  },
  /**
   * 在 history 记录中后退多少步，类似 window.history.go(n)
   * @param number
   */
  back(number) {
    if (validate.isNull(number)) {
      number = 1
    }
    const pages = getCurrentPages()
    if (!validate.isNull(pages)) {
      if (pages.length < number || pages.length === 1 || pages.length === 0) {
        router.replaceAll({
          name: 'Index'
        })
      } else {
        router.back(number)
      }
    } else {
      router.replaceAll({
        name: 'Index'
      })
    }
  },


  /**
   * 5.replace
   * 根据路由路径跳转
   * @param path
   * @param params
   */
  replacePath(path, query) {
    console.log('query',query)
    if (validate.isNull(query)) {
      query = {}
    }

    if(!path.startsWith('/')){
      path = '/' + path;
    }

    router.replace({
      path: path,
      query:query
      // params: params
    })
  },
}
