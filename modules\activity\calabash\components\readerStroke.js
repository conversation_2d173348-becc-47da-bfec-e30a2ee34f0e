export default function(width, height, imgUrl) {
  let promise, resolve;
  promise = new Promise(res => resolve = res)
  let image = new Image()
  let pointR = 10;
  // 向内缩小系数
  let offset = 15;
  // 过滤倍数 (只能设计为奇数)
  let key = 3;
  image.crossOrigin = ''
  image.addEventListener('load', () => {
    var canvas = document.createElement('canvas');
    canvas.width = width
    canvas.height = height
    var ctx = canvas.getContext('2d');
    ctx.drawImage(image, 0, 0, canvas.width, canvas.height);
    // 获取图片的像素数据
    let imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const pixels = imageData.data;
    console.log('图片像素',pixels);
    // 存储像素坐标
    const pixelCoordinates = [];
    // 遍历像素数据
    for (let y = 0; y < canvas.height; y++) {
      for (let x = 0; x < canvas.width; x++) {
        const index = (y * canvas.width + x) * 4;
        const a = pixels[index + 3];
        if (a > 0) { // 如果像素不透明
          pixelCoordinates.push({ x, y });
        }
      }
    }
    console.log('所有像素数据',pixelCoordinates);
    // 聚类
    const clustering = Object.values(pixelCoordinates.reduce((recursive,item,index)=>{
        recursive[item.y] ? recursive[item.y].push(item) : recursive[item.y] = [item]
        return recursive
    },{}))
    console.log('分类后',clustering);
    let centerX = canvas.width / 2;
    let centerY = canvas.height / 2;
    // 获取边界
    let border = clustering.reduce((recursive,item,index)=>{
        recursive.push(...item.map((e,index)=>{
          if (index === 0 || index === item.length - 1) {
             const angle = Math.atan2(e.y - centerY, e.x - centerX);
             // 相对于当前点的位置向内收缩
             const newX = e.x - Math.cos(angle) * offset;
             const newY = e.y - Math.sin(angle) * offset;
             return { x: newX, y: newY, r: pointR, strokeStyle: 'red' };
          }
        }).filter(e=>e))
        return recursive
    },[])
    border = border.filter((e, index) => index % key === 0)
    console.log('过滤后', border);
    // 排序
    border = collate(border)
    // 填充
    sparselyFilled(border)
    console.log('最终生产物理边界',border);
    resolve(border)
  })
  // 排序
    function collate(maps) {
      let list1 = []
      let list2 = []
      let maxX = 0;
      maps.forEach((e)=>e.x > maxX ? maxX = e.x : '')
      maps.forEach((e)=>{
          if(e.x > maxX / 2) list1.push(e)
          else list2.push(e)
      })
      list2.reverse()
      return [...list1,...list2]
    }
    function sparselyFilled(list) {
        [...list].reduce((prevCircle,currentCircle,index)=>{
        let distanceResults = doCirclesIntersect(prevCircle,currentCircle)
          if(distanceResults.type === 'sparse'){
            const numberOfPoints = Math.ceil(distanceResults.distance / pointR);
            for (let i = 1; i < numberOfPoints; i++) {
                const ratio = i / numberOfPoints;
                const newX = prevCircle.x + (currentCircle.x - prevCircle.x) * ratio;
                const newY = prevCircle.y + (currentCircle.y - prevCircle.y) * ratio;
                list.splice(index + i, 0, {
                    r: pointR,
                    x: newX,
                    y: newY,
                    strokeStyle: 'red'
                });
            }
          }
        return currentCircle
      },false)
    }
    function doCirclesIntersect(circle1, circle2) {
    if(!circle1 || !circle2) return '请传入圆'
    // 计算两个圆心之间的距离
    const dx = circle1.x - circle2.x;
    const dy = circle1.y - circle2.y;
    const distance = Math.sqrt(dx * dx + dy * dy);
    // 计算两个圆的半径之和
    const radiusSum = circle1.r + circle2.r;
    // 密集
    if(distance < circle1.r) return 'dense'
    // 稀疏
    if(distance + circle1.r > radiusSum) return {type:'sparse',distance}
    // 判断两个圆是否相交
    return distance <= radiusSum;
  }

  image.src = imgUrl + `?id=${Math.random()}`
  return promise
}
