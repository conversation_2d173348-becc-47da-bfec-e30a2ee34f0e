import request from '@/common/util/request'
import env from '@/config/env'

/**
 * 系统请求工具类，
 * 注意：API接口只做请求数据和格式化数据的事情，其他事务一概不处理
 */
export default {
    // 解析微信公众号JSSDK权限验证配置接口
    handlerJSsdkConfig(param) {
        const url = env.ctx + 'wechat/api/v1/handler/config'
        return request.get(url, param)
    },
    // 微信小程序登录授权并获取手机号
    decryptPhoneNumber(param, resolve, reject) {
        const url = env.ctx + 'wechat/api/v1/session/decodeinfo'
        return request.postForm(url, param)
    },
    getPhone(param) {
        const url = env.ctx + 'manage/api/mini/phone'
        return request.get(url, param)
    },
    quickLogin(param) {
        const url = env.ctx + 'auth/api/v1/applet/quick/login'
        return request.postForm(url, param)
    },
    /**
     * 登录凭证校验
     * @param param:{
     *   appid:appid,
     *   js_code:js_code
     * }
     * @param resolve
     * @param reject
     */
    jscode2session(param) {
        param.appid = env.appId
        const url = env.ctx + 'wechat/api/v1/session/jscode'
        return request.postForm(url, param)
    },
    // 保存小程序用户关系表数据
    insertProgram(param) {
        param.appId = env.appId
        const url = env.ctx + 'auth/api/v1/sysuserprogram/insert'
        return request.postJson(url, param)
    },
    /**
     * 更新微信用户信息
     * @param param
     * @param resolve
     * @param reject
     */
    updateWechatUser(param) {
        const url = env.ctx + 'wechat/api/v1/openwechatuser/update'
        return request.putJson(url, param)
    },
    /**
     * 检查是否绑定微信公众号
     * @param openId
     * @param resolve
     * @param reject
     */
    checkWechatConcern(openId) {
        const url = env.ctx + 'wechat/api/v1/openwechatuser/query/check/concern'
        return request.get(url, { openId: openId })
    }
}