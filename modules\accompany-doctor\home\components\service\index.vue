<template>
  <!-- <page>
    <view slot="content" class="body-main"> -->
      <view class="service" :class="'service-' + skinColor">
        <view class="my-data" style="background-image: none !important; background-color: #FFFFFF !important;">
          <view :style="'height:' + statusBarHeight + 'px;'"></view>
          <view class="top-nav">
            <view class="top-nav-l" @click.stop="handleBack"><image mode="aspectFit" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-left-arrows.png'" class="header-search-img"/></view>
            <view class="top-nav-c">服务</view>
          </view>
        </view>
        <!-- <view class="head-bg"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-head-bg.png'"></image></view> -->

        <!-- 搜索框 -->
        <view class="search-container">
          <view class="input-view">
            <i class="icon-positioning-search"></i>
            <input confirm-type="search" placeholder="搜索服务名称" placeholder-style="color: #BFBFBF" class="input" type="text" v-model="searchKeyword" @confirm="searchServices">
            <view class="line"></view>
            <view class="click" @tap="searchServices">
              搜索
            </view>
          </view>
        </view>

        <!-- 新版分类+服务布局 -->
        <view class="category-service-container">
          <!-- 左侧分类列表 -->
          <scroll-view scroll-y="true" class="category-scroll">
            <view
              class="category-item"
              :class="{ active: currentCategoryIndex === -1 }"
              @click="selectCategory(null, -1)"
            >
              <text>全部服务</text>
            </view>
            <view
              v-for="(category, idx) in categoryList"
              :key="category.id"
              class="category-item"
              :class="{ active: currentCategoryIndex === idx }"
              @click="selectCategory(category, idx)"
            >
              <text>{{ category.name }}</text>
            </view>
          </scroll-view>

          <!-- 右侧服务列表 -->
          <scroll-view scroll-y="true" class="service-scroll" @scrolltolower="loadMoreServices">
            <!-- 加载中状态 -->
            <view v-if="isCategoryLoading" class="loading-container">
              <view class="loading-circle"></view>
              <view class="loading-text">加载中...</view>
            </view>

            <!-- 服务内容 -->
            <view v-else-if="filteredServiceList.length > 0">
              <!-- 陪诊服务 -->
              <view class="accompany-service">
                <view class="accompany-service-item" v-for="item in filteredServiceList" :key="item.id" @click="isComboCategory ? hanldeClickComboJump(item.id) : hanldeClickServiceJump(item.id)">
                  <view class="project-item-l"><image class="img" :src="file_ctx + item.listImg"></image></view>
                  <view class="project-item-r">
                    <view class="project-item-r-title">{{ isComboCategory ? item.comboName : item.serviceName }}</view>
                    <view class="project-item-r-info">{{ isComboCategory ? item.comboDesc : item.description }}</view>
                    <view class="project-item-r-appointment">
                      <view class="project-item-r-money">¥<span v-if="item.cityPrice">{{ item.cityPrice / 100 }}</span><span v-else>{{ item.price / 100 }}</span></view>
                      <view
                        class="project-item-r-btn"
                        :class="{'combo-btn': isComboCategory}"
                        @click.prevent.stop="isComboCategory ? hanldeClickComboJump(item.id) : $navto.push('serviceReservation',{id:item.id, classifyId: item.classifyId || currentCategoryId, name: item.classifyName || currentCategoryName, serviceName: item.serviceName})"
                      >
                        {{ isComboCategory ? '购买' : '预约' }}
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>

            <!-- 空状态 -->
            <view class="empty" v-else-if="!isCategoryLoading && filteredServiceList.length === 0">
              <view class="empty-img"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-classify-empty.png'"></image></view>
              <view class="text">该分类下暂无服务~</view>
            </view>

            <!-- 加载更多提示 -->
            <view v-if="isLoading && !isCategoryLoading" class="loading-tip">加载中...</view>
            <view v-if="serviceLoadEnd && filteredServiceList.length > 0" class="loading-tip">没有更多了</view>
          </scroll-view>
        </view>
      </view>
    <!-- </view>
  </page> -->
</template>

<script>
  import TabsSticky from '@/components/basics/tabs-sticky-v3'
  import serverOptions from '@/config/env/options'
  export default {
    components:{
      TabsSticky
    },
    props: {
      serviceCurrent:Number,
      skinColor: {
        type: String,
        default: ''
      }
    },
    onShareAppMessage(){
      return {
            title: serverOptions.title,
            path: '/pages/service/index',
          }
    },
    onShareTimeline(){
      return {
        title: serverOptions.title,
        path: '/pages/service/index'
      }
    },
    data(){
      return{
        file_ctx:this.file_ctx,
        statusBarHeight: 0,
        curIndex:0,
        tabs:[{name:'全部'},{name:'陪诊服务'},{name:'服务套餐'}],
        serviceList:[],
        comboList:[],

        // 分类相关数据
        categoryList: [], // 分类列表
        currentCategoryIndex: -1, // 当前选中的分类索引，-1表示全部服务
        currentCategoryId: '', // 当前选中的分类ID
        currentCategoryName: '全部服务', // 当前选中的分类名称
        serviceCurrent: 1, // 服务列表当前页
        serviceLoadEnd: false, // 服务列表是否加载完毕
        isLoading: false, // 是否正在加载更多
        isCategoryLoading: false, // 是否正在切换分类加载
        filteredServiceList: [], // 过滤后的服务列表
        searchKeyword: '', // 搜索关键词
        isComboCategory: false, // 是否是套餐分类
      }
    },
    watch: {
      serviceCurrent:{
        handler(){
          if(this.serviceCurrent){
            this.curIndex = this.serviceCurrent
          }
        },immediate:true
      }
    },
    onLoad(){
    },
    mounted (){
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
      this.getCategoryList() // 获取分类列表
      this.serviceList = [] // 清空服务列表
      this.comboList = [] // 清空套餐列表
      this.serviceCurrent = 1 // 重置页码
      this.serviceLoadEnd = false // 重置加载状态
      this.getServicesByCategory() // 获取服务列表
      this.accompanycomboQueryCityPage() // 获取套餐列表
    },
    methods:{
      hanldeClickServiceJump(id){
        const city = this.$common.getKeyVal('user','cityName',true)
        this.$navto.push('ServiceDetail',{id,city})
      },
      hanldeClickComboJump(id){
        const city = this.$common.getKeyVal('user','cityName',true)
        this.$navto.push('comboDetail',{id,city})
      },
      changeTab(index) {
        console.log(index,'index0---==')
      },
      handleBack(){
        this.$navto.back(1)
      },

      // 搜索服务
      searchServices() {
        this.isCategoryLoading = true
        this.serviceCurrent = 1
        this.serviceLoadEnd = false
        // 不立即清空列表，等新数据加载完成后再替换
        this.getServicesByCategory()
      },

      // 获取分类列表
      async getCategoryList() {
        try {
          const providerId = this.$common.getKeyVal('user','providerId',true)
          const res = await this.$api.accompanyDoctor.accompanyserviceclassifyQueryPage({
            current: 1,
            size: 50, // 获取较多分类
            condition: {
              state: 1,
              providerId: providerId
            },
            // 按orderValue排序
            ascs: 'orderValue'
          });

          if (res.data && res.data.records) {
            this.categoryList = res.data.records;
            // 添加"全部套餐"分类
            this.categoryList.push({
              id: 'all-combo',  // 特殊ID标识套餐分类
              name: '全部套餐'
            });
          }
        } catch (error) {
          console.error('获取分类列表失败:', error);
          uni.showToast({
            title: '获取分类失败',
            icon: 'none'
          });
        }
      },

      // 选择分类
      selectCategory(category, index) {
        // 如果点击当前已选中的分类，不做任何操作
        if (index === this.currentCategoryIndex) return;

        // 设置加载中状态，不立即清空列表
        this.isCategoryLoading = true;

        this.currentCategoryIndex = index;
        this.isComboCategory = false; // 重置套餐分类标志

        if (index === -1) {
          // 全部服务
          this.currentCategoryId = '';
          this.currentCategoryName = '全部服务';
        } else if (category.id === 'all-combo') {
          // 全部套餐分类
          this.currentCategoryId = 'all-combo';
          this.currentCategoryName = '全部套餐';
          this.isComboCategory = true;
        } else {
          this.currentCategoryId = category.id;
          this.currentCategoryName = category.name;
        }

        this.serviceCurrent = 1;
        this.serviceLoadEnd = false;

        // 如果是套餐分类，直接显示所有套餐
        if (this.isComboCategory) {
          setTimeout(() => {
            this.filteredServiceList = this.comboList;
            this.serviceLoadEnd = true;
            this.isCategoryLoading = false;
          }, 300); // 短暂延迟以显示加载状态
        } else {
          // 否则加载该分类下的服务
          this.getServicesByCategory();
        }
      },

      // 根据分类ID获取服务列表
      async getServicesByCategory() {
        if (this.serviceLoadEnd || this.isLoading || (this.isComboCategory && !this.isCategoryLoading)) return;

        this.isLoading = true;

        try {
          const providerId = this.$common.getKeyVal('user','providerId',true)
          const city = this.$common.getKeyVal('user','cityName',true)

          const condition = {
            state: 1,
            city: city,
            providerId: providerId
          };

          // 如果选择了特定分类，添加分类筛选条件
          if (this.currentCategoryId) {
            condition.classifyId = this.currentCategoryId;
          }

          // 如果有搜索关键词，添加到筛选条件
          if (this.searchKeyword) {
            condition.serviceName = this.searchKeyword;
          }

          const res = await this.$api.accompanyDoctor.accompanyserviceQueryCityPage({
            current: this.serviceCurrent,
            size: 10,
            condition: condition,
            // 按orderValue排序
            ascs: 'orderValue'
          });

          if (res.data && res.data.records) {
            // 追加服务列表
            if (this.serviceCurrent === 1) {
              this.filteredServiceList = res.data.records;
            } else {
              this.filteredServiceList = [...this.filteredServiceList, ...res.data.records];
            }

            // 判断是否加载完毕
            if (res.data.records.length < 10) {
              this.serviceLoadEnd = true;
            }
          }
        } catch (error) {
          console.error('获取服务列表失败:', error);
          uni.showToast({
            title: '获取服务失败',
            icon: 'none'
          });
        } finally {
          this.isLoading = false;
          this.isCategoryLoading = false;
        }
      },

      // 加载更多服务
      loadMoreServices() {
        if (!this.serviceLoadEnd && !this.isLoading) {
          this.serviceCurrent++;
          this.getServicesByCategory();
        }
      },

      // 服务列表（原方法保留）
      accompanyserviceQueryCityPage(){
        const providerId = this.$common.getKeyVal('user','providerId',true)
        const city = this.$common.getKeyVal('user','cityName',true)
        this.$api.accompanyDoctor.accompanyserviceQueryCityPage({current:1,size:150,condition:{providerId,state:1,city}}).then(res=>{
          this.serviceList = res.data.records
        })
      },

      // 套餐列表（原方法保留）
      accompanycomboQueryCityPage(){
        const providerId = this.$common.getKeyVal('user','providerId',true)
        const city = this.$common.getKeyVal('user','cityName',true)
        this.$api.accompanyDoctor.accompanycomboQueryCityPage({current:1,size:150,condition:{providerId,state:1,city}}).then(res=>{
          this.comboList = res.data.records
        })
      },
    },
 }
</script>

<style lang='scss' scoped>
@import '../style/blueSkin.scss';
// .body-main{
//   background: #F4F6FA;
//   position: relative;
//   height: 100%;
// }
.service{
  width: 100vw;
  height: 100vh;
  background: #FFFFFF;
  // padding: 20rpx 0;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
}
.img{
  width: 100%;
  height: 100%;
}
.my-data{
  height: 188rpx;
  width: 100%;
  .top-nav{
    // position: fixed;
    width: calc(100% - 16rpx);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    line-height: 40px;
    padding: 0 16rpx;
    // z-index: 999;
    // padding: 0 32rpx 0 24rpx;
    .top-nav-l{
      display: flex;
      width: 48rpx;
      height: 48rpx;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .top-nav-c{
      flex: 1;
      text-align: center;
      height: 44rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #2D2F38;
      line-height: 44rpx;
      margin-right: 48rpx;
    }
  }
}
.head-bg{
  height: 122rpx;
  width: 100%;
  background-color: #fff !important;
}

// 搜索框样式
.search-container {
  position: absolute;
  top: 210rpx;
  width: calc(100% - 64rpx);
  margin: 0 32rpx;
  z-index: 10;

  .input-view {
    display: flex;
    align-items: center;
    width: 100%;
    height: 72rpx;
    background: #FFFFFF;
    border-radius: 36rpx;
    border: 2rpx solid #D9DBE0;
    padding: 0 20rpx;
    box-sizing: border-box;

    .search-icon {
      display: inline-block;
      vertical-align: middle;
      margin-right: 12rpx;
      width: 32rpx;
      font-family: "iconfont";
      font-size: 32rpx;
      color: #999;
    }

    .input {
      flex: 1;
      height: 72rpx;
      line-height: 72rpx;
      font-size: 28rpx;
      color: #333;
    }

    .line {
      width: 2rpx;
      height: 32rpx;
      background: #DBDDE0;
      margin: 0 12rpx;
    }

    .click {
      width: 80rpx;
      text-align: center;
      font-size: 28rpx;
      color: #333;
    }
  }
}

// 分类服务容器样式
.category-service-container {
  position: absolute;
  top: 300rpx;
  width: 100%;
  height: calc(100vh - 300rpx - 32rpx);
  display: flex;
  background: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;

  // 左侧分类列表
  .category-scroll {
    width: 220rpx;
    height: 100%;
    background: #F4F6FA;
    .category-item {
      height: 90rpx;
      line-height: 90rpx;
      font-size: 28rpx;
      color: #666;
      text-align: left;
      padding-left: 36rpx;
      border-bottom: 1rpx solid #EAEAEA;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &.active {
        background: #FFFFFF;
        color: #00B484;
        font-weight: 500;
        position: relative;
        padding-left: 36rpx;
        &:before {
          content: "";
          position: absolute;
          left: 16rpx;
          top: 30rpx;
          height: 36rpx;
          width: 12rpx;
          background: #00B484;
          border-radius: 16rpx;
        }
      }
    }
  }

  // 右侧服务列表
  .service-scroll {
    flex: 1;
    height: 100%;
    background: #FFFFFF;

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200rpx;
      padding-top: 100rpx;

      .loading-circle {
        width: 60rpx;
        height: 60rpx;
        border: 4rpx solid #f3f3f3;
        border-top: 4rpx solid #00B484;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      .loading-text {
        margin-top: 20rpx;
        font-size: 26rpx;
        color: #999;
      }
    }

    .accompany-service {
      padding: 0 24rpx;

      .accompany-service-item {
        display: flex;
        padding: 24rpx 0;
        border-bottom: 1rpx solid #E6E6E6;

        .project-item-l {
          width: 144rpx;
          height: 144rpx;
          border-radius: 12rpx;
          margin-right: 20rpx;
          overflow: hidden;
        }

        .project-item-r {
          display: flex;
          flex: 1;
          flex-direction: column;

          .project-item-r-title {
            font-size: 30rpx;
            color: #1D2029;
            line-height: 42rpx;
            width: 280rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .project-item-r-info {
            display: -webkit-box;
            width: 280rpx;
            font-size: 22rpx;
            color: #868C9C;
            line-height: 32rpx;
            margin: 4rpx 0 10rpx;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .project-item-r-appointment {
            display: flex;
            justify-content: space-between;

            .project-item-r-money {
              color: #FF5500;
              span {
                font-size: 36rpx;
                line-height: 50rpx;
              }
            }

            .project-item-r-btn {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 110rpx;
              height: 56rpx;
              background: #fff;
              border-radius: 28rpx;
              border: 1rpx solid #00B484;
              font-size: 26rpx;
              color: #00B484;
              line-height: 36rpx;
              margin-right: 16rpx;

              &.combo-btn {
                background: #00B484;
                color: #FFFFFF;
              }
            }
          }
        }

        &:last-child {
          border-bottom: none;
        }
      }
    }

    // 加载提示和空状态
    .loading-tip {
      text-align: center;
      color: #999;
      font-size: 24rpx;
      padding: 20rpx 0;
    }

    .empty {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 60vh;

      .empty-img {
        display: flex;
        width: 256rpx;
        height: 256rpx;
      }

      .text {
        margin-top: 24rpx;
        font-size: 24rpx;
        color: #4E5569;
        line-height: 34rpx;
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.icon-positioning-search{
    display: inline-block;
    vertical-align: middle;
    margin-right: 6rpx;
    @include iconImg(32, 32, '/system/icon-positioning-search.png');
}
// 原有样式保留但不使用
.accompany-content{
  position: absolute;
  width: calc(100% - 64rpx);
  top: 275rpx;
  border-radius: 16rpx;
  margin: 32rpx 32rpx 0 32rpx;
  .accompany-service,.accompany-combo{
    padding:24rpx;
    background-color: #fff;
    border-radius: 16rpx;
    .title{
      font-size: 32rpx;
      color: #1D2029;
      line-height: 44rpx;
    }
    .accompany-service-item{
      display: flex;
      padding: 24rpx 0;
      border-bottom: 1rpx solid #EAEBF0;
      .project-item-l{
        width: 144rpx;
        height: 144rpx;
        border-radius: 12rpx;
        // border: 1rpx solid #D9DBE0;
        // background-color: skyblue;
        margin-right: 20rpx;
        overflow: hidden;
      }
      .project-item-r{
        display: flex;
        flex: 1;
        flex-direction: column;
        .project-item-r-title{
          font-size: 30rpx;
          color: #1D2029;
          line-height: 42rpx;
        }
        .project-item-r-info{
          display: -webkit-box;
          width: 332rpx;
          font-size: 22rpx;
          color: #868C9C;
          line-height: 32rpx;
          margin: 4rpx 0 10rpx;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .project-item-r-appointment{
          display: flex;
          justify-content: space-between;
          .project-item-r-money{
            color: #FF5500;
            span{
              font-size: 36rpx;
              line-height: 50rpx;
            }
          }
          .project-item-r-btn,.accompany-combo-btn{
            display: flex;
            justify-content: center;
            align-items: center;
            width: 110rpx;
            height: 56rpx;
            background: #fff;
            border-radius: 28rpx;
            border: 1rpx solid #00B484;
            font-size: 26rpx;
            color: #00B484;
            line-height: 36rpx;
          }
          .accompany-combo-btn{
            background: #00B484;
            color: #FFFFFF;
          }
        }
      }
      &:last-child{
        border-bottom: none;
      }
    }
  }
  .empty{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 60vh;
    .empty-img{
      display: flex;
      width: 286rpx;
      height: 212rpx;
    }
    .text{
      margin-top: 24rpx;
      font-size: 24rpx;
      color: #4E5569;
      line-height: 34rpx;
    }
  }
  .accompany-combo{
    margin: 20rpx 0 100rpx;
  }
}

// 皮肤颜色设置
.service-blueSkin {
  .category-service-container {
    .category-scroll {
      .category-item {
        &.active {
          color: #2B76EF;
          &:before {
            background: #2B76EF;
          }
        }
      }
    }
  }
}


</style>
