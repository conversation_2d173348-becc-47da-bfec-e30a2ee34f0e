<template>
  <page>
    <view slot="content" class="body-main">
      <view class="detail-content" id="answer-canvas" v-if="detailObj.id">
        <view class="my-share draw_canvas">
          <!-- #ifdef MP-WEIXIN -->
          <view class="detail-head-bg">
            <image
              :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-everyday-header-poster-bg.png'">
            </image>
          </view>
          <!-- #endif -->
          <view class="detail-head draw_canvas">
            <view class="detail-head-l draw_canvas">
              <image
                class="draw_canvas"
                data-type="image"
                data-delay="1"
                :data-url="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-everyday-poster-text.png'"
                :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-everyday-poster-text.png'">
              </image>
            </view>
            <view class="detail-head-r draw_canvas" data-delay="1" data-type="text" data-text="真相在此·远离谣言">真相在此·远离谣言</view>
          </view>
          <view class="detail-border draw_canvas">
            <image
              class="draw_canvas"
              data-type="image"
              data-delay="1"
              :data-url="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-rumour-center-border.png'"
              :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-rumour-center-border.png'">
            </image>
          </view>
          <view class="title draw_canvas" data-delay="1" data-type="text" :data-text="detailObj.title">{{detailObj.title}}</view>
          <view class="detail-info draw_canvas" data-type="text" data-background="rgba(244, 246, 250, 1)">
            <view class="answer draw_canvas" data-delay="1" data-type="text" :data-text="detailObj.answerText">{{detailObj.answerText}}</view>
            <view class="text draw_canvas" data-delay="1" data-type="text" :data-text="detailObj.answerContent">{{detailObj.answerContent}}</view>
            <view class="date-time draw_canvas" data-delay="1" data-type="text" :data-text="detailObj.answerDateText">{{ detailObj.answerDateText }}</view>
          </view>
          <view class="detail-info-bottom draw_canvas">
            <view class="info-bottom-l draw_canvas">
              <image
                class="draw_canvas"
                data-type="image"
                data-delay="1"
                :data-url="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-everyday-rumour-share-code.jpg'"
                :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-everyday-rumour-share-code.jpg'">
              </image>
            </view>
            <view class="info-bottom-r draw_canvas">
              <view class="info-bottom-r-title draw_canvas" data-delay="1" data-type="text" data-text="小葫芦">小葫芦</view>
              <view class="info-bottom-r-info draw_canvas" data-delay="1" data-type="text" data-text="识别小程序，参与每日辟谣">识别小程序，参与每日辟谣</view>
            </view>
          </view>
        </view>
      </view>
      <view class="detail-bottom" @click="handleSaveShare">
        <view class="detail-bottom-btn"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-everyday-poster-save.png'"></image>保存到相册</view>
      </view>

      <!-- 绘制分享码canvas -->
      <canvas canvas-id="answerCanvas" class="answerCanvas" :style="{'position':'absolute','top':'-99999px','width':`${canvasWidth}`+'px','height':`${canvasHeight}`+'px'}"></canvas>
    </view>
  </page>
</template>

<script>
  import env from '@/config/env/index.js'
  import Wxml2Canvas from 'wxml2canvas';
  import UniIcons from '@/components/uni/uni-icons/uni-icons'
  import { mapState } from 'vuex'
  export default {
    components: {
      UniIcons,
    },
    data(){
      return{
        file_ctx: this.file_ctx,
        env,
        $static_ctx: this.$static_ctx,
        statusBarHeight: 0,
        canvasWidth:null,
        canvasHeight:null,
        detailId:null,
        detailObj:null,
        sharePic:'',
      }
    },
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId,
        fansRecord: state => state.fansRecord,
        curSelectUserInfo: state => state.curSelectUserInfo
      }),
    },
    onLoad(options){
      const query = this.$validate.isNull(this.$Route.query) ? options : this.$Route.query
      if(query?.id){
        this.refuterumortypeQueryOne(query?.id)
      }
    },
    mounted(){
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
    },
    methods:{
      handleSaveShare(){
        // this.openPicture(url)
        this.handleGetSharePic()
      },
      // 绘制图片的方法
      handleGetSharePic(){
        // #ifdef MP-WEIXIN
        uni.createSelectorQuery().select('#answer-canvas').boundingClientRect().exec((res)=>{
          let { width,height } = res[0]
          this.canvasWidth = width
          this.canvasHeight = height
          this.draw(width,height)
        })
        // #endif
      },
      handleBack(){
        this.$navto.back(1)
      },
      // 打开图片授权
      openPicture(url){
        let that = this
        // 首先检查权限
        uni.getSetting({
          success(res) {
            if (!res.authSetting['scope.writePhotosAlbum']) {
              // 请求授权
              uni.authorize({
                scope: 'scope.writePhotosAlbum',
                success() {
                  that.savePicture(url)
                },
                fail() {
                  // 用户拒绝授权，引导用户去设置页手动打开权限
                  uni.showModal({
                    title: '授权提示',
                    content: '需要获取添加图片权限，请到小程序设置页面打开授权',
                    success(modalRes) {
                      if (modalRes.confirm) {
                        uni.openSetting();
                      }
                    }
                  });
                }
              });
            } else {
              that.savePicture(url)
            }
          }
        });
      },

      // 保存图片
      savePicture(url){
        // 用户已授权，可以保存图片
        uni.saveImageToPhotosAlbum({
          filePath: url,
          success: (res) => {
            uni.showToast({
              title: '保存成功',
              icon: 'success'
            })
          },
        })
      },
      draw(width,height){
        let that = this
        uni.showLoading({
          title:"加载中...",
          mask:true
        })
        let wxcanvas = new Wxml2Canvas({
          element: 'answerCanvas', // canvas节点的id,
          obj: that,
          width: width, // 宽 自定义
          height: height, // 高 自定义
          progress(percent) {},
          finish(url) {
            that.sharePic = url
            that.openPicture(url)
            uni.hideLoading()
          },
          error(res) {
            console.log(res);
            that.isShow = false
            uni.hideLoading()
            // 画失败的原因
          }
        })
        let data = {
          //直接获取wxml数据
          list: [{
              type: 'wxml',
              class: '.draw_canvas',  // answer_canvas这边为要绘制的wxml元素跟元素类名， answer_draw_canvas要绘制的元素的类名（所有要绘制的元素都要添加该类名）
              limit: '.detail-content', // 这边为要绘制的wxml元素跟元素类名,最外面的元素
              x: 0,
              y: 0
            } ]
        }
          //传入数据，画制canvas图片
        wxcanvas.draw(data);
      },

      refuterumortypeQueryOne(id){
        uni.showLoading({
          title:"加载中...",
          mask:true
        })
        let that = this
        that.$api.drugBook.refuterumortypeQueryOne({id}).then(res => {
          if(res.data !== ""){
            that.detailObj = {...res.data,answerText:res.data.answer == 0 ? '假的' : '真的',answerDateText:res.data.answerDate?that.$common.formatDate(new Date(res.data.answerDate),'yyyy-MM-dd').replace(/-/g, '/'):'',}
            uni.hideLoading()
          }
        }).then(()=>{
          uni.hideLoading()
        }).catch(err => {
          uni.hideLoading()
        })
      },
    },
 }
</script>

<style lang='scss' scoped>
  .body-main{
    position: relative;
    height: 100vh;
    overflow-y: auto;
    background: #BFEADE;
  }
  .detail-content{
    position: relative;
    margin: 50rpx 32rpx 0;
    border-radius: 32rpx;
    background-color: #fff;
    .my-share{
      position: relative;
      padding: 88rpx 32rpx 56rpx;
      .detail-head-bg{
        position: absolute;
        top: -27rpx;
        left: 43rpx;
        display: flex;
        width: 598rpx;
        height: 68rpx;
        image{
          width: 100%;
          height: 100%;
        }
      }
      .detail-head{
        display: flex;
        align-items: center;
        justify-content: space-between;
        .detail-head-l{
          display: flex;
          width: 158rpx;
          height: 38rpx;
          image{
            width: 100%;
            height: 100%;
          }
        }
        .detail-head-r{
          font-size: 28rpx;
          color: #4E5569;
        }
      }
      .detail-border{
        display: flex;
        margin-top: 34rpx;
        width: 100%;
        height: 24rpx;
        image{
          width: 100%;
          height: 100%;
        }
      }
      .title{
        font-weight: 600;
        font-size: 56rpx;
        color: #000000;
        line-height: 80rpx;
        margin-top: 56rpx;
      }
      .detail-info{
        width: calc(622rpx - 48rpx);
        background: #F4F6FA;
        border-radius: 16rpx;
        padding: 40rpx 24rpx;
        margin-top: 40rpx;
        .answer{
          font-weight: 600;
          font-size: 40rpx;
          color: #00B484;
          line-height: 56rpx;
        }
        .text{
          font-size: 28rpx;
          color: #4E5569;
          line-height: 48rpx;
          word-wrap: break-word;
          margin: 18rpx 0 16rpx;
        }
        .date-time{
          font-size: 26rpx;
          color: #868C9C;
          line-height: 36rpx;
        }
      }
      .detail-info-bottom{
        display: flex;
        align-items: center;
        margin-top: 24rpx;
        .info-bottom-l{
          display: flex;
          width: 144rpx;
          height: 144rpx;
          image{
            width: 100%;
            height: 100%;
          }
        }
        .info-bottom-r{
          margin-left: 16rpx;
          .info-bottom-r-title{
            font-weight: 600;
            font-size: 30rpx;
            color: #1D2029;
            line-height: 42rpx;
          }
          .info-bottom-r-info{
            margin-top: 8rpx;
            font-size: 24rpx;
            color: #4E5569;
          }
        }
      }
    }
  }
  .detail-bottom{
    display: flex;
    justify-content: space-around;
    position: fixed;
    width: 100%;
    bottom: 0;
    left: 0;
    height: 160rpx;
    background-color: #fff;
    .detail-bottom-btn{
      display: flex;
      align-items: center;
      justify-content: center;
      width: 686rpx;
      height: 88rpx;
      margin-top: 20rpx;
      background: #00B484;
      border-radius: 44rpx;
      font-size: 32rpx;
      color: #FFFFFF;
      image{
        display: flex;
        width: 36rpx;
        height: 36rpx;
        margin-right: 8rpx;
      }
    }
  }
</style>
