<template>
  <page>
    <view slot="content" class="main-body">
      <view class="tab">
        <view class="li" v-for="(item,index) in titleList" :key="index" @tap="tab(index)">
          <em class="icon-yijianfankui-d" :class="{'icon-yijianfankui-d-ok':tabEq==index}"></em>
          <text>{{item.value}}</text></view>
      </view>
      <view class="feedback">
        <view class="uni-textarea">
          <textarea placeholder="请输入您宝贵的意见（限制500字符）" maxlength="500" v-model="regForm.content"/>
        </view>
      </view>
      <view class="upload-image">
        <text class="title">
          上传图片
        </text>
        <title-img :cData="regForm.attachmentList" :config="{multiSelectCount: 50}" @returnFn="imgReturnFn"></title-img>
      </view>
      <view class="btn-bg m-t-80" @tap="onSubmit">
        提交
      </view>
    </view>
  </page>
</template>

<script>
import TitleImg from '@/components/business/module/title-img/index'

export default {
  components: {
    TitleImg
  },
  data() {
    return {
      regForm: {
        attachmentList: []
      },

      tabEq: 0,
      titleList: [{ key: '1564', value: '系统问题' }, { key: '1565', value: '提交反馈' }]
    }
  },
  onLoad(){
    // #ifdef MP-WEIXIN
    this.handleClickTrack('OperationDetailsPageView')
    // #endif
  },
  onUnload(){
    // #ifdef MP-WEIXIN
    this.handleClickTrack('EndOperationDetailsPageView')
    // #endif
  },
  methods: {
    // #ifdef MP-WEIXIN
    handleClickTrack(type){
      let pages = getCurrentPages()
      let current = pages[pages.length - 1]; // 获取到当前页面的信息
      let pageInfo = __wxConfig.page && __wxConfig.page__wxConfig.page[`${current.route}.html`] || ''; // 内部属性
      getApp().globalData.sensors.track(type,
        {
          'page_name' : pageInfo?.window?.navigationBarTitleText || '',
          'first_operation_name' : '更多功能',
          'second_operation_name' : '意见反馈',
        }
      ) 
    },
    // #endif
    /**
     *
     * @param type
     * tab 切换作用
     */
    tab(type) {
      this.tabEq = type
    },
    /**
     * 上传图片
     * @param v
     */
    imgReturnFn(v) {
      this.regForm.attachmentList = v
    },
    /**
     * 提交
     */
    onSubmit() {
      this.$uniPlugin.toast('敬请期待！')
      return
      const that = this
      if (!this.regForm.content) {
        this.$uniPlugin.toast('请输入您宝贵的意见！')
        return
      } else {
        that.regForm.type = this.titleList[this.tabEq].key
        const param = {
          type: that.regForm.type,
          content: that.regForm.content
        }
        if (that.regForm.attachmentList && that.regForm.attachmentList.length > 0) {
          param.attachmentList = []
          if (that.regForm.attachmentList && that.regForm.attachmentList.length > 0) {
            for (const i in that.regForm.attachmentList) {
              param.attachmentList.push({
                extName: that.regForm.attachmentList[i].extName,
                fileName: that.regForm.attachmentList[i].fileName,
                filePath: that.regForm.attachmentList[i].filePath
              })
            }
          }
        }
        that.$uniPlugin.loading('提交意见反馈中', true)
        // that.$api.sys.feedbackInsert(param).then(changPwdDate => {
        //   that.$uniPlugin.hideLoading()
        //   setTimeout(() => {
        //     that.$uniPlugin.toast('提交成功')
        //     that.tabEq = 0
        //     that.regForm = {
        //       content: '',
        //       attachmentList: []
        //     }
        //   }, that.$constant.noun.delayedOperationTime)
        // })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.upload-image{
  .title{
    height: 80upx;
    line-height: 80upx;
    margin-left: 32upx;
  }
}
.m-t-80{
  margin-top: 80upx;
}
.main-body{
  height: 100%;
}
.tab{
  width:100%;
  height: 100upx;
  overflow: hidden;
  .li{
    width: 50%;
    float: left;
    text-align: center;
    line-height: 100upx;
    em{
      width: 48upx;
      height: 48upx;
      margin-right: 14upx;
      display: inline-block;
      vertical-align: middle;
    }
    text{
      display: inline-block;
      vertical-align: middle;
      font-size: 30upx;
      line-height: 44upx;
      color: rgba(51,51,51,1);
    }
  }
}
.feedback{
  view{
    textarea{
      width:100%;
      height:450upx;
      background:rgba(255,255,255,1);
      padding: 30upx;
      box-sizing: border-box;
    }
  }
}

.btn-submit{
  width:100%;
  height:88upx;
  margin-top:60upx;
  .btn{
    width:690upx;
    height:88upx;
    line-height: 88upx;
    background: linear-gradient(90deg,rgba(255,185,36,1),rgba(254,219,55,1));
    opacity:0.5;
    border-radius:44px;
    color:#fff;
    text-align: center;
    margin:0 auto;
  }
}
</style>
