.main {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}
.main-content {
  width: 100%;
  box-sizing: border-box;
  padding: 32rpx;
}
.main-content-box {
  background-color: #fff;
  border-radius: 16rpx;
}
.item {
  width: 100%;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #EAEBF0;
  padding: 32rpx 24rpx;
  box-sizing: border-box;
  .avatar-box {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 88rpx;
    height: 88rpx;
    background: #EBEEF5;
    border-radius: 16rpx;
    margin-right: 20rpx;
  }
  .avatar {
    width: 50rpx;
    height: 50rpx;
  }
  .user-box-content {
    height: 88rpx;
    flex: 1;
    display: flex;
    align-items: center;
  }
  .user-name {
    font-size: 28rpx;
    color: #1D2029;
    line-height: 40rpx;
  }
  .btn {
    padding: 0;
    width: 152rpx;
    height: 64rpx;
    background: #fff;
    border-radius: 32rpx;
    border: 1rpx solid #D9DBE0;
    font-size: 26rpx;
    color: #1D2029;
    line-height: 64rpx;
    &.active {
      border: 1rpx solid $topicC;
      background: $topicC;
      color: #fff;
    }
  }
}
.tips-text {
  display: inline-block;
  width: 100%;
  text-align: center;
  font-size: 26rpx;
  line-height: 36rpx;
  color: $topicC;
  padding: 20rpx 0;
}

.empty-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 556rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
}

.empty-img {
  width: 232rpx;
  height: 190rpx;
  margin-bottom: 22rpx;
}

.empty-text {
  font-size: 24rpx;
  color: #4E5569;
  line-height: 34rpx;
}

/deep/ .zp-empty-view-center {
  display: block !important;
}

/deep/ .zp-paging-container-content {
  min-height: 0 !important;
}