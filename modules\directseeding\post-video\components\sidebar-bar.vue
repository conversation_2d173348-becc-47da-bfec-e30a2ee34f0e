<template>
  <view class="sidebar-wrapper">
    <!-- #ifndef H5 -->
    <view class="item" v-if="postData.processStatus !== 1">
      <button class="icon-box wechat-box" open-type="share" @click="sharePosts">
        <uni-icons type="weixin" size="32" color="#fff"></uni-icons>
      </button>
      <text class="num-text">{{ $common.bigNumberTransform(postData.showShareNumber) }}</text>
    </view>
    <!-- #endif -->
    <view class="item">
      <view class="icon-box" @tap="like">
        <uni-icons type="heart-filled" size="36"
          :color="postData.likeSubscribeStatus === 1 ? '#f75578' : '#fff'"></uni-icons>
      </view>
      <text class="num-text">{{ $common.bigNumberTransform(postData.showLikeNumber) }}</text>
    </view>
    <view class="item">
      <view class="icon-box" @tap="openComment">
        <uni-icons type="chat-filled" size="36" color="#fff"></uni-icons>
      </view>
      <text class="num-text">{{ $common.bigNumberTransform(postData.showCommentNumber) }}</text>
    </view>
    <view class="item">
      <view class="icon-box" @tap="collect">
        <uni-icons type="star-filled" size="36"
          :color="postData.collectSubscribeStatus === 1 ? '#fcbd54' : '#fff'"></uni-icons>
      </view>
      <text class="num-text">{{ $common.bigNumberTransform(postData.showCollectNumber) }}</text>
    </view>
    <view style="height: 70px;" :id="'sa-hover-menu-wrap' + timestamp">
      <sa-hover-menu :is-move="false" :default-top="saHoverMenuTop" v-if="saHoverMenuTop&&isShowBtn"></sa-hover-menu>
    </view>
    <uni-popup type="bottom" mask-background-color="rgba(0,0,0,0)" ref="commentPopupRef" id="commentPopupRef" @touchmove.stop.prevent="movehandle" @change="popupChange">
      <view class="comment-box">
        <comment :isShowBtn="isShowBtn" :visible="commentPopupShow" :more-loading="moreLoading" delete-tip="确认删除？" :cm-data="commentData" ref="comment"
          @del="commentDel" @like="commentLike" @focusOn="commentFocusOn" @loadMore="commentLoadMore" @comment="addComment"
          @replyBefore="commentReplyBefore"
          @changeVal="
            (e) => {
              target = e;
            }
          "
        />
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import {
    mapState
  } from 'vuex'
  import uniIcons from '@/components/uni/uni-icons/uni-icons.vue'
  import saHoverMenu from '@/components/basics/sa-hover-menu/sa-hover-menu.vue'
  import uniPopup from '@/components/uni/uni-popup/index.vue'
  import comment from './comment.vue'
  export default {
    components: {
      uniIcons,
      saHoverMenu,
      uniPopup,
      comment
    },
    props: {
      data: {
        type: Object,
        default: function() {
          return {};
        }
      },
      isShowBtn:{
        type:Boolean,
        default:true
      }
    },
    data() {
      return {
        // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
        $constant: this.$constant,
        $common: this.$common,
        $accurateConversion: this.$accurateConversion,
        file_ctx: this.file_ctx,
        $static_ctx: this.$static_ctx,
        $timePlugin: this.$timePlugin,
        $validate: this.$validate,
        $appId: this.$appId,
        saHoverMenuTop: '',
        timestamp: Date.now(),
        windowHeight: uni.getSystemInfoSync().windowHeight, // 视图高度
        postData: {},
        commentList: [],
        pageSize: 20,
        isHasMore: false,
        initCommentLoading: false,
        moreLoading: false,
        pageStartDate: Date.now(),
        commentPopupShow: false
      }
    },
    computed: {
      ...mapState('user', {
        curSelectUserInfo: state => state.curSelectUserInfo,
        accountId: state => state.accountId,
        fansRecord: state => state.fansRecord
      }),
      commentData() {
        return {
          postData: this.postData,
          readNumer: this.postData.showReadNumber,
          commentSize: this.postData.showCommentNumber,
          comment: this.commentList,
          isHasMore: this.isHasMore,
          loading: this.initCommentLoading
        }
      },
    },
    watch: {
      data: {
        handler(val) {
          this.postData = JSON.parse(JSON.stringify(this.data))
        },
        immediate: true,
        deep: true
      }
    },
    mounted() {
      this.getEl(`sa-hover-menu-wrap${this.timestamp}`).then(res => {
        this.saHoverMenuTop = (res.top % this.windowHeight) + 10
      })
    },
    methods: {
      // #ifdef MP-WEIXIN
      handleClickTrack(type){
        getApp().globalData.sensors.track("PopupClick",
          {
            'page_name' : '短视频',
            'popup_id' : 'commentPopupRef',
            'popup_name' :'是否确认删除',
            'click_type' : type == 1 ? '进入弹窗' : '取消弹窗',
          }
        ) 
      },
      // #endif
      popupChange(e) {
        this.commentPopupShow = e.show
      },
      movehandle() {},
      openComment() {
        this.$refs.commentPopupRef.open()
        // #ifdef MP-WEIXIN
        this.handleClickTrack(1)
        // #endif

        this.initCommentLoading = true
        if (!this.$validate.isNull(this.commentList)) return
        this.getCommentQueryLevelPage({}).then(() => {
          this.initCommentLoading = false
        }).catch(() => {
          this.initCommentLoading = false
        })
      },
      // 收藏帖子
      collect() {
        function collect() {
          if (this.postData.collectSubscribeStatus != 1) {
            this.postData.collectSubscribeStatus = 1
            this.postData.showCollectNumber += 1
            this.$api.postmessage.postmessageAddCollection({
              id: this.postData.id,
              accountId: this.accountId
            }).catch(() => {
              this.postData.collectSubscribeStatus = 2
              this.postData.showCollectNumber -= 1
            })
          } else {
            this.postData.collectSubscribeStatus = 2
            this.postData.showCollectNumber -= 1
            this.$api.postmessage.postmessageCancelCollection({
              id: this.postData.id,
              accountId: this.accountId
            }).catch(() => {
              this.postData.collectSubscribeStatus = 1
              this.postData.showCollectNumber += 1
            })
          }
        }
        this.getUserInfo().then(() => {
          collect.call(this)
        })
      },
      // 点赞帖子
      like() {
        function like() {
          if (this.postData.likeSubscribeStatus != 1) {
            this.postData.likeSubscribeStatus = 1
            this.postData.showLikeNumber += 1
            this.$api.postmessage.postmessageAddLike({
              id: this.postData.id,
              accountId: this.accountId
            }).catch(() => {
              this.postData.likeSubscribeStatus = 2
              this.postData.showLikeNumber -= 1
            })
          } else {
            this.postData.likeSubscribeStatus = 2
            this.postData.showLikeNumber -= 1
            this.$api.postmessage.postmessageCancelLike({
              id: this.postData.id,
              accountId: this.accountId
            }).catch(() => {
              this.postData.likeSubscribeStatus = 1
              this.postData.showLikeNumber += 1
            })
          }
        }
        this.getUserInfo().then(() => {
          like.call(this)
        })
      },
      getUserInfo() {
        return this.$ext.user.authCommunityFansInfo()
      },
      shareRecord() {
        const param = {
          accountId: this.accountId,
          businessType: 5, // 业务类型:1-横幅，2-外部服务应用，3-企业微信群，4-圈子，5-帖子，6-帖子评论
          businessId: this.postData.id,
          source: 1, // 来源：1-真实用户，2-马甲
          type: 13, // 类型：1-访问，2-点击，3-长按，4-关注，5-取关，6-点赞，7-收藏，8-删除评论，9-取消点赞，10-取消收藏，11-阅读 13-分享
        }
        this.$api.community.applicationoperatelogInsert(param)
      },
      // 分享帖子
      sharePosts() {
        this.shareRecord()
        this.postData.showShareNumber += 1
      },
      /**
       * 发送文本评论
       */
      async addComment({
        target,
        content = '',
        imagePath = ''
      }) {
        this.$uniPlugin.loading('发送中...')
        const {
          centerUserId = ''
        } = this.curSelectUserInfo || {}
        let params = {
          source: 1, // 来源：1-真实用户，2-马甲
          userId: centerUserId,
          content: content,
          imagePath: this.$validate.isNull(imagePath) ? '' : imagePath.map(item => item.dir).join(','),
          level: this.$validate.isNull(target) ? 1 : 2, // 级别：1-一级，2-子级
          accountId: this.accountId,
          businessId: this.postData.id,
          type: 2, // 1-PGC；2-UGC
        }

        if (!this.$validate.isNull(target)) {
          params = {
            ...params,
            ownerCommentId: target.ownerCommentId ? target.ownerCommentId : target.id,
            mutualCommentId: target.id,
            mutualAccountId: target.accountId,
            mutualUserId: target.userId,
            appId:'wx436b8e65632f880f'
          }
        }

        // 获取openid
        let openId = await this.$ext.wechat.getOpenId()
        params.openid = openId
        const res = await this.$api.postmessage.commentPostMessageComment(params)

        res.data = {
          ...res.data,
          nickName: this.fansRecord.nickName,
          headPath: this.fansRecord.headPath,
          mutualNickName: this.$validate.isNull(target) ? '' : target.nickName
        }
        this.$uniPlugin.hideLoading()
        this.$refs.comment.clearInput()
        this.$refs.comment.closeInput()
        const cmItem = this.etCommentItem(res.data, true)
        this.postData.commentNumber += 1
        this.pushCommentInList(cmItem)
        await this.$uniPlugin.subscribeMessage(this.$constant.system.commentReplyTmplIds)
        const subscribeMessageRes = await this.requestSubscribeMessage(this.$constant.system.commentReplyTmplIds)
        const logParamList = Object.keys(subscribeMessageRes).filter(key => {
          return this.$constant.system.commentReplyTmplIds.includes(key)
        }).map(key => {
          let businessType = null
          if (this.$constant.system.commentReplyTmplIds.includes(key)) {
            businessType = 4
          }
          return {
            appId: this.$appId,
            templateId: key,
            openId: openId,
            subscribeStatus: subscribeMessageRes[key],
            businessType,
            businessId: this.postData.id,
            accountId: this.accountId,
            userId: centerUserId
          }
        })
        this.$api.common.wxsubscribemessagelogInsertBatch({
          wxSubscribeMessageLogList: logParamList
        })

        // #ifdef MP-WEIXIN
        getApp().globalData.sensors.track("Subscription",
          {
            'content_belong_circle' : '',
            'function_name' : '评论回复通知',
            'subscription_type':'一次性订阅'
          }
        )
        // #endif
        // 消息订阅调起成功，返回值'accept'、'reject'、'ban'分别代表用户对此条订阅是同意、拒绝、后台禁用
      },
      // 消息订阅
      requestSubscribeMessage(tmplIds) {
        return new Promise((resolve, reject) => {
          try {
            this.$uniPlugin.requestSubscribeMessage(tmplIds, (res) => {
              let status = true
              tmplIds.forEach(item => {
                if (res[item.toString()] !== 'accept') status = false
              })
              if (status) {
                this.isShow = false
                this.$uniPlugin.toast('订阅成功')
                resolve(res)
              } else {
                this.isShow = true
                this.$uniPlugin.toast('订阅失败')
                resolve(res)
              }
            })
            this.$uniPlugin.hideLoading()
          } catch (err) {
            reject(err)
          }
        })
      },
      async commentDel(data) {
        const {
          id
        } = data
        await this.$api.postmessage.commentDeleteOne({
          id
        })

        // 手动设置删除状态
        if (data.mutualCommentId) {
          const oneIndex = this.commentList.findIndex(item => item.id === data.mutualCommentId)
          let eItem = this.commentList[oneIndex]
          eItem.children = eItem.children.map(item => {
            if (item.id === data.id) {
              item.status = -1
              return this.etCommentItem(item)
            } else {
              return item
            }
          })
          this.$set(this.commentList, oneIndex, eItem)
          this.$forceUpdate()
        } else {
          const oneIndex = this.commentList.findIndex(item => item.id === data.id)
          let eItem = this.commentList[oneIndex]
          eItem.status = -1
          eItem = this.etCommentItem(eItem)
          this.$set(this.commentList, oneIndex, eItem)
          this.$forceUpdate()
        }
      },
      commentLike(data) {
        this.getUserInfo().then(() => {
          this.likeComment(...arguments)
        })
      },
      getUserInfo() {
        return this.$ext.user.authCommunityFansInfo()
      },
      commentFocusOn() {
        console.log('失去焦点', ...arguments)
      },
      back() {
        this.$navto.back()
      },
      async likeComment(data) {
        const {
          centerUserId = ''
        } = this.curSelectUserInfo || {}
        const params = {
          commentId: data.id,
          source: 1, // 1-真实用户，2-马甲用户,3-系统用户
          accountId: this.accountId,
          userId: centerUserId
        }
        // 是否已点赞
        if (data.likeSubscribeStatus == 1) {
          // 取消点赞
          this.$api.postmessage.commentReduceLikenumber(params)
        } else {
          // 新增点赞
          this.$api.postmessage.commentIncreaseLikenumber(params)
        }

        try {
          // 手动设置点赞或取消点赞
          if (data.mutualCommentId) {
            const oneIndex = this.commentList.findIndex(item => item.id === data.mutualCommentId)
            let eItem = this.commentList[oneIndex]
            eItem.children = eItem.children.map(item => {
              if (item.id === data.id) {
                if (data.likeSubscribeStatus == 1) {
                  item.likeSubscribeStatus = 2
                  item.likeNumber -= 1
                } else {
                  item.likeSubscribeStatus = 1
                  item.likeNumber += 1
                }
                return this.etCommentItem(item)
              } else {
                return item
              }
            })
            this.$set(this.commentList, oneIndex, eItem)
            this.$forceUpdate()
          } else {
            const oneIndex = this.commentList.findIndex(item => item.id === data.id)
            let eItem = this.commentList[oneIndex]
            if (data.likeSubscribeStatus == 1) {
              eItem.likeSubscribeStatus = 2
              eItem.likeNumber -= 1
            } else {
              eItem.likeSubscribeStatus = 1
              eItem.likeNumber += 1
            }
            eItem = this.etCommentItem(eItem)
            this.$set(this.commentList, oneIndex, eItem)
            this.$forceUpdate()
          }
        } catch (err) {
          console.log(err)
        }

      },
      async commentLoadMore(params, e) {
        if (this.$validate.isNull(params)) {
          params = {
            lastMsgId: this.commentList[this.commentList.length - 1].id,
            level: 1,
            ownerCommentId: '',
            pageSize: this.pageSize
          }
          this.moreLoading = true
          await this.getCommentQueryLevelPage(params).catch(err => {
            this.moreLoading = false
          })
          this.moreLoading = false
        } else {
          this.updateComment({
            ...e,
            moreLoading: true
          })
          await this.getCommentQueryLevelPage(params).catch(err => {
            this.updateComment({
              ...this.commentList.find(item => item.id === e.id),
              moreLoading: false
            })
          })
          this.updateComment({
            ...this.commentList.find(item => item.id === e.id),
            moreLoading: false
          })
        }
      },
      // 评论时的点击动作 判断是否需要进行授权
      commentReplyBefore () {
          this.getUserInfo().catch(err => {
              this.$refs.comment.closeInput()
          })
      },
      async getCommentQueryLevelPage({
        lastMsgId = '',
        level = 1,
        ownerCommentId = '',
        pageSize = this.pageSize
      }) {
        const params = {
          accountId: this.accountId,
          businessType: 5, // 评论
          businessId: this.postData.id,
          lastMsgId,
          level,
          ownerCommentId,
          pageSize,
          currentDate: this.pageStartDate
        }
        const res = await this.$api.postmessage.commentQueryLevelPage(params).catch(err => {
          return Promise.reject(err)
        })
        const data = res.data.map(item => {
          return this.etCommentItem(item)
        })
        // 第一层级
        if (level === 1) {
          this.commentList.push(...data)
          if (data.length < pageSize) {
            this.isHasMore = false
          } else {
            this.isHasMore = true
          }
        } else {
          this.commentList.forEach((item, index) => {
            if (item.id === ownerCommentId) {
              this.$set(this.commentList, index, {
                ...item,
                children: [...item.children, ...data],
                isHasMore: res.data.length >= pageSize,
                ownerCommentId
              })
            }
          })
        }
        return Promise.resolve()
      },
      /**
       * @param {object} item 评论实体
       * @param {boolean} isAddCm 是否是手动新增的评论 用于下拉加载更多时过滤手动新增的数据
       * @return {object} item 评论实体
       */
      etCommentItem(item, isAddCm = false) {
        return {
          ...item,
          isHasMore: item.mutualNumber && item.mutualNumber > 0 ? true : false,
          children: this.$validate.isNull(item.children) ? [] : item.children,
          owner: item.accountId === this.accountId, // 是否是拥有者，为true则可以删除，管理员全部为true
          hasLike: item.likeSubscribeStatus == 1, // 是否点赞
          likeNum: item.likeNumber, // 点赞数量
          avatarUrl: item.headPath ? this.file_ctx + item.headPath : this.defaultAvatar, // 评论者头像地址
          nickName: item.nickName, // 评论者昵称，昵称过长请在后端截断
          content: item.content, // 评论内容
          parentId: item.id, // 所属评论的唯一主键
          // createTime: this.$common.formatDate(new Date(item.createTime || new Date().getTime()), 'yyyy-MM-dd HH:mm:ss'), // 创建时间
          hasShowMore: item.mutualNumber && item.mutualNumber > 0 ? true : false,
          isAddCm
        }
      },
      /**
       * 将评论手动添加到列表中对应的位置，用于用户发布评论
       * @param {object} cmItem 评论实体
       */
      pushCommentInList(cmItem) {
        // 不存在楼主评论id则为帖子下的评论
        if (!cmItem.ownerCommentId) {
          this.commentList.unshift(cmItem)
        } else {
          const ownerIndex = this.commentList.findIndex(item => item.id === cmItem.ownerCommentId) // 楼主评论索引
          let ownerItem = this.commentList[ownerIndex]
          if (this.$validate.isNull(ownerItem.children) || cmItem.ownerCommentId === cmItem.mutualCommentId) {
            ownerItem.children.unshift(cmItem)
          } else {
            const mutualIndex = ownerItem.children.findIndex(item => item.id === cmItem.mutualCommentId)
            ownerItem.children.splice(mutualIndex + 1, 0, cmItem)
          }
          this.$set(this.commentList, ownerIndex, ownerItem)
        }

      },
      /**
       * 评论更新
       * @param {object} cmItem 评论实体
       */
      updateComment(cmItem) {
        // 不存在楼主评论id则为帖子下的评论
        if (cmItem.level === 1) {
          const index = this.commentList.findIndex(item => item.id === cmItem.id)
          this.$set(this.commentList, index, cmItem)
        } else if (cmItem.level === 2) {
          const ownerIndex = this.commentList.findIndex(item => item.id === cmItem.ownerCommentId) // 一级评论索引
          let ownerItem = this.commentList[ownerIndex]
          const mutualIndex = ownerItem.children.findIndex(item => item.id === cmItem.mutualCommentId) // 二级评论索引
          ownerItem.children[mutualIndex] = cmItem
          this.$set(this.commentList, ownerIndex, ownerItem)
        }

      },
      // 异步获取元素
      getEl(id) {
        // #ifdef H5
        const el = document.getElementById(id)
        // #endif

        // #ifndef H5
        const query = uni.createSelectorQuery().in(this)
        const el = query.select(`#${id}`)
        // #endif

        return new Promise((resolve, reject) => {
          // #ifdef H5
          resolve(el.getBoundingClientRect())
          // #endif
          // #ifndef H5
          if (el.boundingClientRect) {
            el.boundingClientRect(data => {
              resolve(data)
            }).exec()
          }
          // #endif
        })
      },
    }
  }
</script>

<style lang="scss" scoped>
  .sidebar-wrapper {
    display: flex;
    flex-direction: column;
    position: absolute;
    right: 20rpx;
    bottom: 70rpx;
    z-index: 9999;
  }

  .item {
    display: flex;
    flex-direction: column;
    align-items: center;

    &+.item {
      margin-top: 20rpx;
    }
  }

  .num-text {
    color: #fff;
    font-size: 24rpx;
  }

  .icon-box {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .wechat-box {
    background-color: #48c265;
    @include rounded(50%);
    margin-bottom: 8rpx;
  }

  .share-btn {
    background-color: #48c265;
  }

  .comment-box {
    background-color: #fff;
    height: 65vh;
    width: 100%;
    box-sizing: border-box;
    overflow-x: hidden;
    overflow-y: auto;
    padding-bottom: env(safe-area-inset-bottom);
    border-top-left-radius: 36rpx;
    border-top-right-radius: 36rpx;
  }
</style>
