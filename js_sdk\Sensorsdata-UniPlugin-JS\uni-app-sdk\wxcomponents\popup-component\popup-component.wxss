/* popup-component.wxss */
.wx-popup {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;
}
.close_btn {
  width: 50rpx;
  height: 50rpx;
}
.popup-container {
  position: fixed;
  left: 50%;
  top: 46%;
  transform: translate(-50%, -50%);
}

.popup-content {
  margin-top: 20rpx;
  box-sizing: border-box;
  overflow: hidden;
}

.content-image {
  width: 620rpx;
  height: 440rpx;
}

.content-title {
  display: block;
  margin: 0 auto;
  text-align: center;
  font-size: 48rpx;
  line-height: 150%;
  color: rgba(0, 0, 0, 1);
  word-break: break-all;
  overflow: auto;
}

.content-content {
  display: block;
  width: 580rpx;
  margin: 0 auto;
  padding: 0;
  font-size: 28rpx;
  line-height: 150%;
  text-align: center;
  color: rgba(74, 74, 74, 1);
  word-break: break-all;
  overflow: auto;
}

.popup-btn {
  display: flex;
  width: 580rpx;
  align-items: center;
  margin: 32rpx auto 0;
  flex-direction: column;
  text-overflow: clip;
  white-space: nowrap;
  overflow: hidden;
}

.popup-btn_column {
  display: flex;
  width: 590rpx;
  margin: 26rpx auto 0;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  overflow: hidden;
}

.popup-btn_row {
  display: flex;
  justify-content: center;
  width: 580rpx;
  margin: 26rpx auto 0;
  align-items: center;
  flex-direction: row;
  text-overflow: clip;
  letter-spacing: 2rpx;
  overflow: hidden;
}

.popup-btn-second_column {
  margin-top: 20rpx;
}

.popup-btn-second_row {
  margin-left: 20rpx;
}

.popup-btn-button_row {
  width: 280rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 36rpx;
  border-width: 2rpx;
  color: rgba(255, 255, 255, 1);
  border-radius: 40rpx;
  border-color: rgba(2, 203, 148, 1);
  flex-grow: 1;
  overflow: hidden;
}

.popup-btn-button_column {
  width: 590rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 36rpx;
  border-width: 2rpx;
  color: rgba(255, 255, 255, 1);
  border-radius: 40rpx;
  border-color: rgba(2, 203, 148, 1);
  overflow: hidden;
}

.popup-btn-img_row {
  width: 280rpx;
  height: 80rpx;
}

.popup-btn-img_column {
  width: 580rpx;
  height: 80rpx;
}

.popup-btn-linker_row {
  display: block;
  width: 280rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  color: rgba(74, 144, 226, 1);
  text-decoration: underline;
  letter-spacing: 2rpx;
  overflow: hidden;
}

.popup-btn-linker_column {
  width: 580rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  color: rgba(74, 144, 226, 1);
  text-decoration: underline;
  letter-spacing: 2rpx;
  overflow: hidden;
}

.close_btn-right {
  display: flex;
  justify-content: flex-end;
}
.close_btn-center {
  margin-top: 20rpx;
  display: flex;
  justify-content: center;
}
