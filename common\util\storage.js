import validate from '@/common/util/validate'
import constant from '@/constant'
import env from '@/config/env'
let environmental = ''
// 判断env对象里的ctx属性 是否为https路径
environmental = env.ctx.match(/^https/) ? 'pro-' : 'dev-'  


// 上传文件请求头的terminalType参数在不同环境下的配置
let terminalType = ''
// #ifdef H5
terminalType = environmental + constant.system.terminal.h5
// #endif
// #ifdef APP-PLUS
terminalType = environmental + constant.system.terminal.app
// #endif
// #ifdef MP-WEIXIN
terminalType = environmental + constant.system.terminal.miniProgram
// #endif
// #ifdef MP-ALIPAY
terminalType = environmental + constant.system.terminal.alipay
// #endif

/**
 * 永久存储工具类
 */
export default {
  /**
   * 将 data 存储在本地缓存中指定的 key 中，会覆盖掉原来该 key 对应的内容，这是一个同步接口。
   * @param {String} key
   * @param {String || Object} val
   */
  setStorageSync(key, val) {
    const type = validate.judgeTypeOf(val)
    if (type !== 'String' && type !== 'Undefined' && type !== 'Null') {
      val = JSON.stringify(val)
      uni.setStorageSync(terminalType + ':' + key, val)
    } else {
      try {
        if (val.match(/\{/)) { // 若匹配到{则说明是json字符
          val = JSON.parse(val)
        }
      } catch (e) {
        // dosome...
      } finally {
        if (type === 'String') {
          uni.setStorageSync(terminalType + ':' + key, val)
        }
      }
    }
  },
  /**
   * 从本地缓存中同步获取指定 key 对应的内容。
   * @param {String} key
   */
  getStorageSync(key) {
    let result = ''
    try {
      result = uni.getStorageSync(terminalType + ':' + key)
      const type = validate.judgeTypeOf(result)
      if (type === 'String' && result.match(/\{/)) { // 若匹配到{则说明是json字符
        result = JSON.parse(result)
      }
    } catch (err) {
      uni.removeStorageSync(terminalType + ':' + key)
    }
    return result
  },
  /**
   * 从本地缓存中同步移除指定 key
   * @param {String} key
   */
  removeStorageSync(key) {
    uni.removeStorageSync(terminalType + ':' + key)
  },
  /**
   * 同步清理本地数据缓存。
   */
  clearStorageSync() {
    uni.clearStorageSync()
  },
  /**
   * 将数据存储在本地缓存中指定的 key 中，会覆盖掉原来该 key 对应的内容，这是一个异步接口。
   * @param {String} key
   * @param {String || Object} val
   */
  setStorage(key, val) {
    return new Promise((resolve, reject) => {
      uni.setStorage({
        key: terminalType + ':' + key,
        data: val,
        success() {
          resolve()
        },
        fail() {
          reject()
        }
      })
    })
  }
}
