<template>
  <view class='gambit'>
    <view class="my-data" :style="{'background-image':'url('+ file_ctx +'static/image/business/hulu-v2/gambit-detail-bg.png)','background-size': '100%'}">
      <view :style="'height:' + statusBarHeight + 'px;'"></view>
      <view class="top-nav">
        <view class="top-nav-l" @click.stop="handleBack">
          <image mode="aspectFit" :src="file_ctx + 'static/image/business/hulu-v2/icon-circle-left-arrows.png'" class="header-search-img"/>
        </view>
        <view class="top-nav-c">
          <image class="gambit-search" :src="file_ctx + 'static/image/business/hulu-v2/gambit-detail-search.png'" @click="searcFn"></image>
          <button class="my-button" open-type="share"><image mode="aspectFit" class="gambit-transpond" :src="file_ctx + 'static/image/business/hulu-v2/gambit-detail-transpond.png'"></image></button>
          <!-- <image class="gambit-collect" :src="file_ctx + `static/image/business/hulu-v2/${gambitDetailObj.hasCollected == 1 ? 'gambit-detail-collect-active.png' : 'gambit-detail-collect.png'}`" @click="handleClickCollect"></image> -->
        </view>
      </view>
      <view class="gambit-name-box">
        <view class="title-l">
          <view class="title" v-if="gambitDetailObj.name">{{ gambitDetailObj.name }}</view>
          <view class="browse-discuss">
            <!-- <span>{{ gambitDetailObj.preview || 0 }} 浏览</span> -->
            <span class="discuss">{{ gambitDetailObj.postUseCount || 0 }} 讨论</span></view>
        </view>
        <view class="title-r" @click="handleClickCollect">
          <image class="title-r-img" mode="aspectFit" :src="file_ctx + `static/image/business/hulu-v2/${gambitDetailObj.hasCollected == 1 ? 'icon-gambit-detail-collect-active.png' : 'icon-gambit-detail-collect.png'}`"></image>
          {{ ambitDetailObj.hasCollected == 1 ? '已收藏' : '收藏' }}
        </view>
      </view>
    </view>
    <scroll-refresh
      class="my-scroll-refresh"
      :isShowEmptySwitch="true"
      :fixed="false"
      :isAbsolute="false"
      :up="upOption"
      :down="downOption"
      @returnFn="returnFn"
      @scrollInit="scrollInit"
    >
      <view class="main-list">
        <template>
          <tabs-sticky
            class="main-tabs-sticky"
            :fontBigger="true"
            :bdb="false"
            :fixed="false"
            :overflowX="true"
            v-model="curIndex"
            :tabs="tabs"
            @change="changeTab"
          ></tabs-sticky>
        </template>
        <template v-for="(item, index) in tabs">
          <view class="main-list-content" :key="item.id">
            <!-- 帖子 -->
            <nui-list 
              v-if="
                (curIndex == 0 && index === 0) ||
                (curIndex == 1 && index === 1) ||
                (curIndex == index && item.contentType === 1)
              "
              :indexlist="indexlist"
              :posts-params="postsParams"
              :isShowGambit="true"
              @cateClick="cateClick"
            ></nui-list>
            <!-- 直播 -->
            <video-item
              :list="videolist"
              :is-show-empty="false"
              v-else-if="curIndex == index && item.contentType === 2"
            ></video-item>
          </view>
        </template>
      </view>
    </scroll-refresh>
    <view class="go-publish" @click="handleClickIssue"><image class="go-publish-img" :src="file_ctx + 'static/image/business/hulu-v2/gambit-go-publish.png'"></image>去发布</view>
  </view>
</template>

<script>
  import nuiList from '@/components/community/nui-list/nui-list.vue'
  import videoItem from '../../components/videoItem/index.vue'
  import TabsSticky from '@/components/basics/tabs-sticky-v3'
  export default {
    components:{
      nuiList,
      videoItem,
      TabsSticky,
    },
    data(){
      return{
        file_ctx:this.file_ctx,
        statusBarHeight: 0,
        curIndex:0,
        circleCurIndex: 0,
        tabs: [
          { name: '推荐', id: 1 },
          { name: '精华', id: 3 },
        ],
        indexlist: [],
        mescroll: null, // mescroll实例对象
        downOption: {
          auto: false // 不自动加载
        },
        upOption: {
          auto: false, // 不自动加载
          empty: {
            top: 0,
            zIndex: 999,
          }
        },
        filterCircleTabs:[],
        videolist: [],
        gambitDetailObj:{},
        topicIds:'',
      }
    },
    computed: {
      circleInfo() {
        return this.filterCircleTabs[this.circleCurIndex]
      },
      postsParams() {
        return {
          circleClassifyId: (this.$validate.isNull(this.filterCircleTabs) || !this.filterCircleTabs[this.circleCurIndex]) ? '' : this.filterCircleTabs[this.circleCurIndex].id,
          medicineClassifyId: (this.curIndex == 0 || !this.tabs[+this.curIndex]) ? null : this.tabs[+this.curIndex].id,
          topicIds:this.topicIds,
        }
      }
    },
    watch:{
      curIndex: {
        handler() {
          this.indexlist = []
          this.init()
        }
      },
    },
    onLoad(options){
      const query = this.$validate.isNull(this.$Route.query) ? options : this.$Route.query
      console.log(query,'11112query')
      if(query?.id){
        this.topicIds = query.id
        this.postmessagetopicQueryOne(query.id)
        //统计页面浏览
        this.trackExposure(18)
      }
      this.init()
    },
    onShow(){
      if(this.topicIds){
        this.postmessagetopicQueryOne(this.topicIds)
      }
    },
    // 发送给好友
    onShareAppMessage(res) {
      this.trackExposure(13)
      // 转发统计
      return {
        title: this.gambitDetailObj.name, //分享的名称
        path: `modules/community/posts/gambit/index?id=${this.topicIds}`,
      }
    },
    mounted(){
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
    },
    methods:{
      // 曝光方法
      async trackExposure(type){
        const param = {
          accountId: this.$common.getKeyVal('user', 'accountId'),
          businessType: 12, // 业务类型:话题转发
          businessId: this.topicIds,
          source: 1, // 来源：1-真实用户，2-马甲
          type: type, // 类型：18（预览或者浏览）、13（分享）、7（收藏）
        }
        await this.$api.postmessage.applicationoperateV2logInsert(param)
        if(type === 18){
          this.postmessagetopicQueryOne(this.topicIds)
        }
      },
      // 点击发布
      handleClickIssue(){
        this.$navto.push('postMessageIndex',{gambitName:this.gambitDetailObj.name,gambitId:this.gambitDetailObj.id})
      },
      async postmessagetopicQueryOne(id){
        const res = await this.$api.postmessage.postmessagetopicQueryOne({id})
        this.gambitDetailObj = res.data
      },
      returnFn(obj) {
        if (this.$validate.isNull(this.filterCircleTabs) || this.tabs[+this.curIndex].contentType !== 2) {
          this.postReturnFn(obj)
        } else if (this.tabs[+this.curIndex].contentType === 2) {
          this.videoReturnFn(obj)
        }

      },
      videoReturnFn(obj) {
        const that = this
        function queryPage(pageNum, pageSize, fn) {
          const param = {
            current: pageNum,
            size: pageSize,
            condition: {
              // 活动状态：1-草稿，2-预告，3-直播中，4-直播结束，5-回放，6-下架
              activityStatusList:[2,3,5],
              showType:1,
              businessType: that.tabs[+that.curIndex].liveBusinessType || 7,// 直播活动
              medicineClassifyId: that.curIndex == 0 ? null : that.tabs[+that.curIndex].id,
              orderByActivityStatus:'5,2,3'
            },
            descs:"createTime"
          }

          that.$api.cloudClassroom.getMeetingQueryPage(param).then(res => {
            if (res && res.data.records) {
              let list = res.data.records
              if (list.length > 0) {
                for (const a in list) {
                  const data = list[a]
                  data.startTimeText = that.$common.formatDate(new Date(data.startTime), 'yyyy-MM-dd HH:mm:ss');
                  data.endTimeText = that.$common.formatDate(new Date(data.endTime), 'yyyy-MM-dd HH:mm:ss');
                  data.coverPathsUrl = isDomainUrl(data.coverPaths)
                }
              }
              fn(res.data.records.length > 0 ? res.data.records : [])
            }
          })
        }
        queryPage(obj.pageNum, obj.pageSize, (data) => {
          if (obj.pageNum === 1) {
            that.videolist = []
          }
          that.videolist = that.videolist.concat(data)
          obj.successCallback && obj.successCallback(data)
        })
      },
      postReturnFn(obj) {
        const that = this
        setTimeout(async function () {
          if (that.accountId) {
            let params = {
              current: obj.pageNum,
              size: obj.pageSize,
              condition: {
                circleClassifyId: that.$validate.isNull(that.filterCircleTabs) ? '' : that.filterCircleTabs[that.circleCurIndex].id,
                // accountId: that.accountId,
                medicineClassifyId: that.curIndex == 0 ? null : that.tabs[+that.curIndex].id,
                topicIds:that.topicIds || null,
                ...that.regForm
              }
            }
            let res = null
            if (that.tabs[that.curIndex].id === 2) {
              // 名医热点
              res = await that.$ext.community.postmessageQueryPhysicianHotPage(params)
            } else if (that.tabs[that.curIndex].id === 3) {
              // 精华
              params.condition.isEssence = 1
              res = await that.$api.postmessage.postmessageQueryRecommendTopicPage(params)
            } else {
              // 推荐
              res = await that.$api.postmessage.postmessageQueryRecommendTopicPage(params)
            }
            let data = res.data.records.map(item=>{
              try{
                item.topicIdsArr = item.topicIds && JSON.parse(item.topicIds) || []
              } catch(e){
                console.log('JSON解析错误:',e)
              }
              return ({
                ...item
              })
            }) || []
            if (obj.pageNum === 1) {
              that.indexlist = []
            }
            that.indexlist = [...that.indexlist, ...data]
            obj.successCallback && obj.successCallback(data)
          }

        }, that.$constant.noun.scrollRefreshTime)
      },
      cateClick(data) {
        this.navtoGo('Circle', { cid: data.circleClassifyId })
      },
      init() {
        this.$nextTick(() => {
          this.mescroll.triggerDownScroll()
        })
      },
      scrollInit(scroll) {
        scroll.optUp.page.num = 1
        scroll.optUp.page.size = 10
        this.mescroll = scroll
      },
      changeTab(index) {
        this.curIndex = index
      },
      // 收藏
      async handleClickCollect(){
        if(this.gambitDetailObj.hasCollected == 1){
          await this.applicationoperatelogCancelTopicCollection()
          uni.showToast({title:'已取消收藏',icon:'none'})
        } else {
          await this.trackExposure(7)
          uni.showToast({title:'已收藏',icon:'none'})
        }
        this.postmessagetopicQueryOne(this.gambitDetailObj.id)
      },
      // 取消收藏
      async applicationoperatelogCancelTopicCollection(){
        const res = await this.$api.postmessage.applicationoperatelogCancelTopicCollection({accountId:this.$common.getKeyVal('user', 'accountId'),topicId:this.gambitDetailObj.id})
        return Promise.resolve(res)
      },
      searcFn() {
        this.$navto.push('CommonSystemSearch')
      },
      handleBack(){
        this.$navto.back(1)
      },
    },
 }
</script>

<style lang='scss' scoped>
  .gambit{
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100vh;
  }
  .go-publish{
    display: flex;
    align-items: center;
    position: fixed;
    bottom: 146rpx;
    left: 50%;
    z-index: 999;
    transform: translateX(-50%);
    padding: 22rpx 40rpx 20rpx 42rpx;
    background: #00B484;
    border-radius: 108rpx 108rpx 108rpx 108rpx;
    font-size: 32rpx;
    color: #FFFFFF;
    font-weight: 600;
    .go-publish-img{
      display: flex;
      width: 34rpx;
      height: 34rpx;
      margin-right: 20rpx;
    }
  }
  .my-data{
    position: relative;
    height: 392rpx;
    width: 100%;
  }
  @mixin contentFlex {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .top-nav{
    position: relative;
    @include contentFlex;
    width: calc(100% - 56rpx);
    height: 40px;
    line-height: 40px;
    padding: 0 32rpx 0 24rpx;
    .top-nav-l{
      @include contentFlex;
      position: absolute;
      height: 40px;
      left: 24rpx;
      top: 0;
      .header-search-img{
        display: flex;
        width: 48rpx;
        height: 48rpx;
        margin-right: 24rpx;
      }
    }
    .top-nav-c{
      @include contentFlex;
      position: absolute;
      left: 66%;
      transform: translateX(-60%);
      top: 0;
      height: 40px;
      font-weight: 500;
      font-size: 32rpx;
      color: #1D2029;
      .gambit-search,.gambit-transpond,.gambit-collect{
        display: flex;
        width: 64rpx;
        height: 64rpx;
      }
      .my-button{
        width: 64rpx;
        height: 64rpx;
        margin: 0 20rpx;
        background-color: transparent; /* 背景透明 */
        border: none; /* 无边框 */
        padding: 0; /* 根据需要调整内边距 */
        outline: none; /* 去除点击时的轮廓 */
        box-shadow: none; /* 去除阴影 */
        &::after{
          border: none !important;
        }
      }
    }
  }
  .gambit-name-box{
    position: absolute;
    width: calc(100% - 60rpx);
    bottom: 76rpx;
    // left: 24rpx;
    padding: 0 36rpx 0 24rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title-l{
      .title{
        font-weight: 600;
        font-size: 34rpx;
        color: #333333;
        line-height: 40rpx;   
        margin-bottom: 20rpx;   
      }
      .browse-discuss{
        display: flex;
        line-height: 29rpx;
        span{
          font-size: 24rpx;
          color: #1D2029;
        }
        .discuss{
          margin-left: 12rpx;
        }
      }
    }
    .title-r{
      display: flex;
      align-items: center;
      padding: 10rpx 28rpx;
      border-radius: 16rpx  16rpx  16rpx  16rpx;
      background-color: #fff;
      font-size: 24rpx;
      color: #333333;
      .title-r-img{
        display: flex; 
        width: 36.67rpx;
        height: 36.67rpx;
        margin: -1rpx 8rpx 0 0;
      }
    }
  }
  .my-scroll-refresh{
    flex: 1;
  }
  .main-list {
    position: relative;
    border-radius: 20rpx;
    background-color: #ffffff;
    // /deep/.banner-ads{
    //   .banner-main{
    //     padding: 12rpx 32rpx 0;
    //   }
    // }
    /deep/.main-tabs-sticky{
      .tabs-sticky{
        padding:32rpx 32rpx 0;
        position: sticky;
        top: 0;
        z-index:999;
      }
    }
    /deep/.tabs-sticky-body{
      padding: 0;
    }

  }
  .main-list-content {
    position: relative;
  }
</style>