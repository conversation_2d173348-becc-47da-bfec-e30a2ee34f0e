<template>
  <view class="scratch">
    <!-- 背景图 S -->
    <image class="scratchMask" :src="scratchMask"></image>
    <!-- 背景图 E -->

    <!-- 页面标题 S -->
    <view :style="'height:' + statusBarHeight + 'px;'"></view>
    <view class="accompany-header" :style="'height:' + headerHeight + 'px;'">
      <image mode="aspectFit" :src="whiteLeftArrows" class="header-search-img"/>
      活动专区
    </view>
    <!-- 页面标题 E -->
    <image class="scratchTabIcon" :src="scratchTabIcon"></image>
    <view class="scratchTabBox"></view>

    <!-- 活动规则 S -->
    <cardBox :options="activeRuleOptions">
      <view class="activeRule">
        <view class="activeRuleItem">
          <view class="activeRuleItemTitle">活动时间</view>
          <view class="activeRuleItemContent">xxxx-xx-xx至xxxx-xx-xx</view>
        </view>
        <view class="activeRuleItem">
          <view class="activeRuleItemTitle">活动时间</view>
          <view class="activeRuleItemContent">xxxx-xx-xx至xxxx-xx-xx</view>
        </view>
        <view class="activeRuleItem">
          <view class="activeRuleItemTitle">活动时间</view> 
          <view class="activeRuleItemContent">xxxx-xx-xx至xxxx-xx-xx</view>
        </view>
      </view>
    </cardBox>
    <!-- 活动规则 E -->

    <!-- 刮刮乐 S -->
    <cardBox :options="scrachOptions">
      <view class="scrachBox">
        <luckyPaper @scratchComplete="scratchComplete" />
        <view class="luckyPaperText">活动时间:2025-06-01至2025-06-30</view>
        <view class="luckyPaperText">每次消耗10积分 每天最多参与3次</view>
      </view>
    </cardBox>
    <!-- 刮刮乐 E -->

    <!-- 奖品内容 S -->
    <cardBox :options="prizeOptions">
      <view class="prizeBox">
        <view class="prizeItem" v-for="(item,index) in prizeIconMap">
          <image class="prizeItemIcon" :src="item.icon" />
          <view class="prizeItemTitle">{{item.title}}</view>
          <view class="prizeItemContent">{{item.content}}</view>
        </view>
      </view>
    </cardBox>
    <!-- 奖品内容 E -->
    
    <!-- 中奖记录 S -->
    <cardBox :options="winRecordOptions">
      <view class="winRecordBox">
        <winRecord :winRecordList="winRecordList" />
      </view>
    </cardBox>
    <!-- 中奖记录 E -->

  </view>

</template>

<script>
import cardBox from './components/cardBox.vue'
import luckyPaper from './components/luckyPaper.vue'
import winRecord from './components/winRecord.vue'

  export default {
    name: '',
    components:{
      cardBox,
      luckyPaper,
      winRecord
    },
    data() {
      return {
        file_ctx: this.file_ctx,
        scratchMask: this.file_ctx + 'static/image/business/hulu-v2/scratchMask.png',
        scratchTabIcon: this.file_ctx + 'static/image/business/hulu-v2/scratchTabIcon.png',
        whiteLeftArrows: this.file_ctx + 'static/image/business/hulu-v2/white-left-arrows.png',
        statusBarHeight: 0,
        headerHeight: 0,
        prizeIconMap:[
          {
            icon: this.file_ctx + 'static/image/business/hulu-v2/firstPrize.png',
            title: '一等奖',
            content: '微信红包100元'
          },
          {
            icon: this.file_ctx + 'static/image/business/hulu-v2/twoPrize.png',
            title: '二等奖',
            content: '微信红包100元'
          },
          {
            icon: this.file_ctx + 'static/image/business/hulu-v2/threePrize.png',
            title: '三等奖',
            content: '微信红包100元'
          },
          {
            icon: this.file_ctx + 'static/image/business/hulu-v2/fourthPrize.png',
            title: '四等奖',
            content: '微信红包100元'
          },
          {
            icon: this.file_ctx + 'static/image/business/hulu-v2/fifthPrize.png',
            title: '五等奖',
            content: '微信红包100元'
          },
        ],
        winRecordList:[
          {name:'xxx',content:'抽中了x元微信红包'},
          {name:'xxx',content:'抽中了x元微信红包'},
          {name:'xxx',content:'抽中了x元微信红包'},
          {name:'xxx',content:'抽中了x元微信红包'},
          {name:'xxx',content:'抽中了x元微信红包'},
          {name:'xxx',content:'抽中了x元微信红包'},
          {name:'xxx',content:'抽中了x元微信红包'},
        ],
        activeRuleOptions:{left:'32rpx',top:'44rpx',outlineColor:'#FFE2A8',interiorColor:'#FFAE3D',title:'活动规则'},
        scrachOptions:{left:'32rpx',top:'35rpx',outlineColor:'#FFE2A8',interiorColor:'#FFAE3D',title:'刮刮乐'},
        prizeOptions:{left:'32rpx',top:'35rpx',outlineColor:'#FFE2A8',interiorColor:'#FFAE3D',title:'奖品内容'},
        winRecordOptions:{left:'32rpx',top:'35rpx',outlineColor:'#FFE2A8',interiorColor:'#FFAE3D',title:'中奖记录'},
      }
    },
    methods: {
      scratchComplete(){
        console.log('刮开完成');
        
      }
    },
    created() {
      let {height,top} = uni.getMenuButtonBoundingClientRect()
      this.statusBarHeight = top;
      this.headerHeight = height;
    }
  }
</script>
<style scoped lang="scss">
.scratch{
  width: 100vw;
  min-height: 100vh;
  background: #FFF2DE;
  position: relative;
  padding-bottom: 174rpx;
}
.scratchMask{
  width: 100vw;
  height: 990rpx;
  position: absolute;
  top: 0;
  left: 0;
}
.accompany-header{
  position: sticky;
  top: 0px;
  z-index: 99;
  padding: 0 16rpx;
  box-sizing: border-box;
  text-align: center;
  font-weight: bold;
  font-size: 30rpx;
  color: #FFFFFF;
  display: flex;
  justify-content: center;
  align-items: center;
  .header-search-img{
    width: 48rpx;
    height: 48rpx;
    position: absolute;
    left: 0;
  }
}
.scratchTabIcon{
  margin-left: 16rpx;
  width: 717.82rpx;
  height: 726rpx;
  margin-top: 24rpx;
  position: absolute;
}
.scratchTabBox{
  height: 726rpx;
  margin-top: 24rpx;
}
.activeRule{
  width: 100%;
  height: 464rpx;
  padding: 46rpx 36rpx 64rpx 64rpx;
  box-sizing: border-box;
  .activeRuleItem{
    width: 100%;
    height: 102rpx;
    margin-bottom: 24rpx;
    padding-bottom: 12rpx;
    border-bottom: 2rpx dashed  #AAAAAA;
    .activeRuleItemTitle{
      font-weight: 500;
      font-size: 28rpx;
      color: #000000;
    }
    .activeRuleItemContent{
      font-weight: 500;
      font-size: 24rpx;
      color: #333333;
      margin-top: 8rpx;
    }
  }
}
.scrachBox{
  width: 100%;
  height: 424rpx;
  padding: 30rpx 28rpx;
  box-sizing: border-box;
  .luckyPaperText{
    text-align: center;
    font-weight: 500;
    font-size: 24rpx;
    color: #777777;
  }
}
.prizeBox{
  width: 100%;
  height: 524rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-evenly;
  .prizeItem{
    width: 188rpx;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    .prizeItemIcon{
      width: 180rpx;
      height: 180rpx;
    }
    .prizeItemTitle{
      font-weight: 500;
      font-size: 24rpx;
      color: #333333;
      width: 100%;
      text-align: center;
    }
    &:nth-of-type(4){
      margin-left: 124rpx;
    }
    &:nth-of-type(5){
      margin-right: 124rpx;
    }
  }
}
.winRecordBox{
  width: 100%;
  height: 420rpx;
  padding: 40rpx 48rpx;
  box-sizing: border-box;
}
</style>