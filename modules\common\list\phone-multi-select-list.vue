<template>
  <page>
    <view slot="content" class="main-body">
      <phone-search-list class="search-list-custom" :phones="phones" @paramInput="paramInput" @paramClick="paramClick"></phone-search-list>
      <phone-directory class="hone-directory-body" v-if="name == ''" :phones="phones" @paramClick="paramClick"></phone-directory>
      <view class="foot">
        <view class="main">
          <view class="all-choose" @tap="chooseLesson('all')">
            <em :class="isAll?'icon-yijianfankui-d-ok':'icon-xuanze'"></em>
            <text>全选</text>
          </view>
          <view class="submit" @tap="clickData()">
            确认
          </view>
        </view>
      </view>
    </view>
  </page>
</template>

<script>

import phoneSearchList from '@/modules/common/components/basics/phone-directory/phone-search-list.vue'
import phoneDirectory from '@/modules/common/components/basics/phone-directory/phone-directory.vue'
import { mapState } from 'vuex'

export default {
  name: 'PhoneMultiSelectList',
  components: {
    phoneSearchList,
    phoneDirectory
  },
  data() {
    return {
      isOnShowType: false,
      isOnShowTwoType: false,
      isAll: false,
      paramsObj: {},
      name: '',
      phones: {},
      title: '',
      isAccountSelected: false // 是否开启一定选中当前账号人员
    }
  },
  computed: {
    ...mapState('user', {
      isLogin: state => state.isLogin,
      userInfoList: state => state.userInfoList,
      storeList: state => state.storeList,
      codeUserInfo: state => state.codeUserInfo // 当前登录用户信息
    }),
    ...mapState('system', {
      temporaryStorage: state => state.temporaryStorage
    })
  },
  onLoad(paramsObj) {
    const query = this.$Route.query
    if (!this.$validate.isNull(query)) {
      this.title = query.title
      this.isAccountSelected = query.isAccountSelected
      this.isOnShowType = query.isOnShowType
      this.isOnShowTwoType = query.isOnShowTwoType
    }
    if (this.title) {
      const title = this.title
      uni.setNavigationBarTitle({
        title: title
      })
    }
    if (this.$validate.isNull(this.temporaryStorage)) {
      this.$navto.back(1)
    } else {
      this.paramsObj = this.temporaryStorage
    }
    this.$uniPlugin.loading('加载中', true)
    this.$api.record.userQueryList({}, res => {
      let staffs = {}
      if (res.code === 0) {
        if (res.result.staffs) {
          staffs = res.result.staffs
          for (const key in staffs) {
            for (const i in staffs[key]) {
              staffs[key][i].name = staffs[key][i].fullname
              staffs[key][i].spell = staffs[key][i].namePinyin
              staffs[key][i].customChecked = false
              if (this.isAccountSelected) {
                if (this.codeUserInfo.id === staffs[key][i].userId) {
                  staffs[key][i].customChecked = true
                }
              }
            }
          }
          this.phones = staffs
          if (this.paramsObj.teacherParams) {
            if (this.paramsObj.teacherParams[this.paramsObj.teacherParams.routerKey]) {
              const arr = this.paramsObj.teacherParams[this.paramsObj.teacherParams.routerKey]
              for (let e = 0; e < arr.length; e++) {
                this.chooseLesson(arr[e].userId)
              }
            }
          }
        }
      }
      this.$uniPlugin.hideLoading()
    })
  },
  methods: {
    chooseLesson(type) {
      if (type === 'all') {
        this.isAll = !this.isAll
        for (const key in this.phones) {
          for (const i in this.phones[key]) {
            this.phones[key][i].customChecked = this.isAll
            if (this.isAccountSelected) {
              if (this.codeUserInfo.id === this.phones[key][i].userId) {
                this.phones[key][i].customChecked = true
              }
            }
          }
        }
      } else {
        let ketSry = ''
        for (const key in this.phones) {
          for (const i in this.phones[key]) {
            if (this.phones[key][i].userId == type) {
              ketSry = key
              this.phones[key][i].customChecked = !this.phones[key][i].customChecked
            }
            if (this.isAccountSelected) {
              if (this.codeUserInfo.id === this.phones[key][i].userId) {
                ketSry = key
                this.phones[key][i].customChecked = true
              }
            }
          }
        }
        let isVal = true
        for (const key in this.phones) {
          for (const i in this.phones[key]) {
            if (!this.phones[key][i].customChecked) {
              isVal = false
            }
          }
        }
        if (isVal) {
          this.isAll = true
        } else {
          this.isAll = false
        }
        if (ketSry != '') {
          this.$set(this.phones, ketSry, this.phones[ketSry])
        }
      }
    },
    clickData() {
      let arr = []
      for (const key in this.phones) {
        for (const i in this.phones[key]) {
          if (this.phones[key][i].customChecked) {
            arr.push(this.phones[key][i])
          }
        }
      }
      this.paramsObj.teacherParams[this.paramsObj.teacherParams.routerKey] = arr
      this.$common.setKeyVal('system', 'temporaryStorage', this.paramsObj)
      if (!!this.paramsObj.teacherParams.routerName && this.paramsObj.teacherParams.routerName.length > 0) {
        this.$navto.replace(this.paramsObj.teacherParams.routerName)
        return
      }
      if (this.isOnShowType) {
        this.$common.setKeyVal('system', 'isOnShow', true)
      }
      if (this.isOnShowTwoType) {
        this.$common.setKeyVal('system', 'isOnShowTwo', true)
      }
      this.$navto.back(1)
    },
    paramClick(e) {
      this.chooseLesson(e.userId)
    },
    paramInput(e) {
      this.name = e
    }
  }
}
</script>

<style lang="scss" scoped>
.main-body{
  height: 100%;
}
.search-list-custom{
  /*position: fixed;*/
  /*left: 0;*/
  /*right: 0;*/
  /*z-index: 12;*/
  /deep/.search-main{
    position: fixed;
    bottom: 98upx;
    top: 88upx;
    /* #ifdef H5*/
    top: 178upx;
    /* #endif */
    height: auto;
  }
}
.hone-directory-body{
  height: calc(100% - 186upx) !important;
}
.foot{
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 98upx;
  overflow: hidden;
  background-color:#ffffff;
  z-index: 15;
  .main{
    width: 100%;
    height: 98upx;
    position: relative;
    .all-choose{
      width:100%;
      em{
        float: left;
        width:46upx;
        height: 46upx;
        margin:26upx 0 0 30upx;
      }
      text{
        float: left;
        line-height: 98upx;
        font-size: 36upx;
        color:#999999;
        margin-left: 12upx;
      }
    }
    .submit{
      position: absolute;
      right: 0;
      width: 300upx;
      height: 98upx;
      line-height: 98upx;
      text-align: center;
      color:#ffffff;
      background-color:$topicC;
      font-size: 36upx;
      /*display: none;*/
    }
    .del{
      position: absolute;
      right: 0;
      width: 66.6%;
      overflow: hidden;
      text{
        width: 50%;
        height: 98upx;
        line-height: 98upx;
        font-size: 36upx;
        text-align: center;
        float: left;
      }
      .cancel{
        background:#F1F1F1;
        color:#333333;
      }
      .delete{
        background:#F43530;
        color:#ffffff;
      }
    }
  }
}
</style>
