<template>
  <view class="my-poster">
    <view class="" v-if="false"></view>
    <view class="bg" v-else :style="{'background-image':'url(' + calabashBg + ')',height:'calc(100vh - ' + windowTop + 'px)'}">
      <!-- 葫芦容器 -->
      <calabashContainer :goldMaxNum='goldMaxNum' :initGoldNum='initGoldNum' @goldInsert='goldInsert' :initGoldMap='ScoreList' />
      <!-- 弹幕群 -->
      <bottomInputDM></bottomInputDM>
    </view>
  </view>
</template>

<script>
  import { mapState } from "vuex";
  import calabashApis from "@/modules/common/api/calabash.js"
  import bottomInputDM from "./components/bottomInputDM.vue";
  import calabashContainer from "./components/calabashContainer.vue";
  export default {
    components:{
      bottomInputDM,
      calabashContainer
    },
    computed: {
      ...mapState("user", {
        accountId: (state) => state.accountId,
        fansRecord: (state) => state.fansRecord,
      }),
    },
    data() {
      return {
        $appId: this.$appId,
        calabashBg: this.$static_ctx + "image/business/hulu-v2/calabashBg.png",
        initGoldNum:0,
        windowTop: 0,
        goldMaxNum:150,
        ScoreList:[],
        isWx:null,
      }
    },
    mounted: async function(e) {
      console.log('show触发');
      this.initGold()
      this.getSystemInfo()
    },
    methods: {
      // 金币初始化
      async initGold() {
        // 获取初始化金币数量
        let {data} = await calabashApis.pointuserQueryUser({accountId:this.accountId})
        this.initGoldNum = data.totalPoint;
        // this.initGoldNum = 0;
        // 获取积分列表
        let {data:ScoreList} = await calabashApis.pointrecordScoreList({accountId:this.accountId})
        this.ScoreList = ScoreList.map(e=>{e.goldNum = e.point;return e})
      },
      goldInsert(index){
        calabashApis.pointrecordReceivePoint({id:this.ScoreList[index].id})
        this.ScoreList.splice(index,1)
      },
      getSystemInfo() {
        uni.getSystemInfo({
          success: res => this.windowTop = res.windowTop,
        })
      }
    },
  }
</script>

<style scoped lang="scss">
  .bg {
    width: 100vw;
    height: 100vh;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    overflow: hidden;
  }
</style>
