<template>
  <page>
    <view slot="content" class="body-main">
      <status-bar-height />
      <!-- #ifdef MP-WEIXIN -->
      <view style="height: 44px;"></view>
      <!-- #endif -->
      <view class="title-box">
        <text class="title-text">关注感兴趣的圈子</text>
        <text class="title-desc">选择感兴趣的圈子可以获得更精准的内容推荐</text>
      </view>
      <attention :tabs="circleclassifyList" @changeSelect="changeSelect" />
      <view class="fixed-box bottom-box">
        <view class="back" @tap="jumpOver">
          <text class="back-text">跳过</text>
          <uni-icons :size="18" color="#4E5569" type="right" />
        </view>
        <button type="default" class="submit-btn" @tap="attention" v-if="checkboxList.length">我选好了（已选{{ checkboxList.length }}个）</button>
        <button type="default" class="submit-btn" @tap="openPopup" v-else>没我感兴趣的，提交登记</button>
      </view>
      <uni-popup ref="inputDialog" id="inputDialog" type="dialog">
        <uni-popup-dialog
          v-if="showPopup"
          v-model="regForm.name"
          mode="input"
          title="未找到想要关注的？"
          placeholder="请输入希望开通的圈子"
          @confirm="dialogInputConfirm"
          @close="regForm.name = ''"
        ></uni-popup-dialog>
      </uni-popup>
    </view>
  </page>
</template>

<script>
import attention from './components/circle-attention'
import UniIcons from '@/components/uni/uni-icons/uni-icons'
import uniPopup from '@/components/uni/uni-popup/uni-popup.vue'
import uniPopupDialog from '@/components/uni/uni-popup-dialog/uni-popup-dialog.vue'
import { mapState } from 'vuex'
export default {
  components: {
    attention,
    UniIcons,
    uniPopup,
    uniPopupDialog
  },
  data() {
    return {
      circleclassifyList: [],
      checkboxList: [],
      regForm: {
        name: ''
      },
      query: {},
      showPopup: false
    }
  },
  computed: {
    ...mapState('user', {
      accountId: state => state.accountId
    })
  },
  onLoad() {
    this.getCircleclassifyQueryList()
    const query = this.$Route.query
    this.query = query
  },
  methods: {
    // 提交登记
    dialogInputConfirm() {
      const vm = this
      const param = {
        businessType: 4, // 业务类型:1-横幅，2-外部服务应用，3-企业微信群，4-圈子，5-帖子，6-帖子评论
        name: this.regForm.name,
        accountId: this.accountId,
        desc: this.regForm.name
      }
      this.$api.community.matterregisterInsert(param).then(res => {
        this.$uniPlugin.toast('提交成功')
        this.regForm.name = ''
        setTimeout(() => {
          vm.jumpOver()
        }, vm.$constant.noun.delayedOperationTime1000)
      })
    },
    openPopup() {
      this.showPopup = true
      this.$refs.inputDialog.open()
      // #ifdef MP-WEIXIN
      getApp().globalData.sensors.track("PopupClick",
        {
          'page_name' : '关注圈子',
          'popup_id' : 'inputDialog',
          'popup_name' : '圈子弹窗',
          'click_type' : '进入弹窗',
        }
      ) 
      // #endif
    },
    jumpOver() {
      this.$common.setKeyVal('business', 'openGuideSubscribe', false)
      this.$navto.back(1)
    },
    changeSelect(e) {
      this.checkboxList = e
    },
    // 一键关注
    attention() {
      if (this.$validate.isNull(this.checkboxList)) {
        this.$uniPlugin.toast('请先选择圈子！')
        return
      }
      const param = {
        accountId: this.accountId,
        circleClassifyIds: this.checkboxList
      }
      this.$uniPlugin.loading('正在提交', true)
      this.$api.circleclassify.circleclassifySubscribe(param).then(res => {
        this.$uniPlugin.hideLoading()
        this.$uniPlugin.toast('关注成功')
        setTimeout(() => {
          this.$common.setKeyVal('business', 'openGuideSubscribe', false)
          uni.switchTab({
            url: 'pages/circle-home/index'
          })
        }, this.$constant.noun.delayedOperationTime1000)
      }).catch(() => {
        this.$uniPlugin.hideLoading()
      })
    },
    // 获取圈子列表
    getCircleclassifyQueryList() {
      const that = this
      const param = {
        accountId: this.accountId
      }
      that.$api.circleclassify.circleclassifyQueryUnsubscribeList(param).then(res => {
        this.circleclassifyList = res.data
      })
    }
  }
}
</script>

<style scoped lang="scss">
.body-main {
  height: 100%;
  overflow-y: auto;
  background-color: #fff;
}
.title-box {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 24rpx;
  .title-text {
    font-weight: 500;
    font-size: 48rpx;
    color: #1D2029;
    line-height: 66rpx;
  }
  .title-desc {
    font-weight: 400;
    font-size: 28rpx;
    color: #4E5569;
    line-height: 40rpx;
    margin-top: 16rpx;
  }
}
.bottom-box {
  padding: 0 48rpx;
  box-sizing: border-box;
}
.submit-btn {
  width: 530rpx;
  height: 104rpx;
  background: #00B484;
  border-radius: 52rpx;
  font-weight: 500;
  font-size: 32rpx;
  color: #FFFFFF;
  line-height: 104rpx;
}
.fixed-box {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: fixed;
  bottom: calc(constant(safe-area-inset-bottom) + 50rpx);
  bottom: calc(env(safe-area-inset-bottom) + 50rpx);
}
.back {
  display: flex;
  align-items: center;
  &-text {
    font-size: 32rpx;
    color: #4E5569;
    line-height: 44rpx;
  }
}
.more {
  display: flex;
  flex-direction: column;
  margin: 100upx auto 0;
  padding: 0 34upx;
  &-title {
    font-size: 31upx;
    font-weight: 500;
    color: #282828;
    line-height: 42upx;
    &::before {
      content: "*";
      color: #00d29d;
    }
  }
  &-input {
    margin-top: 24upx;
    height: 78upx;
    background: #ffffff;
    border: 1upx solid #e5e5e5;
    @include rounded(15upx);
    padding-left: 24upx;
    font-size: 31upx;
    line-height: 42upx;
  }
  .submit-btn {
    flex: 1;
    height: 100%;
    background: #00d29d;
    border-radius: 40upx;
    font-size: 33upx;
    font-weight: 500;
    color: #ffffff;
    line-height: 80upx;
    & + .submit-btn {
      margin-left: 24rpx;
    }
  }
  .default-btn {
    background-color: #fff;
    border: 1px solid #00d29d;
    color: #00d29d;
  }
  .btn-box {
    display: flex;
    align-items: center;
    margin-top: 90upx;
    height: 80upx;
  }
}
</style>
