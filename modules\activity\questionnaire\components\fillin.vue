<template>
  <!-- :templateData="templateData" -->
  <view>
    <template v-if="!isAccuratePromotion">
      <title-input
        :disabled="cDisabled"
        :config="config.userName"
        v-model="regForm.userName"
        horizontal
      ></title-input>
    </template>
    <!-- value ="活动类型1病例征集2学术调研"， -->
    <form-template
      :activitytype="activitytype"
      :cDisabled="cDisabled"
      :height="height"
      :ptype="1"
      :noloading="noloading"
      :cmainid="mainId"
      :emainId="emainId"
      :updatecount="updatecount"
      :updatepage="updatepage"
      :businessid="editid"
      :isone="false"
      :tenantId="tenantId"
      :isAccuratePromotion='isAccuratePromotion'
      :collectionType='collectionType'
      :submitText='submitText'
      :provinceCity="provinceCity"
      @returnFn="returnFn"
      @getSubmitItemlog="getSubmitItemlog"
    >
    </form-template>
  </view>
</template>

<script>
import FormTemplate from '@/modules/activity/components/form-template/index.vue';
import { mapState } from "vuex"
import TitleInput from '@/components/business/module/v2/title-input/index'
// import common from '@/common/util/main'
export default {
  name: 'fillincase',
  components: {
    FormTemplate,
    TitleInput
  },
  props: {
    // 提交按钮文案
    submitText:{
      type:String,
      default:null,
    },
    // 入口是不是小葫芦精准地推
    isAccuratePromotion:{
      type:Boolean,
      default:false,
    },
    tenantId: {
      type: String,
      default: '1'
    },
    // 表单提交参数
    submitParams: {
      type: Object,
      default: () => {
        return {}
      }
    },
    disabled: {
      type: Boolean,
      default: false
    },
    height: {
      type: String,
      default: '100vh'
    },
    updatecount: {
      type: Number,
      default: 0,
    },
    steps: {
      type: Array,
      default: function () {
        return [];
      }
    },
    ids: {
      type: [Number, String],
    },
    // 采集类型
    collectionType: {
      type: Number,
      default: null,
    },
    provinceCity: {
      type: Array,
      default: () => []
    }

  },
  data() {
    return {
      $common: this.$common,
      editid: '',
      updatepage: 0,
      mainId: null,
      noloading: false,
      emainId: null,
      back: false,
      activitytype: 2,
      cDisabled: false,
      // step: 0,
      // updatecount: 0,
      // templateData: null
      // height
      regForm: {
        userName: '',
      },
      config: {
        userName: {
          label: '昵称',
          name: 'userName',
          required: true,
          type: 'nickname'
        }
      }
    };
  },
  watch: {
    disabled() {
      this.cDisabled = this.disabled
    },
    // updatecount(n){
    //   this.editid = this.id;
    // },
    ids(n) {
      console.log('进来了', n)
      this.editid = n;
    }
  },
  onLoad(options) {
    // console.log('options',options)
    const query = this.$Route.query
    console.log('query', query)
    if (!this.$validate.isNull(query)) {
      this.editid = query.id;
      this.emainId = query.mainId
      this.back = query.back;
      this.cDisabled = query.disabled

      this.$nextTick(() => {
        this.updatepage += 1
      })
      //   this.id = query.id
      //   this.index = query.index
    }
  },
  mounted() {
    if (this.ids) {
      this.editid = this.ids;
    }
    // 834610603728510977

    // this.getDetail('835483840192643077');
  },
  computed: {
    ...mapState('user', {
      recordUserInfo: state => state.recordUserInfo // 当前登录用户信息
    })
  },
  methods: {
    getSubmitItemlog(e) {
      const { userName } = e
      this.regForm.userName = userName
    },
    setData({ id, mainId, userName }) {
      this.editid = id ? id : this.editid;
      this.emainId = mainId ? mainId : this.emainId
      this.regForm.userName = userName || ''
      this.$nextTick(() => {
        this.updatepage += 1
      })
    },
    updateForm(obj) {
      const that = this
      obj.key = obj.key.trim()
      that.regForm[obj.key] = obj.value
    },
    // 835483840192643077
    // getDetail(id) {
    //   const that = this;
    //   that.$api.activity.researchQueryOne({ id: id }).then(res => {
    //     res.data.pushTimeText = this.$common.formatDate(new Date(res.data.createTime), 'yyyy-MM-dd');
    //     that.regForm = res.data;
    //     that.$api.activity.queryBusinessTemplateDTOList({ templateIds: that.regForm.copyTemplateId }).then(res => {
    //       this.templateData = res.data;
    //       that.openTemplate = true;
    //       // this.$forceUpdate()

    //       this.$nextTick(() => {
    //         console.log('进来');
    //         console.log(this.templateData);
    //         this.updatecount += 1;
    //       });
    //     });
    //   });
    // },
    returnFn(obj) {
      let param = obj.param
      if (!this.regForm.userName && !this.isAccuratePromotion) {
        this.$uniPlugin.toast('请填写昵称')
        this.noloading = true
        this.$nextTick(() => {
          setTimeout(() => {
            this.noloading = false;
          }, 500)
        })
        this.cDisabled = false
        return
      }else if(this.isAccuratePromotion){
        // 精准问卷地推
        this.regForm.userName = obj.userName;
      }
      console.log(this.regForm)
      const { name, userId } = this.recordUserInfo;
      if (param.patientInfo instanceof Object) {
        const curSelectStore = this.$common.getKeyVal('user', 'curSelectStore', true)
        let recordId = curSelectStore && curSelectStore.userTenantRecordList[0] ? curSelectStore.userTenantRecordList[0].recordId : null;

        if (!recordId) {
          this.noloading = true;
          this.$nextTick(() => {
            setTimeout(() => {
              this.noloading = false;
            }, 500)
          })
          return this.$uniPlugin.toast('档案为空,不能保存');
        }
        param.patientInfo.physicianUserId = recordId
      }
      param = {
        ...param,
        ...this.submitParams,
        userName: this.regForm.userName,
        accountId: this.$common.getKeyVal('user', 'accountId', true),
        userId: userId,
        // attacheUserId:userId,
        // physicianUserId:userId,
        activityType: this.activitytype,
        formTemplateWriteType: 5, // 3 病例征集流程提交记录 4 病例征集回访 5 学术调研流程提交记录 6 学术调研回访
        tenantId: this.tenantId
      };
      if (!param.mainId) {
        param.auditStatus = 1 // 默认待审核
      }
      const that = this;
      that.$api.activity.casecollectsubmitlogformWrite(param, { 'gb-part-tenant-id': this.tenantId }).then(res => {
        that.$uniPlugin.toast('提交成功');
        this.mainId = res.data.mainId


        this.noloading = true;

        this.$nextTick(() => {
          setTimeout(() => {
            this.noloading = false;
          }, 500)
        })
        if (obj.next) {
          let pages = getCurrentPages();
          if (pages.length < 2) {
            this.$navto.replace('Research')
            return
          }
          if (this.emainId && this.back) {
            this.$navto.back(1);
          }
        }





        // this.getDetail(this.id);
      }).catch(e => {
        // console.log('jkkk错误')
        this.noloading = true;

        this.$nextTick(() => {
          setTimeout(() => {
            this.noloading = false;
          }, 500)
        })
      });
    }
  }
};
</script>

<style lang="scss" scoped></style>
