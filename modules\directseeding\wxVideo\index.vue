<template>
  <view class="">

<!--    <view class="abc" @click="toRoot">
      点我去往看直播
    </view>
    <navigator :url="'plugin-private://'+zbAPPID+'/pages/live-player-plugin?room_id='+roomId+'&custom_params='+customParams">

      点我去往看直播
    </navigator> -->
  </view>
</template>

<script>
  export default {
    name:"wxVideo",
    data(){
      return {
        zbAPPID:"wx2b03c6e691cd7370",
        roomId:1,
      }
    },
    methods:{
      toRoot(){
        console.log('toRoot')
        // let roomId = [1] // 填写具体的房间号，可通过下面【获取直播房间列表】 API 获取
        // let customParams = encodeURIComponent(JSON.stringify({ path: 'modules/directseeding/wxVideo/index', pid: 1 })) // 开发者在直播间页面路径上携带自定义参数（如示例中的path和pid参数），后续可以在分享卡片链接和跳转至商详页时获取，详见【获取自定义参数】、【直播间到商详页面携带参数】章节（上限600个字符，超过部分会被截断）
        // this.setData({
        //     roomId,
        //     customParams
        // })
        let roomId = [1] // 填写具体的房间号，可通过下面【获取直播房间列表】 API 获取
        let customParams = encodeURIComponent(JSON.stringify({ path: 'modules/directseeding/wxVideo/index', pid: 1 })) // 开发者在直播间页面路径上携带自定义参数（如示例中的path和pid参数），后续可以在分享卡片链接和跳转至商详页时获取，详见【获取自定义参数】、【直播间到商详页面携带参数】章节（上限600个字符，超过部分会被截断）
        uni.navigateTo({
            url: `plugin-private://${this.zbAPPID}/pages/live-player-plugin?room_id=${roomId}&custom_params=${customParams}`
        })
        // 其中wx2b03c6e691cd7370是直播组件appid不能修改

      }
    }
  }
</script>

<style lang="scss" scoped>
  .abc{
    font-size: 32upx;
    height: 80upx;
    background-color: yellowgreen;
    padding: 0 20upx;
    display: inline-flex;
    margin: 0 auto;
    justify-content: center;
    align-items:center;
    color: #fff;

  }
</style>
