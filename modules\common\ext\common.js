import api from '@/service/api'
import common from '@/common/util/main'
import validate from '@/common/util/validate'
import store from '@/store'
import uniPlugin from '@/common/util/uni-plugin'
import $env from '@/config/env'
const file_ctx = $env.file_ctx
import { getQueryObject } from '@/utils/index'
/**
 * 系统共用EXT数据业务类
 */
export default {
  /**
   * 解析APP获取页面参数解析
   * @param option
   */
  parseAppScene(option) {
    if (!validate.isNull(option) && !validate.isNull(option.query) && !validate.isNull(option.query.scene)) {
      const sceneResult = {
        scene: option.scene,
        path: option.path,
        sceneCode: option.query.scene
      }
      common.setKeyVal('system', 'scene', sceneResult, true)
      common.setKeyVal('system', 'extraData', {}, true)

      let gbScene = ''
      let pharmacyPhone = ''
      let pharmacyShareType = ''
      let homePopupTypeObj = null
      if (validate.judgeTypeOf(option.query.scene) === 'Object') {
        gbScene = option.query.scene.gbScene || option.query.scene.gs
      } else {
        gbScene = option.query.scene
        try {
          console.log('gbScene---------', gbScene)
          gbScene = decodeURIComponent(decodeURIComponent(decodeURIComponent(gbScene)))  //2024.5.13 扫码进来有% 报错URI malformed
        } catch(err) {
          console.log('err--------', err)
        }
        let temp = getQueryObject(gbScene).gbScene || getQueryObject(gbScene).gs
        pharmacyPhone = getQueryObject(gbScene).p
        pharmacyShareType = getQueryObject(gbScene).t
        homePopupTypeObj = getQueryObject(gbScene)

        common.setKeyVal('system', 'gbScene', temp, true)
        common.setKeyVal('user', 'pharmacyPhone', pharmacyPhone, true)
        common.setKeyVal('user', 'pharmacyShareType', pharmacyShareType, true)
        common.setKeyVal('user', 'homePopupTypeObj', homePopupTypeObj)
      }
    } else if (!validate.isNull(option) && !validate.isNull(option.referrerInfo) && !validate.isNull(option.referrerInfo.extraData)) {
      // 从另外一个小程序跳转所带过来的参数的临时存储
      const extraData = option.referrerInfo.extraData
      common.setKeyVal('system', 'extraData', extraData, true)
      common.setKeyVal('system', 'scene', {}, true)
    } else if (!validate.isNull(option) && !validate.isNull(option.query) && (!validate.isNull(option.query.gbScene) || !validate.isNull(option.query.gs))) {
      // 渠道链入口
      const gbScene = option.query.gbScene || option.query.gs
      const pharmacyPhone = option?.query?.phone //说明书卡片分享进来
      const pharmacyShareType = option?.query?.shareType 

      common.setKeyVal('system', 'gbScene', gbScene, true)
      common.setKeyVal('user', 'pharmacyPhone', pharmacyPhone, true)
      common.setKeyVal('user', 'pharmacyShareType', pharmacyShareType, true)
    } else {
      common.setKeyVal('system', 'scene', {}, true)
      common.setKeyVal('system', 'extraData', {}, true)
    }
  },
  /**
   * 解析路由函数方法，针对分享二维码和分享好友
   * @param routeObject 原始路由对象
   * @param callback 回调方法块
   */
  parseSceneResult(routeObject, callback = () => {}){
    const query = routeObject.query
    let sceneResult = common.getKeyVal('system', 'scene', true) || {}
    if (!validate.isNull(sceneResult)) {
      if ('/'+sceneResult.path === routeObject.path){
        const param = {
          path:sceneResult.path,
          scene:sceneResult.sceneCode,
        }
        api.common.getSmallProgramQrcode(param).then(res => {
          const parameters = JSON.parse(res.parameters)
          // 判断是否存在主动分享用户主键
          if (!validate.isNull(query.shareUserId)){

          }
          callback(parameters)
          common.setKeyVal('system', 'scene', {},true)
          common.setKeyVal('system', 'shareUserId', parameters.shareUserId,true)
        })
      }
    }else {
      // 判断是否存在主动分享用户主键
      if (!validate.isNull(query.shareUserId)){

      }
      // 从另外一个小程序跳转所带过来的参数的临时存储
      const extraData = common.getKeyVal('system', 'extraData', true)
      if (!validate.isNull(extraData)) {
        callback(extraData)
        common.setKeyVal('system', 'extraData', {}, true)
        return
      }
      callback(query)
    }
  },
  /**
   * 获取未读数据量
   */
  queryUnread(callback = () => {}) {
    if (!this.loginCheck()) {
      return
    }
    const curSelectUserInfo = common.getKeyVal('user', 'curSelectUserInfo')

    if (validate.isNull(curSelectUserInfo)) {
      return
    }
    const params = {
      userType: $env.userType,
      readStatus:2
    }
    api.common.noticelogQueryAppRecordStatUnread(params).then(res => {
      const num = Number(res.data)
      if (callback) callback({
          campusNum: Number(res.data)
      })
      // numFn(num)
    })
    function numFn(num) {
      if (num > 0) {
        uniPlugin.setTabBarBadge(
          1,
          (num > 99 ? '99+' : num + '')
        )
      } else {
        uniPlugin.removeTabBarBadge(1)
      }
    }
  },
  /**
   * 处理当前获取经纬度后的业务逻辑
   * @param params.longitude 经度
   * @param params.latitude 纬度
   * @returns {Promise<any>}
   */
  getPosition(params) {
    return new Promise((resolve, reject) => {
      api.common.homepageQueryCity({ lat: params.longitude, lng: params.latitude }).then(obj => {
        // console.log('homepageQueryCity:', obj)
        obj.longitude = params.longitude
        obj.latitude = params.latitude
        common.setKeyVal('system', 'longitudeAndLatitude', obj, true)
        const poiObj = {
          city: obj.city,
          keywords: obj.formatted_address,
          offset: 1,
          page: 1
        }
        if(poiObj.city == '[]'){    //2024.8.14 加容错 以为拿不到城市
          poiObj.city = obj.province
        }
        api.common.homepageQueryPoi(poiObj).then(data => {
          // console.log('homepageQueryPoi:', data)
          let cityInfo;
          if (data) {
            cityInfo = data.pois[0]
            common.setKeyVal('system', 'cityInfoStorage', cityInfo, true)
            let arr = common.getKeyVal('system', 'cityInfoArrStorage', true)
            if (typeof arr === 'string' && arr === '') {
              arr = []
            } else if (typeof arr === 'string' && arr !== '') {
              arr = JSON.parse(arr)
            }
            if (arr) {
              if (typeof arr === 'object' && arr.length > 0) {
                let state = false
                let index = ''
                for (let i = 0; i < arr.length; i++) {
                  if (cityInfo.id === arr[i].id) {
                    state = true
                    index = i
                    break
                  }
                }
                if (state) {
                  if (typeof index === 'number') {
                    arr.splice(index, 1)
                  }
                }
              }
              arr.unshift(cityInfo)
              common.setKeyVal('system', 'cityInfoArrStorage', arr, true)
            } else {
              common.setKeyVal('system', 'cityInfoArrStorage', [], true)
            }
          }
          resolve(cityInfo)
        })
      }).catch(error => {
        reject(error)
      })
    })
  },
  /**
   * 登录状态判断
   */
  loginCheck() {
    return store.state.user.isLogin
    // #ifdef H5

    // #endif
  },
  /**
   * 文件下载
   */
  downloadFile(id, fn = () => {}) {
    uniPlugin.loading('加载中', true)
    api.common.attachmentDownload({}, '/' + id).then(res => {
      uniPlugin.hideLoading()
    })
  },
  /**
   * 查询消息未读数
   */
  async queryNewsUnread () {
    const that = this
    const [ tenantUnreadRes, communityUnreadRes, beInviteCountRes, consultUnreadRes ] = await Promise.allSettled(
      [
        that.getTenantUnread(),
        that.getCommunityUnread(),
        that.getBeInviteCount(),
        that.getConsultUnread()
      ]
    )
    const tenantUnread = tenantUnreadRes.status === 'fulfilled' ? tenantUnreadRes.value : 0
    const communityUnread = communityUnreadRes.status === 'fulfilled' ? communityUnreadRes.value : 0
    const beInviteCount = beInviteCountRes.status === 'fulfilled' ? beInviteCountRes.value : 0
    const consultUnread = consultUnreadRes.status === 'fulfilled' ? consultUnreadRes.value : 0
    const newsUnreadObj = {
      tenantUnread,
      communityUnread,
      consultUnread,
      beInviteCount
    }
    common.setKeyVal('user', 'newsUnreadObj', newsUnreadObj)
    let allUnredNum = 0
    allUnredNum += beInviteCount ? Number(beInviteCount) : 0
    allUnredNum += tenantUnread ? Number(tenantUnread) : 0
    allUnredNum += communityUnread.collectionCount ? Number(communityUnread.collectionCount) : 0
    allUnredNum += communityUnread.commitCount ? Number(communityUnread.commitCount) : 0
    allUnredNum += consultUnread ? Number(consultUnread) : 0

    // #ifdef MP-WEIXIN
    let tabBarBadge = {}
    if (allUnredNum) {
      wx.setStorageSync('tabBarBadge', {
        ...tabBarBadge,
        3: allUnredNum > 99 ? '99+' : allUnredNum + ''
      })
      tabBarBadge[3] = allUnredNum > 99 ? '99+' : allUnredNum + ''
    } else {
      wx.setStorageSync('tabBarBadge', {
        ...tabBarBadge,
        3: 0
      })
      tabBarBadge[3] = 0
    }
    return Promise.resolve(tabBarBadge)
    // #endif
    // 更新未读数
    if (allUnredNum) {
      uniPlugin.setTabBarBadge(3, allUnredNum > 99 ? '99+' : allUnredNum + '')
    } else {
      uniPlugin.removeTabBarBadge(3)
    }
    return Promise.resolve()
  },
  // 获取租户未读消息数
  getTenantUnread () {
    // 页面暂时不需要展示租户消息未读数
    return Promise.resolve(0)
    const that = this
    return new Promise((resolve, reject) => {
      if (!that.loginCheck()) {
        resolve(0)
        return
      }
      const curSelectUserInfo = common.getKeyVal('user', 'curSelectUserInfo')
      if (validate.isNull(curSelectUserInfo)) {
        resolve(0)
        return
      }
      const params = {
        userType: $env.userType,
        readStatus:2
      }
      api.common.noticelogQueryAppRecordStatUnread(params).then(res => {
        const num = Number(res.data)
        resolve(num)
      }).catch(err => {
        reject(0)
      })
    })
  },

  // 获取社区回复、点赞收藏数
  getCommunityUnread () {
    return new Promise((resolve, reject) => {
      const param = {
        accountId: common.getKeyVal('user', 'accountId')
      }
      api.community.noticelogGetCommunityUnreadStatistics(param).then(res => {
        resolve(res.data)
      }).catch(err => {
        resolve(0)
      })
    })
  },

  // im咨询列表消息未读数统计
  getConsultUnread () {
    let unReadNum = 0
    const chatlist = common.getKeyVal('chat', 'chatlist', false)
    if (validate.isNull(chatlist)) return 0
    chatlist.forEach(item => {
      unReadNum += item.extendUnMsgCount
    })

    return unReadNum
  },

  // 帖子邀请评论 未回复数
  async getBeInviteCount() {
    const accountId = common.getKeyVal('user', 'accountId', true)
    if (!accountId) return Promise.resolve(0)
    const param = {
      accountId
    }
    const res = await api.community.cfpostinvitelogBeInviteCount(param).catch(err => {
      return Promise.reject(err)
    })
    return Promise.resolve(res.data)
  },

  /**
   * 添加渠道链访问记录
   * @param {number} visitType 1-进入，2-离开 3-显示
   */
  async addMinichannellinklogvisit (visitType) {
    return new Promise((resolve, reject) => {
      const gbScene = common.getKeyVal('system', 'gbScene', true)
      if (!gbScene) return reject()
      const param = {
        visitType,
        code: common.getKeyVal('system', 'gbScene', true),
        accountId: common.getKeyVal('user', 'accountId', true),
        phone:common.getKeyVal('user', 'pharmacyPhone', true),
        shareType:common.getKeyVal('user', 'pharmacyShareType', true),
  
        // #ifdef H5
        terminalType: 2, // 1-小程序，2-H5
        // #endif
        // #ifdef MP-WEIXIN
        terminalType: 1, // 1-小程序，2-H5
        // #endif
      }
      api.sys.minichannellinklogVisit(param).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },

  /**
   * 记录小程序渠道链打开应用记录
   * 兼容接口报错的情况下 App.vue里的逻辑不会重复调用 会记录不到
   */
  addMinichannellinklogLaunchVisit() {
    const channellinkIsRecord = common.getKeyVal('business', 'channellinkIsRecord')
    if (channellinkIsRecord) return
    common.setKeyVal('business', 'channellinkIsRecord', true)
    this.addMinichannellinklogvisit(1).then(() => {
      common.setKeyVal('business', 'channellinkIsRecord', true)
    }).catch(() => {
      common.setKeyVal('business', 'channellinkIsRecord', false)
    })
  }
}
