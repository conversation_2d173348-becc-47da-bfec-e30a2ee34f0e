// 引入积分模块接口 并拦截全局api请求判定当前是否属于任务接口
import calabashApis from "@/modules/common/api/calabash.js"
import common from '@/common/util/main'
import {taskAPIsOrRouterMaps,isPathInInterfaceTable,reloadTaskList,pointtasklogHandle,taskList,getCurrentPath} from './options'
import {stratagemOnLoad,stratagemOnUnload,stratagemInterceptor} from '@/stratagem'
// 定时器
let timeerMap = [];
// 定义函数创造积分埋点
  // 定义变量传递到混入里被vue洗过一次后 可直接对该变量进行操作
  const IntegrationData = {
    integrationRuntimeNum:0, //加载时长
    integrationNum:0, //获取的积分
    backTaskFlag:false ,//判断是否显示返回福币任务按钮
    codePopupFlag:false // 判断是否显示积分弹窗
  }
function createIntegrationBuried(){
  IntegrationData.integrationRuntimeNum = 0 //加载时长
  IntegrationData.integrationNum = 0 //获取的积分
  // 定义 混入onLoad生命周期 确保可以触发判定当前进入的路由
  const IntegrationOnLoad = function(load){
    const accountId = common.getKeyVal('user', 'accountId', true);
    reloadTaskList(accountId,'onReady')
    let pages = getCurrentPages();
    let currentPath = pages[pages.length - 1].$page.fullPath;
    let [routeOptions] = isPathInInterfaceTable(currentPath,'route');
    // 调用神策埋点模块
    stratagemOnLoad.call(this,currentPath)
    if(!routeOptions) return
    if(routeOptions.shareTask) return
    routeOptions.taskList = taskList.filter(task=>task.eventType === routeOptions.eventType && !task.finish)
    // 当前若无任务则退出监听
    if(routeOptions.taskList.length === 0) return
    // 通知任务完成
    pointtasklogHandle(routeOptions,{},IntegrationData)
  }
  // 定义 混入onShow生命周期 确保可以触发判定当前进入的路由
  const IntegrationOnShow = (load,that)=>{
    let backTaskFlag = that.$store.state.system.backTaskFlag;
    IntegrationData.backTaskFlag = backTaskFlag;
  }
  // 定义 混入onShareAppMessage生命周期 确保可以触发判定当前进入的路由
  const IntegratiOnShare = function (Share){
    // console.log('触发分享');
    let currentPath = getCurrentPath()
    let [routeOptions] = isPathInInterfaceTable(currentPath,'route');
    if(!routeOptions) return
    if(!routeOptions.shareTask) return
    routeOptions.taskList = taskList.filter(task=>task.eventType === routeOptions.eventType && !task.finish)
    // 当前若无任务则退出监听
    if(routeOptions.taskList.length === 0) return
    // 通知任务完成
    // console.log('通知任务完成');
    pointtasklogHandle(routeOptions,{},IntegrationData)
  }
    // 定义 混入Destroy生命周期 确保在页面销毁后关闭定时器
  const IntegratiOnUnload = function (){
    // console.log('页面销毁',timeerMap);
    let pages = getCurrentPages();
    let currentPath = pages[pages.length - 1].$page.fullPath;
    let [routeOptions] = isPathInInterfaceTable(currentPath,'route');
    // 调用神策埋点模块
    stratagemOnUnload.call(this,currentPath)
    // 每次页面销毁重置请求开关
    taskAPIsOrRouterMaps.map(e=>{e.isRequest = false})
    timeerMap.map(e=>{
      clearInterval(e)
    })
    timeerMap.length = 0;
  }
  return {IntegrationData,IntegrationOnLoad,IntegratiOnShare,IntegratiOnUnload,IntegrationOnShow}
}
const integrationInterceptor = {
  // 监听请求之前
  invoke(args){
    stratagemInterceptor.invoke(args)
    if(!args.url) return
    // 判定当前是否是哈希表中的api接口 如果是那么读取请求参数获取id
    let optionsMap = isPathInInterfaceTable(args.url);
    if(optionsMap[0]?.getId !== 'invoke') return true
    if(optionsMap.length > 1){
      // 判断是否存在判定函数 如果有 那么说明这条接口存在多种情况调用 需要分情况确定任务 在这里给定标识符currentApi
      optionsMap.map(e=>e.judging && (e.currentApi = e.judging(args)))
    }else{
      optionsMap[0].id = args.params[optionsMap[0].idKey]
    }
    return true
  },
  // 监听请求成功后
  async success(args){
    if(args.config.header?.recordType != 1 && args.config.url?.includes('/refuterumorrecord/insert')) return 
    stratagemInterceptor.success(args)
      const accountId = common.getKeyVal('user', 'accountId', true);
      if(!accountId || timeerMap.length > 0) return
        let optionsMap = isPathInInterfaceTable(args.config.url)
        let argsConfig = (args.config?.data && Object.keys(args.config?.data).length > 0) && JSON.parse(args.config?.data)?.businessType
        if(args.config.url?.includes('/refuterumorrecord/insert')){
          optionsMap = optionsMap.length && optionsMap.filter(item=>(item.businessType === argsConfig))
        }
        let options;
        // 判定是否有多个配置共享一条接口 如果有就对其对应的路由进行检测
        if(optionsMap.length > 1){
          let currentPath = getCurrentPath()
          optionsMap.map(e=>{
            if(currentPath.indexOf(e.route)>0) options = e;
            // 判断是否存在判定函数 如果有 那么说明这条接口存在多种情况调用 需要分情况确定任务 在这里给定标识符currentApi
            e.judging && (e.currentApi = e.judging(args.data))
            if(e.currentApi) options = e
            e.currentApi = false
          })
        }else{
          options = optionsMap[0]
        }
        // console.log('optionsMap',optionsMap,args.config.url);
        // console.log('options2222',options);
        if(!options) return
        // 判定当前的任务是否正在处理中
        if(options.isRequest) return
        options.taskList = taskList.filter(task=>task.eventType === options.eventType && !task.finish)
        // console.log('当前任务队列',JSON.parse(JSON.stringify(options.taskList)));
        // 判断当前任务是否为删除任务 如果是则无需判定当前是否有对应任务
        if(options.eventType < 0){
          options.taskList = [{businessType:-1,eventType:options.eventType,accountId}]
          // 通知任务完成
          await pointtasklogHandle(options,{relateId:options.id},IntegrationData)
          options.isRequest = false;
          return
        }
        // 当前若无任务则退出监听
        if(options.taskList.length === 0) return options.isRequest = false;
        options.isRequest = true;
        if(options.getId === 'success' || options.successGetId){
          if(typeof args?.data?.data === 'string'){
            options.id = args?.data?.data
          }else{
            options.id = args?.data?.data?.[options.idKey]
          }
          // 如果当前是帖子类的任务 获取存储在变量里的参数
          if(options.isInvitation){
            const {source:articleWriterType,vType:articleVType,id:articleAccount,userId:articleUserId} = args?.data?.data;
            options.invitationOptions = {articleWriterType,articleVType,articleAccount,articleUserId}
          }
        }
        if(options.eventType == 12 || options.eventType == 13){
          // console.log('args?.data?.data',args?.data?.data);
          let argsData = Array.isArray(args?.data?.data) ? args?.data?.data[0] : args?.data?.data
          // console.log('argsData',argsData);
          let assignTaskMap = options.taskList.filter(task=>{
            task.classifyId = argsData.classifyId
            // 判定当前任务是否指定了栏目 如果当前任务指定的栏目和本次浏览的直播或视频的栏目一致 那么就返回
            if(task.videoColumn && task.videoColumn == task.classifyId){
              return true
            }
          })
          // 如果本次浏览的直播或视频和任务对应上了 那么就直接清空掉其他的任务
          if(assignTaskMap.length>0) options.taskList = assignTaskMap
          // 否则说明当前浏览的直播或视频不存在对应任务那么就开启非指定任务
          else options.taskList = options.taskList.filter(task=>!task.videoColumn)
        }
        // console.log('options.taskList2',options.taskList);
        if(options.taskList.length === 0) return
        // 定义任务时间
        let taskTimes = 0;
        IntegrationData.integrationNum = 0;
        IntegrationData.integrationRuntimeNum = 0;
        // 如果有时间那么就选时间最长的那个任务的时间
          options.taskList.map(task=>{
            IntegrationData.integrationNum+=task.point
            if(task.videoSetup || task.articleSetup){
              let integrationRuntimeNum = IntegrationData.integrationRuntimeNum
              let timeKeys = task.videoSetup && 'videoTime' || task.articleSetup && 'articleTime'
              if(task[timeKeys]>integrationRuntimeNum) taskTimes = task[timeKeys]
            }
          })
          // console.log('taskTimes',taskTimes);
          IntegrationData.integrationRuntimeNum = taskTimes
          // 判定当前任务是否存在任务时间
          if(taskTimes === 0){
            let queryOptions = {relateId:options.id,visitSecond:taskTimes};
            // 如果当前是帖子类的任务 获取存储在变量里的参数
            if(options.isInvitation){
              queryOptions = {...queryOptions,...options.invitationOptions}
            }
            // 通知任务完成
            await pointtasklogHandle(options,queryOptions,IntegrationData)
            options.isRequest = false;
            return
          }
          // 定义定时器触发任务
          let timeer = setInterval(async ()=>{
            if(IntegrationData.integrationRuntimeNum <= 0){
              // console.log('完成任务');
              let queryOptions = {relateId:options.id,visitSecond:taskTimes};
              // 如果当前是帖子类的任务 获取存储在变量里的参数
              if(options.isInvitation){
                queryOptions = {...queryOptions,...options.invitationOptions}
              }
              // 通知任务完成
              await pointtasklogHandle(options,queryOptions,IntegrationData)
              options.isRequest = false;
              clearInterval(timeer)
              timeer = null
              return
            }
            // console.log('IntegrationData.integrationRuntimeNum',IntegrationData.integrationRuntimeNum);
            IntegrationData.integrationRuntimeNum-=0.5;
          },500)
          timeerMap.push(timeer)
  }
};
export default {createIntegrationBuried,integrationInterceptor}

