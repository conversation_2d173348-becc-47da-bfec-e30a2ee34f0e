<!--
*
*config  [Object] 参数设置
bdt: true,              上划线border-top
bdb: true,              下划线border-bottom
label: '下拉框',         左边name值
name: 'select',         字段名
placeholder: '请选择',   提示
required: false,        是否必填
array: [],              展示数组
disabled: false         是否禁用
-------------------------------------------

*returnFn    [function]     回调函数
*cData        [String]       (默认选中)key传入方式
*getData     [String]      做数据处理首次默认值watch执行监听
*
*字典选择器(已经放到全局使用) <dictionary-selector  :config="xxx" :cData="xxx"  @returnFn = "xxx"></dictionary-selector>
 -->
<template>
  <view class="title-select-address" :class="{'bdt':defaultConfig.bdt,'bdb':defaultConfig.bdb}">
    <view class="l-t" :style="{'color': defaultConfig.titleColor}"><em class="star" v-if="defaultConfig.required">*</em>{{defaultConfig.label}}</view>
    <view class="l-r" :style="{'color': (dataValue && dataValue.length > 0) ? defaultConfig.textColor : '#A5AAB8'}" :class="{'color-999': disabled}" @tap="show()">
      {{defaultConfig.placeholder}}
    </view>
    <em class="jump" @tap="show()"></em>
    <select-address ref="selectAddress" :disabled="disabled" :config="configObj" :cData="dataValue" @updateForm="updateForm"></select-address>
  </view>
</template>

<script>
  import SelectAddress from './select-address'
  export default {
    components: {
      SelectAddress
    },
    data() {
      return {
        // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
        $constant: this.$constant,
        $common: this.$common,
        $accurateConversion: this.$accurateConversion,
        file_ctx: this.file_ctx,
        $static_ctx: this.$static_ctx,
        $timePlugin: this.$timePlugin,
        $validate: this.$validate,
        dataValue: [], // 当前页面双向数据
        dataValueList: [], // 当前选中的数据
        array: [],
        defaultConfig: {
          bdt: false,
          bdb: true,
          titleColor: '#1D2029',
          textColor: '#4E5569',
          label: '地区',
          name: 'select',
          placeholder: '请选择',
          required: false,
          array: [],
          length: 3, // 选择地区的长度，1：省、2：省市、3：省市区、4省市区街道
          isRequired: true // 是否必选全都选择才能触发确认
        },
        configObj: {
          length: 3, // 选择地区的长度，1：省、2：省市、3：省市区、4省市区街道
          isRequired: true // 是否必选全都选择才能触发确认
        }
      }
    },
    watch: {
      cData: {
        handler(val) {
          this.watchDataMain(val)
        },
        immediate: true,
        deep: true
      },
      config: {
        handler(val) {
          this.copyConfig()
        },
        immediate: true,
        deep: true
      }
    },
    props: {
      // 是否禁用disable
      disabled: {
        type: Boolean,
        default() {
          return false
        }
      },
      // 初始值传值，用于回显
      cData: {
        type: Array,
        required: false,
        default() {
          return []
        }
      },
      // 参数设置
      config: {
        type: Object,
        required: false,
        default: () => {
          return {}
        }
      },
      // 数组下标
      arrayIndex: {
        type: [String, Number],
        default() {
          return ''
        }
      }
    },
    computed: {

    },
    mounted() {
      this.init()
    },
    methods: {
      // 初始化数据
      init() {
        this.dataValue = JSON.parse(JSON.stringify(this.cData))
        this.copyConfig()
      },
      // 开启选择界面
      show() {
        if (this.disabled) return
        this.$refs['selectAddress'].show()
      },
      // 关闭选择界面
      close() {
        this.$refs['selectAddress'].close()
      },
      /**
       * 初始化拷贝config对象
       */
      copyConfig() {
        const that = this
        that.dataValue = JSON.parse(JSON.stringify(that.cData))
        const obj = JSON.parse(JSON.stringify(that.config))
        Object.keys(obj).forEach(function(key) {
          that.defaultConfig[key] = obj[key]
          if (key === 'length' || key === 'isRequired') {
            that.configObj[key] = obj[key]
          }
        })
        that.configObj = Object.assign({}, that.configObj)
        that.defaultConfig = Object.assign({}, that.defaultConfig)
        if (!that.$validate.isNull(that.dataValue)) {
          that.watchDataMain(that.dataValue)
        }
      },
      // 显示字段回显
      placeholderFn(data) {
        const that = this
        if (!that.$validate.isNull(data)) {
          that.defaultConfig.placeholder = ''
          for (let i = 0; i < data.length; i++) {
            that.defaultConfig.placeholder += data[i].text + ' '
          }
        } else {
          that.defaultConfig.placeholder = '请选择'
        }

      },
      /**
       * 监听cData主逻辑方法
       */
      watchDataMain(val) {
        const that = this
        that.dataValue = JSON.parse(JSON.stringify(val))
        that.defaultConfig = Object.assign({}, that.defaultConfig)
        that.$nextTick(() => {
          const data = that.$refs['selectAddress'].returnDataFn()
          that.placeholderFn(data)
        })
      },
      updateForm(obj) {
        this.returnFn(obj)
      },
      /**
       * picker触发选中事件
       * @param e
       */
      returnFn(e) {
        const that = this
        const obj = e
        that.dataValue = obj.value
        that.placeholderFn(obj.data)
        const returnVal = {
          key: that.config.name,
          value: that.dataValue
        }
        if (String(that.arrayIndex)) {
          returnVal.index = that.arrayIndex
        }
        that.defaultConfig = Object.assign({}, that.defaultConfig)
        that.$emit('updateForm', returnVal)
      },
      getLocationNameData() {
        return this.$refs['selectAddress'].returnDataFn()
      }
    }
  }
</script>

<style lang="scss" scoped>
  .color-999{
    color: #999 !important;
  }
  .title-select-address{
    height: 88upx;
    overflow: hidden;
    background-color:#ffffff;
    box-sizing: border-box;
    .l-t{
      display: inline-block;
      vertical-align: middle;
      line-height: 88upx;
      color: #333333;
      font-size: 32upx;
      width: 240upx;
      @include ellipsis(1);
    }
    .l-r{
      width: calc(100% - 274upx);
      display: inline-block;
      vertical-align: middle;
      font-size: 32upx;
      color: #000000;
      line-height: 88upx;
      text-align: right;
      @include ellipsis(1);
    }
    .jump{
      display: inline-block;
      vertical-align: middle;
      @include iconImg(34,34,'/business/icon-gengduo.png');
    }
  }
  .star{
    color: #FF5500;
    padding-top: 6upx;
    font-size: 32upx;
    display: inline-block;
  }
</style>
