<template>
  <page>
    <view slot="content" class="body-main">
      <!-- <view class="look-more"> -->
        <!-- <view class="my-header" :style="{'height':'176rpx','background-size': '100%','background-image':'url('+ file_ctx +'static/image/business/pharmacy-cyclopedia/icon-header-nav-bg.png)'}">
          <view :style="'height:' + statusBarHeight + 'px;'"></view>
          <view class="top-nav">
            <view class="top-nav-l" @click="back"><image mode="aspectFit" :src="file_ctx + 'static/image/business/hulu-v2/icon-circle-left-arrows.png'" class="header-search-img"/></view>
            <view class="top-nav-r" @tap.stop="openShare"><image mode="aspectFit" :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/shareCodeBg.png'" class="header-search-img"/></view>
          </view>
        </view> -->
        <view class="my-data">
          <view class="my-bg">
            <view :style="'height:' + statusBarHeight + 'px;'"></view>
            <view class="top-nav">
              <view class="top-nav-l" @click="back"><image mode="aspectFit" :src="file_ctx + 'static/image/business/hulu-v2/icon-circle-left-arrows.png'" class="header-search-img"/></view>
              <view class="top-nav-r" @tap.stop="openShare"><image mode="aspectFit" :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/shareCodeBg.png'" class="header-search-img"/></view>
            </view>
            <view class="top-user">
              <view class="top-user-l">
                <view class="top-user-title" v-if="paramsObj.drugName">
                  <view class="text-l">{{ paramsObj.drugName }}<view class="img"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-trade-name-r.png'"></image></view></view>
                  <view class="text"> {{ paramsObj.commonName }}</view>
                </view>
                <view class="top-user-title" v-else>
                  <view class="text" style="font-weight: 600;font-size:32rpx;">{{ paramsObj.name }}</view>
                </view>
                <view class="top-user-company">{{ paramsObj.brandName }}</view>
                <view class="top-user-tag">
                  <view class="tag-item-standard" v-if="paramsObj.standard">{{ paramsObj.standard }}</view>
                  <template v-if="paramsObj.tags && paramsObj.tags.split(',').length > 1">
                    <view 
                      v-for="item in paramsObj.tags.split(',')"
                      :key="item"
                      class="tag-item"
                    >
                    {{ item }}
                    </view>
                  </template>
                  <view v-else-if="paramsObj.tags && paramsObj.tags.split(',').length == 1" class="tag-item">{{ paramsObj.tags }}</view>
                </view>
              </view>
              <view class="top-user-r">
                <view class="img">
                  <image :src="paramsObj.banner[0]"></image>
                </view>
              </view>
            </view>
            <view class="user-nav" v-if="topNavListFilter.length">
              <view class="user-nav-item" v-for="item in topNavListFilter" :key="item.id" @click="handleUserNav(item.id)">
                <view class="img"><image :src="item.url"></image></view>
                <view class="text">{{ item.name }}</view>
              </view>
            </view>
            <view class="my-nav-list" :style="{margin:lookBrandIdList.includes(paramsObj.brandId)?'16rpx 32rpx 0':'0 32rpx'}">
              <view class="nav-list-item" v-for="item in navListFilter" :key="item.id" @click="handleNavList(item.id)">
                <view class="img"><image :src="item.url"></image></view>
                <view class="text">{{ item.name }}</view>
              </view>
            </view>
            <view class="my-electronic-book" @click="handleClickBook">
              <view class="book-header">
                <view class="book-header-l">
                  <view class="img"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-electronic-book-img.png'"></image></view>
                  <span>完整版电子说明书</span>
                </view>
                <view class="book-header-r"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-specification-content-right.png'"></image></view>
              </view>
              <view class="book-bottom">{{ bookTips }}</view>
            </view>

            <view class="my-electronic-notice" @click="handleGuard" v-if="guardObj">
              <view class="notice-header">
                <view class="book-header-l">
                  <view class="img"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-electronic-book-img.png'"></image></view>
                  <span>用药注意事项</span>
                </view>
                <view class="book-header-r"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-specification-content-right.png'"></image></view>
              </view>
            </view>
          </view>
        </view>
        <view class="look-more-content">
          <!-- 用药小贴士 -->
          <view class="pharmacy-tips">
            <view class="img"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-pharmacy-tips-look.png'"></image></view>
            <span>{{pharmacyTips}}</span>
          </view>

          <!-- 标签栏 -->
          <view class="content-header">
            <tabs-sticky class="my-tabs-sticky" :fontBigger="true" :bdb="false" :overflowX="true" v-model="curIndex" :tabs="paramsObj.tipsList" @change="changeTab"></tabs-sticky>
          </view>

          <!-- 内容区域 -->
          <scroll-view class="my-scroll" :style="{'height':`calc(100vh - ${(myDataHeight + headerHeight + pharmacyTipsHeight + 32)}px)`}" :scroll-y="true" :scroll-into-view="scrollIntoView" :show-scrollbar="false">
          <!-- <scroll-view class="my-scroll" :style="{'height':`calc(100vh - ${((tipsListLength) + 32)}px)`}" :scroll-y="true" :scroll-into-view="scrollIntoView" :show-scrollbar="false"> -->
          <!-- <scroll-view class="my-scroll" :style="{'height':`calc(${scrollHeight})px})`}" :scroll-y="true" :scroll-into-view="scrollIntoView" :show-scrollbar="false"> -->
          <!-- <scroll-view class="my-scroll" style="height:100vh;" :scroll-y="true" :scroll-into-view="scrollIntoView" :show-scrollbar="false"> -->
            <!-- <view class="tab-content"> -->
              <view :id="`tab-${index}`" class="tab-content-item" v-for="(item, index) in paramsObj.tipsList" :key="index">
                <view class="title">{{ item.name }}</view>
                <!-- <view class="value">{{ item.value }}</view> -->
                <rich-text class="value" @click="onitemclick(item.value)" :nodes="item.value" :style="{whiteSpace: 'pre-wrap'}"></rich-text>
              </view>
              <view class="look-bott">本网页服务由绿葆提供，相关内容仅供参考<br>不能替代执业医师或药师的意见，请谨慎参阅</view>
            <!-- </view> -->
          </scroll-view>
        </view>
        
      <!-- </view> -->

      <canvas canvas-id="answerCanvas" class="answerCanvas" :style="{'position':'absolute','top':'-99999px','width':`${canvasWidth}`+'px','height':`${canvasHeight}`+'px'}"></canvas>

      <uni-popup class="show-code-popup" ref="showCodePopup" id="showCodePopup" type="center">
        <!-- <image class="show-code" :src="sharePic" mode="widthFix" :show-menu-by-longpress="true"></image> -->
        <view class="show-code-img">
          <image class="show-code" :src="sharePic" :show-menu-by-longpress="true"></image>
          <view class="img" @click="handleClickShowCodeClose"><uni-icons :size="30" color="#fff" type="close" /></view>
        </view>
        <button type="primary" class="circle-attention-btn" @tap="savePicture">保存海报图片</button>
      </uni-popup>

      <view class="share-content" id="answer-canvas" v-show="isShow">
        <view class="share draw_canvas" data-type="background-image" data-delay="1" :style="{'background-image':'url('+ file_ctx +'static/image/business/pharmacy-cyclopedia/shareBg.png)'}">
          <view class="header draw_canvas">
            <view class="header-l draw_canvas">
              <image 
                class="draw_canvas"
                data-type="radius-image" 
                data-delay="1"
                :data-url="file_ctx + headImgPath"
                :src="file_ctx + headImgPath">
              </image>
            </view>
            <view class="header-r draw_canvas">
              <view class="name draw_canvas" data-delay="1" data-type="text" :data-text="fansRecord.nickName">{{ fansRecord.nickName }}</view>
              <view class="info draw_canvas" data-delay="1" data-type="text" data-text="给你分享了一个视频">给你分享了一个视频</view>
            </view>
          </view>
          <view class="conent draw_canvas" data-type="background-image" data-delay="1" :style="{'background-image':'url('+ file_ctx +'static/image/business/pharmacy-cyclopedia/share-code-bg.png)'}">
             <view class="box draw_canvas">
              <image 
                class="draw_canvas" 
                data-type="image" 
                data-delay="1"
                mode="aspectFit"
                :data-url="file_ctx + paramsObj.posterCover"
                :src="file_ctx + paramsObj.posterCover">
              </image>
            </view>
                <!-- mode="widthFix" -->
            
            <view class="title draw_canvas" data-delay="1" data-type="text" :data-text="publishInfo.name + (brandIdList.includes(paramsObj.brandId) ? '使用指南' : labelValue)">{{ publishInfo.name }}{{(brandIdList.includes(paramsObj.brandId) ? '使用指南' : labelValue)}}</view>
            <view class="code-info draw_canvas">
              <view class="code-l draw_canvas">
                <view class="name draw_canvas" data-delay="1" data-type="text" data-text="长按二维码进入">长按二维码进入</view>
                <view class="tags draw_canvas" data-delay="1" data-type="text" data-text="健康科普/经验分享">健康科普/经验分享</view>
              </view>
              <view class="code-r draw_canvas">
                <view class="img draw_canvas">
                  <image 
                    class="draw_canvas" 
                    data-type="image" 
                    data-delay="1"
                    :data-url="queryAndCreateObj.shareQrCode"
                    :src="queryAndCreateObj.shareQrCode">
                  </image>
                </view>
                <!-- <view class="finger draw_canvas">
                  <image 
                    class="draw_canvas" 
                    data-type="image" 
                    data-delay="1"
                    :data-url="file_ctx +'static/image/business/pharmacy-cyclopedia/finger2.png'"
                    :src="file_ctx +'static/image/business/pharmacy-cyclopedia/finger2.png'">
                  </image>
                </view> -->
              </view>
            </view>
          </view>
        </view>      
      </view>

      <!-- 用药警戒弹窗 -->
      <uni-popup ref="guardPopup" id="guardPopup" type="bottom">
        <view class="guard-content">
          <view class="header-btn" @click="handleClickGuardClose"><uni-icons :size="16" style="font-weight: bold;" color="#999" type="bottom" /></view>
          <view class="matters" v-if="guardObj.precautions">
            <view class="title">注意事项</view>
            <!-- <rich-text class="text-one" :nodes="guardObj.precautions" :style="{whiteSpace: 'pre-wrap'}"></rich-text> -->
            <rich-text class="text-one" :nodes="guardObj.precautions"></rich-text>
            <view class="matters-bottom" @click="handleClickMatters(1)">
              <view class="matters-bottom-l">
                <view class="bottom-l-img"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-pharmacy-remind-earth.png'"></image></view>
                <view class="bottom-l-text">查看全文</view>
              </view>
              <view class="matters-bottom-r"><uni-icons :size="16" style="font-weight: bold;" color="#999" type="right" /></view>
            </view>
          </view>
          <view class="apply-method" v-if="guardObj.medicationGuide">
            <view class="title">上药方法</view>
            <rich-text class="text-one" :nodes="guardObj.medicationGuide"></rich-text>
            <view class="matters-bottom" @click="handleClickMatters(2)">
              <view class="matters-bottom-l">
                <view class="bottom-l-img"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-pharmacy-remind-earth.png'"></image></view>
                <view class="bottom-l-text">查看全文</view>
              </view>
              <view class="matters-bottom-r"><uni-icons :size="16" style="font-weight: bold;" color="#999" type="right" /></view>
            </view>
          </view>
          <view class="apply-video" v-if="guardObj.medicationGuideVideo">
            <view class="title">上药视频</view>
            <video 
              v-if="videoShow"
              class="my-video"
              id="myVideo"
              :src="guardObj.medicationGuideVideo"
              play-btn-position="center"
              :show-center-play-btn="true"
              :loop="true"
              object-fit="cover"
              :poster="guardObj.medicationGuideVideoCover"
              :controls="true"
              >
              <!-- @click="togglePlayButton" -->
              <!-- :controls="false" -->
              </video>
              <!-- <view class="custom-play-btn" @click="toggleVideo" v-if="isPlaying">  
                <image class="play-icon" :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/play.png'"></image>  
              </view>   -->
          </view>
        </view>
      </uni-popup>

      <!-- 用药警戒弹窗 -->
      <uni-popup ref="guardDetailPopup" id="guardDetailPopup" type="bottom">
        <view class="guard-detail-content">
          <view class="header-btn" @click="handleClickGuardDetailClose"><uni-icons :size="16" style="font-weight: bold;" color="#999" type="bottom" /></view>
            <view class="matters" v-if="guardType == 1 && guardObj.precautions">
              <view class="title">注意事项</view>
              <rich-text class="text-rich" :nodes="guardObj.precautions" @click ="itemclick(guardObj.precautions)" preview :style="{lineHeight:'52rpx',letterSpacing:'2rpx',whiteSpace: 'pre-wrap'}"></rich-text>
            </view>
            <view class="apply-method" v-if="guardType == 2 && guardObj.medicationGuide">
              <view class="title">上药方法</view>
              <rich-text class="text-rich" :nodes="guardObj.medicationGuide" @click ="itemclick(guardObj.medicationGuide)" preview :style="{lineHeight:'52rpx',letterSpacing:'2rpx',whiteSpace: 'pre-wrap'}"></rich-text>
            </view>
        </view>
      </uni-popup>

    </view>
  </page>
</template>

<script>
  import { mapState } from 'vuex'
  import Wxml2Canvas from 'wxml2canvas';
  import uniPopup from '@/components/uni/uni-popup'
  import UniIcons from '@/components/uni/uni-icons/uni-icons'
  import TabsSticky from '@/components/basics/tabs-sticky-v3'
  import { isDomainUrl } from '@/utils/index.js'
  export default {
    components: {
      uniPopup,
      TabsSticky,
      UniIcons,
    },
    data(){
      return{
        file_ctx: this.file_ctx,
        paramsObj:{},
        channelCode:'',
        indicatorDots: true,
        autoplay: true,
        interval: 2000,
        duration: 500,
        canvasWidth:null,
        canvasHeight:null,
        isShow:false,
        sharePic:'',
        statusBarHeight: 0,
        topNavList:[
          {id:1,name:'病友分享',url:this.file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-pharmacy-wechat.png'},
          {id:2,name:'药师问答',url:this.file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-pharmacy-answers.png'},
          {id:3,name:'用药提醒',url:this.file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-pharmacy-remind.png'},
        ],
        navList:[
          {id:1,name:'健康科普',url:this.file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-pharmacy-coupe.png'},
          {id:2,name:'名医直播',url:this.file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-pharmacy-live.png'},
          {id:3,name:'附近药店',url:this.file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-pharmacy-drugstore.png'},
          {id:4,name:'企业介绍',url:this.file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-pharmacy-enterprise.png'},
          {id:5,name:'小黄车',url:this.file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-look-small-cart.png'},
        ],
        topNavListFilter:[],//过滤后的上部分导航
        navListFilter:[],//过滤后的下部分导航
        scrollIntoView:'tab-0',
        curIndex: 0,
        myDataHeight:null,
        headerHeight:null,
        scrollHeight:null,
        pharmacyTipsHeight:null,
        specificationList:[],
        jsonData:null,
        pharmacyTips:'用药小贴士',
        bookTips:'不同规格、不同生产日期/批次的药品说明书可能因说明书更新和版本更替等原因，内容有所不同。如本页面显示的电子说明书和您所购买的药物药盒中所附的纸质说明书存在内容有所不同的情况，请以药盒中所附的纸质说明书为准。',
        guardObj:null, //用药警戒数据
        videoShow:false,
        guardType:null,
        queryAndCreateObj:null,
        brandIdList:['2109655889818025985','2148011574305714178','2148010541462134786','2148006452183506947'], //针对部分企业做调整
        lookBrandIdList:['2148011574305714178','2148010541462134786','2148006452183506947'], //针对部分企业隐藏配置
      }
    },
    computed: {
      ...mapState('user', {
        fansRecord: state => state.fansRecord,
        publishInfo:state => state.publishInfo,
        curSelectUserInfo: state => state.curSelectUserInfo, // 当前选中的用户信息
        accountId: state => state.accountId,
      }),
      headImgPath(){
        return this.fansRecord.headPath ? this.fansRecord.headPath :  'static/image/system/avatar/icon-default-avatar.png'
      },
      labelValue() {
        let value = '用药指南'
        // if(this.paramsObj.id == '2031448999104024578' || this.paramsObj.id == '2031353194431111174'){
        if(this.paramsObj?.drugType == 2){
          value = '服用指南'
        }
        return value 
      },
    },
    onLoad(option){
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
      const query = this.$validate.isNull(this.$Route.query) ? option : this.$Route.query
      let systemInfo = uni.getSystemInfoSync();
      this.scrollHeight = systemInfo.windowHeight;
      this.channelCode = query.gs 
      this.getChannelQueryOne({code:this.channelCode})    

      // uni.setNavigationBarTitle({
      //   title: this.paramsObj.name
      // });
      if(query?.id){
        this.productmedicationguideQueryOne(query?.id) 
      }
    },
    onShareAppMessage: function (res) {  
      if (res.from === 'share') {  
        // 来自页面内转发按钮  
        console.log(res.target);  
      }
      this.queryAndCreate(3)
      return {
        title: `${this.publishInfo.name}${(this.brandIdList.includes(this.paramsObj.brandId) ? '使用指南' : this.labelValue)}`, //分享的名称
        path: 'modules/pharmacy/pharmacy-cyclopedia/index?gs='+ encodeURIComponent(this.channelCode),
        mpId:this.$appId, //此处配置微信小程序的AppId
      }
    },
    mounted(){
      this.getElementHeight()
    },
    methods:{
      // #ifdef MP-WEIXIN
      handleClickTrack(type,btnType){
        getApp().globalData.sensors.track("PopupClick",
          {
            'page_name' : '药品说明书查看更多页面',
            'popup_id' : type == 1 ? 'guardPopup' : type == 2 ? 'guardDetailPopup' : 'showCodePopup',
            'popup_name' : type == 1 ? '用药警戒外层弹窗' : type == 2 ? '用药警戒内层弹窗' : '分享海报弹窗',
            'click_type' : btnType == 1 ? '进入弹窗' : '取消弹窗',
          }
        ) 
      },
      // #endif
      handleClickShowCodeClose(){
        this.$refs.showCodePopup.close()
        // #ifdef MP-WEIXIN
        this.handleClickTrack(3,2)
        // #endif
      },
      handleClickGuardClose(){
        this.$refs.guardPopup.close()
        // #ifdef MP-WEIXIN
        this.handleClickTrack(1,2)
        // #endif
        this.videoShow=false
      },
      handleClickGuardDetailClose(){
        this.$refs.guardDetailPopup.close()
        // #ifdef MP-WEIXIN
        this.handleClickTrack(2,2)
        // #endif
      },
      // 小黄车跳转
      handleClickYellowCart({path,appId}) {
        this.$uniPlugin.navigateToMiniProgram({
          appId,
          path,
          envVersion: 'release',
          extraData: {}
        }, (res) => {
          resolve(true)
          console.log(res)
        }, (err) => {
          resolve(false)
        })
      },

      handleClickMatters(type){
        this.guardType = type
        this.$refs.guardDetailPopup.open()
        // #ifdef MP-WEIXIN
        this.handleClickTrack(2,1)
        // #endif
      },

      // 用药警戒
      handleGuard(){
        this.videoShow = true
        this.$nextTick(()=>{
          this.$refs.guardPopup.open()       
          // #ifdef MP-WEIXIN
          this.handleClickTrack(1,1)
          // #endif
        })
      },
      
      onitemclick(text){ 
        const regex = /<a[^>]*\shref="([^"]+)"[^>]*>([^<]+)<\/a>/gi;
        let match;
        let linksInfo = [];
        while ((match = regex.exec(text)) !== null) {
          // match[1] 是href属性，match[2] 是<a>标签内的文本
          linksInfo.push({
            href: match[1],
            text: match[2]
          });
        }
        this.$navto.push('WebHtmlView', { src: linksInfo[0].href })
      },
      // 点击查看完整说明书
      handleClickBook(){
       if(this.specificationList?.length == 1){
          // this.$navto.push('ElectronicBook',{drugName:this.paramsObj.drugName,commonName:this.paramsObj.commonName,brandName:this.paramsObj.brandName,productId:this.paramsObj.id})
          this.$navto.push('ElectronicDetail',{id:this.specificationList[0].id,drugName:this.paramsObj.drugName,commonName:this.paramsObj.commonName,brandName:this.paramsObj.brandName,productId:this.paramsObj.id,labelValue:this.labelValue,gs:this.channelCode})
        } else {
          this.$navto.push('ElectronicBook',{drugName:this.paramsObj.drugName,commonName:this.paramsObj.commonName,brandName:this.paramsObj.brandName,productId:this.paramsObj.id,bookTips:this.bookTips,labelValue:this.labelValue,gs:this.channelCode})
        }
      },

      productmedicationguideQueryOne(productId){
        this.$api.drugBook.productmedicationguideQueryOne({productId}).then(res=>{
          if(res.data !=="" && res.data.precautions !==""){
            this.guardObj = {...res.data,medicationGuideVideo:isDomainUrl(res.data.medicationGuideVideo),medicationGuideVideoCover:isDomainUrl(res.data.medicationGuideVideoCover)}
          }
        })
      },

      getElementHeight() {
        let query = uni.createSelectorQuery().in(this);
        query.select('.my-data').boundingClientRect(data => {
          if (data) {
            this.myDataHeight = data.height
          }
        }).exec();
    
        query.select('.content-header').boundingClientRect(data => {
          if (data) {
            this.headerHeight = data.height
          }
        }).exec();

        query.select('.pharmacy-tips').boundingClientRect(data => {
          if (data) {
            this.pharmacyTipsHeight = data.height
          }
        }).exec();
      },

      back(){
        this.$navto.back(1)
      },

      changeTab(index) {
        this.curIndex = index
        this.scrollIntoView = `tab-${index}`;
      },

      //病友分享、药师问答、用药提醒
      handleUserNav(id){
        switch(id){
          case 1:
            this.$navto.push('PharmacyEvaluate',{entryType:2,id:this.paramsObj.id,title:this.jsonData?.shareName || '病友分享',evaluateImg:this.jsonData?.shareImg,labelValue:this.labelValue,gs:this.channelCode})
          break;
          case 2:
            this.$navto.push('DoctorQuestion',{id:this.paramsObj.id,title:this.jsonData?.questionsName || '药师问答',labelValue:this.labelValue,gs:this.channelCode})
          break;
          case 3:
            this.$navto.push('PharmacyRemind',{productId:this.paramsObj.id,productName:this.paramsObj.name,gs:this.channelCode,drugType:this.paramsObj.drugType,title:this.jsonData?.pharmacyRemindName || '用药提醒'})
          break;
        }
      },

      //健康科普、名医直播、附近药店、企业介绍、小黄车
      handleNavList(id){
        switch(id){
          case 1:
            this.$navto.push('PharmacyEvaluate',{entryType:3,id:this.paramsObj.id,title:this.jsonData?.coupeName || '健康科普',labelValue:this.labelValue,gs:this.channelCode})
          break;
          case 2:
            if(this.paramsObj.externalLinksSwitch == 1){
              this.$navto.push('WebHtmlView', { src: this.paramsObj.externalLinks })
            } else {
              this.$navto.push('directseedingVideoListIndex',{name:'名医直播间',productId:this.paramsObj.id,businessType:8,title:this.jsonData?.liveName || '名医直播',searchVal:'搜索相关视频名称'})
            }
          break;
          case 3:
            this.$navto.push('Drugstore',{bindingId:this.paramsObj.id,brandId:this.paramsObj.brandId,labelValue:this.labelValue,gs:this.channelCode,extProfiles:this.paramsObj?.extProfiles})
          break;
          case 4:
            this.$navto.push('Enterprise',{id:this.paramsObj.id,brandId:this.paramsObj.brandId,gs:this.channelCode,labelValue:this.labelValue,gs:this.channelCode})
          break;
          case 5:
            this.handleClickYellowCart({path:this.paramsObj.appPath,appId:this.paramsObj.appId})
          break;
        }
      },

      // 说明书接口列表
      getfullSpecificationQueryPage(){
        let params = {
          current: 1,
          size: 15,
          condition:{
            productId:this.paramsObj.id
          },
        }
        this.$api.drugBook.getfullSpecificationQueryPage(params).then(res => {
          let data = res.data.records.map(item=>{
            return {
              ...item,
              specificationUpdateTime:item.specificationUpdateTime ? this.$common.formatDate(new Date(item.specificationUpdateTime), 'yyyy-MM-dd') : ''
            }
          })
          this.specificationList = data


        })
      },

      // 保存图片
      savePicture(){
        uni.saveImageToPhotosAlbum({
          filePath: this.sharePic,
          success: (res) => {
            this.queryAndCreate(2)
            uni.showToast({
              title: '保存成功',
              icon: 'success'
            })
          },
        })
      },

      async openShare(){
        let res = await this.queryAndCreate(1)
        if(res.data != ""){
          this.isShow = true
          this.$nextTick(()=>{
            try {
              uni.createSelectorQuery().select('#answer-canvas').boundingClientRect().exec((res)=>{
                console.log(res,'res---')
                let { width,height } = res[0]
                this.canvasWidth = width
                this.canvasHeight = height
                this.draw(width,height)
              })
            } catch(error){
              console.log(error)
            }
          })
        }
      },

      draw(width,height){
        let that = this
        uni.showLoading({
          title:"加载中...",
          mask:true
        })
        let wxcanvas = new Wxml2Canvas({
          element: 'answerCanvas', // canvas节点的id,
          obj: that,
          width: width, // 宽 自定义
          height: height, // 高 自定义
          // background: 'transparent', // 默认背景色 设置背景色
          progress(percent) {},
          finish(url) {
            that.sharePic = url
            // console.log("创建的图片", url);
            uni.hideLoading()
            setTimeout(()=>{
              that.isShow = false
              that.$refs.showCodePopup.open()
              // #ifdef MP-WEIXIN
              that.handleClickTrack(3,1)
              // #endif
            },300)
          },
          error(res) {
            console.log(res);
            that.isShow = false
            uni.hideLoading()
            // 画失败的原因
          }
        })
        let data = {
          //直接获取wxml数据
          list: [{
              type: 'wxml',
              class: '.draw_canvas',  // answer_canvas这边为要绘制的wxml元素跟元素类名， answer_draw_canvas要绘制的元素的类名（所有要绘制的元素都要添加该类名）
              limit: '.share-content', // 这边为要绘制的wxml元素跟元素类名,最外面的元素
              x: 0,
              y: 0
            } ]
        }
          //传入数据，画制canvas图片
        wxcanvas.draw(data);
      },
      async queryAndCreate(type){
        let { phone = '' } =  this.curSelectUserInfo || {}
        let params = {
          accountId:this.accountId,
          phone:this.paramsObj.phone || phone,
          productId:this.paramsObj.id,
          shareType:type,  //	分享类型：1分享按钮2保存二维码3微信好友分享按钮
        }
        const res = await this.$api.drugBook.queryAndCreate(params)
        this.queryAndCreateObj = res.data
        return Promise.resolve(res)
      },
      clickImage(index){
        uni.previewImage({
          urls: this.paramsObj.banner, //需要预览的图片http链接列表，多张的时候，url直接写在后面就行了
          current: this.paramsObj.banner[index], // 当前显示图片的http链接，默认是第一个
          success: function(res) {},
          fail: function(res) {},
          complete: function(res) {},
        })
      },
      // 渠道连code单一查询
      getChannelQueryOne(params){
        this.$api.drugBook.getChannelQueryOneCode(params).then(res => {
          this.paramsObj = res.data
          this.paramsObj.qrCode = isDomainUrl(res.data.qrCode)
          this.paramsObj.videoPath = isDomainUrl(res.data.videoPath)
          this.paramsObj.logo = isDomainUrl(res.data.logo)
          this.paramsObj.banner = res.data.banner.split(',').map(item=>(isDomainUrl(item)))
          this.paramsObj.tips = Array.isArray(this.paramsObj.tips) ? this.paramsObj.tips : (this.paramsObj.tips && JSON.parse(this.paramsObj.tips)) || ''
          if(this.paramsObj.otherProfile){
            this.jsonData = JSON.parse(this.paramsObj.otherProfile) 
          }
          this.pharmacyTips = this.jsonData?.tipsName || this.pharmacyTips
          this.bookTips = this.jsonData?.bookDescribeName || this.bookTips
          this.navListFilter = this.navList.filter(item=>{
            if((this.paramsObj?.docLiveSwitch == 2 && item.name == '名医直播')){
              return false
            } else if((this.paramsObj?.pharmacySwitch == 2 && item.name == '附近药店')){
              return false
            } else if((this.paramsObj?.appletSwitch == 2 && item.name == '小黄车')){
              return false
            } else if(item.name == '健康科普'){
              if(this.jsonData?.coupeName){
                item.name = this.jsonData.coupeName
              }
              return true
            } else if(item.name == '名医直播'){
              if(this.jsonData?.liveName){
                item.name = this.jsonData.liveName
              }
              return true
            } else if(this.paramsObj?.appletSwitch == 1 && item.name == '小黄车'){
              if(this.jsonData?.lookName){
                item.name = this.jsonData.lookName
              }
              return true
            } else {
              return true
            }
          })
          if(this.lookBrandIdList.includes(this.paramsObj.brandId)){
            this.topNavListFilter = []
          } else {
            this.topNavListFilter = this.topNavList.filter(item=>{
              if(item.name == '病友分享'){
                if(this.jsonData?.shareName){ 
                  item.name = this.jsonData.shareName
                } else {
                  item.name = '病友分享'
                }
                // 北京健平企业不显示
                if(this.paramsObj.brandId == '2109655889818025985'){
                  return false
                } else {
                  return true
                }
              } else if(item.name == '药师问答'){
                if(this.jsonData?.questionsName){
                  item.name = this.jsonData.questionsName
                } else {
                  item.name = '药师问答'
                }
                // 北京健平企业不显示
                if(this.paramsObj.brandId == '2109655889818025985'){
                  return false
                } else {
                  return true
                }
              } else if(this.paramsObj?.medicineRemindSwitch == 1 && item.name == '用药提醒'){
                item.name = this.jsonData?.pharmacyRemindName || '用药提醒'
                return true
              } else {
                return false
              }
            })
          }
          this.paramsObj.tipsList = []
          if(this.paramsObj.tips.length){
            for(let i = 0; i < this.paramsObj.tips.length; i++){
              let obj = this.paramsObj.tips[i]
              let keys = Object.keys(obj)
              let key = keys[0]
              let value = obj[key]
              let objFlag = {
                name:key,
                value:value
              }
              this.paramsObj.tipsList.push(objFlag)
            }
          }
          this.getfullSpecificationQueryPage() //调用说明书列表 
        })
      },
    },
 }
</script>

<style lang='scss' scoped>
.body-main{
  position: relative;
  height: 100%;
  background: #DFF1ED;
  .my-data{
    position: relative;
    .my-bg {
      width: 100%;
      // height: 498upx;
      padding-bottom: 40rpx;
      background: linear-gradient( 180deg, #B5E7D9 0%, #DFF1ED 100%);
      .top-nav{
        position: fixed;
        width: calc(100% - 56rpx);
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 40px;
        line-height: 40px;
        padding: 0 32rpx 0 24rpx;
        .top-nav-l{
          display: flex;
          width: 48rpx;
          height: 48rpx;
          image{
            width: 100%;
            height: 100%;
          }
        }
        .top-nav-r{
          display: flex;
          width: 64rpx;
          height: 64rpx;
          padding-right: 180rpx;
          // margin-top: 4rpx;
          image{
            width: 100%;
            height: 100%;
          }
        }
      }
      .top-user{
        display: flex;
        justify-content: space-between;
        margin-top: 65px;
        padding: 0 32rpx 0 40rpx;
        .top-user-l{
          .top-user-title{
            position: relative;
            display: flex;
            flex-wrap: wrap;
            padding-right: 5rpx;
            .text-l{
              display: flex;
              height: 60rpx;
              line-height: 60rpx;
              font-size: 44rpx;
              font-weight: 600;
              color: #2D2F38;
              .img{
                display: flex;
                width: 28rpx;
                height: 28rpx;
                margin-left: 5rpx;
                image{
                  width: 100%;
                  height: 100%;
                }
              }
            }
            .text{
              // height: 40rpx;
              font-weight: 400;
              // line-height: 40rpx;
              font-size: 28rpx;
              color: #2D2F38;
              margin-top: auto;
              margin-left: 2rpx;
            }
          }
          .top-user-company{
            font-size: 24rpx;
            color: #4E5569;
            margin: 8rpx 0 16rpx;
          }
          .top-user-tag{
            display: flex;
            width: 502rpx;
            flex-wrap: wrap;
            .tag-item,.tag-item-standard{
              height: 28rpx;
              line-height: 28rpx;
              font-size: 20rpx;
              padding:8rpx 12rpx;
              background: rgba(255,255,255,0.5);
              border-radius: 8rpx;
              // margin-right: 12rpx;
              margin:0 12rpx 12rpx 0;
              border: 1rpx solid #B8DDD3;
              color: #00926B;
              &:last-child{
                margin-right: 0;
              }
            }
            .tag-item-standard{
              color:#1D2029;
            }
          }
        }
        .top-user-r{
          .img{
            width: 160rpx;
            height: 160rpx;
            border-radius: 7rpx;
            overflow: hidden;
            background-color: #fff;
            image{
              width: 100%;
              height: 100%;
            }
          }
        }
      }
      .user-nav{
        display: flex;
        padding: 28rpx 32rpx 20rpx;
        .user-nav-item{
          display: flex;
          flex: 1;
          justify-content: center;
          padding:20rpx 25rpx;
          background: rgba(255,255,255,0.8);
          border-radius: 12rpx;
          border: 1rpx solid #FFFFFF;
          margin-right: 20rpx;
          .img{
            width: 40rpx;
            height: 40rpx;
            image{
              width: 100%;
              height: 100%;
            }
          }
          .text{
            height: 40rpx;
            font-size: 28rpx;
            color: #1D2029;
            line-height: 40rpx;
            margin-left: 8rpx;
          }
          &:last-child{
            margin-right: 0;
          }
        }
      }
      .my-nav-list{
        display: flex;
        justify-content:space-around;
        margin: 0 32rpx;
        padding: 32rpx 0;
        background: rgba(255,255,255,0.5);
        border-radius: 16rpx;
        border: 1rpx solid #FFFFFF;
        .nav-list-item{
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          .img{
            height: 64rpx;
            width: 64rpx;
            image{
              width: 100%;
              height: 100%;
            }
          }
          .text{
            height: 34rpx;
            font-size: 24rpx;
            color: #1D2029;
            line-height: 34rpx;
            margin-top: 12rpx;
          }
        }
      }
      .my-electronic-book,.my-electronic-notice{
        margin: 20rpx 32rpx 0;
        background: rgba(255,255,255,0.5);
        border-radius: 16rpx;
        border: 1rpx solid #FFFFFF;
        padding: 26rpx 24rpx 32rpx;
        .book-header,.notice-header{
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 16rpx;
          .book-header-l{
            display: flex;
            align-items: center;
            .img{
              display: flex;
              width: 40rpx;
              height: 40rpx;
              margin-right: 8rpx;
              image{
                width: 100%;
                height: 100%;
              }
            }
            span{
              display: inline-block;
              font-size: 32rpx;
              color: #1D2029;
              font-weight: 600;
              line-height: 44rpx;
            }
          }
          .book-header-r{
            display: flex;
            width: 28rpx;
            height: 28rpx;
            image{
              width: 100%;
              height: 100%;
            }
          }
        }
        .notice-header{
          margin-bottom: 0;
        }
        .book-bottom{
          font-size: 24rpx;
          color: #4E5569;
          line-height: 34rpx;
        }
      }
      .my-electronic-notice{
        padding: 15rpx 24rpx 15rpx;
      }
    }
  }
  .look-more-content{
    background: #FFFFFF;
    border-radius: 16rpx;
    overflow: hidden;
    padding-bottom: 64rpx;
    .pharmacy-tips{
      display: flex;
      align-items: center;
      padding:32rpx 24rpx 0;
      .img{
        display: flex;
        width: 44rpx;
        height: 44rpx;
        image{
          width: 100%;
          height: 100%;
        }
      }
      span{
        display: inline-block;
        font-weight: 600;
        font-size: 36rpx;
        color: #1D2029;
        line-height: 50rpx;
        margin-left: 8rpx;
      }
    }
    .content-header{
      // position: sticky;
      // top: 0;
      // z-index: 99;
      // padding:32rpx 0 0;
      /deep/.my-tabs-sticky{
        &::-webkit-scrollbar {
          display:none;
          width:0;
          height:0;
          color:transparent;
        }
        .tabs-sticky{
          padding:32rpx 0;
          .tabs-sticky-body{
            padding:0 32rpx
          }
        }
      }
    }
    // .tab-content{
    //   height: 100%;
    //   padding:0 32rpx 64rpx;
    //   .tab-content-item{
    //     margin-top: 64rpx;
    //     .title{
    //       height: 48rpx;
    //       line-height: 48rpx;
    //       font-weight: 600;
    //       font-size: 34rpx;
    //       color: #2D2F38;
    //       margin-bottom: 22rpx;
    //     }
    //     .value{
    //       font-size: 30rpx;
    //       color: #4E5569;
    //       line-height: 48rpx;
    //     }
    //   }
    // }
    .tab-content-item{
      padding: 0 32rpx;
      margin-top: 64rpx;
      .title{
        height: 48rpx;
        line-height: 48rpx;
        font-weight: 600;
        font-size: 34rpx;
        color: #2D2F38;
        margin-bottom: 22rpx;
      }
      .value{
        font-size: 30rpx;
        color: #4E5569;
        line-height: 48rpx;
      }
      &:first-child{
        margin-top: 32rpx;
      }
    }
    .look-bott{
      padding: 0 32rpx;
      text-align: center;
      color: #cfcfcf;
      margin-top: 30rpx;
    }
    
    /deep/.my-scroll ::-webkit-scrollbar {
      display:none;
      width:0;
      height:0;
      color:transparent;
    }
  }

}
.share-content{
  height: 832rpx;
  width: 606rpx;
  .share{
    height: 832rpx;
    width: 606rpx;
    padding:36upx 40upx 48upx;
    // box-sizing: border-box;
    z-index: 99;
    // border-radius: 10rpx;
    .header{
      display: flex;
      align-items: center;
      .header-l{
        width: 88rpx;
        height: 88rpx;
        border-radius: 50%;
        margin-right: 12upx;
        overflow: hidden;
        image{
          width: 100%;
          height: 100%;
        }
      }
      .header-r{
        margin-top: -8upx;
        .name{
          font-weight: 700;
          font-size: 32rpx;
          color: #1D2029;
          text-align: left;
        }
        .info{
          font-weight: 400;
          font-size: 24rpx;
          color: #4E5569;
          width: 260rpx;
        }
      }
    }
    .conent{
      position: relative;
      height: 632rpx;
      width: 526rpx;
      // background-color: #fff;
      // padding:26upx 28upx 0;
      padding:0 28upx;
      box-sizing: border-box;
      margin-top: 28upx;
      overflow: hidden;
      .box{
        // height: 400rpx;
        position: absolute;
        top: 20rpx;
        left:50%;
        transform: translateX(-50%);
        width: 382rpx;
        height: 268rpx;
        image{
          width: 100%;
          height: 100%;
        }
      }
      .title{
        position: absolute;
        width: calc(100% - 56rpx);
        top: 300rpx;
        z-index: 99;
        text-align: center; 
        font-weight: bold;
        font-size: 28rpx;
        color: #1D2029;
      }
      .code-info{
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 40rpx 20rpx 50rpx 0;
        .code-l{
          position: absolute;
          top: 460rpx;
          left: 48rpx;
          z-index: 99;
          display: flex;
          flex-direction: column;
          .name{
            height: 40rpx;
            width: 260rpx;
            // line-height: 40rpx;
            font-size: 28rpx;
            color: #1D2029;
            margin-bottom: 8upx;
          }
          .tags{
            height: 56rpx;
            width: 180rpx;
            line-height: 28rpx;
            font-weight: 400;
            font-size: 20rpx;
            color: #868C9C;
          }
        }
        .code-r{
          display: flex;
          .img{
            position: absolute;
            width: 176rpx;
            height: 176rpx;
            top: 420rpx;
            right: 48rpx;
            image{
              width: 100%;
              height: 100%;
            }
          }
          // .finger{
          //   position: absolute;
          //   top: 540rpx;
          //   right: 38rpx;
          //   width: 90rpx;
          //   height: 88rpx;
          //   image{
          //     width: 100%;
          //     height: 100%;
          //   }
          // }
        }
      }
      // .finger{
      //   position: absolute;
      //   bottom: -89.06rpx;
      //   right: 17.81rpx;
      //   .img{
      //       width: 150.13rpx;
      //       height: 163.49rpx;
      //     image{
      //       width: 100%;
      //       height: 100%;
      //     }
      //   }
      // }
    }
  }
}
.show-code-img{
  position: relative;
  height: 832rpx !important;
  width: 606rpx !important;
  // width: 100%;
  // height: 100%;
  border-radius: 25rpx;
  overflow: hidden;
  .show-code{
    width: 100%;
    height: 100%;
  }
  .img{
    position: absolute;
    right: 25rpx;
    top: 25rpx;
  }
}
.circle-attention-btn{
  height: 96rpx;
  line-height: 96rpx;
  margin-top: 40upx;
  font-weight: 600;
  border-radius: 48rpx;
  background: #fff;
  font-size: 32rpx;
  color: #00B484;
}

.guard-content,.guard-detail-content{
  height: 90vh;
  position: relative;
  background-color: #f5f7f7;
  padding:20rpx 30rpx;
  border-radius: 13rpx 13rpx 0 0;
  overflow-y: scroll;
  .header-btn{
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100rpx;
    height: 50rpx;
    background: #ebeded;
    border-radius: 25rpx;
    margin: 0 auto;
  }
  .matters,.apply-method,.apply-video{
    position: relative;
    padding: 20rpx;
    margin-top: 40rpx;
    border-radius: 16rpx;
    background-color: #fff;
    .title{
      font-weight: bold;
      color:#000;
      font-size: 34rpx;
      margin-bottom: 20rpx;
    }
    .text-one{
      line-height: 50rpx;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .text-rich{
    }
    .my-video{
      height: 180px;
      width: 100%;
      border-radius: 16rpx;
    }
    .custom-play-btn{
      position: absolute;  
      top: 55%;  
      left: 50%;  
      transform: translate(-50%, -55%);  
      width: 48rpx;
      height: 48rpx;
      // width: 96rpx;
      // height: 96rpx;
      // opacity: 0.7;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .matters-bottom{
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 20rpx;
      padding-top: 10rpx;
      border-top: 1rpx solid #f2f2f2;
      .matters-bottom-l{
        display: flex;
        align-items: center;
        .bottom-l-img{
          width: 32rpx;
          height: 32rpx;
          border-radius: 50%;
          image{
            width: 100%;
            height: 100%;
          }
        }
        .bottom-l-text{
          margin-left: 10rpx;
        }
      }
      .matters-bottom-r{

      }
    }
  }
  .apply-video{
    display: flex;
    flex-direction: column;
    flex: 1;
    margin-top: 0;
  }
  .apply-method{
    margin: 30rpx 0;
  }
}
.guard-content{
  display: flex;
  flex-direction: column;
}
</style>