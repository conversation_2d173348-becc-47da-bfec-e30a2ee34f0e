<template>
  <view class="wrapper">
    <view v-for="(item, index) in tabs" :key="index" class="tab-item" :class="{ 'active': value === item.value }" @tap="changeTab(index, item)">
      <text class="tab-name">
        {{ item.name }}
      </text>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    tabs: {
      type: Array,
      default: () => []
    },
    value: {
      type: Number,
      default: 0
    }
  },
  methods: {
    changeTab(index, item) {
      this.$emit('input', item.value)
      this.$emit('change', index)
    }
  }
}
</script>

<style lang="scss" scoped>
.wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.tab-item {
  &.active {
    .tab-name {
      font-weight: 500;
      font-size: 36rpx;
      color: $topicC;
      line-height: 50rpx;
    }
  }
  .tab-name {
    font-size: 32rpx;
    color: #4E5569;
    line-height: 44rpx;
  }
  &+.tab-item {
    margin-left: 32rpx;
  }
}
</style>