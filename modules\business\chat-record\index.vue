<template>
  <view>
    <page>
      <view slot="content">
        <message-list ref="messageList" />
      </view>
    </page>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import messageList from './components/message.vue'

export default {
  components: {
    messageList,
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      PointY: 0, //坐标位置
      timer: null
    }
  },
  // onReady() {
  //   uni.setNavigationBarTitle({
  //     title: this.chatItem.chatList.name
  //   });
  // },
  computed: {
    ...mapState('chat', {
      chatItem: state => state.chatItem,
      pageIsShow: state => state.pageIsShow,
      orderDetail: state => state.orderDetail
    }),
  },
  onHide () {
    this.$common.setKeyVal('chat', 'pageIsShow', false, false)
  },
  async onShow() {
    this.$common.setKeyVal('chat', 'pageIsShow', true, false)
    await this.getNodereplyconfigQueryConfigList()
  },
  methods: {
    async getNodereplyconfigQueryConfigList () {
      // nodeType	节点阶段类型: 1-问诊前,2-问诊后,3-离线
      this.$api.chat.nodereplyconfigQueryConfigList({}).then(res => {
        this.$common.setKeyVal('chat', 'nodereplyconfig', this.$validate.isNull(res.data) ? [] : res.data.sort((a,b) => a.weight-b.weight))
      })
    },
  },
  onPageScroll(e) {
    if (!this.pageIsShow) return
    // var timer
    // 退出和注销时候需要 将 window.onscroll =  null;
    // 滚动
    clearTimeout(this.timer) // 每次滚动前 清除一次

    this.timer = setTimeout(() => {
      if (e.scrollTop < 50) {
        this.$refs.messageList.joinData();
      }

    }, 500)
  }

}
</script>
<style lang="scss" scoped>
page {
  background-color: #f3f3f3;
}
</style>
