import request from '@/common/util/request'
import env from '@/config/env'

/**
 * 红包 请求工具类，
 * 注意：API接口只做请求数据和格式化数据的事情，其他事务一概不处理
 */
export default {
  // 请求二维码接口
  packetQrcode(param) {
    const url = env.ctx + 'manage/api/wx/packet/qrcode'
    return request.postForm(url, param)
  },
  // 待领取红包列表
  waitReceiveList(param) {
    const url = env.ctx + `manage/api/v1/redpacketlog/waitReceiveList/${param.openId}`
    return request.get(url, param)
  },
  // 领取红包更新状态
  receiveRedPacket(param) {
    const url = env.ctx + `manage/api/v1/redpacketlog/receiveRedPacket/${param.mchBillno}`
    return request.get(url, param)
  },
  // 获取领红包所需要的参数
  generateRequest(param) {
    const url = env.ctx + `manage/api/v1/redpacketlog/generateRequest`
    return request.postJson(url, param)
  },
  // 商家付款到零钱
  entpayorderEntpay(param) {
    const url = env.ctx + `manage/api/v1/entpayorder/entpay`
    return request.postJson(url, param)
  }
}
