<template>
  <view class="main">
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px', height: navBarHeight + 'px' }">
      <!-- 左侧返回按钮 -->
      <view class="navbar-left" @click="handleBack">
        <image mode="aspectFit" :src="file_ctx + 'static/image/business/hulu-v2/icon-circle-left-arrows.png'" class="header-search-img"/>
      </view>
      <view class="navbar-center">中国专家共识发布，红卡治疗...</view>
      <view class="navbar-right"></view>
    </view>
    <web-view class="my-web" :src="src"></web-view>
  </view>
</template>

<script>
export default {
  components: {
  },
  data() {
    return {
      file_ctx:this.file_ctx,
      statusBarHeight: 0, // 状态栏高度（如手机顶部信号栏）
      navBarHeight: 44,     // 导航栏内容高度（默认44px，与iOS标准一致）
      src: ''
    }
  },
  onLoad(paramsObj = {}) {
    const query = this.$Route.query
    if (!this.$validate.isNull(query)) {
      this.src = this.fullyDecodeURI(query.src)
      // console.log('query-------', query)
      uni.setNavigationBarTitle({
        title: this.fullyDecodeURI(query.title)
      })
    }
  },
  onNavigationBarButtonTap(e) {
    if (e.type == 'back') {
      // uni.switchTab({
      //   url: '/pages/greencenter/index'
      // })
      this.$navto.push('PharmacyCyclopedia',{gs:'J3iamm'})
      //点击菜单按钮执行的代码
    } else {
      //点击返回按钮执行的代码
    }
  },
  onShareAppMessage(res) {
    const that = this
    if (res.from === 'button') {// 来自页面内分享按钮
      // console.log(res.target)
    }
    return {
      title: '', //分享的名称
      path: 'modules/common/instruction-web-html-view/index?query=' + encodeURIComponent(JSON.stringify({
        src: this.src
      })),
      mpId: this.$appId, //此处配置微信小程序的AppId
    }
  },
  methods: {
    handleBack(){
      this.$navto.push('PharmacyCyclopedia',{gs:'J3iamm'})
    },
    fullyDecodeURI (url) {
      function isEncoded(url) {
        if (url.startsWith('https://') || url.startsWith('http://')) return false
        url = url || '';
        return url !== decodeURIComponent(url);
      }
      while (isEncoded(url)){
        url = decodeURIComponent(url);
      }
      return url;
    }
  }
}
</script>

<style lang="scss" scoped>
  .main{
    display: flex;
    flex-direction: column;
    position: relative;
    height: 100vh;
  }
  .header-search-img{
    display: flex;
    width: 48rpx;
    height: 48rpx;
    margin-right: 24rpx;
  }
  .custom-navbar {
    // position: fixed;
    // top: 0;
    // left: 0;
    // right: 0;
    z-index: 999;
    display: flex;
    align-items: center;
    box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
    background-color: #fff;
  }

  .navbar-left,.navbar-right {
    width: 100rpx;
    padding: 0 20rpx;
    display: flex;
    align-items: center;
  }
  .navbar-center {
    flex: 1;
    text-align: center;
    font-size: 32rpx;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  /deep/.my-web{
    top:44px;
    flex: 1;
  }
</style>
