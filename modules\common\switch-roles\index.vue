<template>
  <page>
    <view slot="content" class="main-body">
      <scroll-view scroll-y="true" class="container" :class="{'height-calc-120': weChatInfo.isShow}">
        <view class="store">
          <view class="title">
            所在租户
          </view>
          <view class="ul">
            <view
              class="li"
              v-for="(item,index) in storeList"
              :key='index'
              @tap="selectedStore(item)"
              :class="{'on':item.select}">
              <default-img :config="config.avatar" :cData="item.logo" :cName="item.tenantName" class="store-image"/>
              <text>{{ item.tenantName }}</text>
            </view>
          </view>
        </view>
        <view class="btn-bg m-tb-20-auto" @tap="navtoBacko()">确认</view>
      </scroll-view>
    </view>
  </page>
</template>
<script>
import { mapState } from 'vuex'
// import uniPopup from '@/components/uni/uni-popup'
export default {
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $timePlugin: this.$timePlugin,
      $static_ctx: this.$static_ctx,
      bindingState: false, // 微信绑定
      isPopupParam: { // 弹窗信息
        title: '',
        content: ''
      },
      config: {
        avatar: {
          widthHeightAuto: true
        }
      }
    }
  },
  components: {
    // uniPopup
  },
  computed: {
    ...mapState('user', {
      storeList: state => state.storeList,
      weChatInfo: state => state.weChatInfo,
      curSelectStore: state => state.curSelectStore,
      userInfoList: state => state.userInfoList,
      curSelectUserInfo: state => state.curSelectUserInfo
    })
  },
  watch: {
    curSelectUserInfo(val) {

    }
  },
  onLoad(paramsObj) {
    const query = this.$Route.query
    if (!this.$validate.isNull(query)) {}
    if (this.storeList.length > 0 && this.curSelectStore) {
      for (const item in this.storeList) {
        if (this.storeList[item].tenantId === this.curSelectStore.tenantId && this.curSelectStore.select) {
          this.storeList[item].select = true
        } else {
          this.storeList[item].select = false
        }
      }
    }
  },
  methods: {
    navtoBacko() {
      this.$navto.back(1)
    },
    /**
     * 跳转租户页面
     */
    pushStore() {
      const path = this.redirect || 'Index'
      this.$navto.pushTab(path)
    },
    /**
     * 选择点击切换租户
     * @param eq 索引
     */
    selectedStore(item) {
      const that = this
      if (!that.$validate.isNull(that.storeList)) {
        if (item.select) {
          return
        }
      }
      that.$uniPlugin.loading('切换中，稍等')
      for (const key in that.storeList) {
        this.storeList[key].select = false
        if (item.tenantId === that.storeList[key].tenantId) {
          that.storeList[key].select = true
        }
      }
      const itemDate = {
        storeList: that.storeList,
        curSelectStore: item,
        curSelectStoreId: item.tenantId,
        curSelectUnitId: item.tenantId
      }
      that.$common.setKeyVal('user', 'storeList', itemDate.storeList, true)
      that.$common.setKeyVal('user', 'curSelectStore', itemDate.curSelectStore, true)
      that.$common.setKeyVal('user', 'curSelectStoreId', itemDate.curSelectStoreId, true)
      that.$common.setKeyVal('user', 'curSelectUnitId', itemDate.curSelectUnitId, true)
      that.$common.setKeyVal('system', 'isOnShow', true)
      that.$ext.user.getInfoGroupPage(that, () => {})
    }
  }
}

</script>
<style lang="scss" scoped>
  .m-tb-20-auto{
    margin: 20upx auto;
  }
  .height-calc-120{
    height: calc(100% - 120upx) !important;
  }
  .main-body{
    height: 100%;
    .container{
      height: 100%;
    }
  }
  .store{
    width: 100%;
    .title{
      line-height: 80upx;
      padding-left: 30upx;
      font-size: 28upx;
      color: #333333;
    }
    .ul{
      background-color: #FFFFFF;
      overflow: hidden;
      width: 100%;
      padding: 20upx 30upx 0 30upx;
      box-sizing: border-box;
      .li{
        width: 324upx;
        float: left;
        height: 96upx;
        margin-right: 24upx;
        @include rounded(10upx);
        border: 2upx solid #E9EDF3;
        margin-bottom: 30upx;
        .store-image{
          width:64upx;
          height: 64upx;
          @include rounded(50%);
          display: inline-block;
          vertical-align: top;
          margin:16upx 8upx 0 22upx;
          overflow: hidden;
        }
        text{
          width:180upx;
          font-size: 30upx;
          color: #333333;
          display: inline-block;
          line-height: 96upx;
          margin-left:8upx;
          @include ellipsis();
        }
      }
      .li:nth-child(2n){
        margin-right: 0;
      }
      .on{
        @include iconImg(324, 96, '/business/icon-select.png');
        border:none;
      }
    }
  }

  .role{
    width: 100%;
    .title{
      line-height: 80upx;
      padding-left: 30upx;
      font-size: 28upx;
      color: #333333;
    }
    .ul{
      background-color: #FFFFFF;
      overflow: hidden;
      width: 100%;
      .li{
        width: 100%;
        box-sizing: border-box;
        padding: 28upx 30upx;
        height: 120upx;
        float: left;
        position: relative;
        .role-image{
          width: 64upx;
          height: 64upx;
          display: inline-block;
          vertical-align: top;
          @include rounded(50%)
        }
        text{
          display: inline-block;
          margin:17upx 0 0 20upx;
          font-size: 30upx;
          color:#333;
        }
        em{
          position: absolute;
          top: 70upx;
          left: 65upx;
          z-index: 2;
        }
        em.home{
          @include iconImg(28, 28, '/business/icon-jiazhang.png');
        }
        em.worker{
          @include iconImg(28, 28, '/business/icon-zhigong.png');
        }
        em.boy{
          @include iconImg(28, 28, '/business/icon-boy.png');
        }
        em.girl{
          @include iconImg(28, 28, '/business/icon-girl.png');
        }
        i{
          position: absolute;
          top:50upx;
          right: 30upx;
          @include iconImg(26, 20, '/business/icon-dagou.png');
        }
      }
    }
  }
  .footer{
    position: fixed;
    bottom:0;
    left: 0;
    right: 0;
    background-color: #FFFFFF;
    padding: 0 30upx;
    height: 120upx;
    line-height: 120upx;
    @include downBoxShadow(0upx, 2upx , 12upx);
    em.icon-wechat-left-bottom{
      position: absolute;
      left: 0;
      bottom: 0;
      @include iconImg(54, 54, '/business/icon-wechat-left-bottom.png');
    }
    .top{
      display: inline-block;
      width: 100%;
      .l{
        vertical-align: middle;
        display: inline-block;
        width: 90upx;
        height: 90upx;
        overflow: hidden;
        @include rounded(50%);
        margin-right: 20upx;
        overflow: hidden;
        image{
          width: 100%;
          height: 100%;
        }
      }
      .m{
        display: inline-block;
        width: calc(100% - 290upx);
        color: #666666;
        font-size: 32upx;
        line-height: 48upx;
        @include ellipsis(1);
        vertical-align: middle;
      }
      .r{
        vertical-align: middle;
        margin-left: 20upx;
        display: inline-block;
        width: 160upx;
        font-size: 32upx;
        line-height: 48upx;
        text-align: right;
        @include ellipsis(1);
      }
      .red{
        color: #FF4A4A;
      }
      .green{
        color: #0DD140;
      }
    }
  }
  .show-popup{
    background-color: #fff;
    width: 560upx;
    @include rounded(6upx);
    overflow: hidden;
    .popup-t{
      text-align: center;
      font-size: 36upx;
      line-height: 48upx;
      padding: 30upx 0 20upx 0;
    }
    .popup-m-img{
      display: inline-block;
      margin-bottom: 20upx;
      .l{
        vertical-align: middle;
        display: inline-block;
        width: 90upx;
        height: 90upx;
        overflow: hidden;
        @include rounded(50%);
        /*margin-right: 20upx;*/
        margin:0 20upx 0 30upx;
        overflow: hidden;
        image{
          width: 100%;
          height: 100%;
        }
      }
      .m{
        display: inline-block;
        color: #333;
        font-size: 28upx;
        line-height: 42upx;
        vertical-align: middle;
      }
    }
    .popup-m{
      display: block;
      margin-bottom: 20upx;
      font-size: 28upx;
      line-height: 42upx;
      color: #333;
      padding: 0 30upx;
    }
    .popup-b{
      border-top: 2upx solid $contentDdt;
      view {
        line-height: 88upx;
        height: 88upx;
        font-size: 36upx;
        text-align: center;
        display: inline-block;
      }
      .l{
        width: calc(50% - 2upx);
        border-right: 2upx solid $contentDdt;
      }
      .r{
        width: 50%;
        color: $topicC;
      }
    }
  }
</style>
