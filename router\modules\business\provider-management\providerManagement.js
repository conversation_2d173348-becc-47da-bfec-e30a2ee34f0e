/**
 * 服务商管理路由
 */
export default [
  {
    path: '/modules/provider-management/pages/dashboard/index',
    name: 'ProviderWorkBench',
    meta: {
      index: 2,
      headerObj: {
        title: '工作台',
        isShow: true
      }
    }
  },
  {
    path: '/modules/provider-management/pages/update-data/index',
    name: 'ProviderUpdateData',
    meta: {
      index: 2,
      headerObj: {
        title: '资料修改',
        isShow: true
      }
    }
  },
  {
    path: '/modules/provider-management/pages/update-data/update-user-info',
    name: 'ProviderUpdateUserInfo',
    meta: {
      index: 2,
      headerObj: {
        title: '资料修改',
        isShow: true
      }
    }
  },
  {
    path: '/modules/provider-management/pages/data-analytics/index',
    name: 'ProviderDataPanel',
    meta: {
      index: 2,
      headerObj: {
        title: '数据面板',
        isShow: true
      }
    }
  },
  {
    path: '/modules/provider-management/pages/order-center/index',
    name: 'ProviderOrderCenter',
    meta: {
      index: 2,
      headerObj: {
        title: '订单中心',
        isShow: true
      }
    }
  },
  {
    path: '/modules/provider-management/pages/order-details/index',
    name: 'ProviderAccompanyDetails',
    meta: {
      index: 2,
      headerObj: {
        title: '订单详情',
        isShow: true
      }
    }
  },
  {
    path: '/modules/provider-management/pages/create-order/index',
    name: 'ProviderAccompanyCreateOrder',
    meta: {
      index: 2,
      headerObj: {
        title: '创建订单',
        isShow: true
      }
    }
  },
  {
    path: '/modules/provider-management/pages/accompany-record/index',
    name: 'ProviderAccompanyRecord',
    meta: {
      index: 2,
      headerObj: {
        title: '陪诊记录',
        isShow: true
      }
    }
  }
]
