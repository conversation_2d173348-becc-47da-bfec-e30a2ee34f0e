<template>
  <view>
    <page>
      <view slot="content">

        <view class="main-search">
            <search placeholder="搜索" :fixed="true" top="88" v-model="regForm.search" @changeSearch="changeSearch"></search>
        </view>
        <scroll-refresh :fixed="true" top="92" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
            <view class="main-content">
              <information-item :isStore="true" :list="itemList"></information-item>
            </view>
          </scroll-refresh>
      </view>
    </page>
  </view>

</template>

<script>
// import MescrollUni from '@/components/uni/mescroll-uni'
import search from '@/components/basics/form/search'
import InformationItem from "../../../pages/index/components/information-item";
export default {
  components: {
    // MescrollUni,
    search,
    InformationItem
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      // $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      // $timePlugin: this.$timePlugin,
      $validate: this.$validate,
      mescroll: null, // mescroll实例对象
      downOption: {
        auto: false // 不自动加载
      },
      upOption: {
        auto: false // 不自动加载
      },
      isInit: false, // 列表是否已经初始化
      scrollY: 0,
      regForm: {
        search: ''
      },
      typeofAll: '',
      itemList:[],
    }
  },
  watch: {
    // 监听下标的变化

  },
  onLoad(paramsObj) {
    const query = this.$Route.query
    if (!this.$validate.isNull(query)) {
    }
    // #ifdef MP-WEIXIN
    wx.showShareMenu({
      withShareTicket:true,
      //设置下方的Menus菜单，才能够让发送给朋友与分享到朋友圈两个按钮可以点击
      menus:["shareAppMessage","shareTimeline"]
    })
    // #endif
  },
  mounted() {
    this.init()
  },
  methods: {
    navtoGo(url = '', obj = {}) {
      this.$navto.push(url, obj)
    },
    init(val) {
      this.$nextTick(() => {
        this.isInit = true // 标记为true
        this.regForm.search = ''
        this.mescroll.triggerDownScroll()
      })
    },
    changeSearch(obj) {
      this.mescroll.optUp.page.num = 1
      this.mescroll.optUp.page.size = 7
      this.regForm.search = obj.name
      this.mescroll.triggerDownScroll()
    },
    scrollInit(scroll) {
      scroll.optUp.page.num = 1
      scroll.optUp.page.size = 7
      this.mescroll = scroll
    },
    returnFn(obj) {
      const that = this
      function queryPage(pageNum, pageSize, fn) {
        const param = {
          current: pageNum,
          size: pageSize,
          condition:{
            terminalType: 1
          }
        }
        if (that.regForm.search) {
          param.condition.title = that.regForm.search
        }
        that.$api.information.informationscienceQueryAppPage(param).then(res => {
          if (res && res.data.records) {
            for (const a in res.data.records) {
              const data = res.data.records[a]
              if (data.coverPath.length !=0){
                if (data.coverPath.indexOf("http")!=-1){

                }else {
                  data.coverPath = that.file_ctx + data.coverPath
                }
              }
              data.creationTimeText = that.$common.formatDate(new Date(data.creationTime), 'yyyy-MM-dd')
              // res.data.records[a] = data
            }
            // debugger
            fn(res.data.records)
          }
        })
      }
      setTimeout(function() {
        queryPage(obj.pageNum, obj.pageSize, (data) => {
          if (obj.pageNum === 1) {
            that.itemList = []
          }
          that.itemList = that.itemList.concat(data)
          obj.successCallback && obj.successCallback(data || [])
        })
      }, that.$constant.noun.scrollRefreshTime)
    }
  }
}
</script>
<style lang="scss" scoped>
  .main-container{
    background-color:#f7f7f7;
    height: 100%;
  }
  .main-search{
    background-color:#FFFFFF;
  }
  .main-content{
    padding:0upx 16upx 30upx 16upx;
    background: #f7f7f7;
  }
</style>
