<template>
	<view>
<!--		<view class="search">-->
<!--      <input-->
<!--        @input="handleInput"-->
<!--        class="search-input"-->
<!--        type="text"-->
<!--        focus-->
<!--        placeholder="请输入要搜索的联系人"-->
<!--      >-->
<!--    </view>-->
<!--		</view>-->
    <view class="search-custom">
      <view class="input">
        <em class="icon-search"></em>
        <input
          class="icon-search"
          type="text"
          :placeholder="pPlaceholder"
          @input="handleInput"
        >
      </view>
    </view>
		<scroll-view class="search-main" scroll-y="true" v-if="keyword">
			<view class="search-main-errtitle" v-if="hasNoData">无搜索结果</view>
<!--      <view-->
<!--      class="search-main-title"-->
<!--      hover-class="hover"-->
<!--      @click="handleClick"-->
<!--      :hover-start-time="20"-->
<!--      :hover-stay-time="70"-->
<!--      v-for="item of list"-->
<!--      :key="item.id"-->
<!--      :data-name="item.name"-->
<!--      :data-id="item.id"-->
<!--      :data-phoneNumber="item.phoneNumber">-->
			<view
			hover-class="hover"
			@click="handleClick(item)"
			:hover-start-time="20"
			:hover-stay-time="70"
			v-for="(item, index) in list"
			:key="index">
        <view class="communication">
          <i v-if="iconState" :class="item.customChecked?'icon-yijianfankui-d-ok':'icon-xuanze'"></i>
<!--          <image v-if="imgState" mode="scaleToFill" :src="item.avatar ? file_ctx + item.avatar : $static_ctx + 'image/system/avatar/icon-default-avatar.png'"/>-->
          <default-img v-if="imgState" :config="config.avatar" :cData="item.avatar" :cName="item.name" class="role-image"/>
          <text>{{item.name}}</text>
        </view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
export default {
  name: 'PhoneSearchList',
  props: {
    phones: Object,
    // 搜索框提示
    pPlaceholder: {
      type: String,
      default() {
        return '请输入要搜索的联系人'
      }
    },
    // 控制icon是否显示
    iconState: {
      type: Boolean,
      default() {
        return true
      }
    },
    // 控制图片是否显示
    imgState: {
      type: Boolean,
      default() {
        return true
      }
    }
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      $validate: this.$validate,
      keyword: '',
      list: [],
      timer: null,
      config: {
        avatar: {
          widthHeightAuto: true
        }
      }
    }
  },
  computed: {
    hasNoData() {
      return !this.list.length
    }
  },
  watch: {
    keyword() {
      if (this.timer) {
        clearTimeout(this.timer)
      }
      if (!this.keyword) {
        this.list = []
        return
      }
      this.timer = setTimeout(() => {
        const result = []
        for (const i in this.phones) {
          this.phones[i].forEach((item) => {
            if (item.spell.indexOf(this.keyword) > -1 || item.name.indexOf(this.keyword) > -1) {
              result.push(item)
            }
          })
        }
        this.list = result
      }, 100)
    }
  },
  methods: {
    handleInput(e) {
      this.keyword = e.detail.value
      this.$emit('paramInput', e.detail.value)
    },
    handleClick(e) {
      this.$emit('paramClick', e)
      // this.$emit('paramClick', e.target.dataset)
    }
  }
}
</script>

<style lang="scss" scoped>
  .m-b-98{
    margin-bottom: 98upx;
  }
  .height-atuo{
    height: 100%;
  }
.search-custom{
  height: 56upx;
  background-color: $pageBg;
  padding: 16upx;
  .input{
    width:696upx;
    height:56upx;
    background-color:#fff;
    border-radius: 28upx;
    border: 1px solid #ebebeb;
    margin:0 auto;
    position: relative;
    em{
      position: absolute;
      top:14upx;
      left:18upx;
      width:34upx;
      height: 34upx;
    }
    input{
      width:100%;
      height:62upx;
      padding:0 20upx 0 60upx;
      box-sizing: border-box;
    }
  }
}
.communication{
  padding: 0 30upx;
  background-color: #fff;
  border-bottom: 2upx solid $contentDdt;
  line-height: 120upx;
  height: 120upx;
  i{
    vertical-align: middle;
    display: inline-block;
    height: 46upx;
    width: 46upx;
    margin-right: 14upx;
  }
  .role-image{
    vertical-align: middle;
    display: inline-block;
    width: 90upx;
    height: 90upx;
    @include rounded(50%);
    margin-right: 10upx;
  }
  text{
    vertical-align: middle;
    font-size: 32upx;
    line-height: 48upx;
    display: inline-block;
    width: calc(100% - 160upx);
    color: #333;
  }
}
.hover{
  background-color: #eee;
}
.search{
  background-color: #fff;
  padding: 10upx 20upx;
  border-bottom: 2upx solid $contentDdt;
  /*width: 100%;*/
  /*height: 88upx;*/
  /*background-color: $pageBg;*/
  /*padding: 16upx 20upx;*/
  /*border-bottom: 1upx solid #D6D6DB;*/
}

.search-input{
  font-size:28upx;
  border: 2upx solid $contentDdt;
  border-radius: 3px;
  padding: 10upx 20upx 10upx 20upx;
  /*height: 56upx;*/
  /*background: #FFFFFF;*/
  /*border-radius: 28upx;*/
  /*border: 1upx solid #E6E6EA;*/
  /*padding: 16upx 21upx;*/
}

.search-main{
  height: 100%;
  padding-bottom: 20upx;
  background-color:#fff;
  overflow: hidden;
}

.search-main-errtitle,.search-main-title{
  width: 100%;
  height: 92upx;
  line-height: 92upx;
  font-size: 32upx;
  padding: 0 20upx;
  border-bottom: 2upx solid $contentDdt;
}

</style>
