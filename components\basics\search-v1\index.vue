<!-- 搜索栏组件 <search :placeholder="***"  :value="***" @input="onKeyInput" ></search> -->
<template>
  <view class="search" :class="{ fixed: fixed }" :style="{ top: top + 'upx' }" @tap="searcFn()">
    <view
      class="input"
      :class="{ 'width-auto': !rightText && !isIcon }"
    >
      <image class="icon-view-l" :src="file_ctx + 'static/image/business/hulu-v2/icon-ai-home-index-bg.png'" mode="aspectFit"></image>
      <swiper class="input-view" circular vertical :autoplay="autoplay" :interval="interval"
        :duration="duration">
        <swiper-item class="item-view" v-for="(item,index) in hotSearchList" :key="index" @touchmove.stop="">
          <view class="swiper-item uni-bg-red">{{ item.word }}</view>
        </swiper-item>
      </swiper>

      <view class="line"></view>
      <view class="search-text" @tap="returnFn">
        <view class="text">搜索</view>
        <view v-if="isIcon" class="icon icon-screen"></view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Search',
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      file_ctx: this.file_ctx,
      $validate: this.$validate,
      inputValue: '',
      timer: undefined,
      width: 0,
      autoplay: true,
      interval: 3000,
      duration: 1000
    }
  },
  props: {
    hotSearchList:{ // 热搜列表
      type: Array,
      default() {
        return []
      }
    },
    // 提示文本style
    placeholderStyle: {
      type: String,
      default() {
        return ''
      }
    },
    isIcon: {
      type: Boolean,
      default() {
        return false
      }
    },
    rightText: {
      type: String,
      default() {
        return ''
      }
    },
    value: {
      type: String,
      required: false,
      default: ''
    },
    placeholder: {
      type: String,
      default() {
        return '搜索'
      }
    },
    k: { // 字段名称
      type: String,
      default() {
        return 'name'
      }
    },
    top: {
      type: String,
      default() {
        return '88'
      }
    },
    fixed: {
      type: [String, Boolean],
      default() {
        return true
      }
    }
  },
  watch: {
    value: {
      handler(val) {
        this.inputValue = val
      },
      deep: true
    },
    /** 监听手机输入 */
    inputValue() {
      if (this.timer) {
        clearTimeout(this.timer)
      }
      this.timer = setTimeout(() => {
        this.inputValue = this.$validate.trim(this.inputValue)
        const obj = {}
        obj[this.k] = this.inputValue
        this.$emit('changeSearch', obj)
      }, 500)
    }
  },
  mounted() {
  },
  methods: {
    searcFn() {
      this.$navto.push('CommonSystemSearch')
    },
    onKeyInput(e) {
      this.inputValue = e.target.value
    },
    returnFn() {
      this.$emit('returnFn', this.inputValue)
    },
  }
}
</script>

<style lang="scss" scoped>
.search {
  height: 102upx;
  padding: 16upx 0;
  box-sizing: border-box;
  .input {
    background-color: #fff;
    @include rounded(50upx);
    position: relative;
    width: calc(100% - 140upx);
    vertical-align: middle;
    display: inline-block;
    box-sizing: border-box;
    border: 2rpx solid #00B484;
    .icon-view-l {
      position: absolute;
      top: 50%;
      -webkit-transform: translateY(-50%);
      transform: translateY(-50%);
      left: 30upx;
      width: 48upx;
      height: 48upx;
    }
    .input-view {
      display: flex;
      align-items: center;
      width: 100%;
      height: 72upx;
      line-height: 72upx;
      box-sizing: border-box;
      .item-view{
        padding: 0 114rpx 0 78rpx;
        display: flex;
        align-items: center;
        .swiper-item{
          margin-top: 5rpx;
          font-size: 28rpx;
          color: #A5AAB8;
        }
      }
    }
  }
  .width-auto {
    width: 100%;
  }
  .line{
    position: absolute;
    right: 112rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 2rpx;
    height: 40rpx;
    z-index: 9999;
    background: #DBDDE0;
  }
  .search-text{
    display: flex;
    justify-content: center;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 112rpx;
    height: 48rpx;
    .text{
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24rpx;
      color: #fff;
      width: 94rpx;
      height: 48rpx;
      background: linear-gradient( 90deg, #05E4B0 0%, #00B484 100%);
      border-radius: 32rpx 32rpx 32rpx 32rpx;
    }
  }
  .right-text {
    vertical-align: middle;
    display: inline-block;
    .text {
      text-align: right;
      font-size: 32upx;
      line-height: 48upx;
      color: #666;
      display: inline-block;
      vertical-align: middle;
      width: calc(100% - 40upx);
    }
    .icon {
      display: inline-block;
      vertical-align: middle;
      margin-left: 8upx;
    }
    .icon-screen {
      @include iconImg(32, 32, "/business/icon-screen.png");
    }
  }
}
</style>
