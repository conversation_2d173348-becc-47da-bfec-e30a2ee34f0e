<template>
  <page>
    <view slot="content" class="body-main">
      <integration-time-er :backTaskFlag='IntegrationData.backTaskFlag'></integration-time-er>
      <!-- #ifdef H5 -->
      <view class="poster-content">
        <wx-open-launch-weapp
          id="launch-btn"
          :appid="$appId"
          @launch="handleClickBtn(1)"
          :path="`modules/activity/health-testing/testing-detail?id=1997282344131911684&gs=32EjMn`"
          style="width: 750rpx;height: 1292rpx;position:absolute;"
        >
          <script type="text/wxtag-template">
            <div style="width: 750rpx;height: 1292rpx;position: relative;z-index:-1;">
              <img style="width: 100%;height: 100%;" src="https://file.greenboniot.cn/static/image/business/pharmacy-cyclopedia/testing-poster.png"></img>
            </div>

          </script>
        </wx-open-launch-weapp>
        <wx-open-launch-weapp
          id="launch-btn"
          :appid="$appId"
          @launch="handleClickBtn(2)"
          :path="`modules/activity/health-testing/testing-detail?id=1997282344131911684&gs=32EjMn`"
          style="width: 512.91rpx;height: 98.97rpx;position:absolute;bottom:40rpx;left:50%;transform: translate(-50%, -50%);"
        >
          <script type="text/wxtag-template">
            <div class="testing-btn" style="
              display: flex;
              align-items: center;
              justify-content: center;
              position: relative;
              width: 512.91rpx;
              height: 98.97rpx;
            ">
              <img style="width: 100%;height: 100%;" src="https://file.greenboniot.cn/static/image/business/pharmacy-cyclopedia/testing-poster-btn.png"></img>
              <div style="width:314.02rpx;38.62rpx;position:absolute;">
                <img src="https://file.greenboniot.cn/static/image/business/pharmacy-cyclopedia/testing-poster-btn-text.png" style="position:absolute;top:50%;left:50%;transform: translate(-50%, -50%);width:157.01px;height:19.31px;">
              </div>
            </div>
          </script>
        </wx-open-launch-weapp>
      </view>
      <!-- #endif -->
      <view class="header">
        <banner-ads class="my-banner" ref="bannerAdsRef" :query-params="{useType: 10}" height="208rpx" />
      </view>
      <!-- #ifdef MP-WEIXIN || MP-ALIPAY -->
      <view class="m-main-body">
        <scroll-refresh class="scroll-refresh-main" :isShowEmptySwitch="false" :fixed="false" :isAbsolute="false" :up="up" :down="down" @returnFn="returnFn" @scrollInit="scrollInit">
          <!-- #ifdef MP-ALIPAY -->
          <view class="scroll-refresh-main">
          <!-- #endif -->
            <view class="health-content">
              <view class="health-item" v-for="item in indexlist" :key="item.id" @click="handleClickJump(item)">
                <view class="item-l">
                  <image :src="file_ctx + item.coverUrl"></image>
                </view>
                <view class="item-r">
                  <view class="item-head">
                    <view class="item-title">{{ item.title }}</view>
                    <view class="ranking">{{ item.formTemplateNum }} 题 / {{ item.answeringTime }}分钟</view>
                  </view>
                  <view class="item-bott">{{ item.participateNum  + item.submitNum }}<span>人测过</span></view>
                </view>
              </view>
            </view>
          <!-- #ifdef MP-ALIPAY -->
          </view>
          <!-- #endif -->
        </scroll-refresh>
      </view>
      <view class="my-evaluating" @click="handleJumpEvaluating"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/health-testing-my-evaluating-new.png'"></image></view>
      <!-- #endif -->
    </view>
  </page>
</template>

<script>
  import { mapState } from 'vuex'
  import bannerAds from '@/components/basics/banner-ads/index.vue'
  export default {
    components: {
      bannerAds,
    },
    data(){
      return{
        $constant:this.$constant,
        $appId: this.$appId,
        file_ctx: this.file_ctx,
        indexlist:[],
        $constant: this.$constant,
        mescroll: null, // mescroll实例对象
        down: {
          auto: false
        },
        up: {
          auto: false
        },
      }
    },
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId,
        isLogin: state => state.isLogin
      }),
    },
    onLoad(){
      // #ifdef MP-WEIXIN
      wx.showShareMenu({
        menus: ['shareAppMessage', 'shareTimeline'],//'shareAppMessage'打开分享好友功能 | 'shareTimeline'打开分享到朋友圈功能
      });
      // #endif
      this.$nextTick(() => {
        this.init()
      })
      this.wxh5ShareInit()
      // #ifdef MP-WEIXIN
      this.handleClickTrack('OperationDetailsPageView')
      // #endif

      // #ifdef H5
      this.pageexposurerecordInsert()
      // #endif
    },
    onShow() { 
      this.$refs.bannerAdsRef.init()
    },
    onUnload(){
      // #ifdef MP-WEIXIN
      this.handleClickTrack('EndOperationDetailsPageView')
      // #endif
    },
    // 分享到朋友圈
    onShareTimeline(res) {
      this.handleHealthSelfTestClick('分享')
      return {
        title: '老是累得不行，是你的身体在报警！这有海量健康测评，动动手指试试吧！',//分享的标题
        path: 'modules/activity/health-testing/index',
      }
    },
    // 发送给好友
    onShareAppMessage(res) {
      this.handleHealthSelfTestClick('分享')
      return {
        title: '身体有点小状况？来个快速自测，安心又放心！', //分享的名称
        path: 'modules/activity/health-testing/index',
        mpId:this.$appId, //此处配置微信小程序的AppId
        imageUrl: this.file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-testing-transpond.jpg',
      }
    },
    mounted(){},
    methods:{
      handleHealthSelfTestClick(type){
        getApp().globalData.sensors.track("HealthSelfTestClick",{'click_type':type})
      },
      handleJumpEvaluating(){
        if(this.isLogin){
          this.$navto.push('MyEvaluating')
        } else {
          this.$navto.push('Login',{formPage: 'MyEvaluating'})
        }
      },
      // 按钮点击
      async handleClickBtn(type){
        this.handleClickCommonPort(type)
      },
      async handleClickCommonPort(type){
        let parmas = {
          imageUrl:this.file_ctx + 'static/image/business/pharmacy-cyclopedia/testing-poster.png',
          accountId:this.accountId,
          businessType:this.$constant.drugBook.businessTypeObj.h5HealthTypePage,
        }
        if(type == 1){
          await this.$api.drugBook.visitpagerecordInsert(parmas)
        } else {
          await this.$api.drugBook.imageoperationrecordInsert(parmas)
        }
      },
      // #ifdef H5
      async pageexposurerecordInsert(){
        let parmas = {
          imageUrl:this.file_ctx + 'static/image/business/pharmacy-cyclopedia/testing-poster.png',
          accountId:this.accountId,
          businessType:this.$constant.drugBook.businessTypeObj.h5HealthTypePage,
        }
        await this.$api.drugBook.pageexposurerecordInsert(parmas)
      },
      // #endif
      // #ifdef MP-WEIXIN
      handleClickTrack(type){
        let pages = getCurrentPages()
        let current = pages[pages.length - 1]; // 获取到当前页面的信息
        let pageInfo = __wxConfig.page && __wxConfig.page[`${current.route}.html`] || ''; // 内部属性
        getApp().globalData.sensors.track(type,
          {
            'page_name' : pageInfo?.window?.navigationBarTitleText || '',
            'first_operation_name' : pageInfo?.window?.navigationBarTitleText || '',
            'second_operation_name' : '',
          }
        ) 
      },
      // #endif
      handleClickJump(item){
        let params = {
          title:item.title,
          id:item.id
        }
        this.$navto.push('TestingDetail', params)
      },

      returnFn(obj) {
        const that = this
        setTimeout(function () {
          let params = {
            current: obj.pageNum,
            size: obj.pageSize,
            condition:{
              collectionType:5,
              sortAsc:1,
            }
          }
          that.$api.activity.researchQueryPage(params).then(res => {
            let data = res.data.records
            if (obj.pageNum === 1) {
              that.indexlist = []
            }
            that.indexlist = [...that.indexlist, ...data]
            obj.successCallback && obj.successCallback(data)
          })
        }, that.$constant.noun.scrollRefreshTime)

      },

      scrollInit(scroll) {
        scroll.optUp.page.num = 1
        scroll.optUp.page.size = 10
        this.mescroll = scroll
      },
      init() {
        this.$nextTick(() => {
          this.mescroll.triggerDownScroll()
        })
      },
    },
 }
</script>

<style lang='scss' scoped>
.body-main{
  position: relative;
  display: flex;
  height: 100vh;
  flex-direction: column;
  // padding:0 20upx;
  /* #ifdef H5 */
  .poster-content{
    position: relative;
    width: 750rpx;
    height: 1292rpx;
    overflow: hidden;
  }
  /* #endif */
  .header{
    padding: 24rpx 32rpx;
    /deep/.my-banner{
      .banner-main{
        margin-bottom: 0;
      }
    }
  }
  .m-main-body{
    // height: 100vh;
    flex: 1;
    background: #F4F6FA;
    // height: 100%;
    .scroll-refresh-main{
      height: 100%;
      /deep/ .mescroll-uni{
        .z-paging-content{
          background-color: transparent !important;
        }
      }
      .health-content{
        padding: 0 32rpx;
        width: calc(100% - 64rpx);
        border-radius:20upx;
        // background-color: #fff;
        .health-item{
          display: flex;
          // align-items: center;
          padding: 24rpx;
          background-color: #fff;
          border-radius: 16rpx 16rpx 16rpx 16rpx;
          margin-bottom: 20rpx;
          .item-l{
            display: flex;
            width: 196rpx;
            height: 152rpx;
            border-radius: 16rpx  16rpx  16rpx  16rpx;
            overflow: hidden;
            margin-right: 20rpx;
            image{
              width: 100%;
              height: 100%;
            }
          }
          &:last-child{
            margin-bottom: 0;
          }
          .item-r{
            display: flex;
            flex-direction: column;
            flex: 1;
            .item-head{
              .item-title{
                font-size: 32rpx;
                color: #333333;
              }
              .ranking{
                margin-top: 4rpx;
                font-size: 24rpx;
                color: #777777;
              }
            }
            .item-bott{
              margin-top: 20rpx;
              font-size: 28rpx;
              color:#00B484;
              span{
                font-size: 20rpx;
                color:#777777;
              }
            }
          }
        }
      }
    }
  }
  .my-evaluating{
    position: fixed;
    top: 50%;
    right: 32rpx;
    width: 144rpx;
    height: 144rpx;
    z-index: 9999;
    image{
      width: 100%;
      height: 100%;
    }
  }
}
</style>