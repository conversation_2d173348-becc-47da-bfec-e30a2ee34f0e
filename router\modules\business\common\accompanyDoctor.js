import serverOptions from '@/config/env/options'
export default [
  {
    path: '/modules/accompany-doctor/home/<USER>',
    name: 'AccompanyHome',
    meta: {
      index: 2,
      headerObj: {
        title: serverOptions.title,
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/home/<USER>',
    name: 'AccompanyTransfers',
    meta: {
      index: 2,
      headerObj: {
        title: serverOptions.title,
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/service-detail/index',
    name: 'ServiceDetail',
    meta: {
      index: 2,
      headerObj: {
        title: '服务详情',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/system-search/index',
    name: 'accompanyDoctorSystemSearch',
    meta: {
      index: 2,
      headerObj: {
        title: '搜索',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/webView/webView',
    name: 'webView',
    meta: {
      index: 2,
      headerObj: {
        title: '搜索',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/system-search/search-data',
    name: 'accompanyDoctorSystemSearchSystem',
    meta: {
      index: 2,
      headerObj: {
        title: '搜索',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/accompany/home/<USER>',
    name: 'accompanyIndex',
    meta: {
      index: 2,
      headerObj: {
        title: '陪诊师首页',
        isShow: true
      }
    },
  },
  {
    path: '/modules/accompany-doctor/service-reservation/index',
    name: 'serviceReservation',
    meta: {
      index: 2,
      headerObj: {
        title: '陪诊服务',
        isShow: true
      }
    }
  },
  {
    path: "/modules/accompany-doctor/service-reservation/insuranceList",
    name: 'insuranceList',
    meta: {
      index: 2,
      headerObj: {
        title: '门诊无忧服务订单',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/service-reservation/accompany-record/index',
    name: 'accompanyRecord',
    meta: {
      index: 2,
      headerObj: {
        title: '陪诊记录',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/service-reservation/add-patient/index',
    name: 'Patient',
    meta: {
      index: 2,
      headerObj: {
        title: '就诊人档案',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/service-reservation/add-patient/add',
    name: 'addPatient',
    meta: {
      index: 2,
      headerObj: {
        title: '新增就诊人',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/service-reservation/add-patient/edit',
    name: 'editPatient',
    meta: {
      index: 2,
      headerObj: {
        title: '编辑就诊人',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/accompany/home/<USER>',
    name: 'accompanyDetails',
    meta: {
      index: 2,
      headerObj: {
        title: '订单详情',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/accompany/home/<USER>',
    name: 'accompanyCreateOrder',
    meta: {
      index: 2,
      headerObj: {
        title: '创建订单',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/accompany/home/<USER>',
    name: 'accompanyPosterLogo',
    meta: {
      index: 2,
      headerObj: {
        title: '海报Logo',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/my-combo/index',
    name: 'myComboIndex',
    meta: {
      index: 2,
      headerObj: {
        title: '订单',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/combo-order/index',
    name: 'comboOrderIndex',
    meta: {
      index: 2,
      headerObj: {
        title: '套餐订单',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/service-reservation/service-message/index',
    name: 'serviceMessage',
    meta: {
      index: 2,
      headerObj: {
        title: '服务信息',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/combo-detail/index',
    name: 'comboDetail',
    meta: {
      index: 2,
      headerObj: {
        title: '套餐详情',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/system-search/accompany-teacher',
    name: 'accompanyTeacher',
    meta: {
      index: 2,
      headerObj: {
        title: '社区交流',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/system-search/doctor-list',
    name: 'doctorList',
    meta: {
      index: 2,
      headerObj: {
        title: '本地名医',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/system-search/accompany-list',
    name: 'accompanyList',
    meta: {
      index: 2,
      headerObj: {
        title: '陪诊师',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/system-search/accompany-details',
    name: 'accompanyDoctorDetails',
    meta: {
      index: 2,
      headerObj: {
        title: '陪诊师',
        isShow: true
      }
    }
  },
  {
    path: "/modules/accompany-doctor/server/balances",
    name: 'serverBalances',
    meta: {
      index: 2,
      headerObj: {
        title: '我的余额',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/accompany/business-target/index',
    name: 'BusinessTarget',
    meta: {
      index: 2,
      headerObj: {
        title: '业务指标',
        isShow: true
      }
    }
  },
  {
    path: "/modules/accompany-doctor/server/withdraw",
    name: 'serverWithdraw',
    meta: {
      index: 2,
      headerObj: {
        title: '提现',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/accompany/data-panel/index',
    name: 'DataPanel',
    meta: {
      index: 2,
      headerObj: {
        title: '数据面板',
        isShow: true
      }
    }
  },
    {
    path: "/modules/accompany-doctor/distribute/application-distribution",
    name: 'applicationDistribution',
    meta: {
      index: 2,
      headerObj: {
        title: '申请分销',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/accompany/work-bench/index',
    name: 'WorkBench',
    meta: {
      index: 2,
      headerObj: {
        title: '工作台',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/accompany/order-center/index',
    name: 'orderCenter',
    meta: {
      index: 2,
      headerObj: {
        title: '订单中心',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/accompany/update-data/index',
    name: 'UpdateData',
    meta: {
      index: 2,
      headerObj: {
        title: '资料修改',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/accompany/update-data/update-user-info',
    name: 'UpdateUserInfo',
    meta: {
      index: 2,
      headerObj: {
        title: '资料修改',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/home/<USER>/personal/my-collect/index',
    name: 'AccompanyPersonalMyCollect',
    meta: {
      index: 2,
      headerObj: {
        title: '我的收藏',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/home/<USER>/personal/my-posts/index',
    name: 'AccompanyPersonalMyPosts',
    meta: {
      index: 2,
      headerObj: {
        title: '我的帖子',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/hospital-ranking/index',
    name: 'AccompanyHospitalRanking',
    meta: {
      index: 2,
      headerObj: {
        title: '医院排行',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/storeManagement/index',
    name: 'storeManagement',
    meta: {
      index: 2,
      headerObj: {
        title: '门店管理',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/storeManagement/fixtures',
    name: 'oneKeyFixtures',
    meta: {
      index: 2,
      headerObj: {
        title: '一键装修',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/home/<USER>/personal/my-comment/index',
    name: 'AccompanyPersonalMyComment',
    meta: {
      index: 2,
      headerObj: {
        title: '我的评论',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/home/<USER>/personal/my-like/index',
    name: 'AccompanyPersonalMyLike',
    meta: {
      index: 2,
      headerObj: {
        title: '我的点赞',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/home/<USER>/news/like-collect/index',
    name: 'AccompanyNewsLikeCollect',
    meta: {
      index: 2,
      headerObj: {
        title: '点赞和收藏',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/home/<USER>/news/reply/index',
    name: 'AccompanyNewsReply',
    meta: {
      index: 2,
      headerObj: {
        title: '我的回复',
        isShow: true
      }
    }
  },
  // {
  //   path: '/modules/accompany-doctor/accompany/course/index',
  //   name: 'CourseIndex',
  //   meta: {
  //     index: 2,
  //     headerObj: {
  //       title: '课程',
  //       isShow: true
  //     }
  //   }
  // },
  // 课程相关路由已迁移到 accompanyDoctorCourse.js
  // {
  //   path: '/modules/accompany-doctor/accompany/course/course-detail',
  //   name: 'CourseDetail',
  //   meta: {
  //     index: 2,
  //     headerObj: {
  //       title: '课程详情',
  //       isShow: true
  //     }
  //   }
  // },
  // {
  //   path: '/modules/accompany-doctor/accompany/course/second-classify',
  //   name: 'SecondClassify',
  //   meta: {
  //     index: 2,
  //     headerObj: {
  //       title: '二级分类',
  //       isShow: true
  //     }
  //   }
  // },
  // {
  //   path: '/modules/accompany-doctor/accompany/course/course-search',
  //   name: 'CourseSearch',
  //   meta: {
  //     index: 2,
  //     headerObj: {
  //       title: '课程搜索',
  //       isShow: true
  //     }
  //   }
  // },
  // {
  //   path: '/modules/accompany-doctor/accompany/course/recently-study',
  //   name: 'RecentlyStudy',
  //   meta: {
  //     index: 2,
  //     headerObj: {
  //       title: '最近学习',
  //       isShow: true
  //     }
  //   }
  // },
  // {
  //   path: '/modules/accompany-doctor/accompany/course/study-statement',
  //   name: 'StudyStatement',
  //   meta: {
  //     index: 2,
  //     headerObj: {
  //       title: '学习报表',
  //       isShow: true
  //     }
  //   }
  // },
  {
    path: '/modules/accompany-doctor/system-search/accompany-list',
    name: 'accompanyList',
    meta: {
      index: 2,
      headerObj: {
        title: '陪诊师',
        isShow: true
      }
    }
  },
  {
    path: '/modules/accompany-doctor/system-search/accompany-details',
    name: 'accompanyDoctorDetails',
    meta: {
      index: 2,
      headerObj: {
        title: '陪诊师',
        isShow: true
      }
    }
  },
]
