<template>
	<view>

		<block v-if="visible">
			<!-- 阴影背景 -->
			<view class="ly-shadow" :class="ly_shadow_animate" :style="{background:'rgba(0,0,0,'+ ly_bg_opacity + ')'}">
			</view>
			<view class="pickBox">
				<view class="pick">
					<view class="pick-t">
						<view class="pick-t-l" @click="closeClick">取消</view>
						<view class="pick-t-r" @click="queryClick">确定</view>
					</view>
					<picker-view indicator-style="height: 50px;" style="width: 100%; height: 250px;"
						:value="selectValue" @change="bindChange">

						<!-- 一列 -- 日期 -->
						<template v-for="item,index in columnArr2" >
							<picker-view-column :key='index'>
								<template v-for="item2,index2 in item" >
									<view style="line-height: 50px;text-align: center;" :key="item2.label">
										{{item2.label}}
										<text style="margin-left:5rpx">{{item2.until}}</text>
									</view>
								</template>
							</picker-view-column>
						</template>
					</picker-view>
				</view>
			</view>
		</block>
	</view>
</template>

<script>
	export default {
		name: "ly-picket",
		props: {
			show: {
				type: Boolean,
				default: false,
			},
			columnArr: {
				type: Array,
				default: function() {
					return []
				}
			},
			// 默认选中值
			value: {
				type: String,
				default: 'now'
			},
			// 下拉框类型
			type: {
				type: String,
				default: 'date', // 日期 年月日
			},
			// 当下拉框为日期时生效
			column: {
				type: Number,
				default: 3, // 默认三栏 ，年 月 日
			},
			// 类型为日期,
			beginTimer: {
				type: String,
				default: '1990-1-1'
			},
			// 结束日期
			endTimer: {
				type: String,
				default: 'now', // 不传默认当前时间，传格式yyyy-m-d
			}
		},
		data() {
			return {
				// 数据
				// columnArr: [
				//   [],
				//   [],
				//   []
				// ],
				ly_shadow_animate: "",
				ly_bg_opacity: "",
				ly_bg_opacity: 0.3,

				// 选中的索引集合
				selectIndex: [],
				// 选中的值
				selectItemArr: [],
				visible: false,
				// 每月天数
				daysArr: {
					1: 31,
					3: 31,
					5: 31,
					7: 31,
					8: 31,
					10: 31,
					12: 31,
					4: 30,
					6: 30,
					9: 30,
					11: 30,
					2: 28
				},
				// 每月天数
				monthDayArr: {},
				// 年集合
				yearArr: [],
				// 月集合
				monthArr: [],
				// 记录开始月份
				beginMouthArr: [],
				// 记录开始月的天数
				beginDaysArr: [],
				// 记录传进来的开始日期
				beginRQ: [],
				// 记录传进了结束月份
				endMouthArr: [],
				// 记录结束日
				endDaysArr: [],
				// 结束的开始日期
				endRQ: [],
				// 活跃的月，默认一月
				activeMonthIndex: 1,
				columnArr2: [],
        selectValue:[]
			}
		},
		methods: {
			bindChange(e) {
				const val = e.detail.value;
				var oneIndex = val[0];
				var twoIndex = val[1];
				var threeIndex = val[3];
				console.log(val)
        // this.selectIndex = val

				if (this.type == 'date') {
					var columnArr = this.columnArr2
					// 月份
					var mountArr = []
					// 日
					var dayArr = []

					// 选中年等于起始年
					if (columnArr[0][oneIndex].label == this.beginRQ[0]) {
						mountArr = this.beginMouthArr
						if (this.column == 3) {
							// 选中月等于起始月
							if (columnArr[1][twoIndex].label == this.beginRQ[1]) {
								dayArr = this.beginDaysArr
							} else {
								dayArr = this.monthDayArr[twoIndex + 1]
							}
						}
					} else if (this.endTimer != 'now' && columnArr[0][oneIndex].label == this.endRQ[
							0]) { // 传了结束时间,最后一年
						mountArr = this.endMouthArr
						if (this.column == 3) {
							// 月份等于结束月份
							if (columnArr[1][twoIndex].label == this.endRQ[1]) {
								dayArr = this.endDaysArr
							} else {
								dayArr = this.monthDayArr[twoIndex + 1]
							}
						}
					} else {
						mountArr = this.monthArr;
						if (this.column == 3) {
							dayArr = this.monthDayArr[twoIndex + 1]
						}
					}
					if (this.column == 3) {
						this.$set(this.columnArr2, 2, dayArr);
						this.$set(this.columnArr2, 1, mountArr);
						// this.setData({
						// 	[`columnArr[2]`]: dayArr,
						// 	[`columnArr[1]`]: mountArr
						// })
					} else {
						this.$set(this.columnArr2, 1, mountArr);
						// this.setData({
						// 	// [`columnArr[2]`]: dayArr,
						// 	[`columnArr[1]`]: mountArr
						// })
					}

				}



				// 记录选中索引值
				this.selectIndex = val

				// return

			},
			// 确定
			queryClick() {
				if (this.type == 'date') {
					if (this.selectIndex.length == 0) {
					  this.selectIndex = this.selectValue
					}
          setTimeout(() => {
          console.log('this.selectIndex',JSON.parse(JSON.stringify(this.selectIndex)))
					var resultStr;
					var arr = this.columnArr2;
					if (this.column == 3) {
						resultStr = arr[0][this.selectIndex[0]].label + '-' + arr[1][this.selectIndex[1]].label +
							'-' + arr[2][this.selectIndex[2]].label
					} else if (this.column == 2) {
						resultStr = arr[0][this.selectIndex[0]].label + '-' + arr[1][this.selectIndex[1]].label
					}
     //      console.log({
					// 	selectIndex: this.selectIndex,
					// 	selectValue: resultStr
					// })

            this.$emit('query', {
            	selectIndex: this.selectIndex,
            	selectValue: resultStr
            })
          },500)
					return
				} else {
					// 没有选直接第一项
					if (this.selectIndex.length == 0) {
						for (var i = 0; i < this.columnArr.length; i++) {
							this.selectIndex.push(0)
						}
					}
					this.$emit('query', {
						selectIndex: this.selectIndex,
						selectValue: '',
						// selectArr:[]
					})
				}
				this.visible = false;
				// this.setData({
				// 	visible: false
				// })
			},
			// 取消
			closeClick() {
				if (this.show) {
					this.$emit('cancel')
				}
				this.visible = false;
				// this.setData({
				// 	visible: false
				// })
			},
      //
      getdefaultIndex(selectValue){
        // 选中数组索引
        var alwayIndex = []
        // 截取开始时间
        var yyArr = this.beginTimer.split('-')
        // this.beginRQ = yyArr;
        var beginYear = yyArr[0];
        var beginMount = yyArr[1] || 1;
        var beginday = yyArr[2] || 1;

        // 结束月份
        var endMount = 1;
        // 结束日
        var endDay = 1;
        // 结束年份
        var endYear;

        var date = new Date();

        endYear = date.getFullYear()
        endMount = date.getMonth() + 1;
        endDay = date.getDate();


        // 生成年
        let yearArr = [];
        for (let i = beginYear; i <= date.getFullYear(); i++) {
        	yearArr.push(i)
          if (i == selectValue[0]) {
        		alwayIndex.push(yearArr.length - 1);
        	}
        }
        // 生产年之后如果选中年值大于区间最后时间
        if (alwayIndex.length == 0) {
        	alwayIndex.push(yearArr.length - 1)
        }
        selectValue[1] = selectValue[1] - 0 - 1;
        selectValue[2] = selectValue[2] - 0 - 1;
        alwayIndex.push(selectValue[1]);
        alwayIndex.push(selectValue[2])

        return alwayIndex;


      },
		},
		watch: {
			show(params) {
				if (params) {
          console.log('kkk')
          if (this.type == 'date') {
            let selectValue = [];
            if (this.value == 'now') {
            	var tempArr = new Date()
            	var one = tempArr.getFullYear()
            	var two = tempArr.getMonth() + 1;
            	var three = tempArr.getDate()
            	selectValue = [one, two, three]
            } else {
            	selectValue = this.value.split('-')
            }
            selectValue = this.getdefaultIndex(selectValue);
            // console.log(this.dayArr)
            // this.columnArr2[2] = this.dayArr[selectValue[1]]
            this.columnArr2[2] = this.monthDayArr[selectValue[1] + 1]

            this.selectValue = selectValue;
          }

          console.log('this.selectValue',this.selectValue)
					this.visible = true;

				} else {
					this.visible = false;

				}
			},
			columnArr(val) {
				this.columnArr2 = val;
			}
		},
		mounted() {
			// 类型为日期
			if (this.type == 'date') {
				if (this.value == 'now') {
					var tempArr = new Date()
					var one = tempArr.getFullYear()
					var two = tempArr.getMonth() + 1;
					var three = tempArr.getDate()
					this.selectValue = [one, two, three]
				} else {
					this.selectValue = this.value.split('-')
				}
        console.log('this.selectValue',this.selectValue)
				// 选中数组索引
				var alwayIndex = []
				// console.log(this.selectValue);
				// 初始化默认选中值
				// 截取开始时间
				var yyArr = this.beginTimer.split('-')
				this.beginRQ = yyArr;
				var beginYear = yyArr[0];
				var beginMount = yyArr[1] || 1;
				var beginday = yyArr[2] || 1;
				// 年
				var yearArr = []
				// 月
				var monthArr = []
				// 日
				var dayArr = {}
				var temp;
				var date;
				// 结束月份
				var endMount = 1;
				// 结束日
				var endDay = 1;
				// 结束年份
				var endYear;
				if (this.endTimer == 'now') {
					date = new Date();
					// endYear = date.getFullYear()
					// endMount = date.getMonth() + 1;
					// endDay = date.getDate();
				} else {
					date = new Date(this.endTimer)
					// endYear = date.getFullYear()
					// endMount = date.getMonth() + 1;
					// endDay = date.getDate();
					// this.endRQ = [
					//   date.getFullYear(),
					//   endMount,
					//   endDay
					// ]
				}
				endYear = date.getFullYear()
				endMount = date.getMonth() + 1;
				endDay = date.getDate();
				this.endRQ = [
					date.getFullYear(),
					endMount,
					endDay
				]
				// console.log(endDay)
				// console.log(endMount)
				// 生成年
				for (let i = beginYear; i <= date.getFullYear(); i++) {
					temp = {
						label: i,
						until: '年'
					}
					yearArr.push(temp)
					if (i == this.selectValue[0]) {
						alwayIndex.push(yearArr.length - 1);
					}
				}
				// 生产年之后如果选中年值大于区间最后时间
				if (alwayIndex.length == 0) {
					alwayIndex.push(yearArr.length - 1)
				}
				// 生成月
				monthArr = [{
					label: '1',
					until: '月'
				}, {
					label: 2,
					until: '月'
				}, {
					label: 3,
					until: '月'
				}, {
					label: 4,
					until: '月'
				}, {
					label: '5',
					until: '月'
				}, {
					label: '6',
					until: '月'
				}, {
					label: '7',
					until: '月'
				}, {
					label: '8',
					until: '月'
				}, {
					label: '9',
					until: '月'
				}, {
					label: '10',
					until: '月'
				}, {
					label: '11',
					until: '月'
				}, {
					label: '12',
					until: '月'
				}]
				// 记录天数
				this.monthDayArr = dayArr;
				if (beginMount > 12) {
					beginMount = 12;
				}
				this.monthArr = monthArr;

				// 截取开始月份
				this.beginMouthArr = monthArr.slice(beginMount - 1);
				// 截取结束月份
				this.endMouthArr = monthArr.slice(0, endMount)
				this.yearArr = yearArr;
        console.log('this.yearArr',this.yearArr)

				// 初始化选中值
				// this.selectValue


				if (this.column == 3) {
					var days = {
						31: [{
							label: '1',
							until: '日'
						}, {
							label: 2,
							until: '日'
						}, {
							label: 3,
							until: '日'
						}, {
							label: 4,
							until: '日'
						}, {
							label: '5',
							until: '日'
						}, {
							label: '6',
							until: '日'
						}, {
							label: '7',
							until: '日'
						}, {
							label: '8',
							until: '日'
						}, {
							label: '9',
							until: '日'
						}, {
							label: '10',
							until: '日'
						}, {
							label: '11',
							until: '日'
						}, {
							label: '12',
							until: '日'
						}, {
							label: '13',
							until: '日'
						}, {
							label: 14,
							until: '日'
						}, {
							label: 15,
							until: '日'
						}, {
							label: 16,
							until: '日'
						}, {
							label: '17',
							until: '日'
						}, {
							label: '18',
							until: '日'
						}, {
							label: '19',
							until: '日'
						}, {
							label: '20',
							until: '日'
						}, {
							label: '21',
							until: '日'
						}, {
							label: '22',
							until: '日'
						}, {
							label: '23',
							until: '日'
						}, {
							label: '24',
							until: '日'
						}, {
							label: '25',
							until: '日'
						}, {
							label: 26,
							until: '日'
						}, {
							label: 27,
							until: '日'
						}, {
							label: 28,
							until: '日'
						}, {
							label: '29',
							until: '日'
						}, {
							label: '30',
							until: '日'
						}, {
							label: '31',
							until: '日'
						}],
						30: [{
							label: '1',
							until: '日'
						}, {
							label: 2,
							until: '日'
						}, {
							label: 3,
							until: '日'
						}, {
							label: 4,
							until: '日'
						}, {
							label: '5',
							until: '日'
						}, {
							label: '6',
							until: '日'
						}, {
							label: '7',
							until: '日'
						}, {
							label: '8',
							until: '日'
						}, {
							label: '9',
							until: '日'
						}, {
							label: '10',
							until: '日'
						}, {
							label: '11',
							until: '日'
						}, {
							label: '12',
							until: '日'
						}, {
							label: '13',
							until: '日'
						}, {
							label: 14,
							until: '日'
						}, {
							label: 15,
							until: '日'
						}, {
							label: 16,
							until: '日'
						}, {
							label: '17',
							until: '日'
						}, {
							label: '18',
							until: '日'
						}, {
							label: '19',
							until: '日'
						}, {
							label: '20',
							until: '日'
						}, {
							label: '21',
							until: '日'
						}, {
							label: '22',
							until: '日'
						}, {
							label: '23',
							until: '日'
						}, {
							label: '24',
							until: '日'
						}, {
							label: '25',
							until: '日'
						}, {
							label: 26,
							until: '日'
						}, {
							label: 27,
							until: '日'
						}, {
							label: 28,
							until: '日'
						}, {
							label: '29',
							until: '日'
						}, {
							label: '30',
							until: '日'
						}],
						28: [{
							label: '1',
							until: '日'
						}, {
							label: 2,
							until: '日'
						}, {
							label: 3,
							until: '日'
						}, {
							label: 4,
							until: '日'
						}, {
							label: '5',
							until: '日'
						}, {
							label: '6',
							until: '日'
						}, {
							label: '7',
							until: '日'
						}, {
							label: '8',
							until: '日'
						}, {
							label: '9',
							until: '日'
						}, {
							label: '10',
							until: '日'
						}, {
							label: '11',
							until: '日'
						}, {
							label: '12',
							until: '日'
						}, {
							label: '13',
							until: '日'
						}, {
							label: 14,
							until: '日'
						}, {
							label: 15,
							until: '日'
						}, {
							label: 16,
							until: '日'
						}, {
							label: '17',
							until: '日'
						}, {
							label: '18',
							until: '日'
						}, {
							label: '19',
							until: '日'
						}, {
							label: '20',
							until: '日'
						}, {
							label: '21',
							until: '日'
						}, {
							label: '22',
							until: '日'
						}, {
							label: '23',
							until: '日'
						}, {
							label: '24',
							until: '日'
						}, {
							label: '25',
							until: '日'
						}, {
							label: 26,
							until: '日'
						}, {
							label: 27,
							until: '日'
						}, {
							label: 28,
							until: '日'
						}, {
							label: '29',
							until: '日'
						}],
					}
					// 生成日
					for (var i = 0; i < monthArr.length; i++) {
						// temp = days[this.daysArr[i + 1]];
						dayArr[i + 1] = days[this.daysArr[i + 1]];
						// 选中索引
						if (monthArr[i].label == this.selectValue[1]) {
							alwayIndex.push(i);
						}
					}
					// 选中索引
					alwayIndex.push(this.selectValue[2] - 1);
					// console.log(alwayIndex)
					// 截取开始天数
					this.beginDaysArr = dayArr[1].slice(beginday - 1);
					// 截取结束天数
					this.endDaysArr = dayArr[endMount].slice(0, endDay)
					// 如果选中值年份不等于指定开始年份，或者不等于结束年份
					if (this.selectValue[0] != beginYear && this.selectValue[0] != endYear) {
						// 月份是完整的
						this.columnArr2 = [yearArr, monthArr, dayArr[alwayIndex[1]]];
						this.selectValue = alwayIndex;
						// this.setData({
						// 	columnArr: [yearArr, monthArr, dayArr[alwayIndex[1]]],
						// 	selectValue: alwayIndex
						// })
					} else if (this.selectValue[0] == beginYear) {
						// console.log('jjj')
						// 初始值等于开始年份
						alwayIndex[1] = alwayIndex[1] - (beginMount - 1);
						// 初始月等于开始月份
						if (this.selectValue[1] == beginMount) {
							this.columnArr2 = [yearArr, this.beginMouthArr, this.beginDaysArr];
							this.selectValue = alwayIndex;
							// this.setData({
							// 	columnArr: [yearArr, this.beginMouthArr, this.beginDaysArr],
							// 	selectValue: alwayIndex
							// })
						} else {
							this.columnArr2 = [yearArr, this.beginMouthArr, dayArr[alwayIndex[1]]];
							this.selectValue = alwayIndex;
							// this.setData({
							// 	columnArr: [yearArr, this.beginMouthArr, dayArr[alwayIndex[1]]],
							// 	selectValue: alwayIndex
							// })
						}

					} else if (this.selectValue[0] == endYear) {
						// 初始值等于结束年份
						// alwayIndex[1] = alwayIndex[1]
						// console.log(endMount)
						// console.log(this.selectValue[1] == endMount)
						// console.log(dayArr[alwayIndex[1]])
						// console.log()
						// 初始月等于开始月份
						if (this.selectValue[1] == endMount) {
							this.columnArr2 = [yearArr, this.endMouthArr, this.endDaysArr];
							this.selectValue = alwayIndex;
							// this.setData({
							// 	columnArr: [yearArr, this.endMouthArr, this.endDaysArr],
							// 	selectValue: alwayIndex
							// })
						} else {
							this.columnArr2 = [yearArr, this.endMouthArr, dayArr[alwayIndex[1]]];
							this.selectValue = alwayIndex;
							// this.setData({
							// 	columnArr: [yearArr, this.endMouthArr, dayArr[alwayIndex[1]]],
							// 	selectValue: alwayIndex
							// })
						}
					}
					// this.setData({
					//   columnArr: [yearArr, this.beginMouthArr, this.beginDaysArr],
					//   selectValue:alwayIndex
					// })
				} else {
					// console.log(monthArr)

					alwayIndex.push(this.selectValue[1] - 1);
					// console.log(alwayIndex)
					if (this.selectValue[0] == beginYear) {
						alwayIndex[1] = alwayIndex[1] - (beginMount - 1);
						this.columnArr2 = [yearArr, this.beginMouthArr];
						this.selectValue = alwayIndex;
						// this.setData({
						// 	columnArr: [yearArr, this.beginMouthArr],
						// 	selectValue: alwayIndex
						// })
					} else if (this.selectValue[0] == endYear) {
						this.columnArr2 = [yearArr, this.endMouthArr];
						this.selectValue = alwayIndex;
						// this.setData({
						// 	columnArr: [yearArr, this.endMouthArr],
						// 	selectValue: alwayIndex
						// })
					} else {
						this.columnArr2 = [yearArr, monthArr];
						this.selectValue = alwayIndex;
						// this.setData({
						// 	columnArr: [yearArr, monthArr],
						// 	selectValue: alwayIndex
						// })
					}


				}

			}
		}

	}
</script>

<style scoped>
	/* components/picket/index.wxss */
	/* miniprogram/pages/ceshi2/index.wxss */

	/* 阴影 */
	.ly-shadow {
		position: fixed;
		z-index: 1;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
	}

	.pickBox {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100vw;
		height: 100vh;
		z-index: 99;
		/* background-color: #fff; */
	}

	.pickBox .pick {
		position: absolute;
		bottom: 0;
		height: 300px;
		width: 100vw;
		background-color: #fff;
	}

	.pickBox .pick-t {
		display: flex;
		align-items: center;
		height: 100rpx;
		/* background-color: red; */
		padding: 0 20rpx;
		font-size: 32rpx;
		box-sizing: border-box;
	}

	.pickBox .pick-t-l {
		color: #7c7c7c;
	}

	.pickBox .pick-t .pick-t-r {
		flex: 1;
		display: flex;
		justify-content: flex-end;
		/* color: #2896c8; */
    color: $topicC;
	}
</style>
