<template>
  <view class='bottom-info-box' @click.stop>
    <view class="bottom-info-user">
      <view class="bottom-info-avator">
        <view class="heardimg-box" :style="data.lightLogoPath ? 'padding: 0 8rpx;' : ''">
          <image v-if="data.lightLogoPath" class="headimg-wrapper" :src="isDomainUrl(data.lightLogoPath)" mode="aspectFill" @click="userClick(data)"></image>
					<image class="heardimg" :src="data.headPath ? isDomainUrl(data.headPath) : defaultAvatar" mode="aspectFill" @click="userClick(data)" ></image>
					<template v-if="data.isAddV == 1">
						<image v-if="data.vType == 2" class="heardimg-addv-icon" :src="isDomainUrl('static/image/system/avatar/icon-v-e.png')" mode="aspectFill"></image>
						<image v-else-if="data.vType == 1" class="heardimg-addv-icon" :src="isDomainUrl('static/image/system/avatar/icon-v.png')" mode="aspectFill"></image>
					</template>
				</view>
      </view>
      <view class="bottom-info-user-t">
        {{data.nickName}}
      </view>
      <!-- <button class="bottom-info-gz" @click="clickGz">
        +关注
      </button> -->
    </view>
    <view class="buttom-info-title">
      {{data.title}}
    </view>
    <view class="buttom-info-desc">
      {{data.intro}}
      <template v-if="topicIdsList.length">
        <span class="topic-item" v-for="item in topicIdsList" :key="item.id" @click="handleLookTopic(item)"># {{ item.name }}</span>
      </template>
    </view>
  </view>
</template>

<script>
  import { isDomainUrl } from '@/utils/index'
  export default {
    props:{
      data: {
        type: Object,
        default: function() {
          return {};
        }
      },
    },
    data() {
      return {
        isDomainUrl,
        defaultAvatar: this.$static_ctx + 'image/system/avatar/icon-default-avatar.png'
      }
    },
    computed: {
      topicIdsList(){
       return this.data.topicIds && JSON.parse(this.data.topicIds) || []
      }
    },
    methods:{
      handleLookTopic(item){
        this.$navto.push('GambitDetail',{id:item.id})
      },
      userClick(data) {
        console.log("json",JSON.stringify(data))
      	if (data.isAnonymity == 1) {
      		this.$uniPlugin.toast('该帖子为匿名发表，无法查看该用户')
      		return
      	}
      	const accountId = data.accountId
      	// 是否是自己
      	if (this.$common.getKeyVal('user', 'accountId') === accountId) {
      		this.$navto.pushTab('Personal', {})
      	} else {
      		this.navtoGo('PersonalHomePage', { homePageAccountId:accountId })
      	}
      },
      navtoGo(url, obj = {}) {
      	this.$navto.push(url, obj)
      },
      // 点击了关注
      clickGz() {
        console.log('点击了关注')
      }
    }
  }
</script>

<style scoped lang="scss">
.bottom-info-box{
  width: 100vw;
  position: absolute;
  bottom: 0;
  background: linear-gradient(rgba(0,0,0,0), rgba(0,0,0,.4));
  color:#fff;
  padding: 0 110rpx 70rpx 30rpx;
  box-sizing: border-box;
  // padding-bottom: constant(safe-area-inset-bottom); /* 兼容 iOS < 11.2 */
  // padding-bottom: env(safe-area-inset-bottom); /* 兼容 iOS > 11.2 */
}
.bottom-info-user{
  display:flex;
  align-items: center;
}

.bottom-info-avator{
  width:80upx;
  height:80upx;
  border:1upx solid #f75578;
  margin-right:20upx;
  border-radius:50%
}
.bottom-info-user-t {
  display: inline-block;
  font-weight: 600;
  @include ellipsis(1);
}
.buttom-info-title{
  font-weight: 600;
  margin-top:20upx;
  font-size:28upx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.bottom-info-gz{
  margin-left:20upx;
  background-color:#f75578;
  padding:0 20upx;
  line-height: 2;
  @include rounded(36rpx);
  font-size:28upx;
  color:#fff
}
.heardimg-box {
  position: relative;
	width: 100%;
	height: 100%;
	background: #fff;
  // @include rounded(50%);
  border-radius: 50%;
	.heardimg {
		width: 100%;
		height: 100%;
		@include rounded(50%);
	}
	.heardimg-addv-icon {
		position: absolute;
		bottom: 0;
		right: 0;
		width: 30rpx;
		height: 30rpx;
	}
}
.buttom-info-desc {
  display: flex;
  flex-wrap: wrap;
  color: #fff;
  @include ellipsis(2);
  .topic-item{
    margin-right: 10rpx;
    margin-bottom: 10rpx;
  }
}
</style>
