<template>
  <view class='rumour-ranking-list'>
    <view class="my-data" :style="{'background-image':'url('+ file_ctx +'static/image/business/pharmacy-cyclopedia/icon-rumour-ranking-list-bg.png)','background-size': '100%'}">
      <view :style="'height:' + statusBarHeight + 'px;'"></view>
      <view class="top-nav">
        <view class="top-nav-l">
          <!-- #ifndef MP-ALIPAY -->
          <image mode="aspectFit" @click.stop="handleBack" :src="file_ctx + 'static/image/business/hulu-v2/icon-circle-left-arrows.png'" class="header-search-img"/>
          <!-- #endif -->
          <!-- #ifdef MP-ALIPAY -->
          <view class="header-search-img"></view>
          <!-- #endif -->
          <view class="rule" @click.stop="handleClickListRule">榜单规则</view>
        </view>
        <view class="top-nav-c">辟谣排行榜</view>
      </view>

      <!-- 周排行 -->
      <view class="ranking-top">
        <view 
          :class="currentIndex == index ? 'ranking-item active' : 'ranking-item'" 
          v-for="(item,index) in rankingTopList" 
          :key="item.id" 
          @click="handleClickIndex(index)"
        >
          {{ item.name }}
        </view>
      </view>

      <!-- 排行榜单 -->
      <view class="ranking-list" :style="{'background-image':'url('+ file_ctx +'static/image/business/pharmacy-cyclopedia/icon-rumour-ranking-list-new-bg.png)','background-size': '100%'}">
        <view class="ranking-content" v-if="rankingList.length">
          <view class="ranking-list-item" v-for="(item,index) in rankingList.slice(0, 3)" :key="item.id">
            <view class="item-img" v-if="index == 0" :style="{'background-image':'url('+ file_ctx +`${item.avatar});background-size: 100%;`}">
              <image class="item-one img" :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-rumour-ranking-list-one.png'"></image>
            </view>
            <view class="item-img" v-else-if="index == 1" :style="{'background-image':'url('+ file_ctx +`${item.avatar});background-size: 100%;`}">
              <image class="item-two img" :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-rumour-ranking-list-two.png'"></image>
            </view>
            <view class="item-img" v-else-if="index == 2" :style="{'background-image':'url('+ file_ctx +`${item.avatar});background-size: 100%;`}">
              <image class="item-three img" :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-rumour-ranking-list-three.png'"></image>
            </view>
          </view>
        </view>
        <view class="ranking-text-list">
          <view class="text-list-item" v-for="item in rankingList.slice(0, 3)" :key="item.id">
            <view class="title">{{ item.nickName }}</view>
            <view class="answer-question">答对<span>{{ item.rightNum }}</span>题</view>
          </view>
        </view>
      </view>
    </view>
    <scroll-view class="bottom-scroll" scroll-y>
      <view class="weekList" v-if="rankingList.length">
        <view class="week-item" v-for="(item,index) in rankingList.slice(3)" :key="index">
          <view class="week-item-l">
            <view class="item-index">{{ index +4 }}</view>
            <view class="item-profile"><image class="img" :src="file_ctx + item.avatar"></image></view>
            <view class="item-title">{{ item.nickName }}</view>
          </view>
          <view class="week-item-r">
            <view class="item-answer">答对<span>{{ item.rightNum }}</span>题</view>
          </view>
        </view>
        <view class="self-week-item" v-if="selfWeekObj.nickName">
          <view class="self-item-l">
            <view class="item-profile"><image class="img" :src="file_ctx + selfWeekObj.avatar"></image></view>
            <view class="name-box">
              <view class="title">{{ selfWeekObj.nickName }}</view>
              <view class="my-rank">我的排名：<span>{{ selfWeekObj.rank }}</span></view>
            </view>
          </view>
          <view class="self-item-r">
            <view class="item-answer">答对<span>{{ selfWeekObj.rightNum }}</span>题</view>
          </view>
        </view>
      </view>
      <view class="weekList-empty" v-else>
        <image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-empty-ranking-list-data.png'"></image>
        <view class="text">暂无榜单数据</view>
      </view>
    </scroll-view>
    <uni-popup class="my-uni-popup" ref="carouselPopups" type="center">
      <view class="carousel-popup-img" @click="$refs.carouselPopups.close()"><image class="img" :src="file_ctx + 'static/image/business/hulu-v2/icon-home-popup-error.png'"></image></view>
      <view class="popup-content">
        <p>排行榜规则</p>
        <p>1）当周累计答对题量越多，排行名次越前。</p>
        <p>2）当周累计答对题量相同，以答题总耗时最短为名次优先排行。</p>
        <p>3）当周累计答题量及答题总耗时相同，以最近一次答对题目时间记录作对比，时间早的名次优先排行。</p>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import { mapState } from 'vuex'
  import uniPopup from '@/components/uni/uni-popup'
  export default {
    components: {
      uniPopup
    },
    data(){
      return{
        file_ctx: this.file_ctx,
        statusBarHeight: 0,
        rankingTopList:[
          {id:1,name:'上周排行'},
          {id:2,name:'本周排行'},
        ],
        currentIndex:1,
        rankingList:[],
        selfWeekObj:{}, //本人所在排行
      }
    },
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId,
      }),
    },
    onLoad(){
      this.init()
      this.refuterumorweekstatisticsQueryRank()
    },
    mounted(){
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
    },
    methods:{
      // 榜单规则
      handleClickListRule(){
        this.$refs.carouselPopups.open()
      },
      // 本人所在排行榜
      async refuterumorweekstatisticsQueryRank(){
        let params = {
          accountId:this.accountId,
          thisWeek:this.currentIndex == 1 ? true : false, // true本周 false 上周
        }
        const res = await this.$api.drugBook.refuterumorweekstatisticsQueryRank(params)
        this.selfWeekObj = res.data
      },
      async init(){
        let res = null
        if(this.currentIndex == 0){
          res = await this.$api.drugBook.refuterumorweekstatisticsQueryLastWeekRank({}) //上周
        } else {
          res = await this.$api.drugBook.refuterumorweekstatisticsQueryWeekRank({}) //本周
        }
        let data = res.data
        this.rankingList = data
      },

      handleClickIndex(index){
        this.currentIndex = index
        this.init()
        this.refuterumorweekstatisticsQueryRank()
      },
      handleBack(){
        this.$navto.back(1)
      },
    },
 }
</script>

<style lang='scss' scoped>
@mixin contentFlex {
  display: flex;
  align-items: center;
  justify-content: center;
}
.img{
  width: 100%;
  height: 100%;
}
.my-uni-popup{
  position: relative;
  /deep/.uni-popup{
    .uni-popup__mask{
      background: rgba(0, 0, 0, 0.4) !important;
    }
  }
}
.popup-content{
  padding: 30rpx;
  border-radius: 20rpx;
  background-color: #fff;
  p{
    line-height: 50rpx;
    &:first-child{
      text-align: center;
      margin-bottom: 10rpx;
    }
  }
}
.carousel-popup-img{
  display: flex;
  margin-left: auto;
  margin-bottom: 16rpx;
  width: 32rpx;
  height: 32rpx;
}
.rumour-ranking-list{
  height: 100vh;
  display: flex;
  flex-direction: column;
  .my-data{
    position: relative;
    width: 750rpx;
    height: 708rpx;
    .top-nav{
      position: relative;
      width: calc(100% - 56rpx);
      @include contentFlex;
      height: 40px;
      line-height: 40px;
      padding: 0 32rpx 0 24rpx;
      .top-nav-l{
        @include contentFlex;
        position: absolute;
        height: 100%;
        left: 24rpx;
        top: 0;
        .header-search-img{
          display: flex;
          width: 48rpx;
          height: 48rpx;
          margin-right: 24rpx;
        }
        .rule{
          font-size: 28rpx;
          color: #1D2029;
          line-height: 40rpx;
          background: rgba(255,255,255,0.3);
          border-radius: 32rpx;
          padding: 12rpx 24rpx;
        }
      }
      .top-nav-c{
        @include contentFlex;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        top: 0;
        height: 100%;
        font-weight: 500;
        font-size: 32rpx;
        color: #1D2029;
        margin-right: 48rpx;
      }
    }
    .ranking-top{
      position: absolute;
      @include contentFlex;
      padding: 4rpx;
      box-sizing: border-box;
      left: 50%;
      transform: translateX(-50%);
      width: 328rpx;
      height: 72rpx;
      background: rgba(255,255,255,0.9);
      border-radius: 36rpx;
      margin-top: 32rpx;
      .ranking-item{
        @include contentFlex;
        width: 160rpx;
        height: 64rpx;
        border-radius: 36rpx;
      }
      .active{
        background: #00B484;
        color: #fff;
      }
    }
    .ranking-list{
      position: absolute;
      top: 472rpx;
      left: 34rpx;
      height: 218rpx;
      width: 712rpx;
      .ranking-content{
        display: flex;
       .ranking-list-item{
        .item-img{
          position: relative;
          width: 120rpx;
          height: 120rpx;
          border: 6rpx solid #FFFFFF;
          border-radius: 50%;
          .item-one,.item-two,.item-three{
            position: absolute;
            width: 56rpx;
            height: 52rpx;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
          }
        }
        &:nth-child(1){
          position: absolute;
          left: 270rpx;        
          top: -144rpx;         
        }
        &:nth-child(2){
          position: absolute;
          left: 35rpx;
          top: -100rpx;
        }
        &:nth-child(3){
          position: absolute;
          left: 510rpx;
          top: -80rpx;
        }
       } 
      }
      .ranking-text-list{
        display: flex;
        .text-list-item{
          // text-align: center;
          @include contentFlex;
          flex-direction: column;
          .title{
            font-weight: 600;
            font-size: 28rpx;
            color: #1D2029;
            line-height: 40rpx;
            max-width: 100px; 
            text-overflow:ellipsis;
            overflow:hidden;
            white-space: nowrap;
          }
          .answer-question{
            font-size: 22rpx;
            color: #1D2029;
            span{
              font-weight: 600;
              font-size: 36rpx;
              color: #1D2029;
            }
          }
          &:nth-child(1){
            position: absolute;
            left: 190rpx;
            top: 74rpx;
            width: 290rpx;
          }
          &:nth-child(2){
            position: absolute;
            left: 0rpx;
            top: 100rpx;
            width: 190rpx;
          }
          &:nth-child(3){
            position: absolute;
            // left: 500rpx;
            left: 478rpx;
            top: 104rpx;
            width: 210rpx;
          }
        }
      }
    }
  }
  .bottom-scroll{
    position: relative;
    flex: 1;
    border-radius: 32rpx 32rpx 0rpx 0rpx;
    overflow: hidden;
    top: -20rpx;
    .weekList{
      position: relative;
      background-color: #fff;
      border-radius: 32rpx 32rpx 0rpx 0rpx;
      padding: 32rpx 40rpx 180rpx;
      .week-item{
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 28rpx;
        color: #1D2029;
        margin-bottom: 32rpx;
        .week-item-l{
          display: flex;
          align-items: center;
          .item-index{
            font-weight: 600;
          }
          .item-profile{
            display: flex;
            width: 96rpx;
            height: 96rpx;
            border-radius: 50%;
            overflow: hidden;
            margin: 0 20rpx 0 24rpx;
          }
          .item-title{
          }
        }
        .week-item-r{
          .item-answer{
            font-size: 22rpx;
            span{
              font-weight: 600;
              font-size: 36rpx;
            }
          }
        }
        &:last-child{
          margin-bottom: 0;
        }
      }
      .self-week-item{
        position: fixed;
        bottom: 0;
        left: 0;
        width: calc(100% - 72rpx);
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #E9F7F3;
        padding: 24rpx 32rpx 60rpx 40rpx;
        .self-item-l{
          display: flex;
          align-items: center;
          .item-profile{
            display: flex;
            width: 96rpx;
            height: 96rpx;
            margin-right: 20rpx;
            border-radius: 50%;
            overflow: hidden;
          }
          .name-box{
            .title{
              font-size: 28rpx;
              color: #1D2029;
              line-height: 40rpx;
            }
            .my-rank{
              font-size: 24rpx;
              color: #4E5569;
              span{
                font-size: 24rpx;
                color: #1D2029;
              }
            }
          }
        }
        .self-item-r{
          .item-answer{
            font-size: 22rpx;
            color: #1D2029;
            span{
              font-size: 36rpx;
              color: #1D2029;
              font-weight: 600;
            }
          }
        }
      }
    }
    .weekList-empty{
      @include contentFlex;
      flex-direction: column;
      height: 100%;
      image{
        display: flex;
        width: 248rpx;
        height: 242rpx;
      }
      .text{
        font-size: 24rpx;
        color: #4E5569;
        margin-top: 24rpx;
      }
    }
  }
}
</style>