import common from '@/common/util/main'

// #ifdef H5
const jweixin = require('jweixin-module')
// #endif
export default {
  /**
   * JSSDJ初始化配置
   * @param urlparam 地址参数对象集合
   * @param data 服务器后端返回结果对象
   * @param callback 外暴露回调方法
   */
  initiConfig(urlparam, data, callback) {
    jweixin.config({
      debug: false,
      appId: urlparam.appId,
      timestamp: data.result.timestamp,
      nonceStr: data.result.nonceStr,
      signature: data.result.signature,
      jsApiList: [
        'hideMenuItems', 'chooseImage', 'previewImage', 'uploadImage', 'downloadImage', 'updateAppMessageShareData', 'scanQRCode', 'getLocation', 'openLocation', 'closeWindow', 'updateTimelineShareData', 'onMenuShareTimeline', 'onMenuShareAppMessage', 'onMenuShareQQ', 'onMenuShareWeibo', 'onMenuShareQZone', 'getLocalImgData']
    })
    jweixin.ready(function() {
      jweixin.hideMenuItems({ // 要隐藏的菜单项，只能隐藏“传播类”和“保护类”按钮，所有menu项见附录3
        menuList: [
          // 'menuItem:share:appMessage', // 发送给朋友
          // 'menuItem:share:timeline', // 分享到朋友圈
          // 'menuItem:share:qq', // 分享到QQ
          // 'menuItem:share:weiboApp', // 分享到Weibo
          // 'menuItem:share:facebook', // 分享到FB
          // 'menuItem:share:QZone', // 分享到 QQ 空间
          // 'menuItem:copyUrl', // 复制链接
          // 'menuItem:openWithSafari', // 在Safari中打开
          // 'menuItem:share:email', // 邮件
          // 'menuItem:share:brand', // 一些特殊公众号
          // 'menuItem:openWithQQBrowser', // 在QQ浏览器中打开
          // 'menuItem:originPage', // 原网页
          // 'menuItem:refresh' // 刷新
        ]
      })
      // console.log('微信公众号JSSDK-验证成功')
      common.setKeyVal('system', 'isWechat', true)
      callback()
    })
    jweixin.error(function(res) {
      // console.log('微信公众号JSSDK-验证失败')
      // console.log('微信公众号JSSDK,res:', res)
      common.setKeyVal('system', 'isWechat', false)
    })
  },
  /**
   * 是否微信公众号JSSDK初始化成功状态
   */
  isWechatConfig() {
    return common.getKeyVal('system', 'isWechat') || false
  },
  /**
   * 判断是否在微信中
   * @returns {boolean}
   */
  isWechatEnv: function() {
    var ua = window.navigator.userAgent.toLowerCase()
    if (ua.match(/micromessenger/i) == 'micromessenger') {
      // console.log('微信公众号客户端')
      return true
    } else {
      // console.log('非微信公众号客户端')
      return false
    }
  },
  /**
   * 调起微信扫一扫接口
   * @param callback
   */
  scanQRCode(callback) {
    jweixin.scanQRCode({
      desc: 'scanQRCode desc',
      needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
      scanType: ['qrCode', 'barCode'], // 可以指定扫二维码还是一维码，默认二者都有
      success: function(res) {
        const result = res.resultStr // 当needResult 为 1 时，扫码返回的结果
        callback(result)
      }
    })
  },
  /**
   * 获取当前地理信息,默认为gcj02
   * @param success
   * @param fail
   */
  getLocation(success, fail) {
    jweixin.ready(function() {
      jweixin.getLocation({
        type: 'gcj02', // 默认为wgs84的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
        success: function(res) {
          if (success) {
            success(res)
          }
        },
        fail: function(res) {
          if (fail) {
            fail(res)
          }
        }
      })
    })
  }
}
