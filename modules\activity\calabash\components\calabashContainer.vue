<template>
  <view>
    <!-- 金币数展示 -->
    <view class="goldNumBox" @click="gotoPages('/modules/activity/calabash/myLuckyCoin')" :style="{'background-image':'url(' + goldNumBox + ')'}">
     <image class='headPath' :src="headPath" mode="aspectFill"></image>
     <view class="goldNum">{{goldNum}}</view>
     <view class="goldSign">金币></view>
    </view>
    <!-- 页面跳转 -->
    <image class="exchangeGift" @click="gotoPages('/modules/activity/calabash/exchangeWings/integrationShop')" :style="{top:(windowTop + 15)+'px'}" :src="exchangeGift"></image>
    <image class="calabashdailyPrayers" @click="gotoPages('/modules/pharmacy/pharmacy-cyclopedia/everyday-rumour/activity-prefecture')" :style="{top:(windowTop + 15)+'px'}" :src="calabashdailyPrayers"></image>
    <!-- 金币 -->
    <view class="goldBox">
      <view class="goldBoxSon" v-for="(item,index) in initGoldMap.slice(0,4)" @click="receiveGold(index,item.goldNum)" :key="index">
        <view class="goldShell">
          <image v-if="!showGoldFellMap[index]" :src="calabashGoldBox" class="goldBubble initBubble"  mode="aspectFill"></image>
          <view v-if="!showGoldFellMap[index]" class="gold"></view>
          <text v-if="!showGoldFellMap[index]" class="goldMinNum">+{{item.goldNum}}</text>
          <image  v-if="showGoldFellMap[index]" :src="goldBubble+'?key='+Math.random()" class="goldBubble" mode="aspectFill"></image>
          <view v-if="!showGoldFellMap[index]" class="botTitle">{{currentGoldTitleMap[index]}}</view>
          <!-- 领取金币掉落集合 -->
          <view v-show="showGoldFellMap[index]" class="fellFellBox">
            <!-- <view class="goldFell" :class='"fellSign" + index' v-for="(item,fellIndex) in item.goldNum" :key="fellIndex"> -->
              <!-- <view class="gold fell"></view> -->
            <!-- </view> -->
          </view>
        </view>
      </view>
    </view>
    <!-- 画布 -->
    <view class="calabashBox">
      <view id="calabashTank"></view>
      <image ref="calabashBg" class="calabashBg" :src="this.calabash" mode="aspectFill"></image>
    </view>
  </view>
</template>

<script>
  // 为了避免vue监听发动机的数据变化 造成加载缓慢 所以使用一个外部对象来存储发动机实例
  let matterObjects = {};
  import getStrokeCall from  "./readerStroke.js"
  const calabashGold = "image/business/hulu-v2/calabashGold.png"
  export default{
    props:{
      initGoldNum:{
        type:Number,
        default:0
      },
      goldMaxNum:{
        type:Number,
        default:0
      },
      // 金币集合
      initGoldMap:{
        type:Array,
        default:[]
      }
    },
    data() {
      return {
        headPath:'',
        file_ctx: this.file_ctx,
        windowTop: 0,
        calabashBg: this.$static_ctx + "image/business/hulu-v2/calabashBg.png",
        calabashGold: this.$static_ctx + calabashGold,
        calabash: this.$static_ctx + "image/business/hulu-v2/calabash.png",
        goldNumBox: this.$static_ctx + "image/business/hulu-v2/goldNumBox.png",
        calabashGoldBox: this.$static_ctx + "image/business/hulu-v2/calabashGoldBox.png",
        calabashdailyPrayers: this.$static_ctx + "image/business/hulu-v2/calabashdailyPrayers.gif",
        exchangeGift: this.$static_ctx + "image/business/hulu-v2/exchangeGift.gif",
        goldBubble: this.$static_ctx + "image/business/hulu-v2/goldBubble.gif",
        MatterTank: null,
        addGoldLock: false,
        alpha: 0,
        beta: 0,
        gamma: 0,
        // 模型配置
        matterOptions: {
          wireframes: false,
          // showIds: true, // 显示实体ID
          // showVelocity: true, // 显示速度矢量
          // showSleeping: true, // 显示睡眠状态
           // showDebug: true, // 显示调试信息
           wireframes: false,//不显示线框
          background: 'transparent'
        },
        // 金币配置
        goldBody: {
          restitution: 0.1,
          friction: 0.1,
          mass: 0.1,
          density: 0.001,
          sleepThreshold: 0.1, // 0.5秒后自动进入睡眠状态
          collisionDetection: {
              mode: 'ccd'
          },
          maxSpeed: 1,
          collisionFilter: {
            group: 0x0001
          }, //减少碰撞次数
          render: {
            // fillStyle: 'black',
            // 渲染贴图路径
            sprite: {
              texture: this.$static_ctx + calabashGold,
              xScale: 1/3/2 * 0.85,
              yScale: 1/3/2 * 0.85
            }
          }
        },
        GourdTracks:[],
        // 判断是否展示金币掉落动画
        showGoldFellMap:[false,false,false,false],
        receiveLock:false,
        goldNum:0,
        openPop:false,
        goldDroppingHeight:0,
        initGoldTime:null,
        isFirstAdd:true,
        goldTitleMap:['健康','消灾','平安','吉祥','纳福','富贵','长寿'],
        currentGoldTitleMap:[]
      }
    },
    mounted: async function(e) {
      try {
        await this.randerCalabash('calabashTank')
        this.getSystemInfo()
        // 触发陀螺仪绑定
        this.bindDevice()
        // 初始化金币
        this.initGold()
        // 获取头像
        this.getHead()
        // 随机文案
        this.currentGoldTitleMap = this.generateUniqueFourDigitArray(this.goldTitleMap)
      } catch (e) {
        console.log('错误', e);
      }
    },
    methods: {
      // 解析hash模式路由参数
       parsedHashRouterOptions(){
        let routerOptions = window.location.href.split('?')[1]?.split('&')?.reduce((res,index)=>{
          let map = index.split('=');
          res[map[0]] = map[1];
          return res
        },{})
        return routerOptions || {}
      },
      generateUniqueFourDigitArray(sevenDigits) {
          let indexes = Array.from({length: sevenDigits.length}, (_, i) => i);
          // 打乱索引数组
          for (let i = indexes.length - 1; i > 0; i--) {
              const j = Math.floor(Math.random() * (i + 1));
              [indexes[i], indexes[j]] = [indexes[j], indexes[i]];
          }

          // 根据打乱后的索引选择前四个元素组成新的数组
          let result = indexes.slice(0, 4).map(i => sevenDigits[i]);

          return result;
      },
      getHead(){
        var {headPath} = this.parsedHashRouterOptions();
        this.headPath = this.file_ctx + headPath
      },
      gotoPages(url){
        uniWebview.navigateTo({url})
      },
      // 接收金币
      receiveGold(goldIndex,goldNum){
        if(this.showGoldFellMap[goldIndex] || this.receiveLock) return
          this.$set(this.showGoldFellMap,goldIndex,true)
          this.receiveLock = true
          requestAnimationFrame(async ()=>{
            let calabashBgDomCli = this.$refs.calabashBg.$el.getBoundingClientRect();
            setTimeout(()=>{
              this.addGold(goldNum,1,this.MatterTank.clientHeight * 0.4,[2,1],true)
              this.receiveLock = false;
              this.$set(this.showGoldFellMap,goldIndex,false)
              this.$emit('goldInsert',goldIndex)
            },800)

            // 获取当前点击的金币对应的掉落集合
            // let fellItemDom = document.querySelectorAll(`.fellSign${goldIndex}`);
            // for (var i = 0; i < [...fellItemDom].length; i += 1) {
            //   let resolve, promise;
            //   promise = new Promise(res => resolve = res)
            //   let time = i === 0 ? 0 : 500
            //   let ele = [...fellItemDom][i]
            //   setTimeout(() => {
            //     let eleCli = ele.getBoundingClientRect();
            //     let dy = calabashBgDomCli.top - eleCli.top
            //     let dx = (calabashBgDomCli.left + calabashBgDomCli.width / 2) - eleCli.left
            //     let sonEle = ele.querySelector('.fell') || {};
            //     ele.style = `transform: translateX(${dx}px)`
            //     sonEle.style = `transform: translateY(${dy}px)`
            //     setTimeout(()=>{
            //       sonEle.style = 'display:none'
            //       ele.style = 'display:none'
            //       this.addGold(1,1,70,[2,1],true)
            //       resolve(true)
            //     },250)
            //   }, time)
            //   await promise
            //   // 关闭接收锁
            //   if(i >= [...fellItemDom].length - 1) {
            //     this.receiveLock = false;
            //     this.$set(this.showGoldFellMap,goldIndex,false)
            //     this.$emit('goldInsert',goldIndex)
            //   }
            // }
          })
      },
      requestPermission(){
        this.openPop = false
        DeviceOrientationEvent.requestPermission().then(permissionState => {
          if (permissionState === 'granted') {
              // 开始监听设备方向
              window.addEventListener("deviceorientation", this.setGravityDirectionAndMagnitude,true);
          } else {
              console.log('当前未授权无法使用陀螺仪');
          }
        }).catch(error => {
            console.error('请求方向权限时出错:', error);
        });
      },
      bindDevice(){
        // iOS 13+ 需要请求权限
        if (window.DeviceOrientationEvent && typeof window.DeviceOrientationEvent.requestPermission === 'function') {
            // iOS 13+ 需要请求权限
            uni.showModal({
            	title: '提示',
            	content: '请授权陀螺仪，获得更好的体验',
              showCancel:false,
              confirmText:'确认授权',
            	success: (res)=> {
            		if (res.confirm) {
            			console.log('用户点击确定');
                  this.requestPermission()
            		} else if (res.cancel) {
            			console.log('用户点击取消');
            		}
            	}
            });
        } else if (window.DeviceOrientationEvent) {
            // 直接监听设备方向
            window.addEventListener("deviceorientation", this.setGravityDirectionAndMagnitude,true);
        } else {
            console.log('此浏览器不支持设备方向。');
        }
      },
      // alpha 表示设备绕z轴旋转的角度（0 到 360 度）
      // beta 表示设备绕x轴旋转的角度（-180 到 180 度）
      // gamma 表示设备绕y轴旋转的角度（-180 到 180 度）
      // 更新渲染
      setGravityDirectionAndMagnitude({alpha,beta,gamma}) {
        // 计算角度的变化幅度
        const alphaChange = alpha - this.alpha
        const betaChange = beta - this.beta
        const gammaChange = gamma - this.gamma
        // 设置阈值
        const threshold = 20; // 根据需求调整阈值
        if (Math.abs(alphaChange) < threshold && Math.abs(betaChange) < threshold && Math.abs(gammaChange) < threshold) return
        // 计算新的重力方向
        let horizontalForce = Math.sin((gammaChange * Math.PI) / 180);
        // 计算垂直方向的重力
        let verticalForce = Math.sin((betaChange * Math.PI) / 180);
        // 只有当变化幅度超过阈值时才更新重力方向 // 合并水平和垂直方向的重力
        let finalForce = {
          x: horizontalForce,
          y: verticalForce
        };
        // 设置物理引擎中的重力方向
        matterObjects.engine.world.gravity.x = finalForce.x * 1;
        matterObjects.engine.world.gravity.y = finalForce.y * 1;

      },
      // 金币初始化
      async initGold() {
        this.initGoldTime = setTimeout(()=>{
          if(!this.initGoldNum){
            this.initGold()
            return
          }
          clearTimeout(this.initGoldTime)
          const circleBotRadius = this.MatterTank.clientWidth / 2 - 10;
          this.goldDroppingHeight = this.MatterTank.clientHeight - circleBotRadius - 50
          this.addGold(this.initGoldNum, 25, this.goldDroppingHeight, [1, 1])
        },100)
      },
      // 添加金币
      /**
       * @param {Number} goldNum 绘制金币的个数
       * @param {Number} step 分批次渲染 每次渲染的金币数量
       * @param {Number} Y 绘制金币的初始y坐标
       * @param {Array} [randMultX,randMultY] 金币x坐标和y坐标的随机倍率
       * @param {Array} [randMultX,randMultY] 金币x坐标和y坐标的随机倍率
       * @param {Array} uptime 每次运行的间隔时间
       */
      async addGold(goldNum, step = 1, Y, randMultMap,uptime = 500) {
          this.goldNum+=goldNum
          goldNum = goldNum > this.goldMaxNum ? this.goldMaxNum : goldNum
          if(this.goldNum > this.goldMaxNum){
            if(this.isFirstAdd){
              this.isFirstAdd = false
            }else{
              return
            }
          }
          if(goldNum === 0) return
          if (this.addGoldLock) return
          // 因为存在设置步数 所以需要手动计算循环运行的真正次数
          let runTimeCount = Math.ceil(goldNum / step)
          let curentCount = 0
          // 上锁
          this.addGoldLock = true
          let newGold = this.createGold(goldNum, Y, randMultMap)
          for (var i = 0; i < goldNum; i += step) {
            curentCount++;
            let resolve, promise;
            promise = new Promise(res => resolve = res)
            let eleMap = newGold.slice(i, i + step)
            let time = i === 0 ? 0 : uptime
            setTimeout(() => {
              // 添加完毕取消上锁
              if (curentCount >= runTimeCount) this.addGoldLock = false
              // 添加刚体到画布世界
              this.addrigid(eleMap)
              resolve(true)
            }, time)
            await promise
          }
      },
      addrigid(eventMap) {
        eventMap = Array.isArray(eventMap) ? eventMap : [eventMap],
        console.log('matterObjects.Composite',matterObjects.Composite);
        eventMap.forEach((event) => {
          matterObjects.Composite.add(matterObjects.engine.world, event)
          matterObjects.boxMap.push(event)
        })
      },
      // 生产金币
      /**
       * @param {Number} n 绘制金币的个数
       * @param {Number} y 绘制金币的初始y坐标
       * @param {Array} [randMultX,randMultY] 金币x坐标和y坐标的随机倍率
       */
      createGold(n = 1, Y = 10, [randMultX, randMultY]) {
        // 计算时混入随机数 防止刚体x坐标完全一致导致堆叠
        return new Array(n).fill(1).map(e => {
          let direction = Math.random() > 0.5 ? 1 : -1
          let X = this.MatterTank.clientWidth / 2 + Math.random() * direction * randMultX
          Y = Y + Math.random() * direction * randMultY
          let circle = Matter.Bodies.circle(X, Y, 10, this.goldBody)
          return circle
        })
      },
      // 初始化渲染
      async randerCalabash(elementName) {
        const Engine = Matter.Engine
        const Render = Matter.Render
        const Bodies = Matter.Bodies
        const Composite = Matter.Composite
        const Runner = Matter.Runner
        const element = document.getElementById(elementName)
        this.MatterTank = element
        // 1. 创建引擎
        let engine = Engine.create()
        const pixelRatio = window.devicePixelRatio || 1;
        // 2. 创建渲染器，并将引擎连接到画布上
        let render = Render.create({
          element, // 绑定页面元素
          engine, // 绑定引擎
          options: {
            ...this.matterOptions,
            width: element.clientWidth,
            height: element.clientHeight,
            pixelRatio // 设置设备像素比
          }
        })
        // 3-2. 创建边界和葫芦体
        let ground = await this.createBoundaries(element, Bodies)
        // 4. 将所有物体添加到世界中
        Composite.add(engine.world, ground)
        // 5. 执行渲染操作
        Render.run(render)

        // 6. 创建运行方法
        let runner = Runner.create()
        // 7. 运行渲染器
        Runner.run(runner, engine)
        matterObjects = {}
        matterObjects.engine = engine
        matterObjects.boxMap = []
        matterObjects.Composite = Composite
      },
      // 创建边界
      async createBoundaries(element, Bodies) {
        // 创建葫芦圆环
        let GourdTracks = await this.createRingWithGap();
        // 存储边界点
        this.GourdTracks = GourdTracks.map(e=>e.id)
        return GourdTracks
      },
      async createRingWithGap() {
        // 创建环形的顶点
        const vertices = await getStrokeCall(this.MatterTank.clientWidth,this.MatterTank.clientHeight,this.calabash);
        // 创建小刚体
        const bodies = vertices.map(vertex => {
          const body = Matter.Bodies.circle(vertex.x, vertex.y, 1, {
            isStatic: true,
            render: {
              fillStyle: 'transparent', // 设置填充为透明
              // strokeStyle: vertex.strokeStyle || 'black', // 设置描边颜色
              strokeStyle: 'transparent', // 设置描边颜色
              lineWidth: vertex.r // 设置描边宽度
            }
          });
          return body;
        });

        return bodies;
      },
      getSystemInfo() {
        uni.getSystemInfo({
          success: res => this.windowTop = res.windowTop,
        })
      }
    }
  }
</script>

<style scoped lang="scss">
  .headPath{
    position: absolute;
    top: 4rpx;
    left: 4rpx;
    width: 68rpx;
    height: 68rpx;
    border-radius: 50%;
  }
  .bgMask{
    position: fixed;
    z-index: 9999999999;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
  }
  .requestButton{
    position: fixed;
    z-index: 9999999999;
    width: 60vw;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    width: 686rpx;
    height: 88rpx;
    background: #00B484;
    border-radius: 44rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #FFFFFF;
    text-align: center;
    line-height: 88rpx;
    margin-top: 144rpx;
  }
  .calabashImage {
    width: 410rpx;
    height: 685rpx;
  }
  .calabashStroke{
    display: none;
  }
  .calabashBox{
    width: 433rpx;
    height:751rpx;
    position: relative;
    margin: 60rpx auto;
    margin-top: 10rpx;
    #calabashTank {
      width: 433rpx;
      height:751rpx;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%,-50%);
      z-index: 2;
    }
    .calabashBg{
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%,-50%);
      width: 100%;
      height: 100%;
      z-index: 2;
    }
  }

  .bg {
    width: 100vw;
    height: 100vh;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    overflow: hidden;
  }

  @keyframes moveGold {
    0% {
      transform: translateY(0%);
    }

    50% {
      transform: translateY(10%);
    }

    100% {
      transform: translateY(0%);
    }
  }
  @keyframes initGold {
    0% {
      transform:translate(-50%,-50%) scale(1);
    }

    50% {
      transform:translate(-50%,-50%) scale(1.1);
    }

    100% {
      transform:translate(-50%,-50%) scale(1);
    }
  }
  .goldNumBox{
    width: 264rpx;
    height: 74rpx;
    line-height: 74rpx;
    margin-top: 65rpx;
    margin-left: 41rpx;
    position: relative;
    padding-left: 92rpx;
    box-sizing: border-box;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    .goldNumBoxBg{
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
    .goldNum{
      color: #7B2519;
      font-size: 40rpx;
      font-weight: bolder;
      width: 92rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .goldSign{
      color: #7B2519;
      font-size: 23rpx;
      font-weight: Medium;
      margin-left: 14rpx;
    }
  }
  .calabashdailyPrayers{
    position: fixed;
    right: 40rpx;
    top: 50rpx;
    width: 134rpx;
    height: 128rpx;
  }
  .exchangeGift{
    position: fixed;
    right: 185.5rpx;
    top: 50rpx;
    width: 134rpx;
    height: 128rpx;
  }
  .goldBox {
    width: 100vw;
    display: flex;
    height: 174rpx;
    margin-top: 113rpx;
    padding: 0 67rpx;
    .goldBoxSon{
      &:nth-of-type(1) {
        margin-top: 58rpx;
      }
      &:nth-of-type(2) {}
      &:nth-of-type(3) {
        margin-top: 8rpx;
      }
      &:nth-of-type(4) {
        margin-top: 58rpx;
      }
    }
    .goldShell {
      width: 116rpx;
      height: 116rpx;
      position: relative;
      margin-right: 48rpx;
      padding: 15rpx 36rpx 0 36rpx;
      box-sizing: border-box;
      font-size: 32.98rpx;
      color: #FFE47E;
      font-weight: 600;
      display: flex;
      flex-wrap: wrap;
      animation: moveGold 2.5s infinite;
      .goldMinNum{
        transform: translateY(-10rpx);
      }
      .botTitle{
        font-size: 33rpx;
        color: white;
        font-weight: 400;
        position: absolute;
        bottom: -58rpx;
        transform: translateX(-13%);
      }
    }
    .gold {
      width: 42rpx;
      height: 46rpx;
      @include iconImg(42, 46, '/business/hulu-v2/calabashGold.png');
    }
    .initBubble{
      animation:initGold .5s;
    }
    .goldBubble{
      width: 116rpx;
      height: 116rpx;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%,-50%);
    }
    .fellFellBox{
      position: relative;
      .goldFell{
        position: absolute;
        width: 48rpx;
        height: 52rpx;
        transition: all 0.5s cubic-bezier(0, 0.8, 1, 0.98);
        .fell{
          transition: all 0.5s ease-out;
        }
      }
    }

  }

</style>
