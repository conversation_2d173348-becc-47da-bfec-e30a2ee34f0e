<template>
  <view class='poster-picture'>
    <!-- #ifdef H5 -->
    <view class="poster-content">
      <wx-open-launch-weapp
        id="launch-btn"
        :appid="$appId"
        @launch="handleClickBtn(1)"
        :path="`modules/common/instruction-web-html-view/index?src=https://www.hkmyt.com/conference/355.html`"
        style="width: 750rpx;height: 1388.89rpx;position:absolute;"
      >
        <script type="text/wxtag-template">
          <div style="width: 750rpx;height: 1388.89rpx;position: relative;z-index:-1;">
            <img style="width: 100%;height: 100%;" src="https://file.greenboniot.cn/static/image/business/pharmacy-cyclopedia/icon-instruction-poster.png"></img>
          </div>

        </script>
      </wx-open-launch-weapp>
      <wx-open-launch-weapp
        id="launch-btn"
        :appid="$appId"
        @launch="handleClickBtn(2)"
        :path="`modules/common/instruction-web-html-view/index?src=https://www.hkmyt.com/conference/355.html`"
        style="width: 524.8rpx;height: 88.29rpx;position:absolute;bottom:40rpx;left:50%;transform: translate(-50%, -50%);"
      >
        <script type="text/wxtag-template">
          <div class="testing-btn" style="
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            width: 524.8rpx;
            height: 88.29rpx;
          ">
            <img style="width: 100%;height: 100%;" src="https://file.greenboniot.cn/static/image/business/pharmacy-cyclopedia/icon-instruction-poster-btn.png"></img>
            <div style="width:232.14rpx;30.75rpx;position:absolute;">
              <img src="https://file.greenboniot.cn/static/image/business/pharmacy-cyclopedia/icon-instruction-poster-btn-text.png" style="position:absolute;top:50%;left:50%;transform: translate(-50%, -50%);width:157.01px;height:19.31px;">
            </div>
          </div>
        </script>
      </wx-open-launch-weapp>
    </view>
    <!-- #endif -->
  </view>
</template>

<script>
  import { mapState } from 'vuex'
  export default {
    data(){
      return{
        $appId: this.$appId,
        file_ctx: this.file_ctx,
        $constant: this.$constant,
      }
    },
    onLoad(){
      this.wxh5ShareInit()
      // #ifdef H5
      this.pageexposurerecordInsert()
      // #endif
    },
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId,
      }),
    },
    mounted(){},
    methods:{
      // 按钮点击
      async handleClickBtn(type){
        this.handleClickCommonPort(type)
      },
      async handleClickCommonPort(type){
        let parmas = {
          imageUrl:this.file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-instruction-poster.png',
          accountId:this.accountId,
          businessType:this.$constant.drugBook.businessTypeObj.h5InstructionTypePage,
        }
        if(type == 1){
          await this.$api.drugBook.visitpagerecordInsert(parmas)
        } else {
          await this.$api.drugBook.imageoperationrecordInsert(parmas)
        }
      },
      // #ifdef H5
      async pageexposurerecordInsert(){
        let parmas = {
          imageUrl:this.file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-instruction-poster.png',
          accountId:this.accountId,
          businessType:this.$constant.drugBook.businessTypeObj.h5InstructionTypePage,
        }
        await this.$api.drugBook.pageexposurerecordInsert(parmas)
      },
      // #endif
    },
 }
</script>

<style lang='scss' scoped>
  /* #ifdef H5 */
  .poster-content{
    position: relative;
    width: 750rpx;
    height: 1388.89rpx;
    overflow: hidden;
  }
  /* #endif */
</style>