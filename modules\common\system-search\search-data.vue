<template>
  <view class="main">
    <view class="my-data">
      <view class="top-nav">
        <view class="top-nav-body">
          <view class="input-view">
            <i class="icon-positioning-search"></i>
            <input confirm-type="search" placeholder="搜索你想要的信息" placeholder-style="color: #BFBFBF" class="input" type="text" :value="search" @input="searchInputFn" @confirm="searchFn">
          </view>
          <view class="click" @tap="searchFn">
            搜索
          </view>
        </view>
      </view>
      <tabs-sticky :fontBigger="true" :bdb="false" :overflowX="true" v-model="curIndex" :tabs="tabs" @change="changeTab"></tabs-sticky>
    </view>
    <view class="search-main">
      <swiper class="swiper" :current="curIndex" @change="swiperChange">
        <swiper-item>
          <ai-doctor ref="aiDoctor" :index="curIndex" :search="search"></ai-doctor>
        </swiper-item>
        <!-- 帖子 -->
        <swiper-item>
          <scroll-refresh style="height: 100%;" :isShowEmptySwitch="true" :index="curIndex" :fixed="false" :isAbsolute="false" :up="upOption" :down="downOption"
            @returnFn="returnFn" @scrollInit="mescrollInit"
          >
            <view class="content">
              <view class="content-main">
                <nui-list ref="nuiList" :indexlist="indexlist" :isShowGambit="true" @cateClick="cateClick"></nui-list>
              </view>
            </view>
          </scroll-refresh>
        </swiper-item>
        <swiper-item>
          <scroll-refresh style="height: 100%;" :isShowEmptySwitch="true" :fixed="false" :isAbsolute="false" :up="upOption" :down="downOption" @returnFn="gambitReturnFn" @scrollInit="gambitScrollInit">
            <view class="gambit-content">
              <view class="gambit-item" v-for="item in gambitList" :key="item.id" @click="handleClickDiscuss(item)">
                <view class="gambit-l">
                  <view class="title">#{{ item.topicName }}</view>
                  <view class="info-box">
                    <!-- <view class="browse">{{ item.preview }}浏览</view> -->
                    <view class="discussion">{{ item.postUseCount }}讨论</view>
                  </view>
                </view>
                <view class="gambit-r">去讨论</view>
              </view>
            </view>
          </scroll-refresh>
        </swiper-item>
        <swiper-item>
          <scroll-refresh :fixed="false" :up="upOption" :index="curIndex" :down="downOption" bgColor="rgba(0,0,0,0)" @returnFn="liveReturnFn" @scrollInit="liveScrollInit">
            <view class="main-content">
              <videoItem :pdList="pdList" :logConditionParams='logConditionParams'></videoItem>
            </view>
          </scroll-refresh>
        </swiper-item>
        <swiper-item>
          <my-instruction-book ref="myInstructionBook" :index="curIndex" :search="search"></my-instruction-book>
        </swiper-item>
        <swiper-item>
          <hospital ref="myHospital" :index="curIndex" :search="search"></hospital>
        </swiper-item>
      </swiper>
    </view>
  </view>
</template>

<script>
import TabsSticky from '@/components/basics/tabs-sticky-v3'
import nuiList from '@/components/community/nui-list/nui-list.vue'
import videoItem from '@/modules/common/components/video-item/index'
import myInstructionBook from './my-instruction-book.vue'
import hospital from './hospital.vue'
import aiDoctor from './ai-doctor.vue'
import { isDomainUrl } from '@/utils/index'
import serverOptions from '@/config/env/options'

export default {
  components: {
    TabsSticky,
    nuiList,
    videoItem,
    myInstructionBook,
    hospital,
    aiDoctor,
  },
  data() {
    return {
      mescroll: null, // mescroll实例对象
      liveMescroll: null, // liveMescroll实例对象
      gambitScroll: null, // 话题实例对象
      downOption: {
          auto: false // 不自动加载
      },
      upOption: {
          auto: false, // 不自动加载
          empty: {
            top: 0,
            zIndex: 999,
          }
      },
      scrollObj: {},
      courseScrollObj: {},
      storeList: [],
      courseList: [],
      cityInfo: {},
      curIndex: 0,
      tabs: [{ name: 'AI+医生' },{ name: '帖子' },{ name: '话题' },{ name: '直播' },{name:'药品说明书'},{name:'医院'}],
      // tabs: [{ name: '帖子' },{ name: '话题' },{ name: '直播' },{name:'药品说明书'},{name:'医院'}],
      courseListOne: [],
      storeListOne: [],
      search: '',
      indexlist: [],
      query: {},
      logConditionParams:{}, //直播的
      pdList: [], // 列表数据
      gambitList:[],
    }
  },
  onLoad(options) {
    const query = this.$validate.isNull(this.$Route.query) ? options : this.$Route.query
    if (!this.$validate.isNull(query)) {
      this.search = query.search
      this.query = query.query
      this.searchFlag = query?.searchFlag
    }
    if (this.search) {
      this.init()
    } else {
      this.init()
    }
  },
  onShareAppMessage (res) {
    if (res.from === 'button') {
      // 来自页面内转发按钮
    }
    return {
      path: `modules/common/system-search/search-data?search=${this.search}&query=${JSON.stringify(this.query)}`,
    }
  },
  onShow() {
  },
  watch: {
    // 监听下标的变化
    curIndex(val) {
      this.init()
    }
  },
  methods: {
    handleClickDiscuss(item){
      this.$navto.push('GambitDetail',{id:item.id})
    },
    gambitReturnFn(obj){
      const that = this
      setTimeout(function() {
        let params = {
          current: obj.pageNum,
          size: obj.pageSize,
          condition: {
            // forApplet:1,
            name:that.search
          }
        }
        that.$api.postmessage.postmessagetopicSearchPage(params).then(res => {
          let data = res.data.records || []
          if (obj.pageNum === 1) {
            that.gambitList = []
          }
          that.gambitList = [...that.gambitList, ...data]
          obj.successCallback && obj.successCallback(data)
        })
      }, that.$constant.noun.scrollRefreshTime)
    },
    gambitScrollInit(scroll) {
      scroll.optUp.page.num = 1
      scroll.optUp.page.size = 7
      this.gambitScroll = scroll
    },
    liveReturnFn(obj) {
		  console.log('触发加载',obj);
      const that = this
      function queryPage(pageNum, pageSize, fn) {
        const param = {
          current: pageNum,
          size: pageSize,
          condition: {
            activityStatusList: [
              2,3,5
            ],
            showType: 1,
            businessType: 7, // 直播活动
            title:that.search,
            // productId: that.productId
          }
        }

        // 在全部类型的请求中 加入排序参数
        if(!param.condition.classifyId){
          delete param.condition.businessType
          param.condition.businessTypeList = [3,7]
          param.condition.orderByActivityStatus = "5,2,3"
        }
        // 记录页面请求参数
        that.logConditionParams = {
          ...param.condition
        }

        that.$api.cloudClassroom.getMeetingQueryPage(param).then(res => {
           if (res && res.data.records) {
             for (const a in res.data.records) {
               const data = res.data.records[a]
               data.coverPathsUrl = isDomainUrl(data.coverPaths)
               // data.endTimeText = that.$common.formatDate(new Date(data.endTime), 'yyyy-MM-dd HH:mm')
             }
             fn(res.data.records)
           }
         })

      }



      setTimeout(function() {
        queryPage(obj.pageNum, obj.pageSize, (data) => {
          if (obj.pageNum === 1) {
            that.pdList = []
          }
          that.pdList = that.pdList.concat(data)
          obj.successCallback && obj.successCallback(data || [])
        })
      }, that.$constant.noun.scrollRefreshTime)
    },
    liveScrollInit(scroll) {
      scroll.optUp.page.num = 1
      scroll.optUp.page.size = 7
      this.liveMescroll = scroll
    },
    cateClick (data) {
        this.navtoGo('Circle', {cid: data.circleClassifyId})
    },
    navtoGo(url, obj = {}) {
        this.$navto.push(url, obj)
    },
    init() {
      this.$nextTick(() => {
        let cityInfoStorage = this.$common.getKeyVal('system', 'cityInfoStorage', true)
        if (typeof cityInfoStorage === 'string' && cityInfoStorage === '') {
          cityInfoStorage = {}
        } else if (typeof cityInfoStorage === 'string' && cityInfoStorage !== '') {
          cityInfoStorage = JSON.parse(cityInfoStorage)
        }
        if (cityInfoStorage) {
          if (typeof cityInfoStorage === 'object' && JSON.stringify(cityInfoStorage) !== '{}') {
            this.cityInfo = cityInfoStorage
          } else {
            this.cityInfo = {}
          }
        } else {
          this.cityInfo = {}
        }
        if (this.curIndex === 0) {
          console.log('点击了ai医生')
          this.$refs.aiDoctor.init()
        } else if (this.curIndex === 1) {
          this.mescroll.triggerDownScroll()
        } else if (this.curIndex === 2) {
          this.gambitScroll.triggerDownScroll()
        } else if (this.curIndex === 3) {
          this.liveMescroll.triggerDownScroll()
        } else if(this.curIndex === 4){
          this.$refs.myInstructionBook.init()
        } else {
          this.$refs.myHospital.init()
        }
        // } else if (this.curIndex === 1) {
        //   this.gambitScroll.triggerDownScroll()
        // } else if (this.curIndex === 2) {
        //   this.liveMescroll.triggerDownScroll()
        // } else if(this.curIndex === 3){
        //   this.$refs.myInstructionBook.init()
        // } else {
        //   this.$refs.myHospital.init()
        // }
      })
    },
    searchInputFn(e) {
      this.search = e.target.value
    },
    changeTab(index) {
      this.curIndex = index
    },
    swiperChange(e) {
      this.changeTab(e.detail.current)
    },
    courseScrollInit(scroll) {
      scroll.optUp.page.size = 10
      this.courseScrollObj = scroll
    },
    scrollInit(scroll) {
      scroll.optUp.page.size = 10
      this.scrollObj = scroll
    },
    mescrollInit(scroll) {
      scroll.optUp.page.size = 10
      this.mescroll = scroll
    },
    returnFn(obj) {
      const that = this
      const query = that.$validate.isNull(that.query) ? {} : that.query
      function queryPage(pageNum, pageSize, fn) {
        const params = {
          current: pageNum,
          size: pageSize,
          condition: {
              keyword: that.search,
              searchFlag:that.searchFlag,
              ...query,
              otherApp:serverOptions.providerId === serverOptions.getoptions().providerId,
              providerId: that.$common.getKeyVal('user','cloudProviderId',true)
          }
        }
        that.$ext.community.postmessageSearch(params).then(res => {
          fn(res.data.records)
        })
      }
      setTimeout(function() {
        queryPage(obj.pageNum, obj.pageSize, (data) => {
          if (obj.pageNum === 1) {
            that.indexlist = []
          }
          that.indexlist = that.indexlist.concat(data)
          that.indexlist = that.indexlist.map(item=>{
            return {
              ...item,
              content:item.content ? item.content : item.contentNew3,
              topicIdsArr : item.topicIds && JSON.parse(item.topicIds) || []
            }
          })
          obj.successCallback && obj.successCallback(data || [])
        })
      }, that.$constant.noun.scrollRefreshTime)
    },
    navto(date) {
      this.$navto.push('StoreDetail', { storeId: date.id, unitId: date.unitId })
    },
    courseListReturnFn(o) {
      this.$navto.push('CourseBusinessDetail', { id: o.id, storeId: o.storeId, unitId: o.unitId })
    },
    searchFn() {
      if (this.search) {
        let arr = this.$common.getKeyVal('system', 'searchHistoryArrStorage', true)
        if (typeof arr === 'string' && arr === '') {
          arr = []
        } else if (typeof arr === 'string' && arr !== '') {
          arr = JSON.parse(arr)
        }
        if (arr) {
          if (typeof arr === 'object' && arr.length > 0) {
            let state = false
            let index = ''
            for (let i = 0; i < arr.length; i++) {
              if (this.search === arr[i]) {
                state = true
                index = i
                break
              }
            }
            if (state) {
              if (typeof index === 'number') {
                arr.splice(index, 1)
              }
            }
          }
          arr.unshift(this.search)
          this.$common.setKeyVal('system', 'searchHistoryArrStorage', arr, true)
        } else {
          this.$common.setKeyVal('system', 'searchHistoryArrStorage', [], true)
        }
        this.init()
      } else {
        this.init()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .m-t-40{
    margin-top:40upx;
  }
  .m-b-20{
    margin-bottom: 20upx;
  }
  .main{
    display: flex;
    height: 100vh;
    flex-direction: column;
    background-color: #fff;
    .my-data{
      position: sticky;
      top: 0;
      z-index:999;
      .top-nav{
        padding: 10upx 30upx 10upx 30upx;
        background-color: #fff;
        .top-nav-body{
          display: flex;
          align-items: center;
          .icon-back-left{
            display: inline-block;
            vertical-align: middle;
            @include iconImg(64, 64, '/system/icon-back-left.png');
            margin-right: 16upx;
          }
          .input-view {
            flex: 1;
            display: inline-block;
            vertical-align: middle;
            width: calc(100% - 220upx);
            @include rounded(38upx);
            line-height: 64upx;
            height: 64upx;
            padding: 0 20upx;
            background: #F7F7F7;
            .icon-positioning-search{
              display: inline-block;
              vertical-align: middle;
              margin-right: 6upx;
              @include iconImg(32, 32, '/system/icon-positioning-search.png');
            }
            .input {
              width: calc(100% - 78upx);
              display: inline-block;
              vertical-align: middle;
              font-size: 28upx;
              line-height: 42upx;
              color: #333;
            }
          }
          .click{
            display: inline-block;
            vertical-align: middle;
            text-align: right;
            width: 100upx;
            line-height: 64upx;
            height: 64upx;
          }
        }
      }
      /deep/.tabs-sticky{
        padding:22rpx 0 22rpx;
        border-bottom: 4rpx solid #F5F5F5;
        .tabs-sticky-body{
          padding: 0 32rpx;
          .tab{
            text{
              padding: 0;
            }
          }
        }
      }
    }
    .search-main{
      flex: 1;
      .swiper{
        height: 100%;
        .tabs-0{
          padding-top: 30upx;
          background-color: #fff;
          border-bottom: 30upx solid #f7f7f7;
          .title{
            font-size: 32upx;
            line-height: 48upx;
            padding: 10upx 30upx 0 30upx;
          }
          .no-data{
            font-size: 32upx;
            line-height: 48upx;
            text-align: center;
            margin: 40upx 0;
            color: #999;
            padding: 0 30upx;
          }
          .more{
            margin-left: 30upx;
            padding: 30upx 30upx 30upx 0;
            .icon-more-yellow{
              display: inline-block;
              vertical-align: middle;
              margin-right: 8upx;
              @include iconImg(32, 32, '/business/icon-more-yellow.png');
            }
            .name{
              width: calc(100% - 90upx);
              display: inline-block;
              vertical-align: middle;
              font-size: 28upx;
              line-height: 42upx;
              color: $topicC;
            }
            .icon-gengduo{
              display: inline-block;
              vertical-align: middle;
              margin-left: 18upx;
              @include iconImg(32, 32, '/business/icon-gengduo.png');
            }
          }
        }
        .tabs-0:last-of-type{
          border-bottom: none;
        }
      }
    }
  }
  .content {
    .content-main {
      overflow: hidden;
      background-color: #fff;
    }
  }
  .gambit-content{
    padding: 32rpx;
    background: #F4F6FA;
    .gambit-item{
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 30rpx 24rpx 28rpx;
      background: #FFFFFF;
      border-radius: 16rpx 16rpx 16rpx 16rpx;
      margin-bottom: 24rpx;
      .gambit-l{
        display: flex;
        flex-direction: column;
        .title{
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 400rpx; /* 设置元素宽度 */
          font-size: 32rpx;
          color: #1D2029;
          line-height: 38rpx;
          margin-bottom: 8rpx;
        }
        .info-box{
          display: flex;
          color: #777777;
          .browse{
            font-size: 24rpx;
            margin-right: 12rpx;
          }
          .discussion{
            font-size: 24rpx;
          }
        }
      }
      .gambit-r{
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
        color: #00B484;
        padding: 6rpx 16rpx;
        height: fit-content;
        border-radius: 66rpx 66rpx 66rpx 66rpx;
        border: 2rpx solid #00B484;
      }
      &:last-child{
        margin-bottom: 0;
      }
    }
  }
</style>
