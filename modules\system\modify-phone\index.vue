<template>
  <page>
    <view slot="content" class="main-body">
      <view class="main">
        <view class="main-text">
          请输入您的登录手机号
        </view>
        <view>
          <view class="main-content">
            <text class="l">手机号</text>
            <input class="r" type="number" maxlength="11"  @input="change" :disabled="true" placeholder="请输入手机号" placeholder-class="f-w-medium" v-model="regForm.phone" >
          </view>
          <view class="main-content">
            <text class="l">验证码</text>
            <input class="r width-calc-310" maxlength="4" type="number" placeholder="请输入验证码" placeholder-class="f-w-medium" v-model="regForm.code" >
            <text class="verification" :class="isGetCodeStatus" @tap="getCode">{{ getVerificationTxt }}</text>
          </view>
        </view>
        <view class="main-prompt">
          *如果当前手机号非本人持有，请联系平台客服处理
        </view>
      </view>
      <view class="btn">
        <!--          <button type="primary" @click="onSubmit">{{ isNextUpdate ? '完成' : '下一步' }}</button>-->
        <button type="primary" @click="onSubmit">下一步</button>
      </view>
    </view>
  </page>
</template>

<script>
import { mapState } from 'vuex'

export default {
  onShow() {
    if (this.curSelectUserInfo) {
      this.regForm.phone = this.curSelectUserInfo.phone
    }
  },
  data() {
    return {
      isNextUpdate: false,
      title: '登录',
      num: 0,
      providerList: [],
      hasProvider: false,
      positionTop: 0,
      redirectUrl: '',
      getVerificationTxt: '获取验证码',
      isAllowGetNum: true,
      regForm: {
        phone: '', // 手机号码
        uuid: this.$common.uuid(), // 随机编码
        code: '' // 验证码
      }
    }
  },
  computed: {
    ...mapState('user', {
      curSelectUserInfo: state => state.curSelectUserInfo, // 当前选中的用户信息
    }),
    isGetCodeStatus: function() {
      return {
        'is-get-code': !this.isAllowGetNum
      }
    },
    isConfirm() {
      let flag = true
      for (const k in this.regForm) {
        if (k && this.regForm.hasOwnProperty(k)) {
          if (!this.regForm[k]) {
            flag = false
            break
          }
        }
      }
      return flag
    }
  },
  onUnload(){
    // #ifdef MP-WEIXIN
    this.handleClickTrack('EndOperationDetailsPageView')
    // #endif
  },
  methods: {
    // #ifdef MP-WEIXIN
    handleClickTrack(type){
      let pages = getCurrentPages()
      let current = pages[pages.length - 1]; // 获取到当前页面的信息
      let pageInfo = __wxConfig.page && __wxConfig.page[`${current.route}.html`] || ''; // 内部属性
      getApp().globalData.sensors.track(type,
        {
          'page_name' : pageInfo?.window?.navigationBarTitleText || '',
          'first_operation_name' : '更多功能',
          'second_operation_name' : '我的手机',
        }
      ) 
    },
    // #endif
    change(e) {
    },
    getCode() {
      const that = this
      if (!this.isAllowGetNum) return
      const isPhone = that.$validate.isMobile(that.regForm.phone)
      if (!isPhone.flag) {
        this.$uniPlugin.toast(isPhone.msg)
        return
      }
      const params = {
        phone: that.regForm.phone, // 用户填写的手机号
        uuid: this.$common.getTokenUuid(), // 随机编码
      }
      this.$uniPlugin.toast('操作中')
      that.$api.sys.sendSms(params, res => {
        this.resetTime(60)
        this.$uniPlugin.toast('发送成功')
      })
    },
    /**
     * 修改密码
     */
    onSubmit() {
      const that = this
      if (!this.isConfirm) {
        this.$uniPlugin.toast('请完善信息')
        return
      } else {
        const params = {
          phone: this.regForm.phone, // 用户填写的手机号
          uuid: this.$common.getTokenUuid(), // 随机编码
          code: this.regForm.code // 验证码
        }
        that.$api.sys.appUserCheckphone(params).then(changPwdDate => {
          that.$uniPlugin.toast('进入修改手机号')
          setTimeout(() => {
            that.$navto.push('ModifyPhoneBindPhone')
          }, that.$constant.noun.delayedOperationTime)
        })
      }
    },
    /**
     * 倒计时
     * @param time
     */
    resetTime(time) {
      this.isAllowGetNum = false
      const countdownMinute = time || 30 // 1分钟倒计时

      const startTimes = new Date() // 开始时间 new Date('2016-11-16 15:21');
      const endTimes = new Date(startTimes.setSeconds(startTimes.getSeconds() + countdownMinute)) // 结束时间
      const curTimes = new Date() // 当前时间
      let surplusTimes = endTimes.getTime() / 1000 - curTimes.getTime() / 1000 // 结束毫秒-开始毫秒=剩余倒计时间

      // 进入倒计时
      let countdowns = setInterval(() => {
        surplusTimes--
        // eslint-disable-next-line no-unused-vars
        let minu = '' + Math.floor(surplusTimes / 60)
        let secd = '' + Math.round(surplusTimes % 60)
        // console.log(minu+':'+secd);
        minu = minu.length === 1 ? '0' + minu : minu
        secd = secd.length === 1 ? '0' + secd : secd
        this.getVerificationTxt = secd + '秒后重试'
        if (surplusTimes <= 0) {
          // console.log('时间到！');

          this.getVerificationTxt = '获取验证码'
          clearInterval(countdowns)
          countdowns = true
          this.isAllowGetNum = true
        }
      }, 1000)
    }
  },
  onReady() {
    if (this.curSelectUserInfo) {
      this.regForm.phone = this.curSelectUserInfo.phone
    }
  },
  mounted() {
    // #ifdef MP-WEIXIN
    this.handleClickTrack('OperationDetailsPageView')
    // #endif
  }
}
</script>

<style lang="scss" scoped>
uni-button:after{
  border: none !important;
}
.main-body {
  height: 100%;
  .main{
    .main-text{
      padding: 10upx 30upx;
      height: 40upx;
      line-height: 40upx;
      color: #999;
      font-size: 24upx;
    }
    .main-content{
      background-color: #fff;
      border-bottom: 2upx solid $contentDdt;
      padding: 0 30upx;
      position: relative;
      height: 88upx;
      .l{
        height: 88upx;
        line-height: 88upx;
        width: 120upx;
        display: inline-block;
        vertical-align: middle;
        color: #333;
        font-size: 32upx;
      }
      .r{
        height: 88upx;
        width: calc(100% - 120upx);
        display: inline-block;
        vertical-align: middle;
      }
      .verification{
        position: absolute;
        right: 30upx;
        top: 14upx;
        text-align: center;
        color: #fff;
        font-size: 24upx;
        width: 160upx;
        height: 60upx;
        //background: linear-gradient(90deg,rgba(255,185,36,1),rgba(254,219,55,1));
        background: $topicC;
        @include rounded(30upx);
        line-height: 60upx;
      }
    }
    .main-prompt{
      padding: 20upx 30upx;
      color: #FF4A4A;
      font-size: 24upx;
    }
  }
  .btn{
    width: 100%;
    padding: 0 30upx;
    margin-top: 50upx;
    button{
      width: 100%;
      //background: linear-gradient(90deg,rgba(255,185,36,1),rgba(254,219,55,1));
      background: $topicC;
      @include rounded(44upx);
    }
  }
}
.width-calc-310{
  width: calc(100% - 310upx) !important;
}
.f-w-medium{
  color: #bfbfbf;
  font-weight: Medium;
  font-size: 30upx;
}
.is-get-code {
  background-color: #ed6d46;
  color: rgba(255, 255, 255, 1);
  opacity: 0.5;
}
</style>
