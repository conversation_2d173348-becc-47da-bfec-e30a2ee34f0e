<template>
  <view class="caselist">
    <template v-for="(item, idx) in list">
      <!-- 病例列表 -->
      <template v-if="istype == 0">
        <view class="case-item" :key="item.id" @tap="toEdit2(item.activityId, 'questionnaireFillin', item.id, true)">
          <view class="case-item-t">{{ item.title }}</view>

          <!-- 待审核 -->
          <template v-if="type == 0">
            <view class="case-item-info">提交人：{{ item.userName }}</view>
            <view class="case-item-info">问卷编号：{{ item.id }}</view>
            <view class="case-item-info">提交时间：{{ item.updateTimeText }}</view>
            <!-- <view class="case-item-info">审核状态：</view> -->
            <view class="case-item-info">填写进度：{{ item.progressText }} %</view>
            <view class="case-item-btns">
              <view class="case-item-btn submit" @click.stop="submitPublish(item.id, item.progress)">提交发布</view>
              <view class="case-item-btn" @click.stop="toEdit2(item.activityId, 'questionnaireFillin', item.id)">编辑</view>
              <view class="case-item-btn" @click.stop="deleteItem(item.id, idx)">删除</view>
            </view>
            <view class="tip-ico">待审核</view>
          </template>
          <template v-if="type == 1">
            <view class="case-item-info">提交人：{{ item.userName }}</view>
            <view class="case-item-info">问卷编号：{{ item.id }}</view>
            <view class="case-item-info">提交时间：{{ item.updateTimeText }}</view>
            <view class="case-item-info">审核状态：待审核</view>


            <view class="case-item-btns">
              <view class="case-item-btn" @click.stop="toEdit2(item.activityId, 'questionnaireFillin', item.id,true)">详情</view>
            </view>
            <!-- <view class="case-item-info">填写进度：{{ item.progressText }} %</view> -->
           <!-- <view class="case-item-btns">
              <view class="case-item-btn submit" @click="submitPublish(item.id, item.progress)">提交发布</view>
              <view class="case-item-btn" @click="toEdit2(item.activityId, 'questionnaireFillin', item.id)">编辑</view>
              <view class="case-item-btn" @click="deleteItem(item.id, idx)">删除</view>
            </view> -->
            <view class="tip-ico">待审核</view>
          </template>
          <!-- 审核通过 -->
          <template v-if="type == 2">
            <view class="case-item-info">提交人：{{ item.userName }}</view>
            <view class="case-item-info">问卷编号：{{ item.id }}</view>
            <view class="case-item-info">提交时间：{{ item.updateTimeText }}</view>
            <view class="case-item-info">审核状态：已通过</view>
            
            <view class="case-item-btns">
              <view class="case-item-btn" @click.stop="toEdit2(item.activityId, 'questionnaireFillin', item.id,true)">详情</view>
            </view>

          </template>
          <!--  -->

          <!-- 审核不通过 -->
          <template v-if="type == 3">
            <view class="case-item-info">发布时间：{{ item.issueDateText }}</view>
            <view class="case-item-info">最后更新时间：{{ item.id }}</view>
            <view class="case-item-info">审核时间：{{ item.updateTimeText }}</view>
            <view class="case-item-info">填写进度：{{ item.progressText }} %</view>
            <view class="case-item-info">不通过原因：{{ item.desc }}</view>

            <view class="case-item-btns">
              <view class="case-item-btn submit" @click.stop="submitPublish(item.id, item.progress)">再次发布</view>
              <!-- <view class="case-item-btn" @click="toEdit(itemid)">编辑</view>
            <view class="case-item-btn">删除</view> -->
              <view class="case-item-btn" @click.stop="toEdit2(item.activityId, 'questionnaireFillin', item.id)">编辑</view>
              <view class="case-item-btn" @click.stop="deleteItem(item.id, idx)">删除</view>
            </view>
          </template>
        </view>
      </template>
    </template>
    <view class="o-space">

    </view>
  </view>
</template>

<script>
export default {
  name: 'questionListitem',
  props: {
    list: {
      type: Array,
      default: function() {
        return [];
      }
    },
    type: {
      type: Number,
      default: '0'
    },
    istype: {
      type: Number,
      default: 0
    },
    pageid: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      activitytype:2,
    };
  },
  methods: {
    // 删除节点操作 casecollectsubmitlogdeletebatch
    deleteItem(ids, idx) {
      // this.$emit('remove',idx)
      const that = this;
      uni.showModal({
        title: '温馨提示',
        content: '此操作会移除选中条目，是否继续？',
        success(res) {
          if (res.confirm) {
            // console.log('用户点击确定');
            that.$api.activity
              .casecollectsubmitlogdeletebatch({
                ids: ids,
                activityType:that.activitytype
              })
              .then(res => {
                that.$uniPlugin.toast('提交成功');

                // this.list
                that.$emit('remove', idx);

                // if (obj.next) {
                //   this.$emit('next');
                // }
                // this.getDetail(this.id);
              });
          } else if (res.cancel) {
            console.log('用户点击取消');
          }
        }
      });
      // return;
    },
    // 提交发布
    submitPublish(id, progress) {
      // casecollectsubmitlogcommitwait
      // if(progress == 1)
      let param = {
        id: id,
        activityType:this.activitytype
      };
      this.$api.activity.casecollectsubmitlogcommitwait(param).then(res => {
        this.$uniPlugin.toast('提交成功');
        this.$emit('next', {
          count: 1
        });

        // if (obj.next) {
        //   this.$emit('next');
        // }
        // this.getDetail(this.id);
      });
    },
    toEdit(id, target, activityId, parentBusinessId) {
      this.$navto.push(target, {
        id: id,
        visitid: activityId,
        parentBusinessId: parentBusinessId,
        activityType:this.activitytype
      });
    },
    toEdit2(id, target, mainId,disabled) {
      this.$navto.push(target, {
        id: id,
        mainId: mainId,
        back: true,
        activityType:this.activitytype,
        disabled,
        // parentBusinessId:parentBusinessId
      });
    }
  }
};
</script>

<style lang="scss" scoped>
  .o-space{
    height: 100px;
    width: 100%;
  }
  // .caselist{
  //   padding-bottom: 110upx;
  // }
.cannel {
  display: inline-block;
  padding: 5upx 30upx;
  background-color: #dbdbdb;
  color: #fff;
  border-radius: 10upx;
  font-size: 24upx;
  margin: 10rpx 0;
}
.success {
  display: inline-block;
  padding: 5upx 30upx;
  background-color: $topicC;
  color: #fff;
  border-radius: 10upx;
  font-size: 24upx;
  margin: 10rpx 0;
}
.add-item {
  display: flex;
  align-items: center;
  height: 80upx;
  // border-top: 1upx solid #dbdbdb;
  background-color: #fff;
  margin: 0 20upx;
  justify-content: center;
  font-size: 28upx;
  color: #b7685b;
  border-radius: 10upx;
}
.add-ico {
  width: 20upx;
  height: 2upx;
  background-color: #bacad9;
  margin-right: 10upx;
}
.case-item {
  background-color: #fff;
  margin: 20upx;
  padding: 40upx 30upx;
  border-radius: 10upx;
  overflow: hidden;
  position: relative;
  .tip-ico {
    position: absolute;
    right: 0;
    top: 0;
    color: #c55e57;
    background-color: #fde9e6;
    border-bottom-left-radius: 35upx;
    border-top-right-radius: 35upx;
    padding: 5upx 30upx;
    font-size: 24upx;
  }
  .case-item-t {
    font-size: 32upx;
    font-weight: 550;
    margin-bottom: 40upx;
  }

  .case-item-info {
    font-size: 24upx;
    line-height: 2;
  }
  .case-item-timer {
    color: #7c7c7c;
    font-size: 24upx;
    line-height: 1.5;
  }
  .case-item-btns {
    margin-top: 20upx;
  }
  .case-item-btn {
    display: inline-flex;
    align-items: center;
    height: 70upx;
    border-radius: 10upx;
    overflow: hidden;
    margin-top: 10upx;
    background-color: #a4adb3;
    padding: 0 60upx;
    color: #fff;
    margin-right: 20rpx;
  }

  .case-item-btn.submit {
    border: 1upx solid #dbdbdb;
    color: #000;
    background-color: #fff;
  }

  .case-item-btn-info {
    background-color: #34cbc7;
    color: #fff;
    height: 100%;
    display: flex;
    align-items: center;
    padding: 0 20upx;
    font-size: 24upx;
  }
  .case-item-btn-t {
    background-color: $topicC;
    color: #fff;
    height: 100%;
    display: flex;
    align-items: center;
    padding: 0 20upx;
    font-size: 24upx;
  }
}
</style>
