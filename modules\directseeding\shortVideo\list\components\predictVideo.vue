<template>
	<view class="video">
		<view class="back-up" @click="closeVideo">
			<!-- <image class="backIcon" :src="clear"></image> -->
		</view>
		<video  id="myVideo" style="width: 100vw;height: 94vh;margin-top: 6vh;" :src="videoPath"
			:controls="true" :muted="muted" :poster="poster" :show-center-play-btn="false"
			:show-play-btn="true" :autoplay='true'>
		</video>
	</view>
</template>

<script>
	export default{
		data(){
			return {
			}
		},
		methods: {
			// 关闭视频组件
			closeVideo(){
				this.$emit('close')
			},
		},
		props:{
			videoPath: {
				type: String,
				default: "",
			},
			title: {
				type: String,
				default: '',
			},
			poster: {
				type: String,
				default: "",
			},
			muted:{
				type:Boolean,
				default:false
			}
		}
	}
</script>

<style lang="scss">
	.video{
		position: fixed;
		background: black;
		z-index: 99999999999999999999;
	}
	.back-up {
		height: 20rpx;
		width: 20rpx;
		border: solid white;
		border-width: 4rpx 4rpx 0 0;
		transform: rotate(-135deg);
		position: absolute;
		top: 125rpx;
		left: 23rpx;
		z-index: 999999999999999999;
			.backIcon{
				width: 100%;
				height: 100%;
			}
		}
</style>