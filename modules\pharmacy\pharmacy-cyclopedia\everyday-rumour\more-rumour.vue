<template>
  <view class="main">
    <view class="top-head-main"></view>
    <view :style="'height:' + statusBarHeight + 'px;'"></view>
    <view class="top-nav">
      <view class="top-nav-l" @click.stop="handleBack"><image mode="aspectFit" :src="file_ctx + 'static/image/business/hulu-v2/icon-circle-left-arrows.png'" class="header-search-img"/></view>
      <view class="top-nav-c">更多辟谣</view>
    </view>
    <view class="l-main">
      <tabs-sticky :fontBigger="true" :bdb="false" :overflowX="true" :overflowY="false" v-model="curIndex" :tabs="tabs" @change="changeTab"></tabs-sticky>
    </view>
    <view class="look-rumour-ranking"  @click="handleLookRankingList">
      <view class="text">查看辟谣排行榜<image class="text-right" :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-rumour-right-new.png'"></image></view>
    </view>
    <view class="top-bott-main"></view>
    <scroll-refresh class="scroll-refresh-main" :isShowEmptySwitch="true" :fixed="false" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
      <view class="rumour-list">
        <view class="rumour-item" v-for="item in contentList" :key="item.id">
          <!-- <view class="rumour-item-date">{{item.answerDateText}}</view> -->
          <view class="rumour-item-content">
            <view class="item-img" v-if="item.answered == 1">
              <image v-if="item.answer == item.userAnswer || item.answerState == 1" :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-rumour-correct.png'" mode="aspectFill"></image>
              <image v-else :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-rumour-error.png'" mode="aspectFill"></image>
            </view>
            <view class="item-title" :style="{marginBottom:item.answered == 1?'0rpx':'40rpx'}">{{item.title}}</view>
            <view class="item-answer" v-if="item.answered == 1">
              <view class="item-answer-title">正确答案：<span>{{item.answerText}}</span></view>
              <view class="item-answer-content" :class="{lineclamp3:item.isAll}">{{item.answerContent}}</view>
              <view v-if="item.isMore">
                <span v-if="item.isAll" @click="handleClickType(item,index)">展开</span>
                <span v-else @click="handleClickType(item,index)">收起</span>
              </view>
              <view class="rumour-item-date">
                <view class="rumour-item-date-l">{{item.answerDateText}}</view>
                <view class="rumour-item-date-r" v-if="item.answered == 1" @click="$navto.push('EverydayMorePoster',{id:item.id})"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-rumour-more-share.png'"></image>海报分享</view>
              </view>
            </view>
            <view class="item-bott" v-else>
              <view class="item-bott-l"><button class="real-btn-l" @click="handleLookAnswer(1,item)">真的</button></view>
              <view class="item-bott-r"><button class="real-btn-r" @click="handleLookAnswer(0,item)">假的</button></view>
            </view>
          </view>
        </view>
      </view>
    </scroll-refresh>
    <view class="present-btn" @click="$navto.push('integrationShop')"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-rumour-present-img.png'"></image>积分兑好礼</view>
    <!-- <codePopup :isTaskControl="true"></codePopup> -->
  </view>
</template>

<script>
  import { mapState } from 'vuex'
  // import codePopup from './components/codePopup.vue'
  import TabsSticky from '@/components/basics/tabs-sticky-v3'
  // import { isDomainUrl } from '@/utils/index.js'
  export default {
    components: {
      TabsSticky,
      // codePopup
    },
    data(){
      let currentDate = new Date()
      let currentYear = currentDate.getFullYear()
      return{
        file_ctx: this.file_ctx,
        $appId:this.$appId,
        mescroll: null, // mescroll实例对象
        curIndex: 0,
        tabs: [],
        downOption: {
          auto: false // 不自动加载
        },
        upOption: {
          auto: false, // 不自动加载
          empty: {
            top: 0,
            zIndex: 999,
          }
        },
        contentList:[],
        tabsAll:[],
        refuteRumortitle:null,
        refuteRumorObj:null,
        currentDate:currentDate,
        // 创建起始日期：当前年份的1月1日 00:00:00
        startDate : new Date(currentYear, 0, 1, 0, 0, 0), // 月份从0开始，0表示1月；时、分、秒都设置为0
        statusBarHeight: 0,
      }
    },
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId,
        fansRecord: state => state.fansRecord,
        isLogin: state => state.isLogin
      }),
    },
    watch: {
      refuteRumorObj:{
        handler(){
          // if(this.refuteRumorObj.id){
            this.$nextTick(() => {
              this.init()
            })
          // }
        },deep:true
      }
    },
    onLoad(){
      this.refuterumortypeQueryPage()
    },
    onShareAppMessage (res) {
      return {
        title: '更多辟谣', //分享的名称
        path: 'modules/pharmacy/pharmacy-cyclopedia/everyday-rumour/more-rumour',
        mpId:this.$appId, //此处配置微信小程序的AppId
        imageUrl: this.file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-rumour-transpond.jpg',
      }
    },
    onShareTimeline(){
      return {
        title: '每日辟谣',
        path: 'modules/pharmacy/pharmacy-cyclopedia/everyday-rumour/more-rumour',
      }
    },
    mounted(){
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
    },
    methods:{
      handleLookRankingList(){
        if(!this.isLogin){
          this.$navto.push('Login',{formPage: 'RumourRankingList'})
        } else {
          this.$navto.push('RumourRankingList')
        }
      },
      handleBack(){
        this.$navto.back(1)
      },
      handleClickType(item){
        item.isAll = !item.isAll
      },
      changeTab(index) {
        this.curIndex = index
        this.refuteRumorObj = this.tabsAll[index]
        this.refuteRumortitle = this.tabsAll[index].name
      },
      async refuterumortypeQueryPage(){
        let params = {
          current:1,
          size:50,
          condition:{
            state:1,
            order:1, // 1升序 0倒序
          }
        }
        this.$api.drugBook.refuterumortypeQueryPage(params).then(res=>{
          this.tabsAll = [{name:'全部',id:null},...res.data.records]
          this.refuteRumorObj = this.tabsAll[0]
          this.tabs =this.tabsAll.map(item=>({name:item.name}))
        })
      },
      async handleLookAnswer(type,item){
        let that = this
        let typeText = item.answer == type ? '恭喜你，答对了' : '很遗憾，答错了'
        item.userAnswer = type
        that.$uniPlugin.modal('提示', typeText, {
          showCancel: false, // 是否显示取消按钮，默认为 true
          cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
          cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
          confirmText: '我知道了', //  确定按钮的文字，默认为"确定"，最多 4 个字符
          confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
          fn: async (n) => {
            if (n) {
              let params = {
                accountId:that.accountId, //账户id
                username:that.fansRecord.nickName, //用户昵称
                userType:1, //用户类型 1-活跃用户2-用户(马甲)
                // refuteRumorDetailId:item.refuteRumorId, //辟谣详情主键id
                refuteRumorDetailId:item.id, //辟谣详情主键id
                recordType:1, //操作类型 1-答题2-分享3-订阅
                answer:type, //答题 1真的 0假的
              }
              const res = await that.$api.drugBook.refuterumorrecordInsert(params)
              if(res.data !== ""){
                item.answered = 1
                // item.answerState = type
              }
              // this.$uniPlugin.toast(res.msg)
              // this.init()
            }
          }
        })
      },
      returnFn(obj) {
        const that = this
        setTimeout(function () {
          let params = {
            current: obj.pageNum,
            size: obj.pageSize,
            condition: {
              refuteRumorId:that.refuteRumorObj.id,
              // title:that.refuteRumorObj.name,
              accountId:that.accountId,
              state:1,
              startAnswerDate:that.$common.formatDate(that.startDate, 'yyyy-MM-dd HH:mm:ss'), //开始时间
              endAnswerDate:that.$common.formatDate(that.currentDate, 'yyyy-MM-dd HH:mm:ss'), //结束时间
            }
          }
          that.$api.drugBook.refuterumordetailQueryPage(params).then(res => {
            // let data = res.data.records.map(item=>({...item,listCover:isDomainUrl(item.listCover)}))
            let data = res.data.records.map(item=>{
              if(item.answerContent.length > 80){
                item.isMore = true
                item.isAll = true
              } else {
                item.isMore = false
                item.isAll = false
              }
              return {
                ...item,
                answerDateText:item.answerDate?that.$common.formatDate(new Date(item.answerDate),'yyyy-MM-dd').replace(/-/g, '/'):'',
                answerText:item.answer == 0 ? '假的' : '真的',
                userAnswer:null,
                // isShow:false,
              }
            })
            if (obj.pageNum === 1) {
              that.contentList = []
            }
            that.contentList = [...that.contentList, ...data]
            obj.successCallback && obj.successCallback(data)
          })
        }, that.$constant.noun.scrollRefreshTime)
      },
      scrollInit(scroll) {
        scroll.optUp.page.num = 1
        scroll.optUp.page.size = 10
        this.mescroll = scroll
      },
      init() {
        this.$nextTick(() => {
          this.mescroll.triggerDownScroll()
        })
      },
    },
 }
</script>

<style lang='scss' scoped>
  @mixin contentFlex {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .main{
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
    background: #F4F6FA;
  }
  .top-nav{
    // position: fixed;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 44px;
    line-height: 44px;
    // z-index: 999;
    padding: 0 32rpx 0 24rpx;
    .top-nav-l{
      display: flex;
      width: 48rpx;
      height: 48rpx;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .top-nav-c{
      flex: 1;
      text-align: center;
      height: 44rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #2D2F38;
      line-height: 44rpx;
      margin-right: 48rpx;
    }
  }
  .top-head-main{
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    height: 282rpx;
    background-image: url($imgUrl + '/business/pharmacy-cyclopedia/icon-everyday-rumour-header-bg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
  .l-main{
    position: sticky;
    top: 0;
    z-index: 999;
    /deep/.tabs-sticky{
      padding:22rpx 32rpx 22rpx;
      overflow-y: hidden;
      background-color: transparent !important;
      .tabs-sticky-body{
        padding: 0;
        .tab{
          text{
            padding: 0;
          }
          &:last-child{
            padding-right: 50rpx;
          }
        }
      }
    }
  }
  .present-btn{
    position: absolute;
    left: 50%;
    bottom: 40rpx;
    z-index: 999;
    transform: translateX(-50%);
    @include contentFlex;
    width: 290rpx;
    height: 88rpx;
    background: #00B484;
    box-shadow: 0rpx 4rpx 12rpx 0rpx rgba(9,97,73,0.45);
    border-radius: 44rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #FFFFFF;
    image{
      display: flex;
      width: 34rpx;
      height: 32rpx;
      margin-right: 8rpx;
    }
  }

  .scroll-refresh-main{
    flex: 1;
    overflow-x: hidden;
    margin-top: 14rpx;
    /deep/ .mescroll-empty-box{
      position: absolute !important;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .look-rumour-ranking{
    position: relative;
    display: flex;
    align-items: center;
    width: 686rpx;
    height: 104rpx;
    background-image: url($imgUrl + '/business/pharmacy-cyclopedia/icon-more-look-rankinglist-new.png');
    background-size: 100%;
    margin-left: 32rpx;
    z-index: 999;
    .text{
      display: flex;
      align-items: center;
      padding-left: 32rpx;
      font-size: 32rpx;
      color: #1D2029;
      font-weight: 600;
      .text-right{
        display: flex;
        width: 16rpx;
        height: 28rpx;
        margin: 4rpx 0 0 10rpx;
      }
    }
  }
  .top-bott-main{
    position: absolute;
    height: 172rpx;
    top: 136px;
    width: 100%;
    background-image: url($imgUrl + '/business/pharmacy-cyclopedia/icon-everyday-rumour-header-c-bg.png');
  }

  .rumour-list{
    position: relative;
    height: calc(100% - 30rpx);
    // padding:30rpx 20rpx 0;
    overflow-x: hidden;
    background-color: #f0f2f2;
    .rumour-item{
      margin-bottom: 20rpx;
      padding: 0 32rpx;
      .rumour-item-content{
        position: relative;
        padding: 32rpx 24rpx;
        margin-top: 10rpx;
        border-radius: 16rpx;
        background-color: #fff;
        .item-img{
          position: absolute;
          right: 0;
          top: 30rpx;
          display: flex;
          // width: 150rpx;
          width: 174rpx;
          height: 136rpx;
          // transform: rotate(-30deg);
          opacity: .3;
          image{
            width: 100%;
            height: 100%;
          }
        }
        .item-title{
          font-size: 30rpx;
          font-weight: 600;
          color:#000;
          width: 480rpx;
        }
        .item-answer{
          margin-top: 32rpx;
          .item-answer-title{
            font-weight: 600;
            font-size: 30rpx;
            color: #4E5569;
            margin-bottom: 24rpx;
            span{
              font-size: 30rpx;
              color: #00B484;
            }
          }
          .item-answer-content{
            word-wrap: break-word;
            font-size: 26rpx;
            color: #4E5569;
            line-height: 44rpx;
          }
          .rumour-item-date{
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 24rpx;
            .rumour-item-date-l{
              color: #868C9C;
            }
            .rumour-item-date-r{
              @include contentFlex;
              width: 192rpx;
              height: 56rpx;
              border-radius: 28rpx;
              border: 1rpx solid #00B484;
              font-size: 26rpx;
              color: #00B484;
              font-weight: 600;
              image{
                display: flex;
                width: 36rpx;
                height: 32rpx;
                margin-right: 6rpx;
              }
            }
          }
          .lineclamp3{
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            white-space: normal;
          }
        }
        .item-bott{
          display: flex;
          justify-content: space-between;
          .item-bott-l,.item-bott-r{
            width: 48%;
            button{
              @include contentFlex;
              height: 88rpx;
              background: #F4F6FA;
              border-radius: 44rpx;
              color: #1D2029;
              font-size: 30rpx;
              &::after{
                border: none !important;
              }
            }
          }
        }
      }
      &:last-child{
        margin-bottom: 0;
      }
    }
  }
</style>
