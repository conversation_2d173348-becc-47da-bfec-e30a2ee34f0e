<template>
  <view class="container">
    <!-- 申请状态提示 -->
    <view class="application-status" v-if="applicationStatus === 1">
      <view class="application-status-content">
        <view class="application-status-title">陪诊师申请已提交，请支付报名费</view>
        <view class="application-status-desc">完成支付后，您的申请将进入审核流程</view>
      </view>
      <view class="application-status-btn" @click="handlePayFee">支付 ¥{{ formatFee(employeeFee) }} 报名费</view>
      <view class="application-status-back-btn" @click="backToHome">返回首页</view>
    </view>

    <view class="application-status" v-if="applicationStatus === 2">
      <view class="application-status-content">
        <view class="application-status-title">您的申请正在审核中</view>
        <view class="application-status-desc">请耐心等待，审核结果将会通知您</view>
      </view>
      <view class="application-status-back-btn" @click="backToHome">返回首页</view>
    </view>

    <!-- 审核不通过状态 -->
    <view class="application-status" v-if="applicationStatus === 3">
      <view class="application-status-content">
        <view class="application-status-title">审核不通过</view>
        <view class="application-status-desc">不通过原因：{{ failReason }}</view>
      </view>
      <view class="application-status-btn" @click="handleReapply">重新申请</view>
      <view class="application-status-back-btn" @click="backToHome">返回首页</view>
    </view>

    <!-- 表单主体 -->
    <view class="boxTab" v-if="applicationStatus !== 1 && applicationStatus !== 2 && applicationStatus !== 3">
      <!-- 头部表单区域 -->
      <view class="botTab">
        <view class="botTabLine avatar-line">
          <view class="lineTitle"><span class="required">*</span>形象照</view>
          <view class="upload-box" @click="handleUploadAvatar">
            <view v-if="form.avatar" class="preview-container">
              <image :src="form.avatar.url || form.avatar" class="preview-image" mode="aspectFill"/>
            </view>
            <view v-else class="empty-upload">
              <view class="plus-icon"></view>
            </view>
          </view>
        </view>

        <view class="botTabLine">
          <view class="lineTitle"><span class="required">*</span>姓名</view>
          <input
            type="text"
            placeholder="请输入真实姓名"
            class="bookInput"
            v-model="form.name"
            @focus="handleNameFocus"
          />
        </view>

        <view class="botTabLine" @tap.stop="checkLoginBeforeShow">
          <view class="lineTitle"><span class="required">*</span>性别</view>
          <view class="select-wrapper">
            <picker mode="selector" :range="genderListText" @change="handleGenderChange" :disabled="!isLogin" style="width: 100%;">
              <view class="picker-inner">
                <text>{{form.gender || '请选择'}}</text>
                <image class="lineIcon" :src="iconRightArrow" mode=""></image>
              </view>
            </picker>
          </view>
        </view>

        <view class="botTabLine" @tap.stop="checkLoginBeforeShow">
          <view class="lineTitle"><span class="required">*</span>服务城市</view>
          <view class="select-wrapper">
            <picker mode="region" @change="handleCityChange" level="city" :disabled="!isLogin" style="width: 100%;">
              <view class="picker-inner">
                <text>{{getSelectCity}}</text>
                <image class="lineIcon" :src="iconRightArrow" mode=""></image>
              </view>
              <text v-if="form.city && form.city.split(',').length > 0" @tap.stop="showCityList" class="view-all">查看</text>
            </picker>
          </view>
        </view>

        <view class="botTabLine" @tap.stop="checkLoginBeforeShow">
          <view class="lineTitle"><span class="required">*</span>擅长语言</view>
          <view class="select-wrapper">
            <picker mode="selector" :range="languageListText" @change="handleLanguageChange" :disabled="!isLogin" style="width: 100%;">
              <view class="picker-inner">
                <text>{{getSelectLanguage}}</text>
                <image class="lineIcon" :src="iconRightArrow" mode=""></image>
              </view>
            </picker>
          </view>
        </view>

        <view class="botTabLine">
          <view class="lineTitle"><span class="required">*</span>年龄</view>
          <input type="number" placeholder="请输入年龄" class="bookInput" v-model="form.age" @focus="handleAgeFocus"/>
        </view>

        <view class="botTabLine">
          <view class="lineTitle"><span class="required">*</span>手机号</view>
          <input type="text" placeholder="请输入手机号码" class="bookInput" v-model="form.phone" @focus="handlePhoneFocus"/>
        </view>

        <view class="botTabLine">
          <view class="lineTitle"><span class="required">*</span>身份证号</view>
          <input type="text" placeholder="请输入身份证号码" class="bookInput" v-model="form.idcard" @focus="handleIdCardFocus"/>
        </view>

        <view class="botTabLine">
          <view class="lineTitle">毕业院校</view>
          <input type="text" placeholder="请输入毕业院校" class="bookInput" v-model="form.school" @focus="handleSchoolFocus"/>
        </view>
        <view class="additionalContent">
          <view class="lineTitle"><span class="required">*</span>身份证</view>
          <view class="idcard-upload-container">
            <view class="idcard-item" v-if="form.idCardFront" @click="handlePreviewIdCardFront">
              <image class="idcard-delete" @click.stop="handleDeleteIdCardFront" :src="iconPostMenuClose" mode=""></image>
              <image class="idcard-image" :src="form.idCardFront.url || form.idCardFront"></image>
            </view>
            <view class="idcard-item empty-idcard" v-else @click="handleUploadIdCardFront">
              <image class="idcard-icon" :src="IDCardFace" mode=""></image>
              <view class="idcard-text">人像面</view>
            </view>

            <view class="idcard-item" v-if="form.idCardBack" @click="handlePreviewIdCardBack">
              <image class="idcard-delete" @click.stop="handleDeleteIdCardBack" :src="iconPostMenuClose" mode=""></image>
              <image class="idcard-image" :src="form.idCardBack.url || form.idCardBack"></image>
            </view>
            <view class="idcard-item empty-idcard" v-else @click="handleUploadIdCardBack">
              <image class="idcard-icon" :src="IDCardNationalEmblemFace" mode=""></image>
              <view class="idcard-text">国徽面</view>
            </view>
          </view>
        </view>
        <view class="additionalContent">
          <view class="lineTitle">经验描述</view>
          <textarea
            class="experience-input"
            placeholder="请描述您的陪诊经验（200字以内）"
            :value="form.experience"
            @input="handleExperienceInput"
            @focus="handleExperienceFocus"
            :maxlength="10000"
          ></textarea>
          <text class="word-counter" :class="{'word-counter-exceed': form.experience.length > 200}">{{ form.experience.length }}/200</text>
        </view>

        <!-- 隐藏上传组件 -->
        <view class="lineHide">
          <title-img :config="{}" ref="avatarUploader" @returnFn="avatarReturnFn" :cData="avatarList"></title-img>
        </view>
        <view class="lineHide">
          <title-img :config="{}" ref="idCardFrontUploader" @returnFn="idCardFrontReturnFn" :cData="idCardFrontList"></title-img>
        </view>
        <view class="lineHide">
          <title-img :config="{}" ref="idCardBackUploader" @returnFn="idCardBackReturnFn" :cData="idCardBackList"></title-img>
        </view>
      </view>

      <!-- 错误提示区域 -->
      <!-- <view class="error-message" v-if="validationError">
        <view class="error-icon">!</view>
        <text>{{validationError}}</text>
      </view> -->

      <!-- 底部 -->
      <view class="bottomBtn">
        <view class="agreement-row" @click="handleToggleAgreement">
          <view class="custom-checkbox">
            <view
              class="checkbox-icon"
              :class="{ checked: isAgreed }"
            ></view>
          </view>
          <text class="agreement-text">我已阅读并同意</text>
          <text class="agreement-link" @tap.stop="handleNavToAgreement">【陪诊师入驻协议】</text>
        </view>
        <view class="comBtn" @click="handleSubmit">{{ showEmployeeFee ? '支付 ¥' + formatFee(employeeFee) + ' 报名费' : '提交申请' }}</view>
      </view>
    </view>

    <!-- 提交成功弹窗 -->
    <view class="success-popup" v-if="showSuccessDialog">
      <view class="success-popup-mask" @click.stop></view>
      <view class="success-popup-content">
        <view class="success-popup-title">提示</view>
        <view class="success-popup-body">已提交申请，等待审核</view>
        <view class="success-popup-btn" @click="backToHome">回到首页</view>
      </view>
    </view>

    <!-- 支付失败弹窗 -->
    <view class="success-popup" v-if="showPaymentFailedDialog">
      <view class="success-popup-mask" @click.stop></view>
      <view class="success-popup-content">
        <view class="success-popup-title">支付未完成</view>
        <view class="success-popup-body">您的申请已提交，但支付未完成</view>
        <view class="success-popup-btn-group">
          <view class="success-popup-btn-half border-right" @click="closePaymentFailedDialog">关闭</view>
          <view class="success-popup-btn-half highlight" @click="retryPayment">重新支付</view>
        </view>
      </view>
    </view>

    <!-- 城市列表弹窗 -->
    <view class="city-popup" v-if="showCityDialog">
      <view class="city-popup-mask" @click="closeCityList"></view>
      <view class="city-popup-content">
        <view class="city-popup-title">
          <text>已选择城市</text>
          <image class="city-popup-close" :src="iconPostMenuClose" @click="closeCityList"></image>
        </view>
        <view class="city-popup-body">
          <view class="city-group" v-for="(group, index) in getAllSelectedCities" :key="index">
            <view class="city-province">{{group.province}}</view>
            <view class="city-list">
              <view class="city-item" v-for="(city, cityIndex) in group.cities" :key="cityIndex">
                <text>{{city}}</text>
                <view class="city-delete" @click="removeCity(group.province, city)">×</view>
              </view>
            </view>
          </view>
          <view class="no-cities" v-if="getAllSelectedCities.length === 0">
            暂无选择城市
          </view>
        </view>
        <view class="city-popup-btn" @click="closeCityList">确定</view>
      </view>
    </view>
  </view>
</template>

<script>
// 导入城市数据
import cityData from '../components/title-select-address/city-data.js'
import common from '@/common/util/main'
import serverOptions from '@/config/env/options'
import TitleImg from "@/components/business/module/title-img/index.vue"
import { mapState } from 'vuex' // 导入 mapState

// 表单验证配置
const VALID_RULES = {
  avatar: { required: true, message: '请上传形象照' },
  name: {
    required: true,
    validator: value => value && value.length >= 2 && value.length <= 10,
    message: '姓名需为2-10个字符'
  },
  gender: { required: true, message: '请选择性别' },
  age: {
    required: true,
    validator: value => value && parseInt(value) >= 18 && parseInt(value) <= 65,
    message: '年龄需在18-65岁之间'
  },
  phone: {
    required: true,
    validator: value => value && /^1[3-9]\d{9}$/.test(value),
    message: '手机号格式不正确'
  },
  idcard: {
    required: true,
    validator: value => value && /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(value),
    message: '身份证号格式不正确'
  },
  province: {
    required: true,
    validator: value => value && value.length > 0,
    message: '请选择服务城市'
  },
  city: {
    required: true,
    validator: value => value && value.length > 0,
    message: '请选择服务城市'
  },
  language: {
    required: true,
    validator: value => value && value.length > 0,
    message: '请选择擅长语言'
  },
  idCardFront: { required: true, message: '请上传身份证人像面' },
  idCardBack: { required: true, message: '请上传身份证国徽面' }
}

export default {
  components: {
    TitleImg
  },
  data() {
    return {
      form: {
        avatar: '',
        name: '',
        gender: '',
        age: '',
        phone: '',
        idcard: '',
        city: '',
        province: '',
        language: '',
        school: '',
        experience: '',
        idCardFront: '',
        idCardBack: ''
      },
      // 服务商报名费相关
      showEmployeeFee: false, // 是否显示报名费
      employeeFeeButton: 0, // 报名费开关
      employeeFee: 0, // 报名费金额(分)
      employeeFeeContent: '', // 报名费内容

      validationError: '', // 用于存储验证错误信息
      isAgreed: false, // 是否同意协议
      showSuccessDialog: false, // 控制成功弹窗的显示
      showPaymentFailedDialog: false, // 控制支付失败弹窗的显示
      showCityDialog: false, // 控制城市弹窗的显示
      iconRightArrow: this.$static_ctx + "image/business/hulu-v2/icon-right-arrow.png",
      iconPostUpload: this.$static_ctx + "image/business/hulu-v2/icon-post-upload.png",
      iconPostMenuClose: this.$static_ctx + "image/business/hulu-v2/icon-close.png",
      IDCardFace: this.$static_ctx + "image/business/hulu-v2/IDCardFace.png",
      IDCardNationalEmblemFace: this.$static_ctx + "image/business/hulu-v2/IDCardNationalEmblemFace.png",
      file_ctx: this.file_ctx,
      provinceMap: [],
      genderList: [
        { text: '男', value: '男' },
        { text: '女', value: '女' }
      ],
      genderListText: ['男', '女'],
      languageList: [
        { value: '普通话', label: '普通话' },
        { value: '粤语', label:'粤语'},
        { value: '英语', label:'英语'},
        { value: '客家话', label:'客家话'},
        { value: '潮汕话', label:'潮汕话'},
        { value: '五邑话', label:'五邑话'},
        { value: '高州话', label:'高州话'},
        { value: '湖南方言', label:'湖南方言'}
      ],
      languageListText: ['普通话', '粤语', '英语', '客家话', '潮汕话', '五邑话', '高州话', '湖南方言'],
      selectedLanguages: [], // 存储多选的语言
      avatarList: [], // 存储头像图片列表
      idCardFrontList: [], // 存储身份证正面图片列表
      idCardBackList: [], // 存储身份证背面图片列表

      // 申请状态相关
      applicationStatus: null, // 申请状态：null-未申请，1-已申请未支付，2-已申请已支付待审核，3-审核不通过
      employeeId: '', // 陪诊师ID，用于支付
      payBusinessId: '', // 支付业务ID，用于支付
      failReason: '', // 审核不通过原因
      savedFormData: {}, // 保存用户之前填写的信息，用于重新申请时回显
    }
  },
  onLoad(options) {
    // 这是uni-app的生命周期函数，确保在页面加载时初始化数据

    // 接收传递的参数
    if (options.employeeFeeButton) {
      this.employeeFeeButton = parseInt(options.employeeFeeButton);
    }

    if (options.employeeFee) {
      this.employeeFee = parseInt(options.employeeFee);
      // 根据报名费开关决定是否显示报名费
      this.showEmployeeFee = this.employeeFeeButton === 1 && this.employeeFee > 0;
    }

    if (options.employeeFeeContent) {
      this.employeeFeeContent = decodeURIComponent(options.employeeFeeContent);
    }

    this.initData();
  },
  computed: {
    ...mapState('user', { // 映射 isLogin 状态
      isLogin: state => state.isLogin
    }),
    getSelectCity() {
      if (this.form.province && this.form.city) {
        // 如果已经有选择的省份和城市，则显示它们
        const provinces = this.form.province.split(',');
        const cityGroups = this.form.city.split('$');

        // 将城市按省份分组
        const provinceMap = {};
        provinces.forEach((province, index) => {
          if (index < cityGroups.length) {
            const cities = cityGroups[index].split(',').filter(c => c);
            if (cities.length > 0) {
              provinceMap[province] = cities;
            }
          }
        });

        // 计算总城市数
        const totalCities = Object.values(provinceMap).reduce((total, cities) => total + cities.length, 0);

        // 如果城市数量超过3个，显示简略信息
        if (totalCities > 3) {
          return `已选择${totalCities}个城市`;
        }

        // 生成省份-城市的字符串数组
        const locationStrings = [];
        Object.entries(provinceMap).forEach(([province, provinceCities]) => {
          // 如果省份名称和城市名称相同（例如北京市），或者只有一个城市，则简化显示
          if (provinceCities.length === 1 && (province === provinceCities[0] || province.includes(provinceCities[0]) || provinceCities[0].includes(province))) {
            locationStrings.push(provinceCities[0]);
          } else {
            // 否则显示"省份: 城市1, 城市2"格式
            locationStrings.push(`${province}: ${provinceCities.join(', ')}`);
          }
        });

        return locationStrings.join('; ');
      }

      // 如果还没有选择
      return '请选择';
    },

    // 获取所有选择的城市，用于弹窗显示
    getAllSelectedCities() {
      if (!this.form.province || !this.form.city) {
        return [];
      }

      const provinces = this.form.province.split(',');
      const cityGroups = this.form.city.split('$');

      return provinces.map((province, index) => {
        const cities = index < cityGroups.length ?
                      cityGroups[index].split(',').filter(c => c) :
                      [];
        return {
          province,
          cities
        };
      }).filter(group => group.cities.length > 0);
    },

    // 获取已选择的语言显示文本
    getSelectLanguage() {
      return this.form.language || '请选择';
    },
  },
  mounted() {
    // mounted是Vue的生命周期钩子
    this.initData();
  },
  methods: {
    // 初始化数据
    async initData() {
      try {
        console.log('开始初始化数据');

        // 检查用户是否已经提交过申请
        await this.checkApplicationStatus();

        // 预处理城市数据
        await this.loadCityData();

        // 确保组件已初始化完成
        this.$nextTick(() => {
          console.log('组件和数据初始化完成', this.$refs);
        });
      } catch (error) {
        console.error('初始化数据失败', error);
      }
    },

    // 检查用户申请状态
    async checkApplicationStatus() {
      try {
        // 获取用户信息
        const userInfo = common.getKeyVal('user', 'codeUserInfo', true);
        if (!userInfo || !userInfo.id) {
          console.log('未获取到用户信息，无法检查申请状态');
          return;
        }

        // 调用API查询用户是否已提交申请
        const result = await this.$api.accompanyDoctor.getAccompanyemployeeStatus({
          userId: userInfo.id,
          providerId: serverOptions.providerId
        });
        console.log('返回result', result);
        if (result && result.code === 0 && result.data) {
          const employeeData = result.data;
          console.log('获取到陪诊师申请信息:', employeeData);

          // 保存陪诊师ID，用于后续支付
          this.employeeId = employeeData.id;

          // 保存支付业务ID，用于后续支付
          if (employeeData.payBusinessId) {
            this.payBusinessId = employeeData.payBusinessId;
          } else if (employeeData.payBusinessld) { // 兼容不同的字段名
            this.payBusinessId = employeeData.payBusinessld;
          }

          // 审核状态判断 - auditStatus为3表示审核不通过
          if (employeeData.auditStatus === 3) {
            // 审核不通过
            this.applicationStatus = 3; // 审核不通过
            // 保存审核不通过原因
            this.failReason = employeeData.failReason || '未提供审核失败原因';

            // 处理图片路径，添加前缀
            const processImagePath = (path) => {
              if (!path) return '';
              // 如果路径不包含http或https，则添加file_ctx前缀
              if (path && !path.startsWith('http') && !path.startsWith('https')) {
                return this.file_ctx + path;
              }
              return path;
            };

            // 保存用户之前填写的信息，用于重新申请时回显
            this.savedFormData = {
              name: employeeData.username || '',
              gender: employeeData.sex === 1 ? '男' : '女',
              age: employeeData.age ? employeeData.age.toString() : '',
              phone: employeeData.phone || '',
              idcard: employeeData.idcard || '',
              province: employeeData.province || '',
              city: employeeData.city || '',
              language: employeeData.language || '',
              school: employeeData.college || '',
              experience: employeeData.experience || '',
              // 处理图片路径
              avatar: processImagePath(employeeData.avatar),
              idCardFront: processImagePath(employeeData.frontUrl),
              idCardBack: processImagePath(employeeData.backUrl)
            };

            return;
          }

          // 判断支付状态和报名费开关
          if (this.employeeFeeButton === 1 && this.employeeFee > 0) {
            // 如果开启了报名费
            if (employeeData.pay === 0) {
              // 未支付，设置状态为已申请未支付
              this.applicationStatus = 1; // 已申请未支付
            } else {
              // 已支付，设置状态为待审核
              this.applicationStatus = 2; // 已申请已支付待审核
            }
          } else {
            // 未开启报名费，直接显示待审核状态
            this.applicationStatus = 2; // 已申请待审核
          }
        } else {
          // 未提交申请
          this.applicationStatus = null;
        }
      } catch (error) {
        console.error('检查申请状态失败:', error);
      }
    },

    // 格式化报名费（分转元）
    formatFee(fee) {
      return (fee / 100).toFixed(2);
    },

    // 加载城市数据
    async loadCityData() {
      try {
        // 从API获取城市数据，不再使用本地数据
        let provinceValue = (await this.$api.accompanyDoctor.accompanyproviderQueryOne({id:serverOptions.providerId})).data;

        // 获取城市列表
        this.provinceMap = await this.accompanyproviderQueryPage(provinceValue);
        console.log('城市数据加载完成', this.provinceMap);
      } catch(error) {
        console.error('加载城市数据失败', error);
        // 如果API失败，回退到使用本地数据
        this.provinceMap = JSON.parse(JSON.stringify(cityData));
      }
    },

    // 从reservation.vue复制的方法，用于获取城市数据
    async accompanyproviderQueryPage(provinceValue){
      // 判断当前是否是平台
      let queryOptions;
      if(serverOptions && serverOptions.source === 1){
        queryOptions = (await this.$api.accompanyDoctor.getAccompanyproviderAll()).data
      }else{
        queryOptions = [provinceValue]
      }
      let cityMap = this.getCityMap(queryOptions)
      return [...new Set(cityMap)].filter(e=>e);
    },

    // 从reservation.vue复制的方法，用于处理城市数据
    getCityMap(AccompanyproviderAll){
      return AccompanyproviderAll.reduce((acc, {province, city}) => {
        let provinceMap = province.split(',');
        let cityMap = city.split('$');
        provinceMap.map((provinceItem,index)=>{
          let currentCityMap = cityMap[index].split(',').filter(e=>e);
          let prov = acc.find(p => p.value === provinceItem);
          if (!prov) {
            acc.push(prov = {text: provinceItem, value: provinceItem, children: []});
          }
          prov.children.push(...currentCityMap)
        })
        return acc;
      }, []).map(e=>{
        e.children = [...new Set(e.children)];
        e.children = e.children.map(e=>({text:e,value:e}))
        return e;
      });
    },

    // 城市选择器点击行为
    onnodeclick() {
      // 处理节点点击事件
      console.log('节点点击');
    },
    // 处理登录跳转，保存当前页面信息
    gotoLogin() {
      // 跳转到登录页面，并传递当前页面标识参数
      this.$navto.push('Login', {formPage: 'Application'});
    },

    // 处理性别选择
    handleGenderChange(e) {
      if (!this.isLogin) { this.gotoLogin(); return; }
      const index = e.detail.value;
      this.form.gender = this.genderListText[index];
      console.log('选择性别:', this.form.gender);
    },

    // 处理城市选择
    handleCityChange(e) {
      if (!this.isLogin) { this.gotoLogin(); return; }
      const cityArray = e.detail.value;
      // 只保留省份和城市，不保留区县
      const provinceCity = cityArray.slice(0, 2);

      // 将省份和城市存储为以英文逗号分隔的字符串
      const [province, city] = provinceCity;

      // 检查是否已有此省份城市组合
      let provinces = this.form.province ? this.form.province.split(',') : [];
      let citiesByProvince = {};

      // 解析当前的城市数据
      if (this.form.city) {
        const cityGroups = this.form.city.split('$');
        provinces.forEach((prov, index) => {
          if (index < cityGroups.length) {
            citiesByProvince[prov] = cityGroups[index].split(',').filter(c => c);
          } else {
            citiesByProvince[prov] = [];
          }
        });
      }

      // 检查是否已选择该省份
      const provinceIndex = provinces.indexOf(province);

      if (provinceIndex === -1) {
        // 如果是新的省份，则添加省份和城市
        provinces.push(province);
        citiesByProvince[province] = [city];
      } else {
        // 如果已有该省份，检查是否已选择该城市
        const provinceCities = citiesByProvince[province] || [];
        const cityIndex = provinceCities.indexOf(city);

        if (cityIndex === -1) {
          // 如果未选择过该城市，则添加
          provinceCities.push(city);
        } else {
          // 如果已选择过该城市，则移除
          provinceCities.splice(cityIndex, 1);

          // 如果该省份下没有城市了，则移除该省份
          if (provinceCities.length === 0) {
            provinces.splice(provinceIndex, 1);
            delete citiesByProvince[province];
          } else {
            citiesByProvince[province] = provinceCities;
          }
        }
      }

      // 更新表单中的省份字段
      this.form.province = provinces.join(',');

      // 更新表单中的城市字段，按照省份顺序拼接，省份之间用$分隔
      const cityGroups = provinces.map(prov => (citiesByProvince[prov] || []).join(','));
      this.form.city = cityGroups.join('$');

      console.log('更新后的省份:', this.form.province);
      console.log('更新后的城市:', this.form.city);
    },

    // 处理语言选择
    handleLanguageChange(e) {
      if (!this.isLogin) { this.gotoLogin(); return; }
      const index = e.detail.value;
      const selectedLanguage = this.languageListText[index];

      // 检查是否已选择该语言
      const languageIndex = this.selectedLanguages.indexOf(selectedLanguage);

      if (languageIndex === -1) {
        // 如果未选择过该语言，则添加
        this.selectedLanguages.push(selectedLanguage);
      } else {
        // 如果已选择过该语言，则移除
        this.selectedLanguages.splice(languageIndex, 1);
      }

      // 更新表单语言字段，多个语言以英文逗号拼接
      this.form.language = this.selectedLanguages.join(',');
      console.log('选择语言:', this.form.language);
    },

    // 图片上传
    avatarReturnFn(imageList) {
      console.log('头像上传返回:', imageList);
      this.avatarList = imageList;

      if (imageList && imageList.length > 0) {
        // 更新表单数据
        this.form.avatar = imageList[0] || '';
      } else {
        this.form.avatar = '';
      }
    },

    // 表单验证
    validateForm() {
      const errors = []

      // 遍历验证规则
      Object.entries(VALID_RULES).forEach(([field, rule]) => {
        const value = this.form[field]

        // 特殊处理身份证照片字段
        if (field === 'idCardFront' || field === 'idCardBack') {
          console.log(`验证字段 ${field}:`, value);
          // 判断是否为对象或字符串，只要有任一格式存在即可
          if (rule.required && (!value || (typeof value === 'object' && !value.url && !value.filePath))) {
            console.log(`字段 ${field} 验证失败: 必填项为空或格式不正确`, value);
            errors.push(rule.message);
            return;
          }
        } else {
          // 其他字段的常规验证
          if (rule.required && !value) {
            console.log(`字段 ${field} 验证失败: 必填项为空`);
            errors.push(rule.message);
            return;
          }

          if (rule.validator && !rule.validator(value)) {
            console.log(`字段 ${field} 验证失败: 验证器未通过`, value);
            errors.push(rule.message);
          }
        }
      });

      // 单独处理经验描述字段的长度验证
      if (this.form.experience && this.form.experience.length > 200) {
        errors.push('经验描述不能超过200个字符');
      }

      return errors;
    },

    // 表单提交
    async handleSubmit() {
      if (!this.isLogin) { this.gotoLogin(); return; }
      // 先清除之前的错误信息
      this.validationError = '';

      // 先检查协议状态
      const isAgreed = await this.checkAgreement();
      if (!isAgreed) return;

      const errors = this.validateForm();
      if (errors.length > 0) {
        console.log('表单验证错误:', errors);
        this.validationError = errors[0];
        uni.showToast({
          title: this.validationError,
          icon: 'none',
          duration: 2000 // 显示时间更长，确保用户能看到
        });
        return;
      }

      try {
        uni.showLoading({ title: '提交中...', mask: true });

        // 获取用户信息
        const accountId = common.getKeyVal('user', 'accountId', true);
        const userInfo = common.getKeyVal('user', 'codeUserInfo', true);

        // 处理身份证照片路径
        let frontUrl = '';
        let backUrl = '';

        if (this.form.idCardFront) {
          frontUrl = typeof this.form.idCardFront === 'object' ?
            (this.form.idCardFront.filePath || this.form.idCardFront.url) :
            this.form.idCardFront;
        }

        if (this.form.idCardBack) {
          backUrl = typeof this.form.idCardBack === 'object' ?
            (this.form.idCardBack.filePath || this.form.idCardBack.url) :
            this.form.idCardBack;
        }

        // 处理图片路径，移除file_ctx前缀，只保留相对路径
        const removeFileCtxPrefix = (path) => {
          if (!path) return '';
          // 如果路径包含file_ctx前缀，则移除
          if (path && path.indexOf(this.file_ctx) === 0) {
            return path.substring(this.file_ctx.length);
          }
          return path;
        };

        // 构建提交数据
        const submitData = {
          // 必传字段
          username: this.form.name,
          phone: this.form.phone,
          idcard: this.form.idcard,
          province: this.form.province,
          city: this.form.city,
          county: '', // 不需要区县信息
          language: this.form.language,
          providerId: serverOptions.providerId, // 服务商ID
          college: this.form.school,
          certificate: null, // 默认医疗陪诊顾问
          auditStatus: 1,

          // 非必传字段
          accountId: accountId,
          userId: userInfo ? userInfo.id : null,
          avatar: removeFileCtxPrefix(typeof this.form.avatar === 'object' ?
            (this.form.avatar.filePath || this.form.avatar.url) :
            this.form.avatar),
          sex: this.form.gender === '男' ? 1 : 0,
          age: this.form.age ? parseInt(this.form.age) : null,
          experience: this.form.experience,

          // 证件照信息（修改字段名称以匹配后端API要求）
          frontUrl: removeFileCtxPrefix(frontUrl),
          backUrl: removeFileCtxPrefix(backUrl),

          // 添加报名费信息
          employeeFee: this.showEmployeeFee ? this.employeeFee : 0
        };

        let result;

        // 判断是否是从审核不通过状态重新申请
        if (this.employeeId) {
          // 如果有陪诊师ID，说明是重新申请，调用更新接口
          submitData.id = this.employeeId; // 添加陪诊师ID
          // console.log('调用更新接口重新申请:', submitData);
          result = await this.$api.accompanyDoctor.accompanyemployeeUpdate(submitData);
        } else {
          // 如果没有陪诊师ID，说明是首次申请，调用新增接口
          // console.log('调用新增接口首次申请:', submitData);
          result = await this.$api.accompanyDoctor.accompanyemployeeInsert(submitData);
        }

        uni.hideLoading();

        if (result && result.code === 0) {
          // 只有在首次申请时才从接口返回中获取陪诊师ID
          if (!this.employeeId && result.data) {
            this.employeeId = result.data;
          }
          // 注意：更新接口不会返回陪诊师ID，继续使用原有的ID

          // 根据是否需要支付报名费走不同的流程
          if (this.showEmployeeFee && this.employeeFee > 0) {
            // 检查是否有可用的支付ID（优先使用业务ID，其次使用陪诊师ID）
            if (this.payBusinessId || this.employeeId) {
              // 显示支付提示
              uni.showToast({
                title: '申请提交成功，正在发起支付',
                icon: 'none',
                duration: 1500
              });

              // 调用支付方法
              setTimeout(() => {
                this.handlePayFee();
              }, 1500);
            } else {
              uni.showToast({
                title: '无法获取支付信息',
                icon: 'none'
              });
            }
          } else {
            // 无需支付，直接显示成功
            this.showSuccessDialog = true;
          }
        } else {
          uni.showToast({
            title: result.msg || '提交失败，请稍后再试',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: error.msg || '提交失败，请检查网络',
          icon: 'none'
        });
        console.error('Submit failed:', error);
      }
    },

    // 成功提示
    showSuccessModal() {
      this.showSuccessDialog = true;
    },

    // 返回首页
    backToHome() {
      this.showSuccessDialog = false;
      this.$navto.replaceAll('AccompanyHome')
    },

    idCardFrontReturnFn(imageList) {
      console.log('身份证人像面上传返回:', imageList);
      this.idCardFrontList = imageList;

      if (imageList && imageList.length > 0) {
        // 更新表单数据
        this.form.idCardFront = imageList[0] || '';
        console.log('更新后的身份证人像面:', this.form.idCardFront);
      } else {
        this.form.idCardFront = '';
      }
    },

    idCardBackReturnFn(imageList) {
      console.log('身份证国徽面上传返回:', imageList);
      this.idCardBackList = imageList;

      if (imageList && imageList.length > 0) {
        // 更新表单数据
        this.form.idCardBack = imageList[0] || '';
        console.log('更新后的身份证国徽面:', this.form.idCardBack);
      } else {
        this.form.idCardBack = '';
      }
    },

    // 切换协议选中状态
    toggleAgreement() {
      this.isAgreed = !this.isAgreed
    },

    // 协议检查方法
    checkAgreement() {
      return new Promise((resolve) => {
        if (this.isAgreed) {
          resolve(true);
          return;
        }
        uni.showModal({
          title: '陪诊师入驻协议政策及免责条款',
          content: '我已阅读并同意《陪诊师入驻协议》',
          confirmColor: '#00B578',
          cancelColor: '#666',
          success: (res) => {
            if (res.confirm) {
              this.isAgreed = true;
              resolve(true);
            } else {
              uni.showToast({ title: '请先同意协议', icon: 'none' });
              resolve(false);
            }
          }
        });
      });
    },

    // 页面跳转
    navtoGo(url = '', obj = {}) {
      this.$navto.push(url, obj)
    },

    // 处理经验描述输入，包括粘贴超长文本的情况
    handleExperienceInput(e) {
      if (!this.isLogin) { this.gotoLogin(); return; }
      const newValue = e.detail.value;
      this.form.experience = newValue;

      // 如果超过字符限制，显示提示
      if (newValue.length > 200) {
        uni.showToast({
          title: `已超过200个字符限制，提交时将无法通过验证`,
          icon: 'none',
          duration: 2000
        });
      }
    },

    // 显示城市列表弹窗
    showCityList() {
      if (this.form.city && this.form.city.split(',').length > 0) {
        this.showCityDialog = true;
      }
    },

    // 关闭城市列表弹窗
    closeCityList() {
      this.showCityDialog = false;
    },

    // 删除已选择的城市
    removeCity(province, city) {
      const provinces = this.form.province ? this.form.province.split(',') : [];
      const cityGroups = this.form.city ? this.form.city.split('$') : [];

      // 找到要删除的省份索引
      const provinceIndex = provinces.indexOf(province);

      if (provinceIndex !== -1 && provinceIndex < cityGroups.length) {
        // 获取该省份的城市列表
        const provinceCities = cityGroups[provinceIndex].split(',').filter(c => c);

        // 找到要删除的城市索引
        const cityIndex = provinceCities.indexOf(city);

        if (cityIndex !== -1) {
          // 删除城市
          provinceCities.splice(cityIndex, 1);

          if (provinceCities.length === 0) {
            // 如果该省份下没有城市了，则移除该省份
            provinces.splice(provinceIndex, 1);
            cityGroups.splice(provinceIndex, 1);
          } else {
            // 更新该省份的城市列表
            cityGroups[provinceIndex] = provinceCities.join(',');
          }

          // 更新表单数据
          this.form.province = provinces.join(',');
          this.form.city = cityGroups.join('$');
        }
      }
    },

    // 新增的事件处理函数，包含登录检查
    handleUploadAvatar(){
      if (!this.isLogin) { this.gotoLogin(); return; }
      this.$refs.avatarUploader.uploadImage();
    },
    handleNameFocus(){
      if (!this.isLogin) { this.gotoLogin(); return; }
    },
    handleGenderClick(){
      if (!this.isLogin) { this.gotoLogin(); return; }
    },
    handleCityPickerClick(){
       if (!this.isLogin) { this.gotoLogin(); return; }
       this.showCityDialog = true;
    },
    handleShowCityList(event) {
      if (!this.isLogin) { this.gotoLogin(); return; }
      event.stopPropagation(); // 阻止冒泡到 picker
      this.showCityList();
    },
    handleLanguageClick(){
      if (!this.isLogin) { this.gotoLogin(); return; }
      // 触发 picker
    },
    handleAgeFocus(){
      if (!this.isLogin) { this.gotoLogin(); return; }
    },
    handlePhoneFocus(){
      if (!this.isLogin) { this.gotoLogin(); return; }
    },
    handleIdCardFocus(){
      if (!this.isLogin) { this.gotoLogin(); return; }
    },
    handleSchoolFocus(){
      if (!this.isLogin) { this.gotoLogin(); return; }
    },
    handlePreviewIdCardFront(){
      if (!this.isLogin) { this.gotoLogin(); return; }
      this.$refs.idCardFrontUploader.previewImage(0);
    },
    handleDeleteIdCardFront(){
      if (!this.isLogin) { this.gotoLogin(); return; }
      this.$refs.idCardFrontUploader.del(0);
    },
    handleUploadIdCardFront(){
      if (!this.isLogin) { this.gotoLogin(); return; }
      this.$refs.idCardFrontUploader.uploadImage();
    },
    handlePreviewIdCardBack(){
      if (!this.isLogin) { this.gotoLogin(); return; }
      this.$refs.idCardBackUploader.previewImage(0);
    },
    handleDeleteIdCardBack(){
      if (!this.isLogin) { this.gotoLogin(); return; }
      this.$refs.idCardBackUploader.del(0);
    },
    handleUploadIdCardBack(){
      if (!this.isLogin) { this.gotoLogin(); return; }
      this.$refs.idCardBackUploader.uploadImage();
    },
    handleExperienceFocus(){
      if (!this.isLogin) { this.gotoLogin(); return; }
    },
    handleToggleAgreement(){
      if (!this.isLogin) { this.gotoLogin(); return; }
      this.toggleAgreement();
    },
    handleNavToAgreement(){
      if (!this.isLogin) { this.gotoLogin(); return; }
      this.navtoGo('ArrivalAgreement');
    },
    checkLoginBeforeShow() {
      if (!this.isLogin) {
        this.gotoLogin();
      }
    },

    // 处理支付报名费
    async handlePayFee() {
      if (!this.isLogin) { this.gotoLogin(); return; }

      // 先检查申请状态，确保获取最新的payBusinessId
      await this.checkApplicationStatus();

      // 判断使用哪种ID进行支付
      let payId;
      let isPayBusinessId = false;

      if (this.payBusinessId) {
        // 优先使用支付业务ID
        payId = this.payBusinessId;
        isPayBusinessId = true;
      } else if (this.employeeId) {
        // 如果没有支付业务ID，则使用陪诊师ID
        payId = this.employeeId;
        isPayBusinessId = false;
      }

      if (payId) {
        this.$ext.user.accompanyEmployeeFeePay(
          payId,
          () => {
            uni.showToast({
              title: '支付成功，请等待审核',
              icon: 'none'
            });
            this.applicationStatus = 2; // 已申请已支付待审核

            // 支付成功后，滚动到页面顶部
            setTimeout(() => {
              uni.pageScrollTo({
                scrollTop: 0,
                duration: 300
              });
            }, 500);
          },
          (error) => {
            console.error('支付失败:', error);
            this.showPaymentFailedDialog = true;
          },
          isPayBusinessId, // 传递ID类型标志
          this.employeeId  // 传递陪诊师ID作为备用，用于在历史订单过期时创建新订单
        );
      } else {
        uni.showToast({
          title: '无法获取支付信息',
          icon: 'none'
        });
      }
    },

    // 关闭支付失败弹窗
    closePaymentFailedDialog() {
      this.showPaymentFailedDialog = false;
      // 可以选择是否返回首页
      // this.backToHome();
    },

    // 重试支付
    async retryPayment() {
      // 先检查申请状态，确保获取最新的payBusinessId
      await this.checkApplicationStatus();

      if (this.payBusinessId || this.employeeId) {
        this.showPaymentFailedDialog = false;
        this.handlePayFee();
      } else {
        uni.showToast({
          title: '无法获取订单信息，请返回首页重试',
          icon: 'none'
        });
      }
    },

    // 重新申请
    handleReapply() {
      // 重置申请状态，显示表单让用户重新填写
      this.applicationStatus = null;

      // 如果之前有保存的数据，则回显
      if (Object.keys(this.savedFormData).length > 0) {
        // 处理表单数据回显
        this.form = { ...this.form, ...this.savedFormData };

        // 处理语言回显
        if (this.savedFormData.language) {
          this.selectedLanguages = this.savedFormData.language.split(',').filter(lang => lang);
        }

        // 处理城市数据回显
        if (this.savedFormData.province && this.savedFormData.city) {
          // 城市数据已经是正确格式，不需要额外处理
          console.log('回显城市数据:', this.savedFormData.province, this.savedFormData.city);
        }

        // 处理图片回显
        if (this.savedFormData.avatar) {
          // 回显头像
          this.avatarList = [{
            url: this.savedFormData.avatar,
            filePath: this.savedFormData.avatar
          }];
        }

        if (this.savedFormData.idCardFront) {
          // 回显身份证正面
          this.idCardFrontList = [{
            url: this.savedFormData.idCardFront,
            filePath: this.savedFormData.idCardFront
          }];
        }

        if (this.savedFormData.idCardBack) {
          // 回显身份证背面
          this.idCardBackList = [{
            url: this.savedFormData.idCardBack,
            filePath: this.savedFormData.idCardBack
          }];
        }

        // 清空保存的数据
        this.savedFormData = {};
      }
      // 重新检查是否需要显示报名费
      this.showEmployeeFee = this.employeeFeeButton === 1 && this.employeeFee > 0;
      // console.log('重新申请，报名费开关状态:', this.employeeFeeButton, '报名费金额:', this.employeeFee, '是否显示报名费:', this.showEmployeeFee);
      // 滚动到页面顶部
      setTimeout(() => {
        uni.pageScrollTo({
          scrollTop: 0,
          duration: 300
        });
      }, 100);
    }
  }
}
</script>

<style lang="scss">
.container {
  background: #F4F6FA;
  min-height: 100vh;
  padding-bottom: 200rpx;
  height: calc(100vh - 1px); /* 使用calc(100vh - 1px)解决安卓兼容问题 */
}

.application-status {
  width: 686rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
  margin: 20rpx 32rpx;
  padding: 30rpx 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;

  &-content {
    width: 100%;
    margin-bottom: 30rpx;
    text-align: center;
  }

  &-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #1D2029;
    margin-bottom: 16rpx;
  }

  &-desc {
    font-size: 28rpx;
    color: #4E5569;
  }

  &-btn {
    width: 400rpx;
    height: 88rpx;
    background: #00B484;
    border-radius: 44rpx;
    text-align: center;
    line-height: 88rpx;
    font-size: 32rpx;
    color: #FFFFFF;
    font-weight: 500;
  }

  &-back-btn {
    width: 400rpx;
    height: 88rpx;
    background: #F4F6FA;
    border-radius: 44rpx;
    text-align: center;
    line-height: 88rpx;
    font-size: 32rpx;
    color: #4E5569;
    font-weight: 500;
    margin-top: 20rpx;
    border: 1rpx solid #EAEBF0;
  }
}

.boxTab {
  width: 100vw;
  overflow: scroll;
  box-sizing: border-box;
  padding: 0 32rpx;
}

.botTab {
  box-sizing: border-box;
  padding: 0 32rpx 32rpx 32rpx;
  width: 686rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
  margin: 20rpx 0 200rpx 0;

  .lineHide {
    width: 0;
    overflow: hidden;
    height: 0;
  }

  .additionalContent {
    margin-top: 32rpx;

    .additionalContentImageUpMap {
      display: flex;
      flex-wrap: wrap;
    }

    .additionalContentImageUp {
      margin-top: 12rpx;
      width: 194rpx;
      height: 194rpx;
      background: #F4F6FA;
      border-radius: 16rpx;
      text-align: center;
      position: relative;
      margin-right: calc((100% - (194rpx * 3)) / 2);
      &:nth-of-type(3n) {
        margin-right: 0;
      }

      .imageUpImage {
        width: 64rpx;
        height: 64rpx;
        margin-top: 46rpx;
      }

      .imageUpText {
        font-weight: 400;
        font-size: 24rpx;
        color: #4E5569;
      }

      .imageContent {
        width: 100%;
        height: 100%;
        border-radius: 16rpx;
      }

      .iconPostMenuClose {
        width: 30rpx;
        height: 30rpx;
        position: absolute;
        top: 0;
        right: 0;
        background: white;
        z-index: 9;
      }
    }
  }

  .lineTitle {
    font-weight: 500;
    font-size: 28rpx;
    color: #1D2029;
    width: 212rpx;

    .required {
      font-weight: 500;
      font-size: 14px;
      color: #FF5500;
    }
  }

  .botTabLine {
    width: 100%;
    height: 104rpx;
    line-height: 104rpx;
    border-bottom: 2rpx solid #EAEBF0;
    display: flex;
    align-items: center;

    &.avatar-line {
      height: 150rpx; /* 形象照行的高度设置为150rpx */
      line-height: 150rpx;
    }

    .select-wrapper {
      flex: 1;
      display: flex;
      align-items: center;
    }

    .picker-inner {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      text {
        flex: 1;
        font-size: 28rpx;
        color: #1D2029;
      }
    }

    .lineValue {
      flex: 1;
    }

    .lineIcon {
      width: 32rpx;
      height: 32rpx;
    }

    .view-all {
      font-size: 24rpx;
      color: #00B484;
      margin-left: 10rpx;
    }

    .bookInput {
      width: 438rpx;
      height: 40rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #1D2029;
    }
  }

  .experience-input {
    width: 100%;
    height: 240rpx;
    padding: 20rpx;
    background: #F4F6FA;
    border-radius: 8rpx;
    font-size: 28rpx;
    box-sizing: border-box;
  }

  .word-counter {
    font-size: 24rpx;
    color: #999;
    text-align: right;
    display: block;
    margin-top: 16rpx;
  }

  .word-counter-exceed {
    color: #FF5500;
    font-weight: bold;
  }

  .upload-box {
    width: 140rpx;
    height: 140rpx;
    background: #f5f5f5;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: auto;

    .preview-image {
      width: 100%;
      height: 100%;
      border-radius: 8rpx;
    }

    .empty-upload {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f4f6fa;
      border-radius: 8rpx;
    }

    .plus-icon {
      position: relative;
      width: 40rpx;
      height: 40rpx;

      &:before, &:after {
        content: '';
        position: absolute;
        background-color: #aaa;
      }

      &:before {
        width: 40rpx;
        height: 4rpx;
        top: 18rpx;
        left: 0;
      }

      &:after {
        width: 4rpx;
        height: 40rpx;
        left: 18rpx;
        top: 0;
      }
    }
  }

  .preview-container {
    position: relative;
    width: 100%;
    height: 100%;
  }
}

.bottomBtn {
  width: 750rpx;
  background: #FFFFFF;
  box-shadow: 0rpx -1rpx 0rpx 0rpx #EAEBF0;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 999;

  .comBtn {
    width: 686rpx;
    height: 88rpx;
    background: #00B484;
    border-radius: 44rpx;
    text-align: center;
    line-height: 88rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #FFFFFF;
    margin: 24rpx auto;
  }
}

.additionalContent {
  margin-top: 32rpx;

  .additionalContentImageUpMap {
    display: flex;
    flex-wrap: wrap;
  }

  .additionalContentImageUp {
    margin-top: 12rpx;
    width: 194rpx;
    height: 194rpx;
    background: #F4F6FA;
    border-radius: 16rpx;
    text-align: center;
    position: relative;
    margin-right: calc((100% - (194rpx * 3)) / 2);
    &:nth-of-type(3n) {
      margin-right: 0;
    }

    .imageUpImage {
      width: 64rpx;
      height: 64rpx;
      margin-top: 46rpx;
    }

    .imageUpText {
      font-weight: 400;
      font-size: 24rpx;
      color: #4E5569;
    }

    .imageContent {
      width: 100%;
      height: 100%;
      border-radius: 16rpx;
    }

    .iconPostMenuClose {
      width: 30rpx;
      height: 30rpx;
      position: absolute;
      top: 0;
      right: 0;
      background: white;
      z-index: 9;
    }
  }

  // 新增身份证上传区域样式
  .idcard-upload-container {
    display: flex;
    justify-content: space-between;
    margin-top: 20rpx;
    width: 100%;
  }

  .idcard-item {
    width: 48%;
    height: 200rpx;
    background: #F4F6FA;
    border-radius: 16rpx;
    position: relative;
    overflow: hidden;
  }

  .empty-idcard {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .idcard-icon {
    width: 100rpx;
    height: 100rpx;
    margin-bottom: 10rpx;
  }

  .idcard-text {
    font-size: 28rpx;
    color: #666;
  }

  .idcard-image {
    width: 100%;
    height: 100%;
    border-radius: 16rpx;
  }

  .idcard-delete {
    width: 30rpx;
    height: 30rpx;
    position: absolute;
    top: 10rpx;
    right: 10rpx;
    background: white;
    border-radius: 50%;
    z-index: 9;
  }
}

.error-message {
  width: 100%;
  height: 80rpx;
  background: #FFEBEB;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;

  .error-icon {
    width: 36rpx;
    height: 36rpx;
    margin-right: 16rpx;
    background: #FF5500;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 24rpx;
  }

  text {
    font-size: 28rpx;
    color: #FF5500;
  }
}

.agreement-row {
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 10px 0;
}

.agreement-text {
  font-size: 14px;
  color: #666;
}

.agreement-link {
  font-size: 14px;
  color: #1687F7;
}

.custom-checkbox {
  display: inline-block;
  vertical-align: middle;
  margin-right: 8px;
  margin-left: 32rpx;
}

.checkbox-icon {
  width: 28rpx;
  height: 28rpx;
  border: 2rpx solid #ccc;
  border-radius: 50%;
  position: relative;
  transition: all 0.3s;
}

.checkbox-icon.checked {
  background: #00B578;
  border-color: #00B578;
}

.checkbox-icon.checked::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  width: 16rpx;
  height: 8rpx;
  border: 4rpx solid #fff;
  border-top: none;
  border-right: none;
  transform: translate(-50%, -60%) rotate(-45deg);
}

// 成功弹窗样式
.success-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;

  &-mask {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.6);
  }

  &-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 560rpx;
    background-color: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  &-title {
    width: 100%;
    font-size: 32rpx;
    font-weight: 500;
    color: #1D2029;
    text-align: center;
    padding: 40rpx 0 20rpx;
    border-bottom: 2rpx solid #EAEBF0;
  }

  &-body {
    padding: 60rpx 30rpx;
    font-size: 28rpx;
    color: #4E5569;
    text-align: center;
    width: 100%;
  }

  &-btn {
    width: 100%;
    height: 100rpx;
    line-height: 100rpx;
    text-align: center;
    color: #00B484;
    font-size: 32rpx;
    font-weight: 500;
    border-top: 2rpx solid #EAEBF0;
  }

  &-btn-group {
    width: 100%;
    height: 100rpx;
    display: flex;
    border-top: 2rpx solid #EAEBF0;
  }

  &-btn-half {
    width: 50%;
    height: 100rpx;
    line-height: 100rpx;
    text-align: center;
    font-size: 32rpx;
    font-weight: 500;
    color: #4E5569;

    &.border-right {
      border-right: 1rpx solid #EAEBF0;
    }

    &.highlight {
      color: #00B484;
    }
  }
}

// 城市列表弹窗样式
.city-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;

  &-mask {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.6);
  }

  &-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 560rpx;
    max-height: 80vh;
    background-color: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  &-title {
    width: 100%;
    font-size: 32rpx;
    font-weight: 500;
    color: #1D2029;
    text-align: center;
    padding: 40rpx 0 20rpx;
    border-bottom: 2rpx solid #EAEBF0;
    position: relative;
  }

  &-body {
    padding: 30rpx;
    font-size: 28rpx;
    color: #4E5569;
    width: 100%;
    box-sizing: border-box;
    max-height: 60vh;
    overflow-y: auto;
  }

  &-close {
    width: 40rpx;
    height: 40rpx;
    position: absolute;
    top: 40rpx;
    right: 30rpx;
    z-index: 9;
  }

  &-btn {
    width: 100%;
    height: 100rpx;
    line-height: 100rpx;
    text-align: center;
    color: #00B484;
    font-size: 32rpx;
    font-weight: 500;
    border-top: 2rpx solid #EAEBF0;
  }

  .city-group {
    margin-bottom: 30rpx;
    text-align: left;
  }

  .city-province {
    font-weight: 500;
    font-size: 30rpx;
    color: #1D2029;
    margin-bottom: 20rpx;
  }

  .city-list {
    display: flex;
    flex-wrap: wrap;
  }

  .city-item {
    background: #F4F6FA;
    border-radius: 8rpx;
    padding: 10rpx 20rpx;
    margin-right: 20rpx;
    margin-bottom: 20rpx;
    display: flex;
    align-items: center;
    position: relative;
  }

  .city-delete {
    margin-left: 10rpx;
    width: 30rpx;
    height: 30rpx;
    line-height: 26rpx;
    text-align: center;
    border-radius: 50%;
    font-size: 28rpx;
    color: #999;
  }

  .no-cities {
    text-align: center;
    color: #999;
    padding: 40rpx 0;
  }
}
</style>
