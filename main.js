import Vue from 'vue'
import App from './App'
import '@/router/config'
import { RouterMount } from 'uni-simple-router'
import router from './router/index'
import store from '@/store'
import util from '@/common/util'
import Mixin from './mixins';
import page from '@/components/basics/frame/page'
import defaultImg from '@/components/basics/default-avatar/index'
import zPaging from '@/components/uni/zPaging/index'
import StatusBarHeight from '@/components/business/module/status-bar-height/index'
import LoadingLayer from '@/components/business/module/loading-layer/index'
import IntegrationTimeEr from '@/components/business/activity/integrationTimeEr'
import env from '@/config/env'
import common from '@/common/util/main'
import uniPlugin from '@/common/util/uni-plugin'
import VueClipboard from 'vue-clipboard2'
import saHoverMenuPlug from '@/components/basics/sa-hover-menu/index'
import saHoverMenu from '@/components/basics/sa-hover-menu/sa-hover-menu'
import mNavBar from '@/components/basics/m-nav-bar/index.vue'

Vue.component('m-nav-bar', mNavBar)
Vue.config.productionTip = false
Vue.use(saHoverMenuPlug)

Vue.component('sa-hover-menu', saHoverMenu)
Vue.component('page', page)
Vue.component('default-img', defaultImg)
// 若使用新分页组件把其注释打开
Vue.component('scroll-refresh', zPaging)
Vue.component('status-bar-height', StatusBarHeight)
Vue.component('loading-layer', LoadingLayer)
Vue.component('integration-time-er', IntegrationTimeEr)
Vue.config.productionTip = false
Vue.mixin(Mixin);
Vue.use(VueClipboard)

Vue.use(router)
// 测试代码
let fn = console.log;
console.myLog = fn;

Vue.config.errorHandler = function(err, vm, info) {

}


/**
 * 初始化H5配置
 */
// #ifdef H5
import '@/config/initialize/h5'
// #endif

/**
 * 初始化微信小程序
 */
  // #ifdef MP-WEIXIN
  import '@/config/initialize/mini-program'
  if (env.environmental === 'pro') {
    import ('./utils/ald-stat')
  }

  // 初始化网络状态
  uniPlugin.getNetworkType((res) => {
    if (res === 'none') {
      common.setKeyVal('system', 'networkStatus', false, true)
    }
  })

  // 监听网络变化
  uni.onNetworkStatusChange(function(res) {
    if (!res.isConnected) {
      uniPlugin.toast('您的网络好像掉线了，请稍后再试')
      common.setKeyVal('system', 'networkStatus', false, true)
      return
    } else {
      common.setKeyVal('system', 'networkStatus', true, true)
      return
    }
  })
  // #endif
  /**
   * 全局工具注册
   */
  Object.keys(util).forEach(key => {
    Vue.prototype[key] = util[key]
  })

  /**
 * 初始化获取导航高度值
 */
  Vue.prototype.$common.getHearderBar(Vue)
  App.mpType = 'app'
  const app = new Vue({
    store,
    ...App,
  })
  //v1.3.5起 H5端 你应该去除原有的app.$mount();使用路由自带的渲染方式
  // #ifdef H5
  RouterMount(app, router, '#app');
  // #endif

  // #ifndef H5
  app.$mount(); //为了兼容小程序及app端必须这样写才有效果
  // #endif
