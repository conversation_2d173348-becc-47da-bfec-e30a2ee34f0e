import request from '@/common/util/request'
import env from '@/config/env'
import serverOptions from '@/config/env/options'
// let ctx2 = env.ctx + 'dm2/'
let ctx2 = env.ctx + 'dm/'
import common from '@/common/util/main'

// 捕获request传递给serverOptions
serverOptions.catchRequest(request)
/**
 * 社区请求工具类，
 * 注意：API接口只做请求数据和格式化数据的事情，其他事务一概不处理
 */
export default {
  // 获取所有服务
    getAccompanyservicePage (param) {
        const url = ctx2 + 'api/v1/accompanyservice/query/page'
        return serverOptions.accompanyDoctorPost(url, param)
    },
    // 返回所有服务商
    getAccompanyproviderAll (param) {
        const url = ctx2 + 'api/v1/accompanyprovider/query/all'
        return serverOptions.accompanyDoctorGet(url, param)
    },
    // 根据id查询订单
    getAccompanybookOne (param) {
        const url = ctx2 + 'api/v1/accompanybook/query/one'
        return serverOptions.accompanyDoctorGet(url, param)
    },
    // 查询用户购买套餐信息
    getAccompanycombouserQueryUserCombo (param) {
        const url = ctx2 + 'api/v1/accompanycombouser/query/userCombo'
        return serverOptions.accompanyDoctorGet(url, param)
    },
    // 获取陪诊师信息
    getAccompanyemployeeOneByUserId (param) {
        const url = ctx2 + 'api/v1/accompanyemployee/query/oneByUserId'
        return serverOptions.accompanyDoctorGet(url, param)
    },
        // 获取陪诊师状态
    getAccompanyemployeeStatus (param) {
        const url = ctx2 + 'api/v1/accompanyemployee/query/oneByProvider'
        return serverOptions.accompanyDoctorGet(url, param)
    },
    // 获取陪诊师信息
    getAccompanyemployeeQueryOne (param) {
        const url = ctx2 + 'api/v1/accompanyemployee/query/one'
        return serverOptions.accompanyDoctorGet(url, param)
    },
    // 拒绝订单
    accompanybookReject (param) {
        const url = ctx2 + 'api/v1/accompanybook/reject'
        return serverOptions.accompanyDoctorGet(url, param)
    },
    // 抢单
    accompanybookCompete (param) {
        const url = ctx2 + 'api/v1/accompanybook/compete'
        return serverOptions.accompanyDoctorGet(url, param)
    },
    // 接单
    accompanybookAccept (param) {
        const url = ctx2 + 'api/v1/accompanybook/accept'
        return serverOptions.accompanyDoctorGet(url, param)
    },
    // 返回所有医院
    hospitalserviceevaluatequerypage (param) {
        const url = ctx2 + 'api/v1/hospitalserviceevaluatelog/query/page'
        return serverOptions.accompanyDoctorPost(url, param)
    },
    // 小程序-立即预约
    accompanybook (param) {
        const url = ctx2 + 'api/v1/accompanybook/book'
        let cloudProviderId = common.getKeyVal('system','cloudProviderId', true);
        // store：1-云门店单 0-不是云门店单
        param.store = cloudProviderId ? 1 : 0;
        return serverOptions.accompanyDoctorPost(url, param)
    },
    // 根据id查询陪诊患者档案
    accompanypatientQueryOne (param) {
        const url = ctx2 + 'api/v1/accompanypatient/query/one'
        return serverOptions.accompanyDoctorGet(url, param)
    },
    // 查询当前用户档案
    accompanypatientQueryAll (param) {
        const url = ctx2 + 'api/v1/accompanypatient/query/all'
        return serverOptions.accompanyDoctorGet(url, param)
    },
    // 新增患者档案
    accompanypatientInsert (param) {
        const url = ctx2 + 'api/v1/accompanypatient/insert'
        return serverOptions.accompanyDoctorPost(url, param)
    },
    // 更新患者档案
    accompanypatientUpdate (param) {
        const url = ctx2 + 'api/v1/accompanypatient/update'
        return serverOptions.accompanyDoctorPutJson(url, param)
    },
    // 删除患者档案
    accompanypatientDeleteOne (param) {
        const url = ctx2 + 'api/v1/accompanypatient/delete/one/' + param.id
        return serverOptions.accompanyDoctorDelete(url)
    },
    // 陪诊患者档案分页
    accompanypatientQueryPage (param) {
        const url = ctx2 + 'api/v1/accompanypatient/query/page'
        return serverOptions.accompanyDoctorPost(url, param)
    },
    // 根据id查询陪诊患者档案
    accompanypatientQueryOne (param) {
      const url = env.ctx + 'dm/api/v1/accompanypatient/query/one'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 查询当前用户档案
    accompanypatientQueryAll (param) {
      const url = env.ctx + 'dm/api/v1/accompanypatient/query/all'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 新增患者档案
    accompanypatientInsert (param) {
      const url = env.ctx + 'dm/api/v1/accompanypatient/insert'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 更新患者档案
    accompanypatientUpdate (param) {
      const url = env.ctx + 'dm/api/v1/accompanypatient/update'
      return serverOptions.accompanyDoctorPutJson(url, param)
    },
    // 删除患者档案
    accompanypatientDeleteOne (param) {
      const url = env.ctx + 'dm/api/v1/accompanypatient/delete/one/' + param.id
      return serverOptions.accompanyDoctorDelete(url)
    },
    // 陪诊患者档案分页
    accompanypatientQueryPage (param) {
      const url = env.ctx + 'dm/api/v1/accompanypatient/query/page'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 陪诊记录分页
    accompanybookQueryRecordPage (param) {
      const url = env.ctx + 'dm/api/v1/accompanybook/query/recordPage'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 陪诊订单分页
    accompanybookQuery (param) {
        const url = ctx2 + 'api/v1/accompanybook/query/page'
        return serverOptions.accompanyDoctorPost(url, param)
    },
    // 陪诊记录分页
    accompanybookQueryRecordPage (param) {
        const url = ctx2 + 'api/v1/accompanybook/query/recordPage'
        return serverOptions.accompanyDoctorPost(url, param)
    },
    // 取消订单
    accompanybookCancel (param) {
        const url = ctx2 + 'api/v1/accompanybook/cancel'
        return serverOptions.accompanyDoctorPost(url, param)
    },
    // 完成类型订单退款
    accompanybookFinishOrderRefund (param) {
      const url = ctx2 + 'api/v1/accompanybook/finishOrderRefund'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 套餐支付
    accompanybookPay (param) {
        const url = ctx2 + 'api/v1/accompanybook/pay'
        return serverOptions.accompanyDoctorPost(url, param)
    },
    // 发起提现
    accompanypayoutInsert (param) {
        const url = ctx2 + 'api/v1/accompanypayout/insert'
        return serverOptions.accompanyDoctorPost(url, param)
    },
    // 陪诊订单分页-陪诊师
    accompanybookEmployeePage (param) {
        const url = ctx2 + 'api/v1/accompanybook/query/employeePage'
        return serverOptions.accompanyDoctorPost(url, param)
    },
    // 获取订单二维码
    accompanybookOrderCode (param) {
        const url = ctx2 + 'api/v1/accompanybook/orderCode'
        return serverOptions.accompanyDoctorPost(url, param)
    },
    // 套餐分页列表
    accompanycomboQueryPage(param){
      const url = ctx2 + 'api/v1/accompanycombo/query/page'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 陪诊-用户购买套餐
    accompanycombouserInsert(param){
      const url = ctx2 + 'api/v1/accompanycombouser/insert'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 陪诊-根据id查询服务
    accompanyserviceQueryOne(param){
      const url = ctx2 + 'api/v1/accompanyservice/query/one'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 陪诊-根据id查询服务
    accompanybookFinish(param){
      const url = ctx2 + 'api/v1/accompanybook/finish'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 陪诊-我的套餐订单列表分页
    accompanycombouserQueryPage(param){
      const url = ctx2 + 'api/v1/accompanycombouser/query/page'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 陪诊-根据id查询套餐
    accompanycomboQueryOne(param){
      const url = ctx2 + 'api/v1/accompanycombo/query/one'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 陪诊-根据id查询套餐订单
    accompanycombouserQueryOne(param){
      const url = ctx2 + 'api/v1/accompanycombouser/query/one'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 陪诊-取消订单
    accompanycombouserCancel(param){
      const url = ctx2 + 'api/v1/accompanycombouser/cancel'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 陪诊-支付前判断的接口
    allinpaydetailGetOrderDetail(param){
      const url = ctx2 + 'manage/api/v1/allinpaydetail/accompanyDoctorGet/order/detail'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 陪诊-拉卡拉支付前判断的接口
    lklaccompanyQueryOrder(param){
      const url = ctx2 + 'api/v1/lklaccompany/queryOrderV2'
      // const url = ctx2 + 'api/v1/lklaccompany/queryOrder'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 陪诊——测试支付
    accompanybookPayTest(param){
      const url = ctx2 + 'api/v1/accompanybook/virtualPay'
      !param.productCode && (param.productCode = '')
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 陪诊-更新通用打卡签到记录数据
    signinlogUpdate(param){
      const url = ctx2 + 'api/v1/signinlog/update'
      return serverOptions.accompanyDoctorPutJson(url, param)
    },
    // 陪诊-评价订单
    accompanybookComment(param){
      const url = ctx2 + 'api/v1/accompanybook/comment'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 陪诊-服务商列表
    accompanyproviderQueryPage(param){
      const url = ctx2 + 'api/v1/accompanyprovider/query/page'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 陪诊-获取陪诊师打卡记录
    signinlogGetLogList(param){
      const url = ctx2 + 'api/v1/signinlog/get/logList'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 陪诊-判断用户是否是陪诊师傅和服务商
    accompanybookUserRole(param){
      const url = ctx2 + 'api/v1/accompanybook/userRole'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 陪诊-获取员工服务商id
    accompanyproviderUserProvider(param){
      const url = ctx2 + 'api/v1/accompanyprovideruser/query/provider'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 陪诊-根据id查询服务商
    accompanyproviderQueryOne(param){
      const url = ctx2 + 'api/v1/accompanyprovider/query/one'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 陪诊-财务明细分页
    accompanyfinanceQueryPage(param){
      const url = ctx2 + 'api/v1/accompanyfinance/query/page'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 陪诊-获取陪诊师列表
    getAccompanyemployeePage(param){
      const url = ctx2 + 'api/v1/accompanyemployee/query/page'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 陪诊-更新订单
    accompanybookUpdateOrder(param){
      const url = ctx2 + 'api/v1/accompanybook/updateOrder'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 陪诊-指派订单
    accompanybookDispatchProvider(param){
      const url = ctx2 + 'api/v1/accompanybook/dispatchProvider'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 陪诊-指派订单
    accompanybookChangeEmployee(param){
      const url = ctx2 + 'api/v1/accompanybook/changeEmployee'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 数据统计-订单统计
    accompanyBookTotalOrderStatistic(param){
      const url = ctx2 + 'api/v1/accompanybook/totalOrderStatistic'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 数据统计-帖子阅读数统计
    accompanyBookPostReadCountStatistic(param){
      const url = ctx2 + 'api/v1/accompanybook/postReadCountStatistic'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 数据统计-收益统计
    accompanyBookIncomeStatistic(param){
      const url = ctx2 + 'api/v1/accompanybook/incomeStatistic'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 数据统计-陪诊师排名Top10
    accompanyBookEmployeeRankStatistic(param){
      const url = ctx2 + 'api/v1/accompanybook/employeeRankStatistic'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 数据统计-陪诊师排名Top10
    accompanyBookEmployeeRankStatistic(param){
      const url = ctx2 + 'api/v1/accompanybook/employeeRankStatistic'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 数据统计-陪诊师性别统计
    accompanyBookEmployeeSexStatistic(param){
      const url = ctx2 + 'api/v1/accompanybook/employeeSexStatistic'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 获取角色帖子id集合
    accompanypostQueryPostIdListByRole(param){
      const url = ctx2 + 'api/v1/accompanypost/query/postIdListByRole'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 更新服务商
    accompanyproviderUpdate(param){
      const url = ctx2 + 'api/v1/accompanyprovider/update'
      return serverOptions.accompanyDoctorPutJson(url, param)
    },
    // 数据统计-帖子统计
    accompanyBookPostStatistic(param){
      const url = ctx2 + 'api/v1/accompanybook/postStatistic'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 更新陪诊-粉丝管理数据
    accompanyfansrecordUpdate(param){
      const url = ctx2 + 'api/v1/accompanyfansrecord/update'
      return serverOptions.accompanyDoctorPutJson(url, param)
    },
    // 陪诊课程 ---------
    // 陪诊课程分类分页
    accompanycourseclassifyQueryPage(param){
      const url = ctx2 + 'api/v1/accompanycourseclassify/query/page'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 课程分页
    accompanycourseQueryPage(param){
      const url = ctx2 + 'api/v1/accompanycourse/query/page'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 课程分页 根据id查询课程
    accompanycourseQueryOne(param){
      const url = ctx2 + 'api/v1/accompanycourse/query/one'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 目录管理列表
    accompanycourseDirectoryList(param){
      const url = ctx2 + 'api/v1/accompanycourse/directoryList'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 课程-新增观看记录
    accompanycoursestudylogInsert(param){
      const url = ctx2 + 'api/v1/accompanycoursestudylog/insert'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 课程-最近学习分页
    accompanycoursestudyQueryRecentStudyPage(param){
      const url = ctx2 + 'api/v1/accompanycoursestudy/query/recentStudyPage'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 课程-学习统计
    accompanycoursestudylogStudyStatistic(param){
      const url = ctx2 + 'api/v1/accompanycoursestudylog/studyStatistic'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 课程-数据统计-陪诊师学习时长排名
    accompanybookCourseStudyStatistic(param){
      const url = ctx2 + 'api/v1/accompanybook/courseStudyStatistic'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 课程-数据统计-服务商下陪诊师数量
    accompanyemployeeQueryCount(param){
      const url = ctx2 + 'api/v1/accompanyemployee/query/count'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 课程-数据统计-陪诊师学习排名
    accompanybookCourseEmployeeRank(param){
      const url = ctx2 + 'api/v1/accompanybook/courseEmployeeRank'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 课程-数据统计-陪诊师本月订单统计
    accompanybookEmployeeOrderStatistic(param){
      const url = ctx2 + 'api/v1/accompanybook/employeeOrderStatistic'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 课程-数据统计-陪诊师星级统计
    accompanybookEmployeeStarStatistic(param){
      const url = ctx2 + 'api/v1/accompanybook/employeeStarStatistic'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 课程-数据统计-陪诊师本月学习时长
    accompanybookEmployeeStudyStatistic(param){
      const url = ctx2 + 'api/v1/accompanybook/employeeStudyStatistic'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 配置信息查询
    accompanyconfigQueryConfig(param){
      const url = ctx2 + 'api/v1/accompanyconfig/queryConfig'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 陪诊订单分页-小程序
    accompanybookQueryMiniAppPage(param){
      const url = ctx2 + 'api/v1/accompanybook/query/miniAppPage'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 套餐订单分页-小程序个人套餐订单
    accompanycombouserQueryMiniAppPage(param){
      const url = ctx2 + 'api/v1/accompanycombouser/query/miniAppPage'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 根据id和地市查询服务
    accompanyserviceQueryOneByCity(param){
      const url = ctx2 + 'api/v1/accompanyservice/query/oneByCity'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 陪诊服务根据城市分页
    accompanyserviceQueryCityPage(param){
      const url = ctx2 + 'api/v1/accompanyservice/query/cityPage'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 根据id和地市查询套餐
    accompanycomboQueryOneByCity(param){
      const url = ctx2 + 'api/v1/accompanycombo/query/oneByCity'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 查询套餐城市价格分页
    accompanycomboQueryCityPage(param){
      const url = ctx2 + 'api/v1/accompanycombo/query/cityPage'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 好评陪诊师列表
    accompanyemployeeQueryStarPage(param){
      const url = ctx2 + 'api/v1/accompanyemployee/query/starPage'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 获取课程的资料
    accompanycourseinfoQueryInfo(param){
      const url = ctx2 + 'api/v1/accompanycourseinfo/query/info'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 可见陪诊记录id集合
    accompanypatientrecordQuerySeeList(param){
      const url = ctx2 + 'api/v1/accompanypatientrecord/query/seeList'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 新增陪诊师
    accompanyemployeeInsert(param){
      const url = ctx2 + 'api/v1/accompanyemployee/insert'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 审核失败更新陪诊师
    accompanyemployeeUpdate(param){
      const url = ctx2 + 'api/v1/accompanyemployee/failUpdate'
      return serverOptions.accompanyDoctorPutJson(url, param)
    },
    // 陪诊师报名费支付
    lklaccompanyEmployeeFeePay(param){
      const url = ctx2 + 'api/v1/lklaccompany/employeeFeePay'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 添加购买人信息
    accompanyinsureAdd(param){
      // const url = ctx2 + 'api/v1/accompanyinsure/add'
      const url = ctx2 + 'api/v1/accompanyinsure/add'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 更新购买人信息
    accompanyinsureUpdate(param){
      const url = ctx2 + 'api/v1/accompanyinsure/update'
      return serverOptions.accompanyDoctorPutJson(url, param)
    },
    // 根据陪诊订单id查询购买人信息
    accompanyinsureQuery(param){
      const url = ctx2 + 'api/v1/accompanyinsure/query/accompany'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 根据陪诊订单id取消保障
    accompanyinsureCancel(param){
      const url = ctx2 + 'api/v1/accompanyinsure/cancel'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 获取门诊无忧服务单地址
    accompanyinsureEpolicyUrl(param){
      const url = ctx2 + 'api/v1/accompanyinsure/epolicyUrl'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 小程序门诊无忧服务订单分页
    accompanyinsureQueryMiniPage(param){
      const url = ctx2 + 'api/v1/accompanyinsure/query/miniPage'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 小程序门诊无忧服务订单开发票
    accompanyinsureInvoiceOpen(param){
      const url = ctx2 + 'api/v1/accompanyinsure/invoiceOpen'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 查询联合订单
    accompanycombineorderQueryCombineOrder(param){
      const url = ctx2 + 'api/v1/accompanycombineorder/queryCombineOrder'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 根据业务id和业务类型查询订单详情
    minichannellinkQueryOne(param){
      const url = ctx2 + 'api/v1/minichannellink/query/one/business'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 可见陪诊记录id集合
    accompanypatientrecordQuerySeeList(param){
      const url = env.ctx + 'dm/api/v1/accompanypatientrecord/query/seeList'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 新增陪诊师
    accompanyemployeeInsert(param){
      const url = env.ctx + 'dm/api/v1/accompanyemployee/insert'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 查询账户余额
    lklaccompanyBalanceQuery(param){
      const url = ctx2 + 'api/v1/lklaccompany/balance/query'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 流水列表查询
    accompanybookQueryStatisticLog(param){
      const url = ctx2 + 'api/v1/accompanybook/query/statisticLog'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 流水统计
    accompanybookQueryStatistic(param){
      const url = ctx2 + 'api/v1/accompanybook/query/statistic'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 服务分类分页
    accompanyserviceclassifyQueryPage(param){
      const url = ctx2 + 'api/v1/accompanyserviceclassify/query/page'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 根据id查询服务分类
    accompanyserviceclassifyQueryOne(param){
      const url = ctx2 + 'api/v1/accompanyserviceclassify/query/one'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 根基id查询医嘱
    accompanyadviceQueryAccompany (param) {
      const url = ctx2 + 'api/v1/accompanyadvice/query/accompany'
      return serverOptions.accompanyDoctorGet(url, param)
    },
    // 新增医嘱
    accompanyadviceInsert (param) {
      const url = ctx2 + 'api/v1/accompanyadvice/insert'
      return serverOptions.accompanyDoctorPost(url, param)
    },
    // 更新医嘱
    accompanyadviceUpdate (param) {
      const url = ctx2 + 'api/v1/accompanyadvice/update'
      return serverOptions.accompanyDoctorPutJson(url, param)
    },
}
