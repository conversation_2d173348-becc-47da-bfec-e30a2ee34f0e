<template>
	<view>
		<view class="statusBar" :style="{'height':statusHeight + 'px','background-color':headerobj.headBgColor}">
		</view>
		<view class="header mobile-item" :style="{height:titleHeight + 'px',top:statusHeight + 'px'}">
			<view class="hx-view">
				<view class="customNavigation" :style="{'background-color':headerobj.headBgColor,height:titleHeight + 'px'
					,color:headerobj.contentColor}">

					<view class="customeNavbg hx-search-box" :class="[headerobj.alignL ? 'left' : 'center']"
						v-if="headerobj.currentIndex == 0">

						<view class="tx-center items">
							<block v-if="headerobj.titleType == 'txt'">
								{{ headerobj.titleTxt }}
							</block>
							<image style="height: 60rpx" mode="heightFix" :src="headerobj.titleImg" alt="" v-else />
						</view>
					</view>
					<block v-if="headerobj.currentIndex == 3">
						<slot></slot>
					</block>
					<view class="back-up" :style="{
            borderColor:headerobj.borderColor ? headerobj.borderColor : '#fff'
          }" @click="preStep" v-if="isBack"></view>

				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "titleHeader",
		props: {
			headerobj: {
				type: Object,
				default: function (){
					return {}
				},
			},
      isHome:{
        type:Boolean,
        default:false
      }
		},
		data() {
			return {
        homeURL:'/pages/index/index',
				isBack: false,
				statusHeight: 0,
				titleHeight:0,
				allheights:0,
				// 没有返回页面路径
				nopages:['pages/home/<USER>','pages/index/index','pages/category/index','pages/customize/index','pages/tabulation/index'],

			};
		},
		watch: {
		},
		mounted() {
			var pages = getCurrentPages()
      console.log('this.nopages.indexOf(pages[pages.length - 1].__route__) == -1',this.nopages.indexOf(pages[pages.length - 1].__route__) == -1)

			if(this.nopages.indexOf(pages[pages.length - 1].__route__) == -1){
				this.isBack = true;
			}

			// // 在组件实例进入页面节点树时执行
			// 初始化头部导航栏
			this.initHeader();
		},
		methods: {
			setData(obj) {

			},
			// 初始化头部导航栏
			initHeader() {
				let systemInfo = uni.getSystemInfoSync();
				let statusHeight = systemInfo.statusBarHeight; //状态栏的高度
				let titleHeight = 45; //导航栏高度，这个一般是固定的
				let allheights = systemInfo.windowHeight;
				// console.log((systemInfo))
				this.statusHeight = statusHeight;
				this.titleHeight = titleHeight;
				this.allheights = allheights;


        this.$emit('init',titleHeight + statusHeight)


			},
			preStep() {
        var pages = getCurrentPages()
        if(pages.length == 1 || this.isHome){
          uni.switchTab({
            // url:"/pages/store/index",
            url:this.homeURL
          })
        }else{
          uni.navigateBack({
            delta: 1,
          })
        }
			}
		}
	}
</script>

<style lang="scss">
	.statusBar {
		position: fixed;
		top: 0;
		width: 100%;
		z-index: 99999
	}

	.header {
		position: fixed;
		top: 0;
		height: 128rpx;
		z-index: 99999;
	}

	.back-up {
		height: 20rpx;
		width: 20rpx;
		// border: solid rgb(0, 0, 0);
		border: solid #fff;
		border-width: 2px 2px 0 0;
		transform: rotate(-135deg);
		position: absolute;
		/* top: calc(50% - 14rpx); */
		top: 15px;
		left: 23rpx;
	}

	.header .customNavigation {
		height: 128rpx;
		background-color: #ffffff;
	}

	.header .customNavigation .customeNavbg {
		position: absolute;
		/* bottom: 10rpx; */
		height: 45px;
		line-height: 45px;
		padding: 0 20rpx;

	}

	.customeNavbg.center {
		left: 50%;
		transform: translateX(-50%);
	}

	.customeNavbg.left {
		left: 50rpx;

	}

	.header .customNavigation .hx-search-box {
		line-height: 40px;
	}

	.header .customNavigation .hx-search-item2 {
		position: absolute;
		bottom: 8px;
		/* bottom: 20rpx; */
		height: 60rpx;
		line-height: 60rpx;
		padding: 0 50rpx;
		/* padding-left: 50rpx; */

		width: 100%;
		padding-right: 205rpx;
		box-sizing: border-box;
	}

	.header .customNavigation .hx-search-item2 .items {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.header .customNavigation .tx-center {
		-webkit-box-flex: initial;
		-ms-flex: initial;
		flex: initial;
		-webkit-box-pack: center;
		-ms-flex-pack: center;
		justify-content: center;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		max-width: 400rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
    line-height:inherit;
	}

	.hx-view {
		position: relative;
		/* zoom: 0.5; */
		width: 750rpx;
	}

	.hx-search-box .hx-search {
		padding: 20rpx;
		position: relative;
		width: 100%;
	}

	.hx-search-box .search-ico {
		font-size: 40rpx;
		position: absolute;
		left: 35rpx;
		top: 50%;
		transform: translateY(-50%);
	}

	.hx-search-box .hx-search-input {
		height: 60rpx;
		flex: 1;
		border-radius: 40rpx;
		padding-left: 70rpx;
		padding-right: 70rpx;
		box-sizing: border-box;
		font-size: 24rpx;
		line-height: 60rpx;
		color: #c5c5c5;
		border: 3rpx solid #ecebef;
	}

	.hx-search-box .hx-search-ico {
		width: 70rpx;
		/* background: red; */
		height: 70rpx;
	}

	.hx-search-box .mgl20 {
		margin-left: 20rpx;
	}

	.hx-search-box .mgr20 {
		margin-right: 20rpx;
	}

	.hx-search-box .l_ico_b {
		position: relative;
	}

	.hx-search-box .square {
		padding: 5rpx;
		background-color: red;
		color: #fff;
		position: absolute;
		right: -10rpx;
		top: -8rpx;
		border-radius: 50%;
	}

	// .hx-search-box input {
	// 	outline: none;
	// }

	.hx-search-box .hx-dc {
		border: none;
	}

	.d-flex {
		display: flex;
		align-items: center;
	}

	.header .customNavigation .hx-search-left-ico {
		position: absolute;
		bottom: 10rpx;
		height: 60rpx;
		left: 20rpx;
	}

</style>
