<template>
  <page>
    <view class="body-main" slot="content" :style="{backgroundImage:'url(' + file_ctx + bgImageUrl + ')'}">
      <!-- #ifndef H5 -->
      <sa-hover-menu />
      <!-- #endif -->
      <view class="wish">
        <text class="wish-title" v-if="wishingwellConfig.guardDay">
          健康保护神已守护你{{ wishingwellConfig.guardDay }}天{{ wishingwellConfig.patronSaintGourdConfigList.length > wishingwellConfig.lightDay ? '，明天还可点亮一个' : '' }}
        </text>
        <text class="wish-title" v-else>您暂时还未点亮健康保护神</text>
        <view class="hulu-list">
          <view class="hulu-item" v-for="(item,index) in wishingwellConfig.patronSaintGourdConfigList" :key="index" @tap="checkCard(item)">
            <image class="hulu-logo" mode="aspectFill" v-if="item.lightStatus == 1" :src="file_ctx + item.afterLightLogoPath" />
            <image class="hulu-logo" mode="aspectFill" v-else :src="file_ctx + item.beforeLightLogoPath" />
            <text :style="item.lightStatus == 1 ? 'color: #00D29D;' : ''" class="hulu-name">{{item.name}}</text>
          </view>
        </view>
        <view class="wishing-well">
          <text class="wishing-title">许愿池</text>
          <scroll-view class="wishing-list" scroll-y scroll-with-animation :scroll-top="wishScrollTop" :lower-threshold="26"
            @scrolltolower="scrolltolower" @touchstart="wishTouchstart" @touchend="wishTouchend"
          >
            <view class="wishing-item" v-for="(item, index) in virtualWishList" :key="index" style="height: 26px;">
              <uni-icons :size="16" type="star-filled" color="#fcbd54"></uni-icons>
              <text class="wishing-text">
                {{ item.nickName + '刚发表了许愿：' + item.content }}
              </text>
            </view>
          </scroll-view>
          <view class="wishing-submit-box">
            <input type="text" class="wishing-submit-input" placeholder="接健康保护神" v-model="wishContent" />
            <button class="wishing-submit-btn" @tap="submitWish" :disabled="submitLoading">{{ submitLoading ? '发表中...' : '发表许愿'}}</button>
          </view>
        </view>
      </view>
      <card :info="cardInfo" @take="takeCard" ref="huluCardRef" />
    </view>
  </page>
</template>

<script>
import UniIcons from '@/components/uni/uni-icons/uni-icons.vue';
import card from './components/card.vue'
export default {
  components: {
    UniIcons,
    card
  },
  data () {
    return {
      file_ctx: this.file_ctx,
      bgImageUrl: '', // 背景图
      wishingwellConfig: {},
      wishList: [],
      cardInfo: {},
      wishContent: '',
      wishScrollTop: 0,
      timer: null, // 许愿池滚动定时器
      mousedownTimer: null, // 手指触摸离开许愿池定时器 用于重新开启许愿池滚动定时器
      virtualWishList: [],
      submitLoading: false,
      isTipJumpHome: true
    }
  },
  onLoad () {
    this.getWishingwellGetLightSet().then(async () => {
      await this.lightInit()
      // 当前最近点亮的葫芦是否有赠送头像挂件
      const currentHulu = this.wishingwellConfig.patronSaintGourdConfigList[this.wishingwellConfig.lightDay-1]
      console.log(currentHulu)
      if (currentHulu.isGivePendant == 1) {
        // 重新获取粉丝档案信息
        this.$ext.user.getCommunityFansRecord()
      }
      if (!this.$validate.isNull(this.wishList)) {
        this.$nextTick(() => {
          this.timer = setInterval(() => {
            this.wishScrollTop += 26
          }, 1200)
        })
      }
    })
    this.getWishingwellGetWish()
    
  },
  beforeDestroy() {
    clearInterval(this.timer)
    this.timer = null
  },
  methods: {
    // 触摸许愿池
    wishTouchstart () {
      // 停止许愿池滚动
      clearInterval(this.timer)
      this.timer = null
      clearTimeout(this.mousedownTimer)
      this.mousedownTimer = null
    },
    // 手指离开触摸许愿池
    wishTouchend () {
      this.mousedownTimer = setTimeout(() => {
        this.timer = setInterval(() => {
          this.wishScrollTop += 26
        }, 1200)
      }, 2000)
    },
    // 许愿池列表滚动触底
    scrolltolower () {
      if (this.virtualWishList.length <= 500) {
        this.virtualWishList = [...this.virtualWishList,...this.wishList]
      } else {
        this.wishScrollTop = 0
        this.virtualWishList = this.wishList
      }
    },
    async lightInit () {
      // 全部未点亮
      if (!this.wishingwellConfig.lightDay) {
        this.cardInfo = this.wishingwellConfig.patronSaintGourdConfigList[0]
        this.$refs.huluCardRef.open()
        await this.addLight()
        await this.getWishingwellGetLightSet()
      } else if (this.wishingwellConfig.lightDay == this.wishingwellConfig.patronSaintGourdConfigList.length) {
        // 全部点亮
        return
      } else {
        const lastLightObj = this.wishingwellConfig.patronSaintGourdConfigList[this.wishingwellConfig.lightDay - 1]
        // 当天是否点亮
        if (new Date(lastLightObj.lightTime).getDay() === new Date().getDay()) return

        this.cardInfo = this.wishingwellConfig.patronSaintGourdConfigList[this.wishingwellConfig.lightDay]
        this.$refs.huluCardRef.open()
        await this.addLight()
        await this.getWishingwellGetLightSet()
      }
    },
    checkCard (data) {
      if (data.lightStatus != 1) {
        this.$uniPlugin.toast('该葫芦暂未点亮')
        return
      }
      this.cardInfo = data
      this.$refs.huluCardRef.open()
    },
    // 收下卡片
    takeCard () {
      this.$refs.huluCardRef.close()
      const modalConfig = {
        showCancel: true, // 是否显示取消按钮，默认为 true
        cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
        cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
        confirmText: '确定', //  确定按钮的文字，默认为"确定"，最多 4 个字符
        confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
        fn: async (n) => {
          if (n) {
            
          } else {
            this.$navto.pushTab('Index', {})
          }
        }
      }
      this.$uniPlugin.modal('',`${this.cardInfo.name}葫芦已被点亮，是否留下您的许愿祝福？`,modalConfig)
    },
    // 点亮
    async addLight () {
      await this.$api.wishingwell.wishingwellAddLight()
    },
    // 发表许愿
    submitWish () {
      if (this.submitLoading) return
      this.$uniPlugin.modal('','确认发表许愿？', {
        showCancel: true, // 是否显示取消按钮，默认为 true
        cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
        cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
        confirmText: '确定', //  确定按钮的文字，默认为"确定"，最多 4 个字符
        confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
        fn: async (n) => {
          if(n) {
            this.submitLoading = true
            const content = this.wishContent ? this.wishContent : '接健康保护神'
            const param = {
              wishDesc: content
            }
            this.$api.wishingwell.wishingwellAddWish(param).then(res => {
              this.submitLoading = false
              this.$uniPlugin.toast(res.msg)
              // 手动添加到轮播列表
              const addIndex = Math.ceil(this.wishScrollTop/26) + 6 // 插入位置索引
              const fansRecord = this.$common.getKeyVal('user', 'fansRecord')
              this.virtualWishList.splice(addIndex, 0, {nickName: fansRecord.nickName, content: content});
              this.wishContent = ''

              if (this.isTipJumpHome) {
                  setTimeout(() => {
                    const modalConfig = {
                      showCancel: true, // 是否显示取消按钮，默认为 true
                      cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
                      cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
                      confirmText: '确定', //  确定按钮的文字，默认为"确定"，最多 4 个字符
                      confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
                      fn: async (n) => {
                        this.isTipJumpHome = false
                        if (n) {
                          this.$navto.pushTab('Index', {})
                        }
                      }
                    }
                    this.$uniPlugin.modal('','返回首页马上与1000万病友在线交流，分享治疗经验，获取健康帮助！',modalConfig)
                  }, 1000)
              }
            }).catch(() => {
              this.submitLoading = false
            })
          }
        }
      })
    },
    // 获取许愿
    async getWishingwellGetWish () {
      const res = await this.$api.wishingwell.wishingwellGetWish()
      this.wishList = res.data
      this.virtualWishList = res.data
    },
    // 获取点亮配置
    getWishingwellGetLightSet () {
      return new Promise((resolve, reject) => {
        this.$api.wishingwell.wishingwellGetLightSet().then(res => {
          this.wishingwellConfig = res.data
          // 是否没点亮葫芦 点亮天数为0 拿第一天点亮前背景图
          if (!this.wishingwellConfig.lightDay) {
            this.bgImageUrl = this.wishingwellConfig.bigImagePath
          } else {
            this.bgImageUrl = this.wishingwellConfig.patronSaintGourdConfigList[this.wishingwellConfig.lightDay-1].afterLightBgPath
          }
          resolve()
        }).catch(err => {
          reject(err)
        })
      })
    }
  },
}
</script>

<style lang="scss" scoped>
.body-main {
  height: 100%;
  overflow: hidden;
  background-size: cover;
  background-repeat: no-repeat;
}
.wish {
  position: fixed;
  bottom: 0;
  padding: 0 31upx;
  padding-bottom: 28upx;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 28upx);
  padding-bottom: calc(env(safe-area-inset-bottom) + 28upx);
  left: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  box-sizing: border-box;
  &-title {
    font-size: 27upx;
    font-weight: 500;
    color: #fff;
    line-height: 36upx;
    padding: 8upx 24upx;
    background-color: rgba(255,255,255, .3);
    border: 1px solid #adc39e;
  }
}
.hulu-list {
  width: 100%;
  display: flex;
  margin: 18upx 0;
  .hulu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 90upx;
    height: 100upx;
    @include rounded(5upx);
    box-sizing: border-box;
    background: #FFFFFF;
    overflow: hidden;
    &+.hulu-item {
      margin-left: 10upx;
    }
    .hulu-logo {
      width: 35upx;
      height: 60upx;
    }
    .hulu-name {
      font-size: 24upx;
      color: #282828;
    }
  }
}
.wishing-well {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 410upx;
  background: #FFFFFF;
  @include rounded(15upx);
  padding-top: 12upx;
  .wishing-title {
    display: block;
    text-align: center;
    font-size: 33upx;
    font-weight: 500;
    color: #00D29D;
    line-height: 69upx;
  }
  .wishing-list {
    padding: 0 22upx;
    flex: 1;
    overflow-y: auto;
    .wishing-item {
      display: flex;
      align-items: center;
      box-sizing: border-box;
      overflow: hidden;
      padding-right: 24upx;
    }
    .wishing-text {
      flex: 1;
      font-size: 27upx;
      color: #282828;
      line-height: 52upx;
      display: inline-block;
      vertical-align: middle;
      @include ellipsis(1);
      margin-left: 17upx;
    }
  }
}
.wishing-submit-box {
  width: 100%;
  height: 87upx;
  display: flex;
  box-sizing: border-box;
  .wishing-submit-input {
    flex: 1;
    height: 100%;
    font-size: 30upx;
    font-weight: 500;
    line-height: 42upx;
    padding-left: 22upx;
    background: #F6F6F6;
  }
  .wishing-submit-btn {
    width: 191upx;
    height: 100%;
    background: #FFD941;
    @include rounded(15upx);
    margin: 0;
    padding: 0;
    font-size: 31upx;
    font-weight: 500;
    color: #000000;
    line-height: 87upx;
    text-align: center;
  }
}
</style>