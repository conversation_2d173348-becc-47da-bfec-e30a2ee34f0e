<template>
  <page>
    <view slot="content" class="content">
      <!-- tab菜单-->
      <tabs-sticky
        v-model="curIndex"
        :fixed="false"
        :tabs="tabs"
        @change="changeTab"
      ></tabs-sticky>
      <view class="main">
        <swiper class="swiper" :current="curIndex" @change="swiperChange">
          <swiper-item v-for="(item, index) in tabs" :key="index">
            <posts-list ref="postsListRef" :params="{...item,accompanyPostIdList}" :isShowBtn="isShowBtn" />
          </swiper-item>
        </swiper>
      </view>
    </view>
  </page>
</template>

<script>
import TabsSticky from '@/components/basics/tabs-sticky'
import postsList from './components/posts-list.vue'
import page from '@/components/basics/frame/page'
export default {
  components: {
    TabsSticky,
    postsList,
    page
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      $validate: this.$validate,
      curIndex: 0, // 当前菜单下标,
      tabs: [{ name: '已发布', processStatus: 2 }, { name: '待审核', processStatus: 1 }],
      isShowBtn:true,
      roleType:'',
      accompanyPostIdList:null,
    }
  },
  async onLoad() {
    const res = await this.accompanypostQueryPostIdListByRole()
    this.accompanyPostIdList = res.data
    const query = this.$Route.query
    if (!this.$validate.isNull(query) && !this.$validate.isNull(query.isShowBtn)) {
      this.isShowBtn = JSON.parse(query.isShowBtn)
    }
    if (!this.$validate.isNull(query) && !this.$validate.isNull(query.processStatus)) {
      const curIndex = this.tabs.findIndex(item => item.processStatus == query.processStatus)
      this.curIndex = curIndex
    }
    this.init()
  },
  methods: {
    // 轮播菜单
    swiperChange(e) {
      this.changeTab(e.detail.current)
    },
    changeTab(index) {
      this.curIndex = index
    },
    navtoGo(url = '', obj = {}) {
      this.$navto.push(url, obj)
    },
    async accompanypostQueryPostIdListByRole(){
      const isUserRole = this.$common.getKeyVal('user','isUserRole',true)
      const codeUserInfo = this.$common.getKeyVal('user', 'codeUserInfo',true);
      if(isUserRole !=="" && isUserRole.hasEmployee && isUserRole.hasProvider){
        this.roleType = 1
      } else if(isUserRole !=="" && isUserRole.hasEmployee){
        this.roleType = 2
      } else if(isUserRole !=="" && isUserRole.hasProvider){
        this.roleType = 1
      }
      let params = {
        userId:codeUserInfo.id,
        roleType:this.roleType,
      }
      const res = this.$api.accompanyDoctor.accompanypostQueryPostIdListByRole(params)
      return Promise.resolve(res)
    },
    init(val) {

      this.$nextTick(() => {
        const curIndex = this.curIndex
        this.changeTab(curIndex)
        for (let i = 0; i < this.$refs.postsListRef.length; i++) {
          const item = this.$refs.postsListRef[i];
          item.init()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}
.main {
  flex: 1;
  .swiper {
    height: 100%;
  }
}
</style>
