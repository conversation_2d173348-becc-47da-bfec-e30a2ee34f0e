<template>
  <view class="playback">
    <view class="playback-box" v-for="(item,index) in list" :key="index" @tap="navTo('MeetingPlayBack', item)">
      <image mode="aspectFill" :src="item.imageCoverPath ? file_ctx + item.imageCoverPath : ''"  class="playback-cover"/>
      <view class="playback-right">
        <text class="playback-title">{{ item.title }}</text>
        <text class="playback-duration">时长：{{ item.burningTimeText }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      default: function () {
        return []
      }
    },
    isJump: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $timePlugin: this.$timePlugin,
    }
  },
  methods: {
    navTo(name, paramObj) {
      if (this.isJump) {
        this.$navto.push(name, {id: paramObj.id})
      } else {
        this.$emit('clickItem', paramObj)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.playback {
  width: 100%;
 
  &-box {
    @include rounded(20upx);
    background-color:#FFFFFF;
    padding:16upx;
    margin-bottom: 24upx;
    height: 200upx;
    display: flex;
    border-bottom: 1upx solid #f0f0f0;
    box-sizing: border-box;
  }
  &-cover {
    width: 240upx;
    height: 100%;
    display: inline-block;
    vertical-align: middle;
    @include rounded(20upx);
  }
  &-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding-left: 24upx;
  }
  &-title {
    display: inline-block;
    vertical-align: middle;
    @include ellipsis(2);
    font-size: 32upx;
    color: #333333;
    font-weight: 600;
	white-space: nowrap;
	overflow-x: auto;
  }
  &-duration {
    color: #c0c0c0;
    font-size: 22upx;
  }
}
</style>
