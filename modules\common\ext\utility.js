import wechatPlugin from '@/common/util/wechat-plugin'
import common from '@/common/util/main'
import uniPlugin from '@/common/util/uni-plugin'
/**
 * 针对uni组件中间层代码 获取数据进行二次数据清洗、加工、转化，特别指API
 */
export default {
  /**
   * 获取授权地理位置
   * @param params {}
   * @param resolve
   * @param reject
   */
  getSetting() {
    return new Promise((resolve, reject) => {
      uniPlugin.getSetting((res) => {
        if (res && res.authSetting && res.authSetting.hasOwnProperty('scope.userLocation')) {
          if (res.authSetting['scope.userLocation']) {
            resolve()
          } else { // 拒绝授权，打开授权设置
            uniPlugin.openSetting(() => {
              reject()
            },
            () => {

            })
          }
        } else {
          uniPlugin.openSetting(() => {
            reject()
          },
          () => {

          })
        }
      }
      )
    })
  },
  /**
   * 调起客户端扫码界面，扫码成功后返回对应的结果。
   * @param callback
   */
  scanCode(callback) {
    // #ifdef H5
    wechatPlugin.scanQRCode(callback)
    // #endif
    // #ifndef H5
    uniPlugin.scanCode(callback)
    // #endif
  },
  /**
   * 获取当前地址
   * @param params {}
   * @param resolve
   * @param reject
   */
  getLocation() {
    return new Promise((resolve, reject) => {
      // #ifdef H5
      wechatPlugin.getLocation((res) => {
        console.log('定位成功1:', res)
        resolve(res)
      }, (res) => {
        console.log('定位失败1:', res)
        uniPlugin.toast('定位失败')
        common.setKeyVal('system', 'longitudeAndLatitude', {}, true)
        reject()
      })
      // #endif
      // #ifndef H5
      // that.getSetting().then(() => {
      uniPlugin.authorize('scope.userLocation', () => {
        uniPlugin.getLocation((res) => {
          console.log('定位成功1:', res)
          resolve(res)
        },
        (res) => {
          console.log('定位失败1:', res)
          // uniPlugin.toast('定位失败')
          common.setKeyVal('system', 'longitudeAndLatitude', {}, true)
          reject()
        })
      },
      (res) => {
        uniPlugin.toast('地理位置授权失败')
        reject()
      })
      // }).catch(() => {
      //
      // })
      // #endif
    })
  },
  /**
   * 获取当前地址 强制用户授权
   * @param params {}
   * @param resolve
   * @param reject
   */
  getLocationExpand(modalConfig = {}) {
    return new Promise(async (resolve, reject) => {
      const locationRes = await this.getLocation().catch(() => {
        uniPlugin.hideToast()
        // 没有权限则提示弹窗
        uniPlugin.modal('提示', '需要获取地理位置权限，请到小程序设置页面打开授权', {
          fn: async (res) => {
            if (res) {
              // 选择弹框内授权
              uni.openSetting({
                success: async () => {
                  // that.getPosition()
                  let Position = await this.getLocationExpand();
                  resolve(Position)
                }
              })
            } else {
              // 选择弹框内 不授权
              reject()
            }
          },
          showCancel: true, // 是否显示取消按钮，默认为 true
          cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
          cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
          confirmText: '确定', //  确定按钮的文字，默认为"确定"，最多 4 个字符
          confirmColor: '#00B484', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
          ...modalConfig
        })
      })
      if (locationRes) return resolve(locationRes)
    })
  }
}
