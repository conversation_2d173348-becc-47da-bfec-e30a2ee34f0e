<template>
  <page>
    <view slot="content" class="body-main">
      <scroll-refresh style="height: 100%;" :isShowEmptySwitch="true" :fixed="false" :isAbsolute="false" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
        <view class="content">
          <view class="content-main">
            <nui-list class="nui-list" :indexlist="indexlist" @cateClick="cateClick" mode="collect" @collect="collect"></nui-list>
          </view>
        </view>
      </scroll-refresh>
    </view>
  </page>
</template>

<script>

import { mapState } from 'vuex'
import nuiList from '@/components/community/nui-list/nui-list.vue'
import scrollRefresh from '@/components/uni/zPaging/index'
import page from '@/components/basics/frame/page'
export default {
  components: {
    nuiList,
    scrollRefresh,
    page
  },
  data() {
    return {
      file_ctx: this.file_ctx,
      mescroll: null, // mescroll实例对象
      downOption: {
          auto: false // 不自动加载
      },
      upOption: {
          auto: false, // 不自动加载
          empty: {
            top: 0,
            zIndex: 999,
          }
      },
      isInit: false, // 列表是否已经初始化
      indexlist: [],
      pageStartDate: null
    }
  },
  computed: {
    ...mapState('user', {
      codeUserInfo: state => state.codeUserInfo, // 当前登录用户信息
      curSelectUserInfo: state => state.curSelectUserInfo, // 当前选中的用户信息
      curSelectUserInfoId: state => state.curSelectUserInfoId, // 当前选中的档案的ID
      recordUserInfo: state => state.recordUserInfo,
      accountId: state => state.accountId
    })
  },
  onLoad(paramsObj) {
    const that = this
    const query = that.$Route.query
    this.pageStartDate = that.$common.formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss')
    if (!that.$validate.isNull(query)) {

    }
    this.$nextTick(() => {
      that.init()
    })
  },
  onShow() {

  },
  methods: {
    collect (data) {
      this.$uniPlugin.modal('','确认取消收藏该帖子？', {
        showCancel: true, // 是否显示取消按钮，默认为 true
        cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
        cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
        confirmText: '确定', //  确定按钮的文字，默认为"确定"，最多 4 个字符
        confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
        fn: async (n) => {
          if(n) {
            const res = await this.$api.postmessage.postmessageCancelCollection({id: data.id, accountId: this.accountId})
            this.$uniPlugin.toast(res.msg)
            this.init()
          }
        }
      })
    },
    cateClick (data) {
      this.navtoGo('Circle', {cid: data.circleClassifyId})
    },
    navtoGo(url, obj = {}) {
      this.$navto.push(url, obj)
    },
    init() {
      this.$nextTick(() => {
        this.mescroll.triggerDownScroll()
      })
    },
    scrollInit(scroll) {
        scroll.optUp.page.num = 1
        scroll.optUp.page.size = 10
        this.mescroll = scroll
    },
    returnFn(obj) {
        const that = this
        setTimeout(function() {
            let params = {
              current: obj.pageNum,
              size: obj.pageSize,
              condition: {
                  accountId: that.accountId,
                  createTime: that.pageStartDate
              }
            }
            that.$ext.community.commoncollectlikesQueryMyCollectionPage(params).then(res => {
              let data = res.data.records || []
              // data = data.map(item => {
              //   return {
              //     ...item,
              //     id: item.businessId,
              //     isDel: item.postMessageStatus == -1 || [2,4].includes(item.putawayStatus)
              //   }
              // })
              if (obj.pageNum === 1) {
                that.indexlist = []
              }
              that.indexlist = [...that.indexlist, ...data]
              obj.successCallback && obj.successCallback(data)
            })
        }, that.$constant.noun.scrollRefreshTime)

    },
  }
}
</script>

<style lang="scss" scoped>
  .body-main {
    height: 100%;
    overflow-y: auto;
    background-color: #fff;
    .content {
      padding-bottom: env(safe-area-inset-bottom);
      padding-bottom: constant(safe-area-inset-bottom);
      box-sizing: border-box;
      &-main {
        // padding:24upx 32upx 0;
        box-sizing: border-box;
        // /deep/.nui-list{
        //   .lists{
        //     .list-wrapper{
        //       margin-top: 24rpx;
        //       .list{
        //         padding: 24rpx 32rpx 0;
        //       }
        //     }
        //   }
        // }
      }
    }
  }
</style>
