<template>
    <view class="reply-item">
        <view class="user-avatar" @tap="userClick(info)">
          <image :src="info.headPath ? file_ctx + info.headPath : defaultAvatar" class="img" />
          <template v-if="info.isAddV === 1">
            <image v-if="info.vType === 2" class="head-v-icon" :src="file_ctx + 'static/image/system/avatar/icon-v-e.png'" mode="aspectFill"></image>
            <image v-else-if="info.vType == 1" class="head-v-icon" :src="file_ctx + 'static/image/system/avatar/icon-v.png'" mode="aspectFill"></image>
          </template>
        </view>
        <view class="reply">
            <view class="reply-top">
                <view class="user-info" @tap="userClick(info)">
                    <text class="name">{{ info.nickName }}</text>
                </view>
                <view class="reply-box" @click="$emit('reply', info)" v-if="info.subscribeStatus == 1">
                    <text class="reply-text">回复</text>
                </view>
            </view>
            <template v-if="info.subscribeStatus != 2">
                <template v-if="info.imagePath">
                    <image @click="previewImage(info.imagePath.split(','), eIndex)" v-for="(e, eIndex) in info.imagePath.split(',')" :key="eIndex"
                        :src="file_ctx + e" mode="widthFix" class="content-img"
                    ></image>
                </template>
                <template v-if="info.content">
                    <view class="reply-content" v-html="info.content"></view>
                </template>
            </template>

            <template v-else>
                该评论已被删除
            </template>
            
            <view class="reply-main">
                <text class="reply-user">
                    @{{ info.mutualNickname }}
                </text>
                <view class="reply-user-content" v-html="info.mutualContent">
                </view>
            </view>
            <text class="reply-time">{{ info.createTime }}</text>
        </view>
    </view>
</template>

<script>
import defaultImg from '@/components/basics/default-avatar/index'
import mpHtml from '@/components/basics/mp-html/mp-html.vue'
export default {
    components: {
        defaultImg,
        mpHtml
    },
    props: {
        info: {
            type: Object,
            default: function () {
                return {}
            }
        }
    },
    data () {
        return {
            file_ctx: this.file_ctx,
            defaultAvatar: this.$static_ctx + 'image/system/avatar/icon-default-avatar.png'
        }
    },
    methods: {
        userClick(data) {
			if (data.isAnonymity == 1) {
				this.$uniPlugin.toast('该帖子为匿名发表，无法查看该用户')
				return
			}
			const accountId = data.accountId
			// 是否是自己
			if (this.$common.getKeyVal('user', 'accountId') === accountId) {
				this.$navto.pushTab('Personal', {})
			} else {
				this.navtoGo('PersonalHomePage', { homePageAccountId:accountId })
			}
		},
    }
}
</script>

<style scoped lang="scss">
.reply-item {
    display: flex;
    box-sizing: border-box;
}
.reply {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding-left: 21upx;
    box-sizing: border-box;
    &-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 62upx;
    }
    &-box {
        width: 120upx;
        height: 50upx;
        background: #F6F6F6;
        @include rounded(25upx);
        box-sizing: border-box;
        text-align: center;
    }
    &-text {
        font-size: 31upx;
        font-weight: 500;
        color: #00D29D;
        line-height: 50upx;
    }
    .user-info {
        display: flex;
        align-items: center;
        .name {
            font-size: 31upx;
            font-weight: 500;
            color: #2D2D2D;
            line-height: 42upx;
        }
    }
    &-content {
        padding: 13upx 0 16upx;
        font-size: 26upx;
        font-weight: 400;
        color: #282828;
        line-height: 42upx;
    }
    &-main {
        padding: 24upx;
        background: #F6F6F6;
        @include rounded(15upx);
        .reply-user {
            display: block;
            padding-bottom: 12upx;
            font-size: 26upx;
            color: #282828;
            line-height: 42upx;
        }
        .reply-user-content {
            font-size: 26upx;
            color: #7F7F7F;
            line-height: 42upx;
        }
    }
    &-time {
        font-size: 24upx;
        color: #7F7F7F;
        line-height: 42upx;
        padding-top: 12upx;
    }
}
.user-avatar {
    position: relative;
    width: 62upx;
    height: 62upx;
    .img {
      @include rounded(50%);
      vertical-align:middle;
      display: inline-block;
      overflow: hidden;
      box-sizing: border-box;
      height: 100%;
      width: 100%;
    }
    .head-v-icon {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 32upx;
        height: 32upx;
    }
}
.content-img {
	width: 200upx;
	height: 100%;
}
</style>
