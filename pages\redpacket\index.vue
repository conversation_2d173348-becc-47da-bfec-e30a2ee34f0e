<template>
  <view class="content" :style="{ 'background-image': 'url('+ file_ctx + 'static/image/business/redpacket/page-bg.png' +')' }">
    <uni-nav-bar backgroundColor="rgba(0,0,0,0)" :border="false" fixed statusBar>
      <image :src="file_ctx + 'static/image/business/redpacket/icon-home.png'" class="icon-home" mode="aspectFill" slot="left" @tap="back" />
      <view class="nav-bar-title">做任务领红包</view>
    </uni-nav-bar>
    <image :src="imgList.banner" class="banner-img" mode="widthFix" />
    <view class="content-main">

      <template v-if="loading">
        <view class="empty-data-box">
          <text class="empty-data-text">加载中...</text>
        </view>
      </template>

      <template v-else-if="!qrCodeUrl">
        <view class="empty-data-box">
          <image
            mode="aspectFit"
            :src="file_ctx + 'static/image/business/redpacket/empty-data.png'"
            class="empty-data-img"
          />
          <text class="empty-data-text">暂无活动权益</text>
        </view>
      </template>

      <template v-else>
        <view class="press-tips-top-box" :style="{ opacity: touchstart ? '1' : '0' }">
          <text class="press-tips-top-text">再按住2秒</text>
          <text class="press-tips-top-text">关注送</text>
          <text class="press-tips-top-text">红包</text>
        </view>
        <view
          class="qrcode-box"
          :style="{ 'background-image': 'url('+ file_ctx + 'static/image/business/redpacket/qrcode-btn-bg.png' +')' }"
          @touchstart="gtouchstart()"
          @touchmove="gtouchmove()"
          @touchend="gtouchend()"
          :class="{
            'animation': touching,
            'no-border': !touchstart
          }"
        >
          <view class="qrcode-content-box" :style="{ opacity: touchstart ? '1' : '0' }">
            <image
              mode="aspectFit"
              :src="qrCodeUrl"
              :show-menu-by-longpress="true"
              class="qrcode-img"
            />
          </view>
          <view class="press-tips-box" v-show="!touchstart">
            <text class="qrcode-tips-text">领取奖励</text>
            <text class="qrcode-tips-text">请按住我</text>
            <text class="qrcode-tips-text">3秒</text>
          </view>
          <image mode="aspectFit" :src="file_ctx + 'static/image/business/redpacket/press-tips.gif'" class="press-tips-gif" v-show="!touchstart" />
        </view>

        <view v-if="!showError && qrCodeUrl" class="long-press-tips-box" :style="{ opacity: touchstart ? '1' : '0' }">
          <text class="long-press-tips">识别添加关注不会获取敏感信息</text>
        </view>
      </template>

    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'
export default {
  components: {
    uniNavBar
  },
  data() {
    return {
      file_ctx: this.file_ctx,
      touchstart: false,
      touching: false,
      timeOutEvent: null, // 长按定时器
      showError: false,
      qrCodeUrl: '',
      imgList: {
        banner: this.file_ctx + 'static/image/business/redpacket/banner.png'
      },
      loading: false,
      siteKey: '' // 投放站点的生成KEY 页面路径上拿
    }
  },
  computed: {
    ...mapState('user', {
      accountId: state => state.accountId
    })
  },
  onLoad() {
    const query = this.$Route.query
    if (this.$validate.isNull(query)) return this.errorTip()
    let params = query.scene ? decodeURIComponent(query.scene) : {}
    this.siteKey = query.siteKey || params.siteKey
    if (this.$validate.isNull(this.siteKey)) return this.errorTip()
    this.init()
  },
  methods: {
    /**
     * 错误提示
     */
    errorTip() {
      this.$uniPlugin.toast('入口过期，请重新扫码！')
    },
    async init() {
      this.loading = true
      this.$uniPlugin.loading()
      await this.getPacketQrcode().catch(() => {
        this.loading = false
        this.$uniPlugin.hideLoading()
      })
      this.$uniPlugin.hideLoading()
      this.loading = false
    },
    async getPacketQrcode() {
      const openId = await this.$ext.wechat.getOpenId()
      const params = {
        accountId: this.accountId,
        siteKey: this.siteKey,
        openId
      }
      const res = await this.$api.redpacket.packetQrcode(params)
      const data = res.data || {}
      this.qrCodeUrl = data.qrcodeUrl
    },
    back() {
      let pages = getCurrentPages() // 获取栈实例
      if (pages.length > 1) {
        this.$navto.back()
      } else {
        this.$navto.replaceAll('Index')
      }
    },
    // 手势开始
    gtouchstart() {
      this.touchstart = true
      this.touching = true
      const that = this;
      this.timeOutEvent = setTimeout(function () {
        that.touching = false
        that.longPress()
      }, 500);//这里设置定时器，定义长按500毫秒触发长按事件，时间可以自己改，个人感觉500毫秒非常合适
      return false;
    },
    //手释放，如果在500毫秒内就释放，则取消长按事件，此时可以执行onclick应该执行的事件
    gtouchend() {
      this.touching = false
      clearTimeout(this.timeOutEvent);//清除定时器
      if (this.timeOutEvent != 0) {
        //这里写要执行的内容（尤如onclick事件）
        // vm.goChat(item);
      }
      return false;
    },
    //如果手指有移动，则取消所有事件，此时说明用户只是要移动而不是长按
    gtouchmove() {
      this.touching = false
      clearTimeout(timeOutEvent);//清除定时器
      this.timeOutEvent = 0;

    },
    //真正长按后应该执行的内容
    longPress() {
      const that = this;
      setTimeout(()=>{
        // this.longPressShow = true
      }, 2000)
      this.timeOutEvent = 0;
    },
  }
}
</script>

<style lang="scss" scoped>
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  background-repeat: no-repeat;
  background-size: 750rpx 1432rpx;
  background-position: top;
  background-color: #F4F6FA;
}
.icon-home {
  width: 64rpx;
  height: 64rpx;
}
.nav-bar-title {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 32rpx;
  color: #fff;
  line-height: 44rpx;
}
.banner-img {
  width: 662rpx;
  margin-top: 32rpx;
}
.content-main {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 702rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
  padding: 42rpx 0 74rpx;
}
.press-tips-top-box {
  position: relative;
  display: flex;
  justify-content: center;
  width: 420rpx;
  height: 72rpx;
  background: linear-gradient( 180deg, #FF7B5A 0%, #FF493C 100%);
  border-radius: 36rpx;
  box-sizing: border-box;
  margin-bottom: 24rpx;
  transform-origin: center bottom;
  animation: rock .5s infinite;
  &::after {
    content: "";
    position: absolute;
    left: 50%;
    bottom: -16rpx;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 18rpx solid transparent;
    border-right: 18rpx solid transparent;
    border-top: 18rpx solid #FF4A3D;
  }
  .press-tips-top-text {
    line-height: 72rpx;
    font-weight: 600;
    font-size: 32rpx;
    color: #FFFFFF;
    &+.press-tips-top-text {
      margin-left: 4rpx;
    }
    &:last-child {
      font-weight: 600;
      font-size: 40rpx;
      color: #FFFC9A;
      line-height: 56rpx;
      margin-top: 6rpx;
    }
  }
  @keyframes rock {
    0% {
      transform: rotate(0deg);
    }
    25% {
      transform: rotate(4deg);
    }
    50% {
      transform: rotate(0deg);
    }
    75% {
      transform: rotate(-4deg);
    }
    100% {
      transform: rotate(0deg);
    }
  }
}
.qrcode-box {
  position: relative;
  width: 502rpx;
  height: 502rpx;
  background-repeat: no-repeat;
  background-size: cover;
  &.no-border {
    border: none;
    box-shadow: none;
  }

  &.animation::before {
      content: "";
      position: absolute;
      top: 26rpx;
      left: 36rpx;
      right: 36rpx;
      bottom: 26rpx;
      border: 20rpx solid #FFC653;
      border-radius: 40rpx;
      transition: all .3s;
      animation: clippath 2s infinite linear;
  }
  @keyframes clippath {
      0%, 100% { clip-path: inset(0 0 95% 0); }
      25% { clip-path: inset(0 95% 0 0); }
      50% { clip-path: inset(95% 0 0 0); }
      75% { clip-path: inset(0 0 0 95%); }
  }
}
.qrcode-content-box {
  position: absolute;
  top: 46rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  height: 100%;
  z-index: 1;
  text-align: center;
  .qrcode-img {
    width: 390rpx;
    height: 390rpx;
    border-radius: 40rpx;
    overflow: hidden;
  }
}
.press-tips-box {
  position: absolute;
  top: 108rpx;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
}
.qrcode-tips-text {
  font-weight: 600;
  font-size: 56rpx;
  color: #F2FFFB;
  line-height: 80rpx;
  text-shadow: 0px 4px 8px rgba(120,0,0,0.62);
  &:first-child {
    color: #FFFC9A;
  }
  &+.qrcode-tips-text {
    margin-top: 4rpx;
  }
}
.press-tips-gif {
  position: absolute;
  right: 8rpx;
  bottom: 14rpx;
  width: 250rpx;
  height: 200rpx;
}
.long-press-tips-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  .long-press-tips {
    font-weight: 400;
    font-size: 28rpx;
    color: #868C9C;
    line-height: 40rpx;
  }
}

.empty-data-box {
  width: 100%;
  min-height: 762rpx;
  padding-top: 194rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
}
.empty-data-img {
  width: 262rpx;
  height: 246rpx;
  margin-bottom: 30rpx;
}
.empty-data-text {
  font-size: 28rpx;
  color: #1D2029;
  line-height: 40rpx;
}
</style>