<template>
  <uni-popup class="my-uni-popup" ref="carouselPopups" type="center" :mask-click="false">
    <view class="carousel-popup">
      <swiper class="swiper" circular :indicator-dots="popupParams && popupParams.length>1" @change="swiperChange" :autoplay="true" :interval="3000" indicator-color="rgba(255,255,255,0.6)" indicator-active-color="#00B484">
        <swiper-item class="image" v-for="(item,index) in popupParams" :key="item.id">
          <image class="image" :src="file_ctx + item.imageUrl"></image>
          <view class="content-img-btn-box">
            <view 
              class="content-btn-item" 
              v-for="(managementItem,itemIndex) in item.managementItemList" 
              :key="managementItem.id" 
              :style="{width:item.managementItemList.length > 1 ? '279rpx' : '522rpx',height:item.managementItemList.length > 1 ? '166rpx' : '166rpx',marginLeft:itemIndex == 1 ? '12rpx' : ''}"
            >
              <image class="image" :src="managementItem.imageUrl"></image>
              <button class="bottom-btn" @click.prevent.stop="handleClickJump(managementItem)"></button>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>
    <view class="carousel-popup-img" @click="handleClickClose"><image class="my-image" :src="file_ctx + 'static/image/business/hulu-v2/icon-home-popup-error-fault.png'"></image></view>
  </uni-popup>
</template>

<script>
  import { mapState } from 'vuex'
  import env from '@/config/env'
  import uniPopup from '@/components/uni/uni-popup'
  export default {
    components: {
      uniPopup
    },
    props:{
      popupParams:{
        type: Array,
        default: () => []
      }
    },
    data(){
      return{
        $constant: this.$constant,
        file_ctx:this.file_ctx,
        current:0,
      }
    },
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId,
        curSelectUserInfo: state => state.curSelectUserInfo,
        isLogin: state => state.isLogin
      }),
    },
    watch: {
      current:{
        async handler(){
          if(this.popupParams?.length){
            await this.$api.drugBook.advertisementmanagementrecordInsert({accountId:this.accountId,advertisementId:this.popupParams[this.current].id,businessType: 1}) //businessType: 3,  //默认传3(关闭)
          }
        },
        immediate: true
      }
    },
    onLoad(){},
    mounted(){},
    methods:{
      async swiperChange(e){
        this.current = e.detail.current
      },
      
      async handleClickJump(item) {
        // 公共参数处理
        const getLoginParams = (extraParams = {}) => ({
          formPage: item.jumpUrl,
          formPageParams: encodeURIComponent(JSON.stringify({
            afterPopId: item?.afterPopId || '',
            ...extraParams
          }))
        });

        // 统一处理用户跳转极速咨询
        const checkUserAndNavigate = async (centerUserId) => {
          this.$uniPlugin.loading('加载中', true);
          try {
            const res = await this.$api.order.orderInitiatorUserCheck({
              userId: centerUserId,
              tenantId: this.$common.getKeyVal('user', 'curSelectStoreId', true) || env.tenantId
            });
            this.navigateTo(item, { ...res.data, type: 1 });
          } finally {
            this.$uniPlugin.hideLoading();
          }
        };

        // 统一跳转登录
        const navigateToLogin = (extraParams) => {
          this.$navto.push('Login', getLoginParams(extraParams));
        };

        try {
          // 统一广告记录插入
          this.$uniPlugin.loading('加载中', true);
          await this.$api.drugBook.advertisementmanagementitemrecordInsert({
            accountId: this.accountId,
            advertisementItemId: item?.id,
            businessType: 1
          });

          // #ifdef MP-WEIXIN
          getApp().globalData.sensors.track("PopupClick",
            {
              'page_name' : '首页',
              'popup_id' : 'prayPopup',
              'name' : '祈福弹窗',
              'click_type':'立即祈福'
            }
          )
          // #endif

          if (item.jumpUrl === 'Chat') {
            const { centerUserId = '' } = this.curSelectUserInfo || {};
            
            if (item.isLoginStatus === 1) {
              this.isLogin ? await checkUserAndNavigate(centerUserId) : navigateToLogin({ type: 1 });
            } else {
              if (!this.isLogin) navigateToLogin();
              else await checkUserAndNavigate(centerUserId);
            }
          } else {
            if (item.isLoginStatus === 1 && !this.isLogin) {
              navigateToLogin();
            } else {
              const navigateParams = { afterPopId: item?.afterPopId || '' };
              if (item?.param) navigateParams.param = item.param;
              this.navigateTo(item, navigateParams);
            }
          }
        } catch (error) {
          console.error('处理跳转出错:', error);
        } finally {
          this.$uniPlugin.hideLoading();
        }
      },

      // 页面跳转
      navigateTo(item,obj={}) {
        // 跳转类型:1-静态，2-跳内部页面，3-跳Web地址,4-跳小程序
        item.jumpType = typeof(item.jumpType) === 'number' ? JSON.stringify(item.jumpType) : item.jumpType
        switch (item.jumpType) {
          case '2':
            if (item.jumpUrl) {
              const params = obj?.param ? {afterPopId:item?.afterPopId || '',...this.filterParams(obj.param)} : {afterPopId:item?.afterPopId || ''}
              this.$navto.push(item?.jumpUrl,params)
            }
            break
          case '3':
            this.$navto.push('WebHtmlView', { src: item.jumpUrl, title: '' })
            break
          default:
        }
      },
      filterParams(params){
        let paramObject = {}
        let paramsSpt = params.split('&')
        // 遍历数组，分割每个参数成键和值，并存储到对象中
        paramsSpt.forEach(param => {
          let [key, value] = param.split('=')
          paramObject[key] = value
        });
        return paramObject
      },
      async handleClickClose(){
        await this.$api.drugBook.advertisementmanagementrecordInsert({accountId:this.accountId,advertisementId:this.popupParams[this.current].id,businessType: 3}) //businessType: 3,  //默认传3(关闭)
        this.$emit('handlePrayPopupClose','PopupClick',{'click_type':'取消弹窗'})
      }
    },
 }
</script>

<style lang='scss' scoped>
.image{
  width: 100%;
  height: 100%;
}
.carousel-popup-img{
  display: flex;
  justify-content: center;
  width: 100%;
  padding: 32rpx 0;
  .my-image{
    width: 40rpx;
    height: 40rpx;
  }
}
.swiper{
  height: 100%;
}
.carousel-popup{
  position: relative;
  width: 598rpx;
  height: 758rpx;
  .content-img-btn-box{
    display: flex;
    position: absolute;
    bottom: 10rpx;
    left: 50%;
    transform: translateX(-50%);
    .content-btn-item{
      position: relative;
      height: 166rpx;
      .bottom-btn{
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        border-radius: 45rpx;
        opacity: 0;
      }
    }
  }
}
</style>