<template>
  <uni-popup ref="dialog" type="center" :isMaskClick="false">
    <view class="dialog-content">
      <view class="dialog-title">微信手机号授权</view>
      <image
        :src="file_ctx + 'static/image/business/hulu-v2/icon-login-logo.png'"
        class="logo-img"
        mode="aspectFit"
      ></image>
      <text class="auth-desc">登记您的手机号码，以便提供购袋售后服务</text>
      <view class="dialog-floor">
        <view class="btn btn-cancel" @tap="cancel">取消</view>
        <button
          class="btn btn-confirm"
          type="default"
          open-type="getPhoneNumber"
          @getphonenumber="getphonenumber"
        >
          确认授权
        </button>
      </view>
    </view>
  </uni-popup>
</template>

<script>
import uniPopup from '@/components/uni/uni-popup/uni-popup.vue'

export default {
  components: {
    uniPopup
  },
  data() {
    return {
      file_ctx: this.file_ctx
    }
  },
  methods: {
    /**
     * @param {number} type 类型
     * 1、自定义授权窗口-曝光
     * 2、自定义授权窗口-确认授权
     * 3、自定义授权窗口-取消
     * 4、官方授权窗口-允许
     * 5、官方授权窗口-取消
     * 6、成功授权手机
     */
    addRecord(type) {
      this.$emit('add-record', type)
    },
    async getphonenumber(e) {
      this.addRecord(2)
      const that = this
      if (e.detail.errMsg === 'getPhoneNumber:ok') {
        this.addRecord(4)
        const params = {
          code: e.detail.code,
          cacheNumber: that.$common.getTokenUuid(), // 随机编码
        }
        this.getPhone(params)
      } else {
        this.addRecord(5)
      }
    },
    getPhone(params) {
      const that = this
      that.$ext.wechat.getPhone(params).then(async res => {
        await that.$api.user.centeruserPhoneInsert({ phone: res.data.phoneNumber })
        that.quickLogin(res.data.phoneNumber)
      }).catch(err => {
        that.$uniPlugin.toast(err.msg || '手机号授权出错了，请重试')
      })
    },
    async quickLogin(params) {
      const that = this
      if (!params) return that.$uniPlugin.toast('手机号授权出错了，请重试')
      that.$ext.wechat.quickLogin({ phone: params, cacheNumber: that.$common.getTokenUuid()}).then(res => {
        that.$ext.user.getInfoGroup(() => {
          that.$ext.user.bindWeixinAccount({})
          that.$ext.user.usertenantrecordBindFans()
          that.addRecord(6)
          that.$emit('confirm')
          that.close()
        })
      }).catch(err => {
        that.$uniPlugin.toast(err.msg || '手机号授权出错了，请重试')
      })
    },
    cancel() {
      this.addRecord(3)
      this.close()
    },
    close() {
      this.$refs.dialog.close()
    },
    open() {
      this.$refs.dialog.open()
    }
  }
}
</script>

<style scoped lang="scss">
.logo-img {
  width: 250upx;
  height: 73rpx;
}
.dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  border-radius: 16rpx;
  width: 550rpx;
}
.dialog-title {
  font-weight: 500;
  font-size: 32rpx;
  color: #1D2029;
  padding: 24rpx 0 32rpx;
}
.auth-desc {
  width: 100%;
  text-align: left;
  color: #6C6C6C;
  font-size: 26rpx;
  padding: 48rpx;
  box-sizing: border-box;
}
.dialog-floor {
  width: 100%;
  display: flex;
  height: 80rpx;
  border-top: 1px solid #f5f5f5;
}
.btn {
  height: 100%;
  flex: 1;
  font-size: #333;
  font-size: 30rpx;
  background-color: #fff;
  &+.btn {
    border-left: 1px solid #f5f5f5;
  }
  &::after {
    content: none;
    border: none;
  }
  &.btn-confirm {
    color: $topicC;
  }
}
</style>