
<template>
  <view class="title-checkbox clear-float" :class="{'bdt':defaultConfig.bdt,'bdb':defaultConfig.bdb && !child}">
    <view class="l-l" :style="{'color': defaultConfig.titleColor}">
      {{defaultConfig.label}}
      <text class="star" v-if="defaultConfig.required">*</text>
    </view>
    <view class="l-r">
      <checkbox-group class="checkbox" v-model="form.data.select">
        <view class="checkitem" @click="changeCheckBox(item.id)" :style="{'min-width':cwidth}" v-for="(item, index) in array" :key="index">
          <label class="li">
            <view class="box">
              <checkbox :disabled="disabled" :value="item.id" :checked="item.checked" />
            </view>
            <text :class="{'color-topicC': item.checked}">{{item.openStatus === 1 ? item.label : item.value}}</text>
          </label>
          <input
            v-if="item.checked && item.openStatus === 1"
            :disabled="disabled"
            v-model="item.value"
            @input="delivery('')"
          />
        </view>
      </checkbox-group>
    </view>
  </view>
</template>

<script>

export default {
  data() {
    return {
      form: {
        data: {
          select: []
        }
      },
      array: [],
      index: 0,
      defaultConfig: {
        bdt: false,
        bdb: true,
        titleColor: '#333',
        textColor: '#333',
        label: '单选',
        name: 'select',
        required: false,
        array: [],
        dicKey: ''
      },
      cwidth:"50%"
    }
  },
  watch: {
    cData: {
      handler(val) {
        this.watchDataMain(val)
      },
      deep: true
    },
    config: {
      handler(val) {
        this.copyConfig()
      },
      deep: true
    }
  },
  props: {
    child:{
      type:Boolean,
      default:false,
    },
    // 是否禁用disable
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 初始值传值，用于回显
    cData: {
      type: Array,
      required: false,
      default() {
        return []
      }
    },
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    }
  },
  computed: {

  },
  mounted() {
    this.getDic(() => {
      this.copyConfig()
      if (!this.$validate.isNull(this.cData)) {
        this.watchDataMain(this.cData)
      }else{
        for (let i = 0; i < this.array.length; i++) {
          if(this.array[i].openStatus == 1){
            if((this.array[i].label + '').length >= 8){
              this.cwidth = '100%';
            }
          }else{
            if((this.array[i].value + '').length >= 8){
              this.cwidth = '100%';
            }
          }
          // this.array[i].checked = false
          // if (isVal && val[0].id === this.array[i].id) {
          //   isVal = false
          //   this.array[i].checked = true
          //   this.array[i].value = val[0].value
          //   index = i
          // }
        }
      }
    })
  },
  methods: {
    changeCheckBox(id){
      var items = this.config.array;
      let values = [...this.form.data.select];
      let idx = values.findIndex(item => item === id);
      if(idx !== -1) {
        values.splice(idx,1)
      } else {
        values.push(id)
      }
      items.map(item => {
        if(values.includes(item.id)){
          this.$set(item,'checked',true)
        }else{
          this.$set(item,'checked',false)
        }
      })
      console.log('items',items)
    
      this.delivery(values)
    },
    /**
       * 初始化拷贝config对象
       */
    copyConfig() {
      const that = this
      const obj = that.config
      Object.keys(obj).forEach(function(key) {
        that.defaultConfig[key] = obj[key]
      })
    },
    /**
       * 监听Data主逻辑方法
       */
    watchDataMain(val) {
      // debugger
      console.log('val-----------', val)
      let idList = []
      // debugger
      for (let i = 0; i < val.length; i++) {
        idList.push(val[i].id)
      }
      this.form.data.select = idList
      let index = ''
      for (let i = 0; i < this.array.length; i++) {
        if(this.array[i].openStatus == 1){
          if((this.array[i].label + '').length >= 8){
            this.cwidth = '100%';
          }
        }else{
          if((this.array[i].value + '').length >= 8){
            this.cwidth = '100%';
          }
        }
        this.array[i].checked = false
        if (this.form.data.select.includes(this.array[i].id)) {
          this.array[i].checked = true
          const valItem = val.find(item => item.id === this.array[i].id)
          this.array[i].value = valItem.value
          index = i
        }
      }
      if (index !== '') {
        this.$set(this.array, index, this.array[index])
      }
      console.log('this.array-----------', this.array)
    },
    /**
       * 获取字典数据组
       */
    getDic(callBack) {
      const that = this
      const ar = that.config.array || []
      // debugger
      if (ar.length > 0) {
        that.array = ar
        for (const i in that.array) {
          that.array[i].checked = false
        }
        callBack()
        return
      }
      const params = {
        dictType: that.config.dicKey
      }
      that.$ext.dic.getDicInfo(params, (res) => {
        that.array = res
        for (const i in that.array) {
          that.array[i].checked = false
        }
        callBack()
      }, () => {
        this.array = []
        callBack()
      })
    },
    /**
       * picker触发选中事件
       * @param e
       */
    returnFn(e) {
      // debugger
      const that = this
      if (that.disabled) return

      var items = this.config.array,
        values = e.detail.value;
      // debugger
      for (let i = 0, lenI = items.length; i < lenI; ++i) {
        const item = items[i]
        if(values.includes(item.id)){
          this.$set(item,'checked',true)
        }else{
          this.$set(item,'checked',false)
        }
      }
      this.delivery(values)
    },
    delivery (values) {
      const that = this
      let items = this.config.array
      let resultList = []
      for (let i = 0, lenI = items.length; i < lenI; ++i) {
        const item = items[i]
        if (item.checked){
          resultList.push({
            id:item.id,
            value:item.value
          })
        }

      }
      if(values) {
        that.form.data.select = values
      }

      that.$emit('updateForm', { key: '' + that.config.name, value: resultList ,type:'checkbox'})
    }
  }
}
</script>

<style lang="scss" scoped>
  .color-topicC{
    color: $topicC !important;
  }
  .title-checkbox{
    background-color:#ffffff;
    .l-l{
      line-height: 88upx;
      color: #333333;
      font-weight: 600;
      font-size:30upx;
    }
    .l-r{
        // display: flex;
        // flex-wrap: wrap;
        padding-bottom: 20upx;
        .checkbox{
          display: flex;
          flex-wrap: wrap;
          width: 100%;
        }
        .checkitem{
          flex: 1;
          min-width: 50%;
        }
      //display: inline-block;
      .li{
        // min-width: 50%;
        // flex: 1;
        display: flex;
        position: relative;
        line-height: 88upx;
        vertical-align: middle;
        .box{
          padding-top: 16upx;
        }
        text{
          display: inline-block;
          font-size: 32upx;
          vertical-align: middle;
          // color: #999999;
          color: #000;
              margin-left: 10upx;
        }
      }
      .li:last-child{
        margin-right: 0;
      }
      input {
        height: 80upx;
        line-height: 80upx;
        color: #333;
        font-size: 32upx;
        text-align: left;
        display: inline-block;
        vertical-align: middle;
        width: calc(100% - 64upx);
        border: 2upx solid #ede9e9;
        border-radius: 10upx;
      }
    }
  }
  .star{
    color: #F85E4C;
    padding-top: 6upx;
    font-size: 32upx;
    display: inline-block;
        margin-left: 10rpx;
  }

</style>
