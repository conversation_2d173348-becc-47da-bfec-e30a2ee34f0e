import request from '@/common/util/request'
import env from '@/config/env'

export default {
  //获取灯火投放计划 manage/api/v1/dhplan/get/plan?code=1&deviceId=1616&businessInfoId=1
  dhplanGetPlan(data) {
    return request.get(`${env.ctx}manage/api/v1/dhplan/get/plan`, data);
  },
  //添加访问日志  manage/api/v1/dhcallbacklog/addaccesslog?userId=2088902550119930&type=1
  dhcallbacklogAddaccesslog(data) {
    return request.postForm(`${env.ctx}manage/api/v1/dhcallbacklog/addaccesslog`, data);
  },
  //获取回调日志
  dhcallbacklogGetcallbacklog(data) {
    return request.postForm(`${env.ctx}manage/api/v1/dhcallbacklog/getcallbacklog`, data);
  },
  //获取出袋结果
  getPacketRecordReceiveStatus(data) {
    return request.get(`${env.ctx}manage/api/wx/getPacketRecordReceiveStatus`, data);
  },
  //免费领袋
  dhcallbacklogFreePacket(data) {
    return request.postForm(`${env.ctx}manage/api/v1/dhcallbacklog/free/packet`, data);
  },
  // banner广告位-获取素材地址
  weboriginalityGetWebOriginality(data) {
    return request.get(`${env.ctx}adservice/api/v1/weboriginality/get/web/originality`, data);
  },
  // banner广告位-点击
  addWeboriginalityClickNumber(data) {
    return request.get(`${env.ctx}adservice/api/v1/weboriginality/add/weboriginality/click/number`, data);
  },
  // banner广告位-曝光
  addWeboriginalityExposureNumber(data) {
    return request.get(`${env.ctx}adservice/api/v1/weboriginality/add/weboriginality/exposure/number`, data);
  },
  // 支付宝支付
  payAlipay(data) {
    return request.postForm(`${env.ctx}manage/api/wx/pay/alipay`, data);
  },
  //获取灯火投放计划
  dhplanGetAfterPlan(data) {
    return request.postJson(`${env.ctx}manage/api/v1/dhplan/get/after/plan`, data);
  },
}
