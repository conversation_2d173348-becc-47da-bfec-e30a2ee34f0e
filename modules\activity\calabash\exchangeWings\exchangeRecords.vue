<template>
  <view class="pages">
    <!-- 切换tab -->
    <view class="headerTab">
      <view class="tabBox" :class="{selectBox:auditStatus === item.auditStatus}" v-for="(item,index) in tabMap" @click="changeTab(item.auditStatus)">
        {{item.title}}
        <image class="borderBottom" v-if="auditStatus === item.auditStatus" :src="borderBottom" mode="aspectFill"></image>
      </view>
    </view>
    <scroll-view class="exchangeGoodsMap" scroll-y="true" @scrolltolower="lower">
      <view class="noData" v-if="!exchangeGoodsMap.length">
        <image class="noDataIcon" :src="iconCommentEmpty" mode="aspectFill"></image>
        <view>暂无数据</view>
      </view>
      <view class="exchangeGoods" v-for="(item,index) in exchangeGoodsMap" :key="index">
        <view class="exchangeGoodsHead">
          <view class="headL" v-if="item.auditStatus == 1">待审核</view>
          <view class="headL" v-if="item.auditStatus == 2">{{['','待发货','已发货'][item.deliveryStatus]}}</view>
          <view class="headL" v-if="item.auditStatus == 3">审核不通过</view>
          <view class="headR">提交时间：{{formatTimestamp(item.createTime)}}</view>
        </view>
        <view class="exchangeGoodsBody">
          <image class="bodyIcon" :src="file_ctx + item.productImage" mode="aspectFill"></image>
          <view class="bodyContent">
            <view class="bodyContentTitle">{{item.productName}}</view>
            <view class="goodsNums"><text>{{item.point}}</text><text class="goodsSinge">福币</text></view>
          </view>
        </view>
        <!-- 快递信息 -->
        <view class="exchangeGoodsBottom" v-if="item.auditStatus == 2 && item.deliveryStatus == 2">快递公司：{{item.deliveryCompany}}</view>
        <view class="exchangeGoodsBottom" v-if="item.auditStatus == 2 && item.deliveryStatus == 2">
          快递单号：{{item.deliveryNumber}}
          <view class="copyBtn" @click="copy(item.deliveryNumber)">复制</view>
        </view>
        <view class="deliveryImg" v-if="item.deliveryImg" @click="previewImage(file_ctx + item.deliveryImg)">
          <view class="deliveryImgTitle">查看物流截图</view>
          <image class="deliveryImgSign" :src="iconUserNext2" mode="aspectFill"></image>
        </view>
        <!-- 不通过原因 -->
        <view class="reason" v-if="item.auditStatus == 3">
          不通过原因：{{item.remark}}
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
  import calabashApis from "@/modules/common/api/calabash.js"
  import common from '@/common/util/main'
  export default{
    data(){
      return {
        auditStatus:2,
        file_ctx: this.file_ctx,
        tabMap:[
          {title:'已兑换',auditStatus:2},
          {title:'待审核',auditStatus:1},
          {title:'审核不通过',auditStatus:3},
        ],
        borderBottom: this.$static_ctx + "image/business/hulu-v2/border-bottom.png",
        iconCommentEmpty: this.$static_ctx + "image/business/hulu-v2/icon-comment-empty.png",
        iconUserNext2: this.$static_ctx + "image/business/hulu-v2/icon-user-next2.png",
        current:0,
        loadEnd:false,
        exchangeGoodsMap:[]
      }
    },
    mounted() {
      this.getPointgiftexchangeQueryPage()
    },
    methods:{
      copy(str){
        uni.setClipboardData({data: str});
      },
      previewImage(iamge){
        uni.previewImage({urls:[iamge]})
      },
      formatTimestamp(timestamp) {
          const date = new Date(timestamp);
          const year = date.getFullYear();
          const month = ('0' + (date.getMonth() + 1)).slice(-2);
          const day = ('0' + date.getDate()).slice(-2);
          return `${year}年${month}月${day}日`;
      },
      changeTab(Status){
        this.auditStatus = Status;
        this.current = 0;
        this.exchangeGoodsMap.length = 0;
        this.loadEnd = false;
        this.getPointgiftexchangeQueryPage()
      },
      lower(){
        this.getPointgiftexchangeQueryPage()
      },
      async getPointgiftexchangeQueryPage(){
        if(this.loadEnd) return
        this.current+=1
        const accountId = common.getKeyVal('user', 'accountId', true);
        // 获取商品列表
        let {data:{records,total}} = await calabashApis.pointgiftexchangeQueryPage({
          current:this.current,
          size:10,
          condition:{auditStatus:this.auditStatus,accountId}
        })
        if(total <= this.exchangeGoodsMap.length){
           this.loadEnd = true
           return
        }
        this.exchangeGoodsMap.push(...records)
      }
    }
  }
</script>

<style lang="scss">
  .pages{
    width: 100vw;
    height: 100vh;
    background: #F4F6FA;
    overflow: scroll;
  }
  .exchangeGoodsMap{
    width: 100vw;
    height: calc(100vh - 90rpx - 20rpx);
    margin-top: 20rpx;
    padding: 0 32rpx 20rpx;
    box-sizing: border-box;
    .noData{
      width: 686rpx;
      background: #FFFFFF;
      border-radius: 16rpx;
      margin-bottom: 20rpx;
      box-sizing: border-box;
      height: 400rpx;
      text-align: center;
      margin-top: 50rpx;
      .noDataIcon{
        width: 200rpx;
        height: 200rpx;
        margin-bottom: 50rpx;
      }
    }
    .exchangeGoods{
      width: 686rpx;
      padding: 24rpx;
      background: #FFFFFF;
      border-radius: 16rpx;
      margin-bottom: 20rpx;
      box-sizing: border-box;
      .exchangeGoodsHead{
        display: flex;
        justify-content: space-between;
        .headL{
            font-weight: 600;
            font-size: 28rpx;
            color: #04644B;
        }
        .headR{
          font-weight: 400;
          font-size: 24rpx;
          color: #4E5569;
        }
      }
      .exchangeGoodsBody{
        display: flex;
        padding-top: 16rpx;
        .bodyIcon{
          width: 144rpx;
          height: 144rpx;
          margin-right: 20rpx;
          border-radius: 10rpx;
        }
        .bodyContent{
          width: calc(100% - 144rpx);
          padding-top: 20rpx;
          .bodyContentTitle{
            font-weight: 500;
            font-size: 28rpx;
            color: #1D2029;
            word-wrap: break-word;
            overflow: hidden;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            text-overflow: ellipsis;
          }
          .goodsNums{
            font-weight: 600;
            font-size: 40rpx !important;
            color: #FF5500;
            .goodsSinge{
              font-size: 20rpx;
              color: #FF4100;
            }
          }
        }
      }
      .exchangeGoodsBottom{
        font-weight: 400;
        font-size: 24rpx;
        color: #4E5569;
        display: flex;
        align-items: center;
        &:nth-of-type(1){
          margin-top: 16rpx;
        }
        .copyBtn{
          width: 72rpx;
          height: 40rpx;
          background: #FFFFFF;
          border-radius: 200rpx;
          border: 1rpx solid #D9DBE0;
          font-weight: 400;
          font-size: 20rpx;
          color: #1D2029;
          text-align: center;
          line-height: 40rpx;
          margin-left: 8rpx;
        }
      }
      .reason{
        font-weight: 400;
        font-size: 24rpx;
        color: #4E5569;
        margin-top: 40rpx;
      }
      .deliveryImg{
        margin-top: 16rpx;
        width: 100%;
        height: 84rpx;
        border-top: 1rpx solid #EAEBF0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .deliveryImgTitle{
          font-weight: 400;
          font-size: 26rpx;
          color: #4E5569;
        }
        .deliveryImgSign{
          width: 30rpx;
          height: 30rpx;
        }
      }
    }
  }
  .headerTab{
    width: 750rpx;
    height: 90rpx;
    background: #FFFFFF;
    display: flex;
    .tabBox{
      width: 33%;
      height: 90rpx;
      line-height: 90rpx;
      text-align: center;
      font-weight: 400;
      font-size: 30rpx;
      color: #4E5569;
      position: relative;
      .borderBottom{
        width: 38rpx;
        height: 10rpx;
        position: absolute;
        left: 50%;
        bottom: 12rpx;
        transform: translateX(-50%);
      }
    }
    .selectBox{
      font-weight: 600;
      color: #00B484;
    }
  }
</style>
