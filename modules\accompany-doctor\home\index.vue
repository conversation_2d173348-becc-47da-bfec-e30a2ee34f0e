<template>
  <view class="pageIndex" :class="skinColor">
    <!-- 首页 -->
    <view class="noShow" :class="{isShow:navCurrent == 0}">
        <homeIndex @changePage="changePage" ref="homeIndex" :skinColor="skinColor" v-if="navCurrent !== null" />
    </view>

    <!-- 服务页面 -->
    <view class="noShow" :class="{isShow:navCurrent == 1}">
      <ServiceIndex ref="ServiceIndex" :skinColor="skinColor" />
    </view>

    <!-- 订单页面 -->
    <view class="noShow" :class="{isShow:navCurrent == 2}">
      <OrderIndex ref="OrderIndex" :skinColor="skinColor" />
    </view>

    <!-- 我的页面 -->
    <view class="noShow" :class="{isShow:navCurrent == 3}">
      <MyIndex ref="MyIndex" :skinColor="skinColor" />
    </view>
    <!-- 底部部分 -->
    <view class="accompany-bottom" v-if="navCurrent !== null">
      <view class="bottom-item" v-for="(item,index) in navList" :key="index" @tap="handletapJump(index)">
        <view class="bottom-item-img" v-if="navCurrent == index"><image class="img" :src="file_ctx + item.activeUrl"></image></view>
        <view class="bottom-item-img" v-else><image class="img" :src="file_ctx + item.url"></image></view>
        <view :class="navCurrent == index ? 'bottom-item-name active' : 'bottom-item-name'">{{item.name}}</view>
      </view>
    </view>
  </view>
</template>

<script>
  import serverOptions from '@/config/env/options'
  import { mapState } from 'vuex'
  import homeIndex from './components/accompany-home/index'
  import ServiceIndex from './components/service/index'
  import OrderIndex from './components/order/index'
  import MyIndex from './components/my/index'
  import { getQueryObject } from '@/utils/index'
  export default {
    components: {
      homeIndex,
      ServiceIndex,
      OrderIndex,
      MyIndex
    },
    data(){
      return {
        file_ctx: this.file_ctx,
        statusBarHeight: 0,
        navList:[
          {name:'首页',url:'static/image/business/accompany-doctor/icon-home.png',activeUrl:'static/image/business/accompany-doctor/icon-home-active.png'},
          {name:'服务',url:'static/image/business/accompany-doctor/icon-accompany-bottom-service.png',activeUrl:'static/image/business/accompany-doctor/icon-accompany-bottom-service-active.png'},
          {name:'订单',routerName:'accompanyOrder',url:'static/image/business/accompany-doctor/icon-accompany-bottom-order.png',activeUrl:'static/image/business/accompany-doctor/icon-accompany-bottom-order-active.png'},
          {name:'我的',url:'static/image/business/accompany-doctor/icon-my.png',activeUrl:'static/image/business/accompany-doctor/icon-my-active.png'}
        ],
        refList:['homeIndex','ServiceIndex','OrderIndex','MyIndex'],
        navCurrent:null,
        // 换肤颜色列表 蓝色
        skinColorList:['greenSkin','blueSkin'],
        // 当前换肤颜色
        skinColor:''
      }
    },
    mounted() {

    },
    onShareAppMessage(){
      let shareOptions = this.mixinsShareOptions()
      shareOptions.path += `&navCurrent=${this.navCurrent}`
      shareOptions.path += `&providerId=${serverOptions.providerId}`
      console.log('shareOptions',shareOptions);
      return {
        title:serverOptions.title,
        ...shareOptions
      }
    },
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId
      }),
    },
    async onLoad(res){
      // #ifdef MP-ALIPAY
      my.setNavigationBar({
        frontColor: '#000000',
        backgroundColor: '#ffffff'
      })
      // #endif
      if(res){
      console.log('res',res);
      let params = decodeURIComponent(decodeURIComponent(decodeURIComponent(res.scene)))
      console.log('params',params);
      
      let sceneObj = getQueryObject(params)
      console.log('sceneObj',sceneObj)
      const codeUserInfo = this.$common.getKeyVal('user', 'codeUserInfo', true);

      // 处理扫码进入的参数
      if (sceneObj.id) {
        try {
          // 调用绑定分销员接口
          await this.$api.distribution.accompanydistributorcustomerInsert({
            distributorId: sceneObj.id,  // 分销员id
            userId: codeUserInfo.id
          });

          // uni.showToast({
          //   title: '绑定成功',
          //   icon: 'success',
          //   duration: 2000
          // });
        } catch (error) {
          console.error('绑定分销员失败:', error);
          uni.showToast({
            title: '绑定失败',
            icon: 'none',
            duration: 2000
          });
          }
        }
      }
      let cloudProviderId = res.providerId;
      console.log('cloudProviderId',cloudProviderId);
      let navCurrent = 0;
      if(typeof res.query === 'string'){
        let queryOptions = JSON.parse(decodeURIComponent(decodeURIComponent(decodeURIComponent(res.query))));
        navCurrent = queryOptions.navCurrent === undefined ? 0 : +queryOptions.navCurrent;
        cloudProviderId = queryOptions.providerId;
      }
      if(res.navCurrent){
        navCurrent = +res.navCurrent;
      }
      if(res.scene || res.gs){
        let params = decodeURIComponent(decodeURIComponent(decodeURIComponent(res.scene)))
        let sceneObj = getQueryObject(params)
        if(res.gs) sceneObj = res
        console.log('sceneObj',sceneObj)
        sceneObj.navCurrent && (navCurrent = +sceneObj.navCurrent);
        (sceneObj.providerId || sceneObj.pd) && (cloudProviderId = sceneObj.providerId || sceneObj.pd);
        // 判断路由参数中是否含有渠道链参数 如果有则去渠道链模块里查找
        if(sceneObj.gs || sceneObj.gbScene){
          let scCode = sceneObj.gs || sceneObj.gbScene
          let scCodeData = (await this.$api.common.minichannellinkQueryOne({code:scCode})).data
           // 如果当前路由参数存在providerId则修改当前的云陪诊参数
          if(scCodeData && scCodeData.customParameters && getQueryObject(scCodeData.customParameters).providerId){
            cloudProviderId = getQueryObject(scCodeData.customParameters).providerId;
          }
        }
      }
      cloudProviderId = cloudProviderId || serverOptions.providerId;
      this.navCurrent = null;
      let currentOptions = await serverOptions.loadCloudProvider(cloudProviderId);
      console.log('currentOptionscloudProviderId',currentOptions);
      if(currentOptions.decoration === "") currentOptions.decoration = 0;
      // 设置换肤颜色
      this.skinColor = this.skinColorList[currentOptions.decoration];
      if(this.skinColor !== 'greenSkin'){
        // 设置底部导航
        this.navList.map(e=>{
          // 读取图片路径的图片名字
          let original = e.activeUrl.split('/').slice(0,-1).join('/');
          let imgName = e.activeUrl.split('/').pop();
          imgName = `${this.skinColor}-${imgName}`;
          this.$set(e,'activeUrl',`${original}/${imgName}`)
        })
      }
      this.navCurrent = navCurrent;
    },

    methods:{
      back(){
        uni.navigateBack()
      },
      changePage(pageNum){
        this.navCurrent = pageNum;
      },
      handletapJump(index){
        this.navCurrent = index
      },

    },
  }
</script>

<style lang="scss" scoped>
@import './components/style/blueSkin.scss';
  .noShow{
    display: none;
  }
  .isShow{
    display: block;
  }
  .img{
    width: 100%;
    height: 100%;
  }
  .pageIndex{
    position: fixed;
    width: 100vw;
    height: 100vh;
    background: #F4F6FA;
    // padding: 20rpx 0;
    box-sizing: border-box;
    overflow-y: auto;
    overflow-x: hidden;
  }



  ::v-deep .uni-data-tree-dialog{
    bottom: 166rpx !important;
  }
  ::v-deep .main, ::v-deep .service, ::v-deep .page, ::v-deep .my{
    height: calc(100vh - 166rpx) !important;
    box-sizing: border-box !important;
    position: relative;
  }
  ::v-deep .accompany-content{
    height: calc(100% - 288rpx) !important;
    overflow-y: scroll;
    overflow-x: hidden;
  }
  .bottom-stub{
    width: 100%;
    height: 166rpx;
  }
  .accompany-placeholder{
    width: 100%;
    height: 166rpx;
  }
  .accompany-bottom{
    position: fixed;
    width: 100%;
    bottom: 0;
    left:0;
    display: flex;
    z-index: 999;
    // align-items: center;
    justify-content: space-around;
    // height: 98rpx;
    height: 166rpx;
    background-color: #fff;
    .bottom-item{
      display: flex;
      flex-direction: column;
      align-items: center;
      // justify-content: center;
      padding-top: 8rpx;
      .bottom-item-img{
        width: 56rpx;
        height: 56rpx;
        margin-bottom: 4rpx;
      }
      .bottom-item-name{
        font-size: 20rpx;
        color: #868C9C;
      }
      .active{
        color: #00B484;
      }
    }
  }

</style>
