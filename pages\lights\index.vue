<template>
  <page>
    <view slot="content" class="content" :style="{ maxhHeight: `${scrollHeight}px` }">
      <view class="content-bg"></view>
      <!--灯火接入代码-->
      <view v-if="isShowAd && activityType == 1">
        <!--@xxx="testAd"-->
        <ad-lights onAdInstance="onAdInstance" @xxx="testAd"></ad-lights>
      </view>
      <private-pouch-component-pop
        v-else-if="isShowAd && activityType == 2"
        :visible="pouchVisible"
        onSuccess="onPouchSuccess"
        onError="onPouchError"
        onClose="onPouchClose"
        :rtaExtMap="pouchRtaExtMap"
      ></private-pouch-component-pop>
      <view class="content-cell">
        <view :id="ADSID" data-v-fae5bece="" class="banner image">
          <!-- 广告素材 -->
          <template v-if="originalityList && originalityList.length !== 0 && originalityList[0].tacticsId">
            <view v-for="(item, index) in originalityList" :key="index" :tacticsId="item.tacticsId" :id="'tacticsId-' + item.tacticsId" @click="adsClick(item.tacticsId, item)">
              <template v-if="item.materialSpecificationType === 1">
                <text class="">{{ item.content }}</text>
              </template>
              <template v-if="item.materialSpecificationType === 2">
                <image mode="widthFix" :src="isDomainUrl(item.filePath)" class="banner-img" />
              </template>
            </view>
          </template>
          <!-- 支付宝广告 -->
          <view v-else-if="deviceInfo && deviceInfo.gdtAdSwitch === 1 && !adError" class="banner-img">
            <ad
              unit-id="ad_tiny_2021003131638860_202506172202052543"
              @load="onAdRenderSuccess"
              @error="onAdRenderFail"
              style="width: 100%; height: 100%;"
            />
          </view>
          <!-- 默认banner图 -->
          <image v-else mode="widthFix" :src="imgList.devicePoster" alt="" class="banner-img">
        </view>
        <!--<view id="ADS6"></view>-->
        <view class="content-center">
          <image
            v-if="!showError"
            mode="aspectFill"
            :src="imgList.whiteTop"
            class="whiteTop"
          />

          <!-- 故障图文 -->
          <!-- <transition name="van-fade"> -->
          <template v-if="showError && hidenChooseGender">
            <image
              mode="aspectFill"
              :src="imgList.repair"
              alt=""
              class="repair"
            >
          </template>

          <!-- 二维码区域 -->
          <!-- <transition name="van-fade"> -->
          <view v-if="hidenChooseGender">
            <view class="content-code centerC">
              <view class="tips" v-if="showError">
                {{ tips }}
              </view>
              <view class="button-list" v-if="!showError">
                <template v-if="[1,5].includes(packetType)">
                  <button
                    v-if="activityType == 2"
                    class="button equities-btn default-text"
                    @click="openShow"
                  >
                    <text>免费取袋 再送2元支付红包</text>
                  </button>
                  <button
                    v-else
                    class="button equities-btn"
                    @click="openShow"
                  >
                    <view class="tag-box free-box" :style="'background-image: url(' + file_ctx + 'h5/home/<USER>/icon-danger-tag.png);'">权益</view>
                    <text>领取权益</text>
                  </button>
                </template>
                <button
                  v-if="showFreeButton"
                  class="button"
                  type="primary"
                  @click="freeBtnClick"
                >
                  <view class="tag-box free-box" :style="'background-image: url(' + file_ctx + 'h5/home/<USER>/icon-free-tag.png);'">免费</view>
                  <text>免费领袋</text>
                </button>
                <button
                  v-if="userFreeCount <= 0 && paySwitch === 1"
                  class="button pay-btn"
                  type="primary"
                  @click="payBtnClick"
                >
                  <view class="tag-box pay-box" :style="'background-image: url(' + file_ctx + 'h5/home/<USER>/icon-wathet-tag.png);'">付费</view>
                  <text>{{ payAmount }}元购买</text>
                </button>
              </view>

            </view>
          </view>
          <!-- </transition> -->

        </view>
        <!-- 底部 -->
        <view class="content-bottom">
          <image class="logo" mode="aspectFit" :src="imgList.logo"></image>
          <view class="tips-text" @click="handleTel('************')">
            <text>遇到出袋问题请拨打客服电话：</text>
            <text>************</text>
          </view>
        </view>
      </view>
    </view>
  </page>

</template>

<script>
import { getQueryStr, checkHttp ,sitecheckInit, param2Obj, isDomainUrl } from '@/utils/index'
import env from '@/config/env'
import common from '@/common/util/main'
const positionSource = 'ads:position:source'
let aliPayAd;
export default {
  components: {
  },
  name: 'Home',
  data() {
    return {
      file_ctx: env.file_ctx,
      isDomainUrl,
      publicOpinionLevel:'',
      organizationSubType:'',
      deviceLocation:'',

      showFreeButton:false,
      height: '0px',
      qrCodeUrl: null,
      deviceId: null,
      showReload: false,
      scanH5QrCodeText: null,
      reloadText: '请重新扫码进入',
      accountId: null,
      appId: null,
      hidenChooseGender: true,
      scanH5QrCodeData: {},
      imgList: {
        bg: env.file_ctx + 'h5/home/<USER>/home-bg.png',
        white: env.file_ctx + 'h5/home/<USER>',
        code: env.file_ctx + 'h5/home/<USER>',
        logo: env.file_ctx + 'h5/home/<USER>/icon-logo.png',
        xiaobao: env.file_ctx + 'h5/home/<USER>',
        repair: env.file_ctx + 'h5/home/<USER>/device-repair.png',
        banner: env.file_ctx + 'h5/home/<USER>',
        close: env.file_ctx + 'h5/home/<USER>',
        guide: env.file_ctx + 'h5/home/<USER>',
        hand: env.file_ctx + 'h5/home/<USER>/hand.png',
        tips: env.file_ctx + 'h5/home/<USER>',
        whiteTop: env.file_ctx + 'h5/home/<USER>/device-bag.png',
        devicePoster: env.file_ctx + 'h5/home/<USER>/home-banner.png'
      },
      showError: false,
      showHand: false,
      scrollHeight: uni.getSystemInfoSync().screenHeight,
      tips: '',
      spaceCode:"50_2023072425000057897",
      timer: null,
      userId:'',//支付宝用户ID,
      userFreeCount:'',//用户当前免费次数
      planStatus:'',//1新建，2启动，3停止
      packetType: 0, //场景出袋类型 type 1有广告 2无广告 4无广告领袋 5放弃广告领袋
      isAliPayAdOnLoad: false, // 支付宝广告插件初始化是否完毕
      isShowAd: false,
      ADSID: 'ADS17133346140218349',
      originalityList: [],
      query: {}, // 页面参数
      pageScrollTimer: null, // 页面滚动定时器
      paySwitch: 2, // 付费按钮开关 1-开 2-关
      payAmount: '', // 支付金额 单位元
      pouchVisible: false, // 小荷包插件弹窗显示
      pouchRtaExtMap: {}, // 小荷包插件拓展参数
      activityType: 1, // 1-赠险 2-小荷包
      afterId: '', // 小荷包id 赠险插件异常时用这个id来初始化小荷包插件
      deviceInfo: null,
      adError: false
    }
  },
  onShow() {
    const _this = this
    this.$nextTick(() => {
      // this.createJsapiSignature()
    })
  },
  onLoad(params) {
    // 灯火
    this.isShowAd = true
    this.$scope.onAdInstance = this.onAdInstance.bind(this)
    this.$scope.onAdLoad = this.onAdLoad.bind(this)

    // 小荷包
    this.$scope.onPouchSuccess = this.onPouchSuccess.bind(this)
		this.$scope.onPouchError = this.onPouchError.bind(this)
		this.$scope.onPouchClose = this.onPouchClose.bind(this)

    let launchParams = common.getKeyVal('system', 'launchParams')
    const that = this
    let deviceId = ''
    let businessInfoId = ''
    let type = 1
    if (!this.$validate.isNull(launchParams)) {
      deviceId = launchParams.deviceId
      businessInfoId = launchParams.businessInfoId || ''
      type = launchParams.type || 1
    }
    this.query = this.$Route.query
    if (!this.$validate.isNull(this.$Route.query.deviceId)) {
      deviceId = this.$Route.query.deviceId
    }
    if (!this.$validate.isNull(this.$Route.query.businessInfoId)) {
      businessInfoId = this.$Route.query.businessInfoId
    }

    that.$uniPlugin.loading()

    that.deviceId = deviceId

    if (!deviceId) {
      that.showError = true
      that.tips = '设备不存在，请重新扫码！'
      that.$uniPlugin.hideLoading()
      return
    }

    this.getDeviceInfo()

    // that.$nextTick(() => {
      //支付宝用户授权
      my.getAuthCode({
        scopes: 'auth_base',
        success: async (authInfo) => {
          console.log(authInfo.authCode)
          that.$api.lights.dhplanGetPlan({code: authInfo.authCode, deviceId, businessInfoId}).then(res=>{
            // debugger
            that.$uniPlugin.hideLoading()
            res = res.data
            that.userId = res.userId
            that.userFreeCount = +res.freeCount - +res.userFreeCount
            that.planStatus = res.planStatus
            that.businessInfoId = res.businessInfoId

            that.afterId = res.afterId // 小荷包id 灯火插件异常时使用这个id来初始化小荷包
            that.activityType = res.activityType || 1 // 1-赠险 2-小荷包
            that.publicOpinionLevel = res.publicOpinionLevel || 'low_risk'
            that.organizationSubType = res.organizationSubType || 'general_hospital'
            that.deviceLocation = res.deviceLocation || 'inpatient_department'

            that.paySwitch = res.paySwitch
            that.payAmount = res.payAmount

            this.getAds()
            // 判断投放计划
            //投放计划 启动
            if (that.planStatus==='2') {
              if (this.isAliPayAdOnLoad){
                that.packetType = 1
                console.log(that.planStatus==='2', aliPayAd)
                // 添加访问日志
                // type 1有广告 2无广告 4无广告领袋 5放弃广告领袋
                that.$api.lights.dhcallbacklogAddaccesslog({userId: that.userId, type: 1, activityType: that.activityType})
                that.checkUserFreePacketBtn()
                if (!this.showError && this.activityType != 2) {
                  that.openShow()
                }
                // that.onAdLoad(()=>{
                // })
              }else {
                this.packetType = 4
              }
            } else if (that.planStatus==='3'){
              //投放计划 停止
              this.packetType = 4
            }
            that.checkUserFreePacketBtn()
          }).catch(err => {
            that.$uniPlugin.hideLoading()
            this.getAds()
          })
        },
        fail: () => {
          this.getAds()
          that.$uniPlugin.hideLoading()
          that.$uniPlugin.toast('用户授权失败')
        }
      })
    // })


  },
  beforeDestroy() {
    this.stopSetInterval()
  },
  onPageScroll() {
    // 滚动
    clearTimeout(this.pageScrollTimer) // 每次滚动前 清除一次
    this.pageScrollTimer = setTimeout(() => {
      // 停止滚动时上报
      this.checkDivShow()
    }, 200)
  },
  methods: {
    onAdRenderFail(e) {
      console.log('广告加载异常------', e)
      this.adError = true
    },
    onAdRenderSuccess(e) {
      console.log('广告加载成功------', e)
      this.adError = false
    },
    /**
     * 获取设备信息
     */
    async getDeviceInfo() {
      const that = this
      if(!that.deviceId || that.deviceId === 'null') return
      const res = await that.$api.packet.getDeviceInfoByDeviceId({ deviceId: that.deviceId }).catch((err) => { console.log('devideInfo err---------', err) })
      that.deviceInfo = res.data
    },
    getDhplanAfterPlan() {
      const that = this
      this.$api.lights.dhplanGetAfterPlan({
        userId: this.userId,
        deviceId: this.deviceId,
        dhPlanId: this.afterId,
        businessInfoId: this.businessInfoId
      }).then(res => {
        // debugger
        that.$uniPlugin.hideLoading()
        res = res.data
        that.userId = res.userId
        // that.userFreeCount = +res.freeCount - +res.userFreeCount
        that.planStatus = res.planStatus
        that.businessInfoId = res.businessInfoId
        that.afterId = res.afterId // 小荷包id 灯火插件异常时使用这个id来初始化小荷包
        that.activityType = res.activityType || 1 // 1-赠险 2-小荷包
        that.publicOpinionLevel = res.publicOpinionLevel || 'low_risk'
        that.organizationSubType = res.organizationSubType || 'general_hospital'
        that.deviceLocation = res.deviceLocation || 'inpatient_department'
        that.paySwitch = res.paySwitch
        that.payAmount = res.payAmount

        // 判断投放计划
        //投放计划 启动
        if (that.planStatus==='2') {
          if (this.isAliPayAdOnLoad){
            that.packetType = 1
            // 添加访问日志
            // type 1有广告 2无广告 4无广告领袋 5放弃广告领袋
            that.$api.lights.dhcallbacklogAddaccesslog({userId: that.userId, type: 1, activityType: that.activityType})
            that.checkUserFreePacketBtn()
            if (!this.showError && this.activityType != 2) {
              that.openShow()
            }
            // that.onAdLoad(()=>{
            // })
          }else {
            this.packetType = 4
          }
        } else if (that.planStatus==='3'){
          //投放计划 停止
          this.packetType = 4
        }
        that.checkUserFreePacketBtn()
      })
    },
    // 
    onPouchSuccess() {
      this.isAliPayAdOnLoad = true
      if (this.planStatus !== '2') return
      // 添加访问日志
      // type 1有广告 2无广告 4无广告领袋 5放弃广告领袋
      this.packetType = 1
      this.$api.lights.dhcallbacklogAddaccesslog({userId: this.userId, type: 1, activityType: this.activityType})
      // console.log("this.aliPayAd:",aliPayAd)
      this.checkUserFreePacketBtn()
    },
    onPouchError(params) {
      console.log('onPouchError------', params);
      // type 1有广告 2无广告 4无广告领袋 5放弃广告领袋
      this.packetType = 4
      this.$api.lights.dhcallbacklogAddaccesslog({userId: this.userId, type: 2, activityType: this.activityType})
      this.checkUserFreePacketBtn()
    },
    /**
     * 小荷包关闭回调
     * code：
     * '1700' 荷包开通成功
     * '1701' /** 荷包开通失败
     * '1702' /** 荷包默认状态（用户主动放弃）
     * '1703' /** 荷包开通默认状态数据问题
     */
    onPouchClose(params = {}) {
      const { type: { code } = {}, bizId } = params
      console.log('close------', params, code, bizId);

      //用户操作任务，需要轮训回调日志
      if (code === '1700'){
        //开始监听接口返回
        this.timer = setInterval(() => {
          // 执行接口轮训
          this.$api.lights.dhcallbacklogGetcallbacklog({bizId, userId:this.userId}).then(res=>{
            console.log('res------', res)
            if (res.data) {
              if (res.data.packetId){
                this.stopSetInterval()
                this.$navto.push('LightsPacketResult', { packetId: res.data.packetId, userId: this.userId, deviceId: this.deviceId })
                this.pouchVisible = false
              }
            }
          })
        }, 2000)
        return
      }

      this.packetType = 5
      this.checkUserFreePacketBtn()
      this.pouchVisible = false

    },
    async payBtnClick() {
      const res = await this.$api.lights.payAlipay({ associateId: this.userId, deviceId: this.deviceId })
      const { payInfo } = res.data

      uni.requestPayment({
        provider: 'alipay',
        orderInfo: payInfo,
        success: res => {
          const { resultCode } = res || {}
          switch(resultCode) {
            case '9000':
              this.$navto.push('LightsPacketResult', { userId: this.userId, deviceId: this.deviceId})
              break
            default:
          }
        },
        fail: err => {
          console.log('err--------', err)
        }
      })
    },
    // 获取banner广告位素材
    async getAds() {
      const params = {
        entryOpenId: this.userId,
        deviceId: this.deviceId,
        accountId: this.userId,
        positionKeys: [this.ADSID]
      }
      const res = await this.$api.lights.weboriginalityGetWebOriginality(params)
      this.originalityList = res.data || []
      this.$nextTick(() => {
        this.checkAdsDivShow()
      })
    },
    // banner广告位点击
    async adsClick(tacticsId, config) {
      if (!config.landingPage || config.landingPage.length === 0) return
      uni.setStorageSync(positionSource, config.landingPage)
      await this.adsClickRequest(config)
      this.$navto.push('WebHtmlView', { src: config.landingPage, title: config.originalityName })
    },
    async adsClickRequest(config) {
      if (!config.tacticsId) return
      await this.$api.lights.addWeboriginalityClickNumber(this.getAdsDto(config))
    },
    // banner广告位曝光
    adsExposure(config) {
      if (!config.tacticsId) return
      this.$api.lights.addWeboriginalityExposureNumber(this.getAdsDto(config))
    },
    /*添加cookie*/
    addCookie(name, value, days, path) {
      name = encodeURIComponent(name);
      value = encodeURIComponent(value);
      let expires = new Date();
      expires.setTime(expires.getTime() + days * 3600000 * 24);
      //path=/，表示cookie能在整个网站下使用，path=/temp，表示cookie只能在temp目录下使用
      path = path == '' ? '' : ';path=' + path;
      let _expires = (typeof days) == 'string' ? '' : ';expires=' + expires.toUTCString();
      const cookie = name + '=' + value + _expires + path;
      uni.setStorageSync(name, cookie);
    },
    // 获取广告位dto
    getAdsDto(config) {
      let params = {}
      let isOld = true
      const systemInfo = uni.getSystemInfoSync()
      let code = uni.getStorageSync('gb_code')
      if (this.$validate.isNull(code)) {
        this.addCookie('gb_code', code, 365, '/')
        code = uni.getStorageSync('gb_code')
        isOld = false
      }
      params.tacticsIds = Array.isArray(config.tacticsId) ? config.tacticsId : [config.tacticsId];
      params.isOld = isOld;
      params.code = code.toString();
      params.cookieId = code;
      params.accessTime = Date.now();
      params.requestTime = Date.now();
      params.osType = systemInfo.osName;
      params.browserType = systemInfo.browserName || '';
      params.deviceType = systemInfo.deviceModel || systemInfo.deviceType;
      params.isWechat = false;
      params.pageAddress = systemInfo.uniPlatform;
      params.sourceDesc = '';
      params.source = '';
      params.screenWidth = systemInfo.screenWidth;
      params.screenHeight = systemInfo.screenHeight;
      params.screen = params.screenWidth + 'x' + params.screenHeight
      params.accountId = this.userId

      //添加拓展参数
      params = Object.assign(params, this.query);
      params.adsKey = [this.ADSID]
      return params;
    },
    // 异步获取元素
    getEl (getDiv) {
      return new Promise((resolve, reject) => {
        // #ifdef H5
        resolve(getDiv.getBoundingClientRect())
        // #endif

        // #ifndef H5
        if (getDiv.boundingClientRect) {
          getDiv.boundingClientRect(data => {
            resolve(data)
          }).exec()
        }
        // #endif
      })
    },

    // 判断DIV 是否隐藏
    isDivDisplay (el) {
        let ishidden = false
        // #ifdef H5
        if (el.style.display === 'none') {
          ishidden = true
        }
        // #endif
        return ishidden
    },
    // 判断是否在可视区域
    async isElementInViewport (getDiv) {
      var viewPortHeight = uni.getSystemInfoSync().windowHeight
      const elInfo = await this.getEl(getDiv)
      console.log('elInfo--------', elInfo, getDiv)
      let top = 0
      let height = 0
      if (elInfo) {
        top = elInfo.top
        height = elInfo.height
      } else {
        return Promise.resolve(false)
      }
      console.log(viewPortHeight, top, height)
      if (top >= 0) {
        return Promise.resolve(top <= viewPortHeight)
      } else {
        return Promise.resolve((height + top) > 0)
      }
    },
    // 检查ads广告位 是否曝光
    async checkAdsDivShow() {
      this.originalityList.forEach(async item => {
        console.log('item.tacticsId--------', `tacticsId-${item.tacticsId}`)
        // #ifdef H5
        var getDiv = document.getElementById(`tacticsId-${item.tacticsId}`)
        // #endif

        // #ifndef H5
        const query = uni.createSelectorQuery()
        var getDiv = query.select(`#tacticsId-${item.tacticsId}`)
        // #endif

        if (getDiv === null || getDiv.boundingClientRect === undefined) return Promise.resolve(false)
        // 曝光上报
        if (await this.isElementInViewport(getDiv) && !this.isDivDisplay(getDiv)) {
          this.adsExposure(item)
        }
      });
    },
    testAd(){

    },
    // 关闭轮询
    stopSetInterval() {
      console.log('清除定时器---------------', this.timer)
      if (this.timer) {
        console.log('清除定时器---------------')
        clearInterval(this.timer)
        this.timer = null
      }
    },
    //免费出袋
    freeBtnClick(){
      const that = this
      that.$api.lights.dhcallbacklogFreePacket({userId: that.userId, type:that.packetType}).then(res=>{
        if (res.data.packetId){
          that.$navto.push('LightsPacketResult', { packetId: res.data.packetId, userId: this.userId, deviceId: this.deviceId})
        }else {
          that.$uniPlugin.errMsg(res.msg)
        }
      }).catch(err=>{
        that.$uniPlugin.toast(err.msg)
      })
    },
    onAdInstance(ref) {
      const that = this
      // 存储自定义组件实例，方便以后调用
      console.log("ref-----------------:",ref)
      aliPayAd = ref;
      this.onAdLoad()
    },
    onAdLoad(fn){
      const that = this
      aliPayAd.onLoad((res) => {
        this.isAliPayAdOnLoad = true
        // fn()
        if (that.planStatus==='2') {
          // 添加访问日志
          // type 1有广告 2无广告 4无广告领袋 5放弃广告领袋
          this.packetType = 1
          that.$api.lights.dhcallbacklogAddaccesslog({userId: that.userId, type: 1, activityType: that.activityType})
          // console.log("this.aliPayAd:",aliPayAd)
          that.checkUserFreePacketBtn()
          if (!this.showError) {
            that.openShow()
          }
        }
      })
      aliPayAd.onClose((isClick) => {
        this.onAdClose(isClick)
      });
      aliPayAd.onError((err) => {
        if (this.afterId) {
          this.isAliPayAdOnLoad = false
          this.getDhplanAfterPlan()
          return
        }
        this.onAdError(err)
      });
    },
    checkUserFreePacketBtn(){
      // 没有插件广告时才显示免费按钮
      if (this.userFreeCount > 0) return this.showFreeButton = true
      this.showFreeButton = false
      if (this.paySwitch !== 1) {
        this.showError = true
        this.tips = '用户暂无活动权益！'
      }

    },
    /**
     * 用户操作
     // 广告关闭
     // 1. 用户点击「转化」按钮跳出，如「领取权益」，
     // type为"JUMP"，bizId用来标识唯一任务型广告{  type: "JUMP",  bizId: "a51047accd1b55de667fef68b8ae68f6"}//
     // 2. 用户点击关闭广告，type为"CLOSE", bizId无需消费
     // {  type: "CLOSE",  bizId: "67627eced3802641fc3d9927a369c2b1"}
     */
    onAdClose(res){
      const that = this
      console.log('onAdClose', res);
      if (res) {
        //用户操作任务，需要轮训回调日志
        if (res.type === 'JUMP'){
          //开始监听接口返回
          this.timer = setInterval(() => {
            // 执行接口轮训
            that.$api.lights.dhcallbacklogGetcallbacklog({bizId: res.bizId, userId:that.userId}).then(res=>{
              if (res.data) {
                if (res.data.packetId){
                  that.stopSetInterval()
                  that.$navto.push('LightsPacketResult', { packetId: res.data.packetId, userId: this.userId, deviceId: this.deviceId })
                }
              }
            }).catch(err=>{

            })
          }, 2000)
        }
        //用户关闭任务
        if (res.type === 'CLOSE'){
          this.packetType = 5
          this.checkUserFreePacketBtn()
        }
      }
    },
    /**
     * 加载异常
     */
    onAdError(res){
      console.log('onAdError', res);
      // type 1有广告 2无广告 4无广告领袋 5放弃广告领袋
      this.packetType = 4
      this.$api.lights.dhcallbacklogAddaccesslog({userId: this.userId, type: 2, activityType: this.activityType})
      this.checkUserFreePacketBtn()
    },
    /**
     * 打开显示插件
     */
    openShow: function () {
      this.packetType = 1
      const that = this
      // type 1有广告 2无广告 4无广告领袋 5放弃广告领袋
      console.log('openShow');
      if (this.activityType == 2) {
        this.pouchRtaExtMap = {
          organization_type: 'hospital',
          organization_sub_type: that.organizationSubType,
          device_location: that.deviceLocation,
          device_id: '1',
          public_opinion_level: that.publicOpinionLevel,
          touch_point_location: 'index',
          touch_point_type: 'full_screen',
        }
        this.pouchVisible = true
        return
      }
      aliPayAd.show({
        spaceCode: this.spaceCode || 'TABLE_SCREEN_TEST_SPACE_0',
        rtaExtMap: {
          organization_type: 'hospital',
          organization_sub_type: that.organizationSubType,
          device_location: that.deviceLocation,
          device_id: '1',
          public_opinion_level: that.publicOpinionLevel,
          touch_point_location: 'index',
          touch_point_type: 'full_screen',
        },
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.icon-home {
  width: 64rpx;
  height: 64rpx;
}
.nav-bar-title {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 32rpx;
  color: #1D2029;
  line-height: 44rpx;
}
.content {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: #F4F6FA;
  box-sizing: border-box;
  .content-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 750rpx;
    height: 698rpx;
    background: linear-gradient(180deg, #D2F3EE 0%, #ADEDE4 70%, #F4F6FA 100%);
    z-index: 0;
  }
  .content-cell {
    position: relative;
    height: 100%;
    box-sizing: border-box;
    margin-top: 42rpx;
    .banner {
      width: 702rpx;
      border-radius: 16rpx;
      overflow: hidden;
      display: block;
      margin: 0 auto 32rpx;
      .banner-img {
        width: 100%;
      }
    }
    .content-center {
      width: 702rpx;
      margin: 0 auto;
      border-radius: 16rpx;
      background-color: #fff;
      display: flex;
      align-items: center;
      flex-direction: column;
      position: relative;
      // overflow: hidden;
      padding-bottom: 64rpx;
      .whiteTop {
        width: 542rpx;
        height: 282rpx;
        margin: 24rpx auto 100rpx;
      }
      .cell {
        box-sizing: border-box;
        width: 100%;
        height: 100%;
        padding: 60rpx 40rpx 24rpx;
        .top {
          &-title {
            font-weight: 500;
            font-size: 36rpx;
            color: #1D2029;
            line-height: 50rpx;
            margin-bottom: 64rpx;
          }
          &-desc {
            color: #333;
            font-size: 18px;
            letter-spacing: 1px;
          }
        }
      }

      .centerC {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
      }
      .repair {
        margin: 164rpx auto 0;
        width: 306rpx;
        height: 352rpx;
      }
      .scanH5QrCodeText {
        font-size: 28rpx;
        color: #1D2029;
        line-height: 40rpx;
        margin-top: 32rpx;
      }

      .content-code {
        position: relative;
        .tips {
          font-size: 20px;
          color: #383838;
          font-weight: 800;
          margin-bottom: 2vh;
          &.tips-box {
            font-size: 16px;
            font-weight: 550;
            color: #2acbb4;
            background-color: #edfcf9;
            border-radius: 12px;
            padding: 2px 10px;
            margin-bottom: 58px;
          }
          .tips-text.arrows {
            padding: 0 12px;
          }
        }

        .code {
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center;
          position: relative;

          .code-img {
            position: absolute;
            .hand {
              width: 50%;
              position: absolute;
              right: -60%;
              bottom: -43%;
            }
          }
        }
      }

      .button-list {
        width: 100%;
        position: relative;
        padding: 0 40rpx;
        box-sizing: border-box;
        // min-height: calc(70px + 4vh);
        .button {
          position: relative;
          width: 622rpx;
          height: 128rpx;
          background-color: #00B484;
          border-radius: 64rpx;
          font-weight: 600;
          font-size: 40rpx;
          color: #FFFFFF;
          line-height: 128rpx;
          text-align: center;
          border: none;
          &.default-text {
            font-size: 34rpx;
          }
          &::after {
            content: none;
            border: none;
          }
          .tag-box {
            position: absolute;
            left: 0;
            top: 0;
            background-size: cover;
            background-repeat: no-repeat;
            background-position: 0 0;
          }
          .free-box {
            width: 154rpx;
            height: 72rpx;
            padding: 4rpx 0 0 38rpx;
            box-sizing: border-box;
            font-weight: 600;
            font-size: 34rpx;
            color: #FFFFFF;
            line-height: 48rpx;
            text-align: left;
          }
          .pay-box {
            width: 122rpx;
            height: 72rpx;
            padding: 8rpx 0 0 28rpx;
            box-sizing: border-box;
            font-weight: 600;
            font-size: 28rpx;
            color: #00B484;
            line-height: 40rpx;
            text-align: left;
          }
          &+.button {
            margin-top: 48rpx;
          }
          &::before {
            content: none;
          }
        }
        .pay-btn {
          background: #FFFFFF;
          border-radius: 64rpx;
          border: 2rpx solid #98DAC8;
          color: #00B484;
        }
        .equities-btn {
          background-color: #FF8800;
        }
        .hand {
          width: 210rpx;
          height: 158rpx;
          position: absolute;
          right: -32rpx;
          top: 162rpx;
        }
      }
    }
    .content-bottom {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 112rpx 0 8rpx;
      padding-bottom: calc(constant(safe-area-inset-bottom) + 8rpx);//兼容 IOS<11.2
      padding-bottom: calc(env(safe-area-inset-bottom) + 8rpx);//兼容 IOS>11.2
      .logo {
        width: 200rpx;
        height: 24rpx;
        margin-bottom: 16rpx;
      }
      .tips-text {
        font-weight: 400;
        font-size: 26rpx;
        color: #868C9C;
        line-height: 36rpx;
      }
    }
  }
  .videoPop {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .close {
      margin-top: 5vh;
    }
  }
}
.guide-box {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  .guide-text {
    font-size: 32rpx;
    color: #868C9C;
    line-height: 44rpx;
  }
  .icon-guide-bottom {
    width: 24rpx;
    height: 24rpx;
    margin-left: 4rpx;
  }
}
.logo-img {
  width: 100%;
  height: 50rpx;
}
</style>
