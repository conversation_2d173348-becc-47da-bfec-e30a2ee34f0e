<template>
  <view class="main">
    <!-- #ifndef H5 -->
    <sa-hover-menu />
    <!-- #endif -->
    <web-view :src="src"></web-view>
  </view>
</template>

<script>
import { getQueryObject } from '@/utils/index'
import saHoverMenu from '@/components/basics/sa-hover-menu/sa-hover-menu'
export default {
  components: {
    saHoverMenu
  },
  data() {
    return {
      src: ''
    }
  },
  onLoad(paramsObj = {}) {
    const query = this.$Route.query
    if (!this.$validate.isNull(query)) {
      this.src = this.fullyDecodeURI(query.src)
      uni.setNavigationBarTitle({
        title: this.fullyDecodeURI(query.title)
      })
    }
  },
  onShareAppMessage(res) {
    if (res.from === 'button') {// 来自页面内分享按钮
      // console.log(res.target)
    }
    return {
      title: '', //分享的名称
      path: 'modules/common/web-html-view/index?query=' + encodeURIComponent(JSON.stringify({
        src: encodeURIComponent(this.shareUrl)
      })),
    }
  },
  computed: {
    shareUrl() {
      if (!this.src) return ''
      if (!this.src.startsWith('https://open.weixin.qq.com')) return this.src
      return getQueryObject(this.src).redirect_uri || this.src
    }
  },
  methods: {
    fullyDecodeURI (url) {
      if (!url || typeof(url) !== 'string') return url
      let params = url.split('?')[1]
      url = url.split('?')[0]
      if(!params || !url) return url
      function isEncoded(url) {
        if (url.startsWith('https://') || url.startsWith('http://')) return false
        url = url || '';
        return url !== decodeURIComponent(url);
      }
      while (isEncoded(url)){
        url = decodeURIComponent(url);
        params = params ? decodeURIComponent(params) : '';
      }

      const paramsObj = getQueryObject(params)
      params = ''
      Object.keys(paramsObj).forEach(key => {
        if(paramsObj[key].startsWith('https://') || paramsObj[key].startsWith('http://')) {
        paramsObj[key] = encodeURIComponent(paramsObj[key]);
        }
        params += params ? `&${key}=${paramsObj[key]}` : `${key}=${paramsObj[key]}`
      })
      return params ? (url + '?' + params) : url;
    }
  }
}
</script>

<style lang="scss" scoped>
  .main{
    height: 100%;
    position: relative;
  }
</style>
