<template>
  <view class="page">
    <uni-nav-bar
      color="#1d2029"
      :border="false"
      :inFontWeight='true'
      :showBtnsRight='false'
      :fixed="false"
      title='陪诊服务'
      statusBar
      @clickLeft="back"
      left-icon="left"
      left-width="48rpx"
      right-width="100px"
      backgroundColor="rgba(0,0,0,0)"
    >
    </uni-nav-bar>
    <!-- 订单未取消状态 -->
    <view v-if="accompanybookOne.orderState !== 8">
      <tabelTap :pageIndex='pageIndex' :accompanybookOne='accompanybookOne'></tabelTap>
      <reservation :channelCode='channelCode' :classifyId="classifyId" :classifyName="classifyName" :fromQRCode="fromQRCode" :fromOrderQRCode="fromOrderQRCode" @changeServer='openChangeServer' @finish='finish' @handleOneByCity="handleOneByCity" @selectServer="selectServer" :currentServer='currentServer' :cityRes="cityRes" v-if="pageIndex === 1"></reservation>
      <guidance @finish='finish' :accompanybookOne='accompanybookOne' v-if="pageIndex === 2"></guidance>
      <inLinePay ref="inLinePay" @changesetIsNotice="setIsNotice('modelPopup')" @setProductCode='setProductCode' :insuredInfo='insuredInfo' :underInfo='underInfo' @ConfirmPurchaseInsurance='ConfirmPurchaseInsurance' @clearOrder='clearOrder' @inLinePay='inLinePay' @comboPay='comboPay' :accompanybookOne='accompanybookOne' v-if="pageIndex === 3"></inLinePay>

      <accompanyStage :insuredInfo='insuredInfo' @ConfirmPurchaseInsurance='ConfirmPurchaseInsurance' @clearOrder='clearOrder' :accompanybookOne='accompanybookOne' v-if="pageIndex === 4"></accompanyStage>
      <finish @loadData='loadOrderDetails' :showGuardDetail='showGuardDetail' :accompanybookOne='accompanybookOne' v-if="pageIndex === 5"></finish>
      <changeServer v-if="!orderId" @change='changeExchange' @selectServer='selectServer' :openFlag='openServerFlag' :cityRes="cityRes" :classifyId="classifyId" :isUnifiedView="isUnifiedView"></changeServer>
      <customerService ref="customerService" :pageIndex='pageIndex' :popupTitle='(pageIndex === 3 || pageIndex === 4) ? "联系客服，取消订单" : "" ' v-if="showCustomerService"></customerService>
    </view>
    <view v-else>
      <cancel :accompanybookOne='accompanybookOne' :insuredInfo='insuredInfo'></cancel>
    </view>
    <!-- 加载loading -->
    <view v-if="!pageIndex" class="loading-container" :class="{ 'is-loading': !pageIndex }">
      <view class="dual-ring"></view>
    </view>
      <modelPopup ref="modelPopup" @changesetIsNotice="setIsNotice('inLinePay')" :isLocking="isLocking" :underInfo='insuredInfo'  :productCode="productCode" :loadProductCodeFlag='loadProductCodeFlag' @getProductCode='setProductCode' :orderDetails='insuredInfo' @finish='finishModel' @secondaryfinish='secondaryfinish' :modelType='modelType' @change='flag=>openFlagModel = flag' :openFlag='openFlagModel' :modelOptions="modelOptions"></modelPopup>
  </view>
</template>

<script>
  import { mapState } from 'vuex'
  import common from '@/common/util/main'
  import serverOptions from '@/config/env/options'
  import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'
  import tabelTap from '../components/tabelTap'
  import changeServer from '../components/changeServer'
  import customerService from '../components/customerService.vue'
  import reservation from './reservation.vue'
  import guidance from './guidance.vue'
  import inLinePay from './inLinePay.vue'
  import finish from './finish.vue'
  import accompanyStage from './accompanyStage.vue'
  import cancel from './cancel.vue'
  import { getQueryObject } from '@/utils/index'
  import modelPopup from './components/model.vue'
  import getPosition from '@/utils/getPosition'

  export default{
    components: {
      uniNavBar,
      tabelTap,
      reservation,
      guidance,
      inLinePay,
      finish,
      accompanyStage,
      cancel,
      changeServer,
      customerService,
      modelPopup
    },
    data(){
      return {
        pageIndex:null,
        currentServer:{},
        openServerFlag:false,
        accompanybookOne:{},
        orderId:'',
        showPopup:false,
        showGuardDetail:false,
        showCustomerService: false,
        currentServerId:null,
        oneByCity:null,
        cityRes:null,
        selectServerObj:null,
        system:null, //系统信息
        channelCode:'',
        openFlagModel:false,
        modelType:'',
        underInfo:{},
        insuredInfo:{},
        loadProductCodeFlag:false,
        productCode:'',
        isLocking:false,
        modelOptions: {},
        classifyId: '', // 分类ID
        classifyName: '', // 分类名称
        isUnifiedView: true, // 是否使用统一视图模式（默认启用）
        fromQRCode: false, // 标记是否通过扫码进入
        fromOrderQRCode: false // 标记是否是订单二维码
      }
    },
    computed: {
      ...mapState('user', {
        curSelectUserInfo: state => state.curSelectUserInfo
      })
    },
    watch:{
  },
    onShow(){
      console.log('监听onShow');
      uni.$emit('onShow');
    },
    async onLoad(res) {
      await serverOptions.handlingCloudProviderId();
      console.log(res,'res-000--')
      uni.showLoading({title:'加载中',mask:true}) //打开加载
      this.system = uni.getSystemInfoSync() //获取系统信息

      // 获取服务商信息，判断是否显示客服组件
      try {
        const { data } = await this.$api.accompanyDoctor.accompanyproviderQueryOne({id: serverOptions.providerId});
        // 判断联系客服开关是否开启，且位置包含下单页面(2)
        if (data.contactButton === 1 && data.contactPosition && data.contactPosition.includes('2')) {
          this.showCustomerService = true;
        }
      } catch (error) {
        console.error('获取服务商信息失败:', error);
      }
      // 接收分类ID参数
      if (res.classifyId) {
        this.classifyId = res.classifyId;
        this.classifyName = decodeURIComponent(res.name) || '';
        console.log('接收到分类参数:', {classifyId: this.classifyId, classifyName: this.classifyName});
      }
      // 判断传递来的参数是否需要解析
      if(res.scene){
        let params = decodeURIComponent(decodeURIComponent(decodeURIComponent(res.scene)))
        let sceneObj = getQueryObject(params)

        // 设置标记，表明是通过扫码进入
        this.fromQRCode = true;

        let scCode = sceneObj.gbScene || sceneObj.gs
        sceneObj.gs && (this.channelCode = scCode);

        // 订单ID处理 - 多种可能的参数名
        const orderId = sceneObj.i || sceneObj.id || sceneObj.orderId;
        if (orderId) {
          this.orderId = orderId;
          this.fromOrderQRCode = true; // 标记为订单二维码
          await this.loadOrderDetails();
          return uni.hideLoading();
        }

        // 如果有服务ID，也记录下来
        if (sceneObj.s || sceneObj.serviceId) {
          this.currentServerId = sceneObj.s || sceneObj.serviceId;
        }

        // 记录分类ID
        if (sceneObj.c || sceneObj.classifyId) {
          this.classifyId = sceneObj.c || sceneObj.classifyId;
        }

        scCode && this.loadSceneInfo(scCode)
        this.orderId && await this.loadOrderDetails();
      }
      if(res.orderId){
        this.orderId = res.orderId;
        await this.loadOrderDetails()
      }
      if(this.orderId) return uni.hideLoading();
      this.pageIndex = 1;
      this.$nextTick(async ()=>{
        let cityname = this.$common.getKeyVal('user','cityName',true)
        let pname = this.$common.getKeyVal('user','pname',true)
        this.cityRes = (cityname && pname) ? {cityname,pname} : await getPosition.initLocationPerm()
        console.log('输出当前所在地址',this.cityRes);

        cityname || this.$common.setKeyVal('user','cityName',this.cityRes.cityname,true)
        pname || this.$common.setKeyVal('user','pname',this.cityRes.pname,true)
        console.log(this.cityRes,'this.cityRes0000')
        if(res.id || this.currentServerId){
          this.currentServerId = res.id || this.currentServerId
          if(this.cityRes?.cityname){
            this.accompanyserviceQueryOneByCity({id:this.currentServerId,city:this.cityRes?.cityname})
          }
        }else{
          let queryOptons = {current:this.current,size:1,condition:{state:1,city:this.cityRes?.cityname}}
            let {data:{records}} = await this.$api.accompanyDoctor.accompanyserviceQueryCityPage(queryOptons);
            this.currentServer = records[0]
            // 设置分类信息 - 如果没有通过URL参数指定分类，则从服务中获取分类信息
            if (!this.classifyId && this.currentServer) {
              if (this.currentServer.classifyId) {
                this.classifyId = this.currentServer.classifyId || '';
                this.classifyName = this.currentServer.classifyName || '';
              }
            }
        }
        uni.hideLoading()
      })
     },
    methods:{
      async loadSceneInfo(scCode){
        console.log('scCode',scCode);
        // const sceneInfo = (await this.$api.common.minichannellinkQueryOne({code:scCode})).data
        // console.log('sceneInfo',getQueryObject(sceneInfo.customParameters),sceneInfo.customParameters);
        // let {classifyId,name} = getQueryObject(sceneInfo.customParameters);
        // this.classifyId = classifyId;
        // this.classifyName = name;
      },
      setIsNotice(sonBoxName){
        this.$refs[sonBoxName].isNotice = !this.$refs[sonBoxName].isNotice
      },
      loadProductCode(){
        this.loadProductCodeFlag = true;
      },
      // 唤醒模块弹窗
      ConfirmPurchaseInsurance(type, isLocking = false, options = {}) {
        this.openFlagModel = true;
        this.modelType = type;
        this.isLocking = isLocking;

        // 保存传入的保险金额
        if(options && options.insuranceNum) {
          this.modelOptions = options;
        }
      },
      setProductCode({productCode}){
        console.log('触发setProductCode',productCode);

        this.productCode = productCode;
      },
      async finishModel({modelType,options}){
        this.modelType = '';
        options = {accompanyId:this.accompanybookOne.id,...options}
        console.log('{modelType,options}',{modelType,options});
        let apiFunc;
        switch (modelType){
            // 风险提示
            case 'riskAlert':
              return this.ConfirmPurchaseInsurance('insureInfo');
            break;
            // 保险内容
            case 'insureInfo':
              this.$refs.inLinePay.updateInsurance(options.insuranceNum)
              this.insuredInfo.insuranceNum = options.insuranceNum;
              if(!this.$refs.inLinePay.isSelectInsurance){
                return this.$refs.inLinePay.selectInsurance();
              }else{
                this.productCode = options.productCode
              }
            break;
            // 取消保障
            case 'surrender':
              apiFunc = this.$api.accompanyDoctor.accompanyinsureCancel;
            break;
            // 取消保障成功
            case 'surrender':
            break;
          default:
            break;
        }
        uni.showLoading({title:'加载中',mask:true})
        if(!apiFunc) apiFunc = ()=>{}
        let data = await apiFunc(options);
        // 取消保障成功重新刷新页面
        if(modelType === 'surrender'){
          this.finish(this.accompanybookOne.id)
        }

        uni.hideLoading()
        this.openFlagModel = false;
        if(modelType === 'surrender'){
          //唤醒取消保障成功弹窗
          await this.ConfirmPurchaseInsurance('surrenderCG')
        }
      },
      secondaryfinish({modelType}){
        switch (modelType){
            case 'riskAlert':
              this.openFlagModel = false;
              this.$refs.inLinePay.setDirectPayment(true);
              this.$refs.inLinePay.inLinePay();
              break;
            case 'surrender':
              this.openFlagModel = false;
              break;
          default:
            break;
        }
      },
      setChannelInfo(sceneObj){
        if(!sceneObj?.gs) return
        // 定义时间间隔
        let timeStep = 1000 * 60
        // 获取当前时间戳
        let newTime = +new Date()
        // 获取旧的参数
        let oldChannel = uni.getStorage({key:'channel-reservation'});
        if(!oldChannel){
          uni.setStorage({
            key: 'channel-reservation',
            	data: {...sceneObj,time:+new Date()},
          })
        }
      },
      handleOneByCity(cityObj){
        this.oneByCity = cityObj?.city
        this.cityRes.cityname = cityObj?.city
        this.cityRes.pname = cityObj?.province
        this.accompanyserviceQueryOneByCity({id:this.selectServerObj?.id || this.currentServerId,city:this.oneByCity})
      },
      async accompanyserviceQueryOneByCity(params){
        const res = await this.$api.accompanyDoctor.accompanyserviceQueryOneByCity(params)
        this.currentServer = res.data

        // 确保查询到的服务也包含分类信息
        if (this.classifyId) {
          this.currentServer.classifyId = this.classifyId;
          this.currentServer.classifyName = this.classifyName || '';
        }
      },
      clearOrder(){
        this.$refs.customerService.open();
      },
      finish(orderId){
        console.log('orderId',orderId);
        if(orderId) this.orderId = orderId;
        this.loadOrderDetails()
      },
      async loadOrderDetails(){
        let queryFunc = this.$api.accompanyDoctor.getAccompanybookOne
        let {data:accompanybookOne} = await queryFunc({id:this.orderId})
        // 判断是否存在该订单
        if(!accompanybookOne){
          return uni.showModal({
            title:'订单信息有误，请联系客服',
            icon:'none',
            success: function (res) {
              if (res.confirm) {
              console.log('用户点击确定');
              uni.switchTab({url:'pages/accompany-home/index'})
              } else if (res.cancel) {
              console.log('用户点击取消');
              }
            }
            })
        }
        console.log('accompanybookOne',accompanybookOne);
        this.accompanybookOne = accompanybookOne;

        // 保存订单中的分类信息
        if (accompanybookOne.classifyId) {
          this.classifyId = accompanybookOne.classifyId;
          this.classifyName = accompanybookOne.classifyName || '';
        }

        // 设置页面状态
        this.pageIndex = accompanybookOne.processState;

        // 如果是从订单页面进入，设置订单页标志
        if (this.orderId) {
          // 标记为订单页面，用于协议判断
          if (this.fromQRCode) {
            this.fromOrderQRCode = true; // 二维码扫描进入订单页
          }
        }

        this.loadAccompanyinsure()
        if(!this.accompanybookOne.star){
          this.showGuardDetail = true;
        }
      },
      // 查询购买人信息
      async loadAccompanyinsure(){
        let accompanyId = this.orderId;
        let {data} = await this.$api.accompanyDoctor.accompanyinsureQuery({accompanyId})
        console.log('loadAccompanyinsure',data);
        if(!data) return
        let {name,sex,certfType,certfNo,birthDate,id} = data;
        console.log('{name,sex,certfType,certfNo,birthDate}',{name,sex,certfType,certfNo,birthDate});
        birthDate = this.changeTime(birthDate)
        this.underInfo = {name,sex,certfType,certfNo,birthDate};
        data.birthDate = birthDate;
        this.insuredInfo = data;
        this.insuredInfo.insuranceNum = this.insuredInfo.insuranceNum ? this.insuredInfo.insuranceNum : 5;
        console.log('this.insuredInfo1',this.insuredInfo);
        this.productCode = this.insuredInfo.productCode;
      },
      changeTime(time){
        let endDate = new Date(time);
        let Y = endDate.getFullYear() + "-";
        let M = (endDate.getMonth() + 1 < 10 ? "0" + (endDate.getMonth() + 1) : endDate.getMonth() + 1) + "-";
        let D = endDate.getDate() < 10 ? "0" + endDate.getDate() : endDate.getDate() + "";
        return Y + M + D
      },
      back() {
        let pages = getCurrentPages() // 获取栈实例
        if (pages.length > 1) {
          this.$navto.back()
        } else {
          this.$navto.replaceAll('AccompanyHome')
        }
      },
      // 接收服务选择事件，更新服务和分类信息
      selectServer(data){
        this.selectServerObj = data;

        // 检查是否需要根据ID获取完整服务信息
        if (data.id && this.cityRes?.cityname && !data.categoryId && !data.classifyId) {
          this.accompanyserviceQueryOneByCity({id:data.id, city: this.oneByCity || this.cityRes?.cityname});
        } else {
          this.currentServer = {...data}; // 使用解构以创建新对象，避免引用问题

          // 如果服务包含分类信息，更新分类信息
          if (data.categoryId) {
            this.classifyId = data.categoryId;
            this.classifyName = data.categoryName || '';
          } else if (data.classifyId) {
            this.classifyId = data.classifyId;
            this.classifyName = data.classifyName || '';
          }

          // 确保服务对象也包含分类信息
          this.currentServer.classifyId = this.classifyId;
          this.currentServer.classifyName = this.classifyName;
        }
      },

      openChangeServer(){
        this.openServerFlag = true
      },
      changeExchange(flag){
        this.openServerFlag = flag
      },
      // 套餐支付方法
      async comboPay({comboId}){
        const codeUserInfo = common.getKeyVal('user', 'codeUserInfo',true)
        let queryOptions = {
          id:this.orderId,
          userId:codeUserInfo.id,
          comboId
        }
        let data = await this.$api.accompanyDoctor.accompanybookPay(queryOptions);
        this.$uniPlugin.toast('支付成功');
        this.loadOrderDetails()

      },
      async allinpaydetailGetOrderDetail(){
        const res = await this.$api.accompanyDoctor.allinpaydetailGetOrderDetail({bizOrderNo:this.orderId})
        return Promise.resolve(res)
      },
      flush(){
        this.openFlagModel = false;
        let index = 0;
        let timeEr = setInterval(()=>{
          index++;
          if(index >=5){
            clearInterval(timeEr);
            return
          }
          if(this.accompanybookOne.pay){
            clearInterval(timeEr);
            return
          }
          this.loadOrderDetails();
        },500)
      },
      // 在线支付方法
      async inLinePay(isSelectInsurance,providerId){
        console.log('当前支付的服务商ID',providerId);
        
        let options = {bizOrderNo:this.orderId,productCode:this.productCode,successCb:this.flush,failCb:(payErr)=>{
          console.log('payErr.errMsg',payErr);
          if(payErr && payErr.msg === "该订单为联合订单,不能单独支付!"){
            return uni.showModal({
              title: '提示',
              content: '该订单为联合订单，不能单独支付，请点击下方前往支付',
              confirmText:'点击前往',
              success: (res)=> {
                res.confirm && uni.redirectTo({url: `modules/accompany-doctor/service-reservation/joinOrder/joinOrder?orderId=${this.accompanybookOne.combineOrderId}`})
                !res.confirm && this.back()
              }
            });
          }
          this.loadOrderDetails()
        },type:1,providerId};
        if(!isSelectInsurance){
          delete options.productCode
        }
        console.log('options金额',options);

        this.$ext.user.accompanyPay(options)
      }
    }
  }
</script>

<style lang="scss">
  .page{
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    background: #F4F6FA;
  }
  .loading-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease-in-out;
  z-index: -1;
}

.loading-container.is-loading {
  opacity: 1;
  visibility: visible;
  z-index: 9999;
}
.dual-ring {
  display: inline-block;
  width: 64px;
  height: 64px;
  transform: scale(0.8);
  transition: transform 0.3s ease-in-out;
}

.is-loading .dual-ring {
  transform: scale(1);
}

.dual-ring:after {
  content: " ";
  display: block;
  width: 46px;
  height: 46px;
  margin: 8px;
  border-radius: 50%;
  border: 6px solid #3498db;
  border-color: #3498db transparent #3498db transparent;
  animation: dual-ring 1.2s linear infinite;
}

@keyframes dual-ring {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
