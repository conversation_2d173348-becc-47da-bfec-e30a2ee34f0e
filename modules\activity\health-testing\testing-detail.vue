<template>
  <view class='testing-detail'>
    <form-template
      class="form-template"
      :healthType="4"
      :currentNum="currentNum"
      :activitytype="activitytype"
      :cDisabled="cDisabled"
      :ptype="1"
      :noloading="noloading"
      :cmainid="mainId"
      :emainId="emainId"
      :updatecount="updatecount"
      :updatepage="updatepage"
      :businessid="editid"
      :isone="false"
      :tenantId="tenantId"
      :isAccuratePromotion='isAccuratePromotion'
      submitText='提交'
      :healthTypeFlag="false"
      :healthTesting="queryParams"
      @returnFn="returnFn"
    >
    </form-template>
  </view>
</template>

<script>
  import FormTemplate from '@/modules/activity/components/form-template/index.vue';
  import { mapState } from "vuex"
  export default {
    components: {
      FormTemplate
    },
    data(){
      return{
        file_ctx: this.file_ctx,
        editid: '',
        updatepage: 0,
        updatecount: 0,
        mainId: null,
        noloading: false,
        emainId: null,
        // back: true,
        activitytype: 2,
        cDisabled: false,
        isAccuratePromotion:false,
        tenantId:'',
        taskId: '', // 问卷任务id
        caseCollectSubmitLogId:false,
        submitParams: {}, // 表单提交参数
        currentNum:null,
        queryParams:{}, //接口携带code参数
      }
    },
    computed: {
      ...mapState('user', {
        recordUserInfo: state => state.recordUserInfo, // 当前登录用户信息
        healthInfo:state => state.healthInfo, // 获取当前分享好友图片
      })
    },
    onLoad(options){
      const query = this.$validate.isNull(this.$Route.query) ? options : this.$Route.query
      // let params = query?.scene?.split('&')[0]?.split('=')[1]]
      this.queryParams = {
        ...this.queryParams,
        title:query?.title,
        topicIndex:query?.topicIndex,
        topicCurrentIndex:query?.topicCurrentIndex,
        swiperListRadio:query?.swiperListRadio,
        currentChangeIndex:query?.currentChangeIndex,
        myEvaluating:query?.myEvaluating,
        resultScore:query?.resultScore,
        saveId:query?.saveId,
        saveValue:query?.saveValue
      }
      if(query?.scene){
        this.queryParams = this.handleFilterParams(decodeURIComponent(query.scene).split('&'))
      } else if(query?.gs){
        this.queryParams = query
      }
      
      this.editid = this.queryParams?.id || query.id

      if(query?.businessId){
        this.editid = query?.businessId
      }

      this.$nextTick(() => {
        this.updatepage += 1
      })
    },

    // 分享到朋友圈
    onShareTimeline(res) {
      let params = {
        accountId : this.$common.getKeyVal('user', 'accountId', true),
        researchId : this.editid, //问卷id
        channelCode: this.queryParams?.gs || ''
      }
      this.handleHealthSelfTestClick('分享')
      this.$api.activity.scoreresultlogShareLog(params).then(res=>{})
      return {
        title: this.healthInfo.title,//分享的标题
        path: `modules/activity/health-testing/testing-detail`,
        query: `id=${this.editid}&gs=${this.queryParams?.gs || ''}`,
      }
    },
    // 发送给好友
    onShareAppMessage(res) {
      let params = {
        accountId : this.$common.getKeyVal('user', 'accountId', true),
        researchId : this.editid, //问卷id
        channelCode: this.queryParams?.gs || ''
      }
      this.handleHealthSelfTestClick('分享')
      console.log(params,'params')
      this.$api.activity.scoreresultlogShareLog(params).then(res=>{console.log(res,'res分享的')})
      return {
        title: this.healthInfo.title, //分享的名称
        path: `modules/activity/health-testing/testing-detail?id=${this.editid}&gs=${this.queryParams?.gs}`,
        imageUrl: this.healthInfo.coverPath ? this.file_ctx + this.healthInfo.coverPath :''
      }
    },

    mounted(){},
    methods:{
      handleHealthSelfTestClick(type){
        getApp().globalData.sensors.track("HealthSelfTestClick",{'click_type':type})
      },
      handleFilterParams(params){
        // 使用reduce方法将分割后的数组转换成一个对象  
        let obj = params.reduce((acc, current) => {  
          // 将'key=value'分割成key和value  
          let [key, value] = current.split('=');  
          // 将key和value添加到acc对象中  
          acc[key] = decodeURIComponent(value); // 对value进行URL解码，以防有特殊字符  
          return acc;  
        }, {});  
        return obj
      },
      returnFn(obj){
        let param = obj.param
        const { userId } = this.recordUserInfo;
        param = {
          ...param,
          ...this.submitParams,
          accountId: this.$common.getKeyVal('user', 'accountId', true),
          userId: userId,
          activityType: this.activitytype,
          formTemplateWriteType: 5, // 3 病例征集流程提交记录 4 病例征集回访 5 学术调研流程提交记录 6 学术调研回访
          tenantId: this.tenantId,
          channelCode:this.queryParams?.gs || ''
        };
        uni.showLoading({
          title: '提交中'
        });
        this.$api.activity.casecollectsubmitlogformWrite(param, { 'gb-part-tenant-id': this.tenantId }).then(res => {
          uni.hideLoading()
          this.$uniPlugin.toast('提交成功');
          this.mainId = res.data.mainId
          this.noloading = true;
          this.$nextTick(() => {
            setTimeout(() => {
              this.noloading = false;
              this.currentNum = 1
            }, 500)
          })
      }).catch(e => {
        this.noloading = true;

        this.$nextTick(() => {
          setTimeout(() => {
            this.noloading = false;
            this.currentNum = 1
          }, 500)
        })
      });
      }
    },
 }
</script>

<style lang='scss' scoped>
.testing-detail{
  /deep/ .form-template{
    .template-content{
      padding:0;
    }
  }
}
</style>