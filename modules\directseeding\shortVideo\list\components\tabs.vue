<template>
  <scroll-view :scroll-y="true" class="tabsbox">
    <!-- <template v-for="(item, index) in tabs">
      <view class="tabsitem">{{item.name}}</view>
    </template> -->
    <navlist :list="tabs" labelkey="name" :updatecount="updatenavcount" :current="activeindex" @change="changeNav"></navlist>

    <!-- 图文 -->
    <!-- {{item.businessType}} -->
    <template v-if="item.businessType == 1">
      <!-- {{item.columnContent.configJson}} -->
      <mp-html :content="item.configJson" />
      <!-- <rich-text v-if="item.columnContent.configJson" :nodes="item.columnContent.configJson"></rich-text> -->
    </template>
    <!-- 附件 -->
    <template v-else-if="item.businessType == 2">
       <title-file :disabled="true" :config="{count: 6, multiSelectCount: 6}" :cData="item.columnContent.configJson"></title-file>
      <!-- <title-file :disabled="true" :config="{count: 6, multiSelectCount: 6}" :c-data="item.columnContent.configJson" /> -->
    </template>
    <!-- 聊天室 -->
    <template v-else-if="item.businessType == 3">
      <!-- <chat-room :main-id="meetingId" /> -->
    </template>
    <!-- 视频回放 -->
    <template v-else-if="item.businessType == 4">
      <video-playback @clickItem="clickPlayBack" :mainId="editid" />
      <!-- <meeting-playback :main-id="meetingId" @clickItem="clickPlayBack" /> -->
    </template>
    <!-- 学术调研 -->
    <template v-if="item.businessType == 5">
      <research :pd-list="item.columnContent.configJson" />
    </template>
    <!-- 病例征集 -->
    <template v-else-if="item.businessType == 6">
      <case-collect :pd-list="item.columnContent.configJson" />
    </template>
  </scroll-view>
</template>

<script>
import research from './research.vue';
import caseCollect from './case-collect.vue';

import videoPlayback from './meeting-playback.vue';
import mpHtml from '@/components/basics/mp-html/mp-html.vue'
// import chatRoom from './components/chat-room.vue'



import navlist from '@/modules/directseeding/components/navlist/index.vue';

export default {
  name: 'tabs',
  components: {
    caseCollect,
    research,
    navlist,
    videoPlayback,
    mpHtml
  },
  props: {
    // tabs: {
    //   type: Array,
    //   default: function() {
    //     return [];
    //   }
    // }
    editid:{
      type:String,
      default:''
    }
  },
  data() {
    return {
      item: {
        // businessType: 1,
        // columnContent: {
        //   configJson: '我是图文内容'
        // }
      },
      tabs:[],

      updatenavcount: 0,
      activeindex: 0
    };
  },
  mounted() {
    // this.item.businessType = 1;
    // this.item.
    if(this.editid){
      console.log('editid',this.editid)
      this.meetingcolumnQueryList();
    }

  },
  watch:{
    editid(n){
      // setTimeout(() => {
      //   console.log('n',n)
      //   this.meetingcolumnQueryList()
      // },500)
    }
  },
  methods: {
    clickPlayBack(obj){
      console.log(obj)
      this.$emit('updaterow',obj);
    },
    changeNav(index) {
      console.log(index);
      this.item = this.tabs[index]
      // this.businessType =
    },
    // 获取互动栏目
    async meetingcolumnQueryList() {
      // const res = await this.$api.meeting.cloudClassroom({ mainId: this.editid });
      const res = await this.$api.cloudClassroom.meetingcolumnQueryList({ mainId: this.editid })
      this.tabs = res.data
        .sort((a, b) => a - b)
        .map((item, index) => {
          if (item.businessType == 3) {
            this.openChat = true;
          }
          if (index === 0) {
            this.curIndex = index.toString();
          }
          return {
            ...item,
            key: index.toString()
          };
        })
        .filter(item => item.businessType != 3);


        // this.tabs.push({
        //   ...this.tabs[0],
        //   businessType:1,
        // })
         this.item =this.tabs[0];
    }


  }
};
</script>

<style lang="scss" scoped>
  .tabsbox{
    overflow-y: auto;
    height: 80vh;
  }
</style>
