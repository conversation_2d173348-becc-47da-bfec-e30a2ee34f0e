<template>
  <!--  <scroll-view
    :style="{
      height: height
    }"
    :scroll-y="true"
    scroll-with-animation
    :scroll-into-view="scrollintotargetview"

  > -->
  <!-- top="92" -->
  <!-- <view > -->
  <scroll-refresh :fixed="true" :bgcolor="'#f0f0f0'" top="0" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
    <view class="main-content">
      <template v-if="updatenav">
        <navlist :list="navlist" :updatecount="updatenavcount" :current="activeindex" @change="changeNav"></navlist>
      </template>
      <!-- <template v-else-if="activeindex == 2"> -->
        <title-search :value="searchinput" @update="searchFn"></title-search>
      <!-- </template> -->
      <!-- {{activeindex}} -->
      <!-- 待发布 -->
      <template v-if="activeindex == 0"></template>
      <!-- 待审核 -->
      <template v-else-if="activeindex == 1"></template>
      <!-- 审核通过 -->

      <!-- 审核不通过 -->
      <template v-else-if="activeindex == 3">
        <title-timer @query="queryTimer" @toggle="toggleTimer" @cannel="cannelTimer"></title-timer>
        <!-- <case-list-item-two :list="caselist"></case-list-item-two> -->
      </template>

      <case-list-item @remove="removeitem" @next="nextStep" :type="activeindex" :list="caselist"></case-list-item>

      <!-- <nomore  v-if="caselist.length == 0"></nomore> -->
    </view>
  </scroll-refresh>
  <!-- </view> -->

  <!-- </scroll-view> -->
</template>

<script>
import navlist from '@/modules/activity/components/navlist/index.vue';
import caseListItem from './listitem.vue';
// import nomore from '@/modules/activity/components/nomore/index.vue';
import titleSearch from '@/modules/activity/components/title-search/index.vue';

import titleTimer from '@/modules/activity/components/title-timer/index.vue';

export default {
  name: 'caselist',
  components: {
    navlist,
    caseListItem,
    // nomore,
    titleSearch,
    titleTimer
  },
  props: {
    tenantId: {
      type: String,
      default: '1'
    },
    height: {
      type: String,
      default: '100vh'
    },
    pageid: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      updatenav: true,
      updatenavcount: 0,
      auditStatus: 4,
      isInit: false, // 列表是否已经初始化
      mescroll: null, // mescroll实例对象
      downOption: {
        auto: false // 不自动加载
      },
      upOption: {
        auto: false // 不自动加载
        // use:false,
      },
      searchinput: '',
      // 1 2
      moretype: 2,
      caselist: [
        // {
        //   title: '张三',
        //   code: '344106',
        //   createTime: new Date().getTime(),
        //   updateTime: new Date().getTime()
        // }
        // {
        //   title: '消炎镇痛',
        //   desc: '患者性别:男，年龄:15岁，患有消炎镇席，现病史;dhdbs，现病时长: udbd在宜宾市第五人民医院心胸',
        //   reason: '拒绝原因: 患者姓名为空，需要填写患者姓名',
        //   createTime: new Date().getTime(),
        //   createTimeinfo: '2022-10-22'
        // }
      ],
      scrollintotargetview: '',
      navlist: [
        {
          label: '待发布',
          id: '4',
          activecolor: 'red',
          type:"waitCommitAudit",
          number:0,
          // number: 0
        },
        {
          label: '待审核',
          id: '1',
          activecolor: '#e6a23c',
          type: 'waitAudit',
          number: 0
          // number: 10
        },
        {
          label: '审核通过',
          id: '2',
          activecolor: '#67c23a',
          number: 0,
          type: 'successAudit'
        },
        {
          label: '审核不通过',
          id: '3',
          type: 'failAudit',
          number: 0
        }
        // ,{
        //   label:"审核不通过",
        //   id:'4'
        // },{
        //   label:"审核不通过",
        //   id:'4'
        // }
      ],
      activeindex: 0,
      noReq: false,
      // createTime:null,
      auditStatus: 4,
      startCreateTime: null,
      endCreateTime: null,
      searchTitle: '',
      timetype: null,
      activitytype:2,
    };
  },
  mounted() {
    this.changeNav(0);
    this.getcount();

    // console.log()
    // this.init()
  },

  watch: {
    updatecount(n) {
      this.changeNav(0);
      this.getcount();
    }
  },

  methods: {
    nextStep({ count = 0 }) {
      // console.log('count',count)
      // count += 1;
      // 重新获取统计
      this.getcount();

      this.changeNav(count);
      console.log(this.activeindex);
      this.updatenavcount += 1;
    },
    // 获取类型统计数量
    getcount() {
      console.log('getcount');
      // casecollectsubmitlogsubmittypecount
      this.$api.activity
        .casecollectsubmitlogtypecount({
          type: 1,
          activityId: this.pageid,
          activityType:this.activitytype
        })
        .then(res => {
          console.log(res);
          if (res.data instanceof Object) {
            for (let i = 0; i < this.navlist.length; i++) {
              console.log(res.data[this.navlist[i].type]);
              if (res.data[this.navlist[i].type]) {
                this.navlist[i].number = res.data[this.navlist[i].type];
              } else {
                this.navlist[i].number = 0;
              }
            }

            // console.log(this.navlist)
            // this.updatenav = false;
            // this.$nextTick(() => {
            //   this.updatenav = true;
            // })

            // this.$forceUpdate()
          }
        });
    },
    removeitem(idx) {
      this.caselist.splice(idx, 1);
    },
    queryTimer(obj) {
      console.log(obj.item);
      if (obj.item.starttimer) {
        this.startCreateTime = obj.item.starttimer;
        this.endCreateTime = obj.item.endtimer;
        this.timetype = null;
        // this.createTime = obj.item.starttimer + '-' + obj.item.endtimer;
      } else {
        this.startCreateTime = null;
        this.endCreateTime = null;
        this.timetype = 'all';
        // this.createTime = null;
      }
      this.mescroll.resetUpScroll();
    },
    // 获取本月时间
    getTime() {
      let date = new Date();

      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let days = date.getDate();
      let nowtimer = date.getTime();

      // 本月
      let str = year + '-' + month + '-' + '01' + '  ' + '00:00:00';
      // let lastmonth = month + 1;
      let endstr;
      if (month + 1 > 12) {
        // lastmonth = '01'
        endstr = year + 1 + '-' + '01' + '-' + '01' + '  ' + '00:00:00';
      } else {
        endstr = year + '-' + (month + 1) + '-' + '01' + '   ' + '00:00:00';
      }

      // let startimer = new Date(str).getTime()
      // let endtimer = new Date(endstr).getTime()

      this.startCreateTime = str;
      this.endCreateTime = endstr;
      // return startimer + '-' + endtimer;
    },
    cannelTimer() {
      this.mescroll.lockDownScroll(false);
      this.mescroll.lockUpScroll(false);
      // this.noReq = false;
    },
    toggleTimer(obj) {
      if (obj.item.type == 'other') {
        console.log('进来了');
        this.mescroll.lockDownScroll(true);
        this.mescroll.lockUpScroll(true);
        // this.noReq = true;
        // this.upOption.use = false;
      }
      console.log('obj', obj);
    },

    init(val) {
      this.$nextTick(() => {
        this.isInit = true; // 标记为true
        // this.regForm.search = ''
        this.mescroll.triggerDownScroll();
      });
    },
    scrollInit(scroll) {
      console.log('scroll', scroll);
      scroll.optUp.page.num = 1;
      scroll.optUp.page.size = 7;
      this.mescroll = scroll;
    },
    returnFn(obj) {
      const that = this;
      function queryPage(pageNum, pageSize, fn) {
        if (!that.startCreateTime && that.timetype != 'all') {
          that.getTime();
          // that.createTime =
        }
        const param = {
          current: pageNum,
          size: pageSize,
          condition: {
            // openStatus:2,
            auditStatus: that.auditStatus,
            type: 1, // 流程类型：1-征集，2-回访
            activityId: that.pageid,
            activityType:that.activitytype

            // createTime:that.createTime,
          }
        };

        if (that.activeindex == 3) {
          param.condition.startCreateTime = that.startCreateTime;
          param.condition.endCreateTime = that.endCreateTime;

          // startCreateTime:that.startCreateTime,
          // endCreateTime:that.endCreateTime
        }

        if (that.activeindex == 2 && that.searchTitle != '') {
          param.condition.title = that.searchTitle;
        }

        // if (that.regForm.search) {
        //   param.condition.title = that.regForm.search
        // }
        that.$api.activity.casecollectsubmitlogquerylist(param, { 'gb-part-tenant-id': that.tenantId }).then(res => {
          if (res && res.data.records) {
            for (const a in res.data.records) {
              const data = res.data.records[a];
              data.endTimeText = that.$common.formatDate(new Date(data.endTime), 'yyyy-MM-dd HH:mm');
              data.updateTimeText = that.$common.formatDate(new Date(data.updateTime), 'yyyy-MM-dd HH:mm');
              data.progressText = (data.progress - 0) * 100;
              data.createTimeText = that.$common.formatDate(new Date(data.createTime), 'yyyy-MM-dd HH:mm');
              data.issueDateText = that.$common.formatDate(new Date(data.issueDate), 'yyyy-MM-dd HH:mm');
            }
            // debugger
            fn(res.data.records);
          }
        });
      }
      setTimeout(function() {
        that.getcount();
        queryPage(obj.pageNum, obj.pageSize, data => {
          if (obj.pageNum === 1) {
            that.caselist = [];
          }
          // data = [{}]
          that.caselist = that.caselist.concat(data);
          obj.successCallback && obj.successCallback(data || []);
        });
      }, that.$constant.noun.scrollRefreshTime);
      // obj.successCallback && obj.successCallback(data || [])
    },
    searchFn(obj) {
      this.caselist = [];
      this.searchTitle = obj.value;
      this.init();
      // console.log('obj',obj)
    },
    changeNav(index) {
      this.activeindex = index;

      this.auditStatus = this.navlist[index].id;

      this.caselist = [];
      this.init();

      // if (index == 0) {
      //   this.caselist = [];
      // } else if (index == 1) {
      //   this.caselist = [];
      // } else if (index == 2) {
      //   this.caselist = []
      //   // this.caselist = [
      //   //   {
      //   //     title: '张三',
      //   //     code: '344106',
      //   //     createTime: new Date().getTime(),
      //   //     updateTime: new Date().getTime()
      //   //   }
      //   // ];
      // } else if (index == 3) {
      //   // this.caselist = [
      //   //   {
      //   //     title: '消炎镇痛',
      //   //     desc: '患者性别:男，年龄:15岁，患有消炎镇席，现病史;dhdbs，现病时长: udbd在宜宾市第五人民医院心胸',
      //   //     reason: '拒绝原因: 患者姓名为空，需要填写患者姓名',
      //   //     createTime: new Date().getTime(),
      //   //     createTimeinfo: '2022-10-22'
      //   //   }
      //   // ];
      // }
    }
  }
};
</script>

<style lang="scss" scoped>
.main-content {
  background-color: #f0f0f0;
}
</style>
