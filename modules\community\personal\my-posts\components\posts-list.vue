<template>
  <view class="body-main">
    <scroll-refresh
      style="height: 100%"
      :isShowEmptySwitch="true"
      :fixed="false"
      :isAbsolute="false"
      :up="upOption"
      :down="downOption"
      @returnFn="returnFn"
      @scrollInit="scrollInit"
    >
      <view class="content">
        <view class="content-main">
          <nui-list
            class="nui-list"
            :indexlist="indexlist"
            owner
            :posts-params="postsParams"
            :isShowGambit="true"
            @cateClick="cateClick"
            @del="del"
            @edit="edit"
            :isShowBtn="isShowBtn"
          ></nui-list>
        </view>
      </view>
    </scroll-refresh>
  </view>
</template>

<script>

import { mapState } from 'vuex'
import nuiList from '@/components/community/nui-list/nui-list.vue'
export default {
  components: {
    nuiList
  },
  props: {
    params: {
      type: Object,
      default: () => {
        return null
      }
    },
    isShowBtn:{
      type: <PERSON><PERSON><PERSON>,
      default: true
    }
  },
  data() {
    return {
      file_ctx: this.file_ctx,
      mescroll: null, // mescroll实例对象
      downOption: {
        auto: false // 不自动加载
      },
      upOption: {
        auto: false, // 不自动加载
        empty: {
          top: 0,
          zIndex: 999,
        }
      },
      isInit: false, // 列表是否已经初始化
      indexlist: []
    }
  },
  computed: {
    ...mapState('user', {
      accountId: state => state.accountId
    }),
    postsParams() {
      const { name, ...defaultParams } = this.params
      return {
        mode: 'personal',
        ...defaultParams
      }
    }
  },
  methods: {
    del(data) {
      this.$uniPlugin.modal('', '确认删除该帖子？', {
        showCancel: true, // 是否显示取消按钮，默认为 true
        cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
        cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
        confirmText: '确定', //  确定按钮的文字，默认为"确定"，最多 4 个字符
        confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
        fn: async (n) => {
          if (n) {
            const res = await this.$api.postmessage.postmessageDeleteOne({ id: data.id })
            this.$uniPlugin.toast(res.msg)
            this.init()
          }
        }
      })
    },
    edit(data) {
      this.navtoGo('PostsEdit', { id: data.id })
    },
    cateClick(data) {
      this.navtoGo('Circle', { cid: data.circleClassifyId })
    },
    navtoGo(url, obj = {}) {
      this.$navto.push(url, obj)
    },
    init() {
      this.$nextTick(() => {
        this.mescroll.triggerDownScroll()
      })
    },
    scrollInit(scroll) {
      scroll.optUp.page.num = 1
      scroll.optUp.page.size = 10
      this.mescroll = scroll
    },
    returnFn(obj) {
      const that = this
      setTimeout(function () {
        const { name, ...defaultParams } = that.params
        let params = {
          current: obj.pageNum,
          size: obj.pageSize,
          condition: {
            accountId: that.accountId,
            orderByPutawayTime: 1,
            ...defaultParams
          }
        }
        that.$ext.community.postmessageQueryPage(params).then(res => {
          let data = res.data.records || []
          data = data.map(item => {
            return {
              ...item,
              topicIdsArr:item.topicIds && JSON.parse(item.topicIds) || []
            }
          })
          if (obj.pageNum === 1) {
            that.indexlist = []
          }
          that.indexlist = [...that.indexlist, ...data]
          obj.successCallback && obj.successCallback(data)
        })
      }, that.$constant.noun.scrollRefreshTime)

    },
  }
}
</script>

<style lang="scss" scoped>
.body-main {
  height: 100%;
  overflow-y: auto;
  background-color: #fff;
  padding-bottom: env(safe-area-inset-bottom);
  padding-bottom: constant(safe-area-inset-bottom);
  box-sizing: border-box;
  .content {
    box-sizing: border-box;
    &-main {
      box-sizing: border-box;
    }
  }
}
</style>
