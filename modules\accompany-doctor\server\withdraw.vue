<template>
  <view class="page">
    <view class="withdrawTop">
      <view class="topTitle">提现金额</view>
      <view class="inputBox">
        <view class="sign">￥</view>
        <input class="inputPay" type="number" v-model="amount" />
        <view class="all" @click="amount = balance">全部提现</view>
      </view>
      <view class="inputline"></view>
      <view class="prompt">可提现金额{{balance}}元</view>
    </view>
    <view class="withdrawMiddle">
      <view class="middleTitle">提现须知</view>
      <view class="middleContent">
        <view class="">1、提现后请耐心等待平台审核</view>
        <view class="">2、到账渠道为关联的通联账户</view>
      </view>
    </view>
    <view class="botButton" @click="withdraw" :style="{background:amount ? '#00B484' : '#C9CCD4'}">提交申请</view>
  </view>
</template>

<script>
  import common from '@/common/util/main';
  export default {
    data(){
      return {
        balance:'',
        providerId:'',
        amount:0
      }
    },
    async onLoad(){
      const codeUserInfo = common.getKeyVal('user', 'codeUserInfo',true);
      let {data:id} = await this.$api.accompanyDoctor.accompanyproviderUserProvider({userId:codeUserInfo.id})
      this.providerId = id;
      this.loadData();
    },
    methods:{
      async loadData(){
        let queryOne = await this.$api.accompanyDoctor.accompanyproviderQueryOne({id:this.providerId})
        this.balance = +queryOne?.data?.balance / 100;
      },
      async withdraw(){
        if(!this.amount) return
        let {data:id} = await this.$api.accompanyDoctor.accompanypayoutInsert({
          providerId:this.providerId,
          amount:(+this.amount) * 100
        })
        uni.showModal({
          title:'申请成功',
          content:"已申请提现，请等待审核",
          showCancel:false,
          success() {
            this.loadData();
          }
        })
      }
    }
  }
</script>

<style lang="scss">
  .page{
    padding: 48rpx 32rpx;
    box-sizing: border-box;
    width: 100vw;
    background: #F4F6FA;
    height: 100vh;
    overflow: scroll;
    .withdrawTop{
      width: 686rpx;
      height: 278rpx;
      background: #FFFFFF;
      border-radius: 16rpx;
      padding: 32rpx 24rpx;
      box-sizing: border-box;
      .topTitle{
        font-weight: 600;
        font-size: 32rpx;
        color: #1D2029;
      }
      .inputBox{
        width: 100%;
        height: 66rpx;
        margin-top: 24rpx;
        position: relative;
        .sign{
          font-weight: 600;
          font-size: 32rpx;
          color: #1D2029;
          position: absolute;
          top: 16rpx;
        }
        .all{
          font-weight: 600;
          font-size: 28rpx;
          color: #00B484;
          position: absolute;
          top: 18rpx;
          right: 0;
        }
        .inputPay{
          width: 100%;
          height: 66rpx;
          box-sizing: border-box;
          padding: 0 112rpx 0 32rpx;
          font-weight: 600;
          font-size: 48rpx;
          color: #1D2029;
        }
      }
      .inputline{
        background-color: #EAE6E0;
        height: 2rpx;
        width: 100%;
        margin-top: 24rpx;
        margin-bottom: 20rpx;
      }
      .prompt{
        font-weight: 400;
        font-size: 24rpx;
        color: #4E5569;
      }
    }
    .withdrawMiddle{
      width: 100%;
      background: #FFFFFF;
      padding: 32rpx 24rpx;
      margin-top: 20rpx;
      overflow: hidden;
      box-sizing: border-box;
      .middleTitle{
        font-weight: 600;
        font-size: 28rpx;
        color: #1D2029;
      }
      .middleContent{
        font-weight: 400;
        font-size: 26rpx;
        color: #4E5569;
        margin-top: 24rpx;
      }
    }
    .botButton{
      width: 690rpx;
      height: 88rpx;
      border-radius: 44rpx;
      margin-top: 32rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #FFFFFF;
      text-align: center;
      line-height: 88rpx;
    }
  }
</style>
