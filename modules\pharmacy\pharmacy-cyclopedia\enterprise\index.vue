<template>
  <page>
    <view slot="content" class="body-main">
      <!-- 分页加在这里 -->
      <view class="m-main-body">
        <scroll-refresh class="scroll-refresh-main" :isShowEmptySwitch="true" :fixed="false" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
          <view class="my-data">
            <!-- <view class="my-bg"> -->
            <view class="my-header">
              <view :style="'height:' + statusBarHeight + 'px;'"></view>
              <view class="top-nav">
                <view class="top-nav-l" @click="back"><image mode="aspectFit" :src="file_ctx + 'static/image/business/hulu-v2/icon-circle-left-arrows.png'" class="header-search-img"/></view>
              </view>
            </view>
            <view class="my-enterprise">
              <view class="top-half">
                <view class="top-half-l"><image :src="detailsObj.logo" @click="clickImage(detailsObj.logo)"></image></view>
                <view class="top-half-r">
                  <view class="top-half-title">{{ detailsObj.name }}</view>
                  <view class="top-half-tab" v-if="detailsObj.tags">
                    <template v-if="detailsObj.tags && detailsObj.tags.split(',').length > 1">
                      <view 
                        v-for="item in detailsObj.tags.split(',')"
                        :key="item.id"
                        class="tab-item"
                      >
                        {{ item }}
                      </view>
                    </template>
                    <view v-else class="tab-item">{{ detailsObj.tags }}</view>
                  </view>
                </view>
              </view>
            </view>
            <!-- </view> -->
          </view>
          <view class="enterprise-content">

            <!-- 获得荣誉 -->
            <view class="honor-content" v-if="detailsObj.honor">
              <view class="bottom-half-title">
                <view class="img"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-enterprise-glory.png'"></image></view>
                <view class="text">获取荣誉</view>
              </view>
              <view class="bottom-half-info">
                <view class="honor-name1" v-for="(item,index) in detailsObj.honor" :key="index">· {{ item.value }}</view>
              </view>
            </view>

            <!-- 公司介绍 -->
            <view class="company-introduce" :style="detailsObj.honor && detailsObj.honor.length ? 'margin:18rpx 0 20rpx;border-radius:16rpx;' : 'margin:0 0 20rpx;border-radius:0rpx;'">
              <view class="company-title">公司介绍</view>
              <!-- <view class="company-info" :class="moreFlag?'company-info info-active':'company-info'">广州星群(药业)股份有限公司是广药集团控广州星群(药业)股份有限公司是广药集团控广州星群(药业)股份有限公司是广药集团控广州星群(药业)股份有限公司是广药集团控广州星群(药业)股份有限公司是广药集团控</view>
              <view class="company-more" @click="handleClickMore">
                <view class="text">{{ moreFlag ? '收起': '查看更多'}}</view>
                <view :class="moreFlag ? 'img rotate':'img'"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-border-bottom.png'"></image></view>
              </view> -->
              <!-- <view class="company-info" :class="{lineclamp3:detailsObj.isAll}">{{ detailsObj.introduce }}</view> -->
              <view class="company-info" :class="{lineclamp3:detailsObj.isAll}"><rich-text class="name" :nodes="detailsObj.introduce" preview :style="{whiteSpace: 'pre-wrap',}"></rich-text></view>
              <view class="company-more" v-if="detailsObj.isMore">
                <view v-if="detailsObj.isAll" @click="handleClickType(detailsObj)">查看更多</view>
                <view v-else @click="handleClickType(detailsObj)">收起</view>
                <view :class="detailsObj.isAll ? 'img':'img rotate'"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-border-bottom.png'"></image></view>
              </view>
            </view>

            <view class="content">
              <view class="tabs" :style="{backgroundImage:'url(' + file_ctx + flagImg + ')','background-size':'94vw','background-repeat':'no-repeat'}">
                <view 
                  v-for="(item,index) in tabsList"
                  :key="item.id"
                  class="nav-item"
                  :style="{paddingTop:currentActive !== index && '34rpx'}"
                  @click="handleTabClick(index)"
                >
                  <view :class="currentActive == index ? 'text active' : 'text'">{{ item.name }}</view>
                  <view class="img" v-if="currentActive == index"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-active-bottom.png'"></image></view>
                </view>
              </view>
              <view class="tabs-content" :style="{backgroundColor:contentList.length > 0 ? '#fff' : 'transparent'}">
                <view class="content-item" v-for="item in contentList" :key="item.id" @click="handleContentJump(item)">
                  <view class="item-box" :style="{alignItems: currentActive == 0 && 'center'}">
                    <view class="img">
                      <!-- <template v-if="item.banner && item.banner.split(',').length > 1">
                        <image :src="isDomainUrl(item.banner.split(',')[0])||isDomainUrl(item.imagesPath.split(',')[0])" :mode="item.id == '2022757383332663297' ? 'widthFix' : 'aspectFit'"></image>
                      </template> -->
                      <!-- <template v-else-if="item.banner && item.banner.split(',').length == 1 || isDomainUrl(item.imagesPath.split(',')[0])"> -->
                      <template>
                        <!-- <image :src="isDomainUrl(item.banner)||isDomainUrl(item.imagesPath.split(',')[0])" :mode="item.id == '2022757383332663297' ? 'widthFix' : 'aspectFit'"></image> -->
                        <image :src="isDomainUrl(item.listCover)||isDomainUrl(item.imagesPath.split(',')[0])" :mode="item.listCover ? '' : 'aspectFit'"></image>
                      </template>
                    </view>
                    <view class="info">
                      <view class="title" v-if="currentActive == 0">{{ item.name }}</view>
                      <view class="titles" v-else>{{ item.title }}</view>
                      <view class="name" v-if="currentActive == 0"><span v-if="item.standard">{{ '规格：' + item.standard }}</span></view>
                      <view class="name" v-else>{{ item.intro }}</view>
                    </view>
                  </view>
                  <view class="item-right" v-if="item.productExternalLinksSwitch == 1" @click.prevent.stop="handleBuy(item)">前往购买</view>
                </view>
              </view>
            </view>
          </view>
        </scroll-refresh>
        <!-- 购买渠道弹窗 -->
        <uni-popup ref="buyChannelPopup" type="bottom">
          <view class="buy-channel">
            <view class="title">购买渠道</view>
            <!-- <view class="img" @click="$refs.buyChannelPopup.close()"><uni-icons :size="30" color="#fff" type="close" /></view> -->
            <view class="buy-content">
              <view class="buy-item" v-for="(item,index) in buyList" :key="index">
                <view class="buy-item-l">
                  <view class="buy-img"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-pharmacy-enterprise-link.png'"></image></view>
                  <view class="buy-text">{{item.pageName}}</view>
                </view>
                <view class="buy-item-r">
                  <button class="buy-item-btn" @click="handleClickItemBuy(item)">前往购买</button>
                </view>   
              </view>
            </view>
          </view>
        </uni-popup>
      </view>
    </view>
  </page>
</template>

<script>
  import { mapState } from 'vuex'
  import { isDomainUrl } from '@/utils/index.js'
  import uniPopup from '@/components/uni/uni-popup'
  // import UniIcons from '@/components/uni/uni-icons/uni-icons'
  export default {
    components:{
      uniPopup,
      // UniIcons
    },
    data(){
      return{
        file_ctx: this.file_ctx,
        tagList:[
          {id:1,name:'创新型生物医药公司'},
          {id:2,name:'高新企业'},
        ],
        tabsList:[
          {id:1,name:'产品动态'},
          {id:2,name:'企业动态'},
        ],
        productImg:'static/image/business/pharmacy-cyclopedia/productBox.png',
        enterpriseImg:'static/image/business/pharmacy-cyclopedia/enterpriseBox.png',
        flagImg:'static/image/business/pharmacy-cyclopedia/productBox.png',
        // contentList:[
        //   {id:1,title:'盐酸贝尼地平片',name:'规格：片剂；2mg/4mg/8mg/片'},
        //   {id:2,title:'牛磺熊去氧胆酸胶囊',name:'规格：250mg*20粒/盒'},
        // ],
        currentActive:0,
        $constant: this.$constant,
        mescroll: null, // mescroll实例对象
        downOption: {
          auto: false // 不自动加载
        },
        upOption: {
          auto: false, // 不自动加载
          empty: {
            top: 0,
            zIndex: 999,
          },
          onScroll: true
        },
        contentList:[],
        detailsObj:{},
        channelCode:'',
        statusBarHeight: 0,
        moreFlag:false,
        buyList:[],
        productId:null,
        labelValue:null,
      }
    },
    watch: {
      currentActive: {
        handler () {
          this.contentList = []
          this.init()
        }
      },

    },
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId,
        publishInfo:state => state.publishInfo,
        curSelectUserInfo:state => state.curSelectUserInfo
      }),
    },
    async onLoad(option){
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
      const query = this.$validate.isNull(this.$Route.query) ? option : this.$Route.query
      // console.log(query,'query')
      this.channelCode = query.gs
      this.labelValue = query?.labelValue
      this.productId = query?.id
      if(query.brandId){
        await this.getEnterpriseQueryOne(query.brandId)
      }
      this.$nextTick(() => {
        this.init()
      })
    },
    onShareAppMessage: function (res) {  
      if (res.from === 'share') {  
        // 来自页面内转发按钮  
        console.log(res.target);  
      }
      this.queryAndCreate(3)
      return {
        title: `${this.publishInfo.name}${this.labelValue}`, //分享的名称
        path: 'modules/pharmacy/pharmacy-cyclopedia/index?gs='+ encodeURIComponent(this.channelCode),
        mpId:this.$appId, //此处配置微信小程序的AppId
      }
    },
    mounted(){},
    methods:{
      // #ifdef MP-WEIXIN
      handleClickTrack(type){
        getApp().globalData.sensors.track("PopupClick",
          {
            'page_name' : '用药说明书企业动态',
            'popup_id' : 'buyChannelPopup',
            'popup_name' : '购买渠道弹窗',
            'click_type' : type == 1 ? '进入弹窗' : '取消弹窗',
          }
        ) 
      },
      // #endif
      async queryAndCreate(type){
        let { phone = '' } =  this.curSelectUserInfo || {}
        let params = {
          accountId:this.accountId,
          phone: phone,
          productId:this.productId,
          shareType:type,  //	分享类型：1分享按钮2保存二维码3微信好友分享按钮
        }
        await this.$api.drugBook.queryAndCreate(params)
      },
      handleCloseChannelPopup(){
        
      },
      handleClickItemBuy(item){
        if(item.type == 1){
          this.$navto.push('WebHtmlView', { src: item.path })
        } else {
          this.handleJumpExternal(item.appId,item.path)
        }
      },
      handleBuy(item){
        this.buyList = item.productExternalLinks
        if(this.buyList.length > 1){
          this.$refs.buyChannelPopup.open()
          // #ifdef MP-WEIXIN
          this.handleClickTrack(1)
          // #endif
        } else {
          if(item.productExternalLinks[0].type == 1){
            this.$navto.push('WebHtmlView', { src: item.productExternalLinks[0].path })
          } else {
            this.handleJumpExternal(item.productExternalLinks[0].appId,item.productExternalLinks[0].path)
          }
        }
      },
      isDomainUrl,
      back(){
        this.$navto.back(1)
      },

      // 跳转外部小程序
      handleJumpExternal(appId,path){
        this.$uniPlugin.navigateToMiniProgram({
          appId:appId,
          path:path,
          envVersion: 'release',
          extraData: {}
        }, (res) => {
          resolve(true)
        }, (err) => {
          resolve(false)
        })
      },

      handleClickMore(){
        this.moreFlag = !this.moreFlag
      },

      handleTabClick(index){
        this.currentActive = index
        if(index == 0){
          this.flagImg = this.productImg
        } else {
          this.flagImg = this.enterpriseImg
        }
      },

      clickImage(url){
        uni.previewImage({
          urls: [url], //需要预览的图片http链接列表，多张的时候，url直接写在后面就行了
          current: [url][0], // 当前显示图片的http链接，默认是第一个
          success: function(res) {},
          fail: function(res) {},
          complete: function(res) {},
        })
      },

      handleClickType(obj){
        obj.isAll = !obj.isAll
      },

      handleContentJump(item){ //内容跳转
        if(this.currentActive == 0){
          if(item.businessType == 1){
            // 获取当前页面栈
            const pages = getCurrentPages();
            this.$api.drugBook.getEiproductQueryOne({id:item.id}).then(res=>{
              if(pages.length > 4){
                // this.$navto.replaceAll('LookMore', {...item,qrcodePath:res.data.qrcodePath,jumpName:'企业介绍',gs:this.channelCode})
                this.$navto.replaceAll('LookMore', {gs:res.data.code})
              } else {
                this.$navto.push('LookMore',{gs:res.data.code})
              }
            })
          } else {
            this.handleBuy(item)
          }
        } else {
          this.$navto.push('PostsDetail', {id: item.id,isShowBtn:false})
        }
      },
      
      async getEnterpriseQueryOne(id){
        const res = await this.$api.drugBook.getEnterpriseQueryOne({id})
        if(res.data.introduce.length > 80){
          res.data.isMore = true
          res.data.isAll = true
        } else {
          res.data.isMore = false
          res.data.isAll = false
        }
        this.detailsObj = { 
          ...res.data,
          logo:isDomainUrl(res.data.logo),
          honor:(res.data?.honor?.length && JSON.parse(res.data.honor)) || '',
          // honor:'',
        }
      },

      returnFn(obj) {
        const that = this
        setTimeout(async function () {
          let params = {
            current: obj.pageNum,
            size: obj.pageSize,
          }
          let res = null
          if(that.currentActive == 0){
            res = await that.$api.drugBook.getProductQueryPage({...params,condition:{brandId:that.detailsObj.id,shelfStatus:1}})
            res.data.records = res.data.records.filter(item=>{
              if(item.brandViewSwitch == 1){
                return item
              }
            })
          } else {
            res = await that.$ext.community.postmessageQueryPage({...params,
            condition:{
              entryType:4,
              brandId:that.detailsObj.id,
              processStatus:2,
              putawayStatus:1,
            }})
          }
          let data = res.data.records
          if (obj.pageNum === 1) {
            that.contentList = []
          }
          that.contentList = [...that.contentList, ...data]
          obj.successCallback && obj.successCallback(data)
        }, that.$constant.noun.scrollRefreshTime)
      },
      scrollInit(scroll) {
        scroll.optUp.page.num = 1
        scroll.optUp.page.size = 10
        this.mescroll = scroll
      },
      init() {
        this.$nextTick(() => {
          this.mescroll.triggerDownScroll()
        })
      },
    },
 }
</script>

<style lang='scss' scoped>
.body-main{
  height: 100%;
  .m-main-body{
    height: 100%;
    position: relative;
    .scroll-refresh-main{
      height: 100%;
      /deep/ .mescroll-empty-box{
        min-height: 0%;
      }
      .my-data{
        position: relative;
        // z-index: -99;
        // .my-bg {
          // width: 100%;
          // height: 458rpx;
          // background: #AAE0D2;
        .my-header{
          position: fixed;
          top: 0;
          width: 100%;
          z-index: 999;
          background: #AAE0D2;
          .top-nav{
            width: calc(100% - 56rpx);
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 40px;
            line-height: 40px;
            padding: 0 32rpx 0 24rpx;
            .top-nav-l{
              display: flex;
              width: 48rpx;
              height: 48rpx;
              image{
                width: 100%;
                height: 100%;
              }
            }
          }
        }
        .my-enterprise{
          // display: flex;
          // height: 238rpx;
          background: #AAE0D2;
          // border: 2rpx solid #FFFFFF;
          border-top: 2rpx solid #FFFFFF;
          border-left: 2rpx solid #FFFFFF;
          border-right: 2rpx solid #FFFFFF;
          padding:113px 32rpx 0;
          // box-sizing: border-box;
          // padding-top: 113px;
          .top-half{
            display: flex;
            border-radius: 16rpx 16rpx 0rpx 0rpx;
            background: rgba(255,255,255,0.7);
            padding: 40rpx 32rpx 28rpx;
            .top-half-l{
              display: flex;
              flex-shrink: 0;
              width: 104rpx;
              height: 104rpx;
              border-radius: 50%;
              overflow: hidden;
              background-color: #fff;
              image{
                width: 100%;
                height: 100%;
              }
            }
            .top-half-r{
              display: flex;
              flex-direction: column;
              margin-left: 24rpx;
              .top-half-title{
                display: flex;
                align-items: center;
                height: 96rpx;
                width: 494rpx;
                line-height: 48rpx;
                font-weight: 600;
                font-size: 34rpx;
                color: #1D2029;
              }
              .top-half-tab{
                display: flex;
                flex-wrap: wrap;
                margin-top: 16rpx;
                .tab-item{
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  height: 44rpx;
                  background: #FFFFFF;
                  border-radius: 8rpx;
                  border: 1rpx solid #B8DDD3;
                  // margin-right: 12rpx;
                  margin: 0 12rpx 12rpx 0;
                  padding:0 12rpx;
                  font-size: 20rpx;
                  color: #00926B;
                  &:last-child{
                    margin-right: 0;
                  }
                }
              }
            }
          }
        }
        // }
      }
      .enterprise-content{
        // position: absolute;
        // top: 300rpx;
        // margin-top: -262rpx;
        // padding:24upx;
        padding: 0 32rpx 32rpx;
        // .header{
        //   // display: flex;
        //   height: 238rpx;
        //   background: rgba(255,255,255,0.7);
        //   border-radius: 16rpx 16rpx 0rpx 0rpx;
        //   // border: 2rpx solid #FFFFFF;
        //   border-top: 2rpx solid #FFFFFF;
        //   border-left: 2rpx solid #FFFFFF;
        //   border-right: 2rpx solid #FFFFFF;
        //   padding:40rpx 32rpx;
        //   box-sizing: border-box;
        //   .top-half{
        //     display: flex;
        //     .top-half-l{
        //       display: flex;
        //       flex-shrink: 0;
        //       width: 104rpx;
        //       height: 104rpx;
        //       border-radius: 50%;
        //       overflow: hidden;
        //       background-color: #fff;
        //       image{
        //         width: 100%;
        //         height: 100%;
        //       }
        //     }
        //     .top-half-r{
        //       display: flex;
        //       flex-direction: column;
        //       margin-left: 24rpx;
        //       .top-half-title{
        //         display: flex;
        //         align-items: center;
        //         height: 96rpx;
        //         width: 494rpx;
        //         line-height: 48rpx;
        //         font-weight: 600;
        //         font-size: 34rpx;
        //         color: #1D2029;
        //       }
        //       .top-half-tab{
        //         display: flex;
        //         flex-wrap: wrap;
        //         margin-top: 16rpx;
        //         .tab-item{
        //           display: flex;
        //           align-items: center;
        //           justify-content: center;
        //           height: 44rpx;
        //           background: #FFFFFF;
        //           border-radius: 8rpx;
        //           border: 1rpx solid #B8DDD3;
        //           margin-right: 12rpx;
        //           padding:0 12rpx;
        //           font-size: 20rpx;
        //           color: #00926B;
        //           &:last-child{
        //             margin-right: 0;
        //           }
        //         }
        //       }
        //     }
        //   }
        // }
        .honor-content{
          padding:38rpx 32rpx 34rpx;
          background-color: #fff;
          border-radius: 0rpx 0rpx 16rpx 16rpx;
          .bottom-half-title{
            display: flex;
            align-items: center;
            margin-bottom: 22rpx;
            .img{
              width: 24rpx;
              height: 32rpx;
              margin-right: 12rpx;
              image{
                width: 100%;
                height: 100%;
              }
            }
            .text{
              height: 44rpx;
              line-height: 44rpx;
              font-size: 32rpx;
              font-weight: 600;
              color: #1D2029;
            }
          }
          .bottom-half-info{
            .honor-name1{
              font-size: 28rpx;
              color: #1D2029;
              line-height: 48rpx;
            }
          }
        }
        .company-introduce{
          padding:32rpx;
          margin:18rpx 0 20rpx;
          background-color: #fff;
          border-radius: 16rpx;
          .company-title{
            height: 44rpx;
            line-height: 44rpx;
            font-size: 32rpx;
            font-weight: 600;
            color: #1D2029;
            margin-bottom: 24rpx;
          }
          .lineclamp3{				
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 4; 
            -webkit-box-orient: vertical;
            white-space: normal;
            font-size: 28rpx;
            color: #1D2029;
            line-height: 48rpx;
          }
          // .info-content{
          //   // margin-top: 10upx;
          //   overflow: hidden;
          //   text-overflow: ellipsis;
          //   display: -webkit-box;
          //   -webkit-line-clamp: 3;
          //   -webkit-box-orient: vertical;
          //   font-size: 28rpx;
          //   color: #1D2029;
          //   line-height: 48rpx;
          // }
          span{
            color:#00C1B0;
          }
          .active{
            height:auto;
            overflow:visible;
            display: block;
          }
          .img{
            display: flex;
            width: 28rpx;
            height: 28rpx;
            transform: rotate(0deg);
            image{
              width: 100%;
              height: 100%;
            }
          }
          .rotate{
            transform: rotate(180deg);
          }
          .company-info{
            font-size: 28rpx;
            color: #1D2029;
            line-height: 48rpx;
          }
          .company-more{
            display: flex;
            align-items: center;
            justify-content: center;
            color: #868C9C;
            margin-top: 20rpx;
          }
        }
        .content{
          // height: 100%;
          // background-size: 100%;
          .tabs{
            display: flex;
            align-items: center;
            // border-radius: 13rpx 13rpx 0 0;
            // overflow: hidden;
            .nav-item{
              position: relative;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 50%;
              // padding: 25upx 0;
              padding: 21upx 0;
              .text{
                font-size: 25upx;
                color: #2D2F38;
              }
              .img{
                display: flex;
                position: absolute;
                top: 78%;
                width: 38rpx;
                height: 10rpx;
                image{
                  width: 100%;
                  height: 100%;
                }
              }
              // background: #E5E5E5;
              .active{
                font-weight: bold;
                font-size: 29upx;
                color: #2D2F38;
              }
            }
      
          }
          .tabs-content{
            padding:0 24rpx;
            background-color: #fff;
            margin-top: -4rpx;
            border-radius: 0rpx 0rpx 16rpx 16rpx;
            // height: 100%;
            .content-item{
              display: flex;
              align-items: center;
              justify-content: space-between;
              // padding:26upx 20upx;
              padding:32rpx 0;
              border-radius: 13rpx;
              border-bottom: 2rpx solid #EAEBF0;
              .item-box{
                display: flex;
                // align-items: center;
                height: 112rpx;
                .img{
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  width: 160rpx;
                  height: 112rpx;
                  background-color: #fff;
                  margin-right: 24upx;
                  border-radius: 8rpx;
                  border: 1rpx solid #D9DBE0;
                  overflow: hidden;
                  flex-shrink: 0;
                  image{
                    width: 100%;
                    height: 100%;
                  }
                }
                .info{
                  display: flex;
                  flex: 1;
                  flex-direction: column;
                  .title,.titles{
                    height: 40rpx;
                    font-weight: 600;
                    font-size: 28rpx;
                    color: #2D2F38;
                    line-height: 40rpx;
                    margin-bottom: 8upx;
                    display: -webkit-box;
                    -webkit-line-clamp: 1;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    max-width: 300rpx;
                    text-overflow: ellipsis;
                    word-break: break-all;
                  }
                  // .titles{
                  //   margin-bottom: 5rpx;
                  // }
                  .name{
                    // height: 32rpx;
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #2D2F38;
                    line-height: 32rpx;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    word-break: break-all;
                  }
                }
              }
              .item-right{
                margin-right: 10rpx;
                background: #13b38e;
                padding: 10rpx;
                border-radius: 10rpx;
                color: #fff;
                font-size: 20rpx;
              }
              &:last-child{
                border-bottom: 0;
              }
            }
          }
        }
      }
    }
    .buy-channel{
      position: relative;
      padding: 30rpx 30rpx 80rpx;
      border-radius: 15rpx 15rpx 0 0;
      background-color: #f2f2f2;
      .title{
        display: flex;
        align-items: center;
        justify-content: center;
        color:#000;
      }
      // .img{
      //   position: absolute;
      //   top: 0rpx;
      //   right: 30rpx;
      // }
      .buy-content{
        margin-top: 30rpx;
        .buy-item{
          display: flex;
          align-items: center;
          justify-content: space-between;
          background-color: #fff;
          padding: 25rpx 20rpx 25rpx;
          border-radius: 15rpx;
          margin-bottom: 20rpx;
          .buy-item-l{
            display: flex;
            align-items: center;
            .buy-img{
              width: 50rpx;
              height: 50rpx;
              border-radius: 50%;
              margin-right: 10rpx;
              image{
                width: 100%;
                height: 100%;
              }
            }
            .buy-text{}
          }
          .buy-item-r{
            .buy-item-btn{
              display: flex;
              align-items: center;
              justify-content: center;
              flex-shrink: 0;
              color: #fff;
              background: #13b38e;
              font-size: 20rpx;
            }
          }
        }
      }
    }
  }
}
</style>