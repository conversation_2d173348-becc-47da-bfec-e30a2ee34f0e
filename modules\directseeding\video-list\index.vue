<template>
  <page>
    <view slot="content" class="main-container">
      <uni-nav-bar
        @clickLeft="back"
        color="#1d2029"
        :border="false"
        left-icon="left"
        :fixed="false"
        statusBar
        left-width="48rpx"
        right-width="100px"
        backgroundColor="rgba(0,0,0,0)"
      >
        <view class="main-search">
          <search
            v-model="regForm.search"
            :placeholder="searchVal?searchVal:'搜索相关直播名称'"
            :fixed="false"
            search-style="background: #F4F6FA; border-radius: 36rpx; border: 1rpx solid #D9DBE0; padding: 0;height: 64rpx; overflow: hidden;"
            input-style="background: #F4F6FA; height: 64rpx;"
            placeholder-style="color: #A5AAB8;"
            @changeSearch="changeSearch"
          />
        </view>
      </uni-nav-bar>

      <scroll-view scroll-x="true" :scroll-into-view="scrollIntoView" :show-scrollbar='false' :scroll-left='100' v-if="!productId">
      <!-- <view class="mainTabScroll"> -->
        <view class="main-tab-box" ref="tabBox">
          <template v-for="item,index in topTabArr">
            <view :id='"view_" + item.id + ""' class="main-tab" @click="clickTab(index)" :class="activeTabIndex === index ? 'active' : ''" :key="index">
              {{item.name}}
            </view>
          </template>
        </view>
      <!-- </view> -->
      </scroll-view>

      <view class="main-wrapper" :style="{paddingTop:productId?'20rpx':'0'}">
        <scroll-refresh :fixed="false" :up="upOption" :down="downOption" bgColor="rgba(0,0,0,0)"
                        @returnFn="returnFn" @scrollInit="scrollInit">
          <view class="main-content">
            <videoItem :pdList="pdList" :logConditionParams='logConditionParams'></videoItem>
          </view>
        </scroll-refresh>
      </view>

    </view>
  </page>
</template>

<script>
// import MescrollUni from '@/components/uni/mescroll-uni'
import search from '@/components/basics/form/search'
import videoItem from '@/modules/directseeding/components/video-item/index'
import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'

// import lvCascadeSelect from '@/modules/business/hospital/components/lv-cascade-select/index.vue'
// import lxCascadeSelect from '@/modules/business/hospital/components/lx-cascade-select/lx-cascade-select.vue'
// import { getHospitalGrade } from '@/utils/enumeration.js'
// import { selectTabs } from '../mock/list.js'
import { mapState } from 'vuex'
import { isDomainUrl } from '../../../utils'
export default {
  components: {
    // MescrollUni,
    search,
    videoItem,
    uniNavBar
  },
  data() {
    return {
      logConditionParams:{},
      scrollIntoView:'',
      businessType:7,
      topTabArr:[
        {
          name:"全部",
          value:'all',
        },
        // {
        //   label:"全部医院",
        //   value:2,
        // },{
        //   label:"已提交待审核",
        //   value:3,
        //   audit:true,
        //   auditValue:1,
        // },{
        //   label:"审核通过",
        //   value:4,
        //   audit:true,
        //   auditValue:2,
        // },{
        //   label:'审核驳回',
        //   value:5,
        //   audit:true,
        //   auditValue:3,
        // }
      ],
      activeTabIndex:0,
      gradeArray:[
        // {
        //   label:'医院等级',
        //   value:'all'
        // },
        // ...getHospitalGrade()
      ],
      gradeIndex:0,
      // level:0,
      hospitalListText:'全国',
      selectAddressValue:"all",

      selectAddress:[],
      selectAddressLabel:[],
      activeIndex:null,
      // cityValue:'',
      cityOptions:[],
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      $validate: this.$validate,
      mescroll: null, // mescroll实例对象
      downOption: {
        auto: false // 不自动加载
      },
      upOption: {
        auto: false // 不自动加载
      },
      pdList: [], // 列表数据
      isInit: false, // 列表是否已经初始化
      scrollY: 0,
      regForm: {
        search: ''
      },
      liveName:'', //药品说明书传过来
      productId:null,
      searchVal:null,
      // typeofAll: '',
      // field: [],
      // activityId: '',
      // listMode: 'list'
    }
  },
  watch: {
    // 监听下标的变化
  },
  computed:{
    ...mapState('system', {
      temporaryStorage: state => state.temporaryStorage
    }),
    ...mapState('user', {
      // codeUserInfo: state => state.codeUserInfo, // 当前登录用户信息
      curSelectUserInfo: state => state.curSelectUserInfo, // 当前选中的用户信息
      // curSelectUserInfoId: state => state.curSelectUserInfoId, // 当前选中的档案的ID
    }),
  },
  onShow(){
    console.log("this.temporaryStorage",this.temporaryStorage)
    // this.getMeetingQueryPage();
  },
  onLoad(paramsObj) {
    const query = this.$Route.query
    console.log(query,'query000000')
    if (!this.$validate.isNull(query)) {
      this.activityId = query.activityId
      this.listMode = query.listMode
      this.liveName = query?.name
      this.productId = query?.productId
      this.searchVal = query?.searchVal
      this.businessType = query.businessType
      let title = query?.title
      if(title){
        uni.setNavigationBarTitle({
          title: title
        });
      }
    }
    const addressOptions = this.$common.getKeyVal('address', 'addressData', true)
    // console.log('liveName',this.liveName)

    this.getVideoTabList()
    this.init()
    // #ifdef MP-WEIXIN
    this.handleClickTrack('OperationDetailsPageView')
    // #endif
  },
  onUnload(){
    // #ifdef MP-WEIXIN
    this.handleClickTrack('EndOperationDetailsPageView')
    // #endif
  },
  mounted() {
  },
  methods: {
    // #ifdef MP-WEIXIN
    handleClickTrack(type){
      let pages = getCurrentPages()
      let current = pages[pages.length - 1]; // 获取到当前页面的信息
      let pageInfo = __wxConfig.page && __wxConfig.page[`${current.route}.html`] || ''; // 内部属性
      getApp().globalData.sensors.track(type,
        {
          'page_name' : pageInfo?.window?.navigationBarTitleText || '',
          'first_operation_name' : pageInfo?.window?.navigationBarTitleText || '',
          'second_operation_name' : '',
        }
      )
    },
    // #endif
    back() {
      let pages = getCurrentPages() // 获取栈实例
      if (pages.length > 1) {
        this.$navto.back()
      } else {
        this.$navto.replaceAll('Index')
      }
    },
    sheep(timer) {

      return new Promise((resolve,reject) => {
        setTimeout(() => {
          resolve(true)
        },timer)
      })
    },
    async initScrollView(id) {
      await this.sheep(200)
      this.scrollIntoView = 'view_' + id + '';
      setTimeout(() => {
        this.scrollIntoView = null;
      },500)
    },
    clickTab(index) {
      // 移动tab
      // this.initScrollView(this.topTabArr[index].id);
      this.activeTabIndex = index;
      if(index == 0){
        this.businessType = 7 //统一过滤类型为7
      // this.businessType = 'all'
      // } else if(this.topTabArr[index].name == '名医直播间') {
      //   this.businessType = 8
      // } else {
      //   this.businessType = this.topTabArr[index].businessType
      // }
      } else {
        this.businessType = this.topTabArr[index].businessType
      }

      this.init()
    },
    moveTab(index){
      console.log('ref',this.$refs.tabBox);
    },
    // 获取Tab
    async getVideoTabList(){
      const res = await this.$api.cloudClassroom.getMeetingclassifyQueryList({
        // businessType:7,// 直播类型
      })
	  // 过滤直播类型
      const data = res.data;
      this.topTabArr = [
        ...this.topTabArr,
        ...data,
      ]
      if(this.liveName){
        let currentIndex = this.topTabArr.findIndex(item => item.name == this.liveName)
        if(currentIndex != -1){
          this.$nextTick(()=>{
            this.activeTabIndex = currentIndex;
            this.initScrollView(this.topTabArr[currentIndex].id);
          })
        }
      }
    },
    toggleActive(index){
      if(this.activeIndex === index){
        this.activeIndex = null
      }else{
        this.activeIndex = index
      }
    },
    // 获取地址
    async getAddressList(){
      const res = await this.$api.hospital.arealistArea();
      if(res.code === 0){
        this.$common.setKeyVal('address', 'addressData', res.data, true)
        this.cityOptions = res.data
      }

      console.log('res',res)
    },

    handleClick(obj){
      console.log('obj',obj)
      const { close,select,selectLabel} = obj;
      if(close){
        this.selectAddress = [
          ...select
        ]
        this.selectAddressLabel = selectLabel;
        this.selectAddressValue = select[2]
        this.hospitalListText = selectLabel[2]

        this.activeIndex = null
        this.init()
      }

    },
    navtoGo(url = '', obj = {}) {
      this.$navto.push(url, obj)
    },
    init(val) {
      this.$nextTick(() => {
        this.isInit = true // 标记为true
        this.regForm.search = ''
		// 手动触发更新方法
        this.mescroll.triggerDownScroll()
      })
    },
    changeSearch(obj) {
      this.mescroll.optUp.page.num = 1
      this.mescroll.optUp.page.size = 7
      this.regForm.search = obj.name
      this.mescroll.triggerDownScroll()
    },
    scrollInit(scroll) {
      scroll.optUp.page.num = 1
      scroll.optUp.page.size = 7
      this.mescroll = scroll
    },
    returnFn(obj) {
		console.log('触发加载',obj);
      const that = this
      function queryPage(pageNum, pageSize, fn) {
        const param = {
          current: pageNum,
          size: pageSize,
          condition: {
            activityStatusList: [
              2,3,5
            ],
            showType: 1,
            businessType: that.businessType || 7, // 直播活动
            productId: that.productId,
            externalType: 1
          }
        }

        if (that.regForm.search) {
          // 清除开头和结尾的空格
          param.condition.title = that.regForm.search.trim();
          // param.condition.companyName = that.regForm.search.trim();
          // param.condition.doctorName = that.regForm.search.trim();
        }

        if (!that.productId) {
          delete param.condition.productId
        }

        if(that.businessType === 'all'){
          delete param.condition.businessType
        }

        if(that.businessType == 8) {
          param.condition.productId = that.productId
        } else {
          delete param.condition.productId
        }

        if(that.topTabArr[that.activeTabIndex].id){
          param.condition.classifyId = that.topTabArr[that.activeTabIndex].id
        }

        // 在全部类型的请求中 加入排序参数
        if(!param.condition.classifyId){
          delete param.condition.businessType
          param.condition.businessTypeList = [3,7]
          param.condition.orderByActivityStatus = "5,2,3"
        }
        // 记录页面请求参数
        that.logConditionParams = {
          ...param.condition
        }

        that.$api.cloudClassroom.getMeetingQueryPage(param).then(res => {
          // if(res.data.records.length == 0 && param.condition.businessType == 8){
          //   delete param.condition.businessType
          //   delete param.condition.productId
          //   param.condition.classifyId = that.topTabArr[that.activeTabIndex].id
          //   that.$api.cloudClassroom.getMeetingQueryPage(param).then(res => {
          //     if (res && res.data.records) {
          //       for (const a in res.data.records) {
          //         const data = res.data.records[a]
          //         data.coverPathsUrl = isDomainUrl(data.coverPaths)
          //         // data.endTimeText = that.$common.formatDate(new Date(data.endTime), 'yyyy-MM-dd HH:mm')
          //       }
          //       fn(res.data.records)
          //     }
          //   })
          // } else {
          //   for (const a in res.data.records) {
          //     const data = res.data.records[a]
          //     data.coverPathsUrl = isDomainUrl(data.coverPaths)
          //     // data.endTimeText = that.$common.formatDate(new Date(data.endTime), 'yyyy-MM-dd HH:mm')
          //   }
          //   fn(res.data.records)
          // }

           if (res && res.data.records) {
             for (const a in res.data.records) {
               const data = res.data.records[a]
               data.coverPathsUrl = isDomainUrl(data.coverPaths)
               // data.endTimeText = that.$common.formatDate(new Date(data.endTime), 'yyyy-MM-dd HH:mm')
             }
             fn(res.data.records)
           }
         })

      }



      setTimeout(function() {
        queryPage(obj.pageNum, obj.pageSize, (data) => {
          if (obj.pageNum === 1) {
            that.pdList = []
          }
          that.pdList = that.pdList.concat(data)
          obj.successCallback && obj.successCallback(data || [])
        })
      }, that.$constant.noun.scrollRefreshTime)
    }
  }
}
</script>
<style>
  ::-webkit-scrollbar {
  				display: none;
  				width: 0 !important;
  				height: 0 !important;
  				-webkit-appearance: none;
  				background: transparent;
  				color: transparent;
  	}
</style>
<style lang="scss" scoped>

  .d-flex{
    display: flex;
  }
  .float-btn-box{
    position: fixed;
    right: 20upx;
    bottom: 50upx;
  }
  .float-btn{

    width: 130upx;
    height: 130upx;
    background-color: $topicC;
    color: #fff;
    font-size: 32upx;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 550;
    border-radius: 50%;
    box-shadow: 2upx 2upx 2upx rgba(0,0,0,0.3);
    margin-bottom: 30upx;
  }
  .mainTabScroll{
     width: 100vw;
     overflow-x: scroll;
     overflow: -moz-scrollbars-none;
     &::-webkit-scrollbar {
       width: 0; /* 对于竖向滚动条 */
       height: 0; /* 对于横向滚动条 */
       display: none;
}
  }
  .main-tab-box {
    width: max-content;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding: 24rpx 32rpx 32rpx;
  }

    .main-tab {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      font-weight: 400;
      font-size: 32rpx;
      color: #4E5569;
      line-height: 44rpx;
      word-break: break-all;
      &+.main-tab {
        margin-left: 32rpx;
      }
      &.active {
        font-weight: 500;
        font-size: 32rpx;
        color: #00B484;
        line-height: 44rpx;
        &::before {
          content: "";
          background-color: $topicC;
		  // background: url('../../../static/image/system/tabBotIcon.png') no-repeat;
      background-image: url($imgUrl + '/static/image/business/hulu-v2/tabBotIcon.png');
		  background-size: 100% 100%;
          width: 38rpx;
          height: 10rpx;
          position: absolute;
          bottom: -10rpx;
          left: 50%;
          transform: translateX(-50%);
      }

    }
  }
  .tab-list{
    position: fixed;
    top:94upx;
    display: flex;
    align-items: center;
    height: 92upx;
    left: 0;
    right: 0;
    .tab-list-item{
      flex: 1;
      font-size: 24upx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .tab-list-txt{
      margin-right: 10upx;
    }
    .tab-list-next{
      border: 6upx solid #000;
      border-top-color: transparent;
      border-left-color: transparent;
      transform: rotate(45deg);
      transition: all 0.5s;
    }
    .active .tab-list-txt{
      color:$topicC;
    }
    .active .tab-list-next{
      // transform: rotate(135deg);
      transform: rotate(-135deg);
      margin-top: 5upx;
      border-bottom-color:$topicC;
      border-right-color:$topicC;

    }
  }

  .main-container{
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    background: linear-gradient( 180deg, #FFFFFF 0%, #F4F6FA 100%);
  }
  .main-search{
    display: flex;
    align-items: center;
	ttransform: translateY(-2rpx);
  }
  .main-wrapper {
    flex: 1;
  }
  .main-content{
    .portrait-list{
      /*margin-top: 10upx;*/
      .li{
        @include rounded(20upx);
        background-color:#FFFFFF;
        padding:16upx 20upx;
        margin-bottom: 24upx;
        .img{
          width: 240upx;
          height: 180upx;
          display: inline-block;
          vertical-align: middle;
          @include rounded(20upx);
          .role-image{
            width: 100%;
            height: 100%;
          }
        }
        .content{
          height: 180upx;
          position: relative;
          width: calc(100% - 266upx);
          padding-left: 24upx;
          display: inline-block;
          vertical-align: middle;
          .title{
            font-size: 32upx;
            color: #333333;
            display: block;
            font-weight: 600;
            /*line-height: 40upx;*/
            @include ellipsis(2);
            /*margin-bottom: 16upx;*/
          }
          .title-t{
            font-size: 28upx;
            color: #999999;
            display: block;
          }
          .footer{
            position: absolute;
            width: 100%;
            bottom: 0;
            .icon-time{
              display: inline-block;
              margin-right: 10upx;
              @include iconImg(26, 26, '/system/icon-time.png');
            }
            .time{
              color: #999999;
              font-size: 24upx;
              display: inline-block;
            }
            .btn{
              display: inline-block;
              right: 0;
              top: 0;
              width: 108upx;
              height: 40upx;
              line-height: 40upx;
              font-size: 24upx;
            }
          }
        }
      }
    }
  }
  /deep/ .uni-navbar__header-container {
    padding: 0 32rpx;
  }
  .main-search {
    /deep/ .search .input .icon-view-l {
      left: 32rpx;
      width: 26rpx;
      height: 26rpx;
      background-image: url('http://file.greenboniot.cn/static/image/business/hulu-v2/icon-search.png');
    }
    /deep/ .search .input .input-view {
      height: 64rpx;
      font-size: 26rpx;
      color: #1D2029;
      line-height: 36rpx;
      padding: 0 72rpx;
    }
  }
</style>
