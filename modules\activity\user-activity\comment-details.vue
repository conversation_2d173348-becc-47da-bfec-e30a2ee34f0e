<template>
  <page>
    <view slot="content" class="body-main">
      <scroll-refresh
        style="height: 100%"
        :isShowEmptySwitch="true"
        :fixed="false"
        :isAbsolute="false"
        :up="upOption"
        :down="downOption"
        bgColor="#F0F2F7"
        @returnFn="returnFn"
        @scrollInit="scrollInit"
      >
        <view class="content">
          <view class="content-main">
            <view v-for="(item, index) in indexlist" :key="index" class="item" @tap="navtoGo('PostsDetail', { id: item.businessId })">
              <view class="item-form">
                <view class="item-form-label">帖子标题：</view>
                <view class="item-form-value">{{ item.title }}</view>
              </view>
              <view class="item-form">
                <view class="item-form-label">评论内容：</view>
                <view class="item-form-value">{{ item.content }}</view>
              </view>
              <view class="item-form">
                <view class="item-form-label">发布时间：</view>
                <view class="item-form-value">{{ item.putawayTimeText }}</view>
              </view>
            </view>
          </view>
        </view>
      </scroll-refresh>
    </view>
  </page>
</template>

<script>
export default {
  data() {
    return {
      file_ctx: this.file_ctx,
      mescroll: null, // mescroll实例对象
      downOption: {
        auto: false // 不自动加载
      },
      upOption: {
        auto: false, // 不自动加载
        empty: {
          top: 0,
          zIndex: 999,
        }
      },
      isInit: false, // 列表是否已经初始化
      indexlist: [],
      queryParams: null
    }
  },
  onLoad() {
    const query = this.$Route.query
    if (this.$validate.isNull(query)) return this.$uniPlugin.toast('参数异常')
    this.queryParams = query
    this.$nextTick(() => {
      this.init()
    })
  },
  methods: {
    navtoGo(url, obj = {}) {
      this.$navto.push(url, obj)
    },
    init() {
      this.$nextTick(() => {
        this.mescroll.triggerDownScroll()
      })
    },
    scrollInit(scroll) {
      scroll.optUp.page.num = 1
      scroll.optUp.page.size = 10
      this.mescroll = scroll
    },
    returnFn(obj) {
      const that = this
      const { userActivityInviteLogId } = this.queryParams
      if (!userActivityInviteLogId) return obj.successCallback && obj.successCallback([])
      setTimeout(function () {
        let params = {
          current: obj.pageNum,
          size: obj.pageSize,
          condition: {
            businessType: 5,
            userActivityInviteLogId
          }
        }
        that.$api.postmessage.commentQueryPage(params).then(res => {
          let data = res.data.records.map(item => {
            return {
              ...item,
              putawayTimeText: that.$common.formatDate(new Date(item.putawayTime), 'yyyy-MM-dd HH:mm')
            }
          }) || []
          if (obj.pageNum === 1) {
            that.indexlist = []
          }
          that.indexlist = [...that.indexlist, ...data]
          obj.successCallback && obj.successCallback(data)
        })
      }, that.$constant.noun.scrollRefreshTime)

    },
  }
}
</script>

<style lang="scss" scoped>
.body-main {
  height: 100%;
  overflow-y: auto;
  background-color: #fff;
  .content {
    padding-bottom: env(safe-area-inset-bottom);
    padding-bottom: constant(safe-area-inset-bottom);
    box-sizing: border-box;
    &-main {
      box-sizing: border-box;
      padding: 32rpx;
      .item {
        background: #FFFFFF;
        border-radius: 16rpx;
        padding: 32rpx 24rpx;
        &+.item {
          margin-top: 24rpx;
        }
        .item-form {
          display: flex;
          &-label {
            font-weight: 500;
            font-size: 28rpx;
            color: #333333;
          }
          &-value {
            font-weight: 500;
            font-size: 28rpx;
            color: #333333;
          }
        }
      }
    }
  }
}
</style>
