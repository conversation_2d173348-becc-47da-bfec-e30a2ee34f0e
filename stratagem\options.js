import {stratagemAPIsOrRouterMaps} from './stratagemAPIsOrRouterMaps.js'
// 临时寄存api接口
export const temporaryApiList = [];
// 判定当前请求的接口里是否为哈希表里的某一项
export function isPathInInterfaceTable(url,type = 'api'){
  return stratagemAPIsOrRouterMaps.filter((pathOptions,index)=>{
          if(pathOptions.type !== type) return false
          if(url.indexOf(pathOptions.path)>=0)return true
  })
}
// 触发监听寄存api接口
export function triggerListeningApi(api,currentOptions){
  let id = Math.random().toString().split('.')[1]
  currentOptions.apiId = id;
  temporaryApiList.push({api,id,currentOptions})
}
// 删除寄存栈中的固定项
export function clearListeningApi(id){
  let current = temporaryApiList.findIndex(e => e.id === id);
  temporaryApiList.splice(current,1);
}
// 查询当前接口是否存在在临时寄存集合中
export function checkCurrentApi(apiPath){
  return temporaryApiList.filter((pathOptions,index)=>{
      if(apiPath.indexOf(pathOptions.api)>=0)return true
  })
}
// 获取集成参数
export function getQueryOptions(parametersComparison,data){
  if(typeof data === 'function') data = data();
  // 集成参数
  return Object.keys(parametersComparison).reduce((next,current)=>{
    let content = parametersComparison[current];
    if(typeof content === 'string') content = data.content;
    if(typeof content === 'function') content = content(data);
    next[current] = content
    return next
  },{})
}

