<!-- 悬浮菜单 <tabs-sticky v-model="index" :fixed="false" @change="changeTab"></tabs-sticky> -->
<template>
  <scroll-view class="tabs-sticky" :style="'background:' + background" :scroll-x="overflowX" :class="{'tabs-fixed': fixed,'top':top, 'display-flex': !overflowX, 'overflow-x': overflowX, 'bdb': bdb}">
    <view class="tabs-sticky-body" :class="{'white-space': overflowX}">
      <view class="tab" v-for="(tab, i) in tabs" :key="i" :class="{'active': value==i, 'font-bigger': value==i&&fontBigger&&overflowX}" @click="changeTab(i)">
        <text>{{tab.name}}</text>
        <view v-if="value==i" class="text-width"></view>
      </view>
    </view>
  </scroll-view>
</template>

<script>
export default {
  props: {
    // 数组下标
    dIndex: {
      type: [String, Number],
      default() {
        return ''
      }
    },
    background: {
      type: String,
      default() {
        return '#fff'
      }
    },
    fontBigger: {
      type: Boolean,
      default() {
        return false
      }
    },
    overflowX: {
      type: Boolean,
      default() {
        return false
      }
    },
    bdb: {
      type: Boolean,
      default() {
        return true
      }
    },
    tabs: {
      type: Array,
      default() {
        return []
      }
    },
    value: {
      type: Number,
      default() {
        return 0
      }
    },
    fixed: {
      type: Boolean,
      default() {
        return false
      }
    },
    top: {
      type: [Number, String],
      default() {
        return 0
      }
    }

  },
  methods: {
    // 切换tab
    changeTab(i) {
      if (this.value != i) {
        if (!this.$validate.isNull(this.dIndex)) {
          const obj = {
            value: i,
            index: this.dIndex
          }
          this.$emit('changeObj', obj)
          return
        }
        this.$emit('input', i)
        this.$emit('change', i)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .white-space{
    white-space: nowrap;
    padding: 0 20upx;
    display: table;
  }
  .display-flex{
    display: flex;
    view{
      display: flex;
      width: 100%;
      .tab{
        flex: 1;
        text{
          line-height: 70upx;
          flex: 1;
          display: inline-block;
        }
      }
    }
  }
  .overflow-x{
    .tab{
      text{
        line-height: 80upx;
        display: inline-block;
        padding: 0 20upx;
      }
    }
    .active{
      line-height: 70rpx !important;
      text{
        line-height: 70rpx !important;
      }
    }
    .font-bigger{
      color: #333 !important;
      font-weight: bold !important;
      font-size: 36upx !important;
      text{
        font-size: 33rpx;
        color: 28upx;
      }
    }
  }
  .tabs-sticky{
    height: 80upx;
    line-height: 80upx;
    font-size: 36upx;
    text-align: center;
    .tabs-sticky-body{
      height: 80upx;
      .active{
        color: $topicC;
        text{
          line-height: 94upx;
          padding:0 20upx;
          color: $topicC;
        }
      }
      .tab{
        line-height: 80upx;
        display: inline-block;
        position: relative;
        vertical-align: middle;
        .text-width{
          border-bottom: 4upx solid $topicC;
          position: absolute;
          left: 50%;
          -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
          bottom: 0;
          width: 40%;
        }
      }
    }
  }
  /*悬浮样式*/
  .tabs-sticky.tabs-fixed{
    z-index: 2;
    position: fixed;
    /*top: var(--window-top);*/
    top: 88upx;
    left: 0;
    width: 100%;
  }
</style>
