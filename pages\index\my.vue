<template>
  <view class='my-content' v-if="detailObj.id">
    <view class="my-content-head">
      <image class="img" :src="file_ctx + detailObj.imageUrl"></image>
      <view class="content-btn" @click="handleClickPrayPage" v-if="detailObj.managementItemList && detailObj.managementItemList.length"><image class="img" :src="detailObj.managementItemList[0].imageUrl"></image></view>
    </view>
    <view class="my-content-bott"><image class="img" :src="file_ctx + 'static/image/business/hulu-v2/icon-home-boot-page-bott.png'"></image></view>
    <view class="buttom" @click="handleJump(true)" v-if="detailObj.skipStatus == 1">
      跳过<span class="text" v-if="detailObj.skipItem && detailObj.skipItem.countdown>=1">{{ detailObj.skipItem.countdown }}s</span>
    </view>
  </view>
</template>

<script>
  import { mapState } from 'vuex'
  export default {
    data(){
      return{
        file_ctx:this.file_ctx,
        detailObj:{},
        timer:null,
      }
    },
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId,
        isLogin: state => state.isLogin
      }),
    },
    onLoad(options){
      const query = this.$validate.isNull(this.$Route.query) ? options : this.$Route.query
      query?.params ? this.advertisementmanagementValidPopAccount(query) : this.advertisementmanagementValidPopAccount()
    },
    onShareAppMessage (res) {
      if (res.from === 'button') {
      }
      return {
        title: ``, //分享的名称
        path: `pages/index/my?shareShow=1`,
      }
    },
    mounted(){
    },
    methods:{
      async advertisementmanagementValidPopAccount(params){
        try{
          const res = await this.$api.drugBook.advertisementmanagementListValidPopAccount({accountId:this.accountId,useType:5,adsId: ''})
          if(res.data?.length){
            this.detailObj = res.data[0]
            params ? this.detailObj.options = params : '' 
            if(this.detailObj?.id){
              await this.$api.drugBook.advertisementmanagementrecordInsert({accountId:this.accountId,advertisementId:this.detailObj?.id,businessType:1})
              if(this.detailObj?.skipStatus == 1 && this.detailObj?.skipItem?.countdown >= 1){
                this.startJump(this.detailObj?.skipItem?.countdown)
              }
            }
          } else {
            setTimeout(()=>{
              uni.switchTab({url:'/pages/index/index'})
            },500)
            uni.setStorageSync('initialData', true)
            return
          }
        }catch(err){
          setTimeout(()=>{
            uni.switchTab({url:'/pages/index/index'})
          },500)
          uni.setStorageSync('initialData', true)
        }
      },

      startJump(time){
        this.timer = setInterval(()=>{
          if(time > 0){
            time--
            this.detailObj.skipItem.countdown = time
          }else{
            clearInterval(this.timer)
            this.handleJump()
          }
        },1000)
      },

      async handleClickPrayPage(){
        clearInterval(this.timer)
        let item = this.detailObj?.managementItemList[0]
        if(item.isLoginStatus == 1 && !this.isLogin){
          await this.$api.drugBook.advertisementmanagementitemrecordInsert({accountId:this.accountId,advertisementItemId:item?.id,businessType:1})
          this.$navto.push('Login',{formPage: item?.jumpUrl,formPageParams:encodeURIComponent(
            JSON.stringify({
              id:item?.afterPopId || ''
            })
          )})
        } else {
          await this.$api.drugBook.advertisementmanagementitemrecordInsert({accountId:this.accountId,advertisementItemId:item?.id,businessType:1})
          this.navigateTo(item)
        } 
      },

      async handleJump(flag){
        if(this.detailObj?.skipStatus == 1 && flag){
          await this.$api.drugBook.advertisementmanagementitemrecordInsert({accountId:this.accountId,advertisementItemId:this.detailObj?.skipItem?.id,businessType:1})
        }

        clearInterval(this.timer)

        let arr = ['/pages/index/index', '/pages/circle-home/index', '/pages/news/index', '/pages/post-message/index', '/pages/personal/index']
        let newArr = ['Index', 'CircleHome', 'News', 'PostMessage', 'Personal']

        if(this.detailObj?.options?.path){

          if (arr.includes('/'+this.detailObj?.options?.path)) {
            uni.reLaunch({
              url: '/'+this.detailObj?.options?.path
            })
          } else {
            let temp = !this.$validate.isNull(this.detailObj?.options?.params) ? decodeURIComponent(decodeURIComponent(this.detailObj?.options?.params)) : {}
            let parsedData = {}
            if(typeof temp === 'string' && temp.trim() !== ''){
              try{
                parsedData = JSON.parse(temp)
              } catch (error) {
                parsedData = {}
              }
            }
            this.$navto.replacePath(this.detailObj?.options?.path,parsedData)
          }
        } else {
          const currentIndex = newArr.findIndex(item=> item == this.detailObj?.skipItem?.jumpUrl)
          if (newArr.includes(this.detailObj?.skipItem?.jumpUrl)) {
            uni.reLaunch({
              url: arr[currentIndex]
            })
          } else {
            this.$navto.replaceAll(this.detailObj?.skipItem?.jumpUrl)
          }
        }
        uni.setStorageSync('initialData', true)
      },

      // 页面跳转
      navigateTo(item,obj={}) {
        item.jumpType = typeof(item.jumpType) === 'number' ? JSON.stringify(item.jumpType) : item.jumpType
        switch (item.jumpType) {
          case '2':
            if (item.jumpUrl) {
              this.$navto.replaceAll(item?.jumpUrl,obj)
            }
            break
          case '3':
            this.$navto.replaceAll('WebHtmlView', { src: item.jumpUrl, title: '' })
            break
          default:
        }
      },
    },
    destroyed () {
      clearInterval(this.timer)
    },
 }
</script>

<style lang='scss' scoped>
.my-content{
  position: relative;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .my-content-head{
    position: relative;
    height: 1356rpx;
    width: 100%;
    .content-btn{
      position: absolute;
      bottom: 144rpx;
      left: 50%;
      transform: translateX(-50%);
      width: 478rpx;
      height: 132rpx;
    }
  }
  .my-content-bott{
    height: 268rpx;
    width: 100%;
  }
  .buttom{
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0rpx 24rpx;
    background: rgba(0,0,0,0.5);
    border-radius: 28rpx;
    height: 56rpx;
    font-size: 24rpx;
    position: absolute;
    right: 24rpx;
    bottom: 316rpx;
    color:#fff;
    .text{
      margin-left: 8rpx;
    }
  }
}
.img{
  width: 100%;
  height: 100%;
}
</style>