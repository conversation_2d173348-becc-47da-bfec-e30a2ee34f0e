
<template>
  <view class="title-input clear-float" :class="{'bdt':defaultConfig.bdt,'bdb':defaultConfig.bdb && !child,'mgb30':isAccuratePromotion}">
    <template v-if='isAccuratePromotion'>
      <view class="accurate-promotion-input">
        <image :src="ico" class="accurate-promotion-ico">
        </image>
        <view class="accurate-promotion-label">
          {{defaultConfig.label}}
        </view>
        <view class="accurate-promotion-right">
          <input :disabled="disabled"
            v-model="form.data.val"
            @input="returnFn"
            type="nickname"
            class="accurate-promotion-input"
            :class="[disabled ? 'disabled' : '']"
            :placeholder="'请输入'+ defaultConfig.label"
          />
        </view>
        <view class="accurate-promotion-space">

        </view>
      </view>
    </template>
    <template v-else>
      <view class="l-l" :style="{'color': defaultConfig.titleColor}">
        {{defaultConfig.label}}
        <text class="star" v-if="defaultConfig.required">*</text>
      </view>
      <view class="l-r">
        <input :disabled="disabled"
          v-model="form.data.val"
          @input="returnFn"
          type="nickname"
          :class="[disabled ? 'disabled' : '']"
          placeholder="请输入"
        />
      </view>
    </template>

  </view>
</template>

<script>

export default {
  data() {
    return {
      form: {
        data: {
          val: ''
        }
      },
      ico: this.$static_ctx + 'image/business/applet/icon-im-business-componet-user.png',
      array: [],
      index: 0,
      defaultConfig: {
        bdt: false,
        bdb: true,
        titleColor: '#333',
        textColor: '#333',
        label: '单行输入框',
        name: 'input',
        required: false,
      }
    }
  },
  watch: {
    cData: {
      handler(val) {
        // this.watchDataMain(val)
      },
      deep: true
    },
    config: {
      handler(val) {
        this.copyConfig()
      },
      deep: true
    }
  },
  props: {
    // 小葫芦精准地推类型样式
    isAccuratePromotion:{
      type:Boolean,
      default:false
    },
    child:{
      type:Boolean,
      default:false,
    },
    // 是否禁用disable
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 初始值传值，用于回显
    cData: {
      type: [String, Number],
      required: false,
      default() {
        return ''
      }
    },
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    },
    
  },
  computed: {

  },
  mounted() {
    console.log(this.config)
      this.copyConfig()
      if (!this.$validate.isNull(this.cData)) {
        this.watchDataMain(this.cData)
      }
  },
  methods: {
    /**
       * 初始化拷贝config对象
       */
    copyConfig() {
      const that = this
      const obj = that.config
      Object.keys(obj).forEach(function(key) {
        that.defaultConfig[key] = obj[key]
      })
    },
    /**
       * 监听Data主逻辑方法
       */
    watchDataMain(val) {
      this.form.data.val = val
    },
    /**
       * picker触发选中事件
       * @param e
       */
    returnFn(e) {
      const that = this
      if (that.disabled) return
      that.$emit('updateForm', { key: '' + that.config.name, value: that.form.data.val,userName:that.form.data.val })
    }
  }
}
</script>

<style lang="scss" scoped>
  .mgb30{
    margin-bottom: 30upx;
  }
  // 小葫芦精准地推
  .accurate-promotion-input{
    display: flex;
    align-items: center;
    height: 88upx;
    background-color: #f8f8f8;
    border-radius: 10upx;
    overflow: hidden;
    padding: 0 20upx;
    .accurate-promotion-ico{
      height: 30upx;
      width: 30upx;
    }
    .accurate-promotion-label{
      padding: 0 20upx;
      font-weight: 550;
      min-width: 200upx
    }
    .accurate-promotion-space{
      width: 40upx;
      height: 40upx;
    }
    .accurate-promotion-right{
      flex: 1;
    }
    .accurate-promotion-input{
      // width:100%;
      text-align: right;
    }

  }
  .color-topicC{
    color: $topicC !important;
  }
  .title-input{
    background-color:#ffffff;
    .l-l{
      line-height: 88upx;
      color: #333333;
      font-weight: 600;
      font-size:30upx;
    }
    .l-r{
      margin-bottom: 5px;
      display: flex;
      align-items: center;
          padding-bottom: 20rpx;
      input {
        height: 80upx;
        line-height: 80upx;
        color: #333;
        font-size: 32upx;
        text-align: left;
        display: inline-block;
        vertical-align: middle;
        width: calc(100% - 64upx);
        border-bottom: 1upx solid $borderColor;
        border-radius: 10upx;
        padding: 0 20rpx;

      }
      input.disabled{
          background-color: #dbdbdb;
          color: #fff;
      }
      .util{
        width: 64upx;
        font-size: 28upx;
            overflow: hidden;
            text-align: center;

      }
    }
  }
  .star{
    color: #F85E4C;
    padding-top: 6upx;
    font-size: 32upx;
    display: inline-block;
        margin-left: 10rpx;
  }
</style>
