<template>
  <page>
    <view slot="content" class="body-main">
      <scroll-refresh
        style="height: 100%;"
        :isShowEmptySwitch="true"
        :fixed="false"
        :isAbsolute="false"
        :up="upOption"
        :down="downOption"
        @returnFn="returnFn"
        @scrollInit="scrollInit"
      >
        <view class="content">
          <view class="content-main">
            <template v-for="item in replyList">
              <view :id="`reply-${item.replyId}`" :key="item.businessId" class="item"><reply-item :info="item" @reply="reply" /></view>
            </template>
          </view>
        </view>
      </scroll-refresh>
      <comment-input ref="commentInput" :show.sync="inputShow" :placeholder="placeholder" @confirm="confirm" />
    </view>
  </page>
</template>

<script>

import { mapState } from 'vuex'
import replyItem from './components/reply-item.vue'
import commentInput from './components/comment-input.vue'
import env from '@/config/env'
import serverOptions from '@/config/env/options'
export default {
  name: 'CircleHome',
  components: {
    replyItem,
    commentInput
  },
  data() {
    return {
      file_ctx: this.file_ctx,
      mescroll: null, // mescroll实例对象
      downOption: {
          auto: false // 不自动加载
      },
      upOption: {
          auto: false, // 不自动加载
          empty: {
            top: 0,
            zIndex: 999,
          }
      },
      isInit: false, // 列表是否已经初始化
      replyList: [],
      pageStartDate: null,
      inputShow: false,
      replyItem: {},
      placeholder: '',
      timer: null,
      billingKey: []
    }
  },
  computed: {
    ...mapState('user', {
      codeUserInfo: state => state.codeUserInfo, // 当前登录用户信息
      curSelectUserInfo: state => state.curSelectUserInfo, // 当前选中的用户信息
      curSelectUserInfoId: state => state.curSelectUserInfoId, // 当前选中的档案的ID
      recordUserInfo: state => state.recordUserInfo,
      accountId: state => state.accountId
    })
  },
  onLoad(paramsObj) {
    const that = this
    const query = that.$Route.query
    this.pageStartDate = that.$common.formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss')
    if (!that.$validate.isNull(query)) {

    }
    this.$nextTick(() => {
      that.init()
      this.sendReadStatus()
    })
  },
  onShow() {

  },
  methods: {
    async confirm ({content = '', imagePath = ''}) {
      this.$uniPlugin.loading('发送中...')
      const { centerUserId = '' } = this.curSelectUserInfo || {}

	  let openId = this.$common.getCache('openId')

	  if(!openId || openId == ''){
	    // const
	    openId = await this.$ext.wechat.getOpenId()

	  }

      let params = {
		  openid:openId,
          source: 2,
          userId: centerUserId,
          content: content,
          imagePath: this.$validate.isNull(imagePath) ? '' : imagePath.map(item => item.dir).join(','),
          level: 2,
          accountId: this.accountId,
          businessId: this.replyItem.postMessageId,
          type: 1,
          ownerCommentId: this.replyItem.ownerCommentId ? this.replyItem.ownerCommentId : this.replyItem.businessId,
          mutualCommentId: this.replyItem.businessId,

          mutualAccountId: this.replyItem.accountId,
          mutualUserId: this.replyItem.userId,
          appId: serverOptions.appId,
          appid: serverOptions.appId,
      }

      const res = await this.$api.postmessage.commentPostMessageComment(params)
      this.$uniPlugin.toast(res.msg)
      this.$uniPlugin.hideLoading()
      this.$refs.commentInput.clearInput()
      this.$refs.commentInput.closeInput()
    },
    setReadStatus () {
      this.replyList.forEach((item, index) => {
        this.$set(this.replyList, index, { ...item, readStatus: 1 })
      })
    },
    sendReadStatus () {
      const param = {
        businessTypes: [3, 4],
        accountId: this.accountId
      }
      this.setReadStatus()
      this.$api.community.noticelogUpdateAllNoticeLog(param)
    },
    reply (data) {
      this.inputShow = true
      this.replyItem = data
      this.placeholder = '回复' + data.nickName
    },
    init() {
      this.$nextTick(() => {
        this.mescroll.triggerDownScroll()
      })
    },
    scrollInit(scroll) {
        scroll.optUp.page.num = 1
        scroll.optUp.page.size = 10
        this.mescroll = scroll
    },
    returnFn(obj) {
        const that = this
        setTimeout(function() {
            let params = {
              current: obj.pageNum,
              size: obj.pageSize,
              condition: {
                  createTime: that.pageStartDate,
                  accountId: that.$common.getKeyVal('user', 'accountId', true),
                  businessType: [3, 4]
              }
            }
            that.$api.community.noticelogQueryCommentPage(params).then(res => {
              let data = res.data.records || []
              data = data.map(item => {
                return {
                  ...item,
                  replyId: item.id,
                  putawayTimeText: that.$common.formatDate(new Date(item.putawayTime || new Date().getTime()), 'yyyy-MM-dd HH:mm:ss')
                }
              })
              if (obj.pageNum === 1) {
                that.replyList = []
              }
              that.replyList = [...that.replyList, ...data]
              obj.successCallback && obj.successCallback(data)
            })
        }, that.$constant.noun.scrollRefreshTime)

    }
  },
  watch: {
    replyList: {
      handler (newData, oldData) {
        if (this.$validate.isNull(oldData) && !this.$validate.isNull(newData)) {
          this.$nextTick(() => {
            this.checkDivShow()
          })
        }
      }
    }
  },
  onPageScroll() {
    // 滚动
    clearTimeout(this.timer) // 每次滚动前 清除一次
    this.timer = setTimeout(() => {
      // 停止滚动时上报
      this.checkDivShow()
    }, 200)
  }
}
</script>

<style lang="scss" scoped>
.body-main {
  background-color: #fff;
  height: 100%;
  .content {
    padding-bottom: env(safe-area-inset-bottom);
    padding-bottom: constant(safe-area-inset-bottom);
    &-main {
      padding: 44upx 34upx 0;
    }
  }
}
.item {
  & + .item {
    margin-top: 32upx;
  }
}
</style>
