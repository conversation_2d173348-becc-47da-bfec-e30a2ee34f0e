
<template>
  <view class="title-radio clear-float">
    <view class="title-radio-item" v-for="(item, index) in array" :key="index" @tap="returnFn(index)">
      <em :class="[item.checked ? 'icon-yijianfankui-d-ok' : 'icon-yijianfankui-d']"></em>
      <text>{{item.value}}</text>
    </view>
  </view>
</template>

<script>

export default {
  data() {
    return {
      form: {
        data: {
          select: ''
        }
      },
      array: [],
      index: 0,
      defaultConfig: {
        bdt: true,
        bdb: true,
        titleColor: '#333',
        textColor: '#333',
        label: '单选',
        name: 'select',
        required: false,
        array: [],
        dicKey: '',
        marginStyle: ''
      }
    }
  },
  watch: {
    cData: {
      handler(val) {
        this.watchDataMain(val)
      },
      deep: true
    },
    config: {
      handler(val) {
        this.copyConfig()
      },
      deep: true
    }
  },
  props: {
    // 是否禁用disable
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 初始值传值，用于回显
    cData: {
      type: [String, Number],
      required: false,
      default() {
        return ''
      }
    },
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    }
  },
  computed: {

  },
  mounted() {
    this.getDic(() => {
      this.copyConfig()
      if (!this.$validate.isNull(this.cData)) {
        this.watchDataMain(this.cData)
      }
    })
  },
  methods: {
    /**
       * 初始化拷贝config对象
       */
    copyConfig() {
      const that = this
      const obj = that.config
      Object.keys(obj).forEach(function(key) {
        that.defaultConfig[key] = obj[key]
      })
    },
    /**
       * 监听Data主逻辑方法
       */
    watchDataMain(val) {
      this.form.data.select = val
      let isVal = true
      let index = ''
      for (let i = 0; i < this.array.length; i++) {
        this.array[i].checked = false
        if (isVal && val === this.array[i].key) {
          isVal = false
          this.array[i].checked = true
          index = i
        }
      }
      if (index !== '') {
        this.$set(this.array, index, this.array[index])
      }
    },
    /**
       * 获取字典数据组
       */
    getDic(callBack) {
      const that = this
      const ar = that.config.array || []
      if (ar.length > 0) {
        that.array = ar
        for (const i in that.array) {
          that.array[i].checked = false
        }
        callBack()
        return
      }
      const params = {
        dictType: that.config.dicKey
      }
      that.$ext.dic.getDicInfo(params, (res) => {
        that.array = res
        for (const i in that.array) {
          that.array[i].checked = false
        }
        callBack()
      }, () => {
        this.array = []
        callBack()
      })
    },
    /**
       * picker触发选中事件
       * @param e
       */
    returnFn(index) {
      const that = this
      if (that.disabled) return
      for (const i in that.array) {
        that.array[i].checked = false
      }
      that.array[index].checked = true
      that.$set(that.array, index, that.array[index])
      const obj = that.array[index]
      that.form.data.select = obj.key
      that.$emit('updateForm', { key: '' + that.config.name, value: that.form.data.select })
    }
  }
}
</script>

<style lang="scss" scoped>
  .title-radio{
    height: 112upx;
    display: flex;
    overflow: hidden;
    background-color:#ffffff;
    .title-radio-item{
      display: flex;
      align-items: center;
      margin-right: 48upx;
      line-height: 112upx;
        em{
          vertical-align: middle;
          margin-right: 8upx;
        }
        .icon-yijianfankui-d{
          @include iconImg(36, 36, '/system/icon-yijianfankui-d.png');
        }
        .icon-yijianfankui-d-ok{
          @include iconImg(36, 36, '/system/icon-yijianfankui-d-ok.png');
        }
        text{
          vertical-align: middle;
          font-size: 28rpx;
          color: #1D2029;
        }
        &:last-child{
          margin-right: 0;
        }
    }
  }
</style>
