<template>
  <view class="main">
    <view :style="'height:' + statusBarHeight + 'px;'"></view>
    <view class="top-nav">
      <view class="top-nav-l" @click.stop="handleBack"><image mode="aspectFit" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-left-arrows.png'" class="header-search-img"/></view>
      <view class="top-nav-c">套餐订单</view>
    </view>
    <view class="main-title"><view class="img"><image :src="imgUrl"></image></view>{{ handleFilterOrderState(detailObj.comboState) }}</view>
    <view class="main-content">
      <view class="combo-day">
        <view class="head">
          <view class="head-l">
            <!-- <image :src="file_ctx + "></image> -->
          </view>
          <view class="head-r">
            <view class="name">{{ detailObj.comboName }}</view>
            <view class="price" v-if="detailObj.comboPrice">¥<span>{{ detailObj.comboPrice / 100}}</span></view>
          </view>
        </view>
        <view class="combo-content">
          <view class="content-l">套餐内容：</view>
          <view class="content-r" v-if="detailObj.comboUserStandardList.length">{{ detailObj.comboUserStandardList[0].serviceName }}{{ detailObj.comboUserStandardList[0].serviceNum }}次(剩余{{ detailObj.comboUserStandardList[0].remainNum }}次)</view>
        </view>

      </view>
      <view class="bottom">
        <view class="title">订单信息</view>
        <view class="order-num">
          <view class="num-l">订单号</view>
          <view class="num-r">{{ detailObj.id }} <span @click="handleCopyOrder(detailObj.id)">复制</span></view>
        </view>
        <view class="create-time">
          <view class="num-l">创建时间</view>
          <view class="num-r">{{ detailObj.createTime }}</view>
        </view>
      </view>
    </view>
    <view class="main-bottom" v-if="detailObj.comboState == 0">
      <button class="main-bottom-l" @click="handleCancel">取消订单</button>
      <button class="main-bottom-r" @click="handleClickPay">在线支付¥ {{ detailObj.comboPrice / 100 }}</button>
    </view>
  </view>
</template>

<script>
  import { mapState } from 'vuex'
  export default {
    data(){
      return{
        file_ctx: this.file_ctx,
        statusBarHeight: 0,
        detailObj:null,
        imgUrl:null,
      }
    },
    computed: {
      ...mapState('user', {
        curSelectUserInfo: state => state.curSelectUserInfo
      }),
    },
    onLoad(option){
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
      const query = this.$validate.isNull(this.$Route.query) ? option : this.$Route.query
      if(!query.id){
        setTimeout(() => {
          try {
            this.$navto.back(1)
          } catch (error) {
            uni.switchTab({url:'/pages/accompany-home/index'})
          }
          setTimeout(() => {
            this.$uniPlugin.toast('订单不存在');
          }, 150);
        }, 100);
        return
      }
      this.accompanycombouserQueryOne(query.id)
    },
    mounted(){},
    methods:{
      async handleClickPay(){
        const allinpaydetailRes = await this.allinpaydetailGetOrderDetail()
        if(allinpaydetailRes.data !== "" && allinpaydetailRes.data.orderStatus !== "99"){
          const openId = await this.$ext.wechat.getOpenId()
          const { centerUserId: userId = '' } = this.curSelectUserInfo || {}
          const res = await this.$api.pay.agentCollectComboApply({ userId, openId, accompanyComboUserId:this.detailObj.id })
          let { payInfo } = res.data || {}
          payInfo = typeof(payInfo) === 'string' ? JSON.parse(payInfo) : payInfo
          this.$uniPlugin.loading('请求支付中...', true)
          wx.requestPayment({
            timeStamp: payInfo.timeStamp,
            nonceStr: payInfo.nonceStr,
            package: payInfo.package,
            signType: payInfo.signType,
            paySign: payInfo.paySign,
            success: res => {
              this.$uniPlugin.toast('支付成功');
            },
            fail: err => {
              this.$uniPlugin.toast(err.errMsg)
            },
            complete: res => {
              this.$uniPlugin.hideLoading()
            }
          })
        } else {
          let data = allinpaydetailRes.data.payInfo
          let payInfo = typeof(data) === 'string' ? JSON.parse(data) : data
          this.$uniPlugin.loading('请求支付中...', true)
          wx.requestPayment({
            timeStamp: payInfo.timeStamp,
            nonceStr: payInfo.nonceStr,
            package: payInfo.package,
            signType: payInfo.signType,
            paySign: payInfo.paySign,
            success: res => {
              this.$uniPlugin.toast('支付成功');
            },
            fail: err => {
              this.$uniPlugin.toast(err.errMsg)
            },
            complete: res => {
              this.$uniPlugin.hideLoading()
            }
          })
        }
      },

      async allinpaydetailGetOrderDetail(){
        const res = await this.$api.accompanyDoctor.allinpaydetailGetOrderDetail({bizOrderNo:this.detailObj.id})
        return Promise.resolve(res)
      },

      async handleCancel(){
        const res = await this.$api.accompanyDoctor.accompanycombouserCancel({id:this.detailObj.id})
        if(res.data){
          this.$uniPlugin.toast('取消成功');
          this.$navto.back(1)
        }
      },

      handleCopyOrder(id){
        uni.setClipboardData({  
          data: id,  
          success: function () {  
            uni.showToast({  
              title: '复制成功',  
              icon: 'success'  
            });  
          },  
          fail: function (error) {  
            // console.error('复制失败', error);  
            uni.showToast({  
              title: '复制失败',  
              icon: 'none'  
            });  
          }  
        });  
      },
      handleFilterOrderState(type){
        switch(type){
          case 0:
            this.imgUrl = this.file_ctx + 'static/image/business/accompany-doctor/icon-accompany-unpaid.png'
            return '待支付'
          case 1:
            this.imgUrl = this.file_ctx + 'static/image/business/accompany-doctor/icon-accompany-take-effect.png'
            return '生效中'
          case 2:
            this.imgUrl = this.file_ctx + 'static/image/business/accompany-doctor/icon-accompany-completed.png'
            return '已完成'
          case 3:
            this.imgUrl = this.file_ctx + 'static/image/business/accompany-doctor/icon-accompany-cancele.png'
            return '已取消'
          case 4:
            this.imgUrl = this.file_ctx + 'static/image/business/accompany-doctor/icon-accompany-lost-effectiveness.png'
            return '已失效'
        }
      },
      handleBack(){
        this.$navto.back(1)
      },
      // 根据id查询服务
      accompanycombouserQueryOne(id){
        this.$api.accompanyDoctor.accompanycombouserQueryOne({id}).then(res=>{
          this.detailObj = {...res.data,createTime:this.$common.formatDate(new Date(res.data.createTime), 'yyyy-MM-dd').replace(/-/g, '.')}
        })
      },
    },
 }
</script>

<style lang='scss' scoped>
  .main{
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
    background-color: #F4F6FA;
  }
  .top-nav{
    // position: fixed;
    width: calc(100% - 16rpx);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    line-height: 40px;
    padding: 0 16rpx;
    // z-index: 999;
    // padding: 0 32rpx 0 24rpx;
    .top-nav-l{
      display: flex;
      width: 48rpx;
      height: 48rpx;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .top-nav-c{
      flex: 1;
      text-align: center;
      height: 44rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #2D2F38;
      line-height: 44rpx;
      margin-right: 48rpx;
    }
  }
  .main-title{
    display: flex;
    align-items: center;
    font-size: 36rpx;
    color: #1D2029;
    margin: 34rpx 32rpx;
    .img{
      width: 48rpx;
      height: 48rpx;
      image{
        width: 100%;
        height: 100%;
      }
      // background-color: pink;
    }
  }
  .main-content{
    background: #FFFFFF;
    border-radius: 16rpx;
    margin: 32rpx 32rpx 40rpx;
    padding:32rpx 24rpx;
    overflow: hidden;
    .combo-day{
      padding-bottom: 32rpx;
      border-bottom: 1px solid #EAEBF0;
      .head{
        display: flex;
        margin-bottom: 24rpx;
        .head-l{
          width: 96rpx;
          height: 96rpx;
          border-radius: 9rpx;
          border: 1rpx solid #D9DBE0;
        }
        .head-r{
          margin-left: 20rpx;
          .name{
            font-size: 32rpx;
            color: #1D2029;
            line-height: 44rpx;
          }
          .price{
            font-size: 22rpx;
            color: #FF5500;
            span{
              font-size: 36rpx;
              color: #FF5500;
              line-height: 50rpx;
            }
          }
        }
      }
      .combo-content{
        display: flex;
      }
    }
    .bottom{
      .title{
        font-size: 32rpx;
        color: #1D2029;
        line-height: 44rpx;
        margin-top: 28rpx;
      }
      .order-num,.create-time{
        display: flex;
        justify-content: space-between;
        .num-l{
          font-size: 26rpx;
          color: #1D2029;
          line-height: 36rpx;
        }
        .num-r{
          font-size: 26rpx;
          color: #4E5569;
          line-height: 36rpx;
          span{
            font-size: 26rpx;
            color: #316EAB;
            line-height: 36rpx;
          }
        }
      }
      .order-num{
        margin: 32rpx 0 16rpx;
      }
      .create-time{

      }
    }
  }
  .main-bottom{
    display: flex;
    height: 88rpx;
    margin: 0 32rpx;
    width: calc(100% - 64rpx);
    .main-bottom-l,.main-bottom-r{
      display: flex;
      align-items: center;
      justify-content: center;
      background: #FFFFFF;
      border-radius: 44rpx;
      border: 1rpx solid #D9DBE0;
      font-size: 32rpx;
      color: #1D2029;
      &::after{
        border: none !important;
      }
    }
    .main-bottom-l{
      width: 30%;
      color: #1D2029;
      background-color: #fff;
    }
    .main-bottom-r{
      width: calc(70% - 32rpx);
      background: #00B484;
      font-size: 32rpx;
      color: #FFFFFF;
      margin-left: 32rpx;
    }
  }
</style>