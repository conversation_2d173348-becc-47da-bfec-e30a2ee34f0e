<template>
  <showmodel type='center' :show='centerVisible' @cancel='cancelCenterVisible' root-class='disclaimersDialogRootClass'>
    <view class="disclaimers-box">
      <view class="disclaimers-t">
        免责声明
      </view>
      <view class="disclaimers-c">
        本小程序所载的信息源自于医疗机构官方网站或其他权威平台，所发表的评论或观点仅代表作者本人观点，仅供用户参考。我们会将尽最大可能保证信息的准确性，但仍可能存在更新不及时或其他意外的情形，如需更精准信息或进一步了解的，用户可登录其官网确认或前往线下医疗机构咨询。
      </view>
      <view class="disclaimers-btn" @click="clickQuery">
        我已确认知悉
      </view>
    </view>
  </showmodel>
</template>

<script>
  import showmodel from '@/components/basics/showmodel/showmodel.vue'
  export default {
    props:{
      updatecount:{
        type:Number,
        default:0,
      }
    },
    watch:{
      updatecount(n) {
        this.initDialog()
      }
    },
    data() {
      return {
        centerVisible:false,
      }
    },
    components:{
      showmodel
    },
    mounted(){
      // this.initDialog()
    },
    methods:{
      initDialog() {
        let isDisclaimers = this.$common.getCache('disclaimersCount');
        console.log("isDisclaimers",isDisclaimers)
        if(!isDisclaimers || isDisclaimers === '') {
          setTimeout(() => {
            this.centerVisible = true;
          },600)
        }else {
          this.centerVisible = false;
        }
      },
      clickQuery() {
        this.centerVisible = false;
        this.$common.setCache('disclaimersCount','1')
      },
      cancelCenterVisible() {
        this.centerVisible = false;
      },
    }
  }
</script>

<style scoped lang="scss">
  .disclaimers-t {
    padding-top: 70upx;
    font-size: 32upx;
    font-weight: 550;
    padding-bottom: 55upx;
    display: flex;
    justify-content: center;
  }
  .disclaimers-c {
    padding: 0 50upx;
    color: #7c7c7c;
    font-size: 30upx;
  }
  .disclaimers-btn {
    height: 80rpx;
    border: 1rpx solid #dbdbdb;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 50rpx;
    margin-top: 135rpx;
    color: #7c7c7c;
    font-size: 30rpx;
  }

</style>
