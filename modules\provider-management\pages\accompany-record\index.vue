<template>
  <view class="accompany-record">
    <!-- 顶部导航栏 -->
    <!-- <uni-nav-bar
      color="#1d2029"
      :border="false"
      :inFontWeight="true"
      :showBtnsRight="false"
      :fixed="false"
      title="陪诊记录"
      statusBar
      @clickLeft="back"
      left-icon="left"
      left-width="48rpx"
      right-width="100px"
      backgroundColor="rgba(0,0,0,0)"
    /> -->

    <!-- 筛选栏 -->
    <view class="filter-bar-wrapper">
      <view class="filter-bar">
        <view class="filter-item" @click="selectFilter('time')">
          <text class="filter-text">时间</text>
          <image class="filter-arrow" :src="iconRightArrow" mode="aspectFit"></image>
        </view>
        <view class="filter-item" @click="$refs.selectCity.show()">
          <text class="filter-text">城市</text>
          <image class="filter-arrow" :src="iconRightArrow" mode="aspectFit"></image>
        </view>
        <view class="filter-item" @click="selectFilter('hospital')">
          <text class="filter-text">医院</text>
          <image class="filter-arrow" :src="iconRightArrow" mode="aspectFit"></image>
        </view>
        <view class="filter-item" @click="selectFilter('status')">
          <text class="filter-text">科室</text>
          <image class="filter-arrow" :src="iconRightArrow" mode="aspectFit"></image>
        </view>
        <view class="filter-item" @click="selectFilter('doctor')">
          <text class="filter-text">医生</text>
          <image class="filter-arrow" :src="iconRightArrow" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <!-- 选择数量提示 -->
    <view class="selected-count-bar" v-if="showSelectFeature && selectedRecords.length > 0">
      已选择 {{ selectedRecords.length }} 条陪诊记录
    </view>

    <!-- 陪诊记录列表 -->
    <view class="record-list">
      <scroll-refresh
        bgColor='white'
        class="scroll-refresh-main"
        :isShowEmptySwitch="false"
        :fixed="false"
        :up="upOption"
        :down="downOption"
        @returnFn="returnFn"
        @scrollInit="scrollInit"
      >
        <!-- 记录项目 -->
        <view v-if="recordList && recordList.length > 0" style="background-color: #F4F6FA;">
          <view
            class="record-item"
            v-for="item in recordList"
            :key="item.id"
            @click="goToDetail(item)"
          >
            <view class="record-header">
              <view class="service-name">{{ item.serviceName || '陪诊服务' }}</view>
              <view class="service-status-wrapper">
                <view class="service-status" :class="'status-' + item.orderState">{{ getStatusText(item.orderState) }}</view>
                <view v-if="showSelectFeature" class="select-indicator" :class="{ 'selected': selectedRecords.some(record => record.id === item.id) }" @click.stop="selectRecord(item)">
                  <view class="checkbox-icon"></view>
                </view>
              </view>
            </view>
            <view class="record-details">
              <view class="detail-row">
                <text class="detail-label">服务费用：</text>
                <text class="detail-value price">¥{{ (item.payPrice || 0) / 100 }}</text>
              </view>
              <view class="detail-row">
                <text class="detail-label">预约时间：</text>
                <text class="detail-value">{{ formatDateTime(item.startTime) + '～' + formatDateTime(item.endTime) }}</text>
              </view>
              <view class="detail-row">
                <text class="detail-label">就诊医院：</text>
                <text class="detail-value">{{ item.hospitalName || '-' }}</text>
              </view>
              <view class="detail-row">
                <text class="detail-label">订单号：</text>
                <text class="detail-value">{{ item.id || '-' }}</text>
              </view>
            </view>
          </view>
        </view>
      </scroll-refresh>

      <!-- 空数据状态 -->
      <view class="empty-state" v-if="isDataLoaded && (!recordList || recordList.length === 0)">
        <text class="empty-text">暂无陪诊记录</text>
      </view>
    </view>

    <!-- 底部确定按钮 -->
    <view class="bottom-btn-wrapper" v-if="showSelectFeature">
      <view class="confirm-btn" @click="confirm">
        <text>确定</text>
      </view>
    </view>

    <!-- 弹窗选择器 -->
    <view class="picker-container">
      <selectData ref="hospitalPicker" placeholder="输入医院名称，模糊匹配搜索" :localdata="hospitalOptions" popup-title="请选择就诊医院" @change="onHospitalChange"></selectData>
      <selectData ref="deptPicker" placeholder="输入科室名称，模糊匹配搜索" :localdata="deptOptions" popup-title="请选择就诊科室" @change="onDeptChange"></selectData>
      <selectData ref="doctorPicker" placeholder="输入医生名称，模糊匹配搜索" :localdata="doctorOptions" popup-title="请选择就诊医生" @change="onDoctorChange"></selectData>
      <timePicker ref="timePicker" :value="timeMap" type="daterange" @change="handleTimeChange" :show="timePickerVisible"
        @cancel="timePickerVisible = false"></timePicker>
    </view>

    <!-- 隐藏容器 -->
    <view class="lineHide">
      <dataPicker ref="selectCity" :localdata="provinceMap" popup-title="请选择就诊城市" @change="onchangeCity" @nodeclick="onnodeclick"></dataPicker>
    </view>
  </view>
</template>

<script>
import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'
import dataPicker from '../../components/uni-data-picker/uni-data-picker.vue'
import timePicker from '../../components/uni-datetime-picker/uni-datetime-picker.vue'
import selectData from '../../components/select-data.vue'
import serverOptions from '@/config/env/options'

export default {
  components: {
    uniNavBar,
    dataPicker,
    timePicker,
    selectData
  },
  data() {
    return {
      iconRightArrow: this.$static_ctx + "image/business/hulu-v2/icon-right-arrow.png",
      // 接收传递的参数
      orderId: '',
      providerId: '',
      fromPage: '', // 添加来源页面标识
      // 是否已初始化数据
      isInitialized: false,
      // 是否已加载数据
      isDataLoaded: false,
      // 筛选条件
      filterParams: {
        startStartTime: '', // 开始时间
        endStartTime: '', // 结束时间
        hospitalId: '', // 医院ID
        hospitalName: '', // 医院名称
        deptId: '', // 科室ID
        deptName: '', // 科室名称
        doctorName: '', // 医生名称
        orderState: '', // 订单状态
        id: '', // 订单id
        providerId: '', // 服务商id
        userId: '' // 用户id
      },
      // 城市相关数据
      provinceMap: [],
      provinceValue: null,
      // 城市和医院选择
      hospitalQuery: [],
      hospitalName: '',
      queryOptions: {
        provinceValue: [],
      },
      // 下拉刷新配置
      downOption: {
        auto: false
      },
      upOption: {
        auto: false,
        empty: {
          top: 0,
          zIndex: 999,
        },
        onScroll: true
      },
      mescroll: null,
      // 记录列表数据
      recordList: [],
      // 筛选选项
      timeOptions: [
        { text: '全部', value: '' },
        { text: '今天', value: 'today' },
        { text: '本周', value: 'week' },
        { text: '本月', value: 'month' },
        { text: '今年', value: 'year' }
      ],
      hospitalOptions: [], // 医院选项
      deptOptions: [], // 科室选项
      doctorOptions: [], // 医生选项
      timePickerVisible: false,
      timeMap: [], // 时间选择器的值
      city: '', // 城市
      province: '', // 省份
      selectedRecords: [], // 改为数组，支持多选
      preSelectedIds: [], // 存储预选择的ID
    }
  },
  computed: {
    // 添加计算属性判断是否显示选择功能
    showSelectFeature() {
      return this.fromPage === 'model';
    },
    // 获取选中的城市显示
    getSelectCity() {
      return this.queryOptions.provinceValue.join()
    }
  },
  async mounted() {
    // 获取城市列表
    await this.getProvinceList();
  },
  onLoad(options) {
    console.log('options传递',options);

    // 接收传递过来的参数
    if (options.id) {
      this.orderId = options.id;
      this.filterParams.id = options.id;
    }
    if (options.userId) {
      this.filterParams.userId = options.userId;
    }
    if (options.providerId) {
      this.providerId = options.providerId;
      this.filterParams.providerId = options.providerId;
    } else {
      // 如果没有传入providerId，从配置中获取
      this.providerId = serverOptions.providerId;
      this.filterParams.providerId = serverOptions.providerId;
    }
    if (options.city) {
      // 对城市名进行URL解码
      this.city = decodeURIComponent(options.city);
    }
    if (options.province) {
      // 对省份名进行URL解码
      this.province = decodeURIComponent(options.province);
    }
    if (options.from) {
      this.fromPage = options.from;
    }
    // 接收已选择的记录ID
    if (options.selectedIds) {
      try {
        console.log('收到的selectedIds类型:', typeof options.selectedIds, '值:', options.selectedIds);

        // 处理不同格式的selectedIds
        if (typeof options.selectedIds === 'string') {
          // 如果是空字符串，则设为空数组
          if (options.selectedIds.trim() === '') {
            this.preSelectedIds = [];
          } else {
            // 否则按逗号分隔
            this.preSelectedIds = options.selectedIds.split(',');
          }
        } else if (Array.isArray(options.selectedIds)) {
          // 如果已经是数组了，直接使用
          this.preSelectedIds = options.selectedIds;
        } else {
          this.preSelectedIds = [];
        }

        console.log('解析后的preSelectedIds:', this.preSelectedIds);
      } catch (error) {
        console.error('解析选中ID失败', error);
        this.preSelectedIds = [];
      }
    }

    console.log('城市信息：', {
      city: this.city,
      province: this.province
    });

    // 页面加载时初始化数据
    this.init();
  },
  methods: {
    // 根据状态码获取状态文本
    getStatusText(state) {
      const statusMap = {
        1: '待接入',
        2: '待支付',
        3: '待派单',
        4: '待接单',
        5: '待服务',
        6: '服务中',
        7: '已完成',
        8: '已取消'
      };
      return statusMap[state] || '未知状态';
    },

    // 格式化日期时间
    formatDateTime(timestamp) {
      if (!timestamp) return '';
      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');

      return `${year}.${month}.${day} ${hours}:${minutes}`;
    },

    // 设置时间范围
    setTimeRange(timeValue) {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const todayTimestamp = today.getTime();

      switch(timeValue) {
        case 'today':
          this.filterParams.startStartTime = todayTimestamp;
          this.filterParams.endStartTime = now.getTime();
          break;
        case 'week':
          // 本周一
          const weekStart = new Date(today);
          weekStart.setDate(today.getDate() - today.getDay() + 1);
          this.filterParams.startStartTime = weekStart.getTime();
          this.filterParams.endStartTime = now.getTime();
          break;
        case 'month':
          // 本月初
          const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
          this.filterParams.startStartTime = monthStart.getTime();
          this.filterParams.endStartTime = now.getTime();
          break;
        case 'year':
          // 本年初
          const yearStart = new Date(today.getFullYear(), 0, 1);
          this.filterParams.startStartTime = yearStart.getTime();
          this.filterParams.endStartTime = now.getTime();
          break;
        default:
          this.filterParams.startStartTime = '';
          this.filterParams.endStartTime = '';
          break;
      }
    },

    // 返回按钮
    back() {
      uni.navigateBack();
    },

    // 选择筛选条件
    selectFilter(type) {
      switch(type) {
        case 'time':
          this.timePickerVisible = true;
          this.$nextTick(() => {
            this.$refs.timePicker && this.$refs.timePicker.show();
          });
          break;
        case 'hospital':
          if (this.hospitalOptions.length === 0) {
            uni.showToast({
              title: '当前城市未录入医院数据',
              icon: 'none'
            });
            return;
          }
          this.$refs.hospitalPicker.show();
          break;
        case 'status': // 改为科室选择
          if (!this.filterParams.hospitalId) {
            uni.showToast({
              title: '请先选择医院',
              icon: 'none'
            });
            return;
          }
          this.$refs.deptPicker.show();
          break;
        case 'doctor':
          if (!this.filterParams.deptId) {
            uni.showToast({
              title: '请先选择科室',
              icon: 'none'
            });
            return;
          }
          this.$refs.doctorPicker.show();
          break;
      }
    },

    // 时间选择回调
    handleTimeChange(e) {
      console.log('时间选择变化：', e);
      if (e.length === 2) {
        const [start, end] = e;
        this.timeMap = [start, end];

        // 直接使用日期字符串，添加时分秒
        this.filterParams.startStartTime = start + ' 00:00:00';
        this.filterParams.endStartTime = end + ' 23:59:59';

        console.log('设置的时间范围：', {
          start: this.filterParams.startStartTime,
          end: this.filterParams.endStartTime
        });
      } else {
        this.timeMap = [];
        this.filterParams.startStartTime = '';
        this.filterParams.endStartTime = '';
      }
      this.timePickerVisible = false;
      // 重新加载数据
      this.init();
    },

    // 获取医院列表
    async getHospital({province, city}) {
      try {
        let {data:{records:hospitalQuery}} = await this.$api.hospital.hospitalQueryPage({
          current: 0,
          size: 1000,
          condition: {
            province: province.replace(/省|市/g,''),
            // 去掉字符串里的市和市辖区还有市市辖区 使用正则祛除
            city: city.replace(/市市辖区|市辖区|市/g,''),
          }
        });
        hospitalQuery.map(e => {
          e.text = e.hospitalName;
          e.value = e.id
        });
        console.log('hospitalQuery', hospitalQuery);
        this.hospitalOptions = hospitalQuery;

        // 如果没有查到医院，提示用户
        if (this.hospitalOptions.length === 0) {
          uni.showToast({
            title: '当前城市未录入医院数据',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取医院列表失败:', error);
        uni.showToast({
          title: '获取医院列表失败',
          icon: 'none'
        });
      }
    },

    // 获取科室列表
    async getDeptList(hospitalId) {
      try {
        const {data} = await this.$api.hospital.crawlershospitaldeptQuery({id: hospitalId});
        this.deptOptions = data.map(e => ({
          text: e.name,
          value: e.id
        }));
      } catch (error) {
        console.error('获取科室列表失败:', error);
        uni.showToast({
          title: '获取科室列表失败',
          icon: 'none'
        });
      }
    },

    // 获取医生列表
    async getDoctorList({hospitalId, deptId}) {
      try {
        const {data} = await this.$api.hospital.crawlershospitaldoctor({hospitalId, deptId});
        this.doctorOptions = data.map(e => ({
          text: e.name,
          value: e.id
        }));
      } catch (error) {
        console.error('获取医生列表失败:', error);
        uni.showToast({
          title: '获取医生列表失败',
          icon: 'none'
        });
      }
    },

    // 选择医院回调
    onHospitalChange({detail:{value}}) {
      if (!value || !value[0]) return;
      this.filterParams.hospitalId = value[0].value;
      this.filterParams.hospitalName = value[0].text;
      this.hospitalName = value[0].text;
      // 清空科室和医生
      this.filterParams.deptId = '';
      this.filterParams.deptName = '';
      this.filterParams.doctorName = '';
      this.deptOptions = [];
      // 获取科室列表
      this.getDeptList(value[0].value);
      this.init();
    },

    // 选择科室回调
    onDeptChange({detail: {value}}) {
      if (!value || !value[0]) return;
      this.filterParams.deptId = value[0].value;
      this.filterParams.deptName = value[0].text;
      // 清空医生
      this.filterParams.doctorName = '';
      this.doctorOptions = [];
      // 获取医生列表
      this.getDoctorList({
        hospitalId: this.filterParams.hospitalId,
        deptId: value[0].value
      });
      this.init();
    },

    // 选择医生回调
    onDoctorChange({detail: {value}}) {
      if (!value || !value[0]) return;
      this.filterParams.doctorName = value[0].text;
      this.init();
    },

    // 选择记录
    selectRecord(item) {
      const index = this.selectedRecords.findIndex(record => record.id === item.id);
      if (index > -1) {
        // 如果已经选中，则取消选中
        this.selectedRecords.splice(index, 1);
      } else {
        // 如果未选中，则添加到选中列表
        this.selectedRecords.push(item);
      }
    },

    // 确认选择
    confirm() {
      if (this.selectedRecords.length === 0) {
        uni.showToast({
          title: '请至少选择一条记录',
          icon: 'none'
        });
        return;
      }

      console.log('选中的记录:', this.selectedRecords);

      // 将选择的记录传回前一页面
      const recordsData = this.selectedRecords.map(record => ({
        id: record.id,
        text: `${record.hospitalName || ''} ${this.formatDateTime(record.startTime)}`
      }));

      console.log('处理后准备发送的数据:', recordsData);

      // 使用全局事件触发器传递数据
      uni.$emit('selectAccompanyRecord', recordsData);

      // 关闭当前页面
      uni.navigateBack();
    },

    // 根据 ID 回显选中状态
    restoreSelectedRecords() {
      if (!this.preSelectedIds || this.preSelectedIds.length === 0) return;

      console.log('开始恢复选中状态，preSelectedIds:', this.preSelectedIds);
      console.log('当前记录列表数量:', this.recordList.length);

      // 根据预选择的ID查找对应的记录
      this.selectedRecords = this.recordList.filter(record => {
        // 将ID转为字符串进行比较，避免类型不匹配问题
        const recordIdStr = String(record.id);
        const isSelected = this.preSelectedIds.some(id => String(id) === recordIdStr);
        if (isSelected) {
          console.log('找到匹配的记录:', record.id, record.hospitalName);
        }
        return isSelected;
      });

      console.log('已恢复选中状态，共选中', this.selectedRecords.length, '条记录');
    },

    // 下拉刷新回调
    returnFn(obj) {
      const that = this;
      setTimeout(() => {
        // 只处理必要的参数
        const processedParams = {
          // 根据来源页面决定使用 userId 还是 id
          ...(this.fromPage === 'model' ? { userId: this.filterParams.userId } : { id: this.filterParams.id }),
          orderState: 7,
          providerId: this.filterParams.providerId || null,
          startStartTime: this.filterParams.startStartTime || null,
          endStartTime: this.filterParams.endStartTime || null,
          hospitalName: this.filterParams.hospitalName || null,
          deptName: this.filterParams.deptName || null,
          doctorName: this.filterParams.doctorName || null
        };

        console.log('请求参数：', processedParams);

        let params = {
          current: obj.pageNum,
          size: obj.pageSize,
          condition: processedParams
        };

        // 根据来源选择不同的接口
        const apiMethod = this.fromPage === 'model'
          ? 'accompanybookQuery'
          : 'accompanybookQueryRecordPage';

        console.log('使用的API方法:', apiMethod);

        // 调用对应的API
        that.$api.providerManagement[apiMethod](params).then(res => {
          // 标记数据已初始化
          that.isInitialized = true;

          // 直接从res.data中获取records
          let data = res.data?.records || [];

          // 第一页时清空列表
          if (obj.pageNum === 1) {
            that.recordList = [];
          }

          // 确保data是数组
          if (Array.isArray(data)) {
            that.recordList = [...that.recordList, ...data];

            // 在第一页数据加载完成后恢复选中状态
            if (obj.pageNum === 1 && that.preSelectedIds.length > 0) {
              that.restoreSelectedRecords();
            }
          }

          // 标记数据已加载
          that.isDataLoaded = true;

          // 调用回调，传入实际的数据长度
          obj.successCallback && obj.successCallback(data, {
            curPageLen: data.length,
            totalPage: res.data?.pages || 1,
            totalSize: res.data?.total || 0
          });
        }).catch(err => {
          that.isInitialized = true;
          that.isDataLoaded = true;
          console.error('获取陪诊记录失败:', err);
          obj.successCallback && obj.successCallback([], {
            curPageLen: 0,
            totalPage: 1,
            totalSize: 0
          });
          uni.showToast({
            title: '获取数据失败',
            icon: 'none'
          });
        });
      }, 500);
    },

    // 初始化滚动组件
    scrollInit(scroll) {
      scroll.optUp.page.num = 1;
      scroll.optUp.page.size = 10;
      this.mescroll = scroll;

      // 在初始化滚动组件后立即触发下拉刷新
      this.init();
    },

    // 初始化页面数据
    init() {
      // 标记数据尚未加载完成
      this.isDataLoaded = false;

      this.$nextTick(() => {
        if (this.mescroll) {
          this.mescroll.triggerDownScroll();
        }
      });
    },

    formatDateDisplay(timestamp) {
      if (!timestamp) return '';
      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}.${month}.${day}`;
    },

    // 添加新的方法 goToDetail
    goToDetail(item) {
      // 跳转到陪诊记录详情页，使用与订单中心页面相同的导航方式
      this.$navto.push('accompanyDetails', {
        id: item.id,
        isServer: true,
        from: 'record' // 添加来源参数，防止循环跳转
      });
    },

    // 关闭所有选择器
    closeAllPickers() {
      this.timePickerVisible = false;
      this.$refs.hospitalPicker && this.$refs.hospitalPicker.close();
      this.$refs.deptPicker && this.$refs.deptPicker.close();
      this.$refs.doctorPicker && this.$refs.doctorPicker.close();
      this.$refs.timePicker && this.$refs.timePicker.close();
    },

    // 获取省市列表
    async getProvinceList() {
      try {
        // 获取服务商信息 - 使用传入的providerId或配置中的值
        let providerId = this.providerId || serverOptions.providerId;
        if (!providerId) {
          console.error('无效的providerId');
          return;
        }

        console.log('使用的providerId:', providerId);
        let provinceValue = (await this.$api.providerManagement.accompanyproviderQueryOne({
          id: providerId
        })).data;
        this.provinceValue = provinceValue;

        // 获取城市列表
        this.provinceMap = await this.accompanyproviderQueryPage(provinceValue);
      } catch (error) {
        console.error('获取省市列表失败:', error);
        uni.showToast({
          title: '获取省市列表失败',
          icon: 'none'
        });
      }
    },

    async accompanyproviderQueryPage(provinceValue) {
      try {
        // 判断当前是否是平台
        let queryOptions;
        if (serverOptions.source === 1) {
          // 平台获取所有服务商
          const res = await this.$api.providerManagement.getAccompanyproviderAll();
          queryOptions = res.data || [];
          console.log('获取所有服务商数据:', queryOptions);
        } else {
          // 非平台只获取当前服务商
          queryOptions = [provinceValue];
          console.log('使用当前服务商数据:', queryOptions);
        }

        // 检查查询结果是否有效
        if (!queryOptions || queryOptions.length === 0) {
          console.error('无有效的服务商数据');
          return [];
        }

        // 生成城市映射
        let cityMap = this.getCityMap(queryOptions);
        return [...new Set(cityMap)].filter(e=>e);
      } catch (error) {
        console.error('处理服务商数据失败:', error);
        return [];
      }
    },

    // 城市数据处理
    getCityMap(AccompanyproviderAll) {
      try {
        return AccompanyproviderAll.reduce((acc, current) => {
          // 确保数据有效
          if (!current || !current.province || !current.city) {
            console.warn('跳过无效的省市数据:', current);
            return acc;
          }

          let provinceMap = current.province.split(',');
          let cityMap = current.city.split('$');

          provinceMap.forEach((provinceItem, index) => {
            // 检查是否有对应的城市数据
            if (!cityMap[index]) {
              console.warn(`省份 ${provinceItem} 没有对应的城市数据`);
              return;
            }

            let currentCityMap = cityMap[index].split(',').filter(e=>e);
            let prov = acc.find(p => p.value === provinceItem);
            if (!prov) {
              acc.push(prov = {text: provinceItem, value: provinceItem, children: []});
            }
            prov.children.push(...currentCityMap);
          });

          return acc;
        }, []).map(e => {
          e.children = [...new Set(e.children)];
          e.children = e.children.map(c => ({text: c, value: c}));
          return e;
        });
      } catch (error) {
        console.error('处理城市数据映射失败:', error);
        return [];
      }
    },

    // 城市选择变化处理
    onchangeCity({detail:{value}}) {
      console.log('value', value);
      this.queryOptions.provinceValue = value.map(e=>e.value);
      console.log('this.queryOptions.provinceValue', this.queryOptions.provinceValue);
      if (this.queryOptions.provinceValue.length >= 2) {
        let [province, city] = this.queryOptions.provinceValue;
        this.getHospital({province, city});
      }
    },

    onnodeclick() {},
  }
}
</script>

<style lang="scss" scoped>
.accompany-record {
  min-height: 100vh;
  height: 100vh;
  background-color: #F4F6FA;
  display: flex;
  flex-direction: column;

  ::v-deep .mescroll-uni-content {
    height: calc(100vh - 88rpx - 136rpx) !important;
    background-color: #F4F6FA !important;
  }
}

/* 筛选栏样式 */
.filter-bar-wrapper {
  position: relative;
  z-index: 1;
  background-color: #FFFFFF;
  border-bottom: 1rpx solid #F2F2F2;
  width: 100%;
}

.filter-bar {
  display: flex;
  width: 100%;
  height: 88rpx;
}

.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: relative;
}

.filter-text {
  font-size: 28rpx;
  color: #333333;
  margin-right: 8rpx;
  font-weight: 500;
}

.filter-arrow {
  width: 24rpx;
  height: 24rpx;
  transform: rotate(90deg);
  position: relative;
  top: 2rpx;
}

/* 选择数量提示样式 */
.selected-count-bar {
  padding: 20rpx 32rpx;
  font-size: 28rpx;
  color: #1687F7;
  text-align: left;
}

/* 记录列表样式 */
.record-list {
  flex: 1;
  background-color: #F4F6FA;
  padding: 20rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 88rpx - 136rpx - 68rpx); /* 减去选择数量提示的高度 */
}

.scroll-refresh-main {
  flex: 1;
  height: 100%;
  position: relative;
  background-color: #F4F6FA;
}

.record-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  position: relative;
  z-index: 1;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.record-header {
  display: flex;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #F5F5F5;
}

.service-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.service-status-wrapper {
  display: flex;
  align-items: center;
}

.service-status {
  font-size: 28rpx;
  color: #666666;
  margin-right: 12rpx;
}

.record-details {
  padding: 20rpx 30rpx;
}

.detail-row {
  display: flex;
  margin-bottom: 16rpx;
}

.detail-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #999999;
}

.detail-value {
  flex: 1;
  font-size: 28rpx;
  color: #666666;
}

.price {
  color: #FF5500;
  font-weight: 500;
}

/* 底部按钮样式 */
.bottom-btn-wrapper {
  padding: 20rpx 32rpx;
  background-color: #FFFFFF;
  border-top: 1rpx solid #F2F2F2;
  position: relative;
  z-index: 1;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.confirm-btn {
  height: 96rpx;
  line-height: 96rpx;
  text-align: center;
  background-color: #00B484;
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 弹窗选择器样式 */
.picker-container {
  position: absolute;
  width: 0;
  height: 0;
  overflow: hidden;
}

/* 空数据状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #F4F6FA;
  z-index: 1;

  .empty-text {
    font-size: 32rpx;
    color: #999;
    font-weight: 500;
  }
}

.status {
  &-1 {
    color: #00B484;
  }
  &-2 {
    color: #FF6B6B;
  }
  &-3 {
    color: #999999;
  }
}

/* 时间容器样式 */
.time-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.date-range {
  display: flex;
  align-items: center;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
}

.date-label {
  color: #666;
  font-size: 28rpx;
  margin: 0 4rpx;
}

.date-value {
  color: #333;
  font-size: 28rpx;
  margin: 0 8rpx;
}

.date-create {
  margin-left: 16rpx;
}

::v-deep .uni-date-x {
  height: 0 !important;
  width: 0 !important;
  overflow: hidden !important;
}

::v-deep .uni-date {
  height: 0 !important;
  width: 0 !important;
  overflow: hidden !important;
}

/* 更新选择指示器样式 */
.select-indicator {
  width: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  .checkbox-icon {
    width: 32rpx;
    height: 32rpx;
    border: 2rpx solid #CCCCCC;
    border-radius: 50%;
    position: relative;
    transition: all 0.3s;
  }

  &.selected {
    .checkbox-icon {
      background: #00B484;
      border-color: #00B484;

      &::after {
        content: '';
        position: absolute;
        left: 50%;
        top: 50%;
        width: 16rpx;
        height: 8rpx;
        border: 4rpx solid #fff;
        border-top: none;
        border-right: none;
        transform: translate(-50%, -60%) rotate(-45deg);
      }
    }
  }
}

/* 添加隐藏样式 */
.lineHide {
  width: 0;
  overflow: hidden;
  height: 0;
}
</style>
