<template>
  <view class="">
    <!-- 弹幕滚动 -->
    <view class="dmGroup">
        <view class="GroupBox" v-for="(leftItem,leftIndex) in GroupBoxLeftMap" :style="'transform: translateX('+GroupBoxLeftMap[leftIndex]+'px);width:'+dmWidth+'px'" ref="GroupBox">
            <view class="GroupSonBox">
              <view class="GroupDM" ref="GroupDM" :class="{addGroupDM:item.addFlag}" :dataId="item.id" v-for="(item,index) in dmMap.filter((e,eIndex)=>eIndex % 2 === 0)" :key="index">
                {{item.username}}：{{item.word}}
              </view>
            </view>
            <view class="GroupSonBox">
              <view class="GroupDM" ref="GroupDM" :class="{addGroupDM:item.addFlag}" :dataId="item.id" v-for="(item,index) in dmMap.filter((e,eIndex)=>eIndex % 2 === 1)" :key="index">
                {{item.username}}：{{item.word}}
              </view>
            </view>
        </view>
    </view>
    <!-- 输入框 -->
    <view class="inputWishingBox" @click="openWinshing">
        <view class="but2" :style="{backgroundImage:'url('+qfBtn+')',bottom:butBottom +'rpx'}">发表许愿</view>
        <image class="but1" :src="qfButBot" alt=""></image>
        <image class="bottom" :src="qfBottom" alt=""></image>
    </view>
    <!-- 遮罩层 -->
    <view class="maskDM" v-if="isFocus" @click="isFocus = false"></view>
    <!-- 弹幕模版 -->
    <view class="dmModule" v-if="isFocus">
      <!-- 弹幕模版 -->
      <view class="dmModuleBox">
        <!-- 模版颜色块 -->
        <view class="dmModuleColor"></view>
        <!-- 模版存储 -->
        <view class="dmModuleLevel">
          <view class="dmModuleContent" v-for="(item,index) in dmModuleMap" :key="item.id">
            <view class="dmWord" @click="addWord(item.word)">{{item.word}}</view>
            <view @click="addWord(item.word)" class="selectBtn" :class="{noSelectBtn:noSelect}">{{noSelect ? '不可选择' : '选择祈福'}}</view>
          </view>
        </view>
      </view>
      <view class="dmInputFa">
        <textarea class="dmInput" :auto-height='true' :maxlength="max" type="text" v-model="currentWord" :focus="isFocus" />
        <view class="dmTag" :class="{dmTagExceed:currentWord.length >= max}">{{currentWord.length}}/{{max}}</view>
        <view @click="pointInsert" class="pushBtn">发送</view>
      </view>
    </view>
    <!-- 积分弹窗组件 -->
    <codePopup v-if="isShow"></codePopup>
  </view>
</template>

<script>
  import { mapState } from "vuex";
  import calabashApis from "@/modules/common/api/calabash.js"
  import common from '@/common/util/main'
  import codePopup from './codePopup.vue'
  export default {
    name: '',
    components:{
      codePopup
    },
    data() {
      return {
        GroupBoxLeftMap:[0,0],
        calabashDM: this.$static_ctx + "image/business/hulu-v2/calabashDM.png",
        qfBottom: this.$static_ctx + "image/business/hulu-v2/qfBottom.png",
        qfButBot: this.$static_ctx + "image/business/hulu-v2/qfButBot.png",
        qfBtn: this.$static_ctx + "image/business/hulu-v2/qfBtn.png",
        dmMap:[],
        GroupSonBox:[],
        dmWidth:0,
        isFocus:false,
        // 当前话术
        currentWord:'',
        max:50,
        dmModuleMap:[],
        current:1,
        hasNextPage:true,
        currentInsertWord:null,
        observer:null,
        butBottom:24,
        noSelect:false,
        isShow:false,
      }
    },
    watch:{
      currentWord(){
        if(this.currentWord.length === 0){
          this.noSelect = false
        }
        if(this.currentWord.length === this.max){
          this.noSelect = true
        }
      }
    },
    methods: {
      addWord(word){
        if(this.noSelect) return uni.showToast({
          icon:"none",
          title:'清空输入框，可重新选择'
        })
        this.noSelect = true
        if(this.currentWord.length + word.length <= this.max){
          this.currentWord+=word;
        }else{
          this.currentWord+=word.slice(0,this.max - this.currentWord.length)
        }
      },
      // 监听当前盒子 保证弹幕可以实时插入
      bindObserver(){
        // 清空旧的监听器
        if(this.observer) this.observer.disconnect()
        this.observer = new IntersectionObserver((entries, observer) => {
          let target = entries[0].target
          // 检测当前是否有弹幕对象 如果有就将其插入到本次进入视口的dom后面
          if(this.currentInsertWord){
            let dataId = target.getAttribute('dataId')
            let currntIndex = 0;
            this.dmMap.forEach((e,index)=>{if(e.id === dataId) currntIndex = index})
            this.dmMap.splice(currntIndex+2,0,this.currentInsertWord)
            this.currentInsertWord = null;
            this.$nextTick(()=>{
              this.changeWidth()
              this.bindObserver()
            })
          }
        }, {threshold: 0.5});
        const targetElement = [...document.querySelectorAll('.GroupDM')];
        targetElement.map(e=>this.observer.observe(e))
      },
      // 插入弹幕
      async pointInsert(){
         this.isFocus = false
         let launchParams = common.getKeyVal('system', 'launchParams')
         let queryOptions = {
           username:launchParams.username,
           accountId:launchParams.accountId,
           word:this.currentWord,
           userType:1
         }
         let data = await calabashApis.pointwishbarrageInsert(queryOptions)
         // 存储当前的弹幕对象
         if(data?.data?.id){
           this.currentInsertWord = {
            username:launchParams.username,
            word:this.currentWord,
            id:data?.data?.id,
            addFlag:true
          }
         }
         this.currentWord = ''
         uni.showToast({title: '祈福成功 好运+1',duration: 2000});
         this.isShow = true
      },
      // 获取弹幕群
        async getDMs(){
          if(this.current === 1) this.dmMap = [];
          let {data} = await calabashApis.pointwishbarrageQueryPage({size:20,current:this.current})
          this.dmMap.push(...data.records)
          // 判断当前的弹幕群长度是否等于所有的长度 如果等于则不允许继续加载
          if(data.total <= this.dmMap.length){
            this.hasNextPage = false
          }
          this.$nextTick(()=>{
            this.changeWidth()
            this.bindObserver()
          })
        },
      // 获取祈福模版
      async getModule(){
        let {data} = await calabashApis.pointwishtemplateQueryPage({size:20,current:1,condition:{wordType:2}})
        this.dmModuleMap = data.records
      },
        // 挪动dom
        moveDM(){
            requestAnimationFrame(()=>{
              if(this.GroupBoxLeftMap[0] <= -this.dmWidth) {
                if(this.hasNextPage){
                  this.current+=1
                  this.getDMs()
                }
              }
              this.GroupBoxLeftMap.map((length,index,arr)=>{
                if(length <= -this.dmWidth) length = 0
                this.$set(arr,index,length-=1)
              })
                this.moveDM()
            })
        },
        openWinshing(){
          let callBack = ()=>{
            this.butBottom-=2;
            if(this.butBottom <=14){
              setTimeout(()=>{
                this.butBottom = 24;
                this.isFocus = true;
              },100)
              return
            }
            requestAnimationFrame(callBack)
          }
          requestAnimationFrame(callBack)
        },
        changeWidth(){
          let GroupBox = this.$refs.GroupBox[0].$children
          let maxWidth = 0;
          GroupBox.map(ele=>ele.$el.getBoundingClientRect()).map(e=>{
            if(maxWidth < e.width) maxWidth = e.width
          })
          this.dmWidth = maxWidth
        }
    },
    async mounted() {
        this.getModule()
        await this.getDMs()
      // 读取弹幕条的宽度 自适应滚动
        this.changeWidth()
        this.moveDM()
    }
  }
</script>

<style scoped lang="scss">
  .GroupBox{
      display: flex;
      flex-wrap: wrap;
  }
  .dmGroup{
    width: 100vw;
    // height: 104rpx;
    position: fixed;
    bottom: 279rpx;
    box-sizing: border-box;
    display: flex;
    z-index: 999;
    .GroupSonBox{
        display: flex;
    }
    .GroupDM{
      white-space: nowrap;
      height: 39rpx;
      display: flex;
      align-items: center;
      padding: 0 18rpx;
      color: #7B2519;
      font-size: 22rpx;
      margin-right: 62rpx;
      background-size: 100% 100%;
      margin-bottom: 16rpx;
      background-color: rgba(240, 240, 240, 0.76);
      border-radius: 40rpx;
    }
    .addGroupDM{
      background-image: linear-gradient(to left, #F4DC90, #FBECD3);
      border: 4rpx solid transparent;
      position: relative;
      background-clip: padding-box; /*important*/
      &::before{
        content: '';
          position: absolute;
          top: 0;
          right: 0;
          left: 0;
          bottom: 0;
          z-index: -1;
          margin: -4rpx;
          border-radius: inherit; /*important*/
          background: linear-gradient(to bottom, #FD9A2F, #FF7436);
      }
    }
  }
  .selectBtn{
    background-color: #B9F5E4;
    width: 122.92rpx;
    height: 43.06rpx;
    line-height: 43.06rpx;
    border-radius: 32rpx;
    text-align: center;
    color: #22BF98;
    font-size: 24rpx;
    box-sizing: border-box;
    margin-left: auto;
  }
  .noSelectBtn{
    background-color: #808080a3;
    color:white;
  }
  .maskDM{
    position: fixed;
    z-index: 99;
    bottom: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
  }
  .dmModule{
    position: fixed;
    z-index: 999999999999999999;
    bottom: 0;
    left: 50%;
    width: 100vw;
    transform: translateX(-50%);
    background-color: #F2F2F2;
    border-radius: 20rpx 20rpx 0rpx 0rpx;
    .dmModuleBox{
      height: 390rpx;
      width: 100%;
      border-radius: 20rpx 20rpx 0 0;
      overflow: scroll;
      box-sizing: border-box;
      .dmModuleColor{
        position: absolute;
        z-index: 0;
        width: 100%;
        height: 140rpx;
        border-radius: 20rpx 20rpx 0 0;
        background-image: linear-gradient(to top, rgba(240, 240, 240, 0), #dcf8f0);
        left: 0;
      }
      .dmModuleLevel{
        position: absolute;
        height: 390rpx;
        overflow: scroll;
        width: 100%;
        z-index: 99999999;
        left: 0;
        padding: 0 25rpx;
        box-sizing: border-box;

      }
      .dmModuleContent{
        min-height: 98rpx;
        width: 100%;
        line-height: 98rpx;
        font-size: 24rpx;
        color: #282828;
        border-bottom: 2rpx dotted #EEEEEE;
        display: flex;
        align-items: center;
        .dmWord{
          width: calc(100% - 148rpx);
          padding: 20rpx;
        }
      }
    }
    .dmInputFa{
      position: relative;
      box-sizing: border-box;
      padding: 0rpx 20rpx 0rpx 20rpx;
      display: flex;
      min-height: 83.33rpx;
      margin: 10rpx 0 66rpx 0;
    }
    .dmInput{
      width: 556.25rpx;
      height: 83.33rpx;
      border-radius: 15rpx;
      padding: 10rpx;
      box-sizing: border-box;
      background-color: white;
      padding-right: 80rpx;
    }
    .pushBtn{
      width: 122.92rpx;
      height: 83.33rpx;
      line-height: 83.33rpx;
      background-color: #22BF98;
      font-size: 31.35rpx;
      color: white;
      text-align: center;
      border-radius: 13.89rpx;
      margin-left: auto;
      margin-top: auto;
    }
    .dmTag{
      width: 70rpx;
      text-align: left;
      position: absolute;
      z-index: 999999;
      left: 500.25rpx;
      top: 50%;
      transform: translateY(-50%);
      font-size: 24.3rpx;
      color: #696969;

    }
    .dmTagExceed {
      animation: shake 800ms ease-in-out;
    }
    @keyframes shake { /* 水平抖动，核心代码 */
      10%, 90% { transform: translate3d(-2rpx, 0, 0);color:red }
      20%, 80% { transform: translate3d(+4rpx, 0, 0);color:red }
      30%, 70% { transform: translate3d(-8rpx, 0, 0);color:red }
      40%, 60% { transform: translate3d(+8rpx, 0, 0);color:red }
      50% { transform: translate3d(-8rpx, 0, 0); }
    }

  }
  .inputWishingBox{
    display: flex;
    position: fixed;
    z-index: 999;
    bottom: 58rpx;
    left: 50%;
    width: 100vw;
    transform: translateX(-50%);
    border-radius: 15rpx;
    background-color: white;
      .bottom{
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 453.47rpx;
          height: 94.44rpx;
      }
      .but1{
          position: absolute;
          bottom: 14rpx;
          left: 50%;
          transform: translateX(-50%);
          width: 438rpx;
          height: 94.44rpx;
          z-index: 2;
      }
      .but2{
          position: absolute;
          bottom: 24rpx;
          left: 50%;
          transform: translateX(-50%);
          background-size: 100% 100%;
          text-align: center;
          font-size: 36.17rpx;
          color: #7B2519;
          width: 438rpx;
          height: 94.44rpx;
          line-height: 94.44rpx;
          z-index: 3;
      }
    .wishingInput{
      width: 463rpx;
      height: 93rpx;
      font-size: 30rpx;
      padding-left: 25rpx;
      box-sizing: border-box;
      text-align: left;
      line-height: 93rpx;
      color: gray;
      &::placeholder{
        color: #9C9B9B;
      }
    }
    .wishingButton{
      width: 195rpx;
      height: 93rpx;
      line-height: 93rpx;
      background-color: #ffcf44;
      color: #7B2519;
      font-size: 30rpx;
      text-align: center;
      border-radius:0 15rpx 15rpx 0;
    }
  }
</style>
