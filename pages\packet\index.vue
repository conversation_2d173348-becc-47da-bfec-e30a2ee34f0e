<template>
  <view class="main">
    <web-view :src="src"></web-view>
  </view>
</template>

<script>
export default {
  components: {

  },
  data() {
    return {
      src: '',
      title:'',
      gbPacketScene:'',
      deviceId: '',
      visitTime: Date.now()
    }
  },
  onLoad(paramsObj = {}) {
    const query = this.$Route.query
    const that = this
    if (!this.$validate.isNull(query)) {
      that.gbPacketScene = query.gbPacketScene
      that.deviceId = query.deviceId
      // this.src = query.src
      uni.setNavigationBarTitle({
        title: '欢迎使用绿葆自助取袋机'
      })
      if(that.deviceId && that.deviceId.length!==0) {
        that.$uniPlugin.loading('加载中', true)
        that.getAppScreenPacketUrl()
      }else if (that.gbPacketScene && that.gbPacketScene.length!==0){
        that.$uniPlugin.loading('加载中', true)
        that.getH5Link()
      }else {
        that.errorTip()
      }
    } else {
      that.errorTip()
    }
    // this.setWebViewCss();
  },
  onShow() {
    let visitTimeNew = Date.now();
    let visitTime = this.visitTime
    if (visitTime!=null){
      if ((visitTimeNew - visitTime)/1000 >= 20) {
        this.$navto.replaceAll('Index')
      }
    }
  },
  methods: {
    /**
     * 错误提示
     */
    errorTip(){
      this.$uniPlugin.toast('网络异常，请重新扫码！')
    },
    /**
     * 获取链接
     */
    getAppScreenPacketUrl(){
      const that = this
      this.$api.packet.getAppScreenPacketUrl({deviceId:this.deviceId}).then((res)=>{
        //todo
        if (res.data) {
          that.src = res.data
        }else {
          that.$navto.replaceAll('Index', {})
          // that.errorTip()
          // setTimeout(()=>{
          //   that.$navto.replaceAll('Index', {})
          // },3000)
        }
        that.$uniPlugin.hideLoading()
      }).catch((res)=>{
        this.$uniPlugin.hideLoading()
        that.errorTip()
      })
    },
    /**
     * 获取链接
     */
    getH5Link(){
      const that = this
      this.$api.packet.getH5Link({gbPacketScene:this.gbPacketScene}).then((res)=>{
        //todo
        if (res.data) {
          that.src = res.data
        }else {
          that.$navto.replaceAll('Index', {})
          // that.errorTip()
          // setTimeout(()=>{
          //   that.$navto.replaceAll('Index', {})
          // },3000)
        }
        that.$uniPlugin.hideLoading()
      }).catch((res)=>{
        this.$uniPlugin.hideLoading()
        that.errorTip()
      })
    },
    /**
     * web-view自定义设置高度
      */
    setWebViewCss(){
      var height = 0; //定义动态的高度变量，如高度为定值，可以直接写
      uni.getSystemInfo({
        //成功获取的回调函数，返回值为系统信息
        success: (sysinfo) => {
          height = sysinfo.windowHeight; //自行修改，自己需要的高度 此处如底部有其他内容，可以直接---(-50)这种
        },
        complete: () => {}
      });
      var currentWebview = this.$scope.$getAppWebview(); //获取当前web-view
      setTimeout(function() {
        var wv = currentWebview.children()[0];
        wv.setStyle({ //设置web-view距离顶部的距离以及自己的高度，单位为px
          top:40 , //此处是距离顶部的高度，应该是你页面的头部
          height:  height , //webview的高度
          scalable: false, //webview的页面是否可以缩放，双指放大缩小,
        })
      }, 500); //如页面初始化调用需要写延迟
    }
  }
}
</script>

<style lang="scss" scoped>
  .main{
    height: 100%;
    position: relative;
  }
</style>
