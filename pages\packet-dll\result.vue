<template>
  <page>
    <view slot="content" class="content">
      <image
        mode="widthFix"
        :src="file_ctx + 'static/image/business/packet-dll/icon-logo-dll.png'"
        class="icon-logo"
      />
      <view
        class="bag-outlet-box"
        :style="{ 'background-image': 'url('+ file_ctx + 'static/image/business/packet-dll/bag-outlet.png' +')' }"
        :class="{ error: outBagStatus == 3 }"
      >
        <template v-if="outBagStatus == 0">出袋中...</template>
        <template v-else-if="outBagStatus == 2">
          <image
            mode="widthFix"
            :src="file_ctx + 'static/image/business/packet-dll/icon-success.png'"
            class="icon-success"
          />
          出袋成功！
        </template>
        <template v-else-if="outBagStatus == 3">
          <image
            mode="widthFix"
            :src="file_ctx + 'static/image/business/packet-dll/icon-error.png'"
            class="icon-error"
          />
          出袋失败！
        </template>
      </view>

      <template v-if="outBagStatus == 3">
        <image
          mode="widthFix"
          :src="file_ctx + 'static/image/business/packet-dll/device-error.png'"
          class="device-error-img"
        />
      </template>

      <view class="sample-graph-box" v-else>
        <image
          mode="widthFix"
          :src="file_ctx + 'static/image/business/packet-dll/out-bag-sample-graph.png'"
          class="sample-graph-img"
        />
      </view>
      <view class="error-tips">
        如遇取袋问题，请拨打<text @tap="callPhone">{{ kefuPhone }}</text>
      </view>
      <view class="device-num" v-if="deviceCode">设备编码  {{ deviceCode.substring(deviceCode.length - 6) }}</view>
    </view>
  </page>
</template>

<script>
export default {
  data() {
    return {
      file_ctx: this.file_ctx,
      kefuPhone: '************',
      outBagStatus: 0, // 出袋状态 0-出袋中 2-出袋成功 3-出袋失败
      dllOrderId: '', // 出袋订单id
      checkOrderNum: 0, // 订单结果查询次数
      deviceCode: ''
    }
  },
  onLoad() {
    const query = this.$Route.query || {}
    if (this.$validate.isNull(query) || !query.dllOrderId) return
    this.dllOrderId = query.dllOrderId
    this.deviceCode = query.deviceCode
    this.start()
  },
  methods: {
    backHome() {
      setTimeout(() => {
        this.$navto.replaceAll('Index')
      }, 2000)
    },
    async start() {
      const res = await this.getDllOrder()
      if (!res) return setTimeout(this.start, 3000)
      const { status } = res.data
      if (status == 0) {
        // 最多查询3次 还返回出袋中就直接返回成功
        if (this.checkOrderNum === 4) {
          this.outBagStatus = 2
          // this.backHome()
          return
        }
        setTimeout(this.start, 3000)
        return
      }
      // this.backHome()
    },
    async getDllOrder() {
      this.checkOrderNum += 1
      const res = await this.$api.dll.dllGetOrder({ dllOrderId: this.dllOrderId })
      if (!res.data) return Promise.resolve(null)
      const { status } = res.data
      this.outBagStatus = status
      return Promise.resolve(res)
    },
    /**
     * 错误提示
     */
    errorTip(){
      this.$uniPlugin.toast('参数错误，请重新进入！')
    },
    callPhone() {
      wx.makePhoneCall({
        phoneNumber: this.kefuPhone, //仅为示例，并非真实的电话号码
        fail: res => {}
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.content {
  height: 100%;
  background-color: #fff;
  padding: 36rpx 46rpx calc(env(safe-area-inset-bottom) + 32rpx);
}

.icon-logo {
  width: 216rpx;
  height: 58rpx;
  margin-bottom: 66rpx;
}

.bag-outlet-box {
  width: 600rpx;
  height: 222rpx;
  background-repeat: no-repeat;
  background-size: 600rpx 222rpx;
  background-position: top;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 42rpx;
  color: #1FA2D1;
  margin: 0 auto 24rpx;
  .error {
    margin-bottom: 100rpx;
  }
}

.icon-success,
.icon-error {
  width: 55rpx;
  height: 55rpx;
  margin-right: 30rpx;
}

.device-error-img {
  display: block;
  width: 436rpx;
  height: 271rpx;
  margin: 0 auto 212rpx;
}

.sample-graph-box {
  background: #FFFFFF;
  border-radius: 18rpx;
  border: 2rpx solid #E8E8E8;
  margin: 0 auto;
  padding: 40rpx 65rpx 18rpx;
}

.sample-graph-img {
  width: 100%;
  height: 530rpx;
}

.error-tips {
  margin-top: 144rpx;
  font-size: 28rpx;
  color: #777777;
  text-align: center;
}

.device-num {
  font-size: 25rpx;
  color: #777777;
  margin-top: 25rpx;
  text-align: center;
}
</style>