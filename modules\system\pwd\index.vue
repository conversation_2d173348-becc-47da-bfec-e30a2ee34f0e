<template>
  <page>
    <view class="main" slot="content">
      <view class="m-t-20 main-body">
        <view class="inp">
          <text class="inp-l">手机号</text>
          <input class="inp-r" type="number" :maxlength="11" placeholder-class="f-w-medium" placeholder="请输入手机号" v-model="regForm.phone" :disabled="codeUserInfo.phone">
        </view>
        <view class="inp border-bottom-none">
          <text class="inp-l">验证码</text>
          <input class="inp-r width-calc-310" :maxlength="4" type="number" placeholder-class="f-w-medium" placeholder="请输入验证码" v-model="regForm.captcha" >
          <text class="verification-code" :class="isGetCodeStatus" @tap="getCode">{{ getVerificationTxt }}</text>
        </view>
      </view>
      <view class="m-t-20 main-body">
        <view class="inp">
          <text class="inp-l">新密码</text>
          <input class="inp-r" :maxlength="20" type="password" placeholder-class="f-w-medium" placeholder="请输入新密码" v-model="regForm.pwd" >
        </view>
        <view class="inp border-bottom-none">
          <text class="inp-l">再输入</text>
          <input class="inp-r" :maxlength="20" type="password" placeholder-class="f-w-medium" placeholder="请输入重复输入密码" v-model="regForm.pwd1" >
        </view>
      </view>
      <view class="m-t-50 main-bottom">
        <button type="" :disabled="regForm.phone === '' || regForm.captcha === '' || regForm.pwd === '' || regForm.pwd1 === ''" :class="{'opacity-5': regForm.phone === '' || regForm.captcha === '' || regForm.pwd === '' || regForm.pwd1 === ''}" class="b-btn" @click="onSubmit">
          确认
        </button>
      </view>
    </view>
  </page>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import md5 from 'js-md5'
// import ext from '@/service/ext';
export default {
  data() {
    return {
      title: '登录',
      num: 0,
      providerList: [],
      hasProvider: false,
      positionTop: 0,
      redirectUrl: '',
      getVerificationTxt: '获取验证码',
      isAllowGetNum: true,
      regForm: {
        phone: '', // 手机号码
        code: '', // 随机编码
        pwd: '', // 密码
        pwd1: '', // 密码
        captcha: '' // 验证码
      }
    }
  },
  computed: {
    ...mapState('user', {
      codeUserInfo: state => state.codeUserInfo
    }),
    isGetCodeStatus: function() {
      return {
        'is-get-code': !this.isAllowGetNum
      }
    },
    isConfirm() {
      let flag = true
      for (const k in this.regForm) {
        if (k && this.regForm.hasOwnProperty(k)) {
          if (!this.regForm[k]) {
            flag = false
            break
          }
        }
      }
      return flag
    }
  },
  onUnload(){
    // #ifdef MP-WEIXIN
    this.handleClickTrack('EndOperationDetailsPageView')
    // #endif
  },
  methods: {
    // #ifdef MP-WEIXIN
    handleClickTrack(type){
      let pages = getCurrentPages()
      let current = pages[pages.length - 1]; // 获取到当前页面的信息
      let pageInfo = __wxConfig.page && __wxConfig.page[`${current.route}.html`] || ''; // 内部属性
      getApp().globalData.sensors.track(type,
        {
          'page_name' : pageInfo?.window?.navigationBarTitleText,
          'first_operation_name' : '更多功能',
          'second_operation_name' : '修改密码',
        }
      ) 
    },
    // #endif
    ...mapActions('user', ['GetToken', 'Login', 'GetUnitList', 'ChangeUnit', 'GetWechatInfo', 'GetUserInfo']),
    initPosition() {
      /**
       * 使用 absolute 定位，并且设置 bottom 值进行定位。软键盘弹出时，底部会因为窗口变化而被顶上来。
       * 反向使用 top 进行定位，可以避免此问题。
       */
      this.positionTop = uni.getSystemInfoSync().windowHeight - 100
    },
    getCode() {
      // this.$uniPlugin.toast('敬请期待')
      // return
      if (!this.isAllowGetNum) return
      const isPhone = this.$validate.isMobile(this.regForm.phone)
      if (!isPhone.flag) {
        this.$uniPlugin.toast(isPhone.msg)
        return
      }
      const params = {
        phone: this.regForm.phone, // 用户填写的手机号
        uuid: this.$common.getTokenUuid(), // 随机编码
      }
      this.$uniPlugin.toast('操作中')
      this.$api.sys.sendSms(params, res => {
        this.resetTime(60)
        this.$uniPlugin.toast('发送成功')
      })
    },
    /**
     * 修改密码
     */
    onSubmit() {
      if (!this.isConfirm) {
        this.$uniPlugin.toast('请完善信息')
      } else {
        const params = {
          phone: this.regForm.phone, // 用户填写的手机号
          uuid: this.$common.getTokenUuid(), // 随机编码
          code: this.regForm.captcha, // 验证码
          // password: md5(this.regForm.pwd) // 密码
          password: (this.$rsa.jsEncryptCode(this.regForm.pwd)) // 密码
        }
        const isPhone = this.$validate.isMobile(this.regForm.phone)
        if (!isPhone.flag) {
          this.$uniPlugin.toast(isPhone.msg)
          return
        }
        if (this.regForm.captcha.length !== 4) {
          this.$uniPlugin.toast('请输入4位数的验证码')
          return
        }
        const pwdObj = this.$validate.checkPWD(this.regForm.pwd)
        if (!pwdObj.statu) {
          this.$uniPlugin.toast(pwdObj.text)
          return
        }
        // 验证密码
        if (!this.regForm.pwd || !this.regForm.pwd1 || this.regForm.pwd !== this.regForm.pwd1) {
          this.$uniPlugin.toast('两次输入密码不一致​')
          return
        }
        this.$uniPlugin.loading('正在提交', true)
        this.$api.user.changePwd(params).then((res) => {
          this.$uniPlugin.hideLoading()
          this.$uniPlugin.successToast('修改成功', true)
          const firstUrlParam = this.$common.getKeyVal('user', 'firstUrlParam', 'sessionStorage')
          this.$common.setKeyVal('user', 'isLogin', false)
          this.$common.setKeyVal('user', 'isCutOutUrl', true, 'sessionStorage')
          this.$common.setKeyVal('user', 'firstUrlParam', firstUrlParam, 'sessionStorage')
          setTimeout(() => {
            this.$uniPlugin.hideToast()
            this.$ext.user.loginOut()
            // this.$navto.back(1)
          }, this.$constant.noun.delayedOperationTime)
        }).catch(e => {

        })
      }
    },
    oauth(value) {
      uni.login({
        provider: value,
        success: res => {
          uni.getUserInfo({
            provider: value,
            success: infoRes => {
              /**
               * 实际开发中，获取用户信息后，需要将信息上报至服务端。
               * 服务端可以用 userInfo.openId 作为用户的唯一标识新增或绑定用户信息。
               */
              this.toMain(infoRes.userInfo.nickName)
            }
          })
        },
        fail: err => {
          // console.error('授权登录失败：' + JSON.stringify(err))
        }
      })
    },
    jumpTo(url) {
      uni.navigateTo({
        url: url
      })
    },
    back() {
      uni.navigateBack({
        delta: 2
      })
    },
    /**
     * 倒计时
     * @param time
     */
    resetTime(time) {
      this.isAllowGetNum = false
      let countdownMinute = time || 30 // 1分钟倒计时

      let startTimes = new Date() // 开始时间 new Date('2016-11-16 15:21');
      let endTimes = new Date(startTimes.setSeconds(startTimes.getSeconds() + countdownMinute)) // 结束时间
      let curTimes = new Date() // 当前时间
      let surplusTimes = endTimes.getTime() / 1000 - curTimes.getTime() / 1000 // 结束毫秒-开始毫秒=剩余倒计时间

      // 进入倒计时
      let countdowns = setInterval(() => {
        surplusTimes--
        // eslint-disable-next-line no-unused-vars
        let minu = '' + Math.floor(surplusTimes / 60)
        let secd = '' + Math.round(surplusTimes % 60)
        // console.log(minu+':'+secd);
        minu = minu.length === 1 ? '0' + minu : minu
        secd = secd.length === 1 ? '0' + secd : secd
        this.getVerificationTxt = secd + '秒后重试'
        if (surplusTimes <= 0) {
          // console.log('时间到！');

          this.getVerificationTxt = '获取验证码'
          clearInterval(countdowns)
          countdowns = true
          this.isAllowGetNum = true
        }
      }, 1000)
    }
  },
  onReady() {
  },
  mounted() {
    // 随机码
    this.regForm.code = this.$common.uuid()
    this.regForm.phone = this.codeUserInfo.phone || ''
    // #ifdef MP-WEIXIN
    this.handleClickTrack('OperationDetailsPageView')
    // #endif
  }
}
</script>

<style lang="scss">
.opacity-5{
  @include opacity(0.5);
}
.main{
  height: 100%;
  overflow: hidden;
  .main-body{
    background-color: #fff;
    .inp{
      padding-right: 30upx;
      margin-left: 30upx;
      height: 88upx;
      line-height: 88upx;
      border-bottom: 2upx solid $contentDdt;
      position: relative;
      .inp-l{
        color: #333;
        font-size: 30upx;
        width: 120upx;
        display: inline-block;
        vertical-align: middle;
      }
      .inp-r{
        height: 88upx;
        width: calc(100% - 120upx);
        display: inline-block;
        vertical-align: middle;
        box-sizing: border-box;
      }
      .verification-code{
        position: absolute;
        right: 30upx;
        top: 14upx;
        height: 60upx;
        text-align: center;
        color: #fff;
        font-size: 24upx;
        line-height: 60upx;
        width: 170upx;
        @include rounded(30upx);
        //background: linear-gradient(90deg,rgba(255,185,36,1),rgba(254,219,55,1));
        background: $topicC;
      }
    }
  }
  .main-bottom{
    padding: 0 30upx;
    margin-top: 50upx;
  }
}
.width-calc-310{
  width: calc(100% - 310upx) !important;
}
.m-t-20{
  margin-top: 20upx;
}
.b-btn {
  //background: linear-gradient(90deg,rgba(255,185,36,1),rgba(254,219,55,1));
  background: $topicC;
  color: #fff;
  @include rounded(44upx);
}
.border-bottom-none{
  border-bottom: none !important;
}
.f-w-medium{
  color: #bfbfbf;
  font-weight: Medium;
  font-size: 30upx;
}
.is-get-code {
  background-color: $topicC;
  color: #fff;
  @include opacity(0.5);
}
uni-button:after{
  border: none !important;
}
</style>
