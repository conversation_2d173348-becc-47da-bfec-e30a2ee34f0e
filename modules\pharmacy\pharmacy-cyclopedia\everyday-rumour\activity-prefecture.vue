<template>
  <view class='activity-prefecture'>
    <view class="my-data" :style="{'background-image':'url('+ file_ctx +'static/image/business/pharmacy-cyclopedia/rumour-activity-prefecture-bg.png)','background-size': '100%'}">
      <view :style="'height:' + statusBarHeight + 'px;'"></view>
      <view class="top-nav">
        <view class="top-nav-l" @click.stop="handleBack">
          <image mode="aspectFit" :src="file_ctx + 'static/image/business/hulu-v2/icon-circle-left-arrows.png'" class="header-search-img"/>
        </view>
        <view class="top-nav-c">活动专区</view>
      </view>
      <view class="activity-sign"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/activity-sign-title-bg.png'"></image></view>
    </view>
    <!-- 答题区域 -->
    <!-- <view class="rumour-box" :style="{'top':statusBarHeight + 148 + 'px'}"> -->
    <view class="rumour-box">
      <view class="rumour-list" v-if="loading">
        <view class="rumour-empty">加载中...</view>
      </view>
      <view v-else>
        <view class="rumour-list" v-if="rumourDetailObj.id">
          <view class="rumour-item">
            <view class="rumour-item-content">
              <view class="item-img" v-if="rumourDetailObj.answered == 1">
                <image v-if="rumourDetailObj.answer == rumourDetailObj.userAnswer || rumourDetailObj.answerState == 1" :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-rumour-correct.png'" mode="aspectFill"></image>
                <image v-else :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-rumour-error.png'" mode="aspectFill"></image>
              </view>
              <view class="question-title">选择题</view>
              <view class="item-title">{{rumourDetailObj.title}}</view>
              <view class="item-answer" v-if="rumourDetailObj.answered == 1">
                <view class="item-answer-title">正确答案：<span>{{rumourDetailObj.answerText}}</span></view>
                <view class="item-answer-content" :class="{lineclamp3:rumourDetailObj.isAll}">{{rumourDetailObj.answerContent}}</view> 
                <view v-if="rumourDetailObj.isMore">
                  <span v-if="rumourDetailObj.isAll" @click="handleClickType">展开</span>
                  <span v-else @click="handleClickType">收起</span>
                </view>
                <view class="rumour-item-date" v-if="userAnswerTime">{{ userAnswerTime }}</view>
              </view>
              <view class="item-bott" v-else>
                <view class="item-bott-l"><button class="real-btn-l" @click="handleLookAnswer(1,rumourDetailObj)">真的</button></view>
                <view class="item-bott-r"><button class="real-btn-r" @click="handleLookAnswer(0,rumourDetailObj)">假的</button></view>
              </view>
              <!-- 我的福币 -->
              <view class="lucky-coin">
                <!-- <view class="coin-l">我的福币：<span>{{ initGoldNum }}</span></view> -->
                <view class="coin-l" @click="handleLookMyCoins">查看我的福币</view>
                <!-- <view class="coin-r" @click="handleCoinExchange">福币兑换</view> -->
                <view class="coin-r" @click="handleMyLuckyCoin"><view class="text">福币记录</view><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-coin-record-right.png'"></image></view>
              </view>
              <!-- 福币签到区显示一周 -->
              <view class="coin-sign">
                <!-- <view :class="item.value == 1 ? 'sign-active sign-weeks' : 'sign-weeks'" v-for="(item, index) in weekDays" :key="index" @click="handleClickSign(index)"> -->
                <view :class="item.value == 1 ? 'sign-active sign-weeks' : 'sign-weeks'" v-for="item in weekDays" :key="item.label">
                  <view class="weeks-img"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-task-lucky-small-sgin.png'"></image></view>
                  <view class="weeks-num">{{ item.label }} 天</view>
                </view>
              </view>
              <!-- 福币兑换 -->
              <view class="coin-record" @click="handleCoinExchange">
                <!-- <view class="text">福币记录</view><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-coin-record-right.png'"></image> -->
                <image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-sign-lucky-coins-new-bg.png'"></image>
              </view>
            </view>
          </view>
        </view>
        <view class="rumour-empty" style="margin:0 32rpx;" v-else>
          <image :src="'http://file.greenboniot.cn/static/image/system/invalid/icon-no-data.png'" class="empty-img"></image>
          <view>今日暂无辟谣</view>
        </view>
      </view>
    </view>
    <!-- banner区域 -->
    <!-- <view class="banner-header" :style="{'top':statusBarHeight + myDataHeight + 148 + 'px'}"> -->
    <view class="banner-header">
      <banner-ads class="my-banner" ref="bannerAdsRef" :query-params="{useType: 11}" height="208rpx" />
    </view>
    <!-- 做任务 赚福币区域 -->
    <!-- <view class="task-lucky-coins" :style="{'top':statusBarHeight + myDataHeight + 276 + 'px'}"> -->
    <view class="task-lucky-coins">
      <image class="task-lucky-head" :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/task-item-lucky-head-bg.png'"></image>
      <view class="task-lucky-content">
        <view class="task-item" v-for="(item, index) in taskList" :key="index">
          <view class="task-item-l">
            <!-- <image class="item-box-img-new" :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/task-item-lucky-head-bg.png'"></image> -->
            <image class="item-box-img-new" :src="file_ctx + item.url"></image>
            <view class="item-l-box">
              <view class="title">{{ item.taskTitle }}</view>
              <view class="box-img">
                <image class="rumour-sign-img" :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-rumour-sign-everyday.png'"></image>
                <span>+{{item.point}}</span>
                {{item.taskNum > 1 ? '(' + item.finishNum + '/' + item.taskNum + ')' : ''}}
              </view>
            </view>
          </view>
          <view v-if="!item.finish" class="task-item-r" @click="gotoFinish(item)">去完成</view>
          <view v-else class="done task-item-r">已完成</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import { mapState } from 'vuex'
  import calabashApis from "@/modules/common/api/calabash.js"
  import cloudClassroomApis from "@/modules/common/api/cloudClassroom.js"
  import {EventTypeList} from "./components/mapOptions"
  import bannerAds from '@/components/basics/banner-ads/index.vue'
  export default {
    components: {
      bannerAds
    },  
    data(){
      return{
        file_ctx: this.file_ctx,
        statusBarHeight: 0,
        currentIndex: 0,
        rumourDetailList:[],
        rumourDetailObj:{},
        userAnswerTime:'',//用户答题时间
        signCurrentIndex:null, //签到下标
        myDataHeight:0,
        taskList:[],
        taskIconMap:[
          {icon:'task-browse.png',MappingIds:[17,1,3,7,9,11]},
          {icon:'task-watch.png',MappingIds:[12,13,18]},
          {icon:'task-sub.png',MappingIds:[5]},
          {icon:'task-finish.png',MappingIds:[2, 4, 8, 10, 14, 16, 22, 23, 24, 25,19,20,26]},
          {icon:'task-share.png',MappingIds:[6, 15, 21]}
        ],
        finishNum:0,
        totalNum:0,
        initGoldNum:0,
        weekDays: [
          { name: 'mondaySign', label: 1, value:0 },
          { name: 'tuesdaySign', label: 2, value:0 },
          { name: 'wednesdaySign', label: 3, value:0 },
          { name: 'thursdaySign', label: 4, value:0 },
          { name: 'fridaySign', label: 5, value:0 },
          { name: 'saturdaySign', label: 6, value:0 },
          { name: 'sundaySign', label: 7, value:0 },
        ],
        loading:true,
      }
    },
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId,
        fansRecord: state => state.fansRecord,
        isLogin: state => state.isLogin
      }),
    },
    onLoad(){
      // this.getRefuterumordetailOne() //获取辟谣题目coni
      this.getRefuterumorrecordQueryAnswerRecord() //获取辟谣签到答题记录
      this.getRefuterumorrecordSignRecord() //获取签到记录列表
      // this.pointuserQueryUser()
    },
    onShow() { 
      this.$refs.bannerAdsRef.init()
      this.getPointtaskQueryPointList()  
    },
    mounted(){
      // 计算内容区域高度
      const sysInfo = uni.getSystemInfoSync()
      this.statusBarHeight = sysInfo.statusBarHeight // 获取设备的状态栏高度
    },
    methods:{
      handleLookMyCoins(){
        this.$navto.push('calabashWebview')
      },
      mapSignData(weekDays, apiData) {
        return weekDays.map(day => {
          // 从apiData中获取对应的值，如果不存在则默认为0
          const signValue = apiData[day.name] !== undefined ? apiData[day.name] : 0
          // 返回更新后的对象（保留原有属性，更新value）
          return { ...day, value: signValue }
        });
      },

      // 获取辟谣签到答题记录
      async getRefuterumorrecordQueryAnswerRecord(){
        this.loading = true
        const res = await this.$api.drugBook.getRefuterumorrecordQueryAnswerRecord({accountId:this.accountId,businessType:2}) //businessType：1-每日辟谣 2-辟谣签到
        if(res.data !== ""){
          this.rumourDetailObj = {
            ...res.data,
            answered:1, //answered = 1答题过了
            isMore:res.data.answerContent?.length > 80 ?  true : false,
            isAll:res.data.answerContent?.length > 80 ?  true : false,
            answerDateText:res.data.answerDate?this.$common.formatDate(new Date(res.data.answerDate),'yyyy-MM-dd').replace(/-/g, '/'):'',
            answerText:res.data.answer == 0 ? '假的' : '真的',
            userAnswer:null
          }
          this.loading = false
          this.$nextTick(()=>{
            this.getElementHeight()
          })
        } else {
          this.getRefuterumordetailOne() //今天没答题过，获取题目
        }
      },

      // 获取签到记录列表
      async getRefuterumorrecordSignRecord(){
        const res = await this.$api.drugBook.getRefuterumorrecordSignRecord({accountId:this.accountId})
        if(res.data !==""){
          this.weekDays = this.mapSignData(this.weekDays,res.data)
        }
      },

      handleClickType(){
        this.rumourDetailObj.isAll = !this.rumourDetailObj.isAll
        this.$nextTick(()=>{
          this.getElementHeight()
        })
      },
      handleMyLuckyCoin(){
        this.$navto.push('myLuckyCoin')
      },
      // 福币兑换
      handleCoinExchange(){
        this.$navto.push('integrationShop')
      },
      // async pointuserQueryUser(){
      //   let {data:{totalPoint}} = await calabashApis.pointuserQueryUser({accountId:this.accountId})
      //   this.initGoldNum = totalPoint
      // },
      gotoFinish({businessType,eventType,accountId,routhPath,name}){
        // 所有路径都要执行的路由选项解析和状态更新
        const pathWithoutParams = routhPath?.split('?')[0]
        const routerOptions = this.parsedHashRouterOptions(routhPath)
        if (routerOptions.backTaskFlag) {
          this.$store.dispatch('system/UpdateBackTaskFlag', true)
        }

        if (routhPath.startsWith('pages')) {
          // 跳转到 tabBar 页面
          uni.switchTab({ url: routhPath })
        } else {
          // 使用自定义方法跳转非 tabBar 页面
          // this.$navto.replacePath(pathWithoutParams)
          console.log(name,'name112')
          this.$navto.push(name)
        }
      },

      // 解析hash模式路由参数
      parsedHashRouterOptions(router){
        let routerOptions = router.split('?')[1]?.split('&')?.reduce((res,index)=>{
          let map = index.split('=');
          res[map[0]] = map[1];
          return res
        },{})
        return routerOptions || {}
      },

      // 获取任务列表
      async getPointtaskQueryPointList(){
        let {data} = await calabashApis.pointtaskQueryPointList({accountId:this.accountId})
        data.taskList && data.taskList.map(async (e,index)=>{
          // 判定是否指定帖子作者
          if(e.articleAccount){
            let queryOptions = {size: 1,current: 1,condition: {exitAccountId: e.articleAccount}}
            let {data:{records}} = await calabashApis.fansrecordQueryPage(queryOptions);
            let currentAuthor = records.filter(record=>record.accountId == e.articleAccount)[0]
            if(currentAuthor)e.username = currentAuthor.nickName
          }
          // 判断是否指定栏目
          if(e.videoColumn){
            console.log('videoColumn',e.videoColumn,index)
            let {data} = await cloudClassroomApis.getMeetingclassifyQueryList()
            console.log('getMeetingclassify',data);
            let currentClass = data.filter(cla=>cla.id == e.videoColumn)[0]
            console.log('currentClass',currentClass);
            e.videoColumnName = currentClass.name
            console.log('eeee',e);
          }
          this.taskIconMap.map(taskIcon=>{
            if(taskIcon.MappingIds.indexOf(e.eventType)>=0){
              e.icon = taskIcon.icon
            }
          })
          let {desc:event,path,url,name} = EventTypeList.filter(event=>event.id === e.eventType)[0]
          // articleSetup 帖子是否设置浏览/观看时间：1-是 0-否
          e.taskTitle = `${event}${e.videoColumnName ? `(${e.videoColumnName})` : ``}`
          e.routhPath = path
          e.url = url
          e.name = name
          if(e.videoSetup || e.articleSetup){
            let times = e.videoSetup && e.videoTime || e.articleSetup && e.articleTime;
            e.taskTitle+=(times + '秒')
          }

          this.$set(this.taskList,index,e)
        })
        this.finishNum = data.finishNum || 0
        this.totalNum = data.totalNum || 0
      },

      // 获取每日辟谣题目
      async getRefuterumordetailOne(){
        let that = this
        let params = {
          accountId:that.accountId,
        }
        const res = await that.$api.drugBook.getRefuterumordetailOne(params)
        if(res.data !==""){
          that.rumourDetailObj = {
            ...res.data,
            isMore:res.data.answerContent.length > 80 ?  true : false,
            isAll:res.data.answerContent.length > 80 ?  true : false,
            answerDateText:res.data.answerDate?that.$common.formatDate(new Date(res.data.answerDate),'yyyy-MM-dd').replace(/-/g, '/'):'',
            answerText:res.data.answer == 0 ? '假的' : '真的',
            userAnswer:null
          }
          this.loading = false
          that.$nextTick(()=>{
            that.getElementHeight()
          })
        }
      },

      // 点击答题
      async handleLookAnswer(type,item){
        let that = this
        let typeText = item.answer == type ? '恭喜你，答对了' : '很遗憾，答错了'
        item.userAnswer = type
        that.$uniPlugin.modal('提示', typeText, {
          showCancel: false, // 是否显示取消按钮，默认为 true
          cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
          cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
          confirmText: '我知道了', //  确定按钮的文字，默认为"确定"，最多 4 个字符
          confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
          fn: (n) => {
            if (n) {
              let params = {
                accountId:that.accountId, //账户id
                username:that.fansRecord.nickName, //用户昵称
                userType:1, //用户类型 1-活跃用户2-用户(马甲)
                refuteRumorDetailId:item.id, //辟谣详情主键id
                recordType:1, //操作类型 1-答题2-分享3-订阅
                answer:type, //答题 1真的 0假的
                answerTime:'', //答题时间，秒
                businessType:2, // 业务类型：1-每日辟谣 2-辟谣签到
              }
              // const res = await that.$api.drugBook.refuterumorrecordInsert(params)
              that.$api.drugBook.refuterumorrecordInsert(params,{'recordType':'1'}).then(res=>{
                if(res.data !== ""){
                  item.answered = 1
                  that.userAnswerTime = res.data.createTime ? that.$common.formatDate(new Date(res.data.createTime),'yyyy-MM-dd').replace(/-/g, '/'):''
                  that.getRefuterumorrecordSignRecord() // 重新获取签到记录
                  // that.pointuserQueryUser() //重新获取积分
                  that.getPointtaskQueryPointList() //重新获取任务列表
                }
                that.$nextTick(()=>{
                  that.getElementHeight()
                })
              })
            }
          }
        })

      },

      handleClickSign(index){
        this.signCurrentIndex = index
      },

      getElementHeight() {
        let query = uni.createSelectorQuery().in(this);
        query.select('.rumour-box').boundingClientRect(data => {
          if (data) {
            this.myDataHeight = data.height
          }
        }).exec();
      },
      handleBack(){
        this.$navto.back(1)
      },
    },
 }
</script>

<style lang='scss' scoped>
@mixin contentFlex {
  display: flex;
  align-items: center;
  justify-content: center;
}
.activity-prefecture{
  display: flex;
  flex-direction: column;
  background-color: #FAF1D7;
  padding-bottom: 80rpx;
  .my-data{
    // height: 662rpx;
    height: 362rpx;
    width: 100%;
    background-repeat: no-repeat;
  }
  .top-nav{
    position: relative;
    @include contentFlex;
    width: calc(100% - 56rpx);
    height: 40px;
    line-height: 40px;
    padding: 0 32rpx 0 24rpx;
    .top-nav-l{
      @include contentFlex;
      position: absolute;
      height: 40px;
      left: 24rpx;
      top: 0;
      .header-search-img{
        display: flex;
        width: 48rpx;
        height: 48rpx;
        margin-right: 24rpx;
      }
    }
    .top-nav-c{
      @include contentFlex;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      top: 0;
      height: 40px;
      font-weight: 500;
      font-size: 32rpx;
      color: #1D2029;
      margin-right: 48rpx;
    }
  }
  .activity-sign{
    display: flex;
    width: 384rpx;
    height: 106rpx;
    margin: 20rpx 0 0 62rpx;
    image{
      width: 100%;
      height: 100%;
    }
  }
  .rumour-box{
    // position: absolute;
    // top:194px;
    // left: 0;
    width: 100%;
    .rumour-list{
      position: relative;
      width: calc(100% - 112rpx);
      margin: 0 32rpx;
      background: #FFFFFF;
      border-radius: 32rpx;
      padding: 32rpx 24rpx;
      .rumour-item{
        // position: relative;
        .rumour-item-date{
          color:#000;
        }
        .rumour-item-content{
          // position: relative;
          border-radius: 16rpx;
          background-color: #fff;
          .item-img{
            position: absolute;
            right: -26rpx;
            top: -42rpx;
            display: flex;
            width: 174rpx;
            height: 136rpx;
            image{
              width: 100%;
              height: 100%;
            }
          }
          .question-title{
            display: flex;
            align-items: center;
            justify-content: center;
            width: 216rpx;
            height: 68rpx;
            background: linear-gradient( 125deg, #FF8646 0%, #FF722B 100%);
            border-radius: 52rpx 52rpx 52rpx 52rpx;
            font-size: 32rpx;
            color: #FFFFFF;
            margin:0 auto;
          }
          .item-title{
            @include contentFlex;
            font-size: 36rpx;
            font-weight: 600;
            color:#000;
            margin: 24rpx 0;
            line-height: 50rpx;
          }
          .item-answer{
            .item-answer-title{
              display: flex;
              justify-content: center;
              font-weight: 600;
              font-size: 30rpx;
              color: #4E5569;
              margin-bottom: 28rpx;
              span{
                font-size: 30rpx;
                color: #00B484;
              }
            }
            .item-answer-content{
              line-height: 50rpx;
              word-wrap: break-word;
              max-height: 240rpx;
              overflow-y:auto;
            }
            .rumour-item-date{
              color:#999;
              margin: 24rpx 0 30rpx;
            }
            .lineclamp3{
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 3;
              -webkit-box-orient: vertical;
              white-space: normal;
            }
          }
          .item-bott{
            .item-bott-l,.item-bott-r{
              button{
                @include contentFlex;
                height: 88rpx;
                background: #F4F6FA;
                border-radius: 44rpx;
                font-size: 32rpx;
                color: #777777;
                margin: 0 46rpx 0 48rpx;
                &::after{
                  border: none !important;
                }
              }
            }
            .item-bott-r{
              margin: 24rpx 0 36rpx;
            }
          }
          .lucky-coin{
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 20rpx;
            .coin-l{
              @include contentFlex;
              width: 184rpx;
              height: 52rpx;
              background: #00B484;
              border-radius: 40rpx 40rpx 40rpx 40rpx;
              font-size: 24rpx;
              color: #FFFFFF;
              span{
                font-size: 32rpx;
                color:#00B484;
              }
            }
            .coin-r{
              display: flex;
              align-items: center;
              .text{
                font-size: 28rpx;
                color: #777777;
              }
              image{
                display: flex;
                width: 32rpx;
                height: 32rpx;
              }
            }
          }
          .coin-sign{
            display: flex;
            // margin: 20rpx 0;
            padding: 20rpx 0 60rpx;
            .sign-weeks{
              display: flex;
              flex-direction: column;
              padding: 18rpx 16rpx 16rpx;
              border-radius: 96rpx 96rpx 96rpx 96rpx;
              margin-right: 14rpx;
              background: #F4F6FA;
              color:#777777;
              .weeks-img{
                display: flex;
                width: 48rpx;
                height: 48rpx;
                margin-bottom: 8rpx;
                image{
                  width: 100%;
                  height: 100%;
                }
              }
              .weeks-num{
                font-size: 22rpx;
              }
              &:last-child{
                margin-right: 0;
              }
            }
            .sign-active{
              background: linear-gradient( 180deg, #F4C284 0%, #FDA12F 100%);
              color: #FFFFFF;
            }
          }
          .coin-record{
            position: absolute;
            right: 0;
            bottom: 0;
            display: flex;
            width: 160rpx;
            height: 52rpx;
            image{
              width: 100%;
              height: 100%;
            }
            // align-items: center;
            // .text{
            //   font-size: 28rpx;
            //   color: #777777;
            // }
            // image{
            //   display: flex;
            //   width: 32rpx;
            //   height: 32rpx;
            // }
          }
        }
      }
    }
    .rumour-empty{
      @include contentFlex;
      flex-direction: column;
      height: 500rpx;
      font-size: 30rpx;
      background-color: #fff;
      border-radius: 16rpx;
      image{
        height: 200rpx;
        width: 300rpx;
        margin-bottom: 20rpx;
      }
    }
  }
  .banner-header{
    // position: absolute;
    // margin: 24rpx 0;
    margin: 24rpx auto;
    // left: 32rpx;
    /deep/.my-banner{
      .banner-main{
        margin-bottom: 0;
        width: calc(100vw - 64rpx);
      }
    }
  }
  .task-lucky-coins{
    // position: absolute;
    width: calc(100% - 74rpx);
    background: #FFFFFF;
    // left: 32rpx;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    padding: 5rpx;
    margin: 0 auto;
    .task-lucky-head{
      height: 80rpx;
      width: 676rpx;
    }
    .task-lucky-content{
      margin-top: 16rpx;
      .task-item{
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12rpx 24rpx;
        margin-bottom: 16rpx;
        .task-item-l{
          display: flex;
          align-items: center;
          .item-box-img-new{
            display: flex;
            width: 52rpx;
            height: 52rpx;
          }
          .item-l-box{
            margin-left: 20rpx;
            .title{
              font-size: 28rpx;
              color: #333333;
            }
            .box-img{
              display: flex;
              align-items: center;
              font-size: 24rpx;
              color: #777777;
              .rumour-sign-img{
                display: flex;
                width: 32rpx;
                height: 32rpx;
              }
            }
          }
        }
        .task-item-r{
          @include contentFlex;
          width: 124rpx;
          height: 52rpx;
          background: #00B484;
          border-radius: 66rpx 66rpx 66rpx 66rpx;
          font-size: 24rpx;
          color: #FFFFFF;
        }
        .done{
          background: #C9CCD4;
        }
        &:last-child{
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>