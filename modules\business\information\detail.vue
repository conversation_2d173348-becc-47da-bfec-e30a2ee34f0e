<template>
  <page>
    <scroll-view :scroll-y="true" slot="content" class="advice">
      <view v-if="regForm.type!==4" class="li">
        <view class="main-content">
          <view class="title">
            <text class="name">{{regForm.title}}</text>
          </view>
          <view class="secenter">
            <view class="l">
              来源：{{ regForm.author }}
            </view>
            <view class="l">
              {{ regForm.creationTimeText }}
            </view>
          </view>

          <view v-if="regForm.type === 3" class="video-content">

            <!-- #ifdef H5 -->
            <view :key="index" v-for="(item,index) in regForm.attachmentList">
              <iframe
                v-if="item.suffix === 'pdf'"
                frameborder="0"
                :src="`${file_ctx}${item.dir}`"
                width="100%"
                height="400upx"
              />
              <video v-else-if="item.suffix === 'mp4'" :src="`${file_ctx}${item.dir}`"></video>
              <image v-else-if="['png','jpg','jpeg'].includes(item.suffix)" :src="`${file_ctx}${item.dir}`" mode="aspectFill"></image>
              <iframe
                v-else
                frameborder="0"
                :src="`https://view.officeapps.live.com/op/view.aspx?src=${file_ctx}${item.dir}`"
                width="100%"
                height="400upx"
              />
            </view>
            <!-- #endif -->

            <!-- #ifndef H5 -->
            <view :key="index" v-for="(item,index) in regForm.attachmentList" class="attachment">
              <text>{{ `${item.name}.${item.suffix}` }}</text>
              <view class="btn" @click="handleOpenDoc(item)">
                点击预览
              </view>
            </view>
            <!-- #endif -->

            <view class="introduction">
              <view class="head">
                <view class="l">
                </view>
                <view class="r">
                  简介
                </view>
              </view>
              <view class="content">
                  {{ regForm.introduction }}
              </view>
            </view>
          </view>

          <view v-else-if="regForm.type === 2" class="video-content">
            <video  id="myVideo" :poster="file_ctx + regForm.coverPath" :src="file_ctx + regForm.videoPath" @error="videoErrorCallback" controls></video>
            <view class="introduction">
              <view class="head">
                <view class="l">
                </view>
                <view class="r">
                  简介
                </view>
              </view>
              <view class="content">
                  {{ regForm.introduction }}
              </view>
            </view>
          </view>

          <view  v-else-if="regForm.type === 1">
            <!--<u-parse :content="regForm.content" @preview="preview" @navigate="navigate" />-->

            <!--<mp-html :content="regForm.content" />-->
            <rich-text v-if="regForm.content" :nodes="regForm.content"></rich-text>

            <!--<rich-text v-if="regForm.content" :nodes="regForm.content"></rich-text>-->
            <!--<view class="article" v-else>-->
            <!--  <text>{{regForm.content}}</text>-->
            <!--</view>-->
          </view>
        </view>
      </view>
      <view v-else>
        <web-view :src="regForm.linkAddress"></web-view>
      </view>
    </scroll-view>
  </page>
</template>

<script>
import parse from 'mini-html-parser2'
import mpHtml from './components/mp-html/mp-html'
import { mapState } from 'vuex'
export default {
  name: 'NoticeAdviceDetail',
  components: {
    mpHtml
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $timePlugin: this.$timePlugin,
      regForm: {},
      id: undefined,
      index: undefined,
      config: {
        avatar: {
          widthHeightAuto: true
        }
      }
    }
  },
  computed: {
    ...mapState('user', {
      isLogin: state => state.isLogin,
      curSelectUserInfo: state => state.curSelectUserInfo // 当前登录用户信息
    }),
  },
  onShareAppMessage(res) {
    if (res.from === 'button') {// 来自页面内分享按钮
      console.log(res.target)
    }
    return {
      title: this.regForm.title, //分享的名称
      path: 'modules/business/information/detail?query='+ encodeURIComponent(JSON.stringify({
        id: this.regForm.id
      })),
      mpId:this.$appId, //此处配置微信小程序的AppId
      imageUrl: this.file_ctx + this.regForm.coverPath
    }
  },
//分享到朋友圈
  onShareTimeline(res) {
    return {
      title: this.regForm.title,
      path: 'modules/business/information/detail?query='+ encodeURIComponent(JSON.stringify({
        id: this.regForm.id
      })),
      imageUrl: this.file_ctx + this.regForm.coverPath
    }
  },
  onLoad(paramsObj) {
    const query = this.$Route.query
    if (!this.$validate.isNull(query)) {
      this.id = query.id
      this.index = query.index
    }
    this.id && this.getDetail(this.id)
    // #ifdef MP-WEIXIN
    wx.showShareMenu({
      withShareTicket:true,
      //设置下方的Menus菜单，才能够让发送给朋友与分享到朋友圈两个按钮可以点击
      menus:["shareAppMessage","shareTimeline"]
    })
    // #endif
  },
  methods: {
    videoErrorCallback: function (e) {
      console.log('视频错误信息:')
      console.log(e.target.errMsg)
    },
    handleOpenDoc (item) {
      uni.downloadFile({
        url: `${this.file_ctx}${item.dir}`,
        success: function (res) {
          let filePath = res.tempFilePath;
          uni.openDocument({
            filePath: filePath,
            fileType: item.suffix,
            showMenu: true,
            success: function (res) {
              console.log('打开文档成功');
            }
          });
        }
      });
      // uni.openDocument({
      //   filePath: `${this.file_ctx}${item.dir}`,
      //   fileType: item.suffix,
      //   showMenu: true
      // })
    },
    getDetail(id) {
      const that = this
      this.$uniPlugin.loading('加载中...', true)
      that.$api.information.informationscienceQueryOne({ id: id }).then(res => {
        res.data.creationTimeText = that.$common.formatDate(new Date(res.data.creationTime), 'yyyy-MM-dd')
        that.regForm = res.data

        // #ifdef MP-ALIPAY

        let nodesList = []
        parse(that.regForm.content, (err, nodesList) => {
          if(!err){
            that.regForm.content = nodesList
          }
        })
        // #endif

        // userType 用户类型 1-游客 2-注册用户
        let params = {
          id: id,
          numberType:1,
        }

        if(that.isLogin) {
          const {userId='', username=''} = that.curSelectUserInfo || {}
          params = {
            ...params,
            userAccount: username,
            accountId: this.$common.getKeyVal('user', 'accountId', true),
            userId: userId,
            userType: 2,
          }
        } else {
          params = {
            ...params,
            userType: 1,
            accountId: this.$common.getKeyVal('user', 'accountId', true),
          }
        }
        this.$uniPlugin.hideLoading()
        that.$api.information.informationscienceNumberUpdate(params).then(res => {

        })
      }).catch(err=>{
        this.$uniPlugin.hideLoading()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
img{
  width: 100%;
}
.advice{
  width: 100%;
  height: 100%;
  overflow-y: auto;
  background: #f7f7f7;
  .li{
    background: #fff;
    width: 100%;
    overflow: hidden;
    .main-content{
      width: 100%;
      padding: 30upx;
      box-sizing: border-box;
      overflow: hidden;
      .title{
        width: 100%;
        .name{
          font-size: 40upx;
          display: block;
          margin:0 14upx 10upx 0;
          font-weight: 600;
          color: #121822;
        }

      }
      .secenter{
        padding: 30upx 0 30upx 0;
        position: relative;
        display: -webkit-box;
        display: -moz-box;
        display: -ms-flexbox;
        display: flex;
        display: -webkit-flex;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        -webkit-justify-content: flex-start;
        justify-content: flex-start;
        -webkit-box-align: center;
        -ms-flex-align: center;
        -webkit-align-items: center;
        align-items: center;
        .l {
          font-size: 25upx;
          font-weight: 400;
          color: #A5AEBB;
          line-height: 20upx;
          margin-right: 40upx;
        }
      }
      .video-content{
        width: 100%;
        video{
          width: 100%;
        }
        .introduction{
          padding-top: 10upx;
          position: relative;
          .head {
            display: -webkit-box;
            display: -moz-box;
            display: -ms-flexbox;
            display: flex;
            display: -webkit-flex;
            -webkit-box-pack: center;
            -ms-flex-pack: center;
            -webkit-justify-content: flex-start;
            justify-content: flex-start;
            -webkit-box-align: center;
            -ms-flex-align: center;
            -webkit-align-items: center;
            align-items: center;
            margin-bottom: 12upx;
            .l{
              width: 10upx;
              height: 32upx;
              background: #00D29D;
              border-radius: 1px;
            }
            .r{
              margin-left: 10upx;
            }
          }
          .content{
            color: #726e6e;
          }
        }
      }
      .article{
        line-height: 1.5;
        font-size: 28upx;
        color: #666666;
        padding-bottom: 20upx;
      }
    }
  }
}

.attachment {
  display: flex;
  align-items: center;
}
.btn {
  display: inline-block;
  width: 120upx;
  height: 44upx;
  line-height: 44upx;
  font-size: 22upx;
  text-align: center;
  border: 2upx solid $topicC;
  color: #fff;
  background: $topicC;
  @include rounded(32upx);
  margin-left: 12upx;
  &:active {
    opacity: .7;
  }
}
</style>
