<template>
  <view class="main">
    <view style="flex: 1" class="main-content">
      <scroll-refresh
        :fixed="false"
        :up="upOption"
        :down="downOption"
        :no-page="true"
        :zPageDefault="{
          loadingMoreEnabled: false,
        }"
        :isShowEmptySwitch="false"
        @returnFn="returnFn"
        @scrollInit="scrollInit"
      >
        <view class="main-content-box">
          <view
            class="item"
            v-for="item in list"
            :key="item.id"
            hover-class="message-hover-class"
            @click="navtoGo('Circle', { cid: item.id })"
          >
            <view class="avatar-box">
              <image
                class="avatar"
                mode="aspectFill"
                :src="item.logoPath || defaultAvatar"
              ></image>
            </view>
            <view class="user-box-content">
              <text class="user-name">{{ item.name }}</text>
            </view>
            <button
              type="primary"
              size="mini"
              class="btn active"
              @click.stop="subscribe(item)"
            >
              关注
            </button>
          </view>
          <text class="tips-text" @click="openPopup">未找到想要关注的？</text>
        </view>
        <view class="empty-box" slot="empty">
          <image
            class="empty-img"
            mode="aspectFill"
            :src="file_ctx + 'static/image/business/hulu-v2/icon-circle-empty.png'"
          ></image>
          <text class="empty-text">暂无更多圈子</text>
        </view>
      </scroll-refresh>
    </view>
    <uni-popup ref="inputDialog" type="dialog">
      <uni-popup-dialog
        ref="inputClose"
        mode="input"
        title="未找到想要关注的？"
        placeholder="请输入希望开通的圈子"
        @confirm="dialogInputConfirm"
        v-model="regForm.name"
      ></uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import uniPopup from '@/components/uni/uni-popup/uni-popup.vue'
import uniPopupDialog from '@/components/uni/uni-popup-dialog/uni-popup-dialog.vue'
export default {
  components: {
    uniPopup,
    uniPopupDialog
  },
  props: {
    params: {
      type: Object,
      default: () => {
        return null
      }
    }
  },
  options: { styleIsolation: 'shared' },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      $validate: this.$validate,
      mescroll: null, // mescroll实例对象
      downOption: {
        auto: false // 不自动加载
      },
      upOption: {
        auto: false // 不自动加载
      },
      isInit: false, // 列表是否已经初始化
      list: [],
      defaultAvatar: this.file_ctx + 'static/image/system/avatar/icon-default-v2.png',
      regForm: {
        name: ''
      }
    }
  },
  computed: {
    ...mapState('user', {
      accountId: state => state.accountId
    })
  },
  methods: {
    // 提交登记
    dialogInputConfirm() {
      const param = {
        businessType: 4, // 业务类型:1-横幅，2-外部服务应用，3-企业微信群，4-圈子，5-帖子，6-帖子评论
        name: this.regForm.name,
        accountId: this.accountId,
        desc: this.regForm.name
      }
      this.$api.community.matterregisterInsert(param).then(res => {
        this.$uniPlugin.toast(res.msg)
        this.regForm.name = ''
      })
    },
    openPopup() {
      this.$refs.inputDialog.open()
      // #ifdef MP-WEIXIN
      getApp().globalData.sensors.track("PopupClick",
        {
          'page_name' : '关注圈子',
          'popup_id' : 'inputDialog',
          'popup_name' : '圈子弹窗',
          'click_type' : '进入弹窗',
        }
      ) 
      // #endif
    },
    // 关注圈子
    subscribe(e) {
      const param = {
        accountId: this.accountId,
        circleClassifyIds: [e.id]
      }
      this.$uniPlugin.loading('正在提交', true)
      this.$api.circleclassify.circleclassifySubscribe(param).then(res => {
        this.$uniPlugin.hideLoading()
        this.$uniPlugin.toast('关注成功')
        this.init()
      }).catch(() => {
        this.$uniPlugin.hideLoading()
      })
    },
    init(val) {
      this.mescroll.triggerDownScroll()
    },
    scrollInit(scroll) {
      scroll.optUp.page.num = 1
      scroll.optUp.page.size = 10
      this.mescroll = scroll
    },
    returnFn(obj) {
      const that = this
      setTimeout(function () {
        const param = {
          accountId: that.accountId
        }
        that.$api.circleclassify.circleclassifyQueryUnsubscribeList(param).then(res => {
          const data = res.data || []
          that.list = data.map(item => {
            return {
              ...item,
              logoPath: that.$common.getHeadImage(item.logoPath)
            }
          })
          obj.successCallback && obj.successCallback(data || [])
        })
      }, that.$constant.noun.scrollRefreshTime)
    },
    navtoGo(url, obj = {}) {
      this.$navto.push(url, obj)
    }
  }
}
</script>

<style lang="scss" scoped>
@import './list.scss';
</style>