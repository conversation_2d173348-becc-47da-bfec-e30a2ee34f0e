<template>
  <view class='pharmacy-manage'>
    <!-- 进行中 -->
    <template v-if="conductList.length">
      <view class="wrapper" v-for="(item,index) in conductList" :key="item">
        <view class="title" v-if="index == 0">进行中</view>
        <view class="conduct-drug" @click="handleItemDetail(item)">
          <view class="product-name">{{ item.productName}}</view>
          <view class="drug-item">
            <view class="name">{{labelFlag ? '服用频次' : '用药频次'}}</view>
            <view>每日 {{ item.times }} 次</view>
          </view>
          <view class="drug-item">
            <view class="name">{{labelFlag ? '服用时间' : '用药时间'}}</view>
            <view>{{ item.remindTime.replaceAll('-','，') }}</view>
          </view>
          <view class="drug-item">
            <view class="name">单次用量</view>
            <view>{{ item.numOfTake }}</view>
          </view>
          <view class="drug-item">
            <view class="name">提醒天数</view>
            <view>{{ item.medicationDays }} 天</view>
          </view>
          <view class="drug-item">
            <view class="name">备注</view>
            <view>{{ item.remark || '暂无' }}</view>
          </view>
        </view>
      </view>
    </template>
    <!-- 已关闭 -->
    <template v-if="closeList.length">
      <view class="wrapper" v-for="(item,index) in closeList" :key="item">
        <view class="title" v-if="index == 0">已关闭</view>
        <view class="add-drug" @click="handleItemDetail(item)">
          <view class="input-container">
            <view class="label">{{ item.productName }}</view>
            <view class="sky">
              <view class="text">每日 {{ item.times }} 次</view>
              <uni-icons style="margin-left: 10rpx;" :size="14" color="#999" type="right" />
            </view>
          </view>
        </view>
      </view>
    </template>
  </view>
</template>

<script>
  import { mapState } from 'vuex'
  import UniIcons from '@/components/uni/uni-icons/uni-icons'
  export default {
    components: {
      UniIcons,
    },
    props:{
      margeUpdateCount:{
        type:Number,
        default:0
      },
      paramsObj:{
        type:Object,
      },
    },
    data(){
      return{
        conductList:[], //进行中的数据
        closeList:[], //已关闭的数据
        labelFlag:false,
      }
    },
    onLoad(){

    },
    computed:{
      ...mapState('user', {
        curSelectUserInfo: state => state.curSelectUserInfo
      }),
    },
    watch: {
      margeUpdateCount(){
        this.getPharmacyRemindQueryDistinctPage()
      },
    },
    mounted(){
      // if(this.paramsObj.productId == '2031353194431111174' || this.paramsObj.productId == '2031448999104024578'){
      if(this.paramsObj?.drugType == 2){        
        this.labelFlag = true
      }
      this.getPharmacyRemindQueryDistinctPage()
    },
    methods:{
      handleItemDetail(obj,type){
        this.$navto.push('PharmacyDetail',{...obj,productId:this.paramsObj.productId,drugType:this.paramsObj.drugType})
      },

      async getPharmacyRemindQueryDistinctPage(){
        const { centerUserId = '' } = this.curSelectUserInfo || {}
        let params = {
          current: 1,
          size: 90,
          condition:{
            userId:centerUserId
          },
        }
        let res = await this.$api.drugBook.getPharmacyRemindQueryDistinctPage(params)
        let data = res.data.records.map(item=>({...item,beginTimeText:this.formatDate(item.beginTime)}))
        // 过滤进行中的数据
        this.conductList = data.filter(item=>item.executeStatus == 2)
        // 过滤已关闭的数据
        this.closeList = data.filter(item=>item.executeStatus == 4)

      },
      // 时间戳转换为日期格式
      formatDate(timestamp) {  
        // 创建一个Date对象  
        let date = new Date(timestamp); // 注意：时间戳通常是毫秒为单位的    
        // 获取月份（注意：月份是从0开始的，所以需要+1）  
        let month = date.getMonth() + 1;         
        // 获取日期  
        let day = date.getDate();         
        // 返回格式化的字符串  
        return month + '月' + day + '日';  
      },
    },
 }
</script>

<style lang='scss' scoped>
.pharmacy-manage{
  // height: 100%;
  // .m-main-body{
  //   height: 100%;
  //   position: relative;
  //   .scroll-refresh-main{
  //     height: 100%;
  //     /deep/ .mescroll-empty-box{
  //       min-height: 0%;
  //     }
  //   }
  // }
  .wrapper{
    margin: 10rpx 30rpx 30rpx;
    .title{
      font-weight: 600;
      color:#999;
      margin-bottom: 20rpx;
      padding-top: 20rpx;
    }
    .add-drug{
      padding: 30rpx 0 30rpx 30rpx;  
      background-color: #fff;  
      border-radius: 20rpx;
      margin-top: 20rpx;
      .input-container {
        display: flex;
        align-items: center;
        .sky{
          display: flex;
          padding-right: 30rpx;
          margin-left: auto;
          span{
            margin-left: 8rpx;
          }
        }
      }
    }
    .conduct-drug{
      padding: 50rpx 40rpx;  
      background-color: #fff;  
      border-radius: 20rpx;
      .product-name{
        font-size: 40rpx;
        font-weight: bold;
      }
      .close{
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #ccc;
        padding: 4rpx 10rpx;
        width: 100rpx;
        border-radius: 13rpx;
        margin: 20rpx 0 40rpx;
        font-weight: 600;
        color:#999;
      }
      .drug-item{
        display: flex;
        margin-top: 20rpx;
        .name{
          width: 120rpx;
          margin-right: 30rpx;
        }
      }
    }
  }
}
</style>