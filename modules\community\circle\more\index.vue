<template>
  <page>
    <view slot="content" class="content">
      <status-bar-height />
      <uni-nav-bar left-icon="left" backgroundColor="rgba(0,0,0,0)" :border="false" @clickLeft="back">
        <!-- tab菜单-->
        <tabs-sticky
          style="width: 100%; height: 100%;"
          v-model="curIndex"
          :tabs="tabs"
          @change="changeTab"
        ></tabs-sticky>
      </uni-nav-bar>
      <view class="main">
        <swiper class="swiper" :current="curIndex" @change="swiperChange">
          <swiper-item>
            <index-list ref="indexListRef" :index="curIndex" :params="{}" />
          </swiper-item>
          <swiper-item>
            <more-list ref="moreListRef" :index="curIndex" :params="{}" />
          </swiper-item>
        </swiper>
      </view>
    </view>
  </page>
</template>

<script>
import TabsSticky from './components/tabs-sticky.vue'
import IndexList from './components/list.vue'
import MoreList from './components/more-list.vue'
import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'
export default {
  components: {
    TabsSticky,
    IndexList,
    MoreList,
    uniNavBar
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      $validate: this.$validate,
      curIndex: 0, // 当前菜单下标,
      tabs: [{ name: '我关注的', value: 0 }, { name: '更多圈子', value: 1 }]
    }
  },
  onLoad() {
    const query = this.$Route.query
    this.init()
  },
  onShow() {

  },
  methods: {
    back() {
      let pages = getCurrentPages() // 获取栈实例
      if (pages.length > 1) {
        this.$navto.back()
      } else {
        this.$navto.replaceAll('Index')
      }
    },
    // 轮播菜单
    swiperChange(e) {
      this.changeTab(e.detail.current)
    },
    changeTab(index) {
      this.curIndex = index
    },
    navtoGo(url = '', obj = {}) {
      this.$navto.push(url, obj)
    },
    init(val) {
      this.$nextTick(() => {
        const curIndex = this.curIndex
        this.changeTab(curIndex)
        this.$refs.indexListRef.init()
        this.$refs.moreListRef.init()
      })
    },
    // 一键关注
    attention(e) {
      if (this.$validate.isNull(e)) {
        this.$uniPlugin.toast('请先选择圈子！')
        return
      }
      const param = {
        accountId: this.accountId,
        circleClassifyIds: e
      }
      this.$uniPlugin.loading('正在提交', true)
      this.$api.circleclassify.circleclassifySubscribe(param).then(res => {
        this.$uniPlugin.hideLoading()
        this.$uniPlugin.toast(res.msg)
        this.getCircleclassifyQueryList()
      }).catch(() => {
        this.$uniPlugin.hideLoading()
      })
    },
    
    // 提交登记
    confirm() {
      const param = {
        businessType: 4, // 业务类型:1-横幅，2-外部服务应用，3-企业微信群，4-圈子，5-帖子，6-帖子评论
        name: this.regForm.name,
        accountId: this.accountId,
        desc: this.regForm.name
      }
      this.$api.community.matterregisterInsert(param).then(res => {
        this.$uniPlugin.toast(res.msg)
        this.regForm.name = ''
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #F4F6FA;
}
.main {
  flex: 1;
  box-sizing: border-box;
  padding-bottom: env(safe-area-inset-bottom);
  .swiper {
    height: 100%;
  }
}
</style>
