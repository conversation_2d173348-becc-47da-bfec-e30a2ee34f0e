<template>
  <view class="videobox">

    <template v-for="(item, index) in list">
      <view class="playback-box" :key="item.id" @click="nextStep(index)">
        <!-- <view class="image-box">我是标题</view> -->
        <view class="image-box">
          <template v-if="!isarray[index]">
            <image :src="item.coverPathsUrl" binderror="errorImg(index)" class="playback-cover"></image>
          </template>
          <template v-else>
            <view class="defaultimg"></view>
            <!-- <image :src="item.coverPathsUrl" @error="errorImg(index)" class="playback-cover"></image> -->
          </template>

          <view class="tag active" v-if="item.activityStatus == 3">直播中</view>
          <view class="tag active" v-else-if="item.activityStatus == 5">回放</view>
          <view class="tag active" v-else-if="item.activityStatus == 2">预告</view>
          <view class="ing" v-if="item.activityStatus == 3">
            <!-- icon-im-increase.png -->
            <image :src="increaseico" class="ingico"></image>
            <!-- <text></text> -->
            {{((item.virtualViewNumber - 0) + (item.realViewNumber - 0)) || 0}} 观看
          </view>
        </view>
        <view class="playback-right">
          <view class="playback-title">{{ item.title }}</view>
          <view class="playback-des">
            {{item.intro}}
          </view>
          <view class="flex1">

          </view>
          <view class="playback-duration">时长：{{ item.startTimeText }} - {{ item.endTimeText }}</view>
        </view>
      </view>
    </template>

    <template v-if="list.length == 0 && isShowEmpty">
      <view class="nodatabox">
        <image :src="nodataico" class="nodataico" mode="widthFix" alt=""></image>
      </view>
    </template>
  </view>
</template>

<script>

const launchOptions = uni.getLaunchOptionsSync()
export default {
  name: 'videoItem',
  props: {
    isShowEmpty: {
      type: Boolean,
      default: true
    },
    list: {
      type: Array,
      default: function() {
        return [];
      }
    }
  },
  data() {
    return {
      file_ctx: this.file_ctx,
      isarray: [],
      nodataico:this.$static_ctx + "image/system/invalid/icon-no-data.png",
      increaseico:this.$static_ctx + 'image/business/live/icon-im-increase.gif'
    };
  },
  methods: {
    errorImg(index) {
      this.isarray[index] = true;
    },
    nextStep(index) {
      if(launchOptions.scene === 1154) return this.$uniPlugin.toast('请前往小程序使用完整服务') 
      console.log('index', index);
      this.$navto.push('shortVideoList', { id: this.list[index].id });
    }
  }
};
</script>

<style lang="scss" scoped>
  .nodatabox{
    display: flex;
    justify-content: center;
    margin-top: 50upx;
  }
  .nodataico{
    width: 60%;
    // margin: 0 auto;
  }
.playback-box {
  -webkit-border-radius: 20upx;
  -moz-border-radius: 20upx;
  border-radius: 20upx;
  overflow: hidden;
  background-color: #fff;
  padding: 16upx;
  margin-bottom: 24upx;
  // height: 200upx;
  height: 230upx;
  display: flex;
  border-bottom: 1px solid #f0f0f0;
  box-sizing: border-box;

  .ing{
    position: absolute;
    font-size: 24upx;
    background-color: rgba(0, 0, 0, 0.5);
    // background-color: #000;
    bottom: 0upx;
    left: 0upx;
    right: 0;
    padding: 5upx 20upx;
    color: #fff;
    border-radius: 25rpx;
    white-space: nowrap;
    overflow: hidden;

  }
  .ingico{
        width: 30upx;
        height: 30upx;
        vertical-align: top;
        margin-right: 10upx;
  }
  .image-box {
    position: relative;
  }
  .playback-cover {
    width: 240upx;
    height: 100%;
    display: inline-block;
    vertical-align: middle;
    -webkit-border-radius: 20upx;
    -moz-border-radius: 20upx;
    border-radius: 20upx;
    overflow: hidden;
  }
  .defaultimg {
    width: 240upx;
    height: 100%;
    background-color: #ccc;
  }
  .tag {
    position: absolute;
    right: 0;
    top: 0;
    z-index: 2;
    // background-color: #1890ff;
    background-color: $topicC;
    color: #fff;
    font-size: 24upx;
    padding: 6upx 12upx;
    border-bottom-left-radius: 8upx;
  }
  .playback-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding-left: 24upx;
    overflow: hidden;
  }
  .playback-des {
    // flex: 1;
    overflow: hidden;
    -webkit-line-clamp: 2;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    word-break: break-all;
    line-height: 1.5;
  }
  .flex1{
    flex: 1;
  }

  .playback-title {
    // display: inline-block;
    vertical-align: middle;
    // word-break: break-all;
    // display: -webkit-inline-box;
    // -webkit-line-clamp: 2;
    // -webkit-box-orient: vertical;
    // overflow: hidden;
    font-size: 32upx;
    color: #333;
    font-weight: 600;
	white-space: nowrap;
	overflow-x: scroll;
  }

  .playback-duration {
    color: silver;
    font-size: 20upx;
  }

}
</style>
