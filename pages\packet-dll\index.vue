<template>
  <page>
    <view slot="content" class="content">
      <image
        mode="widthFix"
        :src="file_ctx + 'static/image/business/packet-dll/icon-logo-dll.png'"
        class="icon-logo"
      />
      <view v-if="pageLoading" class="loading-text">
        加载中...
      </view>
      <view v-else-if="status !== 0" class="expired-box">
        <text v-if="status === 1" class="expired-text">您成功已领取过袋子</text>
        <text v-else-if="status === 2" class="expired-text">当前链接已过期</text>
        <text class="expired-afresh-text">如需再次领袋，请重新扫码</text>
      </view>
      <view v-else class="content-main-box">
        <image
          mode="widthFix"
          :src="file_ctx + 'static/image/business/packet-dll/poster.png'"
          class="poster"
        />
        <button type="default" class="receive-btn" :loading="receiveLoading" @tap="handleReceive">
          <image
            mode="aspectFit"
            :src="file_ctx + 'static/image/business/packet-dll/icon-dianji.png'"
            class="icon-dianji"
          />
          点我免费取袋
        </button>
      </view>
      <view class="error-tips">
        如遇取袋问题，请拨打<text @tap="callPhone">{{ kefuPhone }}</text>
      </view>
      <view class="device-num" v-if="deviceCode">设备编码  {{ deviceCode.substring(deviceCode.length - 6) }}</view>
    </view>
  </page>
</template>

<script>
import storage from '@/common/util/storage.js'
const { setStorageSync, getStorageSync } = storage
export default {
  data() {
    return {
      file_ctx: this.file_ctx,
      kefuPhone: '************',
      receiveLoading: false,
      deviceCode: '',
      timestamp: '',
      uid: '',
      needJump: 0, // needJump 1-跳转快团团 0-不跳转
      jumpParam: '',
      dllOrderId: '', // 出袋接口返回的订单id
      status: 0, // 领袋状态 1-领过袋 2-过期
      pageLoading: true
    }
  },
  onLoad() {
    const query = this.$Route.query || {}
    if (this.$validate.isNull(query)) {
      this.pageLoading = false
      return this.errorTip()
    }
    const { deviceCode, timestamp, uid, needJump = 0, jumpParam = '' } = query
    this.deviceCode = deviceCode
    this.timestamp = timestamp
    this.uid = uid
    this.needJump = needJump
    this.jumpParam = (needJump == 1 && typeof(jumpParam) === 'string') ? JSON.parse(jumpParam) : jumpParam
    this.initExpiredFn()
  },
  methods: {
    // 链接过期判断
    initExpiredFn() {
      if (getStorageSync(`packet-${this.timestamp}`)) {
        this.status = 1
        this.pageLoading = false
        return
      }
      const nowTimestamp = Date.now()
      if (this.$accurateConversion.minus(nowTimestamp, this.$accurateConversion.times(this.timestamp, 1000)) > 600000) {
        this.pageLoading = false
        return this.status = 2
      }
      this.pageLoading = false
      this.status = 0
    },
    async handleReceive() {
      this.receiveLoading = true
      if (this.needJump == 1) {
        await this.navGoKtt()
      }
      const res = await this.receivePacket()
      if (!res) {
        return this.receiveLoading = false
      }
      setStorageSync(`packet-${this.timestamp}`, true)
      this.receiveLoading = false
      this.$navto.replace('PacketDllResult', { dllOrderId: this.dllOrderId, deviceCode: this.deviceCode })
    },
    receivePacket() {
      return new Promise(async (resolve, reject) => {
        if (!this.deviceCode || !this.uid) {
          this.$uniPlugin.toast('参数缺失，请重试扫码进入！')
          return resolve(false)
        }
        const res = await this.$api.dll.dllOutBag({ deviceCode: this.deviceCode, uid: this.uid }).catch(() => {
          return resolve(false)
        })
        if (!res) return resolve(false)
        if (this.$validate.isNull(res.data) || this.$validate.isNull(res.data.data)) {
          this.$uniPlugin.toast(res.msg)
          return resolve(false)
        }
        if (this.$validate.isNull(res.data.data.data)) {
          const message = res.data.data.code === 99999 ? '您已经成功领过袋子了，感谢您的支持！' : res.data.data.message
          this.$uniPlugin.toast(message)
          return resolve(false)
        }
        this.dllOrderId = res.data.data.data.dllOrderId
        return resolve(true)
      })
    },
    navGoKtt() {
      return new Promise((resolve, reject) => {
        if (this.$validate.isNull(this.jumpParam)) return reject()
        const { appId, path } = this.jumpParam
        uni.navigateToMiniProgram({
          appId,
          path, // 打开的页面路径
          extraData: {}, // 需要传递的参数
          success: res => {
            resolve()
          },
          fail: err => {
            resolve()
            console.log('打开失败------', err)
          }
        })
      })
    },
    /**
     * 错误提示
     */
    errorTip(){
      this.$uniPlugin.toast('参数错误，请重新进入！')
    },
    callPhone() {
      wx.makePhoneCall({
        phoneNumber: this.kefuPhone, //仅为示例，并非真实的电话号码
        fail: res => {}
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.content {
  height: 100%;
  background-color: #fff;
  padding: 36rpx 46rpx calc(env(safe-area-inset-bottom) + 32rpx);
}

.icon-logo {
  width: 216rpx;
  height: 58rpx;
  margin-bottom: 75rpx;
}

.loading-text {
  font-weight: 600;
  font-size: 42rpx;
  color: #1FA2D1;
  text-align: center;
}

.expired-box {
  margin: 217rpx auto 452rpx;
}

.expired-text {
  display: block;
  color: #000000;
  font-size: 35rpx;
  margin-bottom: 32rpx;
  text-align: center;
}

.expired-afresh-text {
  display: block;
  color: #1FA2D1;
  font-size: 35rpx;
  text-align: center;
}

.content-main-box {
  width: 100%;
  background: #FFFFFF;
  box-shadow: 0rpx 5rpx 70rpx 2rpx rgba(31,162,209,0.19);
  border-radius: 18rpx;
  padding-bottom: 85rpx;
}

.poster {
  width: 100%;
  height: 526rpx;
}

.receive-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 94rpx auto 0;
  width: 620rpx;
  height: 84rpx;
  background: #1FA2D1;
  border-radius: 24rpx;
  font-size: 35rpx;
  color: #FFFFFF;
  &::after {
    content: none;
    border: none;
  }
}

.icon-dianji {
  width: 42rpx;
  height: 50rpx;
  margin-right: 33rpx;
}

.error-tips {
  margin-top: 144rpx;
  font-size: 28rpx;
  color: #777777;
  text-align: center;
}

.device-num {
  font-size: 25rpx;
  color: #777777;
  margin-top: 25rpx;
  text-align: center;
}
</style>
