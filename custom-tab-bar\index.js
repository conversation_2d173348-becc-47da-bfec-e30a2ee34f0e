Component({
  data: {
    // tabbar 的列表
    list:[
      {
        "pagePath": "/pages/index/index",
        "text": "首页",
        "iconPath": "/static/image/business/icon-home.png",
        "selectedIconPath": "/static/image/business/icon-home-active.png",
        "badge": ""
      },
      {
        "pagePath": "/pages/circle-home/index",
        "text": "交流",
        "iconPath": "/static/image/business/icon-exchange.png",
        "selectedIconPath": "/static/image/business/icon-exchange-active.png",
        "badge": ""
      },
      {
        "pagePath": "/pages/post-message/index",
        "text": "发帖",
        "iconPath": "/static/image/business/icon-plus.png",
        "selectedIconPath": "/static/image/business/icon-plus.png",
        "badge": ""
      },
      {
        "pagePath": "/pages/news/index",
        "text": "消息",
        "iconPath": "/static/image/business/icon-message.png",
        "selectedIconPath": "/static/image/business/icon-message-active.png",
        "badge": ""
      },
      {
        "pagePath": "/pages/personal/index",
        "text": "我的",
        "iconPath": "/static/image/business/icon-my.png",
        "selectedIconPath": "/static/image/business/icon-my-active.png",
        "badge": ""
      }
    ],
    selected: 0,
    color: "#B8BABC",
    selectedColor: "#00D29D",
  },
  lifetimes: {
    attached() {
      const tabBarBadge = wx.getStorageSync('tabBarBadge')
      const list = this.data.list
      if (tabBarBadge) {
        Object.keys(tabBarBadge).forEach(key => {
          list[+key].badge = tabBarBadge[key]
        })
      }
      const active = wx.getStorageSync('tabbarActive')
      this.setData({
        selected: active || 0,
        list
      })
    }
  },
  methods: {
    setBadge(index, badge) {
      const list = this.data.list
      list[index].badge = badge
      this.setData({
        list
      })
    },
    switchTab(e){
      const data = e.currentTarget.dataset
      const url = data.path
      if(getApp()){
        getApp().globalData.sensors.track("HomepageButtonClick",
          {
            'button_name' : data.text,
          }
        )
      }
      wx.switchTab({url})
      this.setData({
        selected: data.index
      })
    }
  }
})
