import {taskAPIsOrRouterMaps,isPathInInterfaceTable,clearListeningApi,triggerListeningApi,checkCurrentApi,getQueryOptions} from './options.js';
// 记录页面时长
let durationTimeS;
let durationTimeE;
// 监听页面进入
export function stratagemOnLoad(currentPath){
  // 获取进入时间
  durationTimeS = +new Date();
  let isStep = isPathInInterfaceTable(currentPath,'route')
  console.log('isStep',isStep);
  if(isStep.length === 0) return;
  isStep.map((e,index)=>{
      // 判断进入当前页面后是否需要监听某条接口
      if(e.apiPath) triggerListeningApi(e.apiPath,e);
      e.enterType = 'onLoad';//判定进入类型
      // 判断进入的页面文件里是否有需要被重写的需要被埋点的方法
      if(e.getOverriddenMethod){
        let [faOptions,funcName] = e.getOverriddenMethod(this);
        let oldFunc = faOptions[funcName];
        // 重写该方法
        faOptions[funcName] = function (){
          let parametersComparison = e.parametersComparison || {};
          // 集成参数
          let queryOptions = getQueryOptions(parametersComparison,{...this,arguments})
          let eventName = typeof e.eventName === 'function' ? e.eventName('OnLoad') : e.eventName;
          console.log('重写方法触发按钮方法埋点',eventName,queryOptions);
          delete queryOptions.duration
          getApp().globalData.sensors.track(eventName,queryOptions || {});
          oldFunc(...arguments);
        }.bind(this)
      }
  })
}
// 监听页面退出
export function stratagemOnUnload(currentPath){
  // 获取退出时间
  durationTimeE = +new Date();
  // 获取页面浏览时间
  let duration = durationTimeE - durationTimeS;
  // 时间归0
  durationTimeE = 0;
  durationTimeS = 0;
  let isStep = isPathInInterfaceTable(currentPath,'route').filter(e=>e.isExit);
  if(isStep.length === 0) return;
  isStep.map(e=>{
    let parametersComparison = e.parametersComparison || {};
    e.enterType = 'onUnload'; //判定进入类型
    // 集成参数
    let eventName = typeof e.eventName === 'function' ? e.eventName('OnUnload') : e.eventName;
    if(e.parametersMap.duration) e.parametersMap.duration = duration / 1000;
    console.log('触发页面退出埋点',eventName,e.parametersMap);
    getApp().globalData.sensors.track(eventName,e.parametersMap || {});
    e.parametersMap = null;
  })
}
// 监听请求
export const stratagemInterceptor = {
  // 监听请求成功前
  invoke(args){
    if(!args.url) return
    // 判定当前是否是哈希表中的api接口 如果是那么读取请求参数获取id
    let optionsMap = isPathInInterfaceTable(args.url).filter(e=>e.isBeforeRequest);
    optionsMap.map(e=>{
      let data = e.getArgument(args);
      let parametersComparison = e.parametersComparison || {};
      // 集成参数
      let queryOptions = getQueryOptions(parametersComparison,data);
      // 如果搜索关键词为空 则不埋点
      if(queryOptions.search_keyword === "") return;
      let eventName = typeof e.eventName === 'function' ? e.eventName('invoke') : e.eventName;
      console.log('触发请求前埋点',eventName,queryOptions);
      e.isBeforeParametersMap = queryOptions;
      getApp().globalData.sensors.track(eventName,queryOptions);
    })
    return true
  },
  // 监听请求成功后
  success(args){
    // 查找寄存栈里的接口
    let optionsMap = checkCurrentApi(args.config.url);
    // 查找固定栈中的api接口
    let apiOptionsMap = isPathInInterfaceTable(args.config.url).filter(e=>e.isAfterRequest);
    optionsMap = [...optionsMap,...apiOptionsMap];
    if(!optionsMap.length) return;
    // 获取当前接口返回参数
    const data = args.data.data;
    // 获取全量参数
    const fullData = {data,config:args.config};
    // 遍历配置参数 寄存栈e的参数分别有 api(api路径),id(寄存id),currentOptions(配置对象stratagemAPIsOrRouterMaps表中的项)
    optionsMap.map((e)=>{
      if(e.isRquery) return
      let {api,id,currentOptions} = e;
      // 有id说明是寄存栈里的接口
      if(id){
        e.isRquery = true;
      }else{
        currentOptions = e;
      }
      let parametersComparison = currentOptions.parametersComparison || {};
      // 集成参数
      let queryOptions = getQueryOptions(parametersComparison,currentOptions.getArgumentType === 'Before' ? fullData : data)
      let timeNum = currentOptions.timeNum || 0;
      // 这里有两种情况 一种是路由模块下填充到路由对应的接口里
      // 第二种是接口情况下 填充到接口的上次请求结果里
      currentOptions.parametersMap = queryOptions;
      // 触发埋点
      setTimeout(()=>{
        let eventName = typeof currentOptions.eventName === 'function' ? currentOptions.eventName('success') : currentOptions.eventName;
        console.log('触发请求后埋点',eventName,queryOptions);
        getApp().globalData.sensors.track(eventName,queryOptions);
        id && clearListeningApi(id)
      },timeNum * 1000)
    })
  }
}
