<template>
  <view class="pages">
    <!-- 地址信息 -->
    <view class="addressInfo">
      <view class="addressItem" v-for="(item,index) in addressInfo">
        <view class="addressItemTitle"><text class="sing">*</text>{{item.text}}</view>
        <view v-if="index === 2" @click="inputChange" class="addressItemInput" >{{item.value || '请选择省市区'}}</view>
        <input v-else-if="!item.type" class="addressItemInput" v-model="item.value" :placeholder="item.placeholder" type="text" />
        <textarea v-else v-model="item.value" :placeholder="item.placeholder" class="addressItemtextarea"></textarea>
      </view>
    </view>
    <!-- 设置默认地址 -->
    <view class="defaultAddress">
      <view>设置为默认地址</view>
      <switch :checked='!!isPrimary' @change="switchChange" />
    </view>
    <cityPicker ref='cityPicker' @onConfirm='selectCity'></cityPicker>
    <!-- 保存地址 -->
    <button class="keep" :loading='btnLoading' @click="keepAddress">保存</button>
  </view>
</template>

<script>
  import calabashApis from "@/modules/common/api/calabash.js"
  import { mapState } from "vuex";
  import cityPicker from './components/city-picker'
  export default{
    components: {
      cityPicker
    },
    data(){
      return {
        addressInfo:[
          {text:'收件人',placeholder:'收件人名字',value:'',key:'username'},
          {text:'手机号',placeholder:'请输入手机号',value:'',key:'phone'},
          {text:'所在地区',placeholder:'省、市、区',value:'',key:'addressText'},
          {text:'详细地址',placeholder:'小区、写字楼、门牌号等',type:'textarea',value:'',key:'address'},
        ],
        isPrimary:0,
        isEditor:false,
        id:null,
        btnLoading:false
      }
    },
    computed: {
      ...mapState("user", {
        accountId: (state) => state.accountId,
        fansRecord: (state) => state.fansRecord,
      }),
    },
    async onLoad(res) {
      console.log('res',res);
      if(!res.id) return
      this.isEditor = true;
      uni.setNavigationBarTitle({title: ' 编辑收货地址'});
      let {data} = await calabashApis.pointaddressQueryOne(res);
      this.addressInfo.map(e=>{
        if(e.key === 'addressText'){
          return this.$set(e,'value',data.province + data.city + data.county)
        }
        this.$set(e,'value',data[e.key])
      })
      this.isPrimary = data.isPrimary;
      this.id = data.id
    },
    methods:{
      selectCity(res){
        this.$set(this.addressInfo[2],'value',res.label)
      },
      inputChange(){
        this.$refs.cityPicker.showPickerView()
      },
      switchChange({detail:{value}}){
        this.isPrimary = +value
      },
      parseAddress(address) {
        // 分割字符串
        const parts = address.split('-')
      	let options = {};
      	parts[0] && (options.province = `${parts[0]}`);
      	parts[1] && (options.city = `${parts[1]}`);
      	parts[2] && (options.county = parts[2]);
        return options
      },
      async keepAddress(){
        let returnTitle = '';
        this.addressInfo.map(e=>(!e.value && !returnTitle) && (returnTitle = `请输入${e.text}`))
        if(returnTitle) return uni.showToast({title:returnTitle,icon:'none'})
        let options = this.addressInfo.reduce((cur,item)=>{
          if(item.key === 'addressText'){
            cur = {...cur,...this.parseAddress(item.value)}
            return cur
          }
          cur[item.key] = item.value
          return cur
        },{})
        options = {...options,isPrimary:this.isPrimary,accountId:this.accountId};
        let apiFunc = this.isEditor ? calabashApis.pointaddressUpdate : calabashApis.pointaddressInsert
        if(this.id) options.id = this.id
        this.btnLoading = true;
        let {data} = await apiFunc(options)
        this.btnLoading = false;
        if(data){
          setTimeout(()=>{
            uni.showToast({title:'插入成功',icon:'none'});
          },100)
          uni.navigateBack()
        }else{
        uni.showToast({title:'插入失败',icon:'none'});
      }
      }
    }
  }
</script>

<style lang="scss">
  .pages{
    width: 100vw;
    height: 100vh;
    background: #F4F6FA;
    overflow: scroll;
  }
  .addressInfo{
    width: 100vw;
    margin: 20rpx 0;
    background: #FFFFFF;
    padding: 0 32rpx;
    box-sizing: border-box;
    .addressItem{
      width: 100%;
      display: flex;
      .addressItemTitle{
        width: 152rpx;
        padding-top: 32rpx;
        font-weight: 600;
        font-size: 28rpx;
        color: #1D2029;
        .sing{
          font-weight: 500;
          font-size: 28rpx;
          color: #FF5500;
        }
      }
      .addressItemInput{
        width: 534rpx;
        height: 104rpx;
        border-bottom: 1rpx solid #EAEBF0;
        line-height: 104rpx;
      }
      .addressItemtextarea{
        width: 502rpx;
        height: 184rpx;
        margin-top: 39rpx;
      }
    }
  }
  .defaultAddress{
    width: 750rpx;
    height: 104rpx;
    background: #FFFFFF;
    display: flex;
    font-weight: 400;
    font-size: 28rpx;
    color: #1D2029;
    justify-content: space-between;
    align-items: center;
    padding: 0 34rpx;
    box-sizing: border-box;
  }
  .keep{
    width: 686rpx;
    height: 88rpx;
    line-height: 88rpx;
    background: #00B484;
    border-radius: 44rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #FFFFFF;
    text-align: center;
    position: fixed;
    bottom: 80rpx;
    left: 50%;
    transform: translateX(-50%);
  }
</style>
