<template>
  <page>
    <view slot="content" class="body-main">
      <view class="pharmacy-remind">
        <view class="remind-content" :style="{'overflow-y':listTempFlag?'scroll':'none','padding-bottom':listTempFlag?'600rpx':'0','height':listTempFlag?'calc(100vh - 780upx)':'100vh'}">
          <template v-if="currentIndex == 0">
            <scroll-view :scroll-y="true" :scroll-into-view="scrollToItemId">  
            <!-- <scroll-view scroll-y :show-scrollbar="false" style="height: 100%;"> -->
              <template v-if="listTempFlag">
                <view class="remind-item" v-for="(group,key, index) in list" :key="index">
                  <view class="title">{{ key }}</view>
                  <view class="title-item" v-for="(timeData,key2, index2) in group" :key="index2">
                    <template v-if="timeData.length > 1">
                      <view class="item-multiterm">
                        <view class="multiterm-content" v-for="(item,key3,index3) in timeData" :key="index3" @click="handleClickDetail(item,key3,index3)">
                        <view class="item-content-l" v-if="key3 == 0">{{ key2 }}</view>
                        <view class="item-content-l" v-else></view>
                        <view class="item-name">
                          <view class="title-l">{{ item.productName }}</view>
                          <view class="title-r">
                            <view class="title-value">{{ item.numOfTake }}</view>
                            <uni-icons :size="14" color="#999" type="right" />
                          </view>
                        </view>
                        </view>
                      </view>
                    </template>
                    <template v-else>
                      <view class="item-box">
                        <view class="item-content-r" v-for="(item,key3,index3) in timeData" :key="index3" @click="handleClickDetail(item,key3,index3)">
                          <view class="item-content-l" v-if="key3 == 0">{{ key2 }}</view>
                          <view class="item-names">
                            <view class="title-l">{{ item.productName }}</view>
                            <view class="title-r">
                              <view class="title-value">{{ item.numOfTake }}</view>
                              <uni-icons :size="14" color="#999" type="right" />
                            </view>
                          </view>
                        </view>
                      </view>
                    </template>
                  </view>
                </view>
              </template>
              <view class="empty" v-else>暂无计划</view>
              <view class="remind-bott" v-if="listTemp.length">已为你展示近 90 日的计划</view>
            </scroll-view>
          </template>
          <!-- <statistics-report v-else-if="currentIndex == 1" /> -->
          <pharmacy-manage v-else :margeUpdateCount="margeUpdateCount" :paramsObj="paramsObj" @handleDrugDetial="handleDrugDetial" />
          <button class="remind-btn" @click="createRemind">
            <view class="img"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/add.png'"></image></view>
            <span>创建</span>
          </button>
        </view>
        <view class="bottom-nav">
          <view 
            :class="currentIndex == index ? 'nav-item active' : 'nav-item'"
            v-for="(item,index) in navFilterList" 
            :key="index"
            @click="handleTabClick(index)"
          >
            <view class="img">
              <image :src="file_ctx + (currentIndex == index ? item.activeImg : item.img)"></image>
            </view>
            <span>{{ item.name }}</span>
          </view>
        </view>
      </view>
      <uni-popup ref="swiperPopup" id="swiperPopup" type="bottom">
        <view class="detail" v-if="groupDetail.id">
          <view class="header-btn" @click="handleCancel"><uni-icons :size="16" style="font-weight: bold;" color="#999" type="bottom" /></view>
          <view class="header">
            <view class="header-l">
              <image :src="file_ctx + headImgPath"></image>
            </view>
            <view class="header-r">
              <view class="title">{{ fansRecord.nickName }}</view>
              <view class="remind">{{ groupDetail.beginTimeText }}{{paramsObj.drugType == 2 ? '服用提醒' : '用药提醒'}}</view>
            </view>
          </view>
          <view class="content">
            <!-- <view class="header">未标记用药</view> -->
            <view class="info">
              <view class="info-title">{{ groupDetail.productName || '--' }}</view>
              <view class="info-item"><view class="img"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/alarm-clock.png'"></image></view>计划时间：{{ groupDetail.beginTimeText }}{{ groupDetail.remindTime }}</view>
              <view class="info-item"><view class="img"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/medicine-red.png'"></image></view>单次用量：{{ groupDetail.numOfTake }}</view>
              <view class="info-item"><view class="img"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/case.png'"></image></view>备注：{{ groupDetail.remark || '暂无' }}</view>
              <view class="info-item"><view class="img"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/distance.png'"></image></view>{{paramsObj.drugType == 2 ? '距上次服用时间' : '距上次用药'}}：暂无</view>
              <view class="info-btn" v-if="paramsObj.productName && guardObj.id && paramsObj.gs">
                <view class="info-btns info-btn-l" @click="handleJumpPage">{{paramsObj.drugType == 2 ? '电子说明书' : '药品说明书' }}</view>
                <view class="info-btns info-btn-r" @click="handleGuard">用药警戒</view>
              </view>
              <view class="info-btn-name" v-else-if="paramsObj.productName && paramsObj.gs" @click="handleJumpPage">
                {{paramsObj.drugType == 2 ? '电子说明书' : '药品说明书' }}
              </view>
            </view>
          </view>
          <!-- <view class="detail-tips">已用药?点此补标记</view> -->
        </view>
      </uni-popup>
    </view>
  </page>
</template>

<script>
  import { mapState } from 'vuex'
  import uniPopup from '@/components/uni/uni-popup'
  import UniIcons from '@/components/uni/uni-icons/uni-icons'
  import add from './components/add'
  import pharmacyManage from './components/pharmacy-manage' 
  // import statisticsReport from './components/statistics-report'
  import pharmacyDetail from './components/pharmacy-detail'
  import { isDomainUrl } from '@/utils/index.js'
  export default {
    components:{
      add,
      pharmacyManage,
      // statisticsReport,
      pharmacyDetail,
      UniIcons,
      uniPopup,
    },
    data(){
      return{
        file_ctx: this.file_ctx,
        navList:[
          {id:1,name:'每日计划',img:'static/image/business/pharmacy-cyclopedia/plan.png',activeImg:'static/image/business/pharmacy-cyclopedia/planActive.png'},
          // {id:2,name:'统计报告',img:''},
          {id:3,name:'用药管理',img:'static/image/business/pharmacy-cyclopedia/medicine.png',activeImg:'static/image/business/pharmacy-cyclopedia/medicineActive.png'},
        ],
        currentIndex:0,
        current:0, //swiper当前索引
        detailData:{}, //回显数据
        $constant: this.$constant,
        mescroll: null, // mescroll实例对象
        downOption: {
          auto: false // 不自动加载
        },
        upOption: {
          auto: false, // 不自动加载
        },
        contentList:[],
        groupDetail:{}, // 列表详情对象
        drugDetial:{}, // 用药详情对象
        setUpdateCount:0,
        margeUpdateCount:0, //用药管理
        list:null,
        listFlag:false,
        paramsObj:null,
        scrollToItemId: '', // 要滚动到的项的 ID  
        listTemp:[],
        listTempFlag:true,
        navFilterList:[], //过滤底部tab
        taskId:null,
      }
    },
    computed:{
      ...mapState('user', {
        fansRecord: state => state.fansRecord,
        curSelectUserInfo: state => state.curSelectUserInfo
      }),
      headImgPath(){
        return this.fansRecord.headPath ? this.fansRecord.headPath :  'static/image/system/avatar/icon-default-avatar.png'
      },

    },
    onLoad(options){
      const query = this.$validate.isNull(this.$Route.query) ? options : this.$Route.query
      console.log(query,'query5555')
      this.paramsObj = query
      if(query?.openDialog && Boolean(query?.openDialog)){
        this.pharmacyRemindQueryOne(this.paramsObj.taskId)
      }
      if(query?.productName){
        this.paramsObj.productName = decodeURIComponent(query.productName)
      }
      let title = query?.title
      if(title){
        uni.setNavigationBarTitle({
          title: title
        });
      }

      this.navFilterList = this.navList.map(item=>{
        // if((this.paramsObj?.productId == '2031353194431111174' || this.paramsObj?.productId == '2031448999104024578') && item.name == '用药管理'){
        if(this.paramsObj?.drugType == 2 && item.name == '用药管理'){
          item.name = '计划管理'
        }
        return {...item}
      })
      this.getPharmacyRemindQueryPage()
      if(this.paramsObj?.productId){
        this.productmedicationguideQueryOne(this.paramsObj?.productId) 
      }
      uni.$on("backPage", (e)=>{
        this.margeUpdateCount += 1
      }) 
      // #ifdef MP-WEIXIN
      this.handleClickPageTrack('OperationDetailsPageView')
      // #endif
    },
    onUnload(){
      // #ifdef MP-WEIXIN
      this.handleClickPageTrack('EndOperationDetailsPageView')
      // #endif
    },

    onUnload() {
      // 清除监听
      uni.$off('backPage');
    },

    mounted(){
      // #ifdef MP-ALIPAY
      my.setNavigationBar({
        frontColor: '#000000',
        backgroundColor: '#ffffff'
      })
      // #endif
      this.videoContext = uni.createVideoContext('myVideo', this)
    },
    methods:{
      // #ifdef MP-WEIXIN
      handleClickTrack(type){
        getApp().globalData.sensors.track("PopupClick",
          {
            'page_name' : '用药提醒',
            'popup_id' : 'homePswiperPopupopup',
            'popup_name' : '用药提醒详情弹窗',
            'click_type' : type == 1 ? '进入弹窗' : '取消弹窗',
          }
        ) 
      },
      handleClickPageTrack(type){
        let pages = getCurrentPages()
        current = pages[pages.length - 1]; // 获取到当前页面的信息
        let pageInfo = __wxConfig.page && __wxConfig.page[`${current.route}.html`] || ''; // 内部属性
        getApp().globalData.sensors.track(type,
          {
            'page_name' : pageInfo?.window?.navigationBarTitleText || '',
            'first_operation_name' : pageInfo?.window?.navigationBarTitleText || '',
            'second_operation_name' : '',
          }
        ) 
      },
      // #endif
      itemclick(item){
        if (item.indexOf("src") >= 0) {
          const imgs = [];
          item.replace(/]*src=['"]([^'"]+)[^>]*>/gi, function (match, capture) {
            imgs.push(capture);
          })
          
          wx.previewImage({
            current: imgs[0], // 当前显示图片的http链接
            urls: imgs
          })
        }
      },

      handleJumpPage(){
        // console.log('点击了跳转')
        this.$navto.replaceAll('PharmacyCyclopedia', {gs:this.paramsObj.gs})
      },

      async getPharmacyRemindQueryPage(){
        const { centerUserId = '' } = this.curSelectUserInfo || {}
        let params = {
          current: 1,
          size: 90,
          condition:{
            userId:centerUserId
          },
        }
        let res = await this.$api.drugBook.getPharmacyRemindQueryPage(params)

        res.data.records.sort((a, b) => a.beginTime - b.beginTime);  
        let data = res.data.records.map(item=>({...item,beginTimeText:this.formatDateDisplay(item.beginTime)}))
        if(res.data.records.length == 0){
          this.listTempFlag = false
        } else {
          this.listTemp = data
          this.list = this.transformArrayToObject(data)
          this.listTempFlag = true
        }
      },

      pharmacyRemindQueryOne(id){
        this.$api.drugBook.pharmacyRemindQueryOne({id}).then(res=>{
          this.groupDetail = res.data
          let arr = res.data.remindTime ? JSON.parse(res.data.remindTime) : ''
          let str = ''
          if(Array.isArray(arr)){
            str = arr.map(item => (item.remindTime)).join('-')
          }
          this.groupDetail.remindTime = str ? str : res.data.remindTime
          this.groupDetail.beginTimeText = this.formatDateDisplay(res.data.beginTime)
          this.paramsObj.drugType = res.data?.drugType || 1
          if(this.groupDetail.otherProfile){
            const jsonData = JSON.parse(this.groupDetail.otherProfile) 
            if(jsonData?.pharmacyRemindName){
              uni.setNavigationBarTitle({
                title: jsonData.pharmacyRemindName || '用药提醒'
              });
            }
          }
          this.$nextTick(()=>{
            this.$refs.swiperPopup.open()
            // #ifdef MP-WEIXIN
            this.handleClickTrack(1)
            // #endif
          })
        })
      },

      productmedicationguideQueryOne(productId){
        this.$api.drugBook.productmedicationguideQueryOne({productId}).then(res=>{
          this.guardObj = {...res.data,medicationGuideVideo:isDomainUrl(res.data.medicationGuideVideo),medicationGuideVideoCover:isDomainUrl(res.data.medicationGuideVideoCover)}
        })
      },

      transformArrayToObject(arrList) {
        // 创建一个空对象，用于存储分组数据  
        let groupedData = {};  
        arrList.forEach(item => {  
          let date = item.beginTimeText;  
          let time = item.remindTime;  
          // 如果当前日期在 groupedData 中不存在，则初始化一个空对象  
          if (!groupedData[date]) {  
            groupedData[date] = {};  
          }  
          
          // 如果当前时间在日期的分组中不存在，则初始化一个空数组  
          if (!groupedData[date][time]) {  
            groupedData[date][time] = [];  
          }  
          
          // 将 item 添加到对应时间和日期的数组中  
          groupedData[date][time].push(item);  

        })
        // 对每个日期的时间进行排序  
        for (let date in groupedData) {  
          // 提取时间键  
          let times = Object.keys(groupedData[date]).sort();  
          // 根据排序后的时间键重新构建时间对象  
          let sortedTimesObj = {};  
          for (let time of times) {  
            sortedTimesObj[time] = groupedData[date][time];  
          }  
            
          // 将排序后的时间对象替换原对象中的时间对象  
          groupedData[date] = sortedTimesObj;  
        }  
        return groupedData
      },

      // 日期转换为今明后天，是今年就是显示月日，不是今年就是显示年月日
      formatDateDisplay(timestamp) {  
        const currentDate = new Date();  
        const targetDate = new Date(timestamp);  
        
        // // 确保目标日期是有效的  
        if (isNaN(targetDate.getTime())) {  
          return '无效的日期';  
        }  
        
        // 计算目标日期与当前日期之间的差值（天）  
        const diffInDays = Math.ceil((targetDate - currentDate) / (1000 * 60 * 60 * 24));  
        
        // 根据差值返回相应的字符串  
        if (diffInDays === 0) {  
          return '今天';  
        } else if (diffInDays === 1) {  
          return '明天';  
        } else if (diffInDays === 2) {  
          return '后天';  
        } else {  
          // 获取当前年份  
          const currentYear = currentDate.getFullYear();  
          // 获取目标年份  
          const targetYear = targetDate.getFullYear();  
        
          // 如果目标日期在当前年份内，则只显示月日  
          if (targetYear === currentYear) {  
            return `${this.padZero(targetDate.getMonth() + 1)}月${this.padZero(targetDate.getDate())}日`;  
          } else {  
            // 否则，显示年月日  
            return `${targetYear}年${this.padZero(targetDate.getMonth() + 1)}月${this.padZero(targetDate.getDate())}日`;  
          }  
        }  
      },  

      // 辅助函数：如果数字小于10，则在前面补零  
      padZero(num) {  
        return num < 10 ? '0' + num : num;  
      },  

      // 创建
      createRemind(){
        this.current = 1
        this.setUpdateCount += 1
        this.$navto.push('PharmacyAdd',this.paramsObj)
      },

      // 计划详情
      handleClickDetail(item,key,index){
        this.groupDetail = item
        // this.pharmacyRemindQueryOne()
        this.$refs.swiperPopup.open()
        // #ifdef MP-WEIXIN
        this.handleClickTrack(1)
        // #endif
      },

      // 用药详情
      handleDrugDetial(obj){
        this.current = 1
        this.drugDetial = obj
      },

      handleCancel(){
        this.$refs.swiperPopup.close()
        // #ifdef MP-WEIXIN
        this.handleClickTrack(2)
        // #endif
      },
      
      handleTabClick(index){
        this.currentIndex = index 
        if(this.currentIndex == 0){
          this.getPharmacyRemindQueryPage()
        }
      } 
    },
 }
</script>

<style lang='scss' scoped>
.body-main{
  .pharmacy-remind{
    // height: 100%;
    position: relative;
    .remind-content{
      // height: calc(100% - 150upx);
      height: calc(100vh - 750upx);
      overflow-y: scroll;
      background-color: #f0f2f2;
      padding-bottom: 600rpx;
      // padding:10upx 30upx 0;
      .remind-item{
        // margin-bottom: 30rpx;
        margin: 50rpx 30rpx 30rpx;
        .title{
          padding-left: 15rpx;
          margin-bottom: 30upx;
          color: #525353;
          font-weight: 700;
        }
        .item-content{
          display: flex;
          flex-direction: column;
          // padding: 30rpx 0 30rpx 30rpx;  
          padding-left: 30rpx;
          background-color: #fff;  
          border-radius: 20rpx;
        }
        .title-item{
          display: flex;
          // align-items: center;
          // padding:30rpx 0;
          // padding: 30rpx 0 30rpx 30rpx; //新加的
          background-color: #fff; //新加的
          border-radius: 20rpx; //新加的
          margin-bottom: 20rpx; //新加的
          .item-content-l{
            margin-right: 20rpx;
            width: 72rpx;
            color: #999;
          }
          .item-name{
            display: flex;
            flex: 1;
            justify-content: space-between;
            border-bottom: 1rpx solid #e5e5e5;
            padding-bottom: 30rpx;
          }
          .item-names{
            display: flex;
            flex: 1;
            justify-content: space-between;
          }
          
          .item-box{
            width: 100%;
            padding:30rpx 0;
            .item-content-r{
              display: flex;
              flex: 1;
              align-items: center;
              justify-content: space-between;
              padding-left: 30rpx; //新加的
              .item-content-l{
                margin-right: 20rpx;
                width: 72rpx;
                color:#999;
              }
              .title-r{
                display: flex;
                padding-right: 10rpx;
                .title-value{
                  color:#999;
                  margin-right: 8rpx;
                }
                .sign{
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  background-color: #e87384;
                  color:#fff;
                  padding: 4rpx 20rpx;
                  border-radius: 25rpx;
                  font-size: 22rpx;
                }
              }
            }
          }
          .item-multiterm{
            display: flex;
            flex-direction: column;
            flex: 1;
            // align-items: center;
            // justify-content: space-between;
            // padding: 30rpx 0 30rpx 30rpx; //新加的
            .multiterm-content{
              display: flex;
              justify-content: space-between;
              padding: 30rpx 0 0 30rpx;
              .title-r{
                display: flex;
                padding-right: 10rpx;
                .title-value{
                  color:#999;
                  margin-right: 8rpx;
                }
                .sign{
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  background-color: #e87384;
                  color:#fff;
                  padding: 4rpx 20rpx;
                  border-radius: 25rpx;
                  font-size: 22rpx;
                }
              }
              &:first-child{
                // padding: 0 0 30rpx 0;
              }
              &:last-child{
                border-bottom: 0;
                .item-name{
                  border-bottom: 0;
                }
              }
            }
          }
        }
        &:first-child{
          margin: 0 30rpx 50rpx 30rpx;
          padding-top: 20rpx;
        }
      }
      .remind-bott{
        display: flex;
        justify-content: center;
        color: #bec0c0;
        padding-top: 100rpx;
      }
      .empty{
        display: flex;
        height: calc(100vh - 250rpx);
        align-items: center;
        justify-content: center;
        color: #bec0c0;
        padding-top: 100rpx;
      }
      .remind-btn{
        position: fixed;
        z-index: 99;
        bottom: 210rpx;
        left: 50%;
        transform: translateX(-50%);
        color: #fff;
        background: #13b38e;
        height: 100rpx;
        width: 300rpx;
        border-radius: 100rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        &::after{
          border: none !important;
        }
        .img{
          height: 32rpx;
          width: 32rpx;
          image{
            width: 100%;
            height: 100%;
          }
        }
      }
    }
    .bottom-nav{
      position: fixed;
      bottom: 0;
      left: 0;
      height: 150upx;
      padding-bottom: 30rpx;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content:space-around;
      background-color: #fff;
      .nav-item{
        display: flex;
        flex-direction: column;
        align-items: center;
        .img{
          width: 39rpx;
          height: 41rpx;
          // background-color: skyblue;
          margin-bottom: 10upx;
          image{
            width: 100%;
            height: 100%;
          }
        }
          &:last-child{
            .img{
              width: 49rpx;
              height: 37rpx;
            }
          }
      }
      .active{
        color:#13b38e;
      }
    }
  }
  .detail{
    height: 90vh;
    position: relative;
    background-color: #f5f7f7;
    padding:20rpx 30rpx;
    border-radius: 13rpx 13rpx 0 0;
    .header-btn{
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100rpx;
      height: 50rpx;
      background: #ebeded;
      border-radius: 25rpx;
      margin: 0 auto;
    }
    .header{
      display: flex;
      margin-top: 20upx;
      .header-l{
        height: 80rpx;
        width: 80rpx;
        border-radius: 50%;
        background-color: skyblue;
        margin-right: 20upx;
        overflow: hidden;
        image{
          width: 100%;
          height: 100%;
        }
      }
      .header-r{
        display: flex;
        flex-direction: column;
        .title{
          color:#333;
        }
        .remind{
          color:#999;
        }
      }
    }
    .content{
      margin-top: 30upx;
      .header{
        height: 100rpx;
        display: flex;
        align-items: center;
        background-color: #e87384;
        font-weight: bold;
        color:#fff;
        padding:0 30rpx;
        border-radius: 20rpx 20rpx 0 0;
        overflow: hidden;
      }
      .info{
        padding:40rpx 30rpx 30rpx;
        background-color: #fff;
        .info-title{
          font-weight: bold;
          color:#000;
          font-size: 34rpx;
        }
        .info-item{
          display: flex;
          margin-top: 25rpx;
          .img{
            width: 28rpx;
            height: 28rpx;
            margin: 4rpx 10rpx 0 0;
            image{
              width: 100%;
              height: 100%;
            }
          }
        }
        .info-btn-name{
          display: flex;
          justify-content: space-between;
          margin-top: 50rpx;
          height: 80rpx;
          align-items: center;
          justify-content: center;
          background-color: #f2f5f5;
          border-radius: 13rpx;
        }
        .info-btn{
          display: flex;
          justify-content: space-between;
          margin-top: 50rpx;
          height: 80rpx;
          .info-btns{
            display: flex;
            flex: 1;
            height: 80rpx;
            align-items: center;
            justify-content: center;
            background-color: #f2f5f5;
            border-radius: 13rpx;
          }
          .info-btn-l{
            margin-right: 20upx;  
          }
          .info-btn-r{}
        }
      }
    }
    .detail-tips{
      margin-top: 100rpx;
      text-align: center;
      text-decoration: underline;
    }
  }
  .guard-content,.guard-detail-content{
    height: 90vh;
    position: relative;
    background-color: #f5f7f7;
    padding:20rpx 30rpx;
    border-radius: 13rpx 13rpx 0 0;
    overflow-y: scroll;
    .header-btn{
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100rpx;
      height: 50rpx;
      background: #ebeded;
      border-radius: 25rpx;
      margin: 0 auto;
    }
    .matters,.apply-method,.apply-video{
      position: relative;
      padding: 20rpx;
      margin-top: 40rpx;
      border-radius: 16rpx;
      background-color: #fff;
      .title{
        font-weight: bold;
        color:#000;
        font-size: 34rpx;
        margin-bottom: 20rpx;
      }
      .text-one{
        line-height: 50rpx;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .text-rich{
      }
      .my-video{
        height: 180px;
        width: 100%;
        border-radius: 16rpx;
      }
      .custom-play-btn{
        position: absolute;  
        top: 55%;  
        left: 50%;  
        transform: translate(-50%, -55%);  
        width: 48rpx;
        height: 48rpx;
        // width: 96rpx;
        // height: 96rpx;
        // opacity: 0.7;
        image{
          width: 100%;
          height: 100%;
        }
      }
      .matters-bottom{
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20rpx;
        padding-top: 10rpx;
        border-top: 1rpx solid #f2f2f2;
        .matters-bottom-l{
          display: flex;
          align-items: center;
          .bottom-l-img{
            width: 32rpx;
            height: 32rpx;
            border-radius: 50%;
            image{
              width: 100%;
              height: 100%;
            }
          }
          .bottom-l-text{
            margin-left: 10rpx;
          }
        }
        .matters-bottom-r{

        }
      }
    }
    .apply-video{
      display: flex;
      flex-direction: column;
      flex: 1;
      margin-top: 0;
    }
    .apply-method{
      margin: 30rpx 0;
    }
  }
  .guard-content{
    display: flex;
    flex-direction: column;
  }
}
/deep/.uni-popup__wrapper.uni-custom.bottom .uni-popup__wrapper-box{
  max-height: none;
  overflow-y: none;
}


</style>