<!-- 悬浮菜单 <tabs-sticky v-model="index" :fixed="false" @change="changeTab"></tabs-sticky> -->
<template>
  <scroll-view class="tabs-sticky" :style="'background:' + background" :scroll-x="overflowX" :class="{'tabs-fixed': fixed,'top':top + 'upx', 'display-flex': !overflowX, 'overflow-x': overflowX, 'bdb': bdb}">
    <view class="tabs-sticky-body" :class="{'white-space': overflowX}">
      <view class="tab" v-for="(tab, i) in tabs" :key="i" @click="changeTab(i)">
        <text :class="value == i ?'text-default active':'text-default'">{{tab.name}}</text>
        <view v-if="value==i" class="text-width"><image :src="file_ctx + 'static/image/business/hulu-v2/border-bottom.png'"></image></view>
      </view>
      <view class="empty-space"></view>
    </view>
  </scroll-view>
</template>

<script>
export default {
  props: {
    // 数组下标
    dIndex: {
      type: [String, Number],
      default() {
        return ''
      }
    },
    background: {
      type: String,
      default() {
        return '#fff'
      }
    },
    fontBigger: {
      type: Boolean,
      default() {
        return false
      }
    },
    overflowX: {
      type: Boolean,
      default() {
        return false
      }
    },
    bdb: {
      type: Boolean,
      default() {
        return true
      }
    },
    tabs: {
      type: Array,
      default() {
        return []
      }
    },
    value: {
      type: Number,
      default() {
        return 0
      }
    },
    fixed: {
      type: Boolean,
      default() {
        return false
      }
    },
    top: {
      type: [Number, String],
      default() {
        return 0
      }
    }

  },
  data(){
    return {
      file_ctx: this.file_ctx,
    }
  },
  methods: {
    // 切换tab
    changeTab(i) {
      if (this.value != i) {
        if (!this.$validate.isNull(this.dIndex)) {
          const obj = {
            value: i,
            index: this.dIndex
          }
          this.$emit('changeObj', obj)
          return
        }
        this.$emit('input', i)
        this.$emit('change', i)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .white-space{
    white-space: nowrap;
    padding: 0 20upx;
    display: flex;
  }
  .display-flex{
    display: flex;
    view{
      display: flex;
      width: 100%;
      .tab{
        flex: 1;
        text{
          line-height: 70upx;
          flex: 1;
          display: inline-block;
        }
      }
    }
  }
  .tabs-sticky{
    height: 60upx;
    font-size: 36upx;
    text-align: center;
    .tabs-sticky-body{
      .tab{
        display: inline-block;
        position: relative;
        vertical-align: middle;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 48rpx;
        .text-default{
          height: 50rpx;
          line-height: 50rpx;
          font-family: '思源黑体';
          font-size: 32rpx;
          color: #1D2029;
          white-space: nowrap;
        }
        .active{
          font-size: 36rpx;
          font-weight: 600;
          color: #00B484 !important;
        }
        .text-width{
          display: flex;
          width: 38rpx;
          height: 10rpx;
          image{
            width: 100%;
            height: 100%;
          }
        }
        &:last-child{
          margin-right: 0;
        }
      }
    }
  }
  .empty-space{
    width: 20rpx;
    flex-shrink: 0;
  }
  /*悬浮样式*/
  .tabs-sticky.tabs-fixed{
    z-index: 2;
    position: fixed;
    top: 88upx;
    left: 0;
    width: 100%;
  }
</style>
