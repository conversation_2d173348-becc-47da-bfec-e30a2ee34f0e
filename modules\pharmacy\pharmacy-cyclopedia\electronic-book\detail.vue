<template>
  <page>
    <view slot="content" class="body-main" @tap="closeBoxIfOutside">
      <view class="my-data" :style="{'background-image':'url('+ file_ctx +'static/image/business/pharmacy-cyclopedia/icon-specification-nav-bg.png)','background-size': '100%'}">
        <view :style="'height:' + statusBarHeight + 'px;'"></view>
        <view class="top-nav">
          <view class="top-nav-l" @click.stop="handleBack"><image mode="aspectFit" :src="file_ctx + 'static/image/business/hulu-v2/icon-circle-left-arrows.png'" class="header-search-img"/></view>
          <view class="top-nav-c">电子说明书</view>
        </view>

        <view class="electronic-info">
          <view class="electronic-content">
            <view class="title">{{ detailObj.commonName }}</view>
            <view class="info">
              <view class="info-l"><view v-if="detailObj.drugName" style="display:flex">{{ detailObj.drugName }}<view class="img"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-pharmacy-detail-r.png'"></image></view></view>{{fullSpecificationData.specification}}</view>
              <view class="info-r" @click.stop.prevent="fontShow = !fontShow">
                <span>字体大小</span>
                <view :class="fontShow ? 'img rotate':'img'"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-specification-look-more.png'"></image></view>
              </view>
            </view>
          </view>
        </view>
        <view class="triangle" :style="{'display':fontShow?'block':'none'}"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-pharmacy-font-triangle.png'"></image></view>
        <view class="font-box" :style="{'display':fontShow?'block':'none'}">
          <view :class="currentIndex == index ? 'active font-item':'font-item'" v-for="(item,index) in fontList" :key="item.id" @click="handleMouseEnter(item,index)" >
            <span :style="{fontSize:item.fontSize,lineHeight:item.lineHeight}">{{ item.name }}</span>
            <view class="img" v-if="currentIndex == index"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-specification-tick.png'"></image></view>
          </view>
        </view>
      </view>
      <scroll-view class="scrollable-content" :scroll-y="true" :style="{'height': `calc(100% - ${380 + myDataHeight}rpx)`,top:380 + myDataHeight +'rpx'}">
        <view class="content">
          <view class="versions" v-for="(item,index) in fullSpecificationData.customData" :key="index">
            <view class="versions-t" @click.stop="handleClickMore(item)">
              <view class="versions-l">
                <view class="title" :style="{fontSize:myFontSize}">{{ item.title }}</view>
                <view v-if="item.convertSwitch == 1" :class="item.ingredientRead ?'broadcast active' :'broadcast'" @click.stop="handleIngredientRead(item,index)">
                  <view class="img" v-if="item.ingredientRead"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-specification-read-gif.gif'"></image></view>
                  <view class="img" v-else><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-specification-read-gif-default.png'"></image></view>
                  <span>语音播报</span>
                </view>
              </view>
              <!-- <view class="versions-r" @click="handleClickMore(isShow,'versions')"> -->
              <view class="versions-r" @click.stop="handleClickMore(item)">
                <image :src="file_ctx + (item.isShow ? 'static/image/business/pharmacy-cyclopedia/icon-specification-up.png' : 'static/image/business/pharmacy-cyclopedia/icon-specification-downward.png')"></image>
              </view>
            </view>
            <view class="versions-b" :style="{'display':item.isShow?'block':'none'}">
              <!-- <rich-text class="name" :nodes="formatRichText(item.content)" preview  @click ="itemclick(item.content)" :style="{fontSize:myFontSize,lineHeight:myFineHeight,letterSpacing:'2rpx',whiteSpace: 'pre-wrap',}"></rich-text> -->
              <rich-text class="name" :nodes="escapeHtml(item.content)"  preview  @click ="itemclick(item.content)" :style="{fontSize:myFontSize,letterSpacing:'2rpx'}"></rich-text>
            </view>
          </view>
        </view>
        <view class="look-bott">本网页服务由绿葆提供，相关内容仅供参考<br>不能替代执业医师或药师的意见，请谨慎参阅</view>
      </scroll-view>
      <!-- </view> -->
    </view>
  </page>
</template>

<script>
  import { mapState } from 'vuex'
  import { isDomainUrl } from '@/utils/index.js'
  export default {
    data(){
      return{
        file_ctx:this.file_ctx,
        statusBarHeight: 0,
        ingredientRead:false,
        fontList:[
          {id:1,name:'小字号',fontSize:'28rpx',lineHeight:'40rpx'},
          {id:2,name:'标准字号',fontSize:'32rpx',lineHeight:'44rpx'},
          {id:3,name:'中字号',fontSize:'36rpx',lineHeight:'48rpx'},
          {id:4,name:'大字号',fontSize:'40rpx',lineHeight:'52rpx'},
        ],
        fontShow:false,
        currentIndex:1,
        detailObj:{},//详情对象
        fullSpecificationData:{},
        myFontSize:'32rpx',
        myFineHeight:'40rpx',
        currentAudio:null,
        myDataHeight:0,
      }
    },
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId,
        publishInfo:state => state.publishInfo,
        curSelectUserInfo:state => state.curSelectUserInfo
      }),
    },
    onLoad(option){
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
      const query = this.$validate.isNull(this.$Route.query) ? option : this.$Route.query
      this.detailObj = query
      if(query?.id){
        this.fullSpecificationQueryOne(query.id)
      }
    },
    onUnload() {
      if (this.currentAudio) {
        this.currentAudio.stop(); // 停止播放
        this.currentAudio.destroy(); // 销毁实例
      }
    },
    onShareAppMessage: function (res) {  
      if (res.from === 'share') {  
        // 来自页面内转发按钮  
        console.log(res.target);  
      }
      this.queryAndCreate(3)
      return {
        title: `${this.publishInfo.name}${this.detailObj.labelValue}`, //分享的名称
        path: 'modules/pharmacy/pharmacy-cyclopedia/index?gs='+ encodeURIComponent(this.detailObj.gs),
        mpId:this.$appId, //此处配置微信小程序的AppId
      }
    },
    mounted(){
      this.getElementHeight()
    },
    methods:{
      getElementHeight() {
        let query = uni.createSelectorQuery().in(this);
        query.select('.electronic-info .electronic-content .title').boundingClientRect(data => {
          if (data) {
            this.myDataHeight = data.height
          }
        }).exec();
      },
      async queryAndCreate(type){
        let { phone = '' } =  this.curSelectUserInfo || {}
        let params = {
          accountId:this.accountId,
          phone: phone,
          productId:this.detailObj?.productId,
          shareType:type,  //	分享类型：1分享按钮2保存二维码3微信好友分享按钮
        }
        await this.$api.drugBook.queryAndCreate(params)
      },
      escapeHtml(str) {
        var arrEntities = {
          'nbsp': ' ',
          'iexcl': '¡',
          'cent': '¢',
          'pound': '£',
          'curren': '¤',
          'yen': '¥',
          'brvbar': '¦',
          'sect': '§',
          'uml': '¨',
          'copy': '©',
          'ordf': 'ª',
          'laquo': '«',
          'not': '¬',
          'shy': '',
          'reg': '®',
          'macr': '¯',
          'deg': '°',
          'plusmn': '±',
          'sup2': '²',
          'sup3': '³',
          'acute': '´',
          'micro': 'µ',
          'para': '¶',
          'middot': '·',
          'cedil': '¸',
          'sup1': '¹',
          'ordm': 'º',
          'raquo': '»',
          'frac14': '¼',
          'frac12': '½',
          'frac34': '¾',
          'iquest': '¿',
          'Agrave': 'À',
          'Aacute': 'Á',
          'Acirc': 'Â',
          'Atilde': 'Ã',
          'Auml': 'Ä',
          'Aring': 'Å',
          'AElig': 'Æ',
          'Ccedil': 'Ç',
          'Egrave': 'È',
          'Eacute': 'É',
          'Ecirc': 'Ê',
          'Euml': 'Ë',
          'Igrave': 'Ì',
          'Iacute': 'Í',
          'Icirc': 'Î',
          'Iuml': 'Ï',
          'ETH': 'Ð',
          'Ntilde': 'Ñ',
          'Ograve': 'Ò',
          'Oacute': 'Ó',
          'Ocirc': 'Ô',
          'Otilde': 'Õ',
          'Ouml': 'Ö',
          'times': '×',
          'Oslash': 'Ø',
          'Ugrave': 'Ù',
          'Uacute': 'Ú',
          'Ucirc': 'Û',
          'Uuml': 'Ü',
          'Yacute': 'Ý',
          'THORN': 'Þ',
          'szlig': 'ß',
          'agrave': 'à',
          'aacute': 'á',
          'acirc': 'â',
          'atilde': 'ã',
          'auml': 'ä',
          'aring': 'å',
          'aelig': 'æ',
          'ccedil': 'ç',
          'egrave': 'è',
          'eacute': 'é',
          'ecirc': 'ê',
          'euml': 'ë',
          'igrave': 'ì',
          'iacute': 'í',
          'icirc': 'î',
          'iuml': 'ï',
          'eth': 'ð',
          'ntilde': 'ñ',
          'ograve': 'ò',
          'oacute': 'ó',
          'ocirc': 'ô',
          'otilde': 'õ',
          'ouml': 'ö',
          'divide': '÷',
          'oslash': 'ø',
          'ugrave': 'ù',
          'uacute': 'ú',
          'ucirc': 'û',
          'uuml': 'ü',
          'yacute': 'ý',
          'thorn': 'þ',
          'yuml': 'ÿ',
          'fnof': 'ƒ',
          'Alpha': 'Α',
          'Beta': 'Β',
          'Gamma': 'Γ',
          'Delta': 'Δ',
          'Epsilon': 'Ε',
          'Zeta': 'Ζ',
          'Eta': 'Η',
          'Theta': 'Θ',
          'Iota': 'Ι',
          'Kappa': 'Κ',
          'Lambda': 'Λ',
          'Mu': 'Μ',
          'Nu': 'Ν',
          'Xi': 'Ξ',	
          'Omicron': 'Ο',
          'Pi': 'Π',
          'Rho': 'Ρ',
          'Sigma': 'Σ',
          'Tau': 'Τ',
          'Upsilon': 'Υ',
          'Phi': 'Φ',
          'Chi': 'Χ',
          'Psi': 'Ψ',
          'Omega': 'Ω',
          'alpha': 'α',
          'beta': 'β',
          'gamma': 'γ',
          'delta': 'δ',
          'epsilon': 'ε',
          'zeta': 'ζ',
          'eta': 'η',
          'theta': 'θ',
          'iota': 'ι',
          'kappa': 'κ',
          'lambda': 'λ',
          'mu': 'μ',
          'nu': 'ν',
          'xi': 'ξ',
          'omicron': 'ο',
          'pi': 'π',
          'rho': 'ρ',
          'sigmaf': 'ς',
          'sigma': 'σ',
          'tau': 'τ',
          'upsilon': 'υ',
          'phi': 'φ',
          'chi': 'χ',
          'psi': 'ψ',
          'omega': 'ω',
          'thetasym': '?',
          'upsih': '?',
          'piv': '?',
          'bull': '•',
          'hellip': '…',
          'prime': '′',
          'Prime': '″',
          'oline': '‾',
          'frasl': '⁄',
          'weierp': '℘',
          'image': 'ℑ',
          'real': 'ℜ',
          'trade': '™',
          'alefsym': 'ℵ',
          'larr': '←',
          'uarr': '↑',
          'rarr': '→',
          'darr': '↓',
          'harr': '↔',
          'crarr': '↵',
          'lArr': '⇐',
          'uArr': '⇑',
          'rArr': '⇒',
          'dArr': '⇓',
          'hArr': '⇔',
          'forall': '∀',
          'part': '∂',
          'exist': '∃',
          'empty': '∅',
          'nabla': '∇',
          'isin': '∈',
          'notin': '∉',
          'ni': '∋',
          'prod': '∏',
          'sum': '∑',
          'minus': '−',
          'lowast': '∗',
          'radic': '√',
          'prop': '∝',
          'infin': '∞',
          'ang': '∠',
          'and': '∧',
          'or': '∨',
          'cap': '∩',
          'cup': '∪',
          'int': '∫',
          'there4': '∴',
          'sim': '∼',
          'cong': '∝',
          'asymp': '≈',
          'ne': '≠',
          'equiv': '≡',
          'le': '≤',
          'ge': '≥',
          'sub': '⊂',
          'sup': '⊃',
          'nsub': '⊄',
          'sube': '⊆',
          'supe': '⊇',
          'oplus': '⊕',
          'otimes': '⊗',
          'perp': '⊥',
          'sdot': '⋅',
          'lceil': '?',
          'rceil': '?',
          'lfloor': '?',
          'rfloor': '?',
          'lang': '?',
          'rang': '?',
          'loz': '◊',
          'spades': '♠',
          'clubs': '♣',
          'hearts': '♥',
          'diams': '♦',
          'quot': '"',
          'amp': '',
          'lt': '<',
          'gt': '>',
          'OElig': 'Œ',
          'oelig': 'œ',
          'Scaron': 'Š',
          'scaron': 'š',
          'Yuml': 'Ÿ',
          'circ': 'ˆ',
          'tilde': '˜',
          'ensp': '',
          'emsp': '',
          'thinsp': '',
          'zwnj': '',
          'zwj': '‍',
          '‎lrm': '',
          'rlm': '',
          'ndash': '–',
          'mdash': '—',
          'lsquo': '‘',
          'rsquo': '’',
          'sbquo': '‚',
          'ldquo': '“',
          'rdquo': '”',
          'bdquo': '„',
          'dagger': '†',
          'Dagger': '‡',
          'permil': '‰',
          'lsaquo': '‹',
          'rsaquo': '›',
          'euro': '€'
        };
        return str.replace(
          /&(nbsp|iexcl|cent|pound|curren|yen|brvbar|sect|uml|copy|ordf|laquo|not|shy|reg|macr|deg|plusmn|sup2|sup3|acute|micro|para|middot|cedil|sup1|ordm|raquo|frac14|frac12|frac34|iquest|Agrave|Aacute|Acirc|Atilde|Auml|Aring|AElig|Ccedil|Egrave|Eacute|Ecirc|Euml|Igrave|Iacute|Icirc|Iuml|ETH|Ntilde|Ograve|Oacute|Ocirc|Otilde|Ouml|times|Oslash|Ugrave|Uacute|Ucirc|Uuml|Yacute|THORN|szlig|agrave|aacute|acirc|atilde|auml|aring|aelig|ccedil|egrave|eacute|ecirc|euml|igrave|iacute|icirc|iuml|eth|ntilde|ograve|oacute|ocirc|otilde|ouml|divde|oslash|ugrave|uacute|ucirc|uuml|yacute|thorn|yuml|fnof|Alpha|Beta|Gamma|Delta|Epsilon|Zeta|Eta|Theta|Iota|Kappa|Lambda|Mu|Nu|Xi|	Omicron|Pi|Rho|Sigma|Tau|Upsilon|Phi|Chi|Psi|Omega|alpha|beta|gamma|delta|epsilon|zeta|eta|theta|iota|kappa|mu|xi|omicron|pi|rho|sigmaf|sigma|tau|upsilon|phi|chi|psi|omega|thetasym|upsih|piv|bull|hellip|prime|Prime|frasl|image|real|trade|alefsym|larr|uarr|rarr|darr|harr|crarr|uArr|rArr|dArr|hArr|forall|part|exist|empty|nabla|isin|notin|ni|prod|sum|minus|lowast|radic|prop|infin|ang|and|or|cap|cup|int|there4|sim|cong|asymp|ne|equiv|le|ge|sub|sup|nsub|sube|supe|oplus|otimes|perp|sdot|lceil|rceil|lfloor|rfloor|lang|rang|loz|spades|clubs|hearts|diams|quot|amp|lt|gt|OElig|oelig|Scaron|scaron|Yuml|circ|tilde|ensp|emsp|thinsp|zwnj|zwj|lrm|rlm|ndash|mdash|lsquo|rsquo|sbquo|ldquo|rdquo|bdquo|dagge|Dagger|permil|lsaquo|rsaquo|euro);/ig,
          function(all, t) {
            return arrEntities[t];
        });
      },


      itemclick(item){
        // 判断含有图片
        if (item.indexOf("src") >= 0) {
          const imgs = [];
          item.replace(/]*src=['"]([^'"]+)[^>]*>/gi, function (match, capture) {
            imgs.push(capture);
          })
          
          uni.previewImage({
            current: imgs[0], // 当前显示图片的http链接
            urls: imgs
          })
        }
      },

      closeBoxIfOutside(e){
        // console.log(e,'e2222')
        // 假设 custom-box 是自定义盒子的类名  
        if (!e.target.classList || !e.target.classList.contains('font-box') && this.fontShow) {  
          this.fontShow = false;  
        }  
      },

      handleMouseEnter(item,index){
        // console.log(item,'item2222')
        this.myFontSize = item.fontSize
        // this.myFineHeight = item.lineHeight
        this.currentIndex = index
      },

      handleClickRead(){
        this.ingredientRead = true 
      },

      handleBack(){
        this.$navto.back(1)
      },

      async fullSpecificationQueryOne(id){
        const res = await this.$api.drugBook.fullSpecificationQueryOne({id})
        let customData = JSON.parse(res.data.customData)
        let newArr = customData.map((item,index)=>({...item,isShow:index == 0 ? true : false,ingredientRead:false}))
        // this.fullSpecificationData = {...res.data,customData:JSON.parse(res.data.customData)}
        this.fullSpecificationData = {...res.data,customData:newArr}
      },

      handleClickMore(item){
        item.isShow = !item.isShow
      },
      handleIngredientRead(item,readIndex){
        // 每次点击都要清空一次状态
        this.currentAudio?.stop();
        this.currentAudio?.destroy();
        this.fullSpecificationData.customData.forEach((item,index)=>{
          if(index != readIndex){
            item.ingredientRead = false
          }
        })
        item.ingredientRead = !item.ingredientRead
        if(item.ingredientRead){
          // console.log('每次进来的开始播放')
          this.playAudio(item,isDomainUrl(item.audioUrl))
        } else {
          // console.log('关闭播放了')
          this.currentAudio.stop();
          this.currentAudio.destroy();
        }
      },
      playAudio(item,src) {
        // 创建新的 InnerAudioContext 实例
        const innerAudioContext = uni.createInnerAudioContext();
        innerAudioContext.autoplay = true;
        innerAudioContext.src = src;
        this.$nextTick(()=>{
          innerAudioContext.onPlay(()=>{
            // console.log('开始播放')
          });
        })
        innerAudioContext.onEnded(()=>{
          // console.log('播放结束')
          item.ingredientRead = false
        })
      
        if (this.currentAudio) {
          // 停止并释放上一个实例
          this.currentAudio.stop();
          this.currentAudio.destroy();
        }
      
        // 更新当前播放器引用
        this.currentAudio = innerAudioContext;
      }
    },
 }
</script>

<style lang='scss' scoped>
.body-main{
  background: #F4F6FA;
  position: relative;
  height: 100%;
}

.my-data{
  height: 350rpx;
  width: 100%;
  .top-nav{
    // position: fixed;
    width: calc(100% - 56rpx);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    line-height: 40px;
    // z-index: 999;
    padding: 0 32rpx 0 24rpx;
    .top-nav-l{
      display: flex;
      width: 48rpx;
      height: 48rpx;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .top-nav-c{
      flex: 1;
      text-align: center;
      height: 44rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #2D2F38;
      line-height: 44rpx;
      margin-right: 48rpx;
    }
  }
  .electronic-info{
    // padding-top: 25rpx;
    padding-top: 20rpx;
    // height: calc(174rpx);
    // background-position-y: 50rpx;
    .electronic-content{
      margin:0 32rpx;
      padding:32rpx 24rpx;
      background: #FFFFFF;
      border-radius: 16rpx;
      // overflow: hidden;
      // position: absolute;
      // width: calc(100% - 112rpx);
      // top: 196rpx;
      .title{
        // height: 50rpx;
        font-weight: 600;
        font-size: 36rpx;
        color: #2D2F38;
        line-height: 50rpx;
      }
      .info{
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 6rpx;
        .info-l{
          display: flex;
          font-size: 28rpx;
          color: #4E5569;
          line-height: 40rpx;
          .img{
            display: flex;
            width: 28rpx;
            height: 28rpx;
            margin-right: 2rpx;
            image{
              width: 100%;
              height: 100%;
            }
          }
        }
        .info-r{
          display: flex;
          align-items: center;
          justify-content: center;
          padding:10rpx 24rpx;
          border-radius: 36rpx;
          border: 1rpx solid rgba(0,180,132,0.4);
          span{
            display: inline-block;
            font-weight: 600;
            font-size: 28rpx;
            color: #00B484;
            line-height: 40rpx;
            margin-right: 8rpx;
          }
          .img{
            display: flex;
            width: 12rpx;
            height: 8rpx;
            transform: rotate(0deg);
            image{
              width: 100%;
              height: 100%;
            }
          }
          .rotate{
            transform: rotate(180deg);
          }
        }
      }
    }
  }
  .triangle{
    position: absolute;
    // top: 184rpx;
    top: 320rpx;
    left: 574rpx;
    width: 58rpx;
    height: 18rpx;
    z-index: 9999;
    image{
      width: 100%;
      height: 100%;
    }
    // border-left: 20rpx solid transparent;
    // border-right: 20rpx solid transparent;
    // border-bottom: 20rpx solid #fff;
  }
  .font-box{
    // position: relative;
    // top: 280rpx;
    position: absolute;
    // top: 215rpx;
    top: 352rpx;
    width: 318rpx;
    left: 400rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    overflow: hidden;
    // padding: 12rpx 0;
    z-index: 999;
    // filter: drop-shadow(0px 1px 3px rgba(0, 0, 0, 0.3));
    box-shadow: 0rpx 8rpx 16rpx 0rpx rgba(0,0,0,0.3);
    .font-item{
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 88rpx;
      padding:0 32rpx;
      background-color: #fff;
      font-weight: 600;
      color:#2D2F38;
      span{
        display: inline-block;
        line-height: 40rpx;
      }
      .img{
        display: flex;
        width: 30rpx;
        height: 20rpx;
        image{
          width: 100%;
          height: 100%;
        }
      }
    }
    .active{
      background-color: #F4F6FA;
      color: #00B484;
    }
  }
}

// .my-data{
//   position: relative;
//   .my-bg {
//     width: 100%;
//     height: 372rpx;
    // background: linear-gradient( 180deg, #B5E7D9 0%, #F4F6FA 100%);
    .my-header{
      position: fixed;
      width: 100%;
      left: 0;
      top: 0;
      z-index: 999;
      overflow: hidden;
      .top-nav{
        // position: fixed;
        width: calc(100% - 56rpx);
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 40px;
        line-height: 40px;
        // z-index: 999;
        padding: 0 32rpx 0 24rpx;
        .top-nav-l{
          display: flex;
          width: 48rpx;
          height: 48rpx;
          image{
            width: 100%;
            height: 100%;
          }
        }
        .top-nav-c{
          flex: 1;
          text-align: center;
          height: 44rpx;
          font-weight: 500;
          font-size: 32rpx;
          color: #2D2F38;
          line-height: 44rpx;
          margin-right: 48rpx;
        }
      }
    }

.scrollable-content{
  // height: calc(100% - 84px);
  // height: calc(100% - 176rpx);
  position: absolute;
  // top: 84px;
  // top: 380rpx;
  // top: 370rpx;
  overflow: hidden;
  // height: calc(100% - 84px);
  // height: calc(100% - 380rpx);
  .content{
    background: #FFFFFF;
    border-radius: 16rpx;
    padding:0 24rpx;
    margin: 0rpx 32rpx 0;
    // margin: 55rpx 32rpx 0;
    // margin: 80rpx 32rpx 0;
    .versions{
      border-bottom: 1px solid #EAEBF0;
      padding:32rpx 0;
      .versions-t{
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        .versions-l{
          display: flex;
          align-items: flex-start;
          .title{
            font-weight: 600;
            font-size: 32rpx;
            color: #2D2F38;
            line-height: 44rpx;
            margin-right: 12rpx;
            max-width: 400rpx;
            // overflow: hidden;
            // white-space: nowrap;
            // text-overflow: ellipsis;
          }
          .broadcast{
            display: flex;
            align-items: center;
            justify-content: center;
            padding:8rpx 16rpx;
            background: #FFFFFF;
            border-radius: 36rpx;
            border: 1rpx solid #D9DBE0;
            font-size: 24rpx;
            // color: #00B484;
            color: #4E5569;
            flex-shrink: 0;
            .img{
              width: 18rpx;
              height: 24rpx;
              image{
                display: flex;
                width: 100%;
                height: 100%;
              }
            }
            span{
              display: inline-block;
              line-height: 34rpx;
              margin-left: 8rpx;
            }
          }
          .active{
            border-color: #00B484;
            color:#00B484;
          }
        }
        .versions-r{
          display: flex;
          width: 36rpx;
          height: 36rpx; 
          margin-top: 4rpx;
          image{
            width: 100%;
            height: 100%;
          }
        }
      }
      .versions-b{
        padding-top: 32rpx;
      }
      &:last-child{
        border-bottom: 0;
      }
    }

  }
  .look-bott{
    text-align: center;
    color: #cfcfcf;
    // padding: 32rpx;
    padding: 32rpx 32rpx 100rpx;
    height: 50rpx;
  }
}
</style>