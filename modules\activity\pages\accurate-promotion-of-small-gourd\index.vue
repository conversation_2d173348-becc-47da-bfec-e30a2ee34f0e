<template>
  <view class="accurate-promotion-box">
    <view class="accurate-promotion-bg">
        <view class="text-buttom">
          <view class="text-buttom-h1">
            你好！
          </view>
          <view class="text-buttom-info">
            欢 迎 注 册 小 葫 芦
          </view>
        </view>
    </view>
    <view class="accurate-promotion-bottom" v-if="!pageLoading">
      <view v-if="locationError" class="error-tips">
        定位授权失败，请重新开启！
      </view>
      <fillincase
        v-else
        :submit-params="submitParams"
        :disabled="!!caseCollectSubmitLogId"
        :updatecount="updatecount2"
        :ids="id"
        :height="tabBar.length > 1 ? 'calc(100vh - 48px)' : '100vh'"
        :tenantId="tenantId"
        :isAccuratePromotion='true'
        :provinceCity="provinceCity"
        ref="fillincaseRef"
      ></fillincase>
      <!-- <fillincase submitText='保存并提交' :isAccuratePromotion='true' :updatecount="updatecount2" :ids="activityid" :height="'calc(100vh - 350upx)'" @next='nextStep' ></fillincase> -->
    </view>
  </view>
</template>

<script>
  import fillincase from '@/modules/activity/questionnaire/components/fillin.vue';
  // import fillincase from './components/fillin.vue';
  import { getQueryObject } from '@/utils/index'
  export default {
    components:{
      fillincase
    },
    data(){
      return {
        updatecount2:0,
        activityid:null,
        submitParams:{},
        caseCollectSubmitLogId:null,
        tenantId:null,
        tabBar:[],
        locationError: false,
        provinceCity: [],
        pageLoading: false
      }
    },
    async onLoad(){
      const query = this.$validate.isNull(this.$Route.query) ? options : this.$Route.query
      console.log("query------------",query)
      const that = this
      // if(this.$validate.isNull(query.id)){
      //   let params = decodeURIComponent(query.scene)
      //   console.log("params:====",params)
      //   query.id= getQueryObject(params).id
      //   if (this.$validate.isNull(query.id)){
      //     that.$uniPlugin.toast('参数异常');
      //     return
      //   }
      // }
      // const query = this.$Route.query;
      this.pageLoading = true
      const positionRes = await this.getPosition().catch(() => {
        this.locationError = true
        this.pageLoading = false
      })
      if (!positionRes) {
        this.locationError = true
        this.pageLoading = false
        return
      }
      const { longitude, latitude } = positionRes || {}
      this.submitParams.longitude = longitude
      this.submitParams.latitude = latitude
      // 根据经纬度获取地址，默认填充省市
      const conversionLocationRes = await this.tencentLngConversionLocation({ longitude, latitude }).catch(() => { this.pageLoading = false })
      const { addressComponent: { city, province } = {} } = conversionLocationRes || {}
      if (city && province) {
        this.provinceCity = [province, city]
      }
      this.pageLoading = false

      if(this.$validate.isNull(query.id)){
        let params = decodeURIComponent(query.scene)
        console.log("params:====",params)
        query.id = getQueryObject(params).id
      }
      if(!this.$validate.isNull(query.i)) query.eId = query.i
      if(this.$validate.isNull(query.eId)){
        let params = decodeURIComponent(query.scene)
        console.log("params:====",params)
        query.eId = getQueryObject(params).eId || getQueryObject(params).i
      }
      if (!this.$validate.isNull(query)) {
        this.id = query.id;
        this.tenantId = query.tenantId || '1'
      }

      // 问卷地推扫码进来
      if (!this.$validate.isNull(query) && !this.$validate.isNull(query.eId)) {
        this.taskId = query.eId
        await this.dmactivityexpandconfigQueryOneTaskId(query.eId)
      }
      // let options = query;

      // // console.log('options')

      // if (options && options.id) {
      //   this.activityid = options.id;
      // }
    },
    onShow(){
      // this.id = '934446694021132289'
     if(this.activityid){
       this.$nextTick(() => {
         this.updatecount2 += 1
       })
     }
    },
    methods:{
      getPosition() {
        return new Promise(async (resolve, reject) => {
          const locationRes = await this.$ext.utility.getLocation().catch(() => {
            this.$uniPlugin.hideToast()
            // 没有权限则提示弹窗
            uni.showModal({
              title: '提示',
              content: '需要获取地理位置权限，请到小程序设置页面打开授权',
              success: async (res) => {
                if (res.confirm) {
                  // 选择弹框内授权
                  uni.openSetting({
                    success: async () => {
                      // that.getPosition()
                      let Position = await this.getPosition();
                      resolve(Position)
                    }
                  })
                } else if (res.cancel) {
                  // 选择弹框内 不授权
                  reject()
                }
              }
            })
          })
          if (locationRes) return resolve(locationRes)
        })
      },
      async tencentLngConversionLocation({ longitude, latitude }) {
        const res = await this.$api.common.tencentLngConversion({ longitude, latitude }).catch((err) => {
          return Promise.reject(err)
        })
        return Promise.resolve(res.data)
      },
      // 根据问卷扫码的id 获取活动详情
      async dmactivityexpandconfigQueryOneTaskId(eId) {
        const params = {
          taskId: eId,
          accountId: this.$common.getKeyVal('user', 'accountId', true)
        }
        const res = await this.$api.activity.dmactivityexpandconfigQueryOneTaskId(params)
        // const data = res.data
        if (this.$validate.isNull(res.data)) {
          this.$uniPlugin.toast('参数异常')
          return Promise.reject()
        }
        const { activityId, caseCollectSubmitLogId, extendUserId, taskId, userName, tenantId = '1' } = res.data
        this.activityid = activityId
        let researchObj = res.data.research
        let expandParams = {};
        if(researchObj instanceof Object && [3,6].includes(researchObj.collectionType)) {
          expandParams.issuestatus = 1
        }

        // this.$nextTick(() => {
        //   this.updatecount2 += 1
        // })
        this.caseCollectSubmitLogId = caseCollectSubmitLogId
        this.tenantId = tenantId
        console.log('tenantId---------', tenantId)
        this.$refs.fillincaseRef.setData({ id: activityId, mainId: caseCollectSubmitLogId, userName })
        this.submitParams = {
          ...this.submitParams,
          ...expandParams,
          inviterUserId: extendUserId,
          taskId: taskId
        }
      },
      // Research
      nextStep(){
        this.$navto.push('Research')
      }
    }
  }
</script>

<style lang="scss" scoped>
  .accurate-promotion-box{
    display: flex;
    flex-direction: column;
    height: 100vh;
    align-items: center;
  }
  .accurate-promotion-bg{
    width: 100%;
    height: 340upx;
    min-height: 340upx;
    background-color: $topicC;
    position: relative;

    .text-buttom{
      position: absolute;
      bottom: 30upx;
      left: 0;
      padding: 0 40upx 60upx;
    }
    .text-buttom-h1{
      font-size: 42upx;
      color: #fff;
      // line-height: 2;
    }
    .text-buttom-info{
      font-size: 42upx;
      color: #fff;
      // line-height: 2;
    }


  }
  .accurate-promotion-bottom{
    border-top-left-radius: 40upx;
    border-top-right-radius: 40upx;
    background-color: #fff;
    flex: 1;
    margin-top: -40upx;
    width: 100%;
    overflow: hidden;
    z-index: 10;
    padding: 30upx 50upx;
    box-sizing: border-box;
    overflow-y: auto;
  }
  .error-tips {
    font-size: 28rpx;
    font-weight: 500;
    color: $dangerColor;
  }
</style>
