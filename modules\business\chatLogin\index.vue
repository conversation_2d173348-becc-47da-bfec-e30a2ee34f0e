<template>
  <page>
    <view slot="content" class="main-body">
      <view class="main">
        <status-bar-height></status-bar-height>
        <view class="main-top">
          <view class="l" @tap="back()">
            <image
              class="width-height-atuo"
              :src="$static_ctx + 'image/business/icon-close-gray.png'"
            />
          </view>
        </view>
        <!-- <view class="main-logo">
          <image :src="$static_ctx + 'image/system/logo/icon-logo.png'" />
        </view> -->
        <view class="m-main">
          <view class="m-main m-main-bg" :style="{backgroundImage:'url(' + file_ctx + loginConfig.picUrl + ')'}">

            <!--#ifndef MP-WEIXIN || H5-->
              <view class="inp">
                <view class="inp-text">
                  <view class="l">
                    <image
                      class="width-height-atuo"
                      :src="$static_ctx + 'image/system/logo/icon-phone.png'"
                    />
                  </view>
                  <text class="r">账号</text>
                </view>
                <view class="inp-input">
                  <mobile
                    :config="mobileConfig"
                    :value="regForm.name"
                    @update="updatePwd"
                  />
                </view>
              </view>
              <view class="inp">
                <view class="inp-text">
                  <view class="l">
                    <image
                      class="width-height-atuo"
                      :src="$static_ctx + 'image/system/logo/icon-pwd.png'"
                    />
                  </view>
                  <text class="r">密码</text>
                </view>
                <view class="inp-input">
                  <password
                    :pwd-config="pwdConfig"
                    :value="regForm.pwd"
                    @updatePwd="updatePwd"
                  />
                </view>
              </view>
              <view class="text-btn">
                <text class="l" @tap="navTo('Password')">忘记密码</text>
                <text
                  class="r"
                  @tap="navTo('VerificationCode', { phone: regForm.name })"
                >验证码登录</text
                >
              </view>

              <view class="wechat-login">
                <button
                  :disabled="regForm.name === '' || regForm.pwd === ''"
                  type=""
                  class="b-btn"
                  :class="{
                    'b-btn-color': btnStatus,
                    'opacity-5': regForm.name === '' || regForm.pwd === '',
                  }"
                  @tap.stop="onSubmit"
                >
                  <image
                    class="width-height-atuo"
                    :src="
                      $static_ctx + 'image/system/logo/icon-login-password-btn.png'
                    "
                  />
                  {{ isLogining ? "正在登录" : "立即登录" }}
                </button>
              </view>
            <!--#endif-->
            <!--#ifdef MP-WEIXIN-->
            <view
              class="wechat-login wechat-login-wrapper"
              :class="loginConfig.picUrl ? 'wechat-login-position' : ''"
              :style="loginConfig.picUrl ? '' : 'margin-top: 300rpx;'"
            >
              <button
                v-if="fansBindRecord && fansBindRecord.phone"
                class="b-btn bg-65CB77"
                type="default"
                style="background-color: #fe7443;font-size: 32rpx;"
                @tap="loginByFansBindRecord"
              >
                {{ $validate.isNull(loginConfig) ? '手机号快捷登录' : loginConfig.title }}
              </button>
              <button
                v-else
                class="b-btn bg-65CB77"
                type="default"
                open-type="getPhoneNumber"
                @getphonenumber="getphonenumber"
                style="background-color: #fe7443;font-size: 32rpx;"
              >
                {{ $validate.isNull(loginConfig) ? '手机号快捷登录' : loginConfig.title }}
              </button>
            </view>
            <!--#endif-->
            <!--#ifdef H5-->
            <view
              class="wechat-login wechat-login-wrapper"
              :class="loginConfig.picUrl ? 'wechat-login-position' : ''"
              :style="loginConfig.picUrl ? '' : 'margin-top: 300rpx;'"
            >
              <view
                class="b-btn bg-65CB77"
                style="background-color: #fe7443;font-size: 32rpx;height:80rpx;width:520rpx;"
              >
                <wx-open-launch-weapp
                  style="width:520rpx; height:80rpx;"
                  id="launch-btn"
                  :appid="$appId"
                  :path="`modules/business/chat/index`"
                >
                  <script type="text/wxtag-template">
                    <style>
                      .jump-wx-btn {
                        color: #fff;
                        width: 100%;
                        height: 100vh;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                      }
                    </style>
                    <div class="jump-wx-btn">{{ $validate.isNull(loginConfig) ? '去咨询' : loginConfig.title }}</div>
                  </script>
                </wx-open-launch-weapp>
              </view>
            </view>
            <!--#endif-->
          </view>

          <!--#ifndef MP-WEIXIN || H5-->
          <!-- <view class="registered" @tap="navTo('Register')">
            <view class="view-but">注 册</view>
          </view> -->
          <!--#endif-->

        </view>
        <!-- #ifndef H5 -->
        <agree-with-protocol ref="checkboxRef" @returnFn = "returnFn"></agree-with-protocol>
        <!-- #endif -->
      </view>
    </view>
  </page>
</template>

<script>
import AgreeWithProtocol from '@/modules/business/agreement-gather/components/agree-with-protocol'
import password from '@/modules/business/components/basics/form/password'
import mobile from '@/modules/business/components/basics/form/mobile'
import { mapState } from 'vuex'
// #ifdef H5
import wxOpenAuth from '@/mixins/wx-open-auth'
// #endif
export default {
  // #ifdef H5
  mixins: [wxOpenAuth],
  // #endif
  components: {
    password,
    mobile,
    AgreeWithProtocol
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $static_ctx: this.$static_ctx,
      $appId: this.$appId,
      file_ctx: this.file_ctx,
      isLogining: false,
      mobileConfig: {
        name: 'name',
        placeholder: '请输入手机号码'
      },
      pwdConfig: {
        name: 'pwd',
        isShowPWD: true,
        placeholder: '请输入密码 (6-20位)'
      },
      regForm: {
        name: '',
        pwd: '',
        isCheckbox: false
      },
      btnStatus: false,
      redirect: '',
      redirectParams: {},
      formPage: '',
      formPageParams: {},
      userInfoData: {},
      getphonenumberData: {},
      loginConfig: {},
      fansBindRecord: null
    }
  },
  computed: {
    ...mapState('user', {
      isLogin: state => state.isLogin
    }),
    isConfirm() {
      let flag = true
      for (const k in this.regForm) {
        if (k && this.regForm.hasOwnProperty(k)) {
          if (!this.regForm[k]) {
            flag = false
            break
          }
        }
      }
      return flag
    }
  },
  watch: {
    /** 密码去首尾空监听 */
    'regForm.pwd'() {
      this.regForm.pwd = this.$validate.trim(this.regForm.pwd)
    }
  },
  methods: {
    // 获取登录页配置
    getQueryconfig () {
        this.$api.chat.loginconfigQueryConfig().then(res => {
          if (res.data.configStatus === 1) {
            this.loginConfig = res.data
          }
        })
    },
    returnFn(isCheckbox){
      this.regForm.isCheckbox = isCheckbox
    },
    getUserInfo() {
      let params = {}
      const getphonenumberData = this.getphonenumberData
      const that = this
      uni.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          console.log(res)
          params = Object.assign(params, res)
          params.rawData = encodeURI(params.rawData)
          delete (params.userInfo)
          delete (params.errMsg)
          delete (params.cloudID)
          that.userInfoData = params
          that.getPhone()
        },
        fail: (error) => { }
      })
    },
    async loginByFansBindRecord() {
      await this.$refs.checkboxRef.isCheckboxToastFn()
      const { phone } = this.fansBindRecord
      this.quickLogin(phone)
    },
    async getphonenumber(e) {
      console.log(e);
      await this.$refs.checkboxRef.isCheckboxToastFn()
      const that = this
      if (e.detail.errMsg === 'getPhoneNumber:ok') {
        const params = {
          code: e.detail.code,
          cacheNumber: that.$common.getTokenUuid(), // 随机编码
        }
        this.getPhone(params)
      } else {
        that.setLoginStatus(false)
        that.$uniPlugin.toast('快捷登录已被取消')
      }
    },
    getPhone(params) {
      const that = this
      that.setLoginStatus(true)
      that.$ext.wechat.getPhone(params).then(res => {
        console.log(res)
        that.quickLogin(res.data.phoneNumber)
      }).catch(err => {
        that.setLoginStatus(false)
        that.$uniPlugin.toast(err.msg || '快捷登录出错了，请重试')
      })
    },
    quickLogin(params) {
      const that = this
      if (params) {
        that.setLoginStatus(true)
        that.$ext.wechat.quickLogin({ phone: params,cacheNumber: that.$common.getTokenUuid()}).then(res => {
          that.setLoginStatus(false)
          that.$uniPlugin.toast('登录成功')
          // debugger
          that.$ext.user.getInfoGroupPage(that, () => {
            that.setLoginStatus(false)
            // #ifdef MP-WEIXIN
            that.$ext.user.bindWeixinAccount({})
            that.$ext.user.usertenantrecordBindFans()
          // #endif
          })
          that.$ext.webSocket.webSocketInit()
        }).catch(err => {
          that.setLoginStatus(false)
          that.$uniPlugin.toast(err.msg || '快捷登录出错了，请重试')
        })
      } else {
        that.setLoginStatus(false)
        that.$uniPlugin.toast('快捷登录出错了，请重试')
      }


    },
    /**
     * 子组件返回 密码更新
     */
    updatePwd(obj) {
      if (obj) {
        for (const i in obj) {
          this.regForm[i] = obj[i]
        }
      }
    },
    /**
     * 立即登录
     */
    async onSubmit() {
      await this.$refs.checkboxRef.isCheckboxToastFn()
      if (this.isLogining) return
      const that = this
      const tipArr = {
        name: '请输入账号',
        pwd: '请输入密码(6-20位)'
      }
      const params = {
        name: this.regForm.name, // 登陆账户名或手机号
        pwd: this.regForm.pwd // 密码
      }
      // 表单验证
      if (!this.$common.validationForm(tipArr, params)) {
        return
      }
      const obj = this.$validate.checkPWD(this.regForm.pwd)
      if (!obj.statu) return
      that.setLoginStatus(true) // 设置登录状态
      that.$uniPlugin.loading('正在登录中')
      that.$ext.user.login(params.name, params.pwd, this.$constant.noun.logType).then(result => {
        that.$uniPlugin.hideLoading()
        that.$uniPlugin.toast('登录成功')
        that.$ext.user.getInfoGroupPage(that, () => {
          that.setLoginStatus(false)
          // #ifdef MP-WEIXIN
            that.$ext.user.bindWeixinAccount({})
            that.$ext.user.usertenantrecordBindFans()
          // #endif
          that.$ext.webSocket.webSocketInit()
        })
      }).catch(e => {
        this.$uniPlugin.hideLoading()
        // this.$uniPlugin.toast(e.message)
        that.setLoginStatus(false)
      })
    },
    /**
     * 设置登录状态
     */
    setLoginStatus(flag) {
      this.isLogining = flag
    },
    navTo(name, paramObj) {
      this.$navto.push(name, paramObj)
    },
    back() {
    //   const path = this.formPage || 'Index'
    //   let queryParams = {}
    //   if (!this.$validate.isNull(this.formPageParams)) {
    //     queryParams = JSON.parse(decodeURIComponent(this.formPageParams))
    //   }
    //   this.$navto.replaceAll(path, queryParams)
        this.$navto.replaceAll('Index')
    }
  },
  async onLoad () {
    this.getQueryconfig()
    // #ifdef H5
    this.wxh5ShareInit() // mixins
    // #endif

    const fansBindRecord = await this.$ext.user.getFansBindRecord()
    this.fansBindRecord = fansBindRecord
  },
  onReady() {
    const redirect = this.$Route.query.redirect
    const redirectParams = this.$Route.query.redirectParams
    const formPage = this.$Route.query.formPage
    const formPageParams = this.$Route.query.formPageParams
    if (!this.$validate.isNull(redirect)) {
      this.redirect = redirect
    }
    if (!this.$validate.isNull(redirectParams)) {
      this.redirectParams = redirectParams
    }
    if (!this.$validate.isNull(formPage)) {
      this.formPage = formPage
    }
    if (!this.$validate.isNull(formPageParams)) {
      this.formPageParams = formPageParams
    }
  }
}
</script>

<style lang="scss" scoped>
uni-input {
  height: 80upx !important;
  font-size: 32upx !important;
}
uni-button:after {
  border: none !important;
}
.opacity-5 {
  @include opacity(0.5);
}
.bg-65CB77 {
  // background: linear-gradient(90deg, #91e09f, #39c752) !important;
}
.main-body {
    height: 100%;
  height: 100%;
  .main {
    background: #fff;
    height: 100%;
    display: flex;
    flex-direction: column;
    .main-top {
      height: 88upx;
      padding: 0 30upx;
      box-sizing: border-box;
      line-height: 88upx;
      .l {
        display: inline-block;
        vertical-align: middle;
        width: 48upx;
        height: 48upx;
      }
    }
    .main-logo {
      //padding: 30upx;
      padding-top: 100upx;
      image {
        width: 340upx;
        height: 150upx;
        margin: 0 auto;
        display: block;
      }
    }
    .m-main {
      flex: 1;
      width: 100%;
      &-bg {
        position: relative;
        height: calc(100% - 180rpx);
        background-size: cover;
        background-repeat: no-repeat;
      }
      .inp {
        width: 520upx;
        margin: 30upx auto 0 auto;
        .inp-text {
          height: 48upx;
          margin-bottom: 14upx;
          .l {
            width: 48upx;
            height: 48upx;
            margin-right: 16upx;
            display: inline-block;
            vertical-align: middle;
          }
          .r {
            color: #333;
            font-size: 32upx;
            height: 48upx;
            line-height: 48upx;
            display: inline-block;
            vertical-align: middle;
          }
        }
      }
      .text-btn {
        width: 520upx;
        margin: 20upx auto 0 auto;
        position: relative;
        height: 30upx;
        .l {
          position: absolute;
          left: 0;
          top: 0;
          color: $topicC;
          font-size: 28upx;
        }
        .r {
          position: absolute;
          right: 0;
          top: 0;
          color: $topicC;
          font-size: 28upx;
        }
      }
    }
  }
}
.m-t-20 {
  margin-top: 20upx;
}
.margin-0-auto {
  margin: 0 auto;
}
.width-height-atuo {
  width: 100%;
  height: 100%;
}
.container {
  background-color: #fff;
  height: calc(100vh - 1upx);
}

.content {
  background: #fff;
  height: calc(100vh - 1upx);
}
.b-btn {
  width: 520upx;
  //background: linear-gradient(
  //  90deg,
  //  rgba(255, 185, 36, 1),
  //  rgba(254, 219, 55, 1)
  //);
  background: $topicC;
  color: #fff;
  @include rounded(44upx);
}

.b-btn-color {
  //background: linear-gradient(
  //  90deg,
  //  rgba(255, 185, 36, 1),
  //  rgba(254, 219, 55, 1)
  //);
  background: $topicC;
  @include opacity(0.5);
  color: #fff;
  @include rounded(44upx);
}
.label-input-pwd,
.label-input {
  width: 100%;
  height: 48upx;

  text {
    padding-left: 54upx;
    display: inline-block;
    background-size: 48upx;
  }
}
.label-input-pwd {
  text {
    background-size: 48upx;
  }
}
.wechat-login {
  // margin-top: 48upx;
  button {
    image {
      width: 24px;
      height: 24px;
      margin-right: 8px;
      display: inline-block;
      vertical-align: middle;
    }
  }
}
.registered {
  width: 120upx;
  margin: 32upx auto 0;
  .view-but {
    font-size: 30upx;
    line-height: 54upx;
    color: #b1b0af;
    text-align: center;
  }
}
// .wechat-login-wrapper {
//   margin-top: 250upx;
// }
.wechat-login + .wechat-login-wrapper {
  margin-top: 48upx;
}
.wechat-login-position {
  position: absolute;
  bottom: 48upx;
  left: 50%;
  transform: translateX(-50%);
}
</style>
