<template>
  <view class="circle-app" v-if="list.length > 0">
    <swiper class="swiper-box" @change="change"
        :style="list.length > 5 ? 'height: 340rpx !important;' : 'height: 200rpx !important;'"
        :indicator-dots="swiperConfig.indicatorDots"
        :indicator-color="swiperConfig.indicatorColor"
        :indicator-active-color="swiperConfig.indicatorActiveColor"
        :autoplay="swiperConfig.autoplay"
    >
      <swiper-item v-for="(appList, appListIndex) in bannerApplicationList" :key="appListIndex">
        <view class="app-main">
          <view class="app-item" v-for="(item, index) in appList" :key="item.id"
            @tap="clickApp(item)"
            :class="{
              'box-right': (index + 1)%5 === 0,
              'box-bottom': (index + 1) > 5
            }"
          >
          <image class="app-logo" mode="aspectFit" :src="item.logoPath ? file_ctx + item.logoPath : defaultAvatar"></image>
          <text class="app-name">{{ item.name }}</text>
          </view>
        </view>
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
export default {
    props: {
      list: {
        type: Array,
        default: () => []
      }
    },
    data () {
        return {
            file_ctx: this.file_ctx,
            defaultAvatar: this.$static_ctx + 'image/system/avatar/icon-default-avatar.png',
            current: 0,
            swiperConfig:{
                //是否显示面板指示点
                indicatorDots:true,
                //	指示点颜色
                indicatorColor:'#EEEEEE',
                // 当前选中的指示点颜色
                indicatorActiveColor:'#00D29D',
                autoplay:false
            },
        }
    },
    computed: {
        bannerApplicationList () {
            let n = 10 // 每个banner显示的个数
            let len = this.list.length
            let lineNum = len%n === 0 ? len/n : Math.floor((len/n) + 1)
            let res = []
            for (let i = 0; i < lineNum; i++) {
                let temp = this.list.slice(i*n, i*n+n)
                res.push(temp)
            }
            return res
        }
    },
    methods: {
        change(e) {
            this.current = e.detail.current
        },
        clickApp (data) {
            const {type,path,appId,name} = data
            switch (type) {
                // 小程序
                case 1:
                    this.$uniPlugin.navigateToMiniProgram({
                        appId,
                        path,
                        envVersion: 'release',
                        extraData: {}
                    }, (res) => {
                        console.log(res)
                    }, (err) => {
                        console.log(err)
                    })
                break
                // H5
                case 2:
                    this.$navto.push('WebHtmlView', { src: path, title: name })
                break
                default:
            }
        }
    }
}
</script>

<style scoped lang="scss">
.circle-app {
  padding: 30upx 24upx 0;
  margin-top: 24upx;
  background: #FFFFFF;
  box-sizing: border-box;
  @include rounded(20upx 20upx 0upx 0upx);
  .app-main {
    display: flex;
    flex-wrap: wrap;
    box-sizing: border-box;
  }
  .app-item {
    width: 20%;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
  }
  .app-logo {
    display: block;
    margin-bottom: 14upx;
    width: 78upx;
    height: 78upx;
    background: #FFFFFF;
    box-shadow: 1upx 4upx 7upx 0upx rgba(205,205,205,0.33);
    @include rounded(50%);
  }
  .app-name {
    font-size: 27upx;
    font-weight: 500;
    color: #282828;
    line-height: 47upx;
    display: inline-block;
    vertical-align: middle;
    @include ellipsis(1);
  }
}
.swiper-box {
    width: 100%;
    height: 340rpx !important;
}
.box-right {
  margin-right: 0;
}
.box-bottom {
  margin-top: 12upx;
}
</style>
