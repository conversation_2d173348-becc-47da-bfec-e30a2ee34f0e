.icon-home {
  width: 64rpx;
  height: 64rpx;
}
.nav-bar-title {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 32rpx;
  color: #1D2029;
  line-height: 44rpx;
}
.content {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: #F4F6FA;
  box-sizing: border-box;
  .content-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 750rpx;
    height: 698rpx;
    background: linear-gradient(180deg, #D2F3EE 0%, #ADEDE4 70%, #F4F6FA 100%);
    z-index: 0;
  }
  .content-cell {
    position: relative;
    height: 100%;
    box-sizing: border-box;
    margin-top: 42rpx;
    .banner {
      width: 702rpx;
      border-radius: 16rpx;
      overflow: hidden;
      display: block;
      margin: 0 auto 32rpx;
      .banner-img {
        width: 100%;
      }
    }
    .device-poster {
      position: relative;
      display: block;
      width: 638rpx;
      height: 222rpx;
      margin: 0 auto 42rpx;
    }
    .content-center {
      width: 702rpx;
      margin: 0 auto;
      border-radius: 16rpx;
      background-color: #fff;
      display: flex;
      align-items: center;
      flex-direction: column;
      position: relative;
      // overflow: hidden;
      padding-bottom: 64rpx;
      .whiteTop {
        width: 542rpx;
        height: 282rpx;
        margin: 24rpx auto 100rpx;
      }
      .cell {
        box-sizing: border-box;
        width: 100%;
        height: 100%;
        padding: 60rpx 40rpx 24rpx;
        .top {
          &-title {
            font-weight: 500;
            font-size: 36rpx;
            color: #1D2029;
            line-height: 50rpx;
            margin-bottom: 64rpx;
          }
          &-desc {
            color: #333;
            font-size: 18px;
            letter-spacing: 1px;
          }
        }
        .centerCell {
          display: flex;
          align-items: center;
          justify-content: center;
          background: #fff;
          border-radius: 10px;
          box-sizing: border-box;
          margin-bottom: 86rpx;
          .imgCell {
            flex: 1;
            position: relative;
            box-sizing: border-box;
            background: #F4F6FA;
            border-radius: 16rpx;
            border: 2px solid transparent;
            .van-image {
              display: block;
            }
            &+.imgCell {
              margin-left: 64rpx;
            }
            &.active {
              border: 2px solid #00B484;
              background: #E2FDF6;
            }
          }
        }
        .chooses-btn {
          background: #00B484;
          border-color: #00B484;
          font-weight: 600;
          font-size: 40rpx;
          color: #FFFFFF;
          line-height: 56rpx;
          width: 100%;
          height: 128rpx;
        }
      }

      .centerC {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
      }
      .repair {
        margin: 164rpx auto 0;
        width: 306rpx;
        height: 352rpx;
      }
      .scanH5QrCodeText {
        font-size: 28rpx;
        color: #1D2029;
        line-height: 40rpx;
        margin-top: 32rpx;
      }

      .content-code {
        position: relative;
        .tips {
          font-size: 20px;
          color: #383838;
          font-weight: 800;
          margin-bottom: 2vh;
          &.tips-box {
            font-size: 16px;
            font-weight: 550;
            color: #2acbb4;
            background-color: #edfcf9;
            border-radius: 12px;
            padding: 2px 10px;
            margin-bottom: 58px;
          }
          .tips-text.arrows {
            padding: 0 12px;
          }
        }

        .code {
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center;
          position: relative;

          .code-img {
            position: absolute;
            .hand {
              width: 50%;
              position: absolute;
              right: -60%;
              bottom: -43%;
            }
          }
        }
      }

      .button-list {
        width: 100%;
        position: relative;
        padding: 0 40rpx;
        box-sizing: border-box;
        // min-height: calc(70px + 4vh);
        .button {
          position: relative;
          width: 622rpx;
          height: 128rpx;
          background-color: #00B484;
          border-radius: 64rpx;
          font-weight: 600;
          font-size: 40rpx;
          color: #FFFFFF;
          line-height: 128rpx;
          text-align: center;
          border: none;
          &::after {
            content: none;
            border: none;
          }
          .tag-box {
            position: absolute;
            left: 0;
            top: 0;
            background-size: cover;
            background-repeat: no-repeat;
            background-position: 0 0;
          }
          .free-box {
            width: 154rpx;
            height: 72rpx;
            padding: 4rpx 0 0 38rpx;
            box-sizing: border-box;
            font-weight: 600;
            font-size: 34rpx;
            color: #FFFFFF;
            line-height: 48rpx;
            text-align: left;
          }
          .pay-box {
            width: 122rpx;
            height: 72rpx;
            padding: 8rpx 0 0 28rpx;
            box-sizing: border-box;
            font-weight: 600;
            font-size: 28rpx;
            color: #FFFFFF;
            line-height: 40rpx;
            text-align: left;
          }
          &+.button {
            margin-top: 48rpx;
          }
          &::before {
            content: none;
          }
        }
        .pay-btn {
          background: #E1F6F1;
          border-radius: 64rpx;
          border: 2rpx solid #98DAC8;
          color: #00B484;
        }
        .hand {
          width: 250rpx;
          height: 200rpx;
          position: absolute;
          right: -32rpx;
          top: 100rpx;
        }
      }
    }
    .content-bottom {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 112rpx 0 8rpx;
      padding-bottom: calc(constant(safe-area-inset-bottom) + 8rpx);//兼容 IOS<11.2
      padding-bottom: calc(env(safe-area-inset-bottom) + 8rpx);//兼容 IOS>11.2
      .logo {
        width: 200rpx;
        height: 24rpx;
        margin-bottom: 16rpx;
      }
      .tips-text {
        font-weight: 400;
        font-size: 26rpx;
        color: #868C9C;
        line-height: 36rpx;
      }
    }
  }
  .videoPop {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .close {
      margin-top: 5vh;
    }
  }
}
.guide-box {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  .guide-text {
    font-size: 32rpx;
    color: #868C9C;
    line-height: 44rpx;
  }
  .icon-guide-bottom {
    width: 24rpx;
    height: 24rpx;
    margin-left: 4rpx;
  }
}
.logo-img {
  width: 100%;
  height: 50rpx;
}