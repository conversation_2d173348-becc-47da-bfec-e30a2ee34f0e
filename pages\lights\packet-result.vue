<template>
  <view class="content">
    <view class="top">
      <image :src="background" class="success" mode="aspectFit"></image>
      <view class="success-tips">
        感谢对环保的支持，出袋指令已下发，请留意手机出袋结果通知！
      </view>
    </view>

    <view class="tips">
      <view class="title">*如机器未出袋,请联系客服</view>
      <view class="call">
        <text>客服电话：</text>
        <text @tap="handleTel('************')">************</text>
      </view>
    </view>
  </view>
</template>
<script>
import env from '@/config/env'
import { getQueryStr } from '@/utils/index'
import router from '@/router'

// import { alipayGetPacketRecordReceiveStatus, attentionOutPacketByIpInfo } from '@/utils/api/index'


export default {
  data() {
    return {
      background: env.file_ctx + '/h5/home/<USER>/out-bag-success.png',
      percent: 0,
      tips: '出袋中...',
      showProgress: false,
      wxInfo: null,
      packetId: null,
      requestNum: 0,
      requestTime: 5000,
      time1: null,
      timer: null,
      userId: null,
      deviceId: null,
      advertisePlanInfo: {}, // 广告计划详情
      deviceInfo: null,
      interstitialAd: null
    }
  },
  beforeDestroy() {
    this.percent = 0
    clearInterval(this.time1)
    this.time1 = null
    clearInterval(this.timer)
    this.timer = null
  },
  methods: {
    // 支付宝全屏/插屏广告
    getAlipayAdvertiseBusiness() {
      return new Promise((resolve, reject) => {
        if (this.$validate.isNull(this.deviceInfo)) return resolve(false)
        const { gdtAdSwitch = 2 } = this.deviceInfo
        if (gdtAdSwitch === 2) return resolve(false)
        if (my.canIUse('createInterstitialAd')) {
          this.interstitialAd = my.createInterstitialAd({
            adUnitId: '50_2025061825002053195' // adUnitId为 广告媒体管理平台 广告位信息 的spaceCode
          })
          this.interstitialAd.load().then(() => {
            this.interstitialAd.show().catch((err) => {
              console.log('插屏/全屏广告显示失败', err)
            })
          }).catch((err) => {
            console.log('插屏/全屏广告加载失败', err)
          })
          this.interstitialAd.onClose((res) => {
            // 广告关闭事件触发
            console.log(res)
          })
        }

        return resolve(true)
      })
    },
    /**
     * 获取设备信息
     */
    async getDeviceInfo() {
      const that = this
      if (!that.deviceId || that.deviceId === 'null') return
      const res = await that.$api.packet.getDeviceInfoByDeviceId({ deviceId: that.deviceId }).catch((err) => { console.log('devideInfo err---------', err) })
      that.deviceInfo = res.data
    },
    handleTel(val) {
      uni.makePhoneCall({
        phoneNumber: val
      })
    },
    start() {
      this.alipayGetPacketRecordReceiveStatus()
      this.showProgress = true
      clearInterval(this.time1)
      this.time1 = setInterval(() => {
        if (this.percent < 90) {
          this.percent += 1
        } else {
          clearInterval(this.time1)
        }
      }, 100)

    },
    alipayGetPacketRecordReceiveStatus() {
      const packetId = this.packetId
      if (this.requestNum >= 22) {
        this.tips = '对不起，出袋失败，给您造成不便，非常抱歉！如需重新取袋，请重新扫码！'
        return
      } else {
        this.requestNum += 1
      }
      this.$api.lights.getPacketRecordReceiveStatus({ packetId }).then(res => {
        // if(this.$validate.isNull(res.data)) {
        //   this.tips = '出袋中'
        //   setTimeout(() => {
        //     this.alipayGetPacketRecordReceiveStatus()
        //   }, this.requestTime)
        //   return
        // }
        const { receiveStatus = 3 } = res.data
        switch (receiveStatus) {
          case 1:
            this.requestNum = 0
            this.percent = 100
            this.tips = '出袋完成，请轻拉取袋，感谢对环保的支持'
            break;
          case 2:
            this.requestNum = 0
            this.percent = 100
            this.tips = '对不起，出袋失败，给您造成不便，非常抱歉！如需重新取袋，请重新扫码！！'
            break;
          case 3:
            this.tips = '出袋中'
            this.timer = setTimeout(() => {
              this.alipayGetPacketRecordReceiveStatus()
            }, this.requestTime)
            break;
          case 4:
            this.requestNum = 0
            this.percent = 100
            this.tips = '对不起，出袋失败，给您造成不便，非常抱歉！如需重新取袋，请重新扫码！'

            break;
          case 5:
            this.requestNum = 0
            this.percent = 100
            this.tips = '对不起，出袋失败，给您造成不便，非常抱歉！如需重新取袋，请重新扫码！'

            break;

          default:
            break;
        }

      })
    },
    async getAdvertisePlan() {
      const res = await this.$api.packet.getAdvertisePlan({ accountId: this.userId, deviceId: this.deviceId })
      this.advertisePlanInfo = res.data
    },
    async finishJump() {
      // #ifndef MP-ALIPAY
      return
      // #endif

      // #ifdef H5
      this.ua = navigator.userAgent.toLowerCase()
      // #endif
      const paramsData = this.advertisePlanInfo
      console.log('paramsData----------', paramsData)
      if (!paramsData) return
      let { clientType, freeLink, alipayFreeLink, delaySecond = 2, posterPath = '', appId = '', id = '' } = paramsData || {}
      // clientType 1-H5 2-自营小程序 3-第三方小程序
      // #ifdef MP-ALIPAY
      if (!alipayFreeLink) return
      let link = alipayFreeLink || '/pages/index/index'
      // #endif
      // #ifndef MP-ALIPAY
      let link = freeLink || '/pages/index/index'
      // #endif

      switch (clientType) {
        // H5
        case 1:
          break
        // 自营小程序
        case 2:
          break
        // 第三方小程序
        case 3:
          link = `/modules/common/app-jump-view/index?appId=${appId}&posterPath=${encodeURIComponent(posterPath)}&path=${encodeURIComponent(freeLink)}`
          break
        // 接口链接
        case 4:
          const targetUrlRes = await this.$api.packet.parseTargetUrl({ id })
          if (!targetUrlRes.data) return
          link = targetUrlRes.data
          break
        default:
      }

      setTimeout(() => {
        this.navtoGo(link)
      }, delaySecond * 1000)
    },
    navtoGo(url, obj = {}) {
      // #ifdef H5
      if (this.ua.indexOf('miniprogram') === -1) {
        wx.miniProgram.reLaunch({
          url
        })
      } else {
        window.location.href = checkHttp(url)
      }
      // #endif

      // #ifdef MP-ALIPAY
      //这个是固定的
      // let link = `https://render.alipay.com/p/s/i/?scheme=${encodeURIComponent('alipays://platformapi/startapp?appId=20000067%26url=')}${encodeURIComponent(url)}`
      let link = url
      console.log('link------', link)
      //使用支付宝小程序再带跳转方式
      my.ap.openURL({
        url: link,
        fail: err => {
          console.log('err-------', err)
        }
      })
      // #endif

      // #ifndef H5 || MP-ALIPAY
      router.replaceAll(url)
      // #endif
    },
  },
  async onLoad() {
    // this.getWxInfo()
    const { deviceId, freeTicket, gbUserId, packetId, userId } = this.$Route.query
    this.packetId = packetId
    this.deviceId = deviceId
    this.userId = userId
    if (this.packetId == 0) {
      this.requestNum = 0
      this.percent = 100
      this.tips = '对不起，出袋失败，给您造成不便，非常抱歉！如需重新取袋，请重新扫码！！'
      return
    }

    // #ifdef MP-ALIPAY
    await this.getAdvertisePlan()
    if (this.$validate.isNull(this.advertisePlanInfo)) {
      await this.getDeviceInfo()
      this.getAlipayAdvertiseBusiness()
      return
    }
    if (!this.$validate.isNull(this.advertisePlanInfo) || this.advertisePlanInfo.alipayFreeLink) {
      this.finishJump()
      return
    }
    // #endif

    this.start()
  }
}
</script>
<style lang="scss" scoped>
.content {
  .top {
    width: 180vw;
    height: 850rpx;
    left: -40vw;
    border-radius: 0 0 50% 50%;
    background: linear-gradient(180deg, #19daa6 0%, #00a579 100%);
    box-shadow: 0px -1px 0px 0px #009970, 0px 1px 0px 0px #009a83;
    position: fixed;
    display: flex;
    align-items: center;
    flex-direction: column;
    .success {
      width: 240rpx;
      height: 240rpx;
      margin: 180rpx 0 40rpx;
    }
    .success-tips {
      font-weight: 400;
      font-size: 32rpx;
      color: #ffffff;
      line-height: 44rpx;
      text-align: center;
      width: 544rpx;
    }
  }
  .tips {
    width: 100vw;
    position: fixed;
    bottom: 10vh;
    bottom: calc(20px + env(safe-area-inset-bottom));
    text-align: center;

    .title {
      font-weight: 400;
      font-size: 13px;
      color: #868c9c;
      line-height: 18px;
    }
    .call {
      font-weight: 400;
      font-size: 13px;
      color: #00b484;
      line-height: 18px;
      margin-top: 4rpx;
    }
  }
}
</style>
