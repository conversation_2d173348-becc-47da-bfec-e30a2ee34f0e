import request from '@/common/util/request'
import env from '@/config/env'

/**
 * 系统请求工具类，
 * 注意：API接口只做请求数据和格式化数据的事情，其他事务一概不处理
 */
export default {
  // 分销商档案-新增（申请）
  distributionapplyInsert(param) {
    const url = env.ctx + 'dm/api/v1/distributionapply/insert'
    return request.postJson(url, param)
  },
  // 分销商档案-新增（申请）
  distributionapplyUpdate(param) {
    const url = env.ctx + 'dm/api/v1/distributionapply/update'
    return request.putJson(url, param)
  },
  // 分销商档案-查询申请分销记录
  distributionapplyUserApplyLog(param) {
    const url = env.ctx + `dm/api/v1/distributionapply/userApplyLog/${param.userId}`
    return request.get(url, param)
  },
  // 门店下的分销员
  physicianinfoGetStoreMembers(param) {
    const url = env.ctx + `dm/api/v1/physicianinfo/get/store/members`
    return request.postForm(url, param)
  },
  // 解绑门店店员
  physicianinfoBatchUnbindMembers(param) {
    const url = env.ctx + `dm/api/v1/physicianinfo/batch/unbind/members`
    return request.postForm(url, param)
  },
  // 获取所有下级分销员
  physicianinfoGetLowerMembers(param) {
    const url = env.ctx + `dm/api/v1/physicianinfo/get/lower/members`
    return request.postForm(url, param)
  },
  // 批量解绑下级分销员
  physicianinfoBatchUnbindLowerMembers(param) {
    const url = env.ctx + `dm/api/v1/physicianinfo/batch/unbind/lower/members`
    return request.postForm(url, param)
  },
  // 分销商档案-单一查询
  physicianinfoQueryOne(param) {
    const url = env.ctx + `dm/api/v1/physicianinfo/query/one`
    return request.get(url, param)
  },
  // 分销记录-分页
  distributionrecordQueryPage(param) {
    const url = env.ctx + `dm/api/v1/distributionrecord/query/mySalesRecord/page`
    return request.postJson(url, param)
  },
  // 分销记录 收益总计
  distributionrecordQueryMySalesRecordSum(param) {
    const url = env.ctx + `dm/api/v1/distributionrecord/query/mySalesRecord/sum`
    return request.postJson(url, param)
  },
  // 根据店长userId获取店铺统计
  distributionrecordStoreStatistics(param) {
    const url = env.ctx + `dm/api/v1/distributionrecord/storeStatistics`
    return request.postForm(url, param)
  },
  // 根据userId获取分销统计
  distributionrecordMyStatistics(param) {
    const url = env.ctx + `dm/api/v1/distributionrecord/myStatistics`
    return request.postForm(url, param)
  },
  // 邀请成为店员或下级
  physicianinfoInviteStaff(param) {
    const url = env.ctx + `dm/api/v1/physicianinfo/invite/staff`
    return request.postForm(url, param)
  },
  // 根据pid单一查询
  physicianinfoQueryOneByPid(param) {
    const url = env.ctx + `dm/api/v1/physicianinfo/query/oneByPid`
    return request.get(url, param)
  },
  // 分销类型list
  distributiontypeQueryList(param) {
    const url = env.ctx + `dm/api/v1/distributiontype/query/list`
    return request.get(url, param)
  },
  // 自定义分销模板-单一查询
  customdistributiontemplateQueryOne(param) {
    const url = env.ctx + `dm/api/v1/customdistributiontemplate/query/one`
    return request.get(url, param)
  },
  // 自定义分销模板-更新模板
  customdistributiontemplateUpdate(param) {
    const url = env.ctx + `dm/api/v1/customdistributiontemplate/update`
    return request.putJson(url, param)
  },
  // 分销员（接收方）创建
  accompanydistributorInsert(param) {
    const url = env.ctx + `dm/api/v1/accompanydistributor/insert`
    return request.postJson(url, param)
  },
  // 根据用户id查询待审核或失败的分销员创建记录
  accompanydistributorQueryHistoryOne(param) {
    const url = env.ctx + `dm/api/v1/accompanydistributor/query/historyOne`
    return request.get(url, param)
  },
  // 文件上传接口
  uploadFile(param) {
    const url = env.ctx + `basics/api/v1/merchantfile/uploadFile`
    return request.postJson(url, param)
  },
  // 配置信息查询
  queryConfig(param) {
    const url = env.ctx + `dm/api/v1/accompanyconfig/queryConfig`
    return request.get(url, param)
  },
  // 对私查询cardbin信息
  queryCardBinInfo(param) {
    const url = env.ctx + `basics/api/v1/merchantfile/cardBinInfo`
    return request.get(url, param)
  },
  // 根据用户id查询分销员
  accompanydistributorQueryOneByUserId(param) {
    const url = env.ctx + `dm/api/v1/accompanydistributor/query/oneByUserId`
    return request.get(url, param)
  },
  // 分销客户分页查询
  accompanycustomerQueryPage(param) {
    const url = env.ctx + `dm/api/v1/accompanycustomer/query/page`
    return request.postJson(url, param)
  },
  // 客户扫码绑定分销员
  accompanydistributorcustomerInsert(param) {
    const url = env.ctx + `dm/api/v1/accompanydistributorcustomer/insert`
    return request.postJson(url, param)
  },
  // 分销流水分页查询
  accompanydistriburecordQueryPage(param) {
    const url = env.ctx + `dm/api/v1/accompanydistriburecord/query/page`
    return request.postJson(url, param)
  },
  // 分销流水金额查询
  accompanydistriburecordQueryAmount(param) {
    const url = env.ctx + `dm/api/v1/accompanydistriburecord/query/amount`
    return request.postJson(url, param)
  },
  // 提现分页查询
  accompanypayoutQueryPage(param) {
    const url = env.ctx + `dm/api/v1/accompanypayout/query/page`
    return request.postJson(url, param)
  },
  // 分销中心统计数据
  accompanydistributorStatistic(param) {
    const url = env.ctx + `dm/api/v1/accompanydistributor/statistic`
    return request.get(url, param)
  }
}
