import request from '@/common/util/request'
import env from '@/config/env'

/**
 * 系统请求工具类，
 * 注意：API接口只做请求数据和格式化数据的事情，其他事务一概不处理
 */
export default {
  /**
   * 选择人员列表
   * @param resolve
   * @param reject
   */
  userQueryList(param) {
    const url = env.ctx + 'dm/api/v1/common/staffUser/query/list'
    return request.get(url, param)
  },
  /**
   * 获取当前用户专员档案
   * @param resolve
   * @param reject
   */
  queryOneAttache(param) {
    const url = env.ctx + 'dm/api/v1/common/queryOneAttache'
    return request.get(url, param)
  },
  /**
   * 获取当前用户患者档案
   * @param resolve
   * @param reject
   */
  queryOnePatient(param) {
    const url = env.ctx + 'dm/api/v1/common/queryOnePatient'
    return request.get(url, param)
  },
  /**
   * 获取当前用户医师档案
   * @param resolve
   * @param reject
   */
  queryOnePhysician(param) {
    const url = env.ctx + 'dm/api/v1/common/queryOnePhysician'
    return request.get(url, param)
  },
}
