.tab-bar {
  bottom: 0;
  left: 0;
  right: 0;
  height: 56px;
  background: white;
  display: flex;
  flex-direction: row;
  padding-bottom: env(safe-area-inset-bottom);
}

.tab-bar-border {
  background-color: #e4e4e4;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 1px;
  transform: scaleY(0.5);
}

.tab-bar-item {
  position: relative;
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.tab-bar-item image {
  width: 64rpx;
  height: 64rpx;
}

.tab-bar-item view {
  font-size: 20rpx;
}

.tab-bar-item .big-icon {
  width: 112rpx;
  height: 72rpx;
}

.tab-bar-item .badge {
  position: absolute;
  top: 2px;
  left: 50%;
  background-color: #f43530;
  color: #fff;
  width: auto;
  height: 16px;
  line-height: 16px;
  border-radius: 16px;
  min-width: 16px;
  padding: 0 2px;
  font-size: 12px;
  text-align: center;
  white-space: nowrap;
}
