<template>
  <view>
    <view v-if="showVideoFlag">
      <view  class="controlCenter" v-if="!showControl" :style="{display:commentShow ? '' : 'none'}">
        <!-- 播放与暂停按钮 -->
         <view class="openOrPause" @click.stop="()=>$emit('openOrPause')">
          <image :src="isOpen ? pauseIcon : openIcon" mode="aspectFill"></image>
         </view>
        <!-- 进度条 -->
        <view class="progress">
          <view>{{convertSecondsToTime(currentTime)}}</view>
          <view class="bar">
            <!-- 填充 -->
            <view class="fill" :style="{width:`${currentPer}%`}"></view>
            <!-- 滑动球 -->
            <view class="ballBox" @touchstart.stop="openDrag" @touchmove.stop="moveDrag" @touchend.stop="closeDrag" :style="{left:`${currentPer}%`}">
              <view class="ball"></view>
            </view>
          </view>
          <view>{{convertSecondsToTime(FullLengthVideo)}}</view>
        </view>
        <!-- 播放与暂停按钮 -->
         <view class="openOrPause" @click.stop="()=>$emit('fullScreen')">
          <image :src="fullScreen" mode="aspectFill"></image>
         </view>
      </view>
      <!-- 隐藏时显示的隐式进度条 -->
      <view class="controlCenter" style="padding: 0;" v-if="!showControl" :style="{display:commentShow ? 'none' : ''}">
        <!-- 进度条 -->
        <view class="progress">
          <view class="bar" style="width: 100vw;margin: 0;">
            <!-- 填充 -->
            <view class="fill" :style="{width:`${currentPer}%`}"></view>
          </view>
        </view>
      </view>
      <!-- 拖动时显示当前秒数 -->
      <view v-if="openDragFlag" class="showCurrentTime">
        {{convertSecondsToTime(currentTime)}}
      </view>
    </view>
    <!-- 控制面板 控制视频倍速 -->
    <view class="controlPanels" v-if="showControlPanels">
      <view class="mask" @click='closeControlPanels'></view>

      <view class="controlPanel" :class="{show: showControlPanels}">
        <!-- 标题 -->
        <view class="titleHeader">
          <text>{{controlTitle}}</text>
        </view>
        <!-- 设置选项 -->
        <view class="emitContent" v-if="controlType === 'emit'">
          <view class="emitBox" @click="changeEmit(item.type)" v-for="(item,index) in getEmitBtnMap" :key="index">
            <image class="emitIcon" :src="item.icon"></image>
            <view>{{item.title}}</view>
          </view>
        </view>
        <!-- 加速选项 -->
        <view class="speedContent" v-if="controlType === 'scpeed'">
          <view class="controlPanelItem" :class='{currentSpeed:currentSpeed === item}' @click='currentSpeed = item' v-for="(item,index) in speedMap" :key="index" >
            {{item}}
          </view>
        </view>
        <!-- 悬浮选项 -->
        <view class="suspendContent" v-if="controlType === 'suspend'">
          <view class="">
            <view class="suspendTitle">悬浮窗播放</view>
            <view class="suspendClue">退出页面后，自动打开悬浮窗</view>
          </view>
          <customerSwitch v-model="isOpenSuspend" @change="selectSuspend"></customerSwitch>
        </view>
        <!-- 确认按钮 -->
        <view class="buttonBox" :class="{clear:buttonContent === '取消'}">
          <view class="confirmBtn"  @click='confirm'>
            <text>{{buttonContent}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import customerSwitch from "./switch.vue";
  export default{
    components:{
      customerSwitch
    },
    props:{
      ControlTimeMap:{
        default:()=>[],
        type:Array
      },
      isOpen:{
        default:true,
        type:Boolean
      },
      showVideoFlag:{
        default:true,
        type:Boolean
      },
      showControl:{
        default:false,
        type:Boolean
      },
      commentShow:{
        default:true,
        type:Boolean
      },
    },
    computed:{
      getEmitBtnMap(){
        return this.emitBtnMap.filter(item=>{
          if(item.isLivePlayerShow) return true;
          return this.showVideoFlag;
        })
      }
    },
    data(){
      return {
        isOpenSuspend:false,
        speedMap:[0.5,0.8,1.0,1.25,1.5,2],
        showControlPanels:false,
        currentTime:0,// 当前时间
        FullLengthVideo:1, // 视频全长
        touchStartX: 0, // 触摸开始时的X坐标
        barWidth:0,
        currentPer:0, //当前进度条百分比
        openDragFlag:false,
        openIcon:this.$static_ctx + "image/business/hulu-v2/open.png",
        pauseIcon:this.$static_ctx + "image/business/hulu-v2/pause.png",
        fullScreen:this.$static_ctx + "image/business/hulu-v2/fullScreen.png",
        speedIcon:this.$static_ctx + "image/business/hulu-v2/speed-icon.png",
        currentSpeed:1, // 当前倍速
        lastcurrentSpeed:1,
        controlTitle:'',
        controlType:'',
        controlTitleMap:{
          'emit':{title:'更多设置',buttonContent:'取消'},
          'scpeed':{title:'倍速',buttonContent:'确定'},
          'suspend':{title:'悬浮窗设置',buttonContent:'确定'}
        },
        buttonContent:'取消',
        emitBtnMap:[
          {title:'倍速',icon:this.$static_ctx + "image/business/hulu-v2/scpeed-green.png",type:'scpeed',isLivePlayerShow:false},
          {title:'悬浮',icon:this.$static_ctx + "image/business/hulu-v2/suspend-green.png",type:'suspend',isLivePlayerShow:true},
        ]
      }
    },
    watch:{
      ControlTimeMap:{
        handler(){
          if(this.openDragFlag) return
          this.currentTime =  this.ControlTimeMap[0];
          this.FullLengthVideo = this.ControlTimeMap[1];
          this.currentPer = this.currentTime / this.FullLengthVideo * 100;
        },
        deep: true, // 深度监听
        immediate: true  // 第一次改变就执行
      },
    },
    mounted() {
      const query = uni.createSelectorQuery().in(this)
      this.getEl(query.select('.bar')).then(bar=>{
        this.barWidth = bar?.width;
      })
      let mediaPictureMode = uni.getStorageSync('mediaPictureMode');
			if(mediaPictureMode === '') {
				mediaPictureMode = 'pop';
				uni.setStorageSync('mediaPictureMode',mediaPictureMode)
			}
      console.log('控制界面mediaPictureMode',mediaPictureMode);

      this.isOpenSuspend = mediaPictureMode === 'pop';
    },
    methods:{
      // 修改控制标题
      changeTitle(type){
        let {title,buttonContent} = this.controlTitleMap[type];
        this.controlTitle = title;
        this.controlType = type;
        this.buttonContent = buttonContent;
      },
      // 修改设置
      changeEmit(type){
        this.changeTitle(type);
      },
      confirm(){
        this.controlType === 'scpeed' && this.selectSpeed()
        this.controlType === 'suspend' && this.setSuspend();
        this.showControlPanels = false;
      },
      selectSpeed(){
        this.lastcurrentSpeed = this.currentSpeed;
        this.$emit('changeSpeed',this.currentSpeed);
      },
      setSuspend(){
        this.$emit('changeSuspend',this.isOpenSuspend);
      },
      // 开启控制面板
      openControlPanels(type){
        this.changeTitle(type);
        this.showControlPanels = true;
      },
      closeControlPanels(){
        this.controlType ==='scpeed' && (this.currentSpeed = this.lastcurrentSpeed);
        this.showControlPanels = false;
      },
      ended(){
        this.$nextTick(()=>{
          this.currentTime = 0;
          this.currentPer = 0;
          console.log('播放结束',this.currentPer);
        })
      },
      // 开启拖拽
      openDrag(event){
        this.openDragFlag = true;
        console.log('开启拖拽',event.touches[0]);
        this.touchStartX = event.touches[0].clientX;
      },
      // 移动拖拽
      moveDrag(event){
        let newX = event.touches[0].clientX - this.touchStartX;
        this.touchStartX = event.touches[0].clientX;
        let newPer = this.currentPer + newX / this.barWidth * 100;
        newPer >= 100 ? newPer = 100 : newPer;
        newPer <= 0 ? newPer = 0 : newPer;
        // 计算得出当前的百分比
        this.currentPer = newPer;
        // 计算得出当前的时间秒数
        this.currentTime = this.currentPer * this.FullLengthVideo / 100;
      },
      // 关闭拖拽
      closeDrag(){
        this.openDragFlag = false;
        this.$emit('week',this.currentTime);
      },
      // 时分秒转换
      convertSecondsToTime(seconds=0) {
        // 计算小时、分钟和秒
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        // 格式化输出
        const formattedHours = ('0' + hours).slice(-2);
        const formattedMinutes = ('0' + minutes).slice(-2);
        const formattedSeconds = ('0' + parseInt(secs)).slice(-2);
        // 返回格式化后的字符串
        if(hours) return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
        return `${formattedMinutes}:${formattedSeconds}`
      }
    }
  }
</script>

<style lang="scss" scoped>
  .openOrPause{
    height: 48rpx;
    width: 48rpx;
    image{
      width: 100%;
      height: 100%;
    }
  }
  .showCurrentTime{
    position: fixed;
    top: 50%;
    left: 50%;
    z-index: 9999;
    transform: translate(-50%,-50%);
    background-color: rgba(0,0,0,.5);
    color: white;
    text-align: center;
    padding: 20rpx;
  }
  .controlCenter{
    display: flex;
    padding: 0 28rpx;
    box-sizing: border-box;
    width: 100vw;
    position: fixed;
    bottom: 68rpx;
    z-index: 99999999999999;
    left: 50%;
    transform: translateX(-50%);
    color: white;
  }
  .progress{
    margin: 0 auto;
    display: flex;
    align-items: center;
    font-size: 11rpx;
    .bar{
      background-color: hsla(0,0%,100%,.2);
      height: 4rpx;
      position: relative;
      width: 384rpx;
      margin: 0 12rpx;
      .fill{
        background-color: white;
        height: 4rpx;
      }
      .ballBox{
        width: 50rpx;
        height: 50rpx;
        position: absolute;
        top: -25rpx;
        margin-left: -14rpx;
      }
      .ball{
        background-color: #fff;
        border-radius: 50%;
        height: 14rpx;
        padding: 8rpx;
        width: 14rpx;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%);
      }
    }
  }
  .controlPanels{
    align-items: center;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 99999999999999;
    width: 100vw;
    height: 100vh;
    // 遮罩层
    .mask{
      width: 100vw;
      height: 100vh;
      background-color: rgba(0,0,0,.5);
      position: absolute;
      top: 0;
      left: 0;
    }

    .controlPanel{
      width: 100vw;
      height: 484rpx;
      background: #FFFFFF;
      border-radius: 24rpx 24rpx 0rpx 0rpx;
      background-color: #fff;
      position: absolute;
      bottom: 0;
      left: 0;
      z-index: 10;
      /* 添加动画效果 */
      transform: translateY(100%); /* 初始状态：隐藏在底部 */
        .titleHeader{
          margin-top: 32rpx;
          width: 100%;
          height: 40rpx;
          display: flex;
          align-items: center;     /* 垂直居中 */
          justify-content: center; /* 水平居中 */
          font-weight: bold;
          font-size: 36rpx;
          color: #333333;
          margin-bottom: 40rpx;
          .speedIcon{
            width: 40rpx;
            height: 40rpx;
            margin-right: 12rpx;
          }
        }
        .emitContent{
          width: 100%;
          display: flex;
          height: 128rpx;
          column-gap: 68rpx;
          padding: 0 44rpx;
          box-sizing: border-box;
          .emitBox{
            height: 128rpx;
            font-weight: 500;
            font-size: 28rpx;
            color: #333333;
            text-align: center;
            .emitIcon{
              width: 76rpx;
              height: 76rpx;
              margin-bottom: 12rpx;
            }
          }
        }
        .speedContent{
          width: 100%;
          padding: 0 24rpx;
          box-sizing: border-box;
          height: 88rpx;
          display: flex;
          align-items: center;     /* 垂直居中 */
          font-weight: 500;
          font-size: 28rpx;
          color: #777777;
          margin-top: 0;
          margin-bottom: 128rpx;
          .currentSpeed{
            color:#00B484;
          }
          .controlPanelItem{
            height: 88rpx;
            line-height: 88rpx;
            width:calc(100% / 6);
            text-align: center;
          }
        }
        .suspendContent{
          width: 100%;
          height: 92rpx;
          display: flex;
          align-items: center;     /* 垂直居中 */
          justify-content: space-between;
          padding: 0 24rpx 0 32rpx;
          box-sizing: border-box;
          .suspendTitle{
            font-weight: 500;
            font-size: 32rpx;
            color: #333333;
          }
          .suspendClue{
            margin-top: 8rpx;
            font-weight: 500;
            font-size: 28rpx;
            color: #777777;
          }
        }
        .buttonBox{
          height: 112rpx;
          width: 100%;
          border-top: 1rpx solid #E6E6E6;
          position: absolute;
          bottom: 68rpx;
          .confirmBtn{
            position: absolute;
            bottom: 0rpx;
            left: 50%;
            transform: translateX(-50%);
            font-weight: 500;
            font-size: 32rpx;
            color: #FFFFFF;
            width: 598rpx;
            height: 88rpx;
            text-align: center;
            line-height: 88rpx;
            background: #00B484;
            border-radius: 44rpx 44rpx 44rpx 44rpx;
          }
        }
        .clear{
          border-top: none !important;
          .confirmBtn{
            background: #F4F6FA;
            font-weight: 500;
            font-size: 32rpx;
            color: #777777;
          }
        }
    }
    /* 显示状态的类 */
    .controlPanel.show {
      transform: translateY(0); /* 显示状态：正常位置 */
    }
  }
</style>
