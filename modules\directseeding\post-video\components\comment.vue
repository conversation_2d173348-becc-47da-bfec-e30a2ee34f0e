<template>
<scroll-view scroll-y :show-scrollbar="false" style="height: 100%;">
  <view class="hb-comment">
    <!-- 阅读数-start -->
    <!-- 阅读数下边那条线-end -->
    <!-- 评论主体-start -->
    <view style="height: 100%;" class="comment-list" v-if="commentData.comment.length != 0">
        <!-- 评论主体-顶部数量及发表评论按钮-start -->
        <view class="comment-num">
          <view>共 {{commentData.commentSize}} 条评论</view>
          <view class="add-btn">
            <button type="primary" size="mini" @click="addComment">发表评论</button>
          </view>
        </view>
        <!-- <view style="height: 100%;"> -->
          <!-- 评论主体-顶部数量及发表评论按钮-end -->
          <!-- 评论列表-start -->
          <view class="comment-box" :key="item.id" v-for="(item, index) in commentData.comment">
              <view class="comment-box-item">
                <view @tap="userClick(item)">
                  <image v-if="item.businessType == 10 && !item.userId"
                    :src="file_ctx + (item.headPath ||'0/msg-reply/807939727497555969.jpg')" mode="aspectFill" class="avatar">
                  </image>
                  <image v-else-if="item.businessType == 10 && item.userId"
                    :src="item.headPath.includes(file_ctx) ? item.headPath : file_ctx + item.headPath" mode="aspectFill"
                    class="avatar" @error="handleError(item)">
                  </image>
                  <image v-else :src="item.avatarUrl || emptyAvatar" mode="aspectFill" class="avatar"></image>
                </view>
                <view class="comment-main">
                  <!-- 父评论体-start -->
                  <view class="comment-main-top">
                    <view class="nick-name-box" @tap="userClick(item)">
                      <template>
                        <view class="nick-name nick-name-width-1" v-if="item.businessType == 10 && !item.userId">葫芦大夫</view>
                        <view class="nick-name nick-name-width-1" v-else>{{item.nickName}}<span v-if="item.doctorPost"> ·
                          </span>{{item.doctorPost}}</view>
                      </template>
                      <view class="nick-hospitcal" v-if="item.businessType == 10 && item.userId">
                        <view v-if="item.hospital">{{item.hospital}}<span> · </span>{{item.deptName}}</view>
                      </view>

                    </view>
                    <view class="zan-box" @click="like(item)">
                      <span :class="item.hasLike ? 'isLike' : 'notLike'">{{item.likeNum == 0 ? '抢首赞' : item.likeNum}}</span>
                      <img style="width: 14px; height: 14px;" v-if="!item.hasLike"
                        src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAApVJREFUWEfFlz9oFEEUxr93SWEhFkIaBUljo4WgATEaiI0GwVLBQg2EzLd3hSBqLI2lRgWLY+ftSSQgggoWNv5pImLUwjQBg1goqCCIIBqwueSeTLg7Ln9Mcmdub7vdnZnvN9+8fe+toMWXtFgfDQGMjo52zM7OHgMwIyLTg4ODk41upG4A7/0OEbkPYGdFVEQuOOeuNQJRN4CqBvGw+3dm9kZEBsrCPSRf1gvRCIAFETPrjqLodZIkY2Z2SkQmnHMHUgMgOQ8fx3FvJpMZB/CDZEfqACEgi8XidwA/SW5uKkCN2AzJTYscmCTZ1VQA7/2AiNwSkefOuYNBLEmSnJnlATwgebypAKr6CMBRANdJng9i3vu8iORE5Mrc3NyTlQDM7HMul/tYO2bNX0GhUDhRKpXumtlvAPuiKJouOzBuZr117HyBU6sCqOpeAH0AhssiwyQvVwTjOO5va2s7vRaAGtCrJC+GOVUAVb1UTjDVDLd4URHJOuf8WsSWG5MkSbeZTYR3lc+4CpAkyUpWht3fI/m+UfEwL5/Pb2xvb59ZFkBV5zNchazsSBBeYPn/ABQKhZ5SqfQCwBTJXYuPoOkAqnoGwE0Ad0ieTB3Ae39bRPoBDJEcSR1AVd8C2CMifc65p6kCmJkkSfIHwAYAW0h+SxVAVXcDmDSzL1EUbasEc20eaGoQVuoIgMckj6QOoKo3AJwFMEJyqBUAIegOAegnOdYKgK8AtmYyma7aLjqVGPDebxeRDwB+AeggWUzVAVV1ABTAK5L7l+0HVPUTgM7Q7VQGhPJZe99oHagpw4dJPvsXQPixONeoyErzRGTKzB7W9hFLjiA8iOO4M7iw3hDZbLbq6pIeY73F6l1v1Zas3gXrHf8XhhNvMGSmtPYAAAAASUVORK5CYII=" />
                      <img style="width: 14px; height: 14px;" v-else
                        src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAfVJREFUWEfFl79LHEEUx7/vclr4g51TLFQQW9FNDFhFBDvjtcF0gXSp5eYuWJlUAW9NIP9BIFUEOxFtTKF2FrezKFgkkEBS3h4SAtHbJ6vu5W5d1F3cm20WZt+b7+e9mTfzlqD5Ic36SATw+OPJQP2ft+ABJwQ+VFIcJA0kEYBpVfcAehKIMqHoFISVBCI2wOS76mi9g76HxTLwZiqybzcuRGwAc7WWB/NGWIhBu440ZlIHmFitLRPzm+tC9EtJY1gjAP9VMtelEQC2kuJR6gCm5X4BsBAWImDdluJZqgBjH/4MZr3TYzB6IoRWmHnzJgDK0g+1KL4128SqgnD9x432yn5NSfE88L0VYPx9rS/jYR6EJTCPJxQNu60oKV77gw2Ai/Ly1/ZKRElx8c20XL4n0ZZpgvkbAKbl7gCYDax0ALREmjJAo2SbM9A+AKLPqmC8aNkD4bVOMwPEKNlFUdYGkAE9rUhjSxvA2YOOoaPF7t+6AH4qKUauHURt3AObSoq8NgAGyo4UJW0ABH5py9wnbQAAppq76HYfRLXOXmPg4BWdasoA7yuZm47sB0zL9Vvt0TQvI/bqc06pfzsS4KHlWgwUUgKwmWjdKRhvI1q5/0P+T8dZ9jILTjH31X9PlKuNKzppXxDMFeV/a0eUVPSuftoBzgHKR/ohZwAugwAAAABJRU5ErkJggg==" />
                    </view>
                  </view>
                  <view class="comment-main-content">
                    <template v-if="item.status !== -1">
                      <view v-if="item.imagePath">
                        <image @click="previewImage(item.imagePath.split(','), eIndex)"
                          v-for="(e, eIndex) in item.imagePath.split(',')" :key="eIndex" :src="file_ctx + e" mode="widthFix"
                          class="content-img"></image>
                      </view>

                      <!-- 评论内容 -->
                      <!-- <view class="charInfo mgL20 left clearFix"> -->
                      <block v-for="item2,index2 in item.parsedComment" :key="index2">
                        <text style="word-break: break-all;" v-if="item2.type === 1">{{item2.content}}</text>
                        <view v-if="item2.type === 2" style="display: inline-block;height:55rpx;width:55rpx;overflow:hidden;vertical-align: middle;">
                          <view :class="item2.imageClass" :style="{
                                'background-image':'url(' + emojiSource + ')',
                                'transform': 'scale(' + lineHeight / 64 + ')',
                              }" style="transform-origin: 0 0">
                          </view>
                        </view>
                      </block>
                    </template>

                    <view class="foot-btn" v-else>该评论已被删除</view>
                  </view>
                  <view class="foot-source" v-if="item.dataSource">来源<span>{{ item.dataSource }}</span></view>
                  <view class="comment-main-foot">
                    <!-- <view class="foot-time">{{item.createTime | getTimeStringAutoShort2}} <text class="tip">来自{{ item.province && item.province != '' ? item.province : '未知'}}</text></view> -->
                    <view class="foot-time">
                      {{item.putawayTime | getTimeStringAutoShort2}}
                      <text v-if="item.businessType !== 10"
                        class="tip">来自{{ item.province && item.province != '' ? item.province : '未知'}}</text>
                    </view>
                    <template v-if="item.status !== -1">
                      <view class="foot-btn" @click="reply(item.nickName,item.nickName,item.id, item)">回复</view>
                      <view class="foot-btn" v-if="item.owner" @click="confirmDelete(item)">删除</view>
                    </template>
                  </view>
                  <!-- 父评论体-end -->
                  <!-- 子评论列表-start -->
                  <view class="comment-sub-box">
                    <view class="comment-sub-item" v-for="each in item.children">
                      <view @tap="userClick(each)">
                        <image :src="each.avatarUrl || emptyAvatar" mode="aspectFill" class="avatar">
                        </image>
                      </view>
                      <view class="comment-main">
                        <view class="sub-comment-main-top">
                          <view class="mutual-comment-box">
                            <view class="nick-name nick-name-width-2" @tap="userClick(each)">{{each.nickName}}</view>
                            <template v-if="each.mutualNickName">
                              <view class="reply-text"></view>
                              <view class="nick-name nick-name-width-2" @tap="userClick({accountId: each.mutualAccountId})">
                                {{ each.mutualNickName }}</view>
                            </template>
                          </view>
                          <view class="zan-box" @click="like(each)">
                            <span
                              :class="each.hasLike ? 'isLike' : 'notLike'">{{each.likeNum == 0 ? '抢首赞' : each.likeNum}}</span>
                            <img style="width: 14px; height: 14px;" v-if="!each.hasLike"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAApVJREFUWEfFlz9oFEEUxr93SWEhFkIaBUljo4WgATEaiI0GwVLBQg2EzLd3hSBqLI2lRgWLY+ftSSQgggoWNv5pImLUwjQBg1goqCCIIBqwueSeTLg7Ln9Mcmdub7vdnZnvN9+8fe+toMWXtFgfDQGMjo52zM7OHgMwIyLTg4ODk41upG4A7/0OEbkPYGdFVEQuOOeuNQJRN4CqBvGw+3dm9kZEBsrCPSRf1gvRCIAFETPrjqLodZIkY2Z2SkQmnHMHUgMgOQ8fx3FvJpMZB/CDZEfqACEgi8XidwA/SW5uKkCN2AzJTYscmCTZ1VQA7/2AiNwSkefOuYNBLEmSnJnlATwgebypAKr6CMBRANdJng9i3vu8iORE5Mrc3NyTlQDM7HMul/tYO2bNX0GhUDhRKpXumtlvAPuiKJouOzBuZr117HyBU6sCqOpeAH0AhssiwyQvVwTjOO5va2s7vRaAGtCrJC+GOVUAVb1UTjDVDLd4URHJOuf8WsSWG5MkSbeZTYR3lc+4CpAkyUpWht3fI/m+UfEwL5/Pb2xvb59ZFkBV5zNchazsSBBeYPn/ABQKhZ5SqfQCwBTJXYuPoOkAqnoGwE0Ad0ieTB3Ae39bRPoBDJEcSR1AVd8C2CMifc65p6kCmJkkSfIHwAYAW0h+SxVAVXcDmDSzL1EUbasEc20eaGoQVuoIgMckj6QOoKo3AJwFMEJyqBUAIegOAegnOdYKgK8AtmYyma7aLjqVGPDebxeRDwB+AeggWUzVAVV1ABTAK5L7l+0HVPUTgM7Q7VQGhPJZe99oHagpw4dJPvsXQPixONeoyErzRGTKzB7W9hFLjiA8iOO4M7iw3hDZbLbq6pIeY73F6l1v1Zas3gXrHf8XhhNvMGSmtPYAAAAASUVORK5CYII=" />
                            <img style="width: 14px; height: 14px;" v-else
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAfVJREFUWEfFl79LHEEUx7/vclr4g51TLFQQW9FNDFhFBDvjtcF0gXSp5eYuWJlUAW9NIP9BIFUEOxFtTKF2FrezKFgkkEBS3h4SAtHbJ6vu5W5d1F3cm20WZt+b7+e9mTfzlqD5Ic36SATw+OPJQP2ft+ABJwQ+VFIcJA0kEYBpVfcAehKIMqHoFISVBCI2wOS76mi9g76HxTLwZiqybzcuRGwAc7WWB/NGWIhBu440ZlIHmFitLRPzm+tC9EtJY1gjAP9VMtelEQC2kuJR6gCm5X4BsBAWImDdluJZqgBjH/4MZr3TYzB6IoRWmHnzJgDK0g+1KL4128SqgnD9x432yn5NSfE88L0VYPx9rS/jYR6EJTCPJxQNu60oKV77gw2Ai/Ly1/ZKRElx8c20XL4n0ZZpgvkbAKbl7gCYDax0ALREmjJAo2SbM9A+AKLPqmC8aNkD4bVOMwPEKNlFUdYGkAE9rUhjSxvA2YOOoaPF7t+6AH4qKUauHURt3AObSoq8NgAGyo4UJW0ABH5py9wnbQAAppq76HYfRLXOXmPg4BWdasoA7yuZm47sB0zL9Vvt0TQvI/bqc06pfzsS4KHlWgwUUgKwmWjdKRhvI1q5/0P+T8dZ9jILTjH31X9PlKuNKzppXxDMFeV/a0eUVPSuftoBzgHKR/ohZwAugwAAAABJRU5ErkJggg==" />
                          </view>
                        </view>
                        <view class="comment-main-content">
                          <view v-if="each.imagePath">
                            <image @click="previewImage(each.imagePath.split(','), eIndex)"
                              v-for="(e, eIndex) in each.imagePath.split(',')" :key="eIndex" :src="file_ctx + e"
                              mode="widthFix" class="content-img"></image>
                          </view>
                          <block v-for="item2,index2 in each.parsedComment" :key="index2">
                            <text style="word-break: break-all;" v-if="item2.type === 1">{{item2.content}}</text>
                            <view v-if="item2.type === 2"
                              style="display: inline-block;height:55rpx;width:55rpx;overflow:hidden;vertical-align: middle;">
                              <view :class="item2.imageClass" :style="{
                                  'background-image':'url(' + emojiSource + ')',
                                  'transform': 'scale(' + lineHeight / 64 + ')',
                                }" style="transform-origin: 0 0">
                              </view>
                            </view>
                          </block>
                        </view>
                        <view class="comment-main-foot">
                          <view class="foot-time">
                            {{each.createTime | getTimeStringAutoShort2}}
                            <text class="tip"
                              v-if="item.businessType !== 10">来自{{ each.province && each.province != '' ? each.province : '未知'}}</text>
                          </view>
                          <template v-if="each.status !== -1">
                            <view class="foot-btn" @click="reply(item.nickName,each.nickName,item.id, each)">
                              回复</view>
                            <view class="foot-btn" v-if="each.owner" @click="confirmDelete(each)">删除
                            </view>
                          </template>
                          <view class="foot-btn" v-if="each.owner && each.status === -1">该评论已被删除</view>
                        </view>
                      </view>
                    </view>
                    <view class="comment-sub-more" v-if="item.moreLoading">
                      <text class="comment-sub-more-text">正在加载评论</text>
                      <uni-icons color="#8B8B8B" type="spinner-cycle" />
                    </view>
                    <view class="comment-sub-more" v-else-if="item.isHasMore" @tap="openComment(item)">
                      <text class="comment-sub-more-text"
                        v-if="$validate.isNull(item.children)">展开{{ item.mutualNumber }}条回复</text>
                      <text class="comment-sub-more-text" v-else>展开更多回复</text>
                      <uni-icons color="#8B8B8B" type="bottom" />
                    </view>
                  </view>
                  <!-- 子评论列表-end -->
                </view>
              </view>
          </view>
        <!-- </view> -->
        <view class="more" v-if="moreLoading">
          <text class="more-text">正在加载评论</text>
          <uni-icons color="#00D29D" type="spinner-cycle" />
        </view>
        <view class="more" v-else-if="commentData.isHasMore" @tap="$emit('loadMore')">
          <text class="more-text">查看更多评论</text>
          <uni-icons color="#00D29D" type="bottom" />
        </view>
        <template v-else>
          <nui-nomore defaultStyle="margin: 0 auto 24rpx;" backgroundColor="#fff" text="没有更多评论"></nui-nomore>
        </template>
        <!-- 评论列表-end -->
    </view>
    <!-- 评论主体-end -->
    <!-- 评论loading-start -->
    <view class="comment-loading" v-else-if="commentData.loading">
      <text class="comment-loading-text">正在加载评论</text>
      <uni-icons color="#8B8B8B" type="spinner-cycle" />
    </view>
    <!-- 评论loading-end -->
    <!-- 无评论-start -->
    <view class="comment-none" v-else>
      暂无评论，<span @click="addComment" style="color: #007AFF;">抢沙发</span>
    </view>
    <!-- 无评论-end -->
    <!-- 新增评论-start -->
    <view class="comment-submit-box" :style="{
      'display': submit ? 'flex' : 'none'
    }" @click="closeInput">
      <!-- 下边的click.stop.prevent用于让上边的click不传下去，以防点到下边的空白处触发closeInput方法 -->
      <view class="comment-add" @click.stop.prevent="stopPrevent" :style="'bottom:' + KeyboardHeight + 'px'">
        <textarea class="textarea" v-model="commentReq.content" :placeholder="placeholder ? placeholder : '回复楼主'"
          :adjust-position="false" :show-confirm-bar="false" @blur="blur" @focus="focusOn" :focus="focus"
          @input="onInput" maxlength="250"></textarea>

        <template v-if="!$validate.isNull(sendImgages)">
          <view v-for="(e, eIndex) in sendImgages.map(item => item.dir)" :key="eIndex" class="send-img-box">
            <view class="send-img-remove" @click.stop="sendImgages = []"></view>
            <image @click="previewImage(sendImgages, eIndex)" :src="file_ctx + e" mode="aspectFill" class="send-img">
            </image>
          </view>
        </template>
        <view class="comment-add-bottom">
          <view class="d-t">
            <uni-icons type="image-filled" :size="24" @tap="clickSendImg" />
            <image :src='emojiURL' class="emoji" @click="showEmoji"></image>
          </view>
          <title-img disabled ref="sendImgRef" :config="config.img" @uploadFinish="sendImg"></title-img>
          <view class="comment-add-bottom-right">
            <button @click="handleRelease" class="release-btn" type="primary" size="mini"
              :disabled="!commentReq.content && $validate.isNull(sendImgages)">发布</button>
          </view>
        </view>
        <!-- 表情包的引用 -->
        <view class="reply_panel_wrp" :style="{height: emojiShow ? 300 + 'px' : 200 + 'px'}"
          :hidden="!emojiShow && !functionShow">
          <view class="reply_panel" :class="[emojiShow ? 'show': '']" :hidden="!emojiShow">
            <nui-emoji ref="emojiRef" :source="emojiSource" class="mp-emoji" @insertemoji="insertEmoji"
              @delemoji="deleteEmoji" @send="onsend" :padding='0'></nui-emoji>
          </view>
        </view>
      </view>
    </view>
    <!-- 新增评论-end -->
  </view>
  </scroll-view>
</template>

<script>
  import timePlugin from '@/common/util/time-plugin'
  import nuiNomore from '@/modules/directseeding/components/nui-nomore/nui-nomore.vue'
  import UniIcons from '@/components/uni/uni-icons/uni-icons.vue';
  import TimeUtils from '@/common/util/websocket/timeUtils.js'
  import TitleImg from "@/components/business/module/title-img/index.vue"
  import common from '@/common/util/main'
  import nuiEmoji from '@/modules/directseeding/components/nui-emoji/nui-emoji.vue'

  export default {
    name: 'hb-comment',
    components: {
      nuiNomore,
      UniIcons,
      TitleImg,
      nuiEmoji
    },
    props: {
      cmData: {
        type: Object,
        default: () => {
          return null;
        }
      },
      deleteTip: {
        type: String,
        default: () => {
          return '操作不可逆，如果评论下有子评论，也将被一并删除，确认？';
        }
      },
      moreLoading: {
        type: Boolean,
        default: false
      },
      visible: {
        type: Boolean,
        default: false
      },
      intro: {
        type: String,
        default: ''
      },
      isShowBtn:{
        type:Boolean,
        default:true
      }
    },
    watch: {
      cmData: {
        handler: function(newVal, oldVal) {
          this.init(newVal);
        },
        immediate: true
      },
      replyTarget: {
        handler: function() {
          this.clearInput()
          this.$emit('changeVal', {
            target: this.replyTarget,
            content: this.commentReq.content,
            imagePath: this.sendImgages
          })
        },
        deep: true
      },
      sendImgages: {
        handler: function() {
          this.$emit('changeVal', {
            target: this.replyTarget,
            content: this.commentReq.content,
            imagePath: this.sendImgages
          })
        },
        deep: true
      },
      'commentReq.content': {
        handler: function() {
          this.$emit('changeVal', {
            target: this.replyTarget,
            content: this.commentReq.content,
            imagePath: this.sendImgages
          })
        },
        immediate: true
      },
      visible: {
        handler() {
          if (this.visible) {
            uni.onKeyboardHeightChange(this.keyboardHeightChangeListener)
          } else {
            uni.offKeyboardHeightChange(this.keyboardHeightChangeListener)
          }
        }
      }
    },
    data() {
      return {
        // 表情业务逻辑
        titleHeight: "45",
        statusHeight: 0,
        // 评论输入框
        keyboardHeight: 0,
        lineHeight: 60,
        functionShow: false,
        emojiShow: false,
        comment: '',
        focus: false,
        cursor: 0,
        _keyboardShow: false,
        emojiSource: this.$static_ctx + 'image/system/bg-face.png',
        parsedComment: [{
            type: 2,
            content: "[憨笑]",
            imageClass: "smiley_28"
          },
          {
            type: 1,
            content: "44"
          },
          {
            type: 2,
            content: "[呲牙]",
            imageClass: "smiley_13"
          },
          {
            type: 2,
            content: "[调皮]",
            imageClass: "smiley_12"
          }
        ],
        // $static_ctx: this.$static_ctx,
        emojiURL: this.$static_ctx + 'image/system/icon-face.png',


        // -------------------------------------
        "emptyAvatar": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAABF5JREFUWEfFl11oHFUUx//nbmKwxdJ9qFL7YPEhRJC2gljjF60WG0WsoNkWfSnZ7Jy7FVPF0AoKpmBFqyIG3Jk7G1iIQXHbBz+KbbGtRfBbMe2LseKjiVDoiqIhwZ0jV3fb3cnM7kQCuU+7M+ec/+9+nHPPEJZ50DLrY1EApVJp9fz8/BYRuZ2INgDYWJvAWRE5R0RnZmZmPh4ZGZlPOrFEAMVi8e4gCPYSUZ+IXGGFgiCYIaJpKyQi1yql1orIFgAXARxRSvm5XO67diBtAYwxRQCDAE4RUUkpdWxwcNCKLBiu665TSj0kIpqIbgTgMzO3gmgJYIz5CMB9AIaZ+bXGQMVi8RoRuZeI/lZKHc1ms3/U34+Pj6+cnZ3dC+AggGPMfH8cRCyAMUZqThlmPlwPUCgU0qlUyq7Kww1BrbjHzPsahYwx/QDK9hkzR2pFPjTGnAWwIexkjOkRkRIR3Rozo5Miskdr/VMIxE7mHDPXD+2l1wsA6nseBMHmfD7/dSjQOwB2tTlYC/bddd1blFJfARhj5lyjfxNA7bSfitpz3/d3iYgFaDuUUjeHM8AY8zSAV5VS9+RyudP1IE0Axpj3Aaxk5m1hFc/zPiWiO9uq/2dwiJn3h22NMScB/MnMOxYAjI6Orurq6rpgU0hrXYpw/hFAd0KAD5n5wbCt7/t7ROT1ubm5NUNDQ7/b95dWwHXd7Uqp452dnVcPDAxciACwDlclAbAFynGcdRGr2EtEnwdB0JfP5080ARhjDhLRbY7jbI0SMcZUAKxOAgDgN2ZOx8SxGfEiMz/bBOD7/lgQBCu01o/GOH4PYFNCgElmvinK1vf9X2xxchzHVtfLW2CMOQpgipmHYwBeBtBUaOJgRKSgtX48Js63AH5l5geaADzPe1cpddFxnHyM42YAXyZYAXt+epn557iVFJHzWuudYYDnlFJ9juPcESfi+35JRHa3gdjPzIfibIwxNtPe0Fq/EAZ4hIhcZl4T5+y67nql1CcA1kfZENFnrSZQ6ycqItKvtT4SBthIRJMdHR092WzW5nzk8H1/WEReiQHY4TjOB3G+nuf9qyEim7TW9r65fAjL5fKVlUrlPICXmPnNFquwWym1oFBZ+yAItubz+TMtAJ4gon3pdLo7k8nMNgHYP7ZeE5EWkbuYeaYx0NjY2HXValUDeApAV4zIJIADzPxe+H2hULg+lUp9U6sBl3qLprugXC6nKpXKF0R02nGcZ2wQ3/e3ichOEckQ0aoEWQDbsgF4a3p6eqLeH3qeN0FE3el0ujeTyVTrcaKuY1uIjIg8CaCfiLYnEW1hY4WPi8gEgMeY+e1G27iGxHYxtptZynGYmTPhgK1asqWEiBRfcAjDdMaYpYCIFW8LUMuM54nIsb3/YvbDXskiYtuzA6382n4X1CDWAnCSgNSFa98ETakcWbwWMytjzAoAPUEQ3JBKpXrs75r/VLVanVJK/VC7Uf9KGjfRCiQN9n/slh3gHz9i4jC+FVL5AAAAAElFTkSuQmCC",
        "commentData": null,
        "placeholder": "请输入评论",
        "commentReq": {
          "pId": null, // 评论父id
          "content": null // 评论内容
        },
        "pUser": null, // 标签-回复人
        "showTag": false, // 标签展示与否
        "focus": false, // 输入框自动聚焦
        "submit": false, // 弹出评论
        "KeyboardHeight": 0, // 键盘高度
        $validate: this.$validate,
        file_ctx: this.file_ctx,
        isAnonymous: false, // 发表评论是否匿名
        replyTarget: null,
        config: {
          img: {
            count: 1,
            background: 'rgba(0,0,0,0)',
            formData: {
              groupId: "26000",
              relId: ''
            },
            showImg: false
          }
        },
        sendImgages: []
      };
    },
    mounted() {
      // 表情尺寸
      const systemInfo = uni.getSystemInfoSync();
      var radio = 750 / systemInfo.windowWidth;
      this.lineHeight = 50 / radio;
    },
    methods: {
      keyboardHeightChangeListener(res) {
        this.KeyboardHeight = res.height;
      },
      // 图片失效
      handleError(item) {
        item.headPath = this.file_ctx + 'static/image/system/avatar/icon-doctor-head.png'
      },

      // 表情业务逻辑交互
      _cancelEvent(e) {
        console.log('你点击了取消');
        this.isShown = false;
      },

      _confirmEvent(e) {
        console.log('你点击了确定');
        this.isShown = false;
      },
      showDialog() {
        console.log('执行了')
        this.isShown = true;
      },
      startDrop(e) {
        // var count = e.target.dataset.count;
        if (!this.dropHeight) {
          // this.setData({
          // 	dropHeight: true,
          // })
          this.dropHeight = true;
        }
      },
      Comment(e) {
        // console.log(e)
        var type = e.target.dataset.type;
        var isComment = false
        if (type == 'open') {
          isComment = true
        }
        this.isComment = isComment;
        this.dropHeight = false;
      },
      // // 发送评论
      // sendPL(){
      //   console.log('点击了评论')
      // },
      // 评论
      onkeyboardHeightChange(e) {
        const {
          height
        } = e.detail
        this.keyboardHeight = height;
      },

      hideAllPanel() {
        this.functionShow = false;
        this.emojiShow = false;
      },
      showEmoji() {
        this.focus = false;
        this.functionShow = false;
        this.emojiShow = this._keyboardShow || !this.emojiShow
      },
      showFunction() {
        this.functionShow = this._keyboardShow || !this.functionShow;
        this.emojiShow = false;
      },
      chooseImage() {},
      onFocus() {
        this._keyboardShow = true;

        this.hideAllPanel()
      },
      onBlur(e) {
        this._keyboardShow = false
        this.cursor = e.detail.cursor || 0
      },
      onInput(e) {
        const value = e.detail.value
        // this.comment = value
        // this.cursor = value.length
      },
      onConfirm() {
        this.onsend()
      },
      insertEmoji(evt) {
        const emotionName = evt.emotionName
        let {
          cursor,
          comment
        } = this;
        comment = this.commentReq.content || ''
        const newComment =
          comment.slice(0, cursor) + emotionName + comment.slice(cursor)

        // this.comment = newComment;
        this.commentReq.content = newComment;

        this.cursor = cursor + emotionName.length
      },
      onsend() {
        // const comment = this.comment;
        const comment = this.commentReq.content
        console.log(comment);
        // const parsedComment = this.parseEmoji(this.comment);
        let parsedComment = this.$refs.emojiRef.parseEmoji(this.commentReq.content)
        console.log(parsedComment)
        this.CommentArr.push({
          avator: '/image/bg.jpg',
          parsedComment: parsedComment
        })
      },
      deleteEmoji: function() {
        const pos = this.data.cursor
        const comment = this.data.comment
        let result = '',
          cursor = 0

        let emojiLen = 6
        let startPos = pos - emojiLen
        if (startPos < 0) {
          startPos = 0
          emojiLen = pos
        }
        const str = comment.slice(startPos, pos)
        const matchs = str.match(/\[([\u4e00-\u9fa5\w]+)\]$/g)
        // 删除表情
        if (matchs) {
          const rawName = matchs[0]
          const left = emojiLen - rawName.length
          if (this.emojiNames.indexOf(rawName) >= 0) {
            const replace = str.replace(rawName, '')
            result = comment.slice(0, startPos) + replace + comment.slice(pos)
            cursor = startPos + left
          }
          // 删除字符
        } else {
          let endPos = pos - 1
          if (endPos < 0) endPos = 0
          const prefix = comment.slice(0, endPos)
          const suffix = comment.slice(pos)
          result = prefix + suffix
          cursor = endPos
        }
        this.commentReq.content = result;
        // this.comment = result;
        this.cursor = cursor;

      },

      // ----------------------
      userClick(data) {
        const accountId = data.accountId
        const id = data.userId
        console.log(data, 'data6666')
        if (data.businessType == 10 && id) {
          this.navtoGo('DoctorDetail', {
            id
          })
        } else if (data.businessType == 10 && !id) {
          this.navtoGo('PersonalHomePage', {
            homePageAccountId: "807651524714201090",
            isShowBtn:this.isShowBtn
          })
        } else if (this.$common.getKeyVal('user', 'accountId') === accountId) {
          // 是否是自己
          this.$navto.pushTab('Personal', {})
        } else {
          this.navtoGo('PersonalHomePage', {
            homePageAccountId:accountId,
            isShowBtn:this.isShowBtn
          })
        }
      },
      navtoGo(url, obj = {}) {
        this.$navto.push(url, obj)
      },
      clickSendImg() {
        this.$refs.sendImgRef.uploadImage()
      },
      sendImg(list) {
        this.sendImgages = list
        // this.$emit('comment', {target: this.replyTarget, imagePath: list, type: 2})
      },
      // 预览图片
      previewImage(list, index) {
        uni.previewImage({
          current: index,
          urls: list.map(item => this.file_ctx + item)
        })
      },
      handleRelease() {
        this.$emit('comment', {
          target: this.replyTarget,
          content: this.commentReq.content,
          imagePath: this.sendImgages
        })
      },
      changeAnonymous(e) {
        this.isAnonymous = e.detail.value.includes('anonymous')
      },
      openComment({
        id,
        children
      }) {
        let list = children.reverse()
        const lastItem = list.find(item => !item.isAddCm)
        const params = {
          lastMsgId: (this.$validate.isNull(children) || this.$validate.isNull(lastItem)) ? '' : lastItem.id,
          level: 2,
          ownerCommentId: id,
          pageSize: this.pageSize
        }
        this.$emit('loadMore', params, arguments[0])
      },
      // 初始化评论
      init(cmData) {
        // 表情包内容替换
        for (var i in cmData.comment) {
          if (!cmData.comment[i].parsedComment) {
            cmData.comment[i].parsedComment = this.$refs.emojiRef.parseEmoji(cmData.comment[i].content)
            // cmData.comment[i].parsedinit = true;
          }
          if (cmData.comment[i].children.length > 0) {
            for (let j = 0; j < cmData.comment[i].children.length; j++) {
              if (!cmData.comment[i].children[j].parsedComment) {
                cmData.comment[i].children[j].parsedComment = this.$refs.emojiRef.parseEmoji(cmData.comment[i].children[
                  j].content)
              }
            }
          }
        }
        this.commentData = cmData;
      },
      // 没用的方法，但不要删
      stopPrevent() {},
      // 回复评论
      reply(pUser, reUser, pId, e) {
        this.$emit('replyBefore')
        this.replyTarget = e
        this.pUser = pUser;
        this.commentReq.pId = pId;
        if (reUser) {
          // this.commentReq.content = '@' + reUser + ' ';
          this.placeholder = '回复' + reUser
        } else {
          // this.commentReq.content = '';
          this.placeholder = '回复楼主'
        }
        this.showTag = true;
        this.commentInput();
      },
      // 删除评论前确认
      confirmDelete(item) {
        var that = this;
        uni.showModal({
          title: '警告',
          content: that.deleteTip,
          confirmText: '确认删除',
          success: function(res) {
            if (res.confirm) {
              that.$emit('del', item);
            }
          }
        });
      },
      // 新增评论
      add() {
        if (this.commentReq.content == null || this.commentReq.content.length < 2) {
          uni.showToast({
            title: '评论内容过短',
            duration: 2000
          });
          return
        }
        this.$emit('add', this.commentReq);
      },
      // 点赞评论
      like(item) {
        this.$emit('like', item);
      },
      // 新增完成
      addComplete() {
        this.commentReq.content = null;
        this.tagClose();
        this.closeInput();
      },
      // 点赞完成-本地修改点赞结果
      likeComplete(commentId) {
        for (var i in this.commentData.comment) {
          if (this.commentData.comment[i].id == commentId) {
            this.commentData.comment[i].hasLike ? this.commentData.comment[i].likeNum-- : this.commentData
              .comment[i].likeNum++;
            this.commentData.comment[i].hasLike = !this.commentData.comment[i].hasLike;
            return
          }
          for (var j in this.commentData.comment[i].children) {
            if (this.commentData.comment[i].children[j].id == commentId) {
              this.commentData.comment[i].children[j].hasLike ? this.commentData.comment[i].children[j]
                .likeNum-- : this.commentData.comment[i].children[j].likeNum++;
              this.commentData.comment[i].children[j].hasLike = !this.commentData.comment[i].children[j]
                .hasLike;
              return
            }
          }
        }
      },
      // 删除完成-本地删除评论
      deleteComplete(commentId) {
        for (var i in this.commentData.comment) {
          for (var j in this.commentData.comment[i].children) {
            if (this.commentData.comment[i].children[j].id == commentId) {
              this.commentData.comment[i].children.splice(Number(j), 1);
              return
            }
          }
          if (this.commentData.comment[i].id == commentId) {
            this.commentData.comment.splice(Number(i), 1);
            return
          }
        }
      },
      // 展开评论
      showMore(commentId) {
        for (var i in this.commentData.comment) {
          if (this.commentData.comment[i].id == commentId) {
            this.commentData.comment[i].hasShowMore = !this.commentData.comment[i].hasShowMore;
            this.$forceUpdate();
            return
          }
          for (var j in this.commentData.comment[i].children) {
            if (this.commentData.comment[i].children[j].id == commentId) {
              this.commentData.comment[i].children[j].hasShowMore = !this.commentData.comment[i].children[j]
                .hasShowMore;
              this.$forceUpdate();
              return
            }
          }
        }
      },
      // 输入框失去焦点
      blur(e) {
        this.focus = false;
        this.onBlur(e)
      },
      // 输入框聚焦
      focusOn() {
        // 表情包焦点偏移
        this.onFocus()
        this.$emit('focusOn');
      },
      // 标签关闭
      tagClose() {
        this.showTag = false;
        this.pUser = null;
        this.commentReq.pId = null;
      },
      // 输入评论
      commentInput() {
        // TODO 调起键盘方法
        this.submit = true;
        setTimeout(() => {
          this.focus = true;
        }, 50)
      },
      // 关闭输入评论
      closeInput() {
        // this.clearInput()
        this.focus = false;
        this.submit = false;
      },
      // 清空输入框
      clearInput() {
        this.commentReq = {
          "pId": null, // 评论父id
          "content": null // 评论内容
        }
        this.sendImgages = []
      },
      addComment() {
        this.$emit('replyBefore')
        this.placeholder = ''
        this.replyTarget = null
        this.clearInput()
        this.commentInput()
      }
    },
    filters: {
      getTimeStringAutoShort2(timestamp) {
        // return timestamp ? common.formatDate(new Date(timestamp), 'yyyy-MM-dd HH:mm').replace(/-/g, '/') : ''
        return timestamp ? timePlugin.formatDate(new Date().valueOf(), timestamp) : ''
      }
    }
  };
</script>

<style lang="scss" scoped>
  .tip {
    color: #a5a5a5;
    margin-left: 20rpx;
  }

  .d-t {
    display: flex;
    align-items: center;
  }

  // 表情包辅助CSS

  @import '@/modules/community/components/nui-emoji/index.css';

  .emoji {
    // width: 50upx;
    // height: 50upx;
    width: 20px;
    height: 20px;
    margin-left: 20upx;
  }

  .hb-comment {
    height: 100%;
    width: 100%;
    padding: 0 24rpx;
    box-sizing: border-box;
  }

  .top-read {
    font-size: 28rpx;
    padding-left: 10rpx;
    color: #999999;
  }

  .seg_line_box {
    display: flex;
    height: 5rpx;
    justify-content: space-between;
    margin: 5rpx 0;
  }

  .seg_line {
    width: 45%;
    border-bottom: 1rpx solid #e1e1e1;
  }

  .seg_dot {
    width: 8%;
    border-bottom: 5rpx dotted #dbdbdb;
  }

  .comment-num {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;
  }

  .comment-box {
    padding: 10rpx 0;
  }

  .comment-box-item {
    display: flex;
  }

  .comment-main {
    padding-left: 20rpx;
  }

  .comment-main-top {
    width: 600rpx;
    // padding-top: 6rpx;
    display: flex;
    justify-content: space-between;
  }

  .sub-comment-main-top {
    width: 540rpx;
    padding-top: 6rpx;
    display: flex;
    justify-content: space-between;
  }

  .avatar {
    width: 62rpx;
    height: 62rpx;
    border-radius: 50%;
    // padding-top: 6rpx;
  }

  .nick-name-box {
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    .nick-hospitcal {
      font-size: 22rpx;
      color: #767680;

      span {
        font-weight: 700;
      }
    }
  }

  .comLogo {
    margin-right: 18rpx;
    font-size: 22rpx;
    border-radius: 10rpx;
    padding: 5rpx 15rpx;
    color: #FFFFFF;
  }

  .com1 {
    background-color: #d218b1;
  }

  .com2 {
    background-color: #f19c0b;
  }

  .com3 {
    background-color: #c8da85;
  }

  .com4 {
    background-color: #bfd0da;
  }

  .nick-name {
    color: #2D2D2D;
  }

  .isLike {
    font-size: 28rpx;
    padding-right: 10rpx;
    color: #2D2D2D;
  }

  .notLike {
    font-size: 28rpx;
    padding-right: 10rpx;
    color: #999999;
  }

  .comment-main-content {
    padding: 10rpx 10rpx 10rpx 0;
  }

  .comment-main-foot {
    display: flex;
    align-items: center;
    font-size: 22rpx;
  }

  .replayTag {
    color: #909399;
    margin-bottom: 5px;
    border: 1px solid #c8c9cc;
    background-color: #f4f4f5;
    border-radius: 3px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16rpx;
    padding: 5px 10px;
  }

  .replyTagClose {
    font-size: 20px;
    line-height: 12px;
    padding: 0 0 2px 5px;
  }

  .foot-btn {
    padding-left: 10rpx;
    color: #ACACAC;
  }

  .comment-sub-box {
    padding: 20rpx 0;
  }

  .comment-sub-item {
    display: flex;

    .avatar {
      width: 42upx;
      height: 42upx;
      border-radius: 50%;
    }

    &+.comment-sub-item {
      padding-top: 24upx;
    }
  }

  .comment-none {
    padding: 20rpx;
    width: 100%;
    text-align: center;
    color: #999999;
  }

  .comment-submit-box {
    position: fixed;
    display: flex;
    align-items: flex-end;
    z-index: 9900;
    left: 0;
    top: var(--window-top);
    bottom: 0;
    background-color: rgba($color: #000000, $alpha: 0.5);
    width: 100%;
  }

  .comment-add {
    position: absolute;
    background-color: #FFFFFF;
    width: 100%;
    border: 1px solid #ddd;
    transition: .3s;
    -webkit-transition: .3s;
    padding: 27upx 34upx;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    box-sizing: border-box;

    &-bottom {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-top: 18upx;

      &-right {
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
    }
  }

  .btn-click {
    color: #007AFF;
    font-size: 28rpx;
    padding: 10rpx;
  }

  .cancel {
    color: #606266;
  }

  .textarea {
    height: 135upx;
    padding: 18upx 20upx;
    width: 100%;
    background: #F6F6F6;
    border-radius: 14upx;
    box-sizing: border-box;
  }

  .comment-submit {
    padding: 5rpx 20rpx 0 20rpx;
    border-bottom: 1px dashed #ddd;
    width: calc(100% - 40rpx);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .add-btn {
    position: static;
    width: auto;
    height: auto;

    button {
      background-color: $topicC;
    }
  }

  .more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: 24upx;

    &-text {
      font-size: 24upx;
      font-weight: 400;
      color: $topicC;
      line-height: 34upx;
    }
  }

  .comment-sub-more {
    display: flex;
    align-items: center;
    padding-top: 12upx;

    &::before {
      content: "";
      width: 42upx;
      margin-right: 20upx;
      height: 1upx;
      background-color: #e5e5e5;
    }

    &-text {
      font-size: 24upx;
      font-weight: 400;
      color: #8B8B8B;
      line-height: 34upx;
    }
  }

  .comment-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 12upx;

    &-text {
      font-size: 24upx;
      font-weight: 400;
      color: #8B8B8B;
      line-height: 34upx;
    }
  }

  .mutual-comment-box {
    flex: 1;
    display: flex;
    align-items: center;

    .reply-text {
      width: 0;
      height: 0;
      border-top: 8upx solid transparent;
      border-left: 14upx solid #8B8B8B;
      border-bottom: 8upx solid transparent;
      margin: 0 24upx;
    }

    .nick-name {
      color: #8B8B8B;
    }
  }

  .foot-source {
    font-size: 26upx;
    color: #8B8B8B;

    span {
      background-color: #ecf5ff;
      display: inline-block;
      height: 40rpx;
      line-height: 40rpx;
      border-radius: 10rpx;
      color: #409eff;
      border: 1px solid #d9ecff;
      white-space: nowrap;
      padding: 0 10rpx;
      margin-left: 5rpx;
    }
  }

  .foot-time {
    font-size: 26upx;
    color: #8B8B8B;
    padding-right: 12upx;
  }

  .content-img {
    width: 200upx;
    height: 100%;
  }

  .release-btn {
    margin-left: 24upx;
  }

  .nick-name-width-1 {
    display: inline-block;
    vertical-align: middle;
    @include ellipsis(1);
  }

  .nick-name-width-2 {
    display: inline-block;
    vertical-align: middle;
    @include ellipsis(1);
  }

  .zan-box {
    width: 130upx;
    text-align: right;
  }

  .send-img-box {
    width: 116upx;
    height: 116upx;
    background-size: cover;
    background-position: center center;
    display: inline-block;
    position: relative;

    .send-img {
      width: 100%;
      height: 100%;
      overflow: hidden;
    }

    .send-img-remove {
      position: absolute;
      top: 0upx;
      right: 0upx;
      @include iconImg(40, 40, '/business/icon-close-black-circle.png');
      margin: 0;
      z-index: 2;
    }
  }
  .comment-list {
    &::-webkit-scrollbar {
      display: none;
    }
  }
</style>
