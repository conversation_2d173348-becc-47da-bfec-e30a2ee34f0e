import request from '@/common/util/request'
import env from '@/config/env'

/**
 * 系统请求工具类，
 * 注意：API接口只做请求数据和格式化数据的事情，其他事务一概不处理
 */
export default {
  /**
   * 资讯管理 分页
   * @param resolve
   * @param reject
   */
  informationscienceQueryAppPage(param) {
    const url = env.ctx + 'dm/api/v1/informationscience/query/app/page'
    return request.postJson(url, param)
  },
  /**
   * 资讯管理 根据主键单一查询
   * @param param
   * @returns {*}
   */
  informationscienceQueryOne(param) {
    const url = env.ctx + 'dm/api/v1/informationscience/query/one'
    return request.get(url, param)
  },
  /**
   * 资讯管理 更新真实阅读数、点赞数、分数数、收藏数
   * @param param
   * @returns {*}
   */
  informationscienceNumberUpdate(param) {
    const url = env.ctx + 'dm/api/v1/informationscience/number/update'
    return request.putJson(url, param)
  }
}
