<!-- 搜索栏组件 <search :placeholder="***"  :value="***" @input="onKeyInput" ></search> -->
<template>
  <view class="search" :class="{ fixed: fixed }" :style="'top: ' + top + 'rpx;' + searchStyle">
    <view
      class="input"
      :class="{ 'width-auto': !rightText && !isIcon }"
      :style="'width: ' + 'calc(100% - ' + width + 'px);' + inputStyle"
    >
      <em class="icon-view-l icon-search"></em>
      <input
        class="input-view"
        type="text"
        @input="onKeyInput"
        :placeholder="placeholder"
        :placeholder-class="'placeholder-f-s-28'"
        :placeholder-style="placeholderStyle"
        :value="inputValue"
        :disabled="disabled"
      />
      <em v-if="inputValue" class="icon-view-r" @tap="remove"></em>
    </view>
    <view class="right-text" @tap="returnFn">
      <view class="text" v-if="rightText">
        {{ rightText }}
      </view>
      <view v-if="isIcon" class="icon icon-screen"></view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Search',
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $validate: this.$validate,
      inputValue: '',
      timer: undefined,
      width: 0
    }
  },
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    // 提示文本style
    placeholderStyle: {
      type: String,
      default() {
        return ''
      }
    },
    isIcon: {
      type: Boolean,
      default() {
        return false
      }
    },
    rightText: {
      type: String,
      default() {
        return ''
      }
    },
    value: {
      type: String,
      required: false,
      default: ''
    },
    placeholder: {
      type: String,
      default() {
        return '搜索'
      }
    },
    k: { // 字段名称
      type: String,
      default() {
        return 'name'
      }
    },
    top: {
      type: String,
      default() {
        return '88'
      }
    },
    fixed: {
      type: [String, Boolean],
      default() {
        return true
      }
    },
    searchStyle: {
      type: String,
      default: ''
    },
    inputStyle: {
      type: String,
      default: ''
    }
  },
  watch: {
    value: {
      handler(val) {
        this.inputValue = val
      },
      deep: true
    },
    /** 监听手机输入 */
    inputValue() {
      if (this.timer) {
        clearTimeout(this.timer)
      }
      this.timer = setTimeout(() => {
        this.inputValue = this.$validate.trim(this.inputValue)
        const obj = {}
        obj[this.k] = this.inputValue
        this.$emit('changeSearch', obj)
      }, 500)
    }
  },
  mounted() {
    this.getRightWidth()

  },
  methods: {
    // 清除输入框
    remove() {
      this.inputValue = ''
    },
    onKeyInput(e) {
      this.inputValue = e.target.value
    },
    returnFn() {
      this.$emit('returnFn', this.inputValue)
    },
    getRightWidth() {
      this.$nextTick(() => {
        const that = this
        let width = 0
        // #ifdef H5
        width = this.$el.querySelector('.right-text').clientWidth;
        that.width = width
        // #endif
        // #ifdef MP
        var query = this.createSelectorQuery();
        query.select('.right-text').boundingClientRect(
          function (rect) {
            // console.log(rect)
            if (rect.width) {
              that.width = rect.width;
            }
          }
        ).exec();
        // #endif

      })


    }
  }
}
</script>

<style lang="scss" scoped>
.fixed {
  position: fixed;
}
.search {
  left: 0;
  right: 0;
  height: 92upx;
  padding: 16upx;
  background-color: $pageBg;
  z-index: 9999;
  box-sizing: border-box;
  .input {
    background-color: #fff;
    @include rounded(10upx);
    position: relative;
    width: calc(100% - 140upx);
    vertical-align: middle;
    display: inline-block;
    box-sizing: border-box;
    .icon-view-l {
      position: absolute;
      top: 50%;
      -webkit-transform: translateY(-50%);
      transform: translateY(-50%);
      left: 18upx;
      width: 34upx;
      height: 34upx;
    }
    .input-view {
      width: 100%;
      height: 62upx;
      line-height: 62upx;
      padding: 0 60upx 0 60upx;
      box-sizing: border-box;
    }
    .icon-view-r {
      @include iconImg(40, 40, "/business/hulu-v2/icon-search-clear.png");
      position: absolute;
      top: 50%;
      -webkit-transform: translateY(-50%);
      transform: translateY(-50%);
      right: 16rpx;
    }
  }
  .placeholder-f-s-28 {
    font-size: 28upx;
  }
  .width-auto {
    width: 100%;
  }
  .right-text {
    vertical-align: middle;
    display: inline-block;
    .text {
      text-align: right;
      font-size: 32upx;
      line-height: 48upx;
      color: #666;
      display: inline-block;
      vertical-align: middle;
      width: calc(100% - 40upx);
    }
    .icon {
      display: inline-block;
      vertical-align: middle;
      margin-left: 8upx;
    }
    .icon-screen {
      @include iconImg(32, 32, "/business/icon-screen.png");
    }
  }
}
.uni-input-form,
.uni-input-input,
.uni-input-placeholder,
.uni-input-wrapper {
  margin-top: -1px;
  color: #cccccc !important;
}
</style>
