<template>
	<view :class="{'video-list-horizontal':model === 'horizontal'}"  class="video-list">
		<template v-for="item,index in pdList" >
			<!-- <view class="video-list-item" :style="{backgroundColor:item.screenDirectionType == 2 ? 'black' : ''}" @click='nextStep(index)' :key="item.id"> -->
			<view class="video-list-item" @click='nextStep(index)' :key="item.id">
				<view class="bg">
					<!-- <image :src="item.coverPathsUrl" :mode='item.screenDirectionType == 2 ? "aspectFit" : "aspectFill"' class="video-list-img"></image> -->
					<image :src="item.coverPathsUrl" class="video-list-img"></image>
					<view class="video-list-txt">
						<view class="txt">
							{{item.title}}
							<!-- 开学直播学校,开学直播学校,睡前直播准备好了吗，睡前直播准备好了吗 -->
						</view>
					</view>

					<view class="video-list-tag" :class="{live: item.activityStatus == 3}">
						<view class="ing" v-if="item.activityStatus == 3">
							<image mode="aspectFit" :src="increaseico" class="ingico"></image>
							直播中
						</view>
						<view class="tag" v-else-if="item.activityStatus == 5">回放</view>
						<view class="tag" v-else-if="item.activityStatus == 2">预告</view>
					</view>
				</view>
			</view>
		</template>
	</view>
</template>

<script>
	export default {
		props: {
			pdList: {
				type: Array,
				default: function() {
					return []
				}
			},
			model:{
				type:String,
				default:'vertical'
			},
      logConditionParams:{
        type:Object,
        default:function () {
          return {}
        }
      }
		},
		data() {
			return {
				file_ctx: this.file_ctx,
				increaseico: this.$static_ctx + 'image/business/hulu-v2/icon-liveing.png'
			}
		},
		methods: {
			nextStep(index) {
				console.log('index', index,this.pdList[index]);
				this.$navto.push('shortVideoList', {
					id: this.pdList[index].id,
          coverPathsUrl:this.pdList[index].coverPathsUrl,
          logConditionParams:encodeURIComponent(JSON.stringify(this.logConditionParams))
				});

			}
		},


	}
</script>

<style lang="scss" scoped>
	.video-list-horizontal{
		  display: grid !important;
		  grid-auto-flow: column; /* 让项目在列方向上排列 */
		  grid-gap: 10px; /* 如果需要间距 */
		  .video-list-item{
			  width: 230rpx !important;
			  height: 383.333rpx !important;
		  }
	}
	.video-list {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
    padding: 0 0rpx 32rpx 6rpx;

		.bg {
			box-shadow: 2upx 2upx 5upx rgba(0, 0, 0, 0.2);
			width: 100%;
			height: 100%;
			position: relative;

			display: flex;
			align-items: center;
		}

		.ing {
			font-size: 24upx;

		}

		.ingico {
			width: 24rpx;
			height: 24rpx;
			margin-right: 8px;
		}
    .video-list-item{
      width: 364rpx;
      height: 604rpx;
      position: relative;
      box-sizing: border-box;
      border-radius: 8rpx;
      overflow: hidden;
      &:nth-child(n + 1) {
        margin-bottom: 8rpx;
      }
      &:nth-child(2n) {
        margin-left: 10rpx;
      }
    }

		.video-list-img {
			width: 100%;
			height: 100%;
			background-color: rgba(0, 0, 0, 0.2);
		}

		.video-list-txt {
			display: flex;
			align-items: flex-end;
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			height: 152rpx;
			background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.7) 100%);
			border-radius: 0rpx 0rpx 8rpx 8rpx;
			padding: 0 28rpx 24rpx;

			.txt {
				font-weight: 500;
				font-size: 28rpx;
				color: #FFFFFF;
				line-height: 40rpx;
			}
		}

		.video-list-item {
			overflow: hidden;
			width: 364rpx;
			height: 604rpx;
			position: relative;
			box-sizing: border-box;
			border-radius: 8rpx;

			&:nth-child(n + 1) {
				margin-bottom: 8rpx;
			}

			&:nth-child(2n) {
				margin-left: 10rpx;
			}
		}

		.video-list-tag {
			display: flex;
			align-items: center;
			font-size: 24upx;
			background: rgba(255, 255, 255, 0.95);
			position: absolute;
			top: 16rpx;
			left: 16rpx;
			border-radius: 8rpx;
			padding: 6rpx 16rpx;

			.tag {
				font-weight: 500;
				font-size: 24rpx;
				color: #1D2029;
				line-height: 34rpx;
			}

			&.live {
				background: rgba(255, 85, 0, 0.95);
				color: #fff;
			}
		}

	}
</style>
