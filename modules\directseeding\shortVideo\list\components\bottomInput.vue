<template>
  <view class="box-operation" @click.stop>
	  <!-- 遮罩层 -->
	  <view class="mask" v-if="showMask" @click="touchMask"></view>
	  <!-- 判断当前是否为插槽 -->
	<template v-if="isSlot">
	</template>
    <!-- 竖屏才显示 -->
    <template v-else>
		<template v-if="orientation == 'vertical'">
		  <template v-if="showtype == 1">
		    <view
		      class="sliderbox2"
		      :class="{
		        screenBottom: screenDirectionType === 2,
		      }"
		    >
		      <!-- <template v-if="!likestatus2">
		        <view class="iconbox">
		          <view hover-class="active" class="reply_button replay_media_button" @click="like"><image :src="xinurl" mode="aspectFit" class="reply_tool_pic"></image></view>
		        </view>
		      </template>
		      <template v-else>
		        <view class="iconbox">
		          <view hover-class="active" class="reply_button replay_media_button" @click="like"><image :src="xinurl_a" mode="aspectFit" class="reply_tool_pic"></image></view>
		        </view>
		      </template>
		       -->
		      <view class="iconbox">
		        <live-like :count="count" :data='data' :visible2="visible2"></live-like>
		        <image
		          hover-class="active"
		          @click="clickHandler"
		          class="reply_button replay_media_button"
		          :class="clickDzVisible ? 'likeBtn' : ''"
		          :src="dzurl"
		        ></image>
		      </view>

		      <!-- #ifndef H5 -->
		      <view class="iconbox">
		        <button
		          class="share-btn"
		          open-type="share"
		          style="padding-left: 0; background: transparent; padding-right: 0"
		          @click="share"
		        >
		          <view
		            hover-class="active"
		            class="reply_button replay_media_button"
		          >
		            <image
		              :src="gifturl"
		              mode="aspectFit"
		              class="reply_tool_pic"
		            ></image>
		          </view>
		        </button>
		      </view>

		      <!-- #endif -->
		      <view
		        class="iconbox"
		        @click="addUser"
		        v-if="qrCodePath && qrCodePath !== ''"
		      >
		        <image
		          class="reply_button replay_media_button"
		          :src="lpurl"
		        ></image>
		      </view>
			  <!-- 新版设计稿上并没有更多按钮 暂时先注释掉 -->
		     <!-- <view class="iconbox" v-if="showMoreBtn">
		        <view
		          hover-class="active"
		          class="reply_button replay_media_button"
		          @click="showMoreClick"
		        >
		          <image
		            :src="moreurl"
		            mode="aspectFit"
		            class="reply_tool_pic"
		          ></image>
		        </view>
		      </view> -->
		    </view>
		  </template>
		  <template v-else>
		    <view
		      class="reply_wrp"
		      :style="{
		        bottom: (keyboardHeight == 0 ? 40 : keyboardHeight) + 'px',
		      }"
		    >
			<view class="" @click.stop="touchMask">
        <slot></slot>
      </view>
		      <view class="reply_tool" :class="[focusvisible ? 'bgf' : '']">
		        <view class="reply_form_wrp">
		          <!-- 输入框 -->
		          <label class="reply_label" :class="{focusStyle:showMask}" @touchmove.stop="() => {}">
		            <view class="textareaFaBox">
                  <!-- 模拟placeholder -->
                  <view class="placeholderBox" v-if="!comment && !focusvisible" @click.stop="() => {}">说点什么...</view>
		            	<textarea
		            	  class="reply_input2"
                    :style="{paddingTop:currentPlatform === 'ios' ? '0' : ''}"
		            	  cursor-spacing="8px"
		            	  confirm-type="send"
		            	  :adjust-position="false"
		            	  confirm-hold
		            	  :value="comment"
		            	  :cursor="cursor"
		            	  :maxlength='40'
		            	  :focus="focus"
                    @click.stop="focusInput"
		            	  @blur="onBlur"
		            	  @focus="onFocus"
		            	  @input="onInput"
		            	  @confirm="onConfirm"
		            	  @keyboardheightchange="onkeyboardHeightChange"
		            	/>
                  <view class="promptBox" v-if="comment">{{comment.length}}/40</view>
		            </view>
		            <!-- 表情列表唤起 -->
		            <image
		              hover-class="active"
		              class="reply_button miniIcon"
		              :src="showMask ? (changeIcon ? keyboard : emojiico) : emojiico"
		              mode="aspectFit"
		              @click.stop="showEmoji(true)"
		            ></image>
		          </label>
		        </view>

		        <template v-if="!focusvisible">
				<!-- 横竖屏切换按钮 -->
		          <view class="iconbox changeANDROID" v-if="screenDirection">
		            <view
		              hover-class="active"
		              class="reply_button replay_media_button"
		              @click.stop="toggleVertical"
		            >
		              <image
		                :src="image1"

		                class="reply_tool_pic"
		              ></image>
		            </view>
		          </view>
				  <!-- 礼物图标 暂时先隐藏掉 -->
				  <view
				    class="iconbox"
				    @click="addUser"
				    v-if="qrCodePath && qrCodePath !== ''"
				  >
				    <image
				      class="reply_button replay_media_button"
				      :src="lpurl"
				    ></image>
				  </view>
		          <!-- #ifndef H5 -->
				  <!-- 分享按钮 -->
		          <view class="iconbox">
		            <button
		              class="share-btn"
		              open-type="share"
		              style="
		                padding-left: 0;
		                background: transparent;
		                padding-right: 0;
		              "
		              @click.stop="share"
		            >
		              <view
		                hover-class="active"
		                class="reply_button replay_media_button"
		              >
		                <image
		                  :src="gifturl"
		                  mode="aspectFit"
		                  class="reply_tool_pic"
		                ></image>
		              </view>
		            </button>
		          </view>
		          <!-- #endif -->
				  <!-- 点赞按钮 -->
		          <view class="iconbox" @click="clickHandler">
		            <live-like :data='data' :count="count" :visible2="visible2"></live-like>
		            <image
		              hover-class="active"
		              class="reply_button replay_media_button"
		              :class="clickDzVisible ? 'likeBtn' : ''"
		              :src="dzurl"
		            ></image>
		          </view>

				  <!-- 新版设计稿上并没有更多按钮 暂时先注释掉 -->
		          <!-- <view class="iconbox" v-if="showMoreBtn">
		            <view
		              hover-class="active"
		              class="reply_button replay_media_button"
		              @click="showMoreClick"
		            >
		              <image
		                :src="moreurl"
		                mode="aspectFit"
		                class="reply_tool_pic"
		              ></image>
		            </view>
		          </view> -->
		        </template>

		        <template v-else>
		          <button
		            class="sendBtn"
		            @click="onsend"
		            :loading="sendBtnLoading"
		            :disabled="sendBtnLoading"
		          >
		            发送
		          </button>
		        </template>
		      </view>
		      <!-- emoji列表 -->
		      <view
		        class="reply_panel_wrp"
		        :style="{ height: emojiShow ? 260 + 'px' : 200 + 'px' }"
		        :hidden="!emojiShow && !functionShow"
		      >
		        <view
		          class="reply_panel"
		          :class="[emojiShow ? 'show' : '']"
		          :hidden="!emojiShow"
		        >
		          <nui-emoji
		            ref="emojiRef"
		            :titleColor="'#fff'"
		            :updatecount="updatecount"
		            :source="emojiSource"
		            class="mp-emoji"
		            @insertemoji="insertEmoji"
		            @delemoji="deleteEmoji"
		            @send="onsend"
		            :padding="0"
		          ></nui-emoji>
		        </view>
		      </view>
		    </view>
		  </template>
		</template>

		<!-- 横屏 -->
		<template v-else>
		  <!-- 横屏切换 -->
		  <view class="orientationico changeANDROIDH">
		    <view
		      hover-class="active"
		      class="reply_button replay_media_button"
		      @click="toggleVertical"
		    >
		      <image :src="imageH" mode="aspectFit" class="reply_tool_pic"></image>
		    </view>
		  </view>
			<view class="horizontalIconBox">
				<!-- 转发图标 -->
				<!-- #ifndef H5 -->
				<view class="iconbox zhufico">
				  <button
				    class="share-btn"
				    open-type="share"
				    style="padding-left: 0; background: transparent; padding-right: 0"
				    @click="share"
				  >
				    <view hover-class="active" class="reply_button replay_media_button">
				      <image
				        :src="gifturl"
				        mode="aspectFit"
				        class="reply_tool_pic"
				      ></image>
				    </view>
				  </button>
				</view>
				<!-- #endif -->
				<!-- 点赞图标 -->
				<!-- <view class="t-goodico"> -->
				<view class="iconbox t-goodico">
				  <live-like :count="count" :visible2="visible2"></live-like>
				  <image
				    hover-class="active"
				    @click="clickHandler"
				    class="reply_button replay_media_button"
				    :class="clickDzVisible ? 'likeBtn' : ''"
				    :src="dzurl"
				  ></image>
				</view>
			</view>
		  <!-- </view> -->
		  <!-- 礼物图标 暂时隐藏掉 -->
		  <!-- <view
		    class="iconbox"
		    @click="addUser"
		    v-if="qrCodePath && qrCodePath !== ''"
		  >
		    <image class="reply_button replay_media_button" :src="lpurl"></image>
		  </view> -->
		</template>

		<!-- :scrolly="false" -->
		<showbottommodel
		  :show="visible"
		  zindex="9999"
		  top="calc(100vh - 600rpx - 60px)"
		  :autotitle="true"
		  :clickbg="false"
		  @cancel="cancel"
		  dialogStyle="background-color:#fff"
		  root-class="showbottominputmodel"
		>
		  <tabs :editid="editid" @updaterow="updateTab" v-if="visible"></tabs>
		</showbottommodel>
	</template>
  </view>
</template>

<script>
import nuiEmoji from "@/modules/directseeding/components/nui-emoji/nui-emoji.vue";

import showbottommodel from "@/components/basics/showmodel/showmodel.vue";

import tabs from "./tabs.vue";
// const static_ctx = 'http://localhost:3000/';

import { mapState } from "vuex";

import liveLike from "./liveLike.nvue";

export default {
  name: "bottomInput",
  components: {
    showbottommodel,
    nuiEmoji,
    tabs,
    liveLike,
  },
  props: {
	  data:{
		  type:Object,
		  default: function() {
		  	return {};
		  },
	  },
    // 发送评论Loading
    sendBtnLoading: {
      type: Boolean,
      default: false,
    },
    // 横竖屏值
    screenDirectionType: {
      type: [String, Number],
      default: 1, // 1 竖屏 2 横屏
    },
    // 加入粉丝团
    qrCodePath: {
      type: String,
      default: "",
    },
	// title
    title: {
      type: String,
      default: "",
    },
    //是否需要横屏
    screenDirection: {
      type: Boolean,
      default: false,
    },
    showMoreBtn: {
      type: Boolean,
      default: false,
    },
    showtype: {
      type: [String, Number],
      default: 0,
    },
    updatehidden: {
      type: Number,
      default: 0,
    },
    updateclear: {
      type: Number,
      default: 0,
    },
    editid: {
      type: String,
      default: "",
    },
    likestatus: {
      type: Boolean,
      default: false,
    },
    visible2: {
      type: Boolean,
      default: false,
    },
    once: {
      type: Boolean,
      default: false,
    },
    orientation: {
      type: String,
      default: "vertical",
    },
	// 判断是否被插入插槽
	isSlot:{
		type:Boolean,
		default:false
	}
  },
  watch: {
	showMask(n){
		// console.log('遮罩层改变',n);
		this.$emit('changeMask',n)
	},
    visible2(n) {
      // console.log("visible2", n);
      if (n) {
        this.getcommoncollectlikes();
      }
    },
    updatehidden(n) {
	  this.showMask = false;
	  // console.log('触发watchupdatehidden');
      this.focusvisible = false;
      this.emojiShow = false;
      this.keyboardHeight = 0;
    },
    updateclear(n) {
		// console.log('触发watchupdateclear');
      this.focusvisible = false;
      this.emojiShow = false;
      this.focus = false;
      this.keyboardHeight = 0;

      this.comment = "";
      this.cursor = 0;
    },
    likestatus(n) {
      this.likestatus2 = n;
    },
    emojiShow(n) {
      this.$emit("changeEmojiShow", {
        visible: n,
      });
    },
  },
  // mounted(){
  //   setTimeout(() => {
  //     this.visible = true;
  //   })
  // },
  computed: {
    ...mapState("user", {
      curSelectUserInfo: (state) => state.curSelectUserInfo, // 当前选中的用户信息
      accountId: (state) => state.accountId,
      fansRecord: (state) => state.fansRecord,
    }),
  },
  data() {
    return {
    currentPlatform:'',
	  changeIcon:false,
	  showMask:false,
      clickDzVisible: false,
      image1:this.$static_ctx + "image/business/hulu-v2/changeState.png",
	    imageH:this.$static_ctx + "image/business/hulu-v2/changeStateH.png",
      count: 0,
      likeid: null,
      // tabslist:[{
      //   columnContent:{
      //     configJson:'我是HTML文本'
      //   },
      //   name:'图文',
      //   businessType:1,

      // },{
      //   columnContent:{
      //     configJson:'我是学术调研'
      //   },
      //   name:"学术调研",
      //   businessType:5,

      // },{
      //   columnContent:{
      //     configJson:'我是病例征集'
      //   },
      //   name:'病例征集',
      //   businessType:6,

      // }],

      likestatus2: false,
      focusvisible: false,

      xinurl: this.$static_ctx + "image/business/live/icon-im-xin.png",
      xinurl_a: this.$static_ctx + "image/business/live/icon-im-xin-a.png",
      lpurl:this.$static_ctx + "image/business/live/gift.png",
      dzurl: this.$static_ctx + "image/business/live/dz.png",
      gifturl: this.$static_ctx + "image/business/live/shere.png",
      moreurl: this.$static_ctx + "image/business/live/icon-im-more.png",

      tools: [
        {
          ico: this.$static_ctx + "image/business/live/icon-im-xin.png",
          text: "分享",
        },
        {
          ico: this.$static_ctx + "image/business/live/icon-im-noxq.png",
          text: "不感兴趣",
        },
      ],

      updatecount: 0,
      emojiico2:this.$static_ctx + "image/business/live/reply_tool_emoji_a.svg",
      emojiico: this.$static_ctx + "image/business/live/smilingFace.png",
      visible: false,
	    keyboard:this.$static_ctx + "image/business/live/keyboard.png",
      // 评论输入框
      keyboardHeight: 0,
      lineHeight: 60,
      functionShow: false,
      emojiShow: false,
      comment: "",
      focus: false,
      cursor: 0,
      _keyboardShow: false,
      emojiSource: this.$static_ctx + "image/system/bg-face.png",
      // emojiSource: 'https://res.wx.qq.com/op_res/eROMsLpnNC10dC40vzF8qviz63ic7ATlbGg20lr5pYykOwHRbLZFUhgg23RtVorX',
      parsedComment: [
        {
          type: 2,
          content: "[憨笑]",
          imageClass: "smiley_28",
        },
        {
          type: 1,
          content: "44",
        },
        {
          type: 2,
          content: "[呲牙]",
          imageClass: "smiley_13",
        },
        {
          type: 2,
          content: "[调皮]",
          imageClass: "smiley_12",
        },
      ],
    };
  },
  mounted() {
	  // console.log('qrCodePath',this.qrCodePath);
    if (this.once && this.visible2) {
      this.getcommoncollectlikes();
    }
    const systemInfo = uni.getSystemInfoSync();
    systemInfo.platform === 'ios' ? this.currentPlatform = 'ios' : this.currentPlatform = 'android';
  },
  methods: {
	  // 检测是否登陆
	  checkIsLogin(){
		console.myLog('触发检测1')
	  	this.$emit('checkIsLogin');
	  },
    // 加入粉丝团
    addUser() {
      this.$emit("addUser", {});
      // this.showbottomVisible = true;
    },
    toggleVertical() {
      this.$emit("fullscreenchange", {
        detail: {
          orientation:
            this.orientation == "vertical" ? "horizontal" : "vertical",
        },
      });
    },
    clickHandler(data) {
	  console.myLog('this.clickDzVisible',this.clickDzVisible)
      if (this.clickDzVisible) {
        return;
      }
      this.clickDzVisible = true;
      setTimeout(() => {
        this.clickDzVisible = false;
      }, 200);
      this.count += 1;
      //点赞接口调用
      this.getUserInfo().then(() => {
        this.clickgood(...arguments);
      });
    },

    //点赞
    clickgood() {
      // console.log('this.$api.cloudClassroom.getcommoncollectlikes',this.$api.cloudClassroom.getcommoncollectlikes)
      const { centerUserId = "" } = this.curSelectUserInfo || {};
      // 分享操作
      this.$api.cloudClassroom.postcommoncollectlikes({
        businessType: 9,
        businessId: this.editid,
        // id:this.editid,
        source: 1, // 1-真实用户，2-马甲用户,3-系统用户
        accountId: this.accountId,
        userId: centerUserId,
        type: 2, // 点赞 2
      });
      // 点赞行为流水
      // this.postcommoncollect2(9);
    },
    updateTab(obj) {
      // console.log(obj);
      this.$emit("clicktab", obj);
      this.visible = false;
    },
    // 行为流水
    postcommoncollect2(type) {
      this.getUserInfo().then(() => {
        const { centerUserId = "" } = this.curSelectUserInfo || {};
        // 分享操作
        this.$api.cloudClassroom.postcommoncollectlikes({
          businessType: 9,
          businessId: this.editid,
          // id:this.editid,
          source: 1, // 1-真实用户，2-马甲用户,3-系统用户
          accountId: this.accountId,
          userId: centerUserId,
          type: type,
        });
      });
    },
    share() {
      // console.log("share");
      this.getUserInfo().then(() => {
        const { centerUserId = "" } = this.curSelectUserInfo || {};
        // 分享操作
        this.$api.cloudClassroom.postcommoncollectlikes({
          businessType: 9,
          businessId: this.editid,
          // id:this.editid,
          source: 1, // 1-真实用户，2-马甲用户,3-系统用户
          accountId: this.accountId,
          userId: centerUserId,
          // type: '13'
          type: "5",
        });
      });
    },
    // 获取点赞状态
    getcommoncollectlikes() {
      // console.log("getcommoncollectlikes");
      // return;
      // this.getUserInfo().then(() => {
      //   // console.log('this.$api.cloudClassroom.getcommoncollectlikes',this.$api.cloudClassroom.getcommoncollectlikes)
      //   const { centerUserId = '' } = this.curSelectUserInfo || {};
      //   this.$api.cloudClassroom
      //     .getcommoncollectlikes({
      //       // type:2,
      //       businessType: 9,
      //       businessId: this.editid,
      //       // id:this.editid,
      //       source: 1, // 1-真实用户，2-马甲用户,3-系统用户
      //       accountId: this.accountId,
      //       userId: centerUserId,
      //       type: '2' // 类型：1-评论，2-点赞，3-收藏，4-阅读，5-分享
      //     })
      //     .then(ret => {
      //       // console.log('ret', ret);
      //       // console.log('kkkkkk');
      //       if (ret.data && ret.data.length != 0) {
      //         if (ret.data[0].subscribeStatus == 1) {
      //           this.likestatus2 = true;
      //         } else {
      //           this.likestatus2 = false;
      //         }
      //         // this.likeid = ret.data[0].id;
      //       } else {
      //         this.likestatus2 = false;
      //       }
      //     });
      // });
    },
    async likeComment(data) {
      const { centerUserId = "" } = this.curSelectUserInfo || {};
      const params = {
        businessId: this.editid,
        source: 1, // 1-真实用户，2-马甲用户,3-系统用户
        accountId: this.accountId,
        userId: centerUserId,
        type: "2", // 类型：1-评论，2-点赞，3-收藏，4-阅读，5-分享
        // businessType: 7 // 直播
        businessType: 9,
      };
      // 是否已点赞
      if (this.likestatus2) {
        // 取消点赞
        // let valid = this.$api.cloudClassroom.cannelcommoncollectlikes({
        let valid =
          this.$api.cloudClassroom.postcommoncollectlikesupdate(params);
        // if(valid)
        // console.log('valid',valid)
        // : 1-访问,2-点击,3-长按，4-关注，5-取关,6-点赞,7-收藏，8-删除评论，9-取消点费，10-取消收藏，1-阅读，12新增评论
        this.postcommoncollect2(9);
      } else {
        // 新增点赞
        // this.$api.cloudClassroom.postcommoncollectlikes(params);
        this.$api.cloudClassroom.postcommoncollectlikesupdate(params);
        // : 1-访问,2-点击,3-长按，4-关注，5-取关,6-点赞,7-收藏，8-删除评论，9-取消点费，10-取消收藏，1-阅读，12新增评论
        this.postcommoncollect2(6);
      }

      // this.getcommoncollectlikes();

      this.likestatus2 = !this.likestatus2;
    },
    like(data) {
      this.getUserInfo().then(() => {
        this.likeComment(...arguments);
      });
    },

    getUserInfo() {
      return this.$ext.user.authCommunityFansInfo();
    },

    hideAllPanel() {
      this.functionShow = false;
      this.emojiShow = false;
    },
    onBlur(e) {
      // console.log('触发失去焦点');
      this._keyboardShow = false;
      this.cursor = e.detail.cursor || 0;
      this.focusvisible = false;
      this.keyboardHeight = 0;
      this.focus = false;
    // this.changeIcon = true;
    },
    onFocus() {
      this._keyboardShow = true;
      console.myLog('检测聚焦后触发this.focusvisible')
      this.focusvisible = true;
      this.changeIcon = false;
      this.hideAllPanel();
    },
    focusInput() {
		// 检测下是否登陆
	  this.checkIsLogin();
      console.myLog('检测点击输入框触发this.focusvisible')
      this.showMask = true;
      // console.log('触发点击',this.$data);
      this.focus = true;
    },
    showMoreClick() {
      this.visible = true;
    },
    cancel() {
      this.visible = false;
    },

    _cancelEvent(e) {
      // console.log("你点击了取消");
      this.isShown = false;
    },

    _confirmEvent(e) {
      // console.log("你点击了确定");
      this.isShown = false;
    },
    showDialog() {
      // console.log("执行了");
      this.isShown = true;
    },
    startDrop(e) {
      // var count = e.target.dataset.count;
      if (!this.dropHeight) {
        this.dropHeight = true;
      }
    },
    Comment(e) {
      // console.log(e)
      var type = e.target.dataset.type;
      var isComment = false;
      if (type == "open") {
        isComment = true;
      }
      this.isComment = isComment;
      this.dropHeight = false;
    },
    // // 发送评论
    // sendPL(){
    //   console.log('点击了评论')
    // },
    // 评论
    onkeyboardHeightChange(e) {
      // console.log("键盘抬起", e);
      const { height } = e.detail;
      this.keyboardHeight = height;
    },

    showEmoji(visible) {
      // console.log('触发展示emij');
		this.checkIsLogin()
		// 当当前是唤起键盘模式 让键盘聚焦
		if(this.changeIcon && this.showMask){
			this.changeIcon = false;
			this.focus = false;
      console.myLog('检测唤起表情包模式触发this.focus')
			this.$nextTick(()=>{
				this.focus = true;
			})
			// 当前是唤起表情包模式
		}else{
          console.myLog('检测唤起表情包模式触发this.focusvisible')
			this.focusvisible = true;
			this.changeIcon = true;

		}
		// console.log('触发列表',this.changeIcon);
      if (visible) {
		  this.showMask = true;
        if (this.updatecount == 0) {
          this.updatecount += 1;
        }
        this.functionShow = false;
        this.emojiShow = this._keyboardShow || !this.emojiShow;
      } else {
        this.keyboardHeight = 0;
        this.$nextTick(() => {
          if (this.updatecount == 0) {
            this.updatecount += 1;
          }
          this.functionShow = false;
          this.emojiShow = this._keyboardShow || !this.emojiShow;
        });
      }
    },
    showFunction() {
      this.functionShow = this._keyboardShow || !this.functionShow;
      this.emojiShow = false;
    },
    chooseImage() {},

	touchMask(){
		this.focusvisible = false;
		this.emojiShow = false;
		this.keyboardHeight = 0;
		this.showMask = false;
	},
	onInput(e) {
      const value = e.detail.value;
      this.comment = value;
	  console.myLog('comment',this.comment);
    },
    onConfirm() {
      this.onsend();
    },
    insertEmoji(evt) {
      const emotionName = evt.emotionName;
      const { cursor, comment } = this;
      const newComment =
        comment.slice(0, cursor) + emotionName + comment.slice(cursor);

      this.comment = newComment;
      this.cursor = cursor + emotionName.length;
    },
    onsend() {
      const comment = this.comment;
      console.log(comment);
      // const parsedComment = this.parseEmoji(this.comment);
      let parsedComment = this.$refs.emojiRef.parseEmoji(this.comment);
      console.log(parsedComment);
	  this.showMask = false;
      // this.focusvisible = false;

      this.$emit("send", {
        comment: this.comment,
      });
    },
    deleteEmoji: function () {
      const pos = this.data.cursor;
      const comment = this.data.comment;
      let result = "",
        cursor = 0;

      let emojiLen = 6;
      let startPos = pos - emojiLen;
      if (startPos < 0) {
        startPos = 0;
        emojiLen = pos;
      }
      const str = comment.slice(startPos, pos);
      const matchs = str.match(/\[([\u4e00-\u9fa5\w]+)\]$/g);
      // 删除表情
      if (matchs) {
        const rawName = matchs[0];
        const left = emojiLen - rawName.length;
        if (this.emojiNames.indexOf(rawName) >= 0) {
          const replace = str.replace(rawName, "");
          result = comment.slice(0, startPos) + replace + comment.slice(pos);
          cursor = startPos + left;
        }
        // 删除字符
      } else {
        let endPos = pos - 1;
        if (endPos < 0) endPos = 0;
        const prefix = comment.slice(0, endPos);
        const suffix = comment.slice(pos);
        result = prefix + suffix;
        cursor = endPos;
      }

      this.comment = result;
      this.cursor = cursor;
    },
  },
};
</script>

<style lang="scss" scoped>
  .textBox{
    width: 100vw;
    height: 300px;
    background: white;
  }
	.mask{
		width: 100vw;
		height: 100vh;
		background: rgba(0,0,0,0.6);
		position: fixed;
		top: 0;
		left: 0;
		z-index: 99;
	}
	.horizontalIconBox{
		position: fixed;
		bottom: 75rpx;
		left: 20rpx;
		z-index: 998;
		.iconbox{
			position: static !important;
		}
		.t-goodico{
			margin-top: 18rpx;
		}
	}
	  .m-space {
		height: 100%;
		width: 20upx;
	  }
	  .iconbox3 {
	    width: 70rpx;
	    height: 70rpx;
	    border-radius: 50%;
	    // background-color: rgba(0, 0, 0, 0.4);
	    display: flex;
	    align-items: center;
	    justify-content: center;
	    margin-left: 24rpx;
	    position: relative;
	    margin-left: auto;
	  }
	  .emoji-box {
	    display: flex;
	    align-items: center;
	  }
  .likeBtn{
    transform: scale(1.5);
    transition: transform .2s ease-in-out; /* 过渡效果 */
  }
  .iconbox.zhufico{
    position:absolute;
    right: 20upx;
    bottom: 195upx;
    transform: rotate(90deg);
	z-index: 998;
  }
  .box-operation{
    position: relative;
  }
  .t-goodico{
    // bottom: 200upx;
    position: absolute;
	z-index: 998;
    bottom: 185upx;
    left: 20upx;
    transform: rotate(90deg);
  }
  .orientationico{
    position: fixed;
    left: 50%;
    bottom: 75rpx;
	z-index: 998;
  }
.reply_input_h5 {
  line-height: 35px;
}
.share-btn{
	width: 100%;
	height: 100%;
	.reply_tool_pic{
		width: 100%;
		height: 100%;
	}
}
.share-btn::after {
  border: none;
}
.changeANDROID{
	position: fixed !important;
	top: 52% !important;
	right: 34rpx;
	z-index: 998;
	width: 50rpx !important;
	height: 52rpx !important;
	.reply_tool_pic{
		width: 82%;
		height: 83%;
	}
}
.changeANDROIDH{
	width: 50rpx !important;
	height: 52rpx !important;
	.reply_tool_pic{
		width: 82%;
		height: 83%;
		transform: rotate(90deg);
	}
}
.iconbox {
  width: 70upx;
  height: 70upx;
  border-radius: 50%;
  // overflow: hidden;
  // 暂时注释掉底色
  // background-color: rgba(20, 16, 16, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 24upx;
  position: relative;
}

.sliderbox2 {
  position: absolute;
  right: 20upx;
  z-index: 998;
  // background-color: rgba(0, 0, 0, 0.4);
  top: 58.5vh;
  transform: translateY(-50%);
  border-radius: 20upx;
  // overflow: hidden;
  padding: 20upx;
  z-index: 998;

  .reply_button {
    // margin-bottom: 20upx;
	width: 100%;
	height: 100%;
  }

  .iconbox {
    margin-left: 0;
    margin-bottom: 20upx;
  }
}

.showbottominputbox {
  display: flex;

  .showbottominputitem {
    min-width: 25%;
    width: 25%;
    // height: 100upx;
    color: #fff;
    // background-color: #1aad19;
  }

  .showbottominputico {
    width: 60upx;
    height: 60upx;
  }
}

/* 评论输入框 */
.sendBtn {
  color: #fff;
  margin-left: 20upx;
  font-weight: 500;
  font-size: 28rpx;
  width: 120rpx;
  height: 72rpx;
  background: #00B484;
  border-radius: 36rpx;
}

.reply_wrp {
  position: fixed;
  bottom: 0;
  width: 100%;
  z-index: 998;
  // background-color: #F9F9F9;
}

.reply_tool {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  font-size: 0;
  padding: 16rpx 24rpx;
  // background-color: #F9F9F9;
  // background-color: rgba(0, 0, 0, 0.4);
  position: relative;
}

.bgf.reply_tool {
  // background-color: #fff;
}

.reply_tool:before {
  content: "";
  position: absolute;
  z-index: 998;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1 rpx solid rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.1);
}

.reply_form_wrp {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  min-width: 0;
}

.textareaFaBox{
	width: 100%;
	min-height: 72rpx;
	max-height: 216rpx;
	overflow: scroll;
	position: relative;
	 scrollbar-width: none; /* 针对Firefox */
	  -ms-overflow-style: none; /* IE 和 Edge */
	.placeholderBox{
		position: absolute;
		line-height: 70rpx;
		width: 100%;
		height: 100%;
		padding-left: 32rpx;
		font-weight: 500;
		font-size: 26rpx;
		color: rgba(255,255,255,0.6);
	}
  .promptBox{
    width: 100%;
    text-align: right;
    padding-right: 10rpx;
    box-sizing: border-box;
    padding-bottom: 8rpx;
    font-size: 20rpx;
    color: rgba(255,255,255,0.6);
  }
}
.textareaFaBox::-webkit-scrollbar {
    display: none;
  }
.reply_label {
  display: flex;
  align-items: center;
  min-height: 72rpx;
  max-height: 216rpx;
  background: rgba(0, 0, 0, 0.3);
  color: #fff;
  border-radius: 36rpx;
  // margin-right: 16rpx;
  display: flex;
  align-items: center;
}
.focusStyle{
	  background: rgba(255,255,255,0.3);
}
.reply_input {
  font-size: 24upx;
  min-height: 35px;
  /* height: 40px;  */
  padding: 0 12px;
  // width: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-border-radius: 4px;
  // border-radius: 4px;
  caret-color: #1aad19;
  // background-color: rgba(0, 0, 0, 0.2);
  border-radius: 30rpx;

  // background-color: rgb(145, 138, 139);
  text-align: left;
  // width: 50%;
  width: 100%;
}

.reply_input2 {
  font-size: 24upx;
  min-height: 70rpx;
  height: 70rpx;
  // line-height: 70rpx;
  color: #fff;
  padding: 18rpx 12rpx 0rpx 32rpx;
  width: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  caret-color: #1aad19;
  text-align: left;
  // background: #fff;
  // border: 1rpx solid #dbdbdb;
  // background-color: rgba(0, 0, 0, 0.5);
  border-radius: 20upx;
}

// .reply_input[disabled] {
//   background-color: #e8e8e8;
//   color: #888888;
// }
.reply_button {
  // width: 64rpx;
  // height: 64rpx;

  width: 100%;
  height: 100%;
  overflow: hidden;
  margin-right: 8px;
}

.miniIcon {
  padding: 14rpx 16rpx 14rpx 0;
  width: 44rpx;
  height: 44rpx;
}

.reply_button.active {
  transform: scale(0.9);
}

.reply_button image:active {
  opacity: 50%;
}

.reply_button:last-child {
  margin-right: 0;
}

.reply_panel_wrp {
  height: 400rpx;
  overflow: hidden;
  position: relative;
  background: #333333;
  // background: #fff;
}

.reply_panel {
  height: 400rpx;
  overflow: auto;
  position: absolute;
  left: 0;
  z-index: 998;
  right: 0;
  top: 300px;
  -webkit-transition: top 0.2s;
  transition: top 0.2s;
}

.reply_panel[hidden] {
  display: block;
}

.reply_panel.show {
  height: 300px;
  top: 0;
}

.reply_panel:before {
  content: "";
  position: absolute;
  z-index: 998;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1 rpx solid rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.1);
}

.reply_quick_item {
  position: relative;
  padding: 32rpx 24rpx;
  font-size: 34rpx;
  // background-color: #FFFFFF;
  border-radius: 8rpx;
  margin: 16rpx;
  color: rgba(0, 0, 0, 0.9);
  word-wrap: break-word;
  word-break: break-all;
}

.reply_quick_item:last-child {
  margin-bottom: 0;
}

.reply_quick_item:after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-top: 1 rpx solid rgba(0, 0, 0, 0.05);
  color: rgba(0, 0, 0, 0.05);
}

.reply_quick_item:last-child:after {
  display: none;
}

.reply_quick_tool {
  text-align: center;
  padding: 32rpx 24rpx;
  font-size: 28rpx;
}

.reply_quick_tool navigator {
  color: #204495;
}

.reply_quick_empty {
  position: absolute;
  z-index: 998;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  text-align: center;
  font-size: 34rpx;
}

.reply_quick_empty:active image {
  opacity: 0.5;
}

.pic_reply_quick_edit {
  vertical-align: middle;
  width: 36rpx;
  height: 36rpx;
  vertical-align: middle;
  margin-top: -7rpx;
  margin-right: 16rpx;
}

.reply_quick_empty:after {
  content: "";
  display: inline-block;
  vertical-align: middle;
  width: 0;
  height: 100%;
}

.reply_quick_empty navigator {
  color: #000000;
}

.function_list {
  text-align: justify;
}

.function_item {
  display: inline-block;
  vertical-align: middle;
  padding: 24rpx;
}

.function_item .reply_function_pic.active {
  display: none;
}

.function_item:active .reply_function_pic {
  display: none;
}

.function_item:active .reply_function_pic.active {
  display: inline-block;
}

.reply_function_pic {
  width: 128rpx;
  height: 128rpx;
}
</style>
