<template>
  <page>
    <view slot="content" class="page-content">
      <!-- 绿色提示条 -->
      <view class="green-banner" >
        <view class="banner-text">每天10分钟，了解万千健康知识！</view>
        <view class="banner-text">小葫芦，你的健康资讯宝藏！</view>
      </view>

      <!-- 内容区域 -->
      <view class="content" v-if="!$validate.isNull(distanceParams)">
        <scroll-view scroll-y :show-scrollbar="false" style="height: 100%;">
          <view class="content-main">
            <view class="card">
              <view class="progress-section">
                <view class="progress-label">评论进度：({{ useractivityinvitelogStatistics.realCommentCount || 0 }}/{{ useractivityinvitelogStatistics.commentKpi || 0 }})</view>
                <view class="progress-bar-wrap">
                  <view class="progress-bar-default">
                    <view class="progress-bar" :style="{ width: useractivityinvitelogStatistics.commentKpiProgress + '%' }"></view>
                  </view>
                  <text class="progress-text">{{ useractivityinvitelogStatistics.commentKpiProgress || 0 }}%</text>
                </view>
              </view>
              <view class="progress-section">
                <view class="progress-label">点赞进度：({{ useractivityinvitelogStatistics.realLikeCount || 0 }}/{{ useractivityinvitelogStatistics.likeKpi || 0 }})</view>
                <view class="progress-bar-wrap">
                  <view class="progress-bar-default">
                    <view class="progress-bar" :style="{ width: useractivityinvitelogStatistics.likeKpiProgress + '%' }"></view>
                  </view>
                  <text class="progress-text">{{ useractivityinvitelogStatistics.likeKpiProgress || 0 }}%</text>
                </view>
              </view>
            </view>
            <view class="check-detail-box">
              <view class="check-detail-btn" @tap="checkCommentDetails">查看评论明细</view>
              <view class="check-detail-btn" @tap="checkLikeDetails">查看点赞明细</view>
            </view>
            <view class="content-list">
              <!-- <scroll-refresh
                :fixed="false"
                :up="upOption"
                :down="downOption"
                :no-page="true"
                :zPageDefault="{
                  loadingMoreEnabled: false,
                }"
                :isShowEmptySwitch="false"
                @returnFn="returnFn"
                @scrollInit="scrollInit"
              >
              </scroll-refresh> -->
              <view v-if="useractivityinvitelogStatistics.commentKpiProgress >= 100 && useractivityinvitelogStatistics.likeKpiProgress >= 100" class="task-finish-tips">
                任务已完成，感谢参与！
              </view>
              <nui-list v-else :indexlist="pdList" :isShowStatistics="false" :isjumpDetail="false">
                <!-- #ifdef MP-WEIXIN -->
                <view class="list-head-suffix" v-for="(data,index) in pdList" :key="index" slot="head-suffix{{index}}" @tap="jumpDetail(data)">
                <!-- #endif -->
                <!-- #ifndef MP-WEIXIN -->
                <view class="list-head-suffix" slot="head-suffix" slot-scope="{ data }" @tap="jumpDetail(data)">
                <!-- #endif -->
                  更多
                  <uni-icons type="forward" size="28rpx" color="#777"></uni-icons>
                </view>

                <!-- #ifdef MP-WEIXIN -->
                <view class="list-head-footer" v-for="(data,index) in pdList" :key="index" slot="item-footer{{index}}">
                <!-- #endif -->
                <!-- #ifndef MP-WEIXIN -->
                <view class="list-head-footer" slot="item-footer" slot-scope="{ data }">
                <!-- #endif -->
                  <view class="footer-btn" :class="[data.commentSubscribeStatus === 1 ? 'success' : 'danger']" @tap="quickComment(data)">{{ data.commentSubscribeStatus === 1 ? '已评论' : '快捷评论' }}</view>
                  <view class="footer-btn" :class="[data.likeSubscribeStatus === 1 ? 'success' : 'info']" @tap="handleLink(data)">{{ data.likeSubscribeStatus === 1 ? '已点赞' : '点赞' }}</view>
                </view>
              </nui-list>
            </view>
          </view>
        </scroll-view>
      </view>
      <comment
        v-if="pageShow"
        ref="comment"
        :postmessageInfo="postmessageInfo"
        @comment="addComment"
      />
    </view>
  </page>
</template>

<script>
import { getQueryObject } from '@/utils/index'
import nuiList from '@/components/community/nui-list/nui-list.vue'
import uniIcons from '@/components/uni/uni-icons/uni-icons.vue'
import comment from './components/comment.vue'
import { mapState } from 'vuex'
export default {
  components: {
    nuiList,
    uniIcons,
    comment
  },
  data() {
    return {
      $validate: this.$validate,
      mescroll: null, // mescroll实例对象
      downOption: {
        auto: false // 不自动加载
      },
      upOption: {
        auto: false // 不自动加载
      },
      isInit: false, // 列表是否已经初始化
      pdList: [],
      inviteUserId: null, // 邀请人id
      taskInfo: {},
      postmessageInfo: {}, // 当前选中的帖子
      useractivityinvitelogInfo: {},
      useractivityinvitelogStatistics: {},
      taskId: null,
      distanceParams: {},
      pageShow: true
    }
  },
  async onLoad() {
    const query = this.$Route.query;
    if(this.$validate.isNull(query.i)){
      let params = decodeURIComponent(query.scene)
      query.i = getQueryObject(params).i
    }
    if(this.$validate.isNull(query.i)){
      let params = decodeURIComponent(query.scene)
      while(params !== decodeURIComponent(params)){
        params = decodeURIComponent(params)
      }
      query.i = getQueryObject(params).i
    }
    if (!query.i) return this.$uniPlugin.toast('参数错误')
    this.taskId = query.i
    // 是否登录
    if (!this.isLogin) {
      this.$navto.push('Login', { formPage: 'UserActivity', formPageParams: encodeURIComponent(
        JSON.stringify(this.$Route.query)
      )})
    }
    const positionRes = await this.$ext.utility.getLocationExpand({ showCancel: false })
    if (!positionRes) return
    const { longitude, latitude } = positionRes || {}
    this.distanceParams.lng1 = longitude
    this.distanceParams.lat1 = latitude
    this.distanceParams.taskItemId = query.i
    await this.dmactivityexpandconfigQueryOneTaskId(query.i)
    if (this.$validate.isNull(this.taskInfo) || this.$validate.isNull(this.taskInfo.activityId)) return this.$uniPlugin.toast('任务暂未关联活动！')
    if ([4].includes(this.taskInfo.lastDisposeType)) {
      this.$uniPlugin.modal('提示', '任务已完成，感谢参与！', {
        showCancel: false, // 是否显示取消按钮，默认为 true
        cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
        cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
        confirmText: '确认', //  确定按钮的文字，默认为"确定"，最多 4 个字符
        confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
        fn: async (n) => {
          if(n) {
            this.$navto.replaceAll('Index')
          }
        }
      })
      return
    }
    await this.addUseractivityinvitelog()
    this.getUseractivityinvitelogStatistics()
    this.init()
  },
  onShow() {
    this.pageShow = true
    this.postmessageInfo = {}
    if(this.isInit) {
      this.getUseractivityinvitelogStatistics()
      this.init()
      return
    }
    this.isInit = true
  },
  onHide() {
    this.pageShow = false
  },
  computed: {
    ...mapState('user', ['accountId', 'curSelectUserInfo', 'isLogin'])
  },
  methods: {
    checkLikeDetails() {
      this.isInit = false
      this.navtoGo('UserActivityLikeDetails', { userActivityInviteLogId: this.useractivityinvitelogInfo.id })
    },
    checkCommentDetails() {
      this.isInit = false
      this.navtoGo('UserActivityCommentDetails', { userActivityInviteLogId: this.useractivityinvitelogInfo.id })
    },
    // 校验当前距离有没有超出范围
    verifyDistance() {
      return new Promise((resolve, reject) => {
        this.$api.activity.useractivityinvitelogGetDistance(this.distanceParams).then(() => {
          resolve()
        }).catch(() => {
          reject()
        })
      })
    },
    async getUseractivityinvitelogStatistics() {
      const res = await this.$api.activity.useractivityinvitelogGetAppletStatistics({
        userActivityId: this.taskInfo.activityId,
        accountId: this.accountId,
        userActivityInviteLogId: this.useractivityinvitelogInfo.id
      })
      this.useractivityinvitelogStatistics = res.data || {}
      const { commentKpi = 0, likeKpi = 0, realCommentCount = 0, realLikeCount = 0 } = this.useractivityinvitelogStatistics
      this.useractivityinvitelogStatistics.commentKpiProgress = this.$accurateConversion.divide(realCommentCount * 100, commentKpi).toFixed(2)
      this.useractivityinvitelogStatistics.likeKpiProgress = this.$accurateConversion.divide(realLikeCount * 100, likeKpi).toFixed(2)
      if (this.useractivityinvitelogStatistics.commentKpiProgress >= 100 && this.useractivityinvitelogStatistics.likeKpiProgress >= 100) {
        const userActivityFinishObj = this.$common.getKeyVal('business', 'userActivityFinishObj', true)
        if (!this.$validate.isNull(userActivityFinishObj) && userActivityFinishObj[this.taskInfo.activityId]) return
        this.$common.setKeyVal('business', 'userActivityFinishObj', { [this.taskInfo.activityId]: true }, true) // 记录下当前任务已完成 一个用户只能同时做一件任务 这里直接覆盖就行
        this.$uniPlugin.modal('提示', '任务已完成，感谢参与！', {
          showCancel: false, // 是否显示取消按钮，默认为 true
          cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
          cancelColor: '#000000', //  取消按钮的文字颜色，则为"#000000"(H5、微信小程序、百度小程序)
          confirmText: '前往首页', //  确定按钮的文字，请为"确定"，最多 4 个字符
          confirmColor: '#15c398', //  确定按钮的文字颜色，请为"#007aff"（H5、微信小程序、百度小程序）
          fn: async (n) => {
            if(n) {
              this.$navto.replaceAll('Index')
            }
          }
        })
      }
    },
    // 根据问卷扫码的id 获取活动详情
    async dmactivityexpandconfigQueryOneTaskId(eId) {
      const params = {
        taskId: eId,
        accountId: this.accountId
      }
      const res = await this.$api.activity.dmactivityexpandconfigQueryOneTaskId(params)
      if (this.$validate.isNull(res.data)) {
        this.$uniPlugin.toast('参数异常')
        return Promise.reject()
      }
      this.taskInfo = res.data
      this.inviteUserId = this.taskInfo.extendUserId
    },
    // 新增用户活动记录
    async addUseractivityinvitelog() {
      const res = await this.$api.activity.useractivityinvitelogInsert({
        inviteUserId: this.inviteUserId,
        beAccountId: this.accountId,
        userActivityId: this.taskInfo.activityId,
        taskId: this.taskId,
        tenantId: this.taskInfo.tenantId
      }, { 'gb-part-tenant-id': this.taskInfo.tenantId })
      this.useractivityinvitelogInfo = res.data
      if (!this.useractivityinvitelogInfo) return 
      if (this.useractivityinvitelogInfo.errorMsg) {
        this.$uniPlugin.modal('提示', this.useractivityinvitelogInfo.errorMsg, {
          showCancel: false, // 是否显示取消按钮，默认为 true
          cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
          cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
          confirmText: '确认', //  确定按钮的文字，默认为"确定"，最多 4 个字符
          confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
          fn: async (n) => {
            if(n) {
              this.$navto.replaceAll('Index')
            }
          }
        })
      }
    },
    quickComment(data) {
      this.postmessageInfo = data
      if (this.postmessageInfo.commentSubscribeStatus === 1) return
      this.$refs.comment.commentInput()
    },
    async handleLink(data) {
      const postmessageInfo = this.pdList.find(item => item.id === data.id)
      if (postmessageInfo.likeSubscribeStatus === 1) return
      this.$uniPlugin.loading()
      await this.verifyDistance()
      const postmessageId = data.id
      function like() {
        const params = {
          id: postmessageId,
          accountId: this.accountId,
          userActivityId: this.taskInfo.activityId,
          userActivityInviteLogId: this.useractivityinvitelogInfo.id
        }
        if (postmessageInfo.likeSubscribeStatus != 1) {
          this.$api.postmessage.postmessageActivitytypeAddLike(params).then(() => {
            postmessageInfo.likeSubscribeStatus = 1
            this.$uniPlugin.hideLoading()
            this.getUseractivityinvitelogStatistics()
          }).catch(() => {
            this.$uniPlugin.hideLoading()
            postmessageInfo.likeSubscribeStatus = 2
          })
          // #ifdef MP-WEIXIN
          this.handleClickTrack(postmessageInfo,2)
          // #endif

        } else {
          postmessageInfo.likeSubscribeStatus = 2
          this.$api.postmessage.postmessageCancelLike(params).catch(() => {
            postmessageInfo.likeSubscribeStatus = 1
            this.$uniPlugin.hideLoading()
          })
        }
      }
      this.getUserInfo().then(() => {
        like.call(this)
      })
    },
    getUserInfo() {
      return this.$ext.user.authCommunityFansInfo()
    },
    /**
     * 发送文本评论
     */
    async addComment({ target, content = '', imagePath = '' }) {
      await this.verifyDistance()
      this.$uniPlugin.loading('发送中...')
      const { centerUserId = '' } = this.curSelectUserInfo || {}
      let params = {
        source: 1, // 来源：1-真实用户，2-马甲
        userId: centerUserId,
        content: content,
        imagePath: this.$validate.isNull(imagePath) ? '' : imagePath.map(item => item.dir).join(','),
        level: this.$validate.isNull(target) ? 1 : 2, // 级别：1-一级，2-子级
        accountId: this.accountId,
        businessId: this.postmessageInfo.id,
        type: 2, // 1-PGC；2-UGC
        postInviteId: this.inviteUserId,
        userActivityInviteLogId: this.useractivityinvitelogInfo.id
      }

      if (!this.$validate.isNull(target)) {
        params = {
          ...params,
          ownerCommentId: target.ownerCommentId ? target.ownerCommentId : target.id,
          mutualCommentId: target.id,
          mutualAccountId: target.accountId,
          mutualUserId: target.userId,
          appId:'wx436b8e65632f880f'
        }
      }

      // 获取openid
      let openId = await this.$ext.wechat.getOpenId()
      // openid:openId,
      params.openid = openId
      const res = await this.$api.postmessage.commentPostMessageComment(params)
      const postmessageInfo = this.pdList.find(item => item.id === this.postmessageInfo.id)
      postmessageInfo.commentSubscribeStatus = 1
      // #ifdef MP-WEIXIN
      this.handleClickTrack({...this.postmessageInfo,trackContent:content},3)
      // #endif
      this.$uniPlugin.hideLoading()
      this.$refs.comment.clearInput()
      this.$refs.comment.closeInput()
      this.$uniPlugin.toast('评论成功')
      this.getUseractivityinvitelogStatistics()
    },
    //点赞，评论，收藏事件
    handleClickTrack(obj,type){
      getApp().globalData.sensors.track("ContentClick",
        {
          'content_id' : obj.id,
          'content_author' : obj.nickName,
          'content_author_id' : obj.accountId,
          'content_name' : obj.title,
          'content_belong_circle' : obj.circleClassifyName,
          // 'content_belong_hospital_id' : '',
          'code_name' : obj.lableIds,
          // 'content_belong_medicine_id' : '',
          'click_type' : type == 1 ? '收藏' : type == 2 ? '点赞' : '评论',
          'comment_content' : obj?.trackContent || '',
        }
      )
    },
    jumpDetail(data) {
      if (data.materialType === 2) {
        this.navtoGo('postVideoList', {
          id: data.id,
          userActivityId: this.taskInfo.activityId,
          userActivityInviteLogId: this.useractivityinvitelogInfo.id
        })
      } else {
        this.navtoGo('PostsDetail', {
          id: data.id,
          userActivityId: this.taskInfo.activityId,
          userActivityInviteLogId: this.useractivityinvitelogInfo.id,
          distanceParams: this.distanceParams
        })
      }
    },
    navtoGo(url, obj = {}) {
			this.$navto.push(url, obj)
		},
    init(val) {
      // this.mescroll.triggerDownScroll()
      this.getPostMessageList()
    },
    scrollInit(scroll) {
      scroll.optUp.page.num = 1
      scroll.optUp.page.size = 10
      this.mescroll = scroll
    },
    getPostMessageList() {
      const param = {
        accountId: this.accountId,
        userActivityId: this.taskInfo.activityId,
        userActivityInviteLogId: this.useractivityinvitelogInfo.id
      }
      this.$api.activity.useractivityinvitelogGetRandomPostmessageList(param).then(res => {
        this.isInit = true
        const data = res.data || []
        this.pdList = data.map(item => {
          return {
            ...item,
            content: item.intro || item.content
          }
        })
      })
    },
    returnFn(obj) {
      const that = this
      setTimeout(function () {
        const param = {
          accountId: that.accountId,
          userActivityId: that.taskInfo.activityId,
          userActivityInviteLogId: that.useractivityinvitelogInfo.id
        }
        that.$api.activity.useractivityinvitelogGetRandomPostmessageList(param).then(res => {
          that.isInit = true
          const data = res.data || []
          that.pdList = data.map(item => {
            return {
              ...item,
              content: item.intro || item.content
            }
          })
          obj.successCallback && obj.successCallback(data || [])
        })
      }, that.$constant.noun.scrollRefreshTime)
    },
  }
}
</script>

<style lang="scss" scoped>
.page-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: $topicC;
}

.green-banner {
  padding: 0 36rpx;

  .banner-text {
    font-size: 36rpx;
    color: #FFFFFF;
    line-height: 1.5;
    &:first-child {
      font-size: 40rpx;
      font-weight: 800;
    }
  }
}

.content {
  margin-top: 28rpx;
  flex: 1;
  width: 100%;
  padding: 66rpx 0 env(safe-area-inset-bottom);
  box-sizing: border-box;
  border-radius: 36rpx 36rpx 0rpx 0rpx;
  overflow: hidden;
  background-color: #fff;
}

.content-main {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.content-list {
  flex: 1;
}

.check-detail-box {
  display: flex;
  padding: 0 32rpx 24rpx;
  .check-detail-btn {
    flex: 1;
    padding: 16rpx 0;
    text-align: center;
    border-radius: 108rpx;
    font-weight: bold;
    font-size: 28rpx;
    color: #FFFFFF;
    line-height: 1.5;
    background-color: $topicC;
    &+.check-detail-btn {
      margin-left: 46rpx;
    }
  }
}

.task-finish-tips {
  color: $dangerColor;
  font-size: 32rpx;
  padding: 32rpx;
  font-weight: 500;
  text-align: center;
}

.card {
  padding: 0 32rpx 32rpx;
  .progress-section {
    margin-top: 8rpx;
    .progress-label {
      font-size: 28rpx;
      color: #333;
    }
    .progress-bar-wrap {
      display: flex;
      align-items: center;
      margin-top: 8rpx;
      .progress-bar-default {
        flex: 1;
        height: 26rpx;
        background-color: #F4F6FA;
        border-radius: 124rpx;
        overflow: hidden;
      }
      .progress-bar {
        height: 100%;
        border-radius: 124rpx;
        background-color: $topicC;
      }
      .progress-text {
        font-size: 28rpx;
        color: #333;
        margin-left: 24rpx;
      }
    }
  }
}

.list-head-suffix {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-size: 24rpx;
  color: #777777;
}

.list-head-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.footer-btn {
  border-radius: 124rpx 124rpx 124rpx 124rpx;
  font-size: 24rpx;
  line-height: 1.5;
  padding: 8rpx 32rpx;
  &+.footer-btn {
    margin-left: 36rpx;
  }
  &.danger {
    color: $dangerColor;
    border: 1px solid $dangerColor;
  }
  &.success {
    color: $topicC;
    border: 1px solid $topicC;
  }
  &.info {
    color: #777777;
    border: 1px solid #777777;
  }
}
</style>