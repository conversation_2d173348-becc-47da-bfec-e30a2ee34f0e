<template>
  <page>
    <view slot="content" class="main-body">
      <!-- #ifdef H5 -->
      <template v-if="ua.indexOf('miniprogram') === -1">
        <uni-nav-bar @clickLeft="back" color="#000" :border="false" left-icon="left"
          fixed backgroundColor="#fff" :title="data.name ? data.name : ''" statusBar
        />
      </template>
      <!-- #endif -->
      <view class="main">
        <view class="m-main">
          <view class="title">{{data.name || '病友群分享码'}}</view>
          <view class="img-main">
            <view class="img" @tap="clickImg">
              <image
                class="role-image"
                show-menu-by-longpress="true"
                mode="widthFix"
                :src="data.qrcodePath ? file_ctx + data.qrcodePath : ''"
                @touchstart="gtouchstart"
                @touchend="gtouchend"
                @touchmove="gtouchmove"
              />
            </view>
          </view>
          <view class="prompt" @tap="saveImage">长按识别二维码 {{data.name || ''}}</view>
        </view>
      </view>
    </view>
  </page>
</template>

<script>
import { mapState } from 'vuex'
import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'
export default {
    components: {
      uniNavBar
    },
    data () {
        return {
            file_ctx: this.file_ctx,
            id: '',
            data: {},
            timeOutEvent: null,
            longPressTime: 0,
            // #ifdef H5
            ua: navigator.userAgent.toLowerCase(),
            // #endif
        }
    },
    async onLoad () {
        const query = this.$Route.query
        if (!this.$validate.isNull(query)) {
            this.id = query.id
            await this.getEnterprisewechatgroupQueryOne()
            uni.setNavigationBarTitle({
                title: this.data.name
            });
        }
    },
    computed: {
        ...mapState('user', {
            accountId: state => state.accountId
        })
    },
    methods: {
        back() {
          this.$navto.back(1)
        },
        async getEnterprisewechatgroupQueryOne () {
            const res = await this.$api.community.enterprisewechatgroupQueryOne({id: this.id})
            this.data = res.data
        },
        clickImg () {
            this.previewImage()
        },
        recordslongPressImg () {
            const param = {
                accountId: this.accountId,
                businessType: 3, // 业务类型:1-横幅，2-外部服务应用，3-企业微信群，4-圈子，5-帖子，6-帖子评论
                businessId: this.id,
                source: 1, // 来源：1-真实用户，2-马甲
                type: 3 // 类型：1-访问，2-点击，3-长按，4-关注，5-取关，6-点赞，7-收藏，8-删除评论，9-取消点赞，10-取消收藏，11-阅读
            }
            this.$api.community.applicationoperatelogInsert(param)
        },
        // 图片展示
        previewImage() {
            const that = this
            const imageList = [that.file_ctx + that.data.qrcodePath]
            uni.previewImage({
                current: 0,
                urls: imageList
            })
        },
        // 手势开始
        gtouchstart () {
            const that = this
            this.timeOutEvent = setTimeout(function () {
                let nowTime = new Date().getTime()
                if (nowTime - that.longPressTime >= 5000) {
                    that.longPressTime = nowTime
                    that.longPress()
                }
            }, 500)// 这里设置定时器，定义长按500毫秒触发长按事件，时间可以自己改，个人感觉500毫秒非常合适
            return false
        },
        // 手释放，如果在500毫秒内就释放，则取消长按事件，此时可以执行onclick应该执行的事件
        gtouchend () {
            clearTimeout(this.timeOutEvent)// 清除定时器
            this.timeOutEvent = null
            if (this.timeOutEvent !== 0) {
                // 这里写要执行的内容（尤如onclick事件）
                // vm.goChat(item);
            }
            return false
        },
        // 如果手指有移动，则取消所有事件，此时说明用户只是要移动而不是长按
        gtouchmove () {
            clearTimeout(this.timeOutEvent)// 清除定时器
            this.timeOutEvent = null
        },
        // 真正长按后应该执行的内容
        async longPress () {
            this.timeOutEvent = null
            // 记录长按事件
            this.recordslongPressImg()
        },
    }
}
</script>

<style scoped lang="scss">
.bg-topicC{
  background: $topicC !important;
}
.bg-0DD140{
  background: #0DD140 !important;
}
.bg-blue{
  background: #187FFF !important;
}
.m-r-none{
  margin-right: 0 !important;
}
.main-body{
  height: 100%;
  .main{
    height: 100%;
    background: #fff;
    .m-main{
      .title{
        padding: 80upx 30upx;
        text-align: center;
        font-size: 36upx;
        line-height: 54upx;
      }
      .img-main{
        .img{
          width: 500upx;
          // height: 500upx;
          margin: 0 auto;
          background: #fff;
          .role-image{
            height: 100%;
            width: 100%;
          }
        }
      }
      .prompt{
        padding: 60upx 30upx;
        text-align: center;
        font-size: 28upx;
        line-height: 42upx;
      }
      .but-main{
        margin-bottom: 30upx;
        &:last-of-type{
          margin-bottom: 0;
        }
        .but-ul{
          width: 640upx;
          margin: 0 auto;
          text-align: center;
          .but-li{
            display: inline-block;
            vertical-align: middle;
            width: 200upx;
            height: 66upx;
            line-height: 66upx;
            box-sizing: border-box;
            text-align: center;
            @include rounded(44upx);
            color: #fff;
            font-size: 28upx;
            margin-right: 20upx;
            &:last-child{
              margin-right:0;
            }
          }
        }
      }
    }
  }
}
</style>
