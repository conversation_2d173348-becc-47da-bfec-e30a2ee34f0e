<template>
  <view class='free-get-glareme'>
    <view class='free-get-glareme-img'><image :show-menu-by-longpress="true" @longpress="handleLongPress" :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-free-get-glareme.png'"></image></view>
  </view>
</template>

<script>
  import { mapState } from 'vuex'
  export default {
    data(){
      return{
        $constant: this.$constant,
        file_ctx:this.file_ctx,
      }
    },
    onLoad(){
      this.pageexposurerecordInsert()
    },
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId,
      }),
    },
    mounted(){},
    methods:{
      async handleLongPress(){
        let params = {
          imageUrl:this.file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-free-get-glareme.png',
          accountId:this.accountId,
          businessType:this.$constant.drugBook.businessTypeObj.limitFreeGet
        }
        await this.$api.drugBook.visitpagerecordInsert(params) //businessType :墨角藻免费领取 19
      },
      async pageexposurerecordInsert(){
        let parmas = {
          imageUrl:this.file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-free-get-glareme.png',
          accountId:this.accountId,
          businessType:this.$constant.drugBook.businessTypeObj.limitFreeGet,
        }
        await this.$api.drugBook.pageexposurerecordInsert(parmas)
      },
    },
 }
</script>

<style lang='scss' scoped>
.free-get-glareme{
  .free-get-glareme-img{
    width: 750rpx;
    height: 1448rpx;
    image{
      width: 100%;
      height: 100%;
    }
  }
}
</style>