<template>
  <view class="page">
    <view class="masking"></view>
    <!-- 页面标题 -->
    <uni-nav-bar
       @clickLeft="back"
       color="black"
       :border="false"
       left-icon="left"
       :fixed="false"
       statusBar
       :showBtnsRight='false'
       title='申请分销'
       left-width="48rpx"
       right-width="100px"
       backgroundColor="rgba(0,0,0,0)"
    >
    </uni-nav-bar>

    <!-- 加载指示器 -->
    <view class="loading-container" v-if="isLoading">
      <view class="loading-spinner"></view>
      <text class="loading-text">数据加载中...</text>
    </view>

    <!-- 快速填充测试数据按钮 -->
    <!-- <view class="test-data-button" @tap="fillTestData">
      <text>填充测试数据</text>
    </view> -->

    <!-- 审核状态提示 -->
    <view class="audit-tip" v-if="showAuditTip && applicationStatus === 3">
      <view class="audit-tip-icon">!</view>
      <view class="audit-tip-content">
        <view class="audit-tip-title">审核未通过</view>
        <view class="audit-tip-reason">{{auditRemark}}</view>
      </view>
    </view>

    <!-- 审核中状态提示 -->
    <view class="audit-processing" v-if="applicationStatus === 1">
      <view class="audit-processing-content">
        <view class="audit-processing-title">您的申请正在审核中</view>
        <view class="audit-processing-desc">请耐心等待，审核结果将会通知您</view>
      </view>
    </view>

    <!-- 分销表单 -->
    <view class="boxTab" v-if="applicationStatus !== 1">
      <app-form
        v-model="formData"
        :form-fields="formFields"
        :valid-rules="validRules"
        :static-ctx="$static_ctx"
        submit-text="提交申请"
        @submit="reservation"
        @input="handleFormDataChange"
      >
        <!-- 自定义字段内容可以在这里添加 -->
        <agreement ref="agreementRef" slot="btnBox" class="agreement" v-model="isAgreed" :provinceValue="accompanyproviderQueryOne"></agreement>
      </app-form>
    </view>

    <!-- 成功提示框 -->
    <view class="success-popup" v-if="successTipVisible">
      <view class="success-popup-mask" @click.stop></view>
      <view class="success-popup-content">
        <view class="success-popup-title">提示</view>
        <view class="success-popup-body">{{successTipMessage}}</view>
        <view class="success-popup-btn" @tap="closeSuccessTip">我知道了</view>
      </view>
    </view>

    <!-- 错误提示框 -->
    <view class="error-popup" v-if="errorTipVisible">
      <view class="error-popup-mask" @click.stop></view>
      <view class="error-popup-content">
        <view class="error-popup-title">错误</view>
        <view class="error-popup-body">{{errorTipMessage}}</view>
        <view class="error-popup-btn" @tap="closeErrorTip">我知道了</view>
      </view>
    </view>

  </view>
</template>

<script>
  import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'
  import AppForm from './components/form-components/AppForm.vue'
  import common from '@/common/util/main'
  import serverOptions from '@/config/env/options'
  import agreement from "../components/agreement.vue";

  export default{
    components: {
      uniNavBar,
      AppForm,
      agreement,
    },
    data(){
      return {
        isAgreed:true,
        accompanyproviderQueryOne:null,
        iconRightArrow: this.$static_ctx + "image/business/hulu-v2/icon-right-arrow.png",
        iconPostUpload: this.$static_ctx + "image/business/hulu-v2/icon-post-upload.png",
        cameraGray: this.$static_ctx + "image/business/hulu-v2/camera-gray.png",
        camera: this.$static_ctx + "image/business/hulu-v2/camera.png",
        iconPostMenuClose: this.$static_ctx + "image/business/hulu-v2/icon-post-menu-close.png",
        IDCardNationalEmblemFace: this.$static_ctx + "image/business/hulu-v2/IDCardNationalEmblemFace.png",
        IDCardFace: this.$static_ctx + "image/business/hulu-v2/IDCardFace.png",
        file_ctx: this.file_ctx,
        provinceMap:[],
        distributionMap:[{value:1,text:'企业'},{value:2,text:'个人'}],
        hospitalQuery:[],
        distributionText:'企业',
        distribution:1,
        successTipVisible: false,
        successTipMessage: '',
        errorTipVisible: false,
        errorTipMessage: '',
        formData: {
          distributionType: '个人', // 默认个人类型
          // 企业表单字段
          companyName: '',
          phone: '',
          area: [],
          areaProvince: '',
          areaCity: '',
          areaDistrict: '',
          businessName: '',
          businessAccount: '',
          name: '',
          idCardType: '身份证',
          idCardNumber: '',
          businessLicense: '',
          CardFace: '',
          CardNationalEmblemFace: '',
          // 收款账户字段
          accountNumber: '',
          accountName: '',
          acctCertificateType: '身份证',
          acctCertificateNo: '',
          accountBankBranch: '',
          accountBranchName: '',
          accountBankCode: '',
          openAccountProve: '',
          advantage: '',
          fileAttachments: []
        },
        // 表单字段配置
        formFields: [
          {
            type: 'picker',
            name: 'distributionType',
            label: '分销类型',
            required: true,
            mode: 'selector',
            range: ['企业', '个人'],
            placeholder: '请选择分销类型'
          },
          {
            type: 'picker',
            name: 'area',
            label: '所在地区',
            placeholder: '请选择',
            required: true,
            mode: 'region',
            level: 'district',
            provinceField: 'areaProvince',
            cityField: 'areaCity',
            districtField: 'areaDistrict',
            displayField: 'areaDisplay'
          }
        ],
        // 表单验证规则
        validRules: {
          distributionType: {
            required: true,
            message: '请选择分销类型'
          },
          // 企业表单验证规则
          companyName: {
            required: function(value, formData) {
              return formData.distributionType === '企业'; // 仅企业类型时必填
            },
            message: '请输入分销员名称'
          },
          phone: {
            required: true,
            message: '请输入手机号码'
          },
          area: {
            required: true,
            message: '请选择所在地区'
          },
          businessName: {
            required: function(value, formData) {
              return formData.distributionType === '企业'; // 仅企业类型时必填
            },
            message: '请输入营业执照名称'
          },
          businessAccount: {
            required: function(value, formData) {
              return formData.distributionType === '企业'; // 仅企业类型时必填
            },
            message: '请输入营业执照号'
          },
          idCardType: {
            required: function(value, formData) {
              return formData.distributionType === '企业'; // 仅企业类型时必填
            },
            message: '请选择法人证件类型'
          },
          businessLicense: {
            required: function(value, formData) {
              return formData.distributionType === '企业'; // 仅企业类型时必填
            },
            message: '请上传营业执照'
          },
          CardFace: {
            required: true,
            message: '请上传证件人像面'
          },
          CardNationalEmblemFace: {
            required: true,
            message: '请上传证件国徽面'
          },
          /* 注释掉开户许可证验证
          openAccountProve: {
            required: true,
            message: '请上传开户许可证照片'
          },
          */
          // 账户信息验证规则
          accountNumber: {
            required: true,
            message: '请输入收款银行卡号'
          },
          // accountName: {
          //   required: true,
          //   message: '请输入收款账户名称'
          // },
          // acctCertificateType: {
          //   required: true,
          //   message: '请选择收款账户证件类型'
          // },
          acctCertificateNo: {
            required: true,
            message: '请输入收款人身份证号'
          },
          accountBankBranch: {
            required: true,
            message: '请输入开户行号'
          },
          accountBranchName: {
            required: true,
            message: '请输入开户行名称'
          },
          accountBankCode: {
            required: true,
            message: '请输入清算行号'
          },
          merchantPhoto: {
            required: function(value, formData) {
              return formData.distributionType === '企业'; // 仅企业类型时必填
            },
            message: '请上传商户门头照'
          },
          shopInnerPhoto: {
            required: function(value, formData) {
              return formData.distributionType === '企业'; // 仅企业类型时必填
            },
            message: '请上传商铺内部照片'
          }
        },
        queryOptions:{
          enterprise:'',
          provinceValue:1,
          hospitalName:'',
          imageObj:[],
          range:[],
          bookName:'',
          bookPhone:''
        },
        applicationStatus: null, // 申请状态
        auditRemark: '', // 审核不通过原因
        showAuditTip: false, // 是否显示审核提示
        historyData: null, // 保存历史申请数据
        isLoading: false, // 添加数据加载指示器
      }
    },
    watch:{
      'formData.distributionType': {
        handler(newVal) {
          this.distribution = newVal === '企业' ? 1 : 2;
          this.updateFormFields();
        },
        immediate: true
      }
    },
    computed: {
      // 企业表单字段
      enterpriseFormFields() {
        return [
          {
            type: 'picker',
            name: 'distributionType',
            label: '分销类型',
            required: true,
            mode: 'selector',
            range: ['个人', '企业'],
            placeholder: '请选择分销类型'
          },
          {
            type: 'input',
            name: 'companyName',
            label: '分销员名称',
            placeholder: '请输入',
            required: true
          },
          {
            type: 'input',
            name: 'phone',
            label: '手机号码',
            placeholder: '请输入手机号码',
            required: true
          },
          {
            type: 'picker',
            name: 'area',
            label: '所在地区',
            placeholder: '请选择',
            required: true,
            mode: 'region',
            level: 'district',
            provinceField: 'areaProvince',
            cityField: 'areaCity',
            districtField: 'areaDistrict',
            displayField: 'areaDisplay'
          },
          {
            type: 'input',
            name: 'acctCertificateNo',
            label: '收款人身份证号',
            placeholder: '请输入',
            required: true
          },
          {
            type: 'input',
            name: 'accountNumber',
            label: '收款银行卡号',
            placeholder: '请输入',
            required: true
          },
          {
            type: 'input',
            name: 'accountBankBranch',
            label: '开户行号',
            placeholder: '请输入',
            required: true
          },
          {
            type: 'input',
            name: 'accountBranchName',
            label: '开户行名称',
            placeholder: '请输入',
            required: true
          },
          {
            type: 'input',
            name: 'accountBankCode',
            label: '清算行号',
            placeholder: '请输入',
            required: true
          },
          {
            type: 'input',
            name: 'businessName',
            label: '营业执照名称',
            placeholder: '请输入',
            required: true
          },
          {
            type: 'input',
            name: 'businessAccount',
            label: '营业执照号',
            placeholder: '请输入',
            required: true
          },
          {
            type: 'input',
            name: 'legalPersonName',
            label: '法人姓名',
            placeholder: '请输入',
            required: true
          },
          // {
          //   type: 'picker',
          //   name: 'idCardType',
          //   label: '法人证件类型',
          //   placeholder: '请选择',
          //   required: true,
          //   mode: 'selector',
          //   range: ['身份证'],
          //   defaultValue: '身份证'
          // },
          {
            type: 'input',
            name: 'idCardNumber',
            label: '法人证件号',
            placeholder: '请输入',
            required: true
          },
          // {
          //   type: 'input',
          //   name: 'accountName',
          //   label: '收款账户名称',
          //   placeholder: '请输入',
          //   required: true
          // },
          // {
          //   type: 'picker',
          //   name: 'acctCertificateType',
          //   label: '收款账户证件类型',
          //   placeholder: '请选择',
          //   required: true,
          //   mode: 'selector',
          //   range: ['身份证'],
          //   defaultValue: '身份证'
          // },
          {
            type: 'license',
            name: 'businessLicense',
            label: '营业执照',
            required: true
          },
          {
            type: 'merchantphoto',
            name: 'merchantPhoto',
            label: '商户门头照',
            required: true
          },
          {
            type: 'shopphoto',
            name: 'shopInnerPhoto',
            label: '商铺内部照片',
            required: true
          },
          {
            type: 'idcard',
            label: '法人证件照片',
            required: true,
            frontField: 'CardFace',
            backField: 'CardNationalEmblemFace',
            frontIcon: this.$static_ctx + 'image/business/hulu-v2/IDCardFace.png',
            backIcon: this.$static_ctx + 'image/business/hulu-v2/IDCardNationalEmblemFace.png',
            frontText: '人像面',
            backText: '国徽面'
          },
          /* 注释掉开户许可证字段
          {
            type: 'image',
            name: 'openAccountProve',
            label: '开户许可证照片',
            required: true
          },
          */
          {
            type: 'textarea',
            name: 'advantage',
            label: '优势描述',
            placeholder: '请输入描述',
            maxlength: 200,
            showCounter: true,
            required: false
          }
        ];
      },
      // 个人表单字段
      personFormFields() {
        return [
          {
            type: 'picker',
            name: 'distributionType',
            label: '分销类型',
            required: true,
            mode: 'selector',
            range: ['个人', '企业'],
            placeholder: '请选择分销类型'
          },
          {
            type: 'input',
            name: 'name',
            label: '分销员名称',
            placeholder: '请输入',
            required: true
          },
          {
            type: 'input',
            name: 'phone',
            label: '手机号码',
            placeholder: '请输入手机号码',
            required: true
          },
          {
            type: 'picker',
            name: 'area',
            label: '所在地区',
            placeholder: '请选择',
            required: true,
            mode: 'region',
            level: 'district',
            provinceField: 'areaProvince',
            cityField: 'areaCity',
            districtField: 'areaDistrict',
            displayField: 'areaDisplay'
          },
          {
            type: 'input',
            name: 'acctCertificateNo',
            label: '身份证号码',
            placeholder: '请输入',
            required: true
          },
          {
            type: 'input',
            name: 'accountNumber',
            label: '收款银行卡号',
            placeholder: '请输入',
            required: true
          },
          // {
          //   type: 'input',
          //   name: 'accountName',
          //   label: '收款账户名称',
          //   placeholder: '请输入',
          //   required: true
          // },
          // {
          //   type: 'picker',
          //   name: 'acctCertificateType',
          //   label: '收款账户证件类型',
          //   placeholder: '请选择',
          //   required: true,
          //   mode: 'selector',
          //   range: ['身份证'],
          //   defaultValue: '身份证'
          // },
          {
            type: 'input',
            name: 'accountBranchName',
            label: '开户行名称',
            placeholder: '请输入',
            required: true
          },
          {
            type: 'input',
            name: 'accountBankBranch',
            label: '开户行号',
            placeholder: '请输入',
            required: true
          },
          {
            type: 'input',
            name: 'accountBankCode',
            label: '清算行号',
            placeholder: '请输入',
            required: true
          },
          {
            type: 'idcard',
            label: '身份证照片',
            required: true,
            frontField: 'CardFace',
            backField: 'CardNationalEmblemFace',
            frontIcon: this.$static_ctx + 'image/business/hulu-v2/IDCardFace.png',
            backIcon: this.$static_ctx + 'image/business/hulu-v2/IDCardNationalEmblemFace.png',
            frontText: '人像面',
            backText: '国徽面'
          },
          /* 注释掉开户许可证字段
          {
            type: 'image',
            name: 'openAccountProve',
            label: '开户许可证照片',
            required: true
          },
          */
          {
            type: 'textarea',
            name: 'advantage',
            label: '优势描述',
            placeholder: '请输入描述',
            maxlength: 200,
            showCounter: true,
            required: false
          }
        ];
      }
    },
    async mounted() {
      // 显示加载指示器
      // let {data} = await this.$api.accompanyDoctor.getAccompanyproviderAll();
      // let {pname,cityname,adname} = await this.getPosition();
      // this.queryOptions.provinceValue = [pname,cityname,adname]
      let accompanyproviderQueryOne = (await this.$api.accompanyDoctor.accompanyproviderQueryOne({id:serverOptions.providerId})).data;
      this.accompanyproviderQueryOne = accompanyproviderQueryOne;
      // 获取当前用户信息
      const codeUserInfo = common.getKeyVal('user', 'codeUserInfo', true);
      if (codeUserInfo && codeUserInfo.id) {
        // 检查是否有分销申请记录
        try {

          const res = await this.$api.distribution.accompanydistributorQueryHistoryOne({
            userId: codeUserInfo.id,
            providerId: serverOptions.providerId
          });

          console.log('获取分销申请状态结果:', res);

          if (res && res.code === 0 && res.data) {
            // 保存历史数据
            this.historyData = res.data;

            // 获取申请状态 (1-待审核 2-通过 3-失败)
            this.applicationStatus = res.data.auditStatus;

            // 处理不同的审核状态
            if (res.data.auditStatus === 3) { // 审核失败
              this.auditRemark = res.data.failReason || '未提供审核不通过原因';
              this.showAuditTip = true;

              // 填充表单数据（回显）
              this.fillFormDataFromHistory(res.data);
            } else if (res.data.auditStatus === 1) { // 待审核
              // 显示审核中状态，不显示表单
              this.showAuditTip = false;

              uni.showToast({
                title: '您的申请正在审核中',
                icon: 'none',
                duration: 2000
              });
            } else if (res.data.auditStatus === 2) { // 审核通过
              // 已经是分销员，跳转到分销员中心
              uni.showToast({
                title: '您已是分销员',
                icon: 'success',
                duration: 2000
              });

              setTimeout(() => {
                uni.navigateBack();
              }, 2000);
            }
          }
        } catch (error) {
          console.error('获取分销申请状态失败:', error);
        } finally {
          // 隐藏加载指示器
          this.isLoading = false;
        }
      }

      this.updateFormFields();
    },
    methods:{
      // 获取文件类型映射，与AppForm中的方法保持一致
      getAttTypeByFileKey(fileKey) {
        // 根据图片中的对照表映射文件类型
        const fileTypeMap = {
          CardFace: this.formData.distributionType === '企业' ? 'FR_ID_CARD_FRONT' : 'ID_CARD_FRONT', // 身份证正面
          CardNationalEmblemFace: this.formData.distributionType === '企业' ? 'FR_ID_CARD_BEHIND' : 'ID_CARD_BEHIND', // 身份证反面
          businessLicense: 'BUSINESS_LICENCE', // 营业执照
          merchantPhoto: 'MERCHANT_PHOTO', // 商户门头照
          shopInnerPhoto: 'SHOPINNER', // 商铺内部照片
          openAccountProve: 'OTHERS', // 开户凭证
        };

        return fileTypeMap[fileKey] || 'OTHERS';
      },
      // 从fileAttachments中删除指定类型的附件
      removeAttachmentByType(attachType) {
        if (!this.formData.fileAttachments || !Array.isArray(this.formData.fileAttachments)) {
          return;
        }

        console.log('删除前附件列表:', this.formData.fileAttachments);

        // 过滤掉指定类型的附件
        this.formData.fileAttachments = this.formData.fileAttachments.filter(item => {
          return item.attachType !== attachType;
        });

        console.log('删除后附件列表:', this.formData.fileAttachments);
      },
      // 处理图片删除
      handleImageDelete(imageField) {
        // 获取该图片对应的附件类型
        const attachType = this.getAttTypeByFileKey(imageField);

        // 从fileAttachments中删除对应类型的附件
        this.removeAttachmentByType(attachType);

        // 清空表单中该字段的值
        this.formData[imageField] = '';
      },
      // 更新表单字段
      updateFormFields() {
        this.formFields = this.distribution === 1 ? this.enterpriseFormFields : this.personFormFields;
      },
      back() {
        uni.navigateBack();
      },
      // 快速填充测试数据
      fillTestData() {
        const testData = {
          distributionType: this.formData.distributionType,
          name: '测试用户',
          phone: '',
          area: ['广东省', '深圳市', '南山区'],
          areaProvince: '广东省',
          areaCity: '深圳市',
          areaDistrict: '南山区',
          areaDisplay: '广东省 深圳市 南山区',
          idCardType: '身份证',
          idCardNumber: '******************',
          accountName: '测试账户',
          acctCertificateType: '身份证',
          acctCertificateNo: '******************',
          advantage: '这是一个测试案例',
        };

        // 如果是企业类型，添加企业特有字段
        if (this.formData.distributionType === '企业') {
          Object.assign(testData, {
            companyName: '测试企业有限公司',
            businessName: '测试企业有限公司',
            businessAccount: '91440300MA5EYKP00F',
          });
        }

        // 更新表单数据
        for (const key in testData) {
          if (Object.prototype.hasOwnProperty.call(testData, key)) {
            this.formData[key] = testData[key];
          }
        }

        uni.showToast({
          title: '测试数据已填充',
          icon: 'success'
        });
      },
      // 处理滚动到底部
      handleScrollToBottom() {
        console.log('滚动到底部');
      },
      getPosition(){
        let resFn;
        let promise = new Promise(res=>resFn = res);
        uni.getLocation({
          type: 'wgs84',
          geocode:true,
          	success: async (res)=> {
              console.log('res',res);
              let Position = await this.$ext.common.getPosition(res);
              console.log('Position',Position);
              resFn(Position)
          	}
        });
        return promise
      },
      async reservation(formData){
        // 检查协议是否同意
        const isAgreed = await this.$refs.agreementRef.checkAgreement();
        if (!isAgreed) {
          return;
        }
        // 如果是待审核状态，提示用户
        if (this.applicationStatus === 1) {
          uni.showToast({
            title: '您的申请正在审核中，请耐心等待',
            icon: 'none',
            duration: 2000
          });
          return;
        }

        // 打印表单提交内容
        console.log('提交的表单数据:', formData);

        // 检查优势描述是否超过字符限制
        if (formData.advantage && formData.advantage.length > 200) {
          this.errorTipMessage = `优势描述超出字符限制，当前${formData.advantage.length}个字符，最多允许200个字符`;
          this.errorTipVisible = true;
          return;
        }

        // 直接从areaDisplay获取省市区信息
        let province = '';
        let city = '';
        let county = '';

        // 如果有areaDisplay，解析省市区
        if (formData.areaDisplay) {
          const areaArr = formData.areaDisplay.split(' ');
          if (areaArr.length >= 1) province = areaArr[0];
          if (areaArr.length >= 2) city = areaArr[1];
          if (areaArr.length >= 3) county = areaArr[2];
        }
        // 如果没有areaDisplay，回退到单独字段
        else {
          province = formData.areaProvince || '';
          city = formData.areaCity || '';
          county = formData.areaDistrict || '';
        }

        // 获取服务商ID
        const providerId = serverOptions.providerId;

        // 构建附件列表
        let attachList = [];
        let imagesObj = {};

        // 如果有已上传的文件附件信息，添加到attachList中
        if (formData.fileAttachments && Array.isArray(formData.fileAttachments)) {
          attachList = formData.fileAttachments;

          console.log('原始附件列表:', attachList);

          // 遍历附件列表，构建imagesObj
          attachList.forEach(item => {
            if (item.attachType) {
              // 确保没有重复的附件类型，如果有重复只保留最后一个
              // 优先使用attachStorePath作为图片路径
              if (item.attachStorePath) {
                // 确保路径中不包含服务器前缀
                let filePath = item.originalFilePath;
                const fileCtx = this.file_ctx || 'http://file.greenboniot.cn/';

                // 移除可能存在的文件服务器前缀
                if (filePath.startsWith(fileCtx)) {
                  filePath = filePath.substring(fileCtx.length);
                } else if (filePath.startsWith('http://')) {
                  // 处理其他可能的http前缀
                  const urlParts = filePath.split('/');
                  // 跳过协议和域名部分
                  filePath = urlParts.slice(3).join('/');
                }

                imagesObj[item.attachType] = filePath;
                console.log(`使用attachStorePath: ${item.attachType} = ${filePath}`);
              } else if (item.originalFilePath) {
                // 如果没有attachStorePath，使用originalFilePath
                let filePath = item.originalFilePath;
                const fileCtx = this.file_ctx || 'http://file.greenboniot.cn/';

                // 移除可能存在的文件服务器前缀
                if (filePath.startsWith(fileCtx)) {
                  filePath = filePath.substring(fileCtx.length);
                } else if (filePath.startsWith('http://')) {
                  // 处理其他可能的http前缀
                  const urlParts = filePath.split('/');
                  // 跳过协议和域名部分
                  filePath = urlParts.slice(3).join('/');
                }

                imagesObj[item.attachType] = filePath;
                console.log(`使用originalFilePath: ${item.attachType} = ${filePath}`);
              }
            }
          });
        }

        // 验证必要的图片是否存在
        const validateImages = () => {
          // 如果是企业类型
          if (formData.distributionType === '企业') {
            // 验证企业必须上传的图片
            if (!formData.businessLicense) {
              uni.showToast({
                title: '请上传营业执照',
                icon: 'none'
              });
              return false;
            }
            if (!formData.CardFace) {
              uni.showToast({
                title: '请上传法人证件人像面',
                icon: 'none'
              });
              return false;
            }
            if (!formData.CardNationalEmblemFace) {
              uni.showToast({
                title: '请上传法人证件国徽面',
                icon: 'none'
              });
              return false;
            }
          }

          // 验证所有类型都需要的图片
          // if (!formData.openAccountProve) {
          //   uni.showToast({
          //     title: '请上传开户许可证照片',
          //     icon: 'none'
          //   });
          //   return false;
          // }

          return true;
        };

        // 验证图片
        if (!validateImages()) {
          return;
        }

        console.log('最终附件对象:', imagesObj);
        const receiverFileStr = JSON.stringify(imagesObj);
        console.log('序列化后的附件JSON:', receiverFileStr);

        // 构建DTO对象的通用字段
        const dtoCommon = {
          acctNo: formData.accountNumber,
          acctName: formData.distributionType === '企业' ? formData.companyName : formData.name,
          acctTypeCode: formData.distributionType === '企业' ? '57' : '58', // 57：对公 58：对私
          acctCertificateType: '17',
          acctOpenBankCode: formData.accountBankBranch,
          acctOpenBankName: formData.accountBranchName,
          acctClearBankCode: formData.accountBankCode,
          acctCertificateNo: formData.acctCertificateNo,
          attachList: attachList
        };

        // 根据分销类型构建特定DTO字段
        let dtoSpecific = {};

        if (formData.distributionType === '企业') {
          // 企业类型特定字段
          dtoSpecific = {
            receiverName: formData.companyName,
            contactMobile: formData.phone,
            licenseNo: formData.businessAccount || '', // 营业执照号码
            licenseName: formData.businessName || '', // 营业执照名称
            legalPersonName: formData.legalPersonName || '', // 法人姓名
            legalPersonCertificateType: '17', // 法人证件类型，默认17=身份证
            legalPersonCertificateNo: formData.idCardNumber || '' // 法人证件号
          };
        } else {
          // 个人类型特定字段
          dtoSpecific = {
            receiverName: formData.name,
            contactMobile: formData.phone
          };
        }

        // 构建基本参数
        let params = {
          name: formData.distributionType === '企业' ? formData.companyName : formData.name,
          phone: formData.phone,
          province: province,
          city: city,
          county: county,
          providerId: providerId,
          receiverFile: receiverFileStr,
          advantage: formData.advantage,

          // 合并DTO对象
          dto: {
            ...dtoCommon,
            ...dtoSpecific
          }
        };

        // 打印最终处理后的提交参数
        console.log('处理后的提交参数:', params);

        try {
          uni.showLoading({
            title: '提交中...'
          });

          // 调用新增的API
          const result = await this.$api.distribution.accompanydistributorInsert(params);

          uni.hideLoading();

          // 处理成功响应
          if (result && result.code === 0) {
            // 显示自定义成功提示
            this.successTipMessage = '您已提交申请，请耐心等待审核';
            this.successTipVisible = true;

            // 3秒后自动关闭提示并返回
            setTimeout(() => {
              this.successTipVisible = false;
              uni.navigateBack();
            }, 3000);
          } else {
            uni.showToast({
              title: result.msg || '申请提交失败',
              icon: 'none'
            });
          }
        } catch (error) {
          uni.hideLoading();
          // 使用弹窗显示错误信息
          this.errorTipMessage = error.msg || '申请提交失败，请稍后重试';
          this.errorTipVisible = true;
          console.error('提交分销员申请失败:', error);
        }
      },
      handleFormDataChange(updatedFormData) {
        // 获取更新后的表单数据，避免对象引用问题
        this.formData = JSON.parse(JSON.stringify(updatedFormData));
        console.log('父组件接收到表单更新:', this.formData);

        // 强制重新渲染表单
        this.$forceUpdate();
        this.$nextTick(() => {
          console.log('表单已重新渲染，当前表单数据:', this.formData);
        });
      },
      closeSuccessTip() {
        this.successTipVisible = false;
        uni.navigateBack();
      },
      closeErrorTip() {
        this.errorTipVisible = false;
      },
      async checkApplicationStatus() {
        try {
          const result = await this.$api.distribution.checkApplicationStatus();
          if (result && result.code === 0) {
            this.applicationStatus = result.data.status;
            this.auditRemark = result.data.remark;
            this.showAuditTip = true;
          } else {
            console.error('获取申请状态失败:', result.msg);
          }
        } catch (error) {
          console.error('获取申请状态失败:', error);
        }
      },
      // 从历史记录填充表单数据
      fillFormDataFromHistory(historyData) {
        try {
          // 保存历史数据
          this.historyData = historyData;

          // 显示加载指示器
          this.isLoading = true;

          // 设置分销类型 (根据接收到的数据结构可能需要调整)
          if (historyData.dto && historyData.dto.acctTypeCode) {
            this.formData.distributionType = historyData.dto.acctTypeCode === '57' ? '企业' : '个人';
          } else if (historyData.receiverBody) {
            try {
              // 确保receiverBody是字符串并进行JSON解析
              const receiverBodyStr = typeof historyData.receiverBody === 'string'
                ? historyData.receiverBody
                : JSON.stringify(historyData.receiverBody);

              const dtoData = JSON.parse(receiverBodyStr);
              console.log('成功解析receiverBody:', dtoData);

              // 根据解析出的数据设置分销类型
              if (dtoData.acctTypeCode) {
                this.formData.distributionType = dtoData.acctTypeCode === '57' ? '企业' : '个人';
                console.log('根据receiverBody设置分销类型:', dtoData.acctTypeCode, this.formData.distributionType);
                // 确保立即更新表单字段，以便后续正确判断企业或个人表单
                this.updateFormFields();
              }
            } catch(e) {
              console.error('解析receiverBody失败:', e, historyData.receiverBody);
            }
          }

          // 设置基础信息
          this.formData.name = historyData.name || '';
          this.formData.phone = historyData.phone || '';

          // 设置地区信息
          if (historyData.province || historyData.city || historyData.county) {
            const areaArray = [];
            if (historyData.province) areaArray.push(historyData.province);
            if (historyData.city) areaArray.push(historyData.city);
            if (historyData.county) areaArray.push(historyData.county);

            this.formData.area = areaArray;
            this.formData.areaProvince = historyData.province || '';
            this.formData.areaCity = historyData.city || '';
            this.formData.areaDistrict = historyData.county || '';
            this.formData.areaDisplay = areaArray.join(' ');
          }

          // 设置优势描述
          this.formData.advantage = historyData.advantage || '';

          // 如果DTO在receiverBody中
          let dtoData = historyData.dto;
          if (!dtoData && historyData.receiverBody) {
            try {
              // 确保receiverBody是字符串并进行JSON解析
              const receiverBodyStr = typeof historyData.receiverBody === 'string'
                ? historyData.receiverBody
                : JSON.stringify(historyData.receiverBody);

              dtoData = JSON.parse(receiverBodyStr);
              console.log('成功解析receiverBody:', dtoData);

              // 根据解析出的数据设置分销类型
              if (dtoData.acctTypeCode) {
                this.formData.distributionType = dtoData.acctTypeCode === '57' ? '企业' : '个人';
                console.log('根据receiverBody设置分销类型:', dtoData.acctTypeCode, this.formData.distributionType);
                // 确保立即更新表单字段，以便后续正确判断企业或个人表单
                this.updateFormFields();
              }
            } catch(e) {
              console.error('解析receiverBody失败:', e, historyData.receiverBody);
            }
          }

          // 如果是企业类型，设置企业特有信息
          if (this.formData.distributionType === '企业' && dtoData) {
            this.formData.companyName = dtoData.receiverName || '';
            this.formData.businessAccount = dtoData.licenseNo || '';
            this.formData.businessName = dtoData.licenseName || '';
            this.formData.idCardNumber = dtoData.legalPersonCertificateNo || '';
            this.formData.legalPersonName = dtoData.legalPersonName || '';
          }

          // 设置账户信息
          if (dtoData) {
            this.formData.accountNumber = dtoData.acctNo || '';
            this.formData.accountName = dtoData.acctName || '';
            this.formData.acctCertificateNo = dtoData.acctCertificateNo || '';
            this.formData.accountBankBranch = dtoData.acctOpenBankCode || '';
            this.formData.accountBranchName = dtoData.acctOpenBankName || '';
            this.formData.accountBankCode = dtoData.acctClearBankCode || '';
          }

          // 初始化附件列表
          this.formData.fileAttachments = [];

          // 处理附件信息
          let attachList = [];
          if (dtoData && dtoData.attachList && Array.isArray(dtoData.attachList)) {
            attachList = JSON.parse(JSON.stringify(dtoData.attachList));
          }

          // 尝试解析receiverFile字段中的文件信息
          let receiverFileObj = {};
          try {
            if (historyData.receiverFile) {
              receiverFileObj = JSON.parse(historyData.receiverFile);
              console.log('解析receiverFile成功:', receiverFileObj);
            }
          } catch (e) {
            console.error('解析receiverFile失败:', e);
          }

          // 构建新的附件列表
          const newAttachList = [];

          // 从receiverFile中提取附件信息
          Object.keys(receiverFileObj).forEach(attachType => {
            const filePath = receiverFileObj[attachType];

            // 创建附件对象
            const attachItem = {
              attachType: attachType,
              attachName: this.getAttNameByType(attachType),
              attachStorePath: filePath,
              originalFilePath: filePath
            };

            newAttachList.push(attachItem);
          });

          // 将两个列表合并，优先使用receiverFile中的数据
          this.formData.fileAttachments = newAttachList;
          console.log('回显附件列表:', this.formData.fileAttachments);

          // 回显图片到表单字段
          try {
            if (receiverFileObj.BUSINESS_LICENCE) {
              this.formData.businessLicense = this.file_ctx + receiverFileObj.BUSINESS_LICENCE;
            }
            if (receiverFileObj.FR_ID_CARD_FRONT || receiverFileObj.ID_CARD_FRONT) {
              this.formData.CardFace = this.file_ctx + (receiverFileObj.FR_ID_CARD_FRONT || receiverFileObj.ID_CARD_FRONT);
            }
            if (receiverFileObj.FR_ID_CARD_BEHIND || receiverFileObj.ID_CARD_BEHIND) {
              this.formData.CardNationalEmblemFace = this.file_ctx + (receiverFileObj.FR_ID_CARD_BEHIND || receiverFileObj.ID_CARD_BEHIND);
            }
            if (receiverFileObj.MERCHANT_PHOTO) {
              this.formData.merchantPhoto = this.file_ctx + receiverFileObj.MERCHANT_PHOTO;
            }
            if (receiverFileObj.SHOPINNER) {
              this.formData.shopInnerPhoto = this.file_ctx + receiverFileObj.SHOPINNER;
            }
            if (receiverFileObj.OTHERS) {
              this.formData.openAccountProve = this.file_ctx + receiverFileObj.OTHERS;
            }
          } catch (e) {
            console.error('回显图片失败:', e);
          }

          // 强制更新表单字段
          this.updateFormFields();

        } catch (error) {
          console.error('填充历史数据失败:', error);
        } finally {
          // 隐藏加载指示器
          this.isLoading = false;
        }
      },

      // 根据附件类型获取附件名称
      getAttNameByType(attachType) {
        const typeNameMap = {
          'BUSINESS_LICENCE': '营业执照',
          'FR_ID_CARD_FRONT': '法人身份证正面',
          'FR_ID_CARD_BEHIND': '法人身份证反面',
          'ID_CARD_FRONT': '身份证正面',
          'ID_CARD_BEHIND': '身份证反面',
          'MERCHANT_PHOTO': '商户门头照',
          'SHOPINNER': '商铺内部照片',
          'OTHERS': '开户许可证照片'
        };

        return typeNameMap[attachType] || '其他附件';
      },
    }
  }
</script>

<style lang="scss">
  .agreement-row{
    transform: translateX(-32rpx);
    margin-bottom: 5px;
    padding: 0 0 20rpx 0 !important;
  }
  .page{
    width: 100vw;
    height: calc(100vh - 1px); /* 使用calc(100vh - 1px)解决安卓兼容问题 */
    position: fixed;
    top: 0;
    left: 0;
    overflow-y: auto;
    padding-bottom: 0;
  }
  .scroll-container {
    height: auto; /* 使用auto解决安卓兼容问题 */
    width: 100%;
  }
  .masking{
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    width: 750rpx;
    height: 409rpx;
  }
  .boxTab{
    width: 100vw;
    box-sizing: border-box;
    padding: 0 32rpx;
    padding-bottom: 100rpx; /* u63d0u4f9bu5408u9002u7684u5e95u90e8u95f4u8ddd */
  }
  .test-data-button {
    position: fixed;
    top: 180rpx;
    right: 30rpx;
    width: 180rpx;
    height: 60rpx;
    background-color: #f0f9ff;
    border: 1px solid #2196f3;
    border-radius: 30rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    font-size: 24rpx;
    color: #2196f3;
    z-index: 10;
  }

  /* 审核状态提示样式 */
  .audit-tip {
    width: 686rpx;
    margin: 24rpx 32rpx;
    padding: 24rpx;
    background-color: #FFF5F0;
    border-radius: 12rpx;
    display: flex;
    align-items: flex-start;
  }

  .audit-tip-icon {
    width: 36rpx;
    height: 36rpx;
    line-height: 36rpx;
    text-align: center;
    background-color: #FF5500;
    color: #FFFFFF;
    border-radius: 50%;
    margin-right: 16rpx;
    font-weight: bold;
    flex-shrink: 0;
  }

  .audit-tip-content {
    flex: 1;
  }

  .audit-tip-title {
    font-size: 28rpx;
    font-weight: 500;
    color: #1D2029;
    margin-bottom: 8rpx;
  }

  .audit-tip-reason {
    font-size: 24rpx;
    color: #4E5569;
    line-height: 1.5;
  }

  /* 审核中状态提示 */
  .audit-processing {
    width: 686rpx;
    margin: 24rpx 32rpx;
    padding: 40rpx 24rpx;
    background-color: #F0F9FF;
    border-radius: 12rpx;
    text-align: center;
  }

  .audit-processing-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #1D2029;
    margin-bottom: 16rpx;
  }

  .audit-processing-desc {
    font-size: 28rpx;
    color: #4E5569;
  }

  /* 成功提示框样式 */
  .success-popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;

    &-mask {
      position: absolute;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.6);
    }

    &-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 560rpx;
      background-color: #fff;
      border-radius: 16rpx;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    &-title {
      width: 100%;
      font-size: 32rpx;
      font-weight: 500;
      color: #1D2029;
      text-align: center;
      padding: 40rpx 0 20rpx;
      border-bottom: 2rpx solid #EAEBF0;
    }

    &-body {
      padding: 60rpx 30rpx;
      font-size: 28rpx;
      color: #4E5569;
      text-align: center;
      width: 100%;
    }

    &-btn {
      width: 100%;
      height: 100rpx;
      line-height: 100rpx;
      text-align: center;
      color: #00B484;
      font-size: 32rpx;
      font-weight: 500;
      border-top: 2rpx solid #EAEBF0;
    }
  }

  /* 加载指示器样式 */
  .loading-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .loading-spinner {
    width: 80rpx;
    height: 80rpx;
    border: 6rpx solid #f3f3f3;
    border-top: 6rpx solid #00B484;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .loading-text {
    margin-top: 20rpx;
    font-size: 30rpx;
    color: #333;
    font-weight: 500;
  }

  /* 错误提示框样式 */
  .error-popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;

    &-mask {
      position: absolute;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.6);
    }

    &-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 560rpx;
      background-color: #fff;
      border-radius: 16rpx;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    &-title {
      width: 100%;
      font-size: 32rpx;
      font-weight: 500;
      color: #1D2029;
      text-align: center;
      padding: 40rpx 0 20rpx;
      border-bottom: 2rpx solid #EAEBF0;
    }

    &-body {
      padding: 60rpx 30rpx;
      font-size: 28rpx;
      color: #4E5569;
      text-align: center;
      width: 100%;
    }

    &-btn {
      width: 100%;
      height: 100rpx;
      line-height: 100rpx;
      text-align: center;
      color: #00B484;
      font-size: 32rpx;
      font-weight: 500;
      border-top: 2rpx solid #EAEBF0;
    }
  }
</style>
