<template>
  <page>
    <view slot="content" class="main-body" style="padding-bottom: 164upx;">
      <item :pdList="pdList" v-if="!$validate.isNull(pdList)" />
      <view class="no-data" v-else>
        <em></em>
        <text>暂无数据</text>
      </view>
      <view class="btn-view-main">
        <view class="btn-view-li">
          <view class="btn-view-li-view" @tap="navtoGo('PatientsAddTep')">添加家庭就诊人</view>
        </view>
      </view>
    </view>
  </page>
</template>

<script>
  import Item from "./components/item.vue"
  import { mapState } from "vuex"
  export default {
    components: {
      Item
    },
    data () {
      return {
        pdList: [],
        $validate: this.$validate
      }
    },
    computed: {
      ...mapState('user', {
        recordUserInfo: state => state.recordUserInfo,
        codeUserInfo: state => state.codeUserInfo
      }),
    },
    onUnload(){
      // #ifdef MP-WEIXIN
      this.handleClickTrack('EndOperationDetailsPageView')
      // #endif
    },
    methods: {
      // #ifdef MP-WEIXIN
      handleClickTrack(type){
        let pages = getCurrentPages()
        let current = pages[pages.length - 1]; // 获取到当前页面的信息
        let pageInfo = __wxConfig.page && __wxConfig.page[`${current.route}.html`] || ''; // 内部属性
        getApp().globalData.sensors.track(type,
          {
            'page_name' : pageInfo?.window?.navigationBarTitleText || '',
            'first_operation_name' : '功能使用',
            'second_operation_name' : '家庭就诊人',
          }
        ) 
      },
      // #endif
      navtoGo(name = '', params = {}) {
        this.$navto.push(name, params)
      },
      async getPatientinfoQueryListByMasterId () {
        const res = await this.$api.patientinfo.patientinfoQueryListByMasterId({masterId: this.codeUserInfo.id})
        this.pdList = res.data.map(item => {
          return {
            ...item,
            genderText: this.getEnumText(item.gender, [{label: '男',value: 1},{label: '女',value: 2},{label: '未知',value: 3}]),
            age: item.birthDate ? (new Date().getFullYear() - new Date(item.birthDate).getFullYear()) : ''
          }
        })
        this.$forceUpdate()
      },
      getEnumText (value,list) {
        const itemType = list.find(item => item.value === value)
        return (itemType && Object.keys(itemType).length) ? itemType.label : ''
      },
    },
    onShow () {
      const that = this
      if(!that.$validate.isNull(this.codeUserInfo)) {
        this.getPatientinfoQueryListByMasterId()
      }
      // #ifdef MP-WEIXIN
      this.handleClickTrack('OperationDetailsPageView')
      // #endif
    },
  }
</script>

<style lang="scss" scoped>
  page {
    background-color: #f7f7f7;
  }
  .btn-view-main{
    position: -webkit-fixed;
    // position: sticky;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    margin-top: 20upx;
    @include downBoxShadow(-4upx, 0, 6upx, 1, 222, 222, 222);
    padding: 30upx 0 40upx;
    background: #fff;
    box-sizing: border-box;
    z-index: 2;
    text-align: center;
    .btn-view-li{
      display: inline-block;
      vertical-align: middle;
      box-sizing: border-box;
      overflow: hidden;
      width: 100%;
      padding: 0 30upx;
      .btn-view-li-view{
        height: 64upx;
        line-height: 64upx;
        font-size: 32upx;
        text-align: center;
        @include rounded(32upx);
        //background:linear-gradient(90deg,rgba(255,185,36,1),rgba(254,219,55,1));
        background: $topicC;
        color: #fff;
      }
    }
  }
  .no-data{
    width: 100%;
    text-align: center;
    padding:340upx 0 0 30upx;
    em{
      @include iconImg(300, 200, '/system/invalid/icon-no-data.png');
    }
    text{
      display: block;
      font-size: 28upx;
      margin-top: 10upx;
    }
  }
</style>
