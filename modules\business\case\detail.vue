<template>
    <page>
        <view style="padding: 32rpx;overflow: auto;height: 100%;" slot="content">
            <view class="user" v-if="orderInfo.patientInfoVo">
                <image :src="orderInfo.chatList.headPath ? file_ctx + orderInfo.chatList.headPath : file_ctx + defaultAvatar" class="user-head"></image>
                <view class="user-info">
                    <view class="user-info-name">
                        {{ orderInfo.patientInfoVo.name }}
                    </view>
                    <view class="user-info-gender-age">
                        {{ `${orderInfo.patientInfoVo.gender === 1 ? '男' : orderInfo.patientInfoVo.gender === 2 ? '女' : '未知'} | ${orderInfo.patientInfoVo.age}岁` }}
                    </view>
                </view>
            </view>
            
            <!-- @tap="navtoGo('')" -->
            <view class="menu-list">
                <view class="li">
                    <view class="l">
                        <image class="width-height-atuo" :src="$static_ctx + 'image/business/icon-public-security-authentication.png'"/>
                    </view>
                    <view class="m">基本信息</view>
                    <view class="r">
                        <image class="width-height-atuo" :src="$static_ctx + 'image/business/icon-more.png'"/>
                    </view>
                </view>
            </view>
            <view class="menu-list">
                <view class="li">
                    <view class="l">
                        <image class="width-height-atuo" :src="$static_ctx + 'image/business/icon-public-security-authentication.png'"/>
                    </view>
                    <view class="m">健康信息</view>
                    <view class="r">
                        <image class="width-height-atuo" :src="$static_ctx + 'image/business/icon-more.png'"/>
                    </view>
                </view>
            </view>

            <view class="consult">
                <view class="menu-list" @tap="checkChatRecord">
                    <view class="li">
                        <view class="m" style="left: 0;">咨询记录</view>
                        <view class="more-text">查看更多</view>
                        <view class="r">
                            <image class="width-height-atuo" :src="$static_ctx + 'image/business/icon-more.png'"/>
                        </view>
                    </view>
                </view>
            </view>

            <view class="case">
                <view class="case-top">
                    <view class="case-top-time">{{ orderInfo.createTimeText }}</view>
                    <view class="case-top-desc">图文咨询</view>
                </view>
                <view class="case-key">资讯类型</view>
                <view class="case-value">{{ orderInfo.gfConsultTypeName }}</view>
                <view class="case-key">科室</view>
                <view class="case-value">{{ orderInfo.gfDepartmentName }}</view>
                <view class="case-key">病情描述</view>
                <view class="case-value">{{ orderInfo.gfIssue }}</view>
                <view class="case-key">病历资料</view>
                <image mode="aspectFit" style="max-height: 400rpx;" v-if="orderInfo.gfAttachs" :src="file_ctx + orderInfo.gfAttachs" />
                <view class="case-value" style="text-align: center;" v-else>患者没有上传图片</view>
            </view>

            <!-- <view style="padding: 24rpx 0;" @click="checkChatRecord" class="tips-text">查看本次历史会话消息>></view> -->
        </view>
    </page>
</template>

<script>
export default {
    data () {
        return {
            // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
            $constant: this.$constant,
            $common: this.$common,
            $accurateConversion: this.$accurateConversion,
            file_ctx: this.file_ctx,
            $static_ctx: this.$static_ctx,
            $timePlugin: this.$timePlugin,
            orderInfo: {},
            defaultAvatar: 'static/image/system/avatar/icon-default-avatar.png'
        }
    },
    async onLoad () {
        this.$uniPlugin.loading('加载中...', false)
        const { id } = this.$Route.query
        const res = await this.$api.chat.orderGetById({ id }).catch(() => {
            this.$uniPlugin.hideLoading()
        })
        this.$uniPlugin.hideLoading()
        this.orderInfo = res.data
        this.orderInfo.createTimeText = this.$common.formatDate(new Date(res.data.createTime), 'yyyy-MM-dd HH:mm')
    },
    methods: {
        checkChatRecord () {
            this.navtoGo('ChatRecord', {id: this.orderInfo.id})
        },
        navtoGo(url, obj) {
            const parameter = obj || {}
            this.$navto.push(url, parameter)
        },
    }
}
</script>

<style lang="scss" scoped>
.user {
    display: flex;
    flex-direction: row;
    height: 120rpx;
    margin-bottom: 24rpx;

    &-head {
        width: 120rpx;
        height: 120rpx;
        overflow: hidden;
        @include rounded(8rpx);
    }
    &-info {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 100%;
        font-size: 26rpx;
        padding: 4rpx 0 4rpx 24rpx;
        box-sizing: border-box;

        &-name {
            font-weight: 550;
            font-size: 28rpx;
        }

        &-gender-age {
            color: #666;
        }
    }
}
.menu-list {
    display: flex;
    justify-content: space-between;
    height: 48upx;
    padding: 30upx 0;
    border-bottom: 2upx solid $contentDdt;
    &:last-of-type {
        border-bottom: none;
    }
    .li{
        height: 100%;
        width: 100%;
        position: relative;
        .l{
            height: 48upx;
            width: 48upx;
            position: absolute;
            left: 0upx;
            top: 0upx;
        }
        .more-text {
            position: absolute;
            right: 52upx;
            color: $topicC;
        }
        .m{
            height: 48upx;
            line-height: 48upx;
            color: #000000;
            font-size: 32upx;
            position: absolute;
            left: 60upx;
            right: 60upx;
            top: 0upx;
        }
        .r{
            height: 48upx;
            width: 48upx;
            position: absolute;
            right: 0upx;
            top: 0upx;
        }
    }
}
.consult {
    border-top: 1px solid #e5e5e5;
    border-bottom: 10px solid #efefef;
    margin-top: 200rpx;
}
.case {
    padding: 32upx 0;
    &-top {
        display: flex;
        flex-direction: row;
        align-items: center;

        &-time {
            font-weight: 550;
            font-size: 36upx;
        }
        &-desc {
            font-size: 24upx;
            color: #ff9800;
            margin-left: 32upx;
            overflow: hidden;
            border: 2upx solid #ff9800;
            border-radius: 6upx;
            padding: 0 4upx 0 4upx;
            left: 0;
        }
    }
    &-key {
        font-size: 32upx;
        font-weight: 550;
        padding: 24upx 0;
    }
    &-value {
        font-size: 32upx;
        color: #666;
    }
}

.width-height-atuo{
  width: 100%;
  height: 100%;
}
.m-t-10{
  margin-top: 10upx;
}

.tips-text {
    text-align: center;
    color: #888;
    font-size: 26upx;
}
</style>