export const EventTypeList = [
  {
    "id":1,
    "desc":"进入极速咨询",
    path:'pages/index/index',
    url:'static/image/business/pharmacy-cyclopedia/icon-sign-conin-default.png'
  },
  {
    "id":2,
    "desc":"进行诊后点评",
    path:'modules/activity/pages/diagnosis/add',
    name:'diagnosisCommentAdd',
    url:'static/image/business/pharmacy-cyclopedia/icon-sign-conin-default.png'
  },
  {
    "id":3,
    "desc":"浏览健康自测",
    path:'modules/activity/health-testing/index?backTaskFlag=true',
    name:'HealthTesting',
    url:'static/image/business/pharmacy-cyclopedia/icon-sign-conin-testing.png',
  },
  {
    "id":4,
    "desc":"完成健康自测",
    path:'modules/activity/health-testing/index?backTaskFlag=true',
    name:'HealthTesting',
    url:'static/image/business/pharmacy-cyclopedia/icon-sign-conin-testing.png',
  },
  {
    "id":5,
    "desc":"订阅健康自测",
    path:'modules/activity/health-testing/index?backTaskFlag=true',
    name:'HealthTesting',
    url:'static/image/business/pharmacy-cyclopedia/icon-sign-conin-testing.png',
  },
  {
    "id":6,
    "desc":"分享健康自测",
    path:'modules/activity/health-testing/index?backTaskFlag=true',
    name:'HealthTesting',
    url:'static/image/business/pharmacy-cyclopedia/icon-sign-conin-testing.png',
  },
  {
    "id":7,
    "desc":"浏览用药提醒",
    path:'modules/pharmacy/pharmacy-cyclopedia/pharmacy-remind/index?backTaskFlag=true',
    name:'PharmacyRemind',
    url:'static/image/business/pharmacy-cyclopedia/icon-sign-conin-medication.png',
  },
  {
    "id":8,
    "desc":"创建用药提醒",
    path:'modules/pharmacy/pharmacy-cyclopedia/pharmacy-remind/index?backTaskFlag=true',
    name:'PharmacyRemind',
    url:'static/image/business/pharmacy-cyclopedia/icon-sign-conin-medication.png',
  },
  {
    "id":9,
    "desc":"进入祈福许愿",
    path:'modules/activity/calabash/calabashWebview',
    url:'static/image/business/pharmacy-cyclopedia/icon-sign-conin-pray.png',
    name:'calabashWebview'
  },
  {
    "id":10,
    "desc":"完成祈福许愿",
    path:'modules/activity/calabash/calabashWebview',
    url:'static/image/business/pharmacy-cyclopedia/icon-sign-conin-pray.png',
    name:'calabashWebview'
  },
  {
    "id":11,
    "desc":"进入葫芦直播",
    path:'modules/directseeding/video-list/index',
    url:'static/image/business/pharmacy-cyclopedia/icon-sign-conin-video-live.png',
    name:'directseedingVideoListIndex'
  },
  {
    "id":12,
    "desc":"观看直播",
    path:'modules/directseeding/video-list/index?backTaskFlag=true',
    url:'static/image/business/pharmacy-cyclopedia/icon-sign-conin-video-live.png',
    name:'directseedingVideoListIndex'
  },
  {
    "id":13,
    "desc":"观看回放",
    path:'modules/directseeding/video-list/index?backTaskFlag=true',
    url:'static/image/business/pharmacy-cyclopedia/icon-sign-conin-video-live.png',
    name:'directseedingVideoListIndex'
  },
  {
    "id":14,
    "desc":"预约直播",
    path:'modules/directseeding/video-list/index?backTaskFlag=true',
    url:'static/image/business/pharmacy-cyclopedia/icon-sign-conin-video-live.png',
    name:'directseedingVideoListIndex'
  },
  {
    "id":15,
    "desc":"分享直播/回放",
    path:'modules/directseeding/video-list/index?backTaskFlag=true',
    url:'static/image/business/pharmacy-cyclopedia/icon-sign-conin-video-live.png',
    name:'directseedingVideoListIndex'
  },
  {
    "id":16,
    "desc":"评论直播",
    path:'modules/directseeding/video-list/index?backTaskFlag=true',
    url:'static/image/business/pharmacy-cyclopedia/icon-sign-conin-video-live.png',
    name:'directseedingVideoListIndex'
  },
  {
    "id":17,
    "desc":"浏览帖子",
    path:'pages/circle-home/index?backTaskFlag=true',
    url:'static/image/business/pharmacy-cyclopedia/icon-sign-conin-default.png'
  },
  {
    "id":18,
    "desc":"观看视频",
    path:'pages/circle-home/index?backTaskFlag=true',
    url:'static/image/business/pharmacy-cyclopedia/icon-sign-conin-default.png'
  },
  {
    "id":19,
    "desc":"点赞帖子",
    path:'pages/circle-home/index?backTaskFlag=true',
    url:'static/image/business/pharmacy-cyclopedia/icon-sign-conin-like.png'
  },
  {
    "id":20,
    "desc":"收藏帖子",
    path:'pages/circle-home/index?backTaskFlag=true',
    url:'static/image/business/pharmacy-cyclopedia/icon-sign-conin-collection.png'
  },
  {
    "id":21,
    "desc":"分享帖子",
    path:'pages/circle-home/index?backTaskFlag=true',
    url:'static/image/business/pharmacy-cyclopedia/icon-sign-conin-default.png'
  },
  {
    "id":22,
    "desc":"评论帖子",
    path:'pages/circle-home/index?backTaskFlag=true',
    url:'static/image/business/pharmacy-cyclopedia/icon-sign-conin-default.png'
  },
  {
    "id":23,
    "desc":"发表帖子",
    path:'pages/post-message/index?backTaskFlag=true',
    url:'static/image/business/pharmacy-cyclopedia/icon-sign-conin-posts.png'
  },
  {
    "id":24,
    "desc":"发表视频",
    path:'pages/post-message/index?backTaskFlag=true',
    url:'static/image/business/pharmacy-cyclopedia/icon-sign-conin-live.png'
  },
  {
    "id":25,
    "desc":"添加家庭就诊人",
    path:'modules/business/patients/index?backTaskFlag=true',
    url:'static/image/business/pharmacy-cyclopedia/icon-sign-conin-default.png',
    name:'Patients'
  },
  {
    "id":26,
    "desc":"完成每日辟谣问答",
    path:'modules/pharmacy/pharmacy-cyclopedia/everyday-rumour/index?backTaskFlag=true',
    name:'EverydayRumour',
    url:'static/image/business/pharmacy-cyclopedia/icon-sign-conin-rumour.png'
  },
  {
    "id":27,
    "desc":"每日辟谣签到",
    path:'modules/pharmacy/pharmacy-cyclopedia/everyday-rumour/activity-prefecture?backTaskFlag=true',
    name:'ActivityPrefecture',
    url:'static/image/business/pharmacy-cyclopedia/icon-sign-conin-rumour.png'
  },
]
