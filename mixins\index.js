// vue中提供了一种混合机制--mixins，用来更高效的实现组件内容的复用
// 混入 (mixins) 是一种分发 Vue 组件中可复用功能的非常灵活的方式。
// 混入对象可以包含任意组件选项。当组件使用混入对象时，
// 所有混入对象的选项将被混入该组件本身的选项
import sysConstant from '@/constant/modules/system'
import nodesMixin from './nodes-utils'
import $env from '@/config/env'
import common from '@/common/util/main'
const { shareTitle, shareLogo, shareDesc } = sysConstant
import { getQueryStr } from '@/utils/index'
import api from '@/service/api'
// #ifdef H5
import wx from 'weixin-js-sdk'
// #endif
import navto from '@/router/config/nav-to'
console.log('混入',nodesMixin);
import integration from  "@/integration"
import { mapState } from 'vuex'
// #ifndef MP-ALIPAY
let integrationBuried = integration.createIntegrationBuried()
// #endif
const mixin = {
  data() {
    return {
      shareConfig: {
        shareTitle: shareTitle,
        shareImg: shareLogo ? $env.file_ctx + shareLogo : '',
        shareDesc: shareDesc,
      },
      // #ifndef MP-ALIPAY
      IntegrationData:integrationBuried.IntegrationData,
      // #endif
      // #ifdef MP-ALIPAY
      isAliPay: true,
      // #endif
      // #ifndef MP-ALIPAY
      isAliPay: false,
      // #endif
    }
  },
  computed: {
    ...mapState('user', {
      accountId: state => state.accountId
    })
  },
  methods: {
	  ...nodesMixin.methods,
    gotoLogin(){
      navto.push('Login')
    },
    // #ifdef MP-WEIXIN
    setTabBarIndex(index) {
      if (typeof this.$mp.page.getTabBar === 'function' &&
      this.$mp.page.getTabBar()) {
        this.$mp.page.getTabBar().setData({
          selected: index
        })
      }
    },
    setTabBarBadge(index, badge) {
      if (typeof this.$mp.page.getTabBar === 'function' &&
      this.$mp.page.getTabBar()) {
        this.$mp.page.getTabBar().setBadge(index, badge)
      }
    },
    // #endif
    async wxh5ShareInit() {
      // #ifdef H5
      const UA = navigator.userAgent.toLowerCase()
      // 是否是微信浏览器并存在accountId
      if(UA.match(/MicroMessenger/i) == 'micromessenger' && this.accountId) {
          this.createJsapiSignature() // mixins wxShare
      } else {
          // 重定向到微信包装链
          if (process.env.NODE_ENV !== 'development' && !['c.xhl.greenboniot.cn'].includes(window.location.host)) {
            api.common.wxGetCommonOauth2WrapUrl({ url: encodeURIComponent(window.location.href) }).then(res => {
              window.location.href = res
            })
          }
      }
      // #endif
    },
    createJsapiSignature () {
      let url = window.location.href.split('#')[0]
      let shareUrl = window.location.href.replace(`accountId=${getQueryStr('accountId')}&`, '')
      shareUrl = shareUrl.replace(`&accountId=${getQueryStr('accountId')}`, '')
      shareUrl = shareUrl.replace(`accountId=${getQueryStr('accountId')}`, '')
      // let url = 'gift.ngrok.greenboniot.cn'
      const that = this

      // const path = encodeURIComponent(window.location.href)
      const path = encodeURIComponent(url)
      this.$api.common.createJsapiSignature({ path }).then(res => {
        const data = res.data
        const appId = data.appId
        const timestamp = data.timestamp
        const signature = data.signature
        const nonceStr = data.nonceStr
        that.appId = appId

          wx.config({
            debug: false,
            appId: appId,
            timestamp: timestamp, // 必填，生成签名的时间戳
            nonceStr: nonceStr, // 必填，生成签名的随机串
            signature: signature, // 必填，签名，见附录1
            jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData', 'wx-open-launch-weapp'], // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
            openTagList: ['wx-open-launch-weapp']
          })
          // 通过ready接口处理成功验证
          wx.ready(function () {
            wx.checkJsApi({
              jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData', 'wx-open-launch-weapp'],
              success: function (res) {
                console.log('可用')
              },
              fail: (err) => {
                console.log(err, '不可用 checkJsApi')
                // alert("不可用")
              }
            })

            const { shareTitle = '分享标题', shareImg = '', shareDesc = '分享描述' } = that.shareConfig
            wx.updateAppMessageShareData({
                title: shareTitle, // 分享标题
                desc: shareDesc, // 分享描述
                link: shareUrl, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
                imgUrl: shareImg, // 分享图标
                success: function () {
                    // 用户确认分享后执行的回调函数 添加分享记录
                    // alert('设置成功')
                },
                fail: (err) => {
                console.log(err, '不可用')
                // alert("不可用")
              }
            })

            // 自定义“分享给朋友”及“分享到QQ”按钮的分享内容（1.4.0）
            wx.updateTimelineShareData({
                title: shareTitle, // 分享标题
                desc: shareDesc, // 分享描述
                link: shareUrl, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
                imgUrl: shareImg, // 分享图标
                success: function () {
                    // 用户确认分享后执行的回调函数 添加分享记录
                    // alert('设置成功')
                },
                fail: (err) => {
                // alert(JSON.stringify(err))
                console.log(err, '不可用')
                // alert("不可用")
              }
            })
          })
          // 通过error接口处理失败验证
          wx.error(function (res) {
          })
        })
    },
    getCurrentPath(){
      let pages = getCurrentPages();
      return pages[pages.length - 1].$page.fullPath;
    },
    // 分享混入参数方法
    mixinsShareOptions(){
      const gbScene = common.getKeyVal('system', 'gbScene', true);
      let query = `scene=gs%25253D${gbScene}`;
      let routerPath = this.getCurrentPath().split('?')[0] + '?';
      return {
        path:routerPath + query
      }
    }
  },
  onLoad(res){
    // #ifndef MP-ALIPAY
    integrationBuried.IntegrationOnLoad.call(this,res)
    // #endif
  },
  onShareAppMessage(res){
    // #ifndef MP-ALIPAY
    integrationBuried.IntegratiOnShare.call(this,res)
    // #endif
  },
  onUnload(res){
    // #ifndef MP-ALIPAY
    integrationBuried.IntegratiOnUnload.call(this,res)
    // #endif
  },
  onShow(res){
    // #ifndef MP-ALIPAY
    integrationBuried.IntegrationOnShow(res,this)
    // #endif
  }
}
export default mixin
