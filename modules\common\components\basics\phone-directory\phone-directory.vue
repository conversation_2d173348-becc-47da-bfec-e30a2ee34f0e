<template>
<!--	<view class="phone-main" :style="{height: winHeight + 'px'}">-->
  <view class="phone-main">
<!--		<view class="phone-main-search">-->
<!--			<navigator :url="'phone-search?phones=' + phonesEscape" hover-class="none">-->
<!--				<input disabled="false" class="phone-main-input" type="text" placeholder="请输入要搜索的联系人"/>-->
<!--			</navigator>-->
<!--		</view>-->
		<view class="phoneDirectory directory-custom" :class="{'m-b-0': !chooseState}">
			<phone-list
      class="phone-list-body"
      :chooseState="chooseState"
      :iconState="iconState"
      :imgState="imgState"
			:phones="phones"
			:letter="letter"
			:scrollAnimationOFF="scrollAnimationOFF"
			@change="handlePhoneListIndex"
			@reset="handleReset"
			@handleClick="handleClick"
			>
			</phone-list>
			<phone-alphabet
      class="alphabet-custom"
			:phones="phones"
			:phoneListIndex="phoneListIndex"
			@change="handleDatasetKey"
			@scrollAnimationOFF="handleScrollAnimationOFF"
			@reset="handleReset"
			>
			</phone-alphabet>
		</view>
	</view>
</template>

<script>
import phoneList from './phone-list.vue'
import phoneAlphabet from './phone-alphabet.vue'

export default {
  name: 'PhoneDirectory',
  components: {
    phoneList,
    phoneAlphabet
  },
  props: {
    phones: Object,
    default: false,
    // 控制icon是否显示
    iconState: {
      type: Boolean,
      default() {
        return true
      }
    },
    // 控制图片是否显示
    imgState: {
      type: Boolean,
      default() {
        return true
      }
    },
    // 控制单选还是多选（默认多选）
    chooseState: {
      type: Boolean,
      default() {
        return true
      }
    }
  },
  data() {
    return {
      winHeight: 0,
      letter: 'A',
      scrollAnimationOFF: true,
      phoneListIndex: 'A',
      reset: true
    }
  },
  computed: {
    phonesEscape() {
      return escape(JSON.stringify(this.phones))
    }
  },
  mounted() {
    const windowHeight = uni.getSystemInfoSync().windowHeight

    // #ifndef APP-PLUS
    this.winHeight = windowHeight
    // #endif

    // #ifdef APP-PLUS
    this.winHeight = windowHeight - 56
    // #endif

    if (!this.phones) {
      uni.showToast({
        title: '没有数据',
        icon: 'none',
        mask: false,
        duration: 1500
      })
    }
  },
  methods: {
    handleClick(e) {
      this.$emit('paramClick', e)
    },
    handleDatasetKey(val) {
      this.letter = val
    },
    handleScrollAnimationOFF(val) {
      this.scrollAnimationOFF = val
    },
    handlePhoneListIndex(val) {
      if (this.reset) {
        this.phoneListIndex = val
      }
    },
    handleReset(val) {
      if (val) {
        this.letter = ''
      }
      this.reset = val
    }

  }
}
</script>

<style lang="scss" scoped>
.phone-main{
	display: flex;
	flex-direction: column;
	overflow: hidden;
  width: 100%;
}
.directory-custom {
  margin: 0upx 60upx 98upx 0;
}
.m-b-0 {
  margin-bottom: 0;
}
.alphabet-custom {
  position: absolute;
  right: 0;
  top: 88upx;
  border-bottom: 59upx;
}
.phoneDirectory{
	display: flex;
	flex-direction: row;
}
.phone-main-search{
	background-color: #fff;
	padding: 10upx 20upx;
	border-bottom: 2upx solid $contentDdt;
}

.phone-main-input{
	font-size:28upx;
	border: 2upx solid $contentDdt;
	border-radius: 3px;
	padding: 10upx 20upx 10upx 20upx;
}
.phone-list-body{
  width: 100%;
}
</style>
