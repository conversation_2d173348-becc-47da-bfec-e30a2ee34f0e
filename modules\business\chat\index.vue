<template>
  <view @touchstart="pageTouchstart">
    <page>
      <view slot="content">
        <message-list v-if="!$validate.isNull(chatItem)" ref="messageList" />
        <!-- <select-consult-type  /> -->

        <template v-if="guideMode">
          <!-- 选择科室 -->
          <select-department v-if="guideMode === 'department'" />

          <!-- 输入资讯问题 -->
          <issue v-else-if="guideMode === 'issue'" />

          <!-- 选择咨询人档案 -->
          <select-record v-else-if="guideMode === 'patient'" />

          <!-- 选择资讯类型 -->
          <select-consult-type v-else-if="guideMode === 'consultType'" />


          <!-- 订阅小程序消息通知 -->
        </template>

        <!-- 底部聊天输入框 -->
        <input-box v-else-if="!$validate.isNull(orderDetail) && orderDetail.consultStatus !== 3" ref="inputBox" :answerDuration="answerDuration" @handleClearTimer="stopTimer" />

        <!-- 免责声明弹窗 -->
        <disclaimers :updatecount='disclaimersUpdateCount'></disclaimers>
      </view>
    </page>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import inputBox from './components/input-box.vue'
import messageList from './components/message.vue'
import selectDepartment from './components/select-department.vue'
import issue from './components/issue.vue'
import selectRecord from './components/select-record.vue'
import selectConsultType from './components/select-consult-type.vue'

import HandleConsult from '@/service/ext/modules/websocket/receive/HandleConsult'
import disclaimers from '@/components/basics/disclaimers/index.vue'
import env from '@/config/env'

export default {
  components: {
    inputBox,
    messageList,
    selectDepartment,
    issue,
    selectRecord,
    selectConsultType,
    disclaimers
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      $appId: this.$appId,
      PointY: 0, //坐标位置
      timer: null,
      disclaimersUpdateCount:0,
      timer2:null,
      answerDuration:0,
    }
  },
  onReady() {
    console.log('this.$Route-----------------------', this.$Route)
    // uni.setNavigationBarTitle({
    //   title: this.chatItem.name
    // });

  },
  onLoad() {
    const query = this.$Route.query
    console.log(query,'queryquery9999')
    if(query.type == 1){
      this.disclaimersUpdateCount += 1
    }
    // #ifdef MP-WEIXIN
    wx.showShareMenu({
      withShareTicket: true,
      //设置下方的Menus菜单，才能够让发送给朋友与分享到朋友圈两个按钮可以点击
      menus:["shareAppMessage", "shareTimeline"]
    })
    this.handleClickTrack('OperationDetailsPageView')
    // #endif
  },
  onShareAppMessage(res) {
    return {
      title: '点击立即咨询', //分享的名称
      path: 'modules/business/chat/index',
      mpId: this.$appId, //此处配置微信小程序的AppId
      imageUrl: this.file_ctx + 'static/image/business/icon-im-cover.jpg'
    }
  },
  //分享到朋友圈
  onShareTimeline(res) {
    return {
      title: '点击立即咨询',
      path: 'modules/business/chat/index',
      imageUrl: this.file_ctx + 'static/image/business/icon-im-cover.jpg'
    }
  },
  computed: {
    ...mapState('chat', {
      chatItem: state => state.chatItem,
      pageIsShow: state => state.pageIsShow,
      orderDetail: state => state.orderDetail,
      guideMode: state => state.guideMode,
      messageInit: state => state.messageInit
    }),
    ...mapState('user', {
      curSelectUserInfo: state => state.curSelectUserInfo,
      isLogin: state => state.isLogin
    })
  },
  beforeDestroy() {
    this.clearUnRead()
    this.$common.setKeyVal('chat', 'messageList', [], false)
    this.$common.setKeyVal('chat', 'messageListLoadingStatus', 2, false)
    this.$common.setKeyVal('chat', 'orderDetail', null, false)
    this.$common.setKeyVal('chat', 'chatItem', null, false)
    this.$common.setKeyVal('chat', 'messageInit', false, false)
    this.$common.setKeyVal('chat', 'messageInit', false, false)
    this.$common.setKeyVal('chat', 'guideMode', '', false)
    this.$common.setKeyVal('chat', 'bottomBoxHeight', 0)
  },
  onUnload () {
    this.$common.setKeyVal('chat', 'pageIsShow', false, false)
    // #ifdef MP-WEIXIN
    this.handleClickTrack('EndOperationDetailsPageView')
    // #endif
  },
  async onShow() {
    this.startTimer()
    // 是否登录
    if (!this.isLogin) {
      this.$navto.replaceAll('Login')
      return
    }
    // 获取科室标签
    if (this.$validate.isNull(this.$common.getKeyVal('chat', 'departmentTags'))) {
      this.getOfficeIdList()
    }

    // 获取资讯类型标签
    if (this.$validate.isNull(this.$common.getKeyVal('chat', 'consultTypeTags'))) {
      this.getConsultTypeList()
    }

    this.$common.setKeyVal('chat', 'pageIsShow', true, false)
    const query = this.$Route.query
    if (!this.$validate.isNull(query) && !this.$validate.isNull(query.chatUserId)) {
      this.$common.setKeyVal('chat', 'chatItem', query, false)
    } else {
      const { centerUserId = '' } = this.curSelectUserInfo || {}
      const res = await this.$api.order.orderInitiatorUserCheck({
        userId: centerUserId,
        tenantId: this.$common.getKeyVal('user', 'curSelectStoreId',true) || env.tenantId
      }).catch(() => {

      })
      this.$common.setKeyVal('chat', 'chatItem', res.data)
    }
    uni.setNavigationBarTitle({
      title: this.chatItem.chatUserName
    });
    this.clearUnRead()
    await this.getNodereplyconfigQueryConfigList()
    // this.$ext.common.queryUnread((res) => {
    //   if (res.platformNum || res.platformNum == 0) this.regForm.platformNum = res.platformNum
    //   if (res.campusNum || res.campusNum == 0) this.regForm.campusNum = res.campusNum
    // })
    // 获取资讯节点配置

    this.$nextTick(() => {
      this.$refs.messageList.checkDivShow()
    })
    // this.$ext.common.queryBacklogData()
  },
  onPageScroll(e) {
    if (!this.pageIsShow) return
    // var timer
    // 退出和注销时候需要 将 window.onscroll =  null;
    // 滚动
    clearTimeout(this.timer) // 每次滚动前 清除一次
    this.timer = setTimeout(() => {
      // 停止滚动时上报
      console.log('停止滚动--------------------')
      this.$refs.messageList.checkDivShow()

      if (e.scrollTop < 50) {
        this.$refs.messageList.joinData();
      }

    }, 500)
  },
  watch: {
    // 页面初始化加载完毕手动触发引导语
    messageInit: {
      handler () {
        if (this.messageInit) {
          // 手动触发一次获取接收引导语
          new HandleConsult(this.$ext.webSocket).processMessage()
          this.$uniPlugin.hideLoading()
        } else {
          this.$uniPlugin.loading('加载中...', true)
        }
      },
      immediate: true
    }
  },
  methods: {
    startTimer(){
      this.timer2 = setInterval(() => {
        this.answerDuration++
      }, 1000);
    },
    stopTimer() {
      if (this.timer2) {
        clearInterval(this.timer2); // 清除计时器
        this.timer2 = null;
        this.answerDuration = 0
      }
    },
    // #ifdef MP-WEIXIN
    handleClickTrack(type){
      let pages = getCurrentPages()
      let current = pages[pages.length - 1]; // 获取到当前页面的信息
      let pageInfo = __wxConfig.page && __wxConfig.page[`${current.route}.html`] || ''; // 内部属性
      getApp().globalData.sensors.track(type,
        {
          'page_name' : pageInfo?.window?.navigationBarTitleText || '',
          'first_operation_name' : pageInfo?.window?.navigationBarTitleText || '',
          'second_operation_name' : '',
        }
      )
    },
    // #endif
    // 清空未读数
    clearUnRead () {
      try {
        // 发送清除列表未读消息
        let chatList = this.$common.getKeyVal('chat', 'chatlist')
        const { CHATLIST_UNREADNUM_UPDATE_CMD } = this.$constant.chat
        const data = {
            cmd: CHATLIST_UNREADNUM_UPDATE_CMD,
            data: {
                userId: this.chatItem.userId,
                orderId: this.chatItem.orderId,
                chatUserId: this.chatItem.chatUserId,
                num: chatList.find(item => item.id === this.chatItem.id).extendUnMsgCount
            }
        }
        this.$ext.webSocket.webSocketSend(CHATLIST_UNREADNUM_UPDATE_CMD, data)
        // 更新列表
        chatList.forEach((item,index) => {
          if (item.id === this.chatItem.id) {
            chatList[index].extendUnMsgCount = 0
          }
        })
        this.$common.setKeyVal('chat', 'chatlist', chatList)
        this.$uniPlugin.removeTabBarBadge(1)
      } catch {

      }
    },
    async getNodereplyconfigQueryConfigList () {
      // nodeType	节点阶段类型: 1-问诊前,2-问诊后,3-离线
      this.$api.chat.nodereplyconfigQueryConfigList({}).then(res => {
        this.$common.setKeyVal('chat', 'nodereplyconfig', this.$validate.isNull(res.data) ? [] : res.data.sort((a,b) => a.weight-b.weight))
      })
    },
    // 获取选择科室标签
    async getOfficeIdList () {
      const res = await this.$api.common.findByParentId({ parentId: 120003 })
      this.$common.setKeyVal('chat', 'departmentTags', res.data, true)
    },
    // 获取选择资讯类型标签
    async getConsultTypeList () {
      const res = await this.$api.common.findByParentId({ parentId: 120013 })
      this.$common.setKeyVal('chat', 'consultTypeTags', res.data, true)
    },
    pageTouchstart () {
      this.$refs.messageList.checkDivShow()
    }
  },
  destroyed () {
    clearInterval(this.timer2)
    this.answerDuration = 0
  }

}
</script>
<style lang="scss" scoped>
page {
  background-color: #f3f3f3;
}
</style>
