<template>
  <view class="medication-wrapper">
    <view class="medication-guide">
      <view class="medication-content">
        <view class="video-loading" v-if="!isLoading"></view>
        <view class="top-nav" 
          :style="{border: brandIdTopList.includes(paramsObj.brandId) ? '1px solid #dadada' :'none',
          background:brandIdTopList.includes(paramsObj.brandId)?'rgba(255,255,255,0.6);':'none'}"
        >
          <view class="top-nav-l" @click.stop="handleBack"><image mode="aspectFit" :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-pharmacy-home-left.png'" class="header-search-img"/></view>
          <view class="top-nav-r" v-if="brandIdTopList.includes(paramsObj.brandId)"></view>
          <view class="top-nav-c" v-if="brandIdTopList.includes(paramsObj.brandId)" @click.stop="hanldeHome"><image mode="aspectFit" :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-pharmacy-home-home.png'" class="header-search-img"/></view>
        </view>
        <video 
          v-show="isLoading"
          id="myVideo"
          :style="videoStyle"
          :src="paramsObj.videoPath" 
          play-btn-position="center"
          :show-fullscreen-btn="false"  
          :show-progress="false"
          :show-center-play-btn="false" 
          :show-play-btn="false"
          :autoplay="true"
          :loop="true"
          object-fit="cover"
          :controls="false" 
          @click="togglePlayButton"  
          @loadedmetadata="handleLoadedmetadata"
          @timeupdate="timeupdate"
        >
        </video>

        <view class="custom-play-btn" @click="toggleVideo" v-if="isPlaying">  
          <image class="play-icon" :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/play.png'"></image>  
        </view>  


        <view class="side-nav">
          <!-- 企业logo -->
          <view class="enterprise-logo" @click="handleClickJump">
            <image :src="paramsObj.logo"></image>
          </view>

          <view 
            v-for="item in navListFilter"
            :key="item.id"
            class="nav-item"
            @click="handleNavClick(item.id)"
          >
            <view class="img">
              <image 
                v-if="item.url" 
                :src="item.url" 
                alt="" 
                mode="widthFix"
              >
            </view>
            <span>{{ item.name }}</span>
          </view>
        </view>
        <view class="group-name" v-if="paramsObj.brandName" :style="{'padding':paramsObj.videoDesc.length>20?'50rpx 0 30rpx 34rpx':'90rpx 0 30rpx 34rpx'}">
          <div class="name">{{ paramsObj.videoDesc }}</div>
          <view class="group-eit">@{{ paramsObj.brandName }}</view>
        </view>

        <view class="click-look" @click.stop="handleClickLook">
          <view class="click-look-l">
            <view class="img">
              <image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/home-look.png'"></image>
            </view>
            <span>点击查看“完整版电子说明书”</span>
          </view>
          <view class="click-look-r">
            <div class="img">
              <image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/home-right-arrow.png'"></image>
            </div>
          </view>
        </view>


        <!-- 1.注意：进度条这类拖拽的东西不能放进block\cell这些循环体中的，要不然touchmove方法会捕捉有误 -->          
        <!-- 这里就是进度条了：纯手工进度条，调整位置的话就把他们的 bottom 改成一下就行了 -->
        <view v-if="isDragging == false" style="position: absolute; bottom: 167rpx; left: 0;right: 0; z-index: 99;">
          <view @touchmove.stop.prevent="progressTouchmoveFn" @touchend.stop="progressTouchend" @touchstart.stop="progressTouchstart">
            <!-- 1.这一步必须加，为了适配低端机型 -->
            <!-- <text :style="'width: '+ windowWidth +'px; opacity: 0;'">.</text> -->
            <text :style="'position:absolute;bottom:-13px;opacity: 0;width: '+ windowWidth +'px; '">.</text>
            <!-- 2.这是未加载的时的右边的灰色部分 -->
            <view :style="'width: '+ windowWidth +'px; height: 4px; background-color: #C8C7CC; position: absolute; bottom: '+ ProgressBarBottom +'upx; opacity: '+ ProgressBarOpacity +';'"></view>
            <!-- 3.这里我采用的分离式办法：就是让滑动样式和不滑动的样式分开，这样相互不干扰，可以避免进度条闪动的问题 -->
            <!-- 4.注意：isShowProgressBarTime 加入了返回数据中 -->
            <view v-if="isShowProgressBarTime == false" :style="'width: '+ (currentPosition) +'px; height: 4px; background-color: #FFFFFF; position: absolute; bottom: '+ ProgressBarBottom +'upx; left: 0; opacity: '+ (ProgressBarOpacity - 0.1) +';'"></view>
            <view v-if="isShowProgressBarTime == true" :style="'width: '+ (currentPositions) +'px; height: 8px; background-color: #FFFFFF; position: absolute; bottom: '+ (ProgressBarBottom -30.5) +'rpx; left: 0; opacity: '+ (ProgressBarOpacity + 0.05) +';'"></view>
            <view v-if="isShowProgressBarTime == false" :style="'width: 4px; height: 4px; background-color: #FFFFFF; border-radius: 10px; position: absolute; bottom: '+ ProgressBarBottom +'upx; left: '+ (currentPosition) +'px; opacity: '+ ProgressBarOpacity +';'"></view>
            <view v-if="isShowProgressBarTime == true" :style="'width: '+ dotWidth +'px; height: '+ dotWidth +'px; background-color: #FFFFFF; border-radius: 10px; z-index: 999; position: absolute; bottom: '+ (ProgressBarBottom -33) +'rpx; left: '+ (currentPositions - 5) +'px; opacity: '+ ProgressBarOpacity +';'"></view>
          </view>
        </view>
        <view class="medication-bott-tabs">
          <view 
            v-for="(item,index) in tabListFilter"
            :key="item.id"
            class="tabs-item"
            @click="handleTabClick(item)"
          >
            <span>{{ item.name }}</span>
            <view class="img" v-if="item.url">
              <image :src="item.url"></image>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 手机号弹窗 -->
    <uni-popup ref="phonePopup" class="phonePopup" id="phonePopup" type="bottom">
      <view class="down-popup">
        <view class="down-item" @click="callPhoneNumber">呼叫</view>
        <view class="down-item" @click="copyPhoneNumber">复制号码</view>
        <view class="down-item" @click="handleClose">取消</view>
      </view>
    </uni-popup>
    
    <!-- 二维码弹窗 -->
    <uni-popup ref="codePopup" id="codePopup" type="center">
      <view class="code-popup">
        <view class="title">扫码立即线上沟通</view>
        <view class="img">
          <image :src="paramsObj.qrCode" :show-menu-by-longpress="true"></image>
        </view>
        <view class="name">微信内可长按扫码</view>
      </view>
    </uni-popup>

    <!-- 分享码开始部分 -->
    <uni-popup class="show-code-popup" id="showCodePopup" ref="showCodePopup" type="center">
      <view class="show-code-img">
        <image class="show-code" :src="sharePic" :show-menu-by-longpress="true"></image>
        <view class="img" @click="handleCloseCodePopup"><uni-icons :size="30" color="#fff" type="close" /></view>
      </view>
      <button type="primary" class="circle-attention-btn" @tap="openPicture">保存海报图片</button>
    </uni-popup>

    <!-- 绘制分享码canvas -->
    <canvas canvas-id="answerCanvas" class="answerCanvas" :style="{'position':'absolute','top':'-99999px','width':`${canvasWidth}`+'px','height':`${canvasHeight}`+'px'}"></canvas>

    <!-- 绘制分享码的内容 -->
    <view class="share-content" id="answer-canvas" v-show="isShow">
      <view class="share draw_canvas" data-type="background-image" data-delay="1" :style="{'background-image':'url('+ file_ctx +'static/image/business/pharmacy-cyclopedia/shareBg.png)'}">
        <view class="header draw_canvas">
          <view class="header-l draw_canvas">
            <image 
              class="draw_canvas"
              data-type="radius-image" 
              data-delay="1"
              :data-url="file_ctx + headImgPath"
              :src="file_ctx + headImgPath">
            </image>
          </view>
          <view class="header-r draw_canvas">
            <view class="name draw_canvas" data-delay="1" data-type="text" :data-text="fansRecord.nickName">{{ fansRecord.nickName }}</view>
            <view class="info draw_canvas" data-delay="1" data-type="text" data-text="给你分享了一个视频">给你分享了一个视频</view>
          </view>
        </view>
        <view class="conent draw_canvas" data-type="background-image" data-delay="1" :style="{'background-image':'url('+ file_ctx +'static/image/business/pharmacy-cyclopedia/share-code-bg.png)'}">
          <view class="box draw_canvas">
            <image 
              class="draw_canvas" 
              data-type="image" 
              data-delay="1"
              mode="aspectFit"
              :data-url="file_ctx + publishInfo.posterCover"
              :src="file_ctx + publishInfo.posterCover">
            </image>
          </view>
              <!-- mode="widthFix" -->
          
          <view class="title draw_canvas" data-delay="1" data-type="text" :data-text="publishInfo.name + (brandIdList.includes(paramsObj.brandId) ? '使用指南' : labelValue)">{{ publishInfo.name }}{{(brandIdList.includes(paramsObj.brandId) ? '使用指南' : labelValue)}}</view>
          <view class="code-info draw_canvas">
            <view class="code-l draw_canvas">
              <view class="name draw_canvas" data-delay="1" data-type="text" data-text="长按二维码进入">长按二维码进入</view>
              <view class="tags draw_canvas" data-delay="1" data-type="text" data-text="健康科普/经验分享">健康科普/经验分享</view>
            </view>
            <view class="code-r draw_canvas">
              <view class="img draw_canvas">
                <image 
                  class="draw_canvas" 
                  data-type="image" 
                  data-delay="1"
                  :data-url="queryAndCreateObj.shareQrCode"
                  :src="queryAndCreateObj.shareQrCode">
                </image>
                  <!-- :src="publishInfo.qrcodePath"> -->
              </view>
            </view>
          </view>
        </view>
      </view>      
    </view>

    <!-- 轮播弹窗 -->
    <uni-popup ref="carouselPopup" id="carouselPopup" type="center" v-if="carouselList.length">
      <view class="carousel-popup-img" @click="handleCarouselPopupClose"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-carousel-close.png'"></image></view>
      <view class="carousel-popup">
        <swiper class="swiper" circular :indicator-dots="true" :autoplay="true" :interval="3000" indicator-color="rgba(255,255,255,0.6)" indicator-active-color="#00B484">
          <swiper-item class="swiper-item" v-for="(item,index) in carouselList" :key="index">
            <image :src="item.url"></image>
          </swiper-item>
        </swiper>
      </view>
    </uni-popup>
  </view>
</template> 

<script>
  import { mapState } from 'vuex'
  import { isDomainUrl } from '@/utils/index.js'
  import Wxml2Canvas from 'wxml2canvas';
  import uniPopup from '@/components/uni/uni-popup'
  import UniIcons from '@/components/uni/uni-icons/uni-icons'
  export default {
    components: {
      uniPopup,
      UniIcons,
    },
    data(){
      return{
        file_ctx: this.file_ctx,
        navList:[
          {id:1,name:'药店',url:this.file_ctx + 'static/image/business/pharmacy-cyclopedia/drugstore2.png?timestamp=' + new Date().getTime()},
          {id:2,name:'小黄车',url:this.file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-pharmacy-shopping-trolley.png'},
          {id:3,name:'电话',url:this.file_ctx + 'static/image/business/pharmacy-cyclopedia/phone2.png?timestamp=' + new Date().getTime()},
          {id:4,name:'二维码',url:this.file_ctx + 'static/image/business/pharmacy-cyclopedia/code2.png?timestamp=' + new Date().getTime()},
          {id:5,name:'分享',url:this.file_ctx + 'static/image/business/pharmacy-cyclopedia/transpond2.png?timestamp=' + new Date().getTime()},
        ],
        tabList:[
          {id:1,name:'首页'},
          {id:2,name:'病友分享'},
          {id:3,name:'',url:this.file_ctx + 'static/image/business/pharmacy-cyclopedia/home-more.png'},
          {id:4,name:'药师问答'},
          {id:5,name:'更多'},
        ],
        paramsObj:{},
        canvasWidth:null,
        canvasHeight:null,
        channelCode:'',
        isShow:false,
        sharePic:'',
        videoContext:'',
        isPlaying:false,
        isDragging: false,
        currentStatus:'',
        queryId:null,
        isLoading:false,
        carouselList:[], //轮播数组
        navListFilter:[],//过滤后的侧边导航
        tabListFilter:[],//过滤后的底部导航
        specificationList:[], //说明书列表数据
        jsonData:null,
        bookTips:'不同规格、不同生产日期/批次的药品说明书可能因说明书更新和版本更替等原因，内容有所不同。如本页面显示的电子说明书和您所购买的药物药盒中所附的纸质说明书存在内容有所不同的情况，请以药盒中所附的纸质说明书为准。',
        queryAndCreateObj:null,
        brandIdList:['2109655889818025985','2148011574305714178','2148010541462134786','2148006452183506947'], //针对部分企业做调整
        brandIdTopList:['2022748678633525253','2031348997172924423','2228461205865345029'], //针对部分企业顶部做调整


        windowWidth: uni.getSystemInfoSync().screenWidth, //获取屏幕宽度
        windowHeight: uni.getSystemInfoSync().screenHeight,
        isShowimage: false,//是否显示封面【1.0.4已废弃，但是意思需要记住】
        dotWidth: 0,//播放的小圆点，默认没有💗  
        percent: 0,//百分小数💗
        isShowProgressBarTime: false,//是否拖动进度条，如果拖动（true）则显示进度条时间，否则不显示（false）【1.0.4已废弃，但是意思需要记住】
        ProgressBarOpacity: 0.7,//进度条不拖动时的默认值，就是透明的💗
        videoTime: '',//视频总时长，这个主要用来截取时间数值💗
        videoTimes: '',//视频时长，用这个来获取时间值，例如：00:30这个时间值💗
        currentPosition: 0,//滑块当前位置💗//2.0已弃用，现已用于后端参数
        currentPositions: 0,//滑块当前位置的副本💗//2.0已弃用，现已用于后端参数
        newTime: 0,//跟手滑动后的最新时间💗
        currenttimes: 0,//当前时间💗
        ProgressBarBottom: 20,//进度条离底部的距离💗
        timeNumber: 0,//🌟💗
        changeTime: '',//显示滑动进度条时变化的时间💗
        progressTouchmoveFn: () => {}
      }
    },
    computed: {
      ...mapState('user', {
        curSelectUserInfo: state => state.curSelectUserInfo, // 当前选中的用户信息
        accountId: state => state.accountId,
        publishInfo:state => state.publishInfo,
        fansRecord: state => state.fansRecord,
      }),
      headImgPath(){
        return this.fansRecord.headPath ? this.fansRecord.headPath :  'static/image/business/pharmacy-cyclopedia/icon-default-avatar.png'
      },
      labelValue() {
        let value = '用药指南'
        if(this.paramsObj?.drugType == 2){
          value = '服用指南'
        }
        return value 
      },
      videoStyle() {
        return {
          width: '100%',
          height: '100%',
          // #ifndef MP-WEIXIN
          objectFit: 'cover'
          // #endif
        }
      }
    },
    watch: {
      queryId:{
        handler(val) {
          if(this.queryId){
            this.getEiproductQueryOne({id:this.queryId})
          }
        },
      },
    },
    onLoad(options){
      const query = this.$validate.isNull(this.$Route.query) ? options : this.$Route.query
      let params = ''
      // 创建一个对象来存储参数和值
      let paramObject = {};
      let paramsSpt = null

      if(query.scene){
        // params = query.scene.indexOf('%') != -1 ? query.scene.replace('%', '=') : query.scene
        params = decodeURIComponent(query.scene)
        paramsSpt = params.split('&')
 
        // 遍历数组，分割每个参数成键和值，并存储到对象中
        paramsSpt.forEach(param => {
          let [key, value] = param.split('=')
          paramObject[key] = value
        });
      } else {
        params = query
      }
      uni.showLoading({
        title: '加载中'
      });
      // // this.channelCode = query.gs || query.scene && query.scene.split('=')[1]
      // this.channelCode = params.gs || params.split('=')[1]
      this.channelCode = params.gs || paramObject.gs

      if(this.channelCode){
        this.getChannelQueryOne({code:this.channelCode})
      }

      this.bannerQueryPage()

      // #ifdef MP-WEIXIN
      wx.showShareMenu({
        withShareTicket:true,
        //设置下方的Menus菜单，才能够让发送给朋友与分享到朋友圈两个按钮可以点击
        // menus:["shareAppMessage","shareTimeline"]
        menus:["shareAppMessage"]
      })
      // #endif
    },

    onReady(){
      const queryId = this.$Route.query.id
      if (!this.$validate.isNull(queryId)) {
        this.queryId = queryId
      }
    },

    onShareAppMessage: function (res) {  
      if (res.from === 'share') {  
        // 来自页面内转发按钮  
        console.log(res.target);  
      }
      let { phone = '' } =  this.curSelectUserInfo || {}
      this.queryAndCreate(3)
      return {
        title: `${this.publishInfo.name}${(this.brandIdList.includes(this.paramsObj.brandId) ? '使用指南' : this.labelValue)}`, //分享的名称
        path: `modules/pharmacy/pharmacy-cyclopedia/index?gs=${encodeURIComponent(this.channelCode)}&phone=${phone || this.paramsObj.phone }&shareType=3`,
        mpId:this.$appId, //此处配置微信小程序的AppId
        imageUrl: this.file_ctx + this.paramsObj.shareCover
      }
    },
    created() {
      this.progressTouchmoveFn = this.$common.throttle(this.progressTouchmove, 50)
    },
    mounted () {
      // 获取播放器上下文（后面的 this 需要传入，在微信小程序上无法暂停播放拖拽精度，所以需要传入这个）
      // this.videoContext = uni.createVideoContext('myVideo', this)
    },
    methods:{
      // #ifdef MP-WEIXIN
      handleClickTrack(type,btnType){
        getApp().globalData.sensors.track("PopupClick",
          {
            'page_name' : '用药说明书',
            'popup_id' : type == 1 ? 'phonePopup' : type == 2 ? 'codePopup' : type == 3 ? 'showCodePopup' : 'carouselPopup',
            'popup_name' : type == 1 ? '手机号码弹窗' : type == 2 ? '二维码弹窗' : type == 3 ? '分享海报弹窗' : '轮播图弹窗',
            'click_type' : btnType == 1 ? '进入弹窗' : '取消弹窗',
          }
        ) 
      },
      // #endif
      handleCarouselPopupClose(){
        this.$refs.carouselPopup.close()
        // #ifdef MP-WEIXIN
        this.handleClickTrack(4,2)
        // #endif
      },
      async queryAndCreate(type){
        let { phone = '' } =  this.curSelectUserInfo || {}
        let params = {
          accountId:this.accountId,
          phone:this.paramsObj.phone || phone,
          productId:this.paramsObj.id,
          shareType:type,  //	分享类型：1分享按钮2保存二维码3微信好友分享按钮
        }
        const res = await this.$api.drugBook.queryAndCreate(params)
        this.queryAndCreateObj = res.data
        return Promise.resolve(res)
      },
      hanldeHome(){
        uni.switchTab({
          url: "pages/index/index",
        });
      },
      handleBack(){
        this.$navto.back(1)
      },
      progressTouchstart(event){
        // this.isShowimage = true //刚触摸的时候就要显示预览视频图片了
        this.isShowProgressBarTime = true //显示时间线
        this.ProgressBarOpacity = 1 //让滑块显示起来更明显一点
        this.dotWidth = 10 //让点显示起来更明显一点
      },

      progressTouchend(){//当手松开后，跳到最新时间
        this.videoContext.seek(this.newTime)
        if(this.currentStatus == 'pause'){
          this.isPlaying = false
          this.play()
        }
        this.isShowProgressBarTime = false //触摸结束后，隐藏时间线
        // this.isShowimage = false //触摸结束后，隐藏时间预览
        this.ProgressBarOpacity = 0.5 //隐藏起来进度条，不那么明显了
        this.dotWidth = 0 //隐藏起来进度条，不那么明显了
      },

      timeupdate(event){//计算滑块当前位置，计算当前百分小数
        // console.log(event)
        let currenttime = event.detail.currentTime
        this.timeNumber = Math.round(event.detail.duration)
        this.getTime()
        this.percent = this.$accurateConversion.divide(currenttime, this.timeNumber)
        this.currentPosition = this.$accurateConversion.times(this.windowWidth, this.percent)
        // let theTime = currenttime
        // let middle = 0;// 分
        // if(theTime > 60) {
        //   middle = parseInt(theTime/60);
        //   theTime = parseInt(theTime%60);
        // }
        // this.changeTime = `${Math.round(middle)>9?Math.round(middle):'0'+Math.round(middle)}:${Math.round(theTime)>9?Math.round(theTime):'0'+Math.round(theTime)}`
      },

      getTime(){//得到时间函数
        this.videoTime = this.formatSeconds(this.timeNumber);
        // console.log(that.videoTime)
        let msg = []
        if(this.videoTime !== ''){
          msg = this.videoTime.split(':')
        }
        this.videoTimes = `${msg[0]>9?msg[0]:'0'+msg[0]}:${msg[1]>9?msg[1]:'0'+msg[1]}`;
      },

      formatSeconds(value) {//获取时间函数
        let theTime = parseInt(value);// 秒
        let middle= 0;// 分
        if(theTime > 60) {
          middle= parseInt(theTime/60);
          theTime = parseInt(theTime%60);
        }
        return `${middle>9?middle:middle}:${theTime>9?theTime:theTime}`;
      },

      progressTouchmove(event){//当手移动滑块时，计算位置、百分小数、新的时间
        let msg = []
        if(this.videoTime !== ''){
          msg = this.videoTime.split(':')
        }
        let timeNumber = this.$accurateConversion.plus(this.$accurateConversion.times(msg[0], 60), msg[1])
        this.currentPositions = event.changedTouches[0].clientX
        // this.currentPositions = event.changedTouches[0].clientX - 20
        // console.log(this.currentPositions,'this.currentPositions')
        this.percent = this.$accurateConversion.divide(this.currentPositions.toFixed(2), this.windowWidth)
        this.newTime = this.$accurateConversion.times(this.percent, timeNumber)
        this.currenttimes = parseInt(this.newTime)
        let theTime = +this.newTime
        let middle = 0;// 分
        if(theTime > 60) {
          middle = parseInt(theTime/60);
          theTime = parseInt(theTime%60);
        }
        this.changeTime = `${Math.round(middle)>9?Math.round(middle):'0'+Math.round(middle)}:${Math.round(theTime)>9?Math.round(theTime):'0'+Math.round(theTime)}`
      },

      play() {
        this.currentStatus = 'play'
        this.videoContext.play()
      },

      handleLoadedmetadata(){
        this.isLoading = true
        // #ifdef MP-WEIXIN
        this.$nextTick(() => {
          this.videoContext = uni.createVideoContext('myVideo')
          this.videoContext.seek(0)
        })
        // #endif
        uni.hideLoading()
      },

      toggleVideo() {  
        this.isPlaying = !this.isPlaying
        if (this.isPlaying) {  
          this.currentStatus = 'pause'
          this.videoContext.pause();  
        } else {  
          this.currentStatus = 'play'
          this.videoContext.play();  
        }  
      },  
      
      togglePlayButton() {  
        this.toggleVideo()
      },

      // 跳转到完整用药说明书
      handleClickLook(){
        if(this.specificationList?.length == 1){
          this.$navto.push('ElectronicDetail',{id:this.specificationList[0].id,drugName:this.paramsObj.drugName,commonName:this.paramsObj.commonName,brandName:this.paramsObj.brandName,productId:this.paramsObj.id,labelValue:this.labelValue,gs:this.channelCode})
        } else {
          // this.$navto.push('ElectronicBook',{drugName:this.paramsObj.drugName,commonName:this.paramsObj.commonName,brandName:this.paramsObj.brandName,productId:this.paramsObj.id,indexList:this.specificationList})
          this.$navto.push('ElectronicBook',{drugName:this.paramsObj.drugName,commonName:this.paramsObj.commonName,brandName:this.paramsObj.brandName,productId:this.paramsObj.id,bookTips:this.bookTips,labelValue:this.labelValue,gs:this.channelCode})
        }
      },

      // 企业logo跳转
      handleClickJump(){
        this.$navto.push('Enterprise',{id:this.paramsObj.id,brandId:this.paramsObj.brandId,gs:this.channelCode,labelValue:this.labelValue,gs:this.channelCode})
      },

      // 小黄车跳转
      handleClickYellowCart({path,appId}) {
        this.$uniPlugin.navigateToMiniProgram({
          appId,
          path,
          envVersion: 'release',
          extraData: {}
        }, (res) => {
          console.log(res)
        }, (err) => {
          console.log(err)
        })
      },
      
      handleTabClick(item){
        let res = null
        switch(item.id){
          case 1:
            if(this.brandIdTopList.includes(this.paramsObj.brandId)){
              this.$navto.push('PharmacyEvaluate',{entryType:3,id:this.paramsObj.id,title:this.jsonData?.coupeName || '健康科普',labelValue:this.labelValue,gs:this.channelCode})
            } else {
              uni.switchTab({
                url: "pages/index/index",
              });
            }
          break;
          case 2:
            res = this.isLogin()
            if(this.brandIdList.includes(this.paramsObj.brandId)){
              res && this.$navto.push('PharmacyEvaluate',{entryType:3,id:this.paramsObj.id,title:this.jsonData?.coupeName || '健康科普',labelValue:this.labelValue,gs:this.channelCode})
            } else {
              res && this.$navto.push('PharmacyEvaluate',{entryType:2,id:this.paramsObj.id,title:this.jsonData?.shareName||'病友分享',evaluateImg:this.jsonData?.shareImg || 'static/image/business/hulu-v2/icon-post-banner.png',labelValue:this.labelValue,gs:this.channelCode})
            }
          break;
          case 3:
            res = this.isLogin()
            res && this.$navto.push('PublishPost',{shareImg:this.jsonData?.shareImg || 'static/image/business/hulu-v2/icon-post-banner.png'})
          break;
          case 4:
            res = this.isLogin()
            if(this.brandIdList.includes(this.paramsObj.brandId)){
              if(this.paramsObj.externalLinksSwitch == 1){
                res && this.$navto.push('WebHtmlView', { src: this.paramsObj.externalLinks })
              } else {
                res && this.$navto.push('directseedingVideoListIndex',{name:'名医直播间',productId:this.paramsObj.id,businessType:8,title:this.jsonData?.liveName || '名医直播',searchVal:'搜索相关视频名称'})
              }
            } else {
              res && this.$navto.push('DoctorQuestion',{id:this.paramsObj.id,title:this.jsonData?.questionsName || '药师问答',labelValue:this.labelValue,gs:this.channelCode})
            }
          break;
          case 5:
            res = this.isLogin()
            res && this.$navto.push('LookMore',{id:this.paramsObj.id,gs:this.channelCode})
          break;
        }
      },
      // 侧边导航
      handleNavClick(id){
        let res = null
        switch(id){
          case 1:
            res = this.isLogin()
            let params = {bindingId:this.paramsObj.id,brandId:this.paramsObj.brandId,appletSwitch:this.paramsObj.appletSwitch,path:this.paramsObj.appPath,appId:this.paramsObj.appId,labelValue:this.labelValue,gs:this.channelCode,extProfiles:this.paramsObj?.extProfiles}
            // let params = {bindingId:this.paramsObj.id,brandId:this.paramsObj.brandId,appletSwitch:this.paramsObj.appletSwitch,path:this.paramsObj.appPath,appId:this.paramsObj.appId,labelValue:this.labelValue,gs:this.channelCode,}
            res && this.$navto.push('Drugstore',params)
          break;
          case 2:
            res = this.isLogin()
            res && this.handleClickYellowCart({path:this.paramsObj.appPath,appId:this.paramsObj.appId})
          break;
          case 3:
            res = this.isLogin()
            res && this.$refs.phonePopup.open()
            // #ifdef MP-WEIXIN
            this.handleClickTrack(1,1)
            // #endif
          break;
          case 4:
            res = this.isLogin()
            res && this.$refs.codePopup.open()
            // #ifdef MP-WEIXIN
            this.handleClickTrack(2,1)
            // #endif
          break;
          case 5:
            res = this.isLogin()
            res && this.openShare()
          break;
        }
      },

      isLogin() {
        const { centerUserId = '' } = this.curSelectUserInfo || {}
        if (!centerUserId) {
          this.$navto.push('Login', {
            formPage: 'PharmacyCyclopedia',
            // formPageParams:this.channelCode
            formPageParams: encodeURIComponent(
              JSON.stringify({
                gs:this.channelCode
              })
            )
          });
          return false
        } else {
          return true
        }
      },

      // 打开图片授权
      openPicture(){
        let that = this
        // 首先检查权限
        uni.getSetting({
          success(res) {
            if (!res.authSetting['scope.writePhotosAlbum']) { 
              // 请求授权
              uni.authorize({
                scope: 'scope.writePhotosAlbum',
                success() {
                  that.savePicture()
                },
                fail() {
                  // 用户拒绝授权，引导用户去设置页手动打开权限
                  uni.showModal({
                    title: '授权提示',
                    content: '需要获取添加图片权限，请到小程序设置页面打开授权',
                    success(modalRes) {
                      if (modalRes.confirm) {
                        uni.openSetting();
                      }
                    }
                  });
                }
              });
            } else {
              that.savePicture()
            }
          }
        });
      },

      // 保存图片
      savePicture(){
        // 用户已授权，可以保存图片
        uni.saveImageToPhotosAlbum({
          filePath: this.sharePic,
          success: (res) => {
            this.queryAndCreate(2)
            uni.showToast({
              title: '保存成功',
              icon: 'success'
            })
          },
        })
      },

      // 点击了分享
      async openShare(){
        let res = await this.queryAndCreate(1)
        if(res.data != ""){
          this.isShow = true
          this.$nextTick(()=>{
            try {
              uni.createSelectorQuery().select('#answer-canvas').boundingClientRect().exec((res)=>{
                let { width,height } = res[0]
                this.canvasWidth = width
                this.canvasHeight = height
                this.draw(width,height)
              })
            } catch(error){
              console.log(error)
            }
          })
        }
      },

      // 关闭分享页弹窗
      handleCloseCodePopup(){
        this.$refs.showCodePopup.close()
        // #ifdef MP-WEIXIN
        this.handleClickTrack(3,2)
        // #endif
      },

      draw(width,height){
        let that = this
        uni.showLoading({
          title:"加载中...",
          mask:true
        })
        let wxcanvas = new Wxml2Canvas({
          element: 'answerCanvas', // canvas节点的id,
          obj: that,
          width: width, // 宽 自定义
          height: height, // 高 自定义
          // background: 'transparent', // 默认背景色 设置背景色
          progress(percent) {},
          finish(url) {
            that.sharePic = url
            // console.log("创建的图片", url);
            uni.hideLoading()
            setTimeout(()=>{
              that.isShow = false
              that.$refs.showCodePopup.open()
              // #ifdef MP-WEIXIN
              that.handleClickTrack(3,1)
              // #endif
            },300)
          },
          error(res) {
            console.log(res);
            that.isShow = false
            uni.hideLoading()
            // 画失败的原因
          }
        })
        let data = {
          //直接获取wxml数据
          list: [{
              type: 'wxml',
              class: '.draw_canvas',  // answer_canvas这边为要绘制的wxml元素跟元素类名， answer_draw_canvas要绘制的元素的类名（所有要绘制的元素都要添加该类名）
              limit: '.share-content', // 这边为要绘制的wxml元素跟元素类名,最外面的元素
              x: 0,
              y: 0
            } ]
        }
          //传入数据，画制canvas图片
        wxcanvas.draw(data);
      },

      // 首页弹窗
      bannerQueryPage(){
        let params = {
          useType: 6,
        }
        this.$api.common.bannerQueryList(params).then(res => {
          const now = new Date()
          for (let i = 0; i < res.data.length; i++) {
            let data = res.data[i]
            if(data.openStatus == 1){
              if(now.getTime() >= data.viewStartTime && now.getTime() <= data.viewEndTime){
                this.carouselList.push({
                  ...data,
                  url: this.file_ctx + res.data[i].image,
                })
                if(this.carouselList.length){
                  this.$nextTick(()=>{
                    this.$refs.carouselPopup.open()
                    // #ifdef MP-WEIXIN
                    this.handleClickTrack(4,1)
                    // #endif
                  })
                }
              } 
            }
          }
        })
      },

      // 渠道连code单一查询
      getChannelQueryOne(params){
        this.$api.drugBook.getChannelQueryOneCode(params).then(res => {
          this.paramsObj = {
            ...res.data,
            qrCode:isDomainUrl(res.data.qrCode),
            videoPath:isDomainUrl(res.data.videoPath),
            logo:isDomainUrl(res.data.logo),
          }
          if(this.paramsObj.otherProfile){
            this.jsonData = JSON.parse(this.paramsObj.otherProfile) 
          }
          this.bookTips = this.jsonData?.bookDescribeName || this.bookTips
          this.navListFilter = this.navList.filter(item=>{
            if((this.paramsObj.phoneSwitch == 2 && item.name == '电话')){
              return false
            } else if((this.paramsObj.qrCodeSwitch == 2 && item.name == '二维码')){
              return false
            } else if((this.paramsObj.pharmacySwitch == 2 && item.name == '药店')){
              return false
            } else if((this.paramsObj.appletSwitch == 2 && item.name == '小黄车')){
              return false
            } else if((this.paramsObj.appletSwitch == 1 && item.name == '小黄车')){
              if(this.jsonData?.electronicBookName){
                item.name = this.jsonData.electronicBookName
              }
              return true
            } else {
              return true
            }
          })
          this.tabListFilter = this.tabList.map(item=>{
            if(item.name == '首页' && this.brandIdTopList.includes(this.paramsObj.brandId)){
              item.name = this.jsonData?.coupeName || '健康科普'
            } else if(this.jsonData?.questionsName && item.name == '药师问答'){
              item.name = this.jsonData.questionsName
            } else if(item.name == '病友分享'){
              item.name = this.jsonData?.shareName || '病友分享'
            } else if(item.name == '药师问答' && (this.brandIdList.includes(this.paramsObj.brandId))){
              item.name = '相关视频' || this.jsonData?.questionsName || '药师问答' 
            }
            return {...item}
          })
          
          this.tabListFilter = this.tabListFilter.filter(item=>{
            if((this.brandIdList.includes(this.paramsObj.brandId))){
              if(item.name !== ''){
                return true
              } else {
                return false
              }
            } else {
              return true
            }
          })
          this.$common.setKeyVal('user','publishInfo',this.paramsObj)
          this.getfullSpecificationQueryPage() //调用说明书列表 
        })
      },

      getEiproductQueryOne(params){
        this.$api.drugBook.getEiproductQueryOne(params).then(res => {
          this.paramsObj = {...res.data,qrCode:isDomainUrl(res.data.qrCode),videoPath:isDomainUrl(res.data.videoPath)}
          this.$common.setKeyVal('user','publishInfo',this.paramsObj)
        })        
      },

      // 说明书接口列表
      getfullSpecificationQueryPage(){
        let params = {
          current: 1,
          size: 15,
          condition:{
            productId:this.paramsObj.id
          },
        }
        this.$api.drugBook.getfullSpecificationQueryPage(params).then(res => {
          let data = res.data.records.map(item=>{
            return {
              ...item,
              specificationUpdateTime:item.specificationUpdateTime ? this.$common.formatDate(new Date(item.specificationUpdateTime), 'yyyy-MM-dd') : ''
            }
          })
          this.specificationList = data
        })
      },

      callPhoneNumber(){
        const phoneNumber = this.paramsObj.phone; // 替换为实际的电话号码  
        uni.makePhoneCall({
          phoneNumber: phoneNumber,
          success: () => {
            // console.log('拨打电话成功！');
          },
          fail: () => {
            // console.error('拨打电话失败！');
          }
        });
      },

      copyPhoneNumber(){
        let that = this
        const phoneNumber = this.paramsObj.phone; // 替换为实际的电话号码  
        uni.setClipboardData({  
          data: phoneNumber,  
          success: function () {  
            uni.showToast({  
              title: '复制成功',  
              icon: 'success'  
            });  
            that.$refs.phonePopup.close()
            // #ifdef MP-WEIXIN
            this.handleClickTrack(1,2)
            // #endif
          },  
          fail: function (error) {  
            // console.error('复制失败', error);  
            uni.showToast({  
              title: '复制失败',  
              icon: 'none'  
            });  
          }  
        });  
      },

      handleClose(){
        this.$refs.phonePopup.close()
        // #ifdef MP-WEIXIN
        this.handleClickTrack(1,2)
        // #endif
      },
    },
 }
</script>

<style lang='scss' scoped>
.medication-guide{
  display: flex;
  flex-direction: column;
  .medication-content{
    position: relative;
    display: flex;
    flex: 1;
    flex-direction: column;
    width: 100vw;
    background-color: #000;
    .top-nav{
      position: absolute;
      top: 49px;
      z-index: 999;
      display: flex;
      align-items: center;
      height: 64rpx;
      width: 184rpx;
      box-sizing: border-box;
      padding:0 32rpx 0 20rpx;
      margin-left: 16rpx;
      border-radius: 200rpx;
      .top-nav-l,.top-nav-c{
        width: 46rpx;
        height: 46rpx;
        image{
          width: 100%;
          height: 100%;
        }
      }
      .top-nav-r{
        width: 2rpx;
        height: 40rpx;
        margin:0 24rpx 0 14rpx;
        background: #D9DBDD;
      }
    }
    .video-loading{
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      width: 100%;
      height: calc(100vh - 160rpx);
      border-radius: 0rpx 6.36rpx 31.81rpx 31.81rpx;
      overflow: hidden;
    }
    video{
      width: 100vw;
      height: calc(100vh - 160rpx);
    }
    .custom-play-btn{
      position: absolute;  
      top: 40%;  
      left: 50%;  
      transform: translate(-50%, -40%);  
      width: 96rpx;
      height: 96rpx;
      opacity: 0.7;
      image{
        width: 100%;
        height: 100%;
      }
    }

    .side-nav{
      position: absolute;
      bottom: 272rpx;
      right: 24rpx;
      z-index:3;
      display: flex;
      flex-direction: column;
      .enterprise-logo{
        border-radius: 50%;
        overflow: hidden;
        display: flex;
        width: 80rpx;
        height: 80rpx;
        box-sizing: border-box;
        box-shadow: 0rpx 0rpx 8rpx 0rpx rgba(0, 0, 0, 0.18);
        margin-bottom: 45rpx;
        image{
          width: 100%;
          height: 100%;
        }
      }
      .nav-item{
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-bottom: 32upx;
        .img{
          width: 72rpx;
          height: 72rpx;
          image{
            width: 100%;
            height: 100%;
          }
        }
        span{
          display: inline-block;
          height: 34rpx;
          line-height: 34rpx;
          // font-weight: 600;
          font-size: 24rpx;
          color: #FFFFFF;
          text-shadow: 0px 2px 4px rgba(0,0,0,0.6);
        }
        &:last-child{
          margin-bottom: 0;
        }
      }
    }
    .group-name{
      position: absolute;
      font-family: Source Han Sans CN;
      height: 208rpx;
      // bottom: 233rpx;
      bottom: 230rpx;
      // height: 178rpx;
      // bottom: 160rpx;
      z-index:2;
      box-sizing: border-box;
      width: 100%;
      // padding: 50rpx 0 30rpx 34rpx;
      padding: 90rpx 0 30rpx 34rpx;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.32) 100%);
      .group-eit{
        font-weight: 400;
        font-size: 24rpx;
        color: rgba(255,255,255,0.84);
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        width: 550rpx;
      }
      .name{
        font-weight: 700;
        font-size: 32rpx;
        color: #FFFFFF;
        margin-top: 10rpx;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        width: 590rpx;
      }
    }
    .click-look{
      position: absolute;
      // bottom: 160rpx;
      bottom: 159rpx;
      left: 0;
      width: calc(100% - 40rpx);
      height: 72rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding:0 20rpx;
      background: rgba(0,0,0,0.6);
      .click-look-l,.click-look-r{
        display: flex;
        align-items: center;
        .img{
          display: flex;
          width: 28rpx;
          height: 28rpx;
          image{
            width: 100%;
            height: 100%;
          }
        }
        span{
          display: inline-block;
          height: 34rpx;
          line-height: 34rpx;
          font-size: 24rpx;
          color: #FFFFFF;
          margin-left: 8rpx;
        }
      }
      .click-look-l{
        margin-left: 12rpx;
      }
      .click-look-r{
        margin-right: 4rpx;
        .img{
          width: 24rpx;
          height: 24rpx;
        }
      }
    }

    .medication-bott-tabs{
      display: flex;
      height: 110rpx;
      width: 100%;
      justify-content:space-around;
      // padding-bottom: 50rpx;
      padding-bottom: 42rpx;
      padding-top: 10rpx;
      .tabs-item{
        display: flex;
        align-items: center;
        justify-content: center;
        span{
          font-weight: 500;
          font-size: 30rpx;
          color:#BBBBBB;
        }
        .active{
          color:#fff;
        }
        .img{
          width: 68rpx;
          height: 56rpx;
          image{
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
}
.phonePopup{
  /deep/.uni-popup{
    .uni-popup__mask{
      background: rgba(0, 0, 0, 0.4) !important;
    }
  }
  .down-popup{
    background-color: #fff;
    border-radius: 13upx;
    padding-bottom: 30upx;
    .down-item{
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      padding: 34upx 0;
      font-weight: 500;
      font-size: 29rpx;
      color: #000000;
      border-bottom: 1upx solid #e5e5e5;
    }
    :nth-child(3){
      border-bottom: 0;
    }
  }
}
.code-popup{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding:45upx 104upx 43upx;
  background-color: #fff;
  border-radius: 13rpx;
  .title,.name{
    font-weight: 700;
    font-size: 29rpx;
    color: #000000;
  }
  .img{
    width: 228.37rpx;
    height: 228.37rpx;
    margin:72upx 0 65upx;
    image{
      width: 100%;
      height: 100%;
    }
  }
  .name{
    font-size: 25upx;
  }
}
.share-content{
  height: 832rpx;
  width: 606rpx;
  .share{
    height: 832rpx;
    width: 606rpx;
    padding:36upx 40upx 48upx;
    // box-sizing: border-box;
    z-index: 99;
    // border-radius: 10rpx;
    .header{
      display: flex;
      align-items: center;
      .header-l{
        width: 88rpx;
        height: 88rpx;
        border-radius: 50%;
        margin-right: 12upx;
        overflow: hidden;
        image{
          width: 100%;
          height: 100%;
        }
      }
      .header-r{
        margin-top: -8rpx;
        .name{
          font-weight: 700;
          font-size: 32rpx;
          color: #1D2029;
          text-align: left;
        }
        .info{
          font-weight: 400;
          font-size: 24rpx;
          color: #4E5569;
          width: 260rpx;
        }
      }
    }
    .conent{
      position: relative;
      height: 632rpx;
      width: 526rpx;
      // background-color: #fff;
      // padding:26upx 28upx 0;
      padding:0 28upx;
      box-sizing: border-box;
      margin-top: 28upx;
      overflow: hidden;
      .box{
        // height: 400rpx;
        position: absolute;
        top: 20rpx;
        left:50%;
        transform: translateX(-50%);
        width: 382rpx;
        height: 268rpx;
        image{
          width: 100%;
          height: 100%;
        }
      }
      .title{
        position: absolute;
        width: calc(100% - 56rpx);
        top: 300rpx;
        z-index: 99;
        text-align: center; 
        font-weight: bold;
        font-size: 28rpx;
        color: #1D2029;
      }
      .code-info{
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 40rpx 20rpx 50rpx 0;
        .code-l{
          position: absolute;
          top: 460rpx;
          left: 48rpx;
          z-index: 99;
          display: flex;
          flex-direction: column;
          .name{
            height: 40rpx;
            width: 260rpx;
            // line-height: 40rpx;
            font-size: 28rpx;
            color: #1D2029;
            margin-bottom: 8upx;
          }
          .tags{
            height: 56rpx;
            // width: 180rpx;
            line-height: 28rpx;
            font-weight: 400;
            font-size: 20rpx;
            color: #868C9C;
          }
        }
        .code-r{
          display: flex;
          .img{
            position: absolute;
            width: 176rpx;
            height: 176rpx;
            top: 420rpx;
            right: 48rpx;
            image{
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }
  }
}
.show-code-img{
  position: relative;
  height: 832rpx !important;
  width: 606rpx !important;
  // width: 100%;
  // height: 100%;
  border-radius: 25rpx;
  overflow: hidden;
  .show-code{
    width: 100%;
    height: 100%;
  }
  .img{
    position: absolute;
    right: 25rpx;
    top: 25rpx;
  }
}
.circle-attention-btn{
  height: 96rpx;
  line-height: 96rpx;
  margin-top: 40upx;
  font-weight: 600;
  border-radius: 48rpx;
  background: #fff;
  font-size: 32rpx;
  color: #00B484;
}
/deep/.uni-popup__wrapper.uni-custom.center .uni-popup__wrapper-box{
  margin-top: -90rpx;
}
.carousel-popup-img{
  margin-left: auto;
  margin-bottom: 24rpx;
  width: 48rpx;
  height: 48rpx;
  image{
    width: 100%;
    height: 100%;
  }
}
.carousel-popup{
  width: 580rpx;
  height: 780rpx;
  border-radius: 24rpx;
  overflow: hidden;
  .swiper{
    height: 100%;
    .swiper-item{
      width: 100%;
      height: 100%;
      image{
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>