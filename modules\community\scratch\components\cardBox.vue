<template>
  <view 
    class="cardBox" 
    :style="{
    marginLeft:options.left || '32rpx',
    marginTop:options.top,
    backgroundColor:options.outlineColor,
    }">
    <view class="cardBoxContent" :style="{borderColor:options.interiorColor}">
      <!-- 标题 S -->
      <view class="cardTapBg" 
        :style="{
          backgroundImage:'url('+ cardTapBg + ')'
        }">
        {{options.title}}
      </view>
      <!-- 标题 E -->
      <slot></slot>
    </view>
  </view>
</template>

<script>
  export default {
    name: '',
    props:{
      options:{
        type:Object,
        default:()=>{}
      }
    },
    data() {
      return {
        file_ctx: this.file_ctx,
        cardTapBg: this.file_ctx + 'static/image/business/hulu-v2/cardTapBg.png',
      }
    },
    methods: {},
    created() {}
  }
</script>

<style scoped lang="scss">
  .cardBox{
    box-sizing: border-box;
    padding: 20rpx;
    width: 686rpx;
    border-radius: 24rpx;
    .cardBoxContent{
      border-radius: 16rpx;
      border: 6rpx solid #FFAE3D;
      background-color: white;
      margin: 0 auto;
      position: relative;
      .cardTapBg{
        width: 206rpx;
        height: 52rpx;
        font-size: 32rpx;
        color: #FFFFFF;
        text-align: center;
        position: absolute;
        left: 50%;
        top: -40rpx;
        transform: translateX(-50%);
        background-size: 100%;
      }
    }
  }
</style>