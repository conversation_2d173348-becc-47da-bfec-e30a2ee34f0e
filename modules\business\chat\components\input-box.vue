<template>
<!-- 底部聊天输入框 -->
<view
  class="input-box"
  id="input-box"
  :class="{ 'input-box-mpInputMargin': mpInputMargin }"
>
  <view class="func">
    <!--  -->
    <text
      class="func-item"
      type="primary"
      size="mini"
      @click="endOrder"
    >结束咨询</text>
  </view>
  <view class="input-box-flex">
    <!-- 功能性按钮 -->
    <image class=" icon_btn_add" @tap="switchFun"></image>
    <view class="input-box-flex-grow">
      <!-- #ifdef MP-ALIPAY -->
      <textarea
        row="1"
        type="text"
        class="content"
        id="input"
        v-model="formData.content"
        :hold-keyboard="true"
        :confirm-type="'send'"
        :confirm-hold="true"
        placeholder-style="color:#DDDDDD;"
        :cursor-spacing="10"
        @confirm="sendMsg(null)"
        @linechange="changeLinechange"
        :show-count="false"
      />
      <!-- #endif -->
      <!-- #ifndef MP-ALIPAY -->
      <textarea
        row="1"
        type="text"
        class="content"
        id="input"
        v-model="formData.content"
        :hold-keyboard="true"
        :confirm-type="'send'"
        :confirm-hold="true"
        placeholder-style="color:#DDDDDD;"
        :cursor-spacing="10"
        @confirm="sendMsg(null)"
        @linechange="changeLinechange"
        auto-height
      />
      <!-- #endif -->
    </view>
    <!-- #ifndef MP-ALIPAY -->
    <button @tap="sendMsg(null)" class="btn" type="primary" size="mini">发送</button>
    <!-- #endif -->
    <!-- #ifdef H5 -->
    <!-- #endif -->
  </view>
  <view class="fun-box" :class="{'show-fun-box':showFunBtn}">
    <title-img style="display: inline-block;" :config="config.img" @uploadFinish="sendImg"></title-img>
    <!--<u-grid :col="4"  hover-class="contentType2-hover-class" :border="false" @click="clickGrid">-->
    <!--  <u-grid-item v-for="(item, index) in funList" :index="index" :key="index" bg-color="#eaeaea">-->
    <!--    <u-icon :name="item.icon" :size="52"></u-icon>-->
    <!--    <view class="grid-text">{{ item.title }}</view>-->
    <!--  </u-grid-item>-->
    <!--</u-grid>-->
  </view>

  <!-- 结束咨询弹框 -->
  <showmodel type='center' dialogStyle="width:632rpx;height:289rpx;border-radius:10rpx" :show='centerVisible' @cancel='cancelCenterVisible' root-class='centerRoot'>

  	<view class="dialogContent">
  		<view class="dialog-title">
  			{{centerMsg}}
  		</view>
  		<view class="dialog-btns">
  			<view class="dialog-btn" @click='submitConsult'>
  				{{centertype == 1 ? '确定' : '结束咨询'}}
  			</view>
  			<view class="dialog-space">

  			</view>
  			<view class="dialog-btn active" @click='cancelCenterVisible'>
  				再想想
  			</view>
  		</view>
  	</view>

  </showmodel>

</view>
</template>

<script>
import { mapState } from 'vuex'
import TitleImg from "@/components/business/module/title-img/index.vue"

import showmodel from '@/components/basics/showmodel/showmodel.vue'


export default {
  components: {
    TitleImg,
    showmodel
  },
  props: {
    answerDuration:{
      type:Number
    }
  },
  data() {
    return {
      centerVisible:false,
      centerMsg:"是否确认继续咨询？",
      centertype:1,

      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      regForm: {
        platformNum: 0,
        campusNum: 0
      },
      // chat
      formData: {
        content: '',
        limit: 15,
        index: 1
      },
      // messageList: [],
      loading: true, //标识是否正在获取数据
      imgHeight: '1000px',
      mpInputMargin: false, //适配微信小程序 底部输入框高度被顶起的问题
      chatType: "voice",  // 图标类型 'voice'语音 'keyboard'键盘
      PointY: 0, //坐标位置
      showFunBtn: false, //是否展示功能型按钮
      funList: [
        {icon: this.file_ctx + "static/image/system/icon-image.png", title: "照片", uploadType: ["album"]},
        {icon: this.file_ctx + "static/image/system/icon-paishe.png", title: "拍摄", uploadType: ["camera"]},
      ],
      // chatItem: null,
      defaultAvatar: this.file_ctx + 'static/image/system/avatar/icon-default-avatar.png',
      billingKey: [],
      config: {
        img: {
          background: 'rgba(0,0,0,0)',
          formData: {
              groupId: "26000",
              relId: ''
          },
          showImg: false
        }
      },
      content:'',
    }
  },
  onReady() {
    uni.onKeyboardHeightChange(res => {
      if (res.height == 0) {
        // #ifdef MP-WEIXIN
        this.mpInputMargin = false;
        // #endif
      } else {
        this.showFunBtn = false;
      }
    })
  },
  computed: {
    ...mapState('user', {
      codeUserInfo: state => state.codeUserInfo, // 当前登录用户信息
      curSelectUserInfo: state => state.curSelectUserInfo, // 当前选中的用户信息
      curSelectUserInfoId: state => state.curSelectUserInfoId, // 当前选中的档案的ID
    }),
    ...mapState('chat', {
      ws: state => state.ws,
      webSocketIsOpen: state => state.webSocketIsOpen,
      webSocketWarningText: state => state.webSocketWarningText,
      chatItem: state => state.chatItem,
      messageList: state => state.messageList,
      selectRecordObj: state => state.selectRecordObj,
      chatContent: state => state.chatContent,
    }),
  },
  mounted () {
    this.getInputBoxHeight()
  },
  watch: {
    showFunBtn () {
      this.$common.setKeyVal('chat', 'showFunBtn', this.showFunBtn, false)
      this.$nextTick(() => {
        setTimeout(() => {
          this.getInputBoxHeight().then(() => {
            // 更改底部高度之后再滚动
            this.$nextTick(() => {
              uni.pageScrollTo({
                scrollTop: 99999,
                duration: 0, //小程序如果有滚动效果 input的焦点也会随着页面滚动...
              })
            })
          })
        }, 100)
      })

      // setTimeout(() => {
      //   uni.pageScrollTo({
      //     scrollTop: 99999,
      //     duration: 0, //小程序如果有滚动效果 input的焦点也会随着页面滚动...
      //   });
      // }, 500)
    }
  },
  onUnload(){
    // #ifdef MP-WEIXIN
    this.handleClickTrack()
    // #endif
  },
  methods: {
    submitConsult(){
    	var that = this;
    	if(this.centertype == 1){
    			const tenantId = that.$common.getKeyVal('user', 'curSelectStoreId', true) || env.tenantId
    			that.$uniPlugin.loading('加载中', true)
    			that.$api.order.orderEndInitiatorUserCheck({ userId: this.codeUserInfo.id, tenantId, chatUserId: that.chatItem.chatUserId }).then(res => {
    			  that.$uniPlugin.hideLoading()
    			  that.$navto.replace('Chat', res.data)
				  this.centerVisible = false;

    			}).catch(() => {
    			  that.$uniPlugin.hideLoading()
				  this.centerVisible = false;
    			})
    	}else if(this.centertype == 2){
                const data =  {
                    cmd: this.$constant.chat.ORDER_END_CMD,
                    data: {
                        orderId: this.chatItem.orderId,
                        userId: this.chatItem.userId,
                        endType: 1,
                    }
                }
                this.$ext.webSocket.webSocketSend(this.$constant.chat.ORDER_END_CMD, data)

				this.centerVisible = false;
    	}
      // #ifdef MP-WEIXIN
      this.handleClickTrack()
      this.$emit('handleClearTimer')
      // #endif
    },
    handleClickTrack(){
      // #ifdef MP-WEIXIN
      getApp().globalData.sensors.track("Consult",
        {
          'consult_source' : '极速咨询',
          'consult_content' : this.chatContent,
          'consultant' : this.selectRecordObj?.name,
          'consultant_gender' : this.selectRecordObj?.gender == 1 ? '男' : '女',
          'consultant_age' : this.selectRecordObj?.age,
          'duration' : this.answerDuration,
        }
      ) 
      // #endif
    },
    cancelCenterVisible(){
      this.centerVisible = false
    },
    // 文本框行数变化，重新获取文本框高度
    changeLinechange () {
      this.$nextTick(async () => {
        await this.getInputBoxHeight()
      })
    },
    // 结束咨询订单
    endOrder () {
      this.centerMsg = '确认结束咨询？';

      // console.log(this.centerMsg)
      this.centerVisible = true;
      this.centertype = 2;

      // this.$uniPlugin.modal('','确认结束咨询？', {
      //   showCancel: true, // 是否显示取消按钮，默认为 true
      //   cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
      //   cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
      //   confirmText: '确定', //  确定按钮的文字，默认为"确定"，最多 4 个字符
      //   confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
      //   fn: async (n) => {
      //     if(n) {
      //       const data =  {
      //           cmd: this.$constant.chat.ORDER_END_CMD,
      //           data: {
      //               orderId: this.chatItem.orderId,
      //               userId: this.chatItem.userId,
      //               endType: 1,
      //           }
      //       }
      //       this.$ext.webSocket.webSocketSend(this.$constant.chat.ORDER_END_CMD, data)
      //     }
      //   }
      // })
    },
    sendImg (list) {
      for (let i = 0; i< list.length; i++) {
        const item = list[i]
        const that = this
        let time = new Date().getTime()
        const dto = {
            cmd: this.$constant.chat.SINGLE_CHAT_CMD,
            data: {
                msgType: 2,
                msgContent: '',
                content: item.dir,
                orderId: this.chatItem.orderId,
                seatUserId: "",
                touchType: this.$constant.chat.touchType.defaultChat,
                createTime: time
            }
        }
        that.$ext.webSocket.webSocketSend(this.$constant.chat.SINGLE_CHAT_CMD, dto)
        const listItem = {
            hasBeenSentId: time + '-' + this.chatItem.userId, //已发送过去消息的id
            content: item.dir,
            fromUserHeadImg: '', //用户头像
            fromUserId: that.chatItem.userId,
            isItMe: true,
            createTime: time,
            contentType: 2, // 1文字文本 2语音
            msgType: 2,
            msgCloudStatus: 2, //消息发送结果状态，1是正常，2是发送中，待回调，3是等待回调失败
        }
        // this.messageList.push(listItem);
        this.$common.setKeyVal('chat', 'messageList', [...this.messageList, listItem])
        this.$nextTick(() => {
            uni.pageScrollTo({
              scrollTop: 99999,
              duration: 0, //小程序如果有滚动效果 input的焦点也会随着页面滚动...
            })
        })
      }
    },
    //切换功能性按钮
    switchFun() {
      this.chatType = 'keyboard'
      this.showFunBtn = !this.showFunBtn;
      uni.hideKeyboard()
    },
    // 更新底部高度
    async getInputBoxHeight () {
      return new Promise((resolve, reject) => {
        // #ifdef H5
        const inputEl = document.getElementById('input-box')
        const height = inputEl.getBoundingClientRect() ? inputEl.getBoundingClientRect().height : 0
        this.$common.setKeyVal('chat', 'bottomBoxHeight', height, false)
        resolve(height)
        // #endif

        // #ifndef H5
        const query = uni.createSelectorQuery().in(this)
        const inputEl = query.select('#input-box')
        if (inputEl.boundingClientRect) {
          inputEl.boundingClientRect(data => {
            this.$common.setKeyVal('chat', 'bottomBoxHeight', data.height, false)
            resolve(data.height)
          }).exec()
        }
        // #endif
      })
    },
    //发送消息
    sendMsg(data) {
      const that = this
      if (!this.webSocketIsOpen) {
        this.$uniPlugin.toast('网络连接断开，稍后再试！')
      }
      that.content = that.formData.content
      console.log(that.content,'00000000')
      let time = new Date().getTime()
      const dto = {
        cmd: this.$constant.chat.SINGLE_CHAT_CMD,
        data: {
          msgType: 1,
          msgContent: '',
          content: that.formData.content,
          orderId: this.chatItem.orderId,
          seatUserId: "",
          touchType: this.$constant.chat.touchType.defaultChat,
          createTime: time
        }
      }
      // this.dtoMsgTemplate(dto)
      if (data) {
      } else if (!this.$validate.trim(this.formData.content)) {
        this.$uniPlugin.toast('发送内容不能为空！')
        return;
      }
      that.$ext.webSocket.webSocketSend(this.$constant.chat.SINGLE_CHAT_CMD, dto)
      const listItem = {
        hasBeenSentId: time + '-' + this.chatItem.userId, //已发送过去消息的id
        content: that.formData.content,
        fromUserHeadImg: that.defaultAvatar, //用户头像
        fromUserId: that.chatItem.userId,
        isItMe: true,
        createTime: time,
        contentType: 1, // 1文字文本 2语音
        msgType: 1,
        msgCloudStatus: 2, //消息发送结果状态，1是正常，2是发送中，待回调，3是等待回调失败
      }

      // this.messageList.push(listItem);
      this.$common.setKeyVal('chat', 'messageList', [...this.messageList, listItem])

      this.$nextTick(() => {
        this.formData.content = '';
        // #ifdef MP-WEIXIN
        if (listItem.msgType == 1) {
          uni.pageScrollTo({
            scrollTop: 99999,
            duration: 0, //小程序如果有滚动效果 input的焦点也会随着页面滚动...
          });
        } else {
          setTimeout(() => {
            uni.pageScrollTo({
              scrollTop: 99999,
              duration: 0, //小程序如果有滚动效果 input的焦点也会随着页面滚动...
            });
          }, 150)
        }
        // #endif

        // #ifndef MP-WEIXIN
        uni.pageScrollTo({
          scrollTop: 99999,
          duration: 100
        });
        // #endif

        if (this.showFunBtn) {
          this.showFunBtn = false;
        }

        // #ifdef MP-WEIXIN
        if (params.contentType == 1) {
          this.mpInputMargin = true;
        }
        // #endif
        //h5浏览器并没有很好的办法控制键盘一直处于唤起状态 而且会有样式性的问题
        // #ifdef H5
        uni.hideKeyboard();
        // #endif
      });
    },
    //点击宫格时触发
    clickGrid(index) {
      if (index == 0) {
        this.chooseImage(['album'])
      } else if (index == 1) {
        this.chooseImage(['camera'])
      }
    },
    //发送图片
    chooseImage(sourceType) {
      uni.chooseImage({
        sourceType,
        sizeType: ['compressed'],
        success: res => {
          this.showFunBtn = false;
          for (let i = 0; i < res.tempFilePaths.length; i++) {
            const params = {
              contentType: 3,
              msgType: 2,
              content: res.tempFilePaths[i],
            };
            this.sendMsg(params)
          }
        }
      })
    }
  },
  onPageScroll(e) {
    if (e.scrollTop < 50) {
      this.joinData();
    }
  }
}
</script>
<style lang="scss" scoped>
// @import '../index.scss';
.dialog-title{
	    font-size: 36upx;
	    font-weight: 550;
	    line-height: 2;
	    display: flex;
	    align-items: center;
	    justify-content: center;
	    padding-top: 33upx;
	    padding-bottom: 58upx;
}
.dialog-btns{
	display:flex;
	padding:0 20upx;
	.dialog-btn{
		flex:1;
		height: 80upx;
		background-color:#eeeeee;
		font-size:32upx;
		display:flex;
		align-items:center;
		justify-content:center;
		border-radius:50upx
	}
	.dialog-btn.active{
		background-color:#00d29d;
		color:#fff
	}
	.dialog-space{
		width: 20upx;
		height: 1upx
	}
}

.input-box {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    box-sizing: content-box;
    z-index: 999;
    background-color: #fff;

    /* #ifdef APP-PLUS */
    margin-bottom: 0rpx;
    margin-bottom: constant(safe-area-inset-bottom);
    margin-bottom: env(safe-area-inset-bottom);
    /* #endif */
    /* #ifdef MP-WEIXIN || MP-ALIPAY */
    padding-bottom: 0rpx;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    /* #endif */

    &-flex {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex-wrap: nowrap;
      flex-direction: row;
      // padding: 20rpx;
      padding: 20upx 20upx 70upx;
      box-sizing: border-box;
      image{
        width: 56rpx;
        height: 56rpx;
      }
      .icon_img {
        margin-right: 20rpx;
      }
      .icon_btn_add{
        margin-right: 11px;
        @include iconImg(52, 52, '/system/icon-add.png');
        //margin-left: 20rpx;
      }
      &-grow {
        min-height: 70rpx;
        flex-grow: 1;
        padding-right: 40rpx;

        .content {
          // box-sizing: border-box;
          background-color: #f6f5f8;
          //height: 70rpx;
          // min-height: 70rpx;
          // max-height: 210rpx;
          line-height: 36upx;
          width: 100%!important;

          padding: 20rpx;
          border-radius: 12rpx;
          font-size: 28rpx;
          caret-color: #4cd964;
          // #ifdef MP-ALIPAY
          box-sizing: border-box;
          // #endif
        }

        .voice_title {
          text-align: center;
          background-color: #ffffff;
          height: 80rpx;
          line-height: 80rpx;
          border-radius: 12rpx;
        }
      }

      .btn {
        margin-left: 20rpx;
        // background-color: #4cd964;
        background-color: #00d29d;
        border: none;
        height: 74upx;
        line-height: 74upx;
      }
    }

    .fun-box{
      opacity: 0;
      transition: all 0.1s ease-in-out;
      height: 0;
      .grid-text{
        padding-top: 10rpx;
        color: #999;
      }

    }
    .show-fun-box{
      opacity: 1;
      height: 300rpx;
    }
    .func {
      padding: 24upx 20px 12upx;
      &-item {
        border-radius: 24upx;
        border: 1px solid #f0f0f0;
        color: #666;
        padding: 8upx 12px;
      }
    }
  }
</style>
