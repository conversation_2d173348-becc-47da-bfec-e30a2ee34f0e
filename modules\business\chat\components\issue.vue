<template>
    <view class="fixed-box" id="form">
        <view class="form">
            <div class="content-top">
                <text class="content-top-text" v-if="nodeConfig.nextEnabled === 2" @tap="skip">跳过</text>
            </div>
            <title-textarea v-model="form.issue" :placeholder="'详细的描述有助于全面了解您的疾病'" :config="config.issue" />
            <title-img
                :config="config.pic"
                @returnFn="(obj) => {imgReturnFn(obj,'pic')}"
                :cData="cDataHeadPath"
            ></title-img>

            <button class="btn" type="primary" size="mini" @tap="confirm">确认</button>
        </view>
    </view>
</template>

<script>
import { mapState } from 'vuex'

import TitleTextarea from "@/components/business/module/v1/title-textarea/index"
import TitleImg from "@/components/business/module/title-img/index.vue"
import HandleConsult from '@/service/ext/modules/websocket/receive/HandleConsult'

export default {
    components: {
        TitleTextarea,
        TitleImg
    },
    data () {
        return {
            // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
            $constant: this.$constant,
            $common: this.$common,
            $accurateConversion: this.$accurateConversion,
            file_ctx: this.file_ctx,
            $static_ctx: this.$static_ctx,
            $timePlugin: this.$timePlugin,
            form: {
                issue: '',
                pic: ''
            },
            config: {
                issue: {
                    label: '咨询问题',
                    required: true,
					iconurl:this.$static_ctx + "image/business/im/icon-im-zx.png"
                    // iconurl:"http://localhost:3000/images/zx.png"
                },
                pic: {
                    padding: '0',
                    count: 1,
                    multiSelectCount: 1,
                    theCluesText: '最多只能上传1张症状图片',
                    formData: {
                        groupId: "26000",
                        relId: ''
                    },
                },
            },
            cDataHeadPath: [],
            defaultAvatar: this.file_ctx + 'static/image/system/avatar/icon-default-avatar.png',
        }
    },
    mounted () {
        // 获取高度
        // #ifdef H5
        let getDiv = document.getElementById('form')
        // #endif

        // #ifndef H5
        const query = uni.createSelectorQuery().in(this)
        let getDiv = query.select('#form')
        // #endif
        this.getEl(getDiv).then(data => {
            this.$common.setKeyVal('chat', 'bottomBoxHeight', data.height + 20)
        })
    },
    computed: {
        ...mapState('chat', {
            chatItem: state => state.chatItem,
            nodereplyconfig: state => state.nodereplyconfig,
            orderDetail: state => state.orderDetail
        }),
        // 资讯问题节点配置
        nodeConfig() {
            return this.nodereplyconfig.find(item => item.pushType === 4)
        }
    },
    methods: {
        // 跳过
        skip () {
            // 之前已经填写过问题，直接跳过 重新触发资讯节点流程
            if (this.orderDetail.gfIssue) {
                new HandleConsult(this.$ext.webSocket).processMessage()
            } else {
                // 更新订单详情
                const data =  {
                    cmd: this.$constant.chat.CONSULT_CMD,
                    data: {
                        orderId: this.chatItem.orderId,
                        userId: this.chatItem.userId,
                        gfIssue: "",
                        gfAttachs: "",
                        nodeConfigId: this.nodeConfig.id,
                        nodeConfigReplyContent: ""
                    }
                }
                this.$ext.webSocket.webSocketSend(this.$constant.chat.CONSULT_CMD, data)
            }
        },
        confirm () {
            const that = this
            // 表单校验
            for(const key in this.form) {
                if(!this.$validate.isNull(this.config[key])) {
                    if(this.config[key].required && !this.form[key]) {
                        this.$uniPlugin.toast(`${this.config[key].label}不得为空！`)
                        return
                    }
                }
            }

            const nodeConfigReplyContent = {
                gfIssue: this.form.issue,
                gfAttachs: this.form.pic,
            }

            // 更新订单详情
            const data =  {
                cmd: this.$constant.chat.CONSULT_CMD,
                data: {
                    orderId: this.chatItem.orderId,
                    userId: this.chatItem.userId,
                    gfIssue: this.form.issue,
                    gfAttachs: this.form.pic,
                    nodeConfigId: this.nodeConfig.id,
                    nodeConfigReplyContent: JSON.stringify(nodeConfigReplyContent)
                }
            }
            this.$ext.webSocket.webSocketSend(this.$constant.chat.CONSULT_CMD, data)

            // 发送咨询问题引导语消息
            const time = new Date().getTime()
            const dto = {
                cmd: this.$constant.chat.SINGLE_CHAT_CMD,
                data: {
                    msgType: 1,
                    msgContent: '',
                    content: this.form.issue,
                    orderId: this.chatItem.orderId,
                    seatUserId: "",
                    touchType: this.$constant.chat.touchType.informationGuide,
                    createTime: time
                }
            }
            this.$ext.webSocket.webSocketSend(this.$constant.chat.SINGLE_CHAT_CMD, dto)

            // 咨询消息回显
            let listItem = {
                hasBeenSentId: time + '-' + this.chatItem.userId, //已发送过去消息的id
                content: this.form.issue,
                fromUserHeadImg: that.defaultAvatar, //用户头像
                fromUserId: that.chatItem.userId,
                isItMe: true,
                createTime: time,
                msgType: 1, // 1文字文本 2图片
                msgCloudStatus: 2, //消息发送结果状态，1是正常，2是发送中，待回调，3是等待回调失败
            }
            this.$common.setKeyVal('chat', 'chatContent', this.form.issue)
            let messageList = this.$common.getKeyVal('chat', 'messageList', false)
            this.$common.setKeyVal('chat', 'messageList', [...messageList, listItem])

            if (this.form.pic) {
                // 发送咨询图片
                let picTime = new Date().getTime()
                const picDto = {
                    cmd: this.$constant.chat.SINGLE_CHAT_CMD,
                    data: {
                        msgType: 2,
                        msgContent: '',
                        content: this.form.pic,
                        orderId: this.chatItem.orderId,
                        seatUserId: "",
                        touchType: this.$constant.chat.touchType.defaultChat,
                        createTime: picTime
                    }
                }
                this.$ext.webSocket.webSocketSend(this.$constant.chat.SINGLE_CHAT_CMD, picDto)

                // 咨询消息回显
                listItem = {
                    hasBeenSentId: picTime + '-' + this.chatItem.userId, //已发送过去消息的id
                    content: this.form.pic,
                    fromUserHeadImg: that.defaultAvatar, //用户头像
                    fromUserId: that.chatItem.userId,
                    isItMe: true,
                    createTime: picTime,
                    msgType: 2, // 1文字文本 2图片
                    msgCloudStatus: 2, //消息发送结果状态，1是正常，2是发送中，待回调，3是等待回调失败
                }

                messageList = this.$common.getKeyVal('chat', 'messageList', false)
                this.$common.setKeyVal('chat', 'messageList', [...messageList, listItem])
            }


            this.$nextTick(() => {
                uni.pageScrollTo({
                    scrollTop: 99999,
                    duration: 0, //小程序如果有滚动效果 input的焦点也会随着页面滚动...
                });
            });
        },
        // 异步获取元素
        getEl (getDiv) {
            return new Promise((resolve, reject) => {
                // #ifdef H5
                resolve(getDiv.getBoundingClientRect())
                // #endif

                // #ifndef H5
                if (getDiv.boundingClientRect) {
                    getDiv.boundingClientRect(data => {
                        resolve(data)
                    }).exec()
                }
                // #endif
            })
        },
        imgReturnFn (obj,key) {
            this.form[key] = obj[0].dir
        }
    }
}
</script>

<style lang="scss" scoped>
.fixed-box {
    position: fixed;
    z-index: 999;
    bottom: 0;
    width: 100%;
	font-size: 28upx;
    background: #fff;

    .form {
        padding: 0 24upx;
        padding-bottom: calc(16upx + env(safe-area-inset-bottom) );

        .btn {
            display: block;
            width: 675upx;
            margin: 0 auto;
            background-color: #00d29d;
            border: none;
            height: 80upx;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 37upx;
            border-radius: 40upx;
            // line-height: 74upx;
        }
    }
    .content-top {
        display: flex;
        justify-content: space-between;
        padding: 24upx 20upx 0;
        &-text {
            color: $topicC;
            font-size: 32upx;
            line-height: 42upx;
        }
    }
}
</style>
