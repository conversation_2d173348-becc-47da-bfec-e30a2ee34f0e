<template>
  <page>
    <view slot="content" class="body-main">
      <view class="integration-time-er-box">
        <integration-time-er :point='IntegrationData.integrationNum' :tracks='IntegrationData.integrationRuntimeNum'></integration-time-er>
      </view>
      <!-- #ifndef H5 -->
      <sa-hover-menu v-if="isShowBtn" />
      <!-- #endif -->
      <!--:scroll-top='scrolltop' @scrolltolower='bindscrolltolower' @refresherrefresh='bindscrolltoupper' :refresher-enabled='refresherEnable' :refresher-threshold='100' :refresher-triggered="triggered"-->
      <scroll-view
        :scroll-into-view="currentid"
        class="extendBox"
        :scroll-y="true"
      >
        <view
          v-if="
            !$validate.isNull(postmessageInfo.imagesPath) &&
            postmessageInfo.imgShowType === 1
          "
          class="swiper"
        >
          <view class="swiper-pagesize-box">
            <text class="swiper-pagesize">{{
              `${swiperCurrent + 1}/${postmessageInfo.imagesPath.length}`
            }}</text>
          </view>
          <swiper
             :style="{ width: swiperStyle.width, height: swiperStyle.height }"
            class="picker-modal-body"
            :indicator-dots="false"
            :circular="false"
            :duration="200"
            :current="swiperCurrent"
            adjust-height="none"
            @change="handleChangeSwiper"
          >
            <swiper-item
              class="swiper-item"
              style="height: 100%;width:100%"
              v-for="(item, index) in postmessageInfo.imagesPath"
              :key="index"
            >
              <view class="reference" v-if="postmessageInfo.markerSwitchType == 1">
                <view class="reference-text"><view class="img"><image :src="file_ctx + 'static/image/business/hulu-v2/icon-reference-exclamation.png'"></image></view>仅供参考，如身体不适请及时就医</view>
              </view>
              <image
                v-show="isShowImg"
                lazy-load
                :src="item"
                :mode="dynamicZoom"
                :style="{
                  width: '100%', 
                  height: '100%',
                }"
                @load="handleGetWidthHeight($event, index)"
                @click="previewImage(index)"
              >
              </image>
            </swiper-item>
          </swiper>
        </view>
        <view class="body">
          <view class="news-title text-xl">{{
            postmessageInfo.title || ""
          }}</view>
          <view class="sub-info">
            <view class="sub-left">
              <text>{{ postmessageInfo.putawayTimeText }}</text>
              <!-- <text v-if="postmessageInfo.province" style="margin-left:10rpx">发布于{{ postmessageInfo.province }}</text> -->
              <text class="sub-read-num"
                >{{
                  $common.bigNumberTransform(
                    postmessageInfo.showReadNumber || 0
                  )
                }}人看过</text
              >
            </view>
            <text class="author" v-if="isShowBtn" @click="cateClick(postmessageInfo)"
              >#{{ postmessageInfo.circleClassifyName || "圈子" }}</text
            >
          </view>
          <text class="province-text" style="color: #868c9c;" v-if="postmessageInfo.province">发布于{{ postmessageInfo.province }}</text>
          <view class="user user-wrapper">
            <view
              class="user"
              style="padding-bottom: 0"
              @tap="userClick(postmessageInfo)"
              v-if="postmessageInfo.isAnonymity == 1"
            >
              <view class="avatar-wrapper">
                <image
                  class="user-avatar"
                  :src="file_ctx + 'static/image/system/icon-default-users.png'"
                  mode="aspectFit"
                >
                </image>
              </view>
              <text class="user-name">匿名用户</text>
            </view>
            <view
              class="user"
              style="padding-bottom: 0"
              @tap="userClick(postmessageInfo)"
              v-else
            >
              <view class="avatar-wrapper">
                <image
                  class="user-avatar"
                  :src="
                    postmessageInfo.headPath
                      ? file_ctx + postmessageInfo.headPath
                      : defaultAvatar
                  "
                  mode="aspectFit"
                >
                </image>
              </view>
              <text class="user-name" :style="{'flex':postmessageInfo.vType == 2 || postmessageInfo.vType == 1 ? 'none' : '1'}">{{
                postmessageInfo.nickName || "**"
              }}</text>
              <template v-if="postmessageInfo.isAddV == 1">
                <image
                  v-if="postmessageInfo.vType == 2"
                  class="head-v-icon"
                  :src="file_ctx + 'static/image/system/avatar/icon-v-e.png'"
                  mode="aspectFill"
                ></image>
                <image
                  v-else-if="postmessageInfo.vType == 1"
                  class="head-v-icon-new"
                  :src="file_ctx + 'static/image/system/avatar/icon-v-new.png'"
                  mode="aspectFill"
                ></image>
              </template>
            </view>
            <button
              type="primary"
              size="mini"
              class="invite-reply-btn"
              @tap.stop="inviteReply"
              v-if="
                postmessageInfo.commentsInviteStatus === 1 &&
                postmessageInfo.accountId === accountId
              "
            >
              邀请医生解答
            </button>
            <!-- #ifndef H5 -->
            <button
              class="share-btn"
              open-type="share"
              style="padding: 0; display: flex"
              @click="sharePosts"
            >
              <image
                mode="heightFix"
                class="share-icon"
                :src="file_ctx + 'static/image/business/hulu-v2/icon-share.png'"
              ></image>
            </button>
            <!-- #endif -->

            <!-- #ifdef H5 -->
            <button
              class="share-btn"
              open-type="share"
              style="padding: 0; display: flex"
              @click="handleClickShare"
            >
              <image
                mode="heightFix"
                class="share-icon"
                :src="file_ctx + 'static/image/business/hulu-v2/icon-share.png'"
              ></image>
            </button>
            <!-- #endif -->
          </view>
          <view
            id="article"
            class="article"
            :class="{ 'body-height': contentStatus }"
          >
            <view class="text-l">
              <mp-html
                lazy-load
                show-with-animation
                :tag-style="tagStyle"
                :content="postmessageInfo.content"
              />
            </view>
            <block v-if="postmessageInfo.productPath">
              <nui-card-product :product="postmessageInfo.productPath">
              </nui-card-product>
            </block>
            <template v-if="postmessageInfo.videosPath">
              <video
                v-for="(item, index) in postmessageInfo.videosPath.split(',')"
                :key="index"
                style="width: 100%; border-radius: 12px"
                :src="file_ctx + item"
                mode="widthFix"
              ></video>
            </template>
          </view>
          <view class="person-statement">个人声明：仅供参考，如有不适建议及时就医</view>
          <view class="gambit-box" v-if="postmessageInfo.topicIdsArr && postmessageInfo.topicIdsArr.length">
            <view class="gambit-item" v-for="(item,index) in postmessageInfo.topicIdsArr" :key="index" @click="handleGambitDetail(item.id)">#{{ item.name }}</view>
          </view>
          <banner-ads v-if="postmessageId && isShowBtn" ref="bannerAdsRef" :query-params="{useType: 9, businessId: postmessageId}" height="192rpx" width="686rpx"></banner-ads>
          <view
            class="jumpCard"
            @click="handleClickJump(postmessageInfo.jumpCardLink)"
            v-if="postmessageInfo.jumpCardLink"
          >
            <view class="jumpCard-l"
              ><image
                mode="aspectFit"
                class="share-icon"
                :src="postmessageInfo.jumpCardCover"
              ></image
            ></view>
            <view class="jumpCard-c">{{ postmessageInfo.jumpCardTitle }}</view>
            <view class="jumpCard-r"
              ><uni-icons
                class="move-active"
                :size="10"
                color="#666"
                type="right"
            /></view>
          </view>
          <!-- #ifdef H5 -->
          <view
            v-if="showContentStatus"
            class="content-toggle"
            @tap="changeContentStatus(!contentStatus)"
          >
            <wx-open-launch-weapp
              style="width: 678rpx; height: 60rpx"
              :appid="$appId"
              :path="`modules/community/posts/detail/index?id=${postmessageId}`"
            >
              <script type="text/wxtag-template">
                <style>
                    .jump-wx-btn {
                        width: 100%;
                        height: 100vh;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: #e9686b;
                    }
                </style>
                <div class="jump-wx-btn">{{ contentStatus ? '阅读全文' : '收起全文' }}</div>
              </script>
            </wx-open-launch-weapp>
          </view>
          <!-- #endif -->
        </view>
        <view
          class="bottom"
          id="commentbox"
          :style="'padding-bottom:' + safeAreaInsetBottom + 'px;'"
          >
          <comment
            ref="comment"
            :deleteTip="'确认删除？'"
            :cmData="commentData"
            :postmessageInfo="postmessageInfo"
            :isJumpWx="true"
            :moreLoading="moreLoading"
            :isShowBtn="isShowBtn"
            @scrollcomment="scrollcomment"
            @loadMore="loadMore"
            @add="add"
            @del="del"
            @like="like"
            @focusOn="focusOn"
            @changeCommentInputHeight="changeCommentInputHeight"
            @comment="addComment"
            @collectPosts="collectPosts"
            @likePosts="likePosts"
            @sharePosts="sharePosts"
            @replyBefore="replyBefore"
          >
          </comment>

        </view>

      </scroll-view>
      <uni-popup class="sharePopup" ref="sharePopup" type="top">
        <view class="share-box" @click="handleClickShareClose">
          <image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-everyday-share-box.png'"></image>
        </view>
      </uni-popup>
    </view>
  </page>
</template>

<script>
import uniPopup from '@/components/uni/uni-popup'
import UniIcons from '@/components/uni/uni-icons/uni-icons'
import mpHtml from '@/components/basics/mp-html/mp-html.vue'
import comment from '@/modules/community/components/comment/comment.vue'
import { mapState } from 'vuex'
import nodesUtils from '@/mixins/nodes-utils'
import jumpWxDialog from '@/modules/community/components/jump-wx-dialog/jump-wx-dialog.vue'
import { getQueryObject } from '@/utils/index'
import { isDomainUrl } from '@/utils/index.js'
import contentMore from './components/contentMore/index'
import bannerAds from '@/components/basics/banner-ads/index.vue'
import env from '@/config/env'

export default {
  components: {
    UniIcons,
    mpHtml,
    comment,
    jumpWxDialog,
    uniPopup,
    contentMore,
    bannerAds
  },
  mixins: [nodesUtils],
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      $validate: this.$validate,
      $appId: this.$appId,
      postmessageId: '',
      postmessageInfo: {},
      tagStyle: {
        table: 'box-sizing: border-box; border-top: 1px solid #dfe2e5; border-left: 1px solid #dfe2e5;',
        th: 'border-right: 1px solid #dfe2e5; border-bottom: 1px solid #dfe2e5;',
        td: 'border-right: 1px solid #dfe2e5; border-bottom: 1px solid #dfe2e5;',
        li: 'margin: 5px 0;'
      },
      defaultAvatar: this.file_ctx + 'static/image/system/avatar/icon-default-avatar.png',
      commentList: [],
      // pageSize: 20,
      pageSize: 7,
      pageCurrent:1,
      isHasMore: false,
      safeAreaInsetBottom: 0,
      pageStartDate: '',
      scrolltop: 0,
      currentid: '',
      shareConfig: {},
      contentStatus: false, // 是否收起文章
      showContentStatus: false, // 是否显示收起展开文章按钮
      swiperCurrent: 0,
      moreLoading: false,
      initCommentLoading: true,
      postInviteId: '', // 帖子邀请id
      commentPublishCount: null, //精彩评论数
      launchOptions: uni.getLaunchOptionsSync(),
      isShowBtn:true, //默认显示圈子
      dynamicZoom:'aspectFit',
      swiperStyle: {
        width: '100%',
        height: '502px'
      },
      isShowImg:false,
      swiperHeight:0,
      bodyHeight:0,
      windowHeight:0,
      isReachedBottom:false,
      debounceTimer:null,
      postsProgress:0, //帖子浏览进度
      postsHistoryProgress:0, //帖子浏览进度 历史最大
      percentage:0, //图片轮播进度
      userActivityId: null,
      userActivityInviteLogId: null,
      distanceParams: {},
      codeNames:'',
    }
  },
  async onLoad() {
    const query = this.$Route.query
    const that = this
    if(query?.isShowBtn){
      this.isShowBtn = JSON.parse(query.isShowBtn)
    }
    if (this.$validate.isNull(query.id)) {
      let params = decodeURIComponent(query.scene || query.query)
      // console.log("params:====", params)
      query.id = getQueryObject(params).id
      if (this.$validate.isNull(query.id)) {
        that.$uniPlugin.toast('参数异常');
        return
      }
    }
    // 邀请评论
    if (this.$validate.isNull(query.postInviteId)) {
      let params = decodeURIComponent(query.scene)
      query.postInviteId = getQueryObject(params).postInviteId
    }

    // 用户活动
    this.userActivityId = query.userActivityId
    this.userActivityInviteLogId = query.userActivityInviteLogId
    this.distanceParams = query.distanceParams || {}

    this.postInviteId = query.postInviteId
    // 必须登录
    const { centerUserId = '' } = this.curSelectUserInfo || {}
    if (this.postInviteId && !centerUserId) {
      this.replaceLogin()
    }
    this.postmessageId = query.id
    this.$nextTick(() => {
      this.$refs.bannerAdsRef.init()
    })
    this.pageStartDate = new Date().getTime()
    this.recordPostmessageVisit()
    this.initCommentLoading = true
    this.getPostmessageQueryOne().then(res => {
      setTimeout(() => {
        this.checkShowContentStatus()
      }, 500)
      // #ifdef H5
      this.wxh5ShareInit() // mixins
      // #endif
      uni.setNavigationBarTitle({
        title: this.postmessageInfo.title
      });

      if (this.launchOptions.scene === 1154) return
      this.getCommentQueryLevelPage({}).then(() => {
        this.initCommentLoading = false
      }).catch(() => {
        this.initCommentLoading = false
      })
    }).catch(() => {
      this.initCommentLoading = false
    })
    // #ifdef MP-WEIXIN
    // wx.showShareMenu({
    //   withShareTicket:true,
    //   //设置下方的Menus菜单，才能够让发送给朋友与分享到朋友圈两个按钮可以点击
    //   menus:["shareAppMessage","shareTimeline"]
    // })
    // #endif
    this.windowHeight = uni.getSystemInfoSync().windowHeight //手机屏幕高度
  },
  onHide(){
    this.handleLeavePage()
    this.handleEndContentPictureView(this.postmessageInfo)
  },
  onUnload() {
    this.handleLeavePage()
    this.handleEndContentPictureView(this.postmessageInfo)
  },
  onShareAppMessage(res) {
    let shareOptions = this.mixinsShareOptions()
    const that = this
    if (res.from === 'button') {// 来自页面内分享按钮
      // console.log(res.target)
    }
    return {
      title: this.postmessageInfo.title, //分享的名称
      path: shareOptions.path + '&query=' + encodeURIComponent(JSON.stringify({
        id: this.postmessageId
      })),
      mpId: this.$appId, //此处配置微信小程序的AppId
      imageUrl: this.postmessageInfo.imagesPath.length > 0 ? this.postmessageInfo.imagesPath[0] : '',
      success: () => {
        this.shareRecord()
      }
    }
  },
  //分享到朋友圈
  onShareTimeline(res) {
    const that = this
    let shareOptions = this.mixinsShareOptions()
    return {
      title: this.postmessageInfo.title,
      path: shareOptions.path,
      query: `id=${this.postmessageId}`,
      imageUrl: this.postmessageInfo.imagesPath.length > 0 ? this.postmessageInfo.imagesPath[0] : '',
      success: () => {
        this.shareRecord()
      }
    }
  },
  computed: {
    commentData() {
      return {
        readNumer: this.postmessageInfo.showReadNumber,
        commentSize: this.postmessageInfo.showCommentNumber,
        comment: this.commentList,
        isHasMore: this.isHasMore,
        loading: this.initCommentLoading,
        commentPublishCount:this.commentPublishCount,
      }
    },
    ...mapState('user', {
      curSelectUserInfo: state => state.curSelectUserInfo, // 当前选中的用户信息
      accountId: state => state.accountId,
      fansRecord: state => state.fansRecord,
      isLogin: state => state.isLogin
    }),
  },
  methods: {
    // 用户活动 校验当前距离有没有超出范围
    verifyDistance() {
      return new Promise((resolve, reject) => {
        this.$api.activity.useractivityinvitelogGetDistance(this.distanceParams).then(() => {
          resolve()
        }).catch(() => {
          reject()
        })
      })
    },
    // 计算初始进度
    calculateProgress() {
      if (this.bodyHeight <= this.windowHeight) {
        this.postsProgress = 100
        this.postsHistoryProgress = '100.00'
      } else {
        // 内容超过一屏：计算可见比例
        let visibleRatio = (this.windowHeight - this.swiperHeight - 172) / this.bodyHeight
        this.postsProgress = Math.min(visibleRatio * 100, 100).toFixed(2)
        this.postsHistoryProgress = this.postsProgress
      }
    },

    // 浏览进度离开页面调接口
    handleLeavePage(){
      if(this.postsProgress){
        this.contentfinishrateInsert({contentFinishRate:this.postsHistoryProgress,bannerFinishRate:this.percentage,})
      } 

      // if(this.percentage){
      //   this.contentfinishrateInsert({bannerFinishRate:this.percentage,contentFinishRate:this.postsHistoryProgress})
      // }

      if (this.debounceTimer) clearTimeout(this.debounceTimer)
    },

    handleChangeSwiper(e){
      this.swiperCurrent = e.detail.current
      this.percentage = (((this.swiperCurrent + 1) / this.postmessageInfo.imagesPath.length) * 100).toFixed(2)
    },

    getContentHeight() {
      const query = uni.createSelectorQuery().in(this)
      query.select('.body-main .swiper').boundingClientRect(data => {
        this.swiperHeight = data?.height || 0
      }).exec()

      query.select('.body-main .body').boundingClientRect(data => {
        this.bodyHeight = data?.height || 0
      }).exec()

      setTimeout(()=>{
        this.calculateProgress()
      },500)
    },
    
    // 话题详情入口
    handleGambitDetail(id){
      this.$navto.push('GambitDetail',{id})
    },
    // 获取第一张图信息
    handleGetWidthHeight(e, index){
      if(index !== 0) return
      let screenWidth = uni.getSystemInfoSync().windowWidth //手机屏幕高度
      let imgHeight = e.detail.height * (screenWidth / e.detail.width) //图片高度换算
      let calculatedHeight = 1004 * (screenWidth / 750) //计算后的高度
      if (imgHeight <= calculatedHeight) {
        this.swiperStyle.height = imgHeight + 'px'
        this.dynamicZoom = 'heightFix'
        this.isShowImg = true
      } else {
        this.swiperStyle.height = calculatedHeight + 'px'
        this.isShowImg = true
      }
    },
    // #ifdef MP-WEIXIN
    //点赞，评论，收藏事件
    handleClickTrack(obj,type){
      getApp().globalData.sensors.track("ContentClick",
        {
          'content_id' : obj.id,
          'content_author' : obj.nickName,
          'content_author_id' : obj.accountId,
          'content_name' : obj.title,
          'content_belong_circle' : obj.circleClassifyName,
          // 'content_belong_hospital_id' : '',
          'code_name' : this.codeNames,
          // 'content_belong_medicine_id' : '',
          'click_type' : type == 1 ? '收藏' : type == 2 ? '点赞' : '评论',
          'comment_content' : obj?.trackContent || '',
        }
      )
    },
    // #endif

    findCodeNamesByIds(arr, labelIds) {
      // 将标签ID字符串分割为数组
      const ids = labelIds.split(',');
      const result = [];

      // 递归查找匹配节点的codeName
      function searchInChildren(children) {
        if (!children) return;
        
        for (const child of children) {
          if (ids.includes(child.id)) {
            result.push(child.codeName);
          }
          
          // 如果当前节点有子节点，继续递归查找
          if (child.children && Array.isArray(child.children) && child.children.length > 0) {
            searchInChildren(child.children);
          }
        }
      }

      // 开始在根节点的子节点中查找
      searchInChildren(arr);
      
      return result;
    },

    // #ifdef MP-WEIXIN
    //帖子图片浏览完成
    handleEndContentPictureView(obj,type){
      let params = {
        'content_id' : obj.id,
        'content_author' : obj.nickName,
        'content_author_id' : obj.accountId,
        'content_name' : obj.title,
        'content_belong_circle' : obj.circleClassifyName,
        'code_name' : this.codeNames,
        'post_view_percentage':this.postsHistoryProgress ? this.postsHistoryProgress : 0,
        'content_completion_rate':this.percentage ? this.percentage : 0,
      }
      getApp().globalData.sensors.track("EndContentPictureView",params)
    },
    // #endif
    handleClickShareClose(){
      this.$refs.sharePopup.close()
    },
    handleClickShare(){
      this.$refs.sharePopup.open()
    },
    itemClick(item) { },

    // 携带当前参数跳转到登录页
    replaceLogin() {
      let pages = getCurrentPages();
      let page = pages[pages.length - 1];
      let routeUrl = ''
      // #ifdef H5
      routeUrl = window.location.hash.replace('#/', '')
      routeUrl = routeUrl ? routeUrl.split('?')[0] : ''
      // #endif

      // #ifndef H5
      routeUrl = page.route
      // #endif
      routeUrl = routeUrl === '' ? 'pages/index/index' : routeUrl

      let query = ''
      // #ifdef H5
      query = getQueryObject(window.location.hash)
      // #endif
      // #ifndef H5
      // 参数
      query = page.options
      // #endif
    },
    // 卡片跳转
    handleClickJump(path) {
      if (['/pages/index/index', '/pages/circle-home/index', '/pages/news/index', '/pages/post-message/index', '/pages/personal/index'].includes(path)) {
        uni.switchTab({
          url: path
        })
      } else {
        this.$navto.pushPath(path)
      }
    },
    // 邀请医生解答
    inviteReply() {
      this.navtoGo('PostsInviteReply', { postMessageId: this.postmessageId })
    },
    changeJumpWxDialogStatus() {
      // #ifdef H5
      // this.$refs.jumpWxDialogRef.open()
      // #endif
    },
    changeContentStatus(e) {
      this.changeJumpWxDialogStatus(true)
      this.contentStatus = e
    },
    /**
     * 检查文章高度是否超出
     * 是否需要显示展示全文按钮
     */
    checkShowContentStatus() {
      // #ifdef H5
      this.getElInfo('article').then(info => {
        if (info.height > 500) {
          this.contentStatus = true
          this.showContentStatus = true
        } else {
          this.contentStatus = false
          this.showContentStatus = false
        }
      })
      // #endif

    },
    scrollcomment() {
      this.currentid = 'commentbox';
      setTimeout(() => {
        this.currentid = ''
      }, 1000)
    },
    userClick(data) {
      if (data.isAnonymity == 1) {
        this.$uniPlugin.toast('该帖子为匿名发表，无法查看该用户')
        return
      }
      const accountId = data.accountId
      // 是否是自己
      if (this.$common.getKeyVal('user', 'accountId') === accountId) {
        this.$navto.pushTab('Personal', {})
      } else {
        this.navtoGo('PersonalHomePage', { homePageAccountId:accountId,isShowBtn:this.isShowBtn  })
      }
    },
    recordPostmessageVisit() {
      if (this.launchOptions.scene === 1154) return
      const param = {
        accountId: this.accountId,
        id: this.postmessageId
      }
      this.$api.postmessage.postmessageVisit(param)
    },
    // 评论时的点击动作 判断是否需要进行授权
    replyBefore() {
      this.changeJumpWxDialogStatus(true)
      this.getUserInfo().catch(err => {
        this.$refs.comment.hbComment.closeInput()
      })
    },
    // 收藏帖子
    collectPosts() {
      this.changeJumpWxDialogStatus(true)
      function collect() {
        if (this.postmessageInfo.collectSubscribeStatus != 1) {
          this.postmessageInfo.collectSubscribeStatus = 1
          this.postmessageInfo.showCollectNumber += 1
          this.$api.postmessage.postmessageAddCollection({ id: this.postmessageId, accountId: this.accountId }).catch(() => {
            this.postmessageInfo.collectSubscribeStatus = 2
            this.postmessageInfo.showCollectNumber -= 1
          })
          // #ifdef MP-WEIXIN
          this.handleClickTrack(this.postmessageInfo,1)
          // #endif
        } else {
          this.postmessageInfo.collectSubscribeStatus = 2
          this.postmessageInfo.showCollectNumber -= 1
          this.$api.postmessage.postmessageCancelCollection({ id: this.postmessageId, accountId: this.accountId }).catch(() => {
            this.postmessageInfo.collectSubscribeStatus = 1
            this.postmessageInfo.showCollectNumber += 1
          })
        }
      }
      this.getUserInfo().then(() => {
        collect.call(this)
      })
    },
    // 点赞帖子
    likePosts() {
      this.changeJumpWxDialogStatus(true)
      async function like() {
        if (this.postmessageInfo.likeSubscribeStatus != 1) {
          const params = { id: this.postmessageId, accountId: this.accountId }
          let api = this.$api.postmessage.postmessageAddLike
          // 用户活动
          if (this.userActivityId) {
            await this.verifyDistance()
            params.userActivityId = this.userActivityId
            params.userActivityInviteLogId = this.userActivityInviteLogId
            api = this.$api.postmessage.postmessageActivitytypeAddLike
          }

          this.postmessageInfo.likeSubscribeStatus = 1
          this.postmessageInfo.showLikeNumber += 1
          api(params).catch(() => {
            this.postmessageInfo.likeSubscribeStatus = 2
            this.postmessageInfo.showLikeNumber -= 1
          })
          // #ifdef MP-WEIXIN
          this.handleClickTrack(this.postmessageInfo,2)
          // #endif

        } else {
          this.postmessageInfo.likeSubscribeStatus = 2
          this.postmessageInfo.showLikeNumber -= 1
          this.$api.postmessage.postmessageCancelLike({ id: this.postmessageId, accountId: this.accountId }).catch(() => {
            this.postmessageInfo.likeSubscribeStatus = 1
            this.postmessageInfo.showLikeNumber += 1
          })
        }
      }
      this.getUserInfo().then(() => {
        like.call(this)
      })
    },
    shareRecord() {
      if (this.launchOptions.scene === 1154) return
      const param = {
        accountId: this.accountId,
        businessType: 5, // 业务类型:1-横幅，2-外部服务应用，3-企业微信群，4-圈子，5-帖子，6-帖子评论
        businessId: this.postmessageId,
        source: 1, // 来源：1-真实用户，2-马甲
        type: 13, // 类型：1-访问，2-点击，3-长按，4-关注，5-取关，6-点赞，7-收藏，8-删除评论，9-取消点赞，10-取消收藏，11-阅读 13-分享
      }
      this.$api.community.applicationoperatelogShare(param)
    },
    // 分享帖子
    sharePosts() {
      this.shareRecord()
    },
    /**
     * 发送文本评论
     */
    async addComment({ target, content = '', imagePath = '' }) {
      this.$uniPlugin.loading('发送中...')
      const { centerUserId = '' } = this.curSelectUserInfo || {}
      let params = {
        source: 1, // 来源：1-真实用户，2-马甲
        userId: centerUserId,
        content: content,
        imagePath: this.$validate.isNull(imagePath) ? '' : imagePath.map(item => item.dir).join(','),
        level: this.$validate.isNull(target) ? 1 : 2, // 级别：1-一级，2-子级
        accountId: this.accountId,
        businessId: this.postmessageInfo.id,
        type: 2, // 1-PGC；2-UGC
        postInviteId: this.postInviteId
      }

      if (!this.$validate.isNull(target)) {
        params = {
          ...params,
          ownerCommentId: target.ownerCommentId ? target.ownerCommentId : target.id,
          mutualCommentId: target.id,
          mutualAccountId: target.accountId,
          mutualUserId: target.userId,
        }
      }

      // 获取openid
      let openId = await this.$ext.wechat.getOpenId()
      // openid:openId,
      params.openid = openId
      params.appId = 'wx436b8e65632f880f'
      // 用户活动
      if (this.userActivityId) {
        await this.verifyDistance()
        params.userActivityId = this.userActivityId
        params.userActivityInviteLogId = this.userActivityInviteLogId
      }

      const res = await this.$api.postmessage.commentPostMessageComment(params)
      // #ifdef MP-WEIXIN
      this.handleClickTrack({...this.postmessageInfo,trackContent:content},3)
      // #endif
      // 邀请的评论只保存第一次回复的内容（必须是一级评论）
      if (this.$validate.isNull(target)) {
        this.postInviteId = ''
      }

      res.data = {
        ...res.data,
        // openid:openId,
        nickName: this.fansRecord.nickName,
        headPath: this.fansRecord.headPath,
        mutualNickName: this.$validate.isNull(target) ? '' : target.nickName
      }
      this.$uniPlugin.hideLoading()
      this.$refs.comment.hbComment.clearInput()
      this.$refs.comment.hbComment.closeInput()
      const cmItem = this.etCommentItem(res.data, true)
      this.postmessageInfo.commentNumber += 1
      this.postmessageInfo.showCommentNumber += 1
      this.pushCommentInList(cmItem)
      await this.$uniPlugin.subscribeMessage(this.$constant.system.commentReplyTmplIds)
      const subscribeMessageRes = await this.requestSubscribeMessage(this.$constant.system.commentReplyTmplIds)
      this.$uniPlugin.toast('评论成功')
      const logParamList = Object.keys(subscribeMessageRes).filter(key => {
        return this.$constant.system.commentReplyTmplIds.includes(key)
      }).map(key => {
        let businessType = null
        if (this.$constant.system.commentReplyTmplIds.includes(key)) {
          businessType = 4
        }
        return {
          appId: this.$appId,
          templateId: key,
          openId: openId,
          subscribeStatus: subscribeMessageRes[key],
          businessType,
          businessId: this.postmessageId,
          accountId: this.accountId,
          userId: centerUserId
        }
      })
      this.$api.common.wxsubscribemessagelogInsertBatch({ wxSubscribeMessageLogList: logParamList })
      // 消息订阅调起成功，返回值'accept'、'reject'、'ban'分别代表用户对此条订阅是同意、拒绝、后台禁用
    },
    // 消息订阅
    requestSubscribeMessage(tmplIds) {
      return new Promise((resolve, reject) => {
        try {
          this.$uniPlugin.requestSubscribeMessage(tmplIds, (res) => {
            let status = true
            tmplIds.forEach(item => {
              if (res[item.toString()] !== 'accept') status = false
            })
            if (status) {
              this.isShow = false
              resolve(res)
            } else {
              this.isShow = true
              resolve(res)
            }
          })
            // #ifdef MP-WEIXIN
            getApp().globalData.sensors.track("Subscription",
              {
                'content_belong_circle' : this.postmessageInfo?.circleClassifyName || '',
                'function_name' : '评论回复通知',
                'subscription_type':'一次性订阅'
              }
            )
            // #endif
          this.$uniPlugin.hideLoading()
        } catch (err) {
          reject(err)
        }
      })
    },
    add() {
      // console.log('添加评论', ...arguments)
    },
    async del(data) {
      const { id } = data
      await this.$api.postmessage.commentDeleteOne({ id })

      // 手动设置删除状态
      if (data.mutualCommentId) {
        const oneIndex = this.commentList.findIndex(item => item.id === data.mutualCommentId)
        let eItem = this.commentList[oneIndex]
        eItem.children = eItem.children.map(item => {
          if (item.id === data.id) {
            item.status = -1
            return this.etCommentItem(item)
          } else {
            return item
          }
        })
        this.$set(this.commentList, oneIndex, eItem)
        this.$forceUpdate()
      } else {
        const oneIndex = this.commentList.findIndex(item => item.id === data.id)
        let eItem = this.commentList[oneIndex]
        eItem.status = -1
        eItem = this.etCommentItem(eItem)
        this.$set(this.commentList, oneIndex, eItem)
        this.$forceUpdate()
      }
    },
    like(data) {
      this.getUserInfo().then(() => {
        this.likeComment(...arguments)
      })
    },
    getUserInfo() {
      return this.$ext.user.authCommunityFansInfo()
    },
    focusOn() {
      // console.log('失去焦点', ...arguments)
    },
    back() {
      this.$navto.back()
    },
    async likeComment(data) {
      const { centerUserId = '' } = this.curSelectUserInfo || {}
      const params = {
        commentId: data.id,
        source: 1, // 1-真实用户，2-马甲用户,3-系统用户
        accountId: this.accountId,
        userId: centerUserId
      }
      // 是否已点赞
      if (data.likeSubscribeStatus == 1) {
        // 取消点赞
        this.$api.postmessage.commentReduceLikenumber(params)
      } else {
        // 新增点赞
        this.$api.postmessage.commentIncreaseLikenumber(params)
      }

      try {
        // 手动设置点赞或取消点赞
        if (data.mutualCommentId) {
          const oneIndex = this.commentList.findIndex(item => item.id === data.mutualCommentId)
          let eItem = this.commentList[oneIndex]
          eItem.children = eItem.children.map(item => {
            if (item.id === data.id) {
              if (data.likeSubscribeStatus == 1) {
                item.likeSubscribeStatus = 2
                item.likeNumber -= 1
              } else {
                item.likeSubscribeStatus = 1
                item.likeNumber += 1
              }
              return this.etCommentItem(item)
            } else {
              return item
            }
          })
          this.$set(this.commentList, oneIndex, eItem)
          this.$forceUpdate()
        } else {
          const oneIndex = this.commentList.findIndex(item => item.id === data.id)
          let eItem = this.commentList[oneIndex]
          if (data.likeSubscribeStatus == 1) {
            eItem.likeSubscribeStatus = 2
            eItem.likeNumber -= 1
          } else {
            eItem.likeSubscribeStatus = 1
            eItem.likeNumber += 1
          }
          eItem = this.etCommentItem(eItem)
          this.$set(this.commentList, oneIndex, eItem)
          this.$forceUpdate()
        }
      } catch (err) {
        console.log(err)
      }

    },
    changeCommentInputHeight(height) {
      this.safeAreaInsetBottom = height
    },
    async loadMore(params, e) {
      if (this.$validate.isNull(params)) {
        params = {
          // lastMsgId: this.commentList[this.commentList.length - 1].id,
          level: 1,
          ownerCommentId: '',
          pageSize: this.pageSize,
        }
        if(params.pageCurrent){
          params.pageCurrent = params.pageCurrent
        } else {
          this.pageCurrent += 1
          // params.pageCurrent = this.pageCurrent
        }
        this.moreLoading = true
        await this.getCommentQueryLevelPage(params).catch(err => { this.moreLoading = false })
        this.moreLoading = false
      } else {
        this.updateComment({ ...e, moreLoading: true })
        await this.getCommentQueryLevelPage(params).catch(err => { this.updateComment({ ...this.commentList.find(item => item.id === e.id), moreLoading: false }) })
        this.updateComment({ ...this.commentList.find(item => item.id === e.id), moreLoading: false })
      }
    },
    // async getCommentQueryLevelPage({ lastMsgId = '', level = 1, ownerCommentId = '', pageSize = this.pageSize }) {
    async getCommentQueryLevelPage({ level = 1, ownerCommentId = '', pageSize = this.pageSize,pageCurrent }) {
      const params = {
        accountId: this.accountId,
        businessType: 5, // 评论
        businessId: this.postmessageId,
        // lastMsgId,
        level,
        ownerCommentId,
        pageSize,
        currentDate: this.pageStartDate,
        pageCurrent:pageCurrent || this.pageCurrent,

      }
      const res = await this.$api.postmessage.commentQueryLevelPage(params).catch(err => { return Promise.reject(err) })
      // this.pageCurrent += 1
      const data = res.data.map(item => {
        return this.etCommentItem(item)
      })
      // 第一层级
      if (level === 1) {
        this.commentList.push(...data)
        if (data.length < pageSize) {
          this.isHasMore = false
        } else {
          this.isHasMore = true
        }
      } else {
        this.commentList.forEach((item, index) => {
          if (item.id === ownerCommentId) {
            this.$set(this.commentList, index, { ...item, children: [...item.children, ...data], isHasMore: res.data.length >= pageSize, ownerCommentId })
          }
        })
      }
      return Promise.resolve()
    },
    /**
     * @param {object} item 评论实体
     * @param {boolean} isAddCm 是否是手动新增的评论 用于下拉加载更多时过滤手动新增的数据
     * @return {object} item 评论实体
     */
    etCommentItem(item, isAddCm = false) {
      return {
        ...item,
        isHasMore: item.mutualNumber && item.mutualNumber > 0 ? true : false,
        children: this.$validate.isNull(item.children) ? [] : item.children,
        owner: item.accountId === this.accountId, // 是否是拥有者，为true则可以删除，管理员全部为true
        hasLike: item.likeSubscribeStatus == 1, // 是否点赞
        likeNum: item.likeNumber, // 点赞数量
        avatarUrl: item.headPath ? this.file_ctx + item.headPath : this.defaultAvatar, // 评论者头像地址
        nickName: item.nickName, // 评论者昵称，昵称过长请在后端截断
        content: item.content, // 评论内容
        parentId: item.id, // 所属评论的唯一主键
        // createTime: this.$common.formatDate(new Date(item.createTime || new Date().getTime()), 'yyyy-MM-dd HH:mm:ss'), // 创建时间
        hasShowMore: item.mutualNumber && item.mutualNumber > 0 ? true : false,
        isAddCm
      }
    },
    /**
     * 将评论手动添加到列表中对应的位置，用于用户发布评论
     * @param {object} cmItem 评论实体
     */
    pushCommentInList(cmItem) {
      // 不存在楼主评论id则为帖子下的评论
      if (!cmItem.ownerCommentId) {
        this.commentList.unshift(cmItem)
      } else {
        const ownerIndex = this.commentList.findIndex(item => item.id === cmItem.ownerCommentId) // 楼主评论索引
        let ownerItem = this.commentList[ownerIndex]
        if (this.$validate.isNull(ownerItem.children) || cmItem.ownerCommentId === cmItem.mutualCommentId) {
          ownerItem.children.unshift(cmItem)
        } else {
          const mutualIndex = ownerItem.children.findIndex(item => item.id === cmItem.mutualCommentId)
          ownerItem.children.splice(mutualIndex + 1, 0, cmItem)
        }
        this.$set(this.commentList, ownerIndex, ownerItem)
      }

    },
    /**
     * 评论更新
     * @param {object} cmItem 评论实体
     */
    updateComment(cmItem) {
      // 不存在楼主评论id则为帖子下的评论
      if (cmItem.level === 1) {
        const index = this.commentList.findIndex(item => item.id === cmItem.id)
        this.$set(this.commentList, index, cmItem)
      } else if (cmItem.level === 2) {
        const ownerIndex = this.commentList.findIndex(item => item.id === cmItem.ownerCommentId) // 一级评论索引
        let ownerItem = this.commentList[ownerIndex]
        const mutualIndex = ownerItem.children.findIndex(item => item.id === cmItem.mutualCommentId) // 二级评论索引
        ownerItem.children[mutualIndex] = cmItem
        this.$set(this.commentList, ownerIndex, ownerItem)
      }

    },
    previewImage(index) {
      uni.previewImage({
        current: index,
        urls: this.postmessageInfo.imagesPath.map(item => item)
      })
    },
    async getPostmessageQueryOne() {
      const lableIdsArr = await this.$api.activity.findByParentId({ parentId: 120003 })
      const res = await this.$api.postmessage.postmessageQueryOne({ id: this.postmessageId, accountId: this.accountId })
      this.codeNames = this.findCodeNamesByIds(lableIdsArr.data, res.data.lableIds)?.join(',') || ''
      const data = res.data
      this.commentPublishCount = data.commentStatisticsVo.commentPublishCount
      let imagesPath = Array.isArray(data.imagesPath) ? data.imagesPath : !this.$validate.isNull(data.imagesPath) ? data.imagesPath.split(',') : [];
      this.shareConfig = {
        shareTitle: data.title,
        shareImg: !this.$validate.isNull(imagesPath) ? imagesPath[0].indexOf('http') === -1 ? this.file_ctx + imagesPath[0] : imagesPath[0] : '',
        shareDesc: this.$common.rexFilter(data.content)
      }

      for (let i = 0; i < imagesPath.length; i++) {
        imagesPath[i] = isDomainUrl(imagesPath[i]);
      }
      this.postmessageInfo = {
        ...data,
        imagesPath: imagesPath,
        jumpCardCover: isDomainUrl(data.jumpCardCover),
        putawayTimeText: this.$common.formatDate(new Date(data.putawayTime || new Date().getTime()), 'yyyy-MM-dd HH:mm'),
        showCommentNumber: +data.virtualCommentNumber + data.commentNumber,
        showReadNumber: +data.virtualReadNumber + data.readNumber,
        showLikeNumber: +data.virtualLikeNumber + data.likeNumber,
        showCollectNumber: +data.virtualCollectNumber + data.collectNumber,
        showShareNumber: +data.virtualShareNumber + data.shareNumber,
        comment_number: data.commentNumber + data.virtualCommentNumber,
        topicIdsArr:data.topicIds && JSON.parse(data.topicIds) || []
      }
      
      setTimeout(() => {
        this.getContentHeight()
      }, 500)

      if(imagesPath.length && this.postmessageInfo.imgShowType == 1){
        this.percentage = (((this.swiperCurrent + 1) / imagesPath.length) * 100).toFixed(2)
      }
    },
    cateClick(data) {
      this.navtoGo('Circle', { cid: data.circleClassifyId })
    },
    navtoGo(url, obj = {}) {
      this.$navto.push(url, obj)
    },

    // 帖子完读率新增
    async contentfinishrateInsert(data){
      const { centerUserId = '' } = this.curSelectUserInfo || {}
      let params = {
        accountId : this.accountId,
        bannerFinishRate : data?.bannerFinishRate || 0, //轮播图完读率
        contentFinishRate : data?.contentFinishRate || 0, //文章完读率
        gallery : this.postmessageInfo.imgShowType, //是否为图片集合
        postId : this.postmessageId,
        source:1, //默认传1 活跃用户
        userId:centerUserId,
      }
      await this.$api.postmessage.contentfinishrateInsert(params)
    },

    reportProgress(percent){
      console.log(`浏览进度：${percent}%`);
    }
  },
  onPageScroll(e) {
    // 清除之前的计时器
    if (this.debounceTimer) clearTimeout(this.debounceTimer)

    // 设置新的计时器
    this.debounceTimer = setTimeout(() => {
      const scrollTop = e.scrollTop
      const maxScroll = (this.swiperHeight + this.bodyHeight) - this.windowHeight
        // 内容不足一屏直接标记完成
        if (maxScroll <= 0) {
          if (!this.isReachedBottom) {
            this.isReachedBottom = true
            this.reportProgress(100)
          }
          return
        }
        // 计算当前进度
        let progress = Math.min(100, (scrollTop / maxScroll) * 100)
        this.postsProgress = progress.toFixed(2)
        if(Number(this.postsProgress) > Number(this.postsHistoryProgress)){
          this.postsHistoryProgress = this.postsProgress
        }
        this.reportProgress(this.postsProgress)
        // 检查是否触底（增加 1px 容错）
        if (scrollTop >= maxScroll - 1 && !this.isReachedBottom) {
          this.isReachedBottom = true
          console.log('触底了')
          this.reportProgress(this.postsProgress)
        }
    },200)
  },
}
</script>

<style scoped lang="scss">
   .integration-time-er-box{
      position: fixed;
      top: 284rpx;
      left: 52rpx;
      color: white;
      z-index: 999999999999999;
    }
.extendBox {
  min-height: 100vh;
}
.body-main {
  min-height: 100vh;
  overflow-y: auto;
  background-color: #ffffff;
}
.body {
  background-color: #ffffff;
  padding: 32rpx 32rpx 0;
  position: relative;
}
.news-title {
  font-weight: 500;
  font-size: 38rpx;
  color: #1d2029;
  line-height: 56rpx;
}
.sub-info {
  padding-top: 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 400;
  font-size: 26rpx;
  color: #868c9c;
  line-height: 36rpx;
}
.author {
  display: inline-block;
  height: 50rpx;
  background: #eaebf0;
  border-radius: 24rpx;
  text-align: center;
  font-weight: 400;
  font-size: 24rpx;
  color: #4e5569;
  line-height: 50rpx;
  padding: 0 16rpx;
}
.sub-read-num {
  font-weight: 400;
  font-size: 26rpx;
  color: #4e5569;
  line-height: 36rpx;
  margin-left: 24rpx;
}
.jumpCard {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1rpx solid #ddd;
  margin-top: 5rpx;
  padding: 0 20rpx;
  .jumpCard-l {
    width: 100rpx;
    height: 100rpx;
    image {
      width: 100%;
      height: 100%;
    }
  }
  .jumpCard-c {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 300rpx;
  }
  .jumpCard-r {
  }
}
.article {
  /* text-indent: 2em; */
  padding-bottom: 48rpx;
  word-break: break-all;
  word-wrap: break-word;
  overflow: hidden;
  max-height: 100%;
  // border-bottom: 1px solid #f5f5f5;
}
.body-height {
  height: 500px;
}
.content-toggle {
  position: relative;
  width: 100%;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  color: #e9686b;
  border: 1px solid #e9686b;
  border-radius: 6rpx;
  font-size: 24rpx;
  margin: 30rpx 0;
}
.navbar {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  .avatar {
    width: 50rpx;
    height: 50rpx;
    border-radius: 50%;
    margin-right: 18rpx;
  }
  .nickname {
    font-size: 28rpx;
    font-weight: 500;
  }
}
.bottom {
  background-color: #ffffff;
  padding: 34rpx 36rpx;
  margin-top: 12rpx;
}
.user-wrapper {
  display: flex;
  align-items: center;
  padding: 48rpx 0;
}
.user {
  flex: 1;
  display: flex;
  align-items: center;
  width: 100%;
  &-avatar {
    width: 100%;
    height: 100%;
    @include rounded(50%);
  }
  &-name {
    flex: 1;
    font-size: 31rpx;
    font-weight: 500;
    color: #2d2d2d;
    line-height: 42rpx;
  }
  .invite-reply-btn {
    margin: 0;
    padding: 0 24rpx;
    display: flex;
    align-items: center;
    background-color: $topicC;
    border: none;
    height: 56rpx;
    line-height: 56rpx;
    border-radius: 24rpx;
    font-size: 26rpx;
  }
}
.avatar-wrapper {
  width: 64rpx;
  height: 64rpx;
  position: relative;
  margin-right: 16rpx;
}
.head-v-icon {
  // position: absolute;
  display: flex;
  width: 32rpx;
  height: 32rpx;
  margin-left: 8rpx;
  // right: 0;
  // bottom: 0;
}
.head-v-icon-new{
  display: flex;
  width: 32rpx;
  height: 32rpx;
  margin-left: 8rpx;
}
.user-name {
  font-weight: 500;
  font-size: 30rpx;
  color: #1D2029;
  line-height: 42rpx;
}
.swiper {
  width: 750rpx;
  overflow: hidden;
  position: relative;
  .swiper-item {
    display: flex;
    align-items: center;
    justify-content: center;
    .reference{
      position: absolute;
      top: 0;
      width: 100%;
      height: 88rpx;
      display: flex;
      align-items: center;
      background: linear-gradient( 180deg, rgba(0,0,0,0.5) 0%, rgba(0,0,0,0) 100%);
      .reference-text{
        display: flex;
        align-items: center;
        padding-left: 36rpx;
        font-size: 24rpx;
        color: #FFFFFF;
        line-height: 34rpx;
        .img{
          display: flex;
          width: 28rpx;
          height: 26rpx;
          margin-right: 10rpx;
          image{
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
  .swiper-pagesize-box {
    z-index: 1;
    position: absolute;
    right: 32rpx;
    bottom: 32rpx;
  }
  .swiper-pagesize {
    font-size: 20rpx;
    color: #fff;
    width: 32rpx;
    height: 32rpx;
    padding: 8rpx;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.4);
  }
}
/deep/ .share-btn {
  background: none;
  border: none !important;
  outline: none !important;
}
.share-btn::after {
  border: none;
}
.share-icon {
  height: 36rpx;
  width: auto;
}
  .share-box{
    display: flex;
    width: 670rpx;
    height: 252rpx;
    margin-left: auto;
    margin-right: 20rpx;
    image{
      width: 100%;
      height: 100%;
    }
  }
  .person-statement{
    font-size: 12px;
    color:darkgray;
  }
  .gambit-box{
    display: flex;
    flex-wrap: wrap;
    margin: 10rpx 0 32rpx;
    .gambit-item{
      width: fit-content;
      font-size: 24rpx;
      color: #143666;
      padding: 6rpx 16rpx;
      background: #EAEBF0;
      border-radius: 66rpx 66rpx 66rpx 66rpx;
      margin-right: 20rpx;
      margin-bottom: 10rpx;
      &:last-child{
        margin-right: 0;
      }
    }
  }
</style>
