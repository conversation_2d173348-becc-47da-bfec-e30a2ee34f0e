<template>
  <page>
    <view slot="content" class="body-main">
      <view class="top-head-main"></view>
      <view :style="'height:' + statusBarHeight + 'px;'"></view>
      <view class="top-nav">
        <view class="top-nav-l" @click="back">
          <!-- #ifndef MP-ALIPAY -->
          <image mode="aspectFit" :src="file_ctx + 'static/image/business/hulu-v2/icon-circle-left-arrows.png'" class="header-search-img"/>
          <!-- #endif -->
        </view>
        <view class="top-nav-r" @tap.stop="searcFn"><image mode="aspectFit" :src="file_ctx + 'static/image/business/hulu-v2/icon-personal-home-search.png'" class="header-search-img"/></view>
      </view>
      <scroll-refresh style="height: 100%;" :isShowEmptySwitch="true" :fixed="false" :isAbsolute="false" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
        <view>
          <view class="circle-info-wrapper" v-if="!$validate.isNull(circleClassifyInfo)">
            <view class="circle-info">
              <view class="circle-info-top">
                <view class="circle-info-left">
                  <view class="info-left-item">
                    <view class="circle-info-logo">
                      <image mode="aspectFit" :src="circleClassifyInfo.logoPath ? file_ctx + circleClassifyInfo.logoPath : defaultAvatar"></image>
                    </view>
                  </view>
                  <text class="circle-info-desc">{{ circleClassifyInfo.desc }}</text>
                </view>
              </view>
              <app-list :list="circleApplicationList" />
            </view>
          </view>
          <view class="main-list">
            <banner-ads v-if="cid" ref="bannerAds" :query-params="{useType: 7, circleClassifyId: cid}" height="208rpx" />
            <tabs-sticky class="main-tabs-sticky" :fontBigger="true" :bdb="false" :overflowX="true" v-model="curIndex" :tabs="tabs" @change="changeTab"></tabs-sticky>
            <view class="main-list-content">
              <nui-list :indexlist="indexlist" :isShowGambit="true" @cateClick="cateClick"></nui-list>
            </view>
            <view class="img-wrapper" @click="handlePublish">
              <view class="img-item">
                <image :src="file_ctx + 'static/image/business/hulu-v2/icon-publish-experience.png'"></image>
              </view>
              <view class="text">发布心得</view>
            </view>
          </view>
        </view>
      </scroll-refresh>
    </view>
  </page>
</template>

<script>
import { mapState } from 'vuex'
import nuiList from '@/components/community/nui-list/nui-list.vue'
import TabsSticky from '@/components/basics/tabs-sticky-v3'
import appList from './components/app-list/index.vue'
import { getQueryObject } from '@/utils/index'
import bannerAds from '@/components/basics/banner-ads/index.vue'
import serverOptions from '@/config/env/options'
export default {
  name: 'CircleHome',
  components: {
    TabsSticky,
    // search,
    nuiList,
    appList,
    bannerAds
  },
  data() {
    return {
      file_ctx: this.file_ctx,
      $validate: this.$validate,
      regForm: {
        name: ''
      },
      curIndex: 0,
      tabs: [],
      indexlist: [],
      mescroll: null, // mescroll实例对象
      downOption: {
          auto: false // 不自动加载
      },
      upOption: {
          auto: false, // 不自动加载
          empty: {
            top: 0,
            zIndex: 999,
          }
      },
      isInit: false, // 列表是否已经初始化
      cid: '', // 圈子id
      circleClassifyInfo: {}, // 圈子详情
      $constant: this.$constant,
      circleApplicationList: [], // 圈子外部应用列表
      shareConfig: {},
      statusBarHeight: 0,
    }
  },
  computed: {
    ...mapState('user', {
      codeUserInfo: state => state.codeUserInfo, // 当前登录用户信息
      curSelectUserInfo: state => state.curSelectUserInfo, // 当前选中的用户信息
      curSelectUserInfoId: state => state.curSelectUserInfoId, // 当前选中的档案的ID
      recordUserInfo: state => state.recordUserInfo,
      accountId: state => state.accountId
    })
  },
  async onLoad(paramsObj) {
    const that = this
    const query = that.$Route.query
    that.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
    if(this.$validate.isNull(query.cid)){
      let params = decodeURIComponent(query.scene)
      query.cid= getQueryObject(params).cid
      if (this.$validate.isNull(query.cid)){
        that.$uniPlugin.toast('参数异常');
        return
      }
    }
    that.cid = query.cid
    this.circleclassifyVisit() // 打标签
    this.getApplicationserviceGetCircleApplication()
    await this.getCircleclassifyQueryOne()
    // #ifdef H5
    this.wxh5ShareInit() // mixins
    // #endif
    this.getMedicineclassifyQueryList()
    uni.setNavigationBarTitle({
      title: this.circleClassifyInfo.name
    });
    this.$nextTick(() => {
      that.init()
    })
  },
  watch: {
    curIndex: {
      handler () {
        this.indexlist = []
        this.init()
      }
    }
  },
  methods: {
    circleclassifyVisit() {
      const gbScene = this.$common.getKeyVal('system', 'gbScene', true)
      if (!gbScene) return
      this.$api.circleclassify.circleclassifyVisit({ id: this.cid, accountId: this.accountId })
    },
    handlePublish(){
      uni.switchTab({
        url: `/pages/post-message/index`
      })
    },

    searcFn() {
      this.$navto.push('CommonSystemSearch', { accountId: this.accountId })
    },

    back() {
      this.$navto.back(1)
    },

    async getMedicineclassifyQueryList() {
      if (this.$validate.isNull(this.circleClassifyInfo)) return
      const res = await this.$api.medicineclassify.medicineclassifyQueryList({
        classifyAreaType: 2,
        internalClassifyId: this.circleClassifyInfo.id,
        classifyPutawayStatus: 1
      })
      this.tabs = [
        { name: '推荐', id: 1 },
        // {name: '名医热点', id: 2},
        { name: '精华', id: 3 },
      ].concat(res.data)
    },
    clickApp (data) {
      const {type,path,appId,name} = data
      this.recordClickApp(data)
      switch (type) {
        // 小程序
        case 1:
          this.$uniPlugin.navigateToMiniProgram({
            appId,
            path,
            envVersion: 'release',
            extraData: {}
          }, (res) => {
            // console.log(res)
          }, (err) => {
            // console.log(err)
          })
          break
        // H5
        case 2:
          this.$navto.push('WebHtmlView', { src: path, title: name })
          break 
        default:
      }
    },
    recordClickApp (data) {
      const param = {
          accountId: this.accountId,
          businessType: 2, // 业务类型:1-横幅，2-外部服务应用，3-企业微信群，4-圈子，5-帖子，6-帖子评论
          businessId: data.id,
          source: 1, // 来源：1-真实用户，2-马甲
          type: 2, // 类型：1-访问，2-点击，3-长按，4-关注，5-取关，6-点赞，7-收藏，8-删除评论，9-取消点赞，10-取消收藏，11-阅读
          indexPath: 2 // 操作位置：1-首页，2-圈子
      }
      this.$api.community.applicationoperatelogInsert(param)
    },
    // 获取圈子应用服务
    async getApplicationserviceGetCircleApplication () {
      const param = {
        circleClassifyId: this.cid
      }
      const res = await this.$api.community.applicationserviceGetCircleApplication(param)
      this.circleApplicationList = res.data
    },
    // 关注圈子
    subscribe () {
      const param = {
        accountId: this.accountId,
        circleClassifyIds: [this.cid]
      }
      this.$uniPlugin.loading('正在提交', true)
      this.$api.circleclassify.circleclassifySubscribe(param).then(res => {
        this.$uniPlugin.hideLoading()
        this.$uniPlugin.toast(res.msg)
        this.getCircleclassifyQueryOne()
      }).catch(() => {
        this.$uniPlugin.hideLoading()
      })
    },
    // 取消关注圈子
    unSubscribe () {
      this.$uniPlugin.modal('','确认取消关注该圈子？', {
        showCancel: true, // 是否显示取消按钮，默认为 true
        cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
        cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
        confirmText: '确定', //  确定按钮的文字，默认为"确定"，最多 4 个字符
        confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
        fn: async (n) => {
          if(n) {
            const param = {
              accountId: this.accountId,
              circleClassifyIds: [this.cid]
            }
            this.$api.circleclassify.circleclassifyUnsubscribe(param).then(res => {
              this.$uniPlugin.toast(res.msg)
              this.getCircleclassifyQueryOne()
            })
          }
        }
      })
    },
    cateClick (data) {

    },
    init() {
      this.$nextTick(() => {
        this.mescroll.triggerDownScroll()
      })
    },
    scrollInit(scroll) {
        scroll.optUp.page.num = 1
        scroll.optUp.page.size = 10
        this.mescroll = scroll
    },
    // 获取圈子详情
    async getCircleclassifyQueryOne () {
      const param = {
        id: this.cid,
        accountId: this.accountId
      }
      const res = await this.$api.circleclassify.circleclassifyQueryOne(param)
      this.circleClassifyInfo = res.data
      // console.log(this.circleClassifyInfo,'this.circleClassifyInfo00000')
      this.$common.setKeyVal('business','circleClassifyInfoObj',this.circleClassifyInfo)      
      // 是否开通名医热点模块
      if (this.circleClassifyInfo.isOpenHotspot == 2) {
        this.tabs.filter(item => item.id !== 2)
      }

      const { name = '', logoPath = '', desc = '' } = this.circleClassifyInfo || {}
      this.shareConfig = {
        shareTitle: name,
        shareImg: logoPath ? this.file_ctx + logoPath : '',
        shareDesc: desc
      }
    },
    returnFn(obj) {
        const that = this
        setTimeout(async function() {
            let params = {
              current: obj.pageNum,
              size: obj.pageSize,
              condition: {
                circleClassifyId: that.cid,
                accountId: that.accountId,
                medicineClassifyId: that.curIndex == 0 ? null : that.tabs[+that.curIndex].id,
                ...that.regForm
              }
            }
            let res = null
            if (that.tabs[that.curIndex].id === 2) {
              // 名医热点
              res = await that.$ext.community.postmessageQueryPhysicianHotPage(params)
            } else if (that.tabs[that.curIndex].id === 3) {
              // 精华
              res = await that.$ext.community.postmessageQueryEssencePage(params)
            } else {
              // 推荐
              params.condition.otherApp = serverOptions.providerId === serverOptions.getoptions().providerId
              params.condition.providerId = that.$common.getKeyVal('user','cloudProviderId',true)
              res = await that.$ext.community.postmessageQueryRecommendPage(params)
            }

            let data = res.data.records.map(item=>{
              return {
                ...item,
                topicIdsArr : item.topicIds && JSON.parse(item.topicIds) || []
              }
            }) || []
            if (obj.pageNum === 1) {
              that.indexlist = []
            }
            that.indexlist = [...that.indexlist, ...data]
            obj.successCallback && obj.successCallback(data)

        }, that.$constant.noun.scrollRefreshTime)

    },
    // 轮播菜单
    swiperChange(e) {
      this.changeTabCircle(e.detail.current)
    },
    changeSearch(obj) {
      this.mescroll.optUp.page.num = 1
      this.mescroll.optUp.page.size = 10
      this.regForm.name = obj.name
      this.mescroll.triggerDownScroll()
    },
    changeTab(index) {
      this.curIndex = index
    },
    navtoGo(url, obj = {}) {
      this.$navto.push(url, obj)
    }
  }
}
</script>

<style lang="scss" scoped>
  .body-main {
    height: 100%;
    overflow-y: auto;
    background-color: #f6f6f6;
    .top-head-main{
      position: fixed;
      top: 0;
      right: 0;
      left: 0;
      height: 410rpx;
      background-color: #fff;
    }
    .top-nav{
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 40px;
      line-height: 40px;
      padding: 0 32rpx 0 24rpx;
      .top-nav-l{
        display: flex;
        width: 48rpx;
        height: 48rpx;
        image{
          width: 100%;
          height: 100%;
        }
      }
      .top-nav-r{
        display: flex;
        width: 64rpx;
        height: 64rpx;
        padding-right: 180rpx;
        margin-top: 4rpx;
        image{
          width: 100%;
          height: 100%;
        }
      }
    }
    .main-tabs {
      position: -webkit-sticky;
      position: sticky;
      left: 0;
      top: 0;
      z-index: 3;
    }
    .main-list {
      border-radius: 20rpx;
      background-color: #FFFFFF;
      margin-top: 25upx;
      position: relative;
      /deep/.main-tabs-sticky{
        .tabs-sticky{
          padding:32rpx 32rpx 0;
          position: sticky;
          top: 0;
          z-index:999;
          .tabs-sticky-body{
            padding: 0;
          }
        }
      }
    }
    .main-list-content {
      position: relative;
    }
    .img-wrapper{
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: fixed;
      right: 20rpx;
      bottom: 304rpx;
      width: 112rpx;
      height: 112rpx;
      background: #00B484;
      border-radius: 50%;
      box-shadow: 0rpx 8rpx 16rpx 0rpx rgba(0,180,132,0.2);
      .img-item{
        display: flex;
        width: 32rpx;
        height: 32rpx;
        image{
          width: 100%;
          height: 100%;
        }
      }
      .text{
        margin-top: 4rpx; 
        height: 28rpx;
        line-height: 28rpx;
        font-size: 20rpx;
        color:#fff;
      }
    }
  }
  .circle-info-wrapper {
    width: 100%;
    box-sizing: border-box;
  }
  .circle-info {
    background: #FFFFFF;
    @include rounded(15upx);
    padding: 24upx 32rpx;
    box-sizing: border-box;
    &-top {
      display: flex;
      align-items: center;
      .circle-info-left {
        flex: 1;
        display: flex;
        align-items: center;
        margin-right: 24upx;
        .info-left-item{
          display: flex;
          align-items: center;
          justify-content: center;
          width: 88rpx;
          height: 88rpx;
          background: #EBEEF5;
          border-radius: 16rpx;
          margin-right: 20rpx;
          .circle-info-logo {
            display: block;
            width: 64upx;
            height: 64upx;
            image{
              width: 100%;
              height: 100%;
            }
          }
        }
      }
      .circle-info-desc {
        flex: 1;
        font-size: 32rpx;
        color: #1D2029;
        line-height: 44upx;
        display: inline-block;
        vertical-align: middle;
      }
      .circle-unfollowed-btn,.circle-followed-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28upx;
        border-radius: 36rpx;
        padding:12rpx 32rpx;
        color:#fff;
        background: #00B484;
        .add-img{
          display: flex;
          width: 24rpx;
          height: 24rpx;
          margin:2rpx 4rpx 0 0;
          image{
            width: 100%;
            height: 100%;
          }
        }
      }
      .circle-followed-btn{
        padding:12rpx 24rpx;
        color: #868C9C;
        background-color: #fff;
        border: 1rpx solid #D9DBE0;
      }
    }
  }
</style>
