<template>
  <uniPopup type="bottom" ref='uniPopup' @change="changeShow">
    <view class="confirm">
      <view class="popup-title">请选择服务</view>

      <!-- 分类服务列表模式 -->
      <view class="category-service-container">
        <!-- 左侧分类列表 -->
        <scroll-view scroll-y="true" class="category-scroll">
          <view
            v-for="(category, idx) in categoryList"
            :key="category.id"
            class="category-item"
            :class="{ active: currentCategoryIndex === idx }"
            @click="selectCategory(category, idx)"
          >
            <text>{{ category.name }}</text>
          </view>
          <!-- 分类加载状态 -->
          <view v-if="isLoadingCategories && categoryList.length === 0" class="loading-item">
            <text>加载中...</text>
          </view>
        </scroll-view>

        <!-- 右侧服务列表 -->
        <scroll-view scroll-y="true" class="service-scroll" @scrolltolower="loadMoreServices">
          <!-- 加载状态 -->
          <view v-if="isLoading && currentServices.length === 0" class="loading-container">
            <view class="loading-spinner"></view>
            <view class="loading-text">加载中...</view>
          </view>

          <view
            v-else
            v-for="item in currentServices"
            :key="item.id"
            class="service-item"
            @click="selectServer(item)"
          >
            <image class="service-icon" :src="file_ctx + item.listImg" mode=""></image>
            <view class="service-info">
              <view class="service-name">{{item.serviceName}}</view>
              <view class="service-price">
                <text class="price-symbol">¥</text>
                <text class="price-value">
                  <text v-if="item.cityPrice">{{item.cityPrice / 100}}</text>
                  <text v-else>{{item.price / 100}}</text>
                </text>
                <text class="price-unit">/次起</text>
              </view>
            </view>
          </view>

          <!-- 加载更多提示 -->
          <view v-if="isLoading && currentServices.length > 0" class="loading-tip">加载中...</view>
          <view v-if="serviceLoadEnd && currentServices.length > 0" class="loading-tip">没有更多了</view>
          <view v-if="currentServices.length === 0 && !isLoading && !isFirstLoad" class="empty-tip">该分类下暂无服务</view>
        </scroll-view>
      </view>
    </view>
  </uniPopup>
</template>

<script>
import { mapState } from "vuex";
import uniPopup from '@/components/uni/uni-popup/uni-popup.vue'
import serverOptions from '@/config/env/options'
export default{
  components: {
      uniPopup
  },
  props:{
    openFlag:{
      type:Boolean,
      default:false
    },
    cityRes:{
      type:Object
    },
    isISP:{
      type:Boolean,
      default:false
    },
    // 分类ID
    classifyId: {
      type: String,
      default: ''
    }
  },
  watch:{
    openFlag(n){
        if(n){
          // 如果已经加载过数据，直接打开弹窗
          this.$refs.uniPopup.open();

          // 如果从未加载过数据，先加载数据
          if (this.categoryList.length === 0) {
            this.initCategoryView();
          } else {
            if (this.classifyId) {
              const index = this.categoryList.findIndex(item => String(item.id) === String(this.classifyId));

              if (index >= 0) {
                // 使用nextTick确保DOM更新后再选择分类
                this.$nextTick(() => {
                  this.selectCategory(this.categoryList[index], index);
                });
              } else if (this.pendingCategoryIndex !== null && this.pendingCategoryIndex >= 0) {
                // 如果有待选中的分类索引
                this.$nextTick(() => {
                  this.selectCategory(this.categoryList[this.pendingCategoryIndex], this.pendingCategoryIndex);
                });
                this.pendingCategoryIndex = null; // 清除待选中状态
              } else if (this.currentCategoryIndex === 0 && this.currentServices.length === 0) {
                this.getServicesByCategory();
              }
            } else if (this.currentServices.length === 0) {
              // 如果服务列表为空，加载服务列表
              this.getServicesByCategory();
            }
          }
        } else {
          this.$refs.uniPopup.close();
        }
    },
    cityRes:{
      async handler() {
        if(this.cityRes?.cityname){
          // 只有在城市变更时才重新加载数据
          if (!this.lastCityName || this.lastCityName !== this.cityRes.cityname) {
            this.lastCityName = this.cityRes.cityname;
            this.initCategoryView();
          }
        }
      },
      deep: true
    },
    classifyId: {
      handler(newVal) {
        if(newVal) {
          // 如果分类列表已加载完成
          if(this.categoryList && this.categoryList.length > 0) {
            // 将比较改为字符串比较，确保类型一致
            const index = this.categoryList.findIndex(item => String(item.id) === String(newVal));

            if(index >= 0) {
              // 如果弹窗已打开，立即切换分类
              if (this.openFlag) {
                this.selectCategory(this.categoryList[index], index);
              } else {
                // 如果弹窗未打开，记录当前应选中的分类索引，以便弹窗打开时使用
                this.pendingCategoryIndex = index;
              }
            }
          }
        }
      },
      immediate: true
    }
  },
  data(){
    return {
      file_ctx:this.file_ctx,
      // 分类相关数据
      categoryList: [], // 分类列表
      currentCategoryIndex: 0, // 当前选中的分类索引
      currentCategoryId: '', // 当前选中的分类ID
      currentServices: [], // 当前分类下的服务列表
      serviceCurrent: 1, // 服务列表当前页
      serviceLoadEnd: false, // 服务列表是否加载完毕
      isLoading: false, // 是否正在加载
      lastProviderId: null, // 用于存储最后一次使用的providerId
      isLoadingCategories: false, // 是否正在加载分类
      isFirstLoad: true, // 是否第一次加载
      pendingCategoryIndex: null, // 待选中的分类索引
      lastCityName: null, // 上次城市名称
      categoryServicesCache: {} // 缓存各分类下的服务列表
    }
  },
  computed: {
    ...mapState("user", {
      accountId: (state) => state.accountId,
      fansRecord: (state) => state.fansRecord,
    }),
  },
   mounted() {
    // 组件挂载后，预加载分类数据，但不立即加载服务列表
    this.preloadCategoryData();
  },
  methods:{
    // 预加载分类数据
    async preloadCategoryData() {
      // 仅当分类列表为空时加载
      if (this.categoryList.length === 0) {
        try {
          this.isLoadingCategories = true;
          await this.getCategoryList();
        } catch (error) {
          console.error('预加载分类数据失败:', error);
        } finally {
          this.isLoadingCategories = false;
        }
      }
    },
    // 初始化分类视图
    async initCategoryView() {
      try {
        // 如果已有分类列表数据，不必重新加载
        if (this.categoryList.length === 0) {
          await this.getCategoryList();
        }

        // 确保分类列表已加载后再尝试定位指定分类
        if (this.categoryList && this.categoryList.length > 0) {
          // 如果有待选中的分类索引，优先使用
          if (this.pendingCategoryIndex !== null) {
            this.selectCategory(this.categoryList[this.pendingCategoryIndex], this.pendingCategoryIndex);
            this.pendingCategoryIndex = null; // 清除待选中状态
            return;
          }

          // 如果指定了分类ID，找到对应的分类
          if (this.classifyId) {
            const index = this.categoryList.findIndex(item => item.id === this.classifyId);
            if (index >= 0) {
              this.selectCategory(this.categoryList[index], index);
              return;
            }
          }

          // 没有找到指定分类或没有指定分类ID，使用第一个分类
          if (this.currentCategoryIndex === 0 && this.currentServices.length === 0) {
            this.selectCategory(this.categoryList[0], 0);
          }
        }
      } catch (error) {
        console.error('初始化分类视图失败:', error);
      }
    },

    changeShow(res){
      !res.show && this.$emit('change',res.show)
    },
    selectServer(options){
      // 获取当前选中分类信息
      const currentCategory = this.categoryList[this.currentCategoryIndex];

      // 将分类信息添加到服务对象中
      const serviceWithCategory = {
        ...options,
        categoryId: currentCategory ? currentCategory.id : '',
        categoryName: currentCategory ? currentCategory.name : '',
        classifyId: currentCategory ? currentCategory.id : '',
        classifyName: currentCategory ? currentCategory.name : '',
        providerId: this.lastProviderId || serverOptions.providerId // 确保传递服务商ID
      };

      // 传递带有分类信息的服务对象
      this.$emit('selectServer', serviceWithCategory);
      this.$emit('change', false);
    },
    close(){
      this.$emit('change',false)
    },
    upper(){
      console.log('触底');
      if(this.loadEnd) return
      this.current++;
      this.getServerData();
    },
    async getServerData(flag,insertCondition={}){
      // 保存providerId到lastProviderId
      if(insertCondition && insertCondition.providerId) {
        this.lastProviderId = insertCondition.providerId;
      }

      // 构建基础条件，优先使用lastProviderId
      let condition = {};
      if(this.cityRes?.cityname) {
        condition = {city:this.cityRes?.cityname, state:1};
      } else {
        // 优先使用传入的或保存的providerId，其次才是默认的providerId
        condition = {providerId: this.lastProviderId || serverOptions.providerId};
      }

      if(this.isISP) delete condition.state;

      // 判断是否为平台端
      const isPlatform = serverOptions.source === 1;

      // 如果是平台端并且传入了providerId，先初始化分类视图
      if (isPlatform && insertCondition && insertCondition.providerId) {
        // 清空当前分类列表，以便重新加载
        this.categoryList = [];

        // 记住当前选中的分类ID
        const previousCategoryId = this.currentCategoryId;

        // 使用setTimeout确保先显示弹窗，再加载分类数据
        setTimeout(async () => {
          // 重新加载分类列表
          await this.getCategoryList();

          // 如果分类列表加载成功且有数据
          if (this.categoryList && this.categoryList.length > 0) {
            // 查找之前选中的分类是否在新的分类列表中
            const previousCategoryIndex = this.categoryList.findIndex(item => item.id === previousCategoryId);

            if (previousCategoryIndex >= 0) {
              // 如果找到了之前的分类，选择它
              this.selectCategory(this.categoryList[previousCategoryIndex], previousCategoryIndex);
            } else {
              // 如果没找到之前的分类，默认选择第一个分类
              this.selectCategory(this.categoryList[0], 0);
            }
          }
        }, 100);

        // 如果只是为了初始化分类视图，不需要继续执行获取服务列表
        if (flag) {
          return;
        }
      }

      // 获取服务列表
      let queryOptions = {current:this.current,size:10,condition:{...condition,...insertCondition}};
      let apiFunc = this.cityRes?.cityname ? 'accompanyserviceQueryCityPage' : 'getAccompanyservicePage';
      let {data:{records,total}} = await this.$api.providerManagement[apiFunc](queryOptions);
      if(flag){
        this.serverMap = records
      } else {
        this.serverMap.push(...records)
      }
      // this.serverMap = records
      if(total <= this.serverMap.length){
        this.loadEnd = true
      }
    },

    // 获取分类列表
    async getCategoryList() {
      try {
        this.isLoadingCategories = true;

        // 构建查询条件
        const condition = {};

        // 如果不是ISP模式，添加state条件
        if (!this.isISP) {
          condition.state = 1;
        }

        // 直接使用lastProviderId，不再使用其他逻辑
        if (this.lastProviderId) {
          condition.providerId = this.lastProviderId;
        } else {
          condition.providerId = serverOptions.providerId;
        }

        const res = await this.$api.providerManagement.accompanyserviceclassifyQueryPage({
          current: 1,
          size: 50, // 获取较多分类
          condition: condition,
          // 按orderValue排序
          ascs: 'orderValue'
        });

        if (res.data && res.data.records) {
          this.categoryList = res.data.records;
        }
      } catch (error) {
        console.error('获取分类列表失败:', error);
        uni.showToast({
          title: '获取分类失败',
          icon: 'none'
        });
      } finally {
        this.isLoadingCategories = false;
      }
    },

    // 选择分类
    selectCategory(category, index) {
      // 不再阻止选择相同分类，因为可能服务列表未加载
      // if (this.currentCategoryIndex === index && this.currentServices.length > 0) {
      //   return;
      // }

      this.currentCategoryIndex = index;
      this.currentCategoryId = category.id;
      this.serviceCurrent = 1;
      this.serviceLoadEnd = false;

      // 先清空服务列表，显示加载状态
      this.isFirstLoad = false;

      // 检查缓存中是否有该分类的服务列表
      const cacheKey = `${category.id}_${this.lastProviderId || serverOptions.providerId}_${this.cityRes?.cityname || ''}`;
      if (this.categoryServicesCache[cacheKey]) {
        // 使用缓存数据
        this.currentServices = this.categoryServicesCache[cacheKey].services;
        this.serviceLoadEnd = this.categoryServicesCache[cacheKey].loadEnd;
      } else {
        // 没有缓存，清空当前服务列表并加载
        this.currentServices = [];
        // 加载该分类下的服务
        this.getServicesByCategory();
      }
    },

    // 根据分类ID获取服务列表
    async getServicesByCategory() {
      if (this.serviceLoadEnd || this.isLoading) return;

      this.isLoading = true;

      try {
        const city = this.cityRes?.cityname;

        // 构建查询条件
        const condition = {
          classifyId: this.currentCategoryId
        };

        // 如果不是ISP模式，添加state条件
        if (!this.isISP) {
          condition.state = 1;
        }

        // 添加城市条件(如果有)
        if (city) {
          condition.city = city;
        }

        // 直接使用lastProviderId，不再使用其他逻辑
        if (this.lastProviderId) {
          condition.providerId = this.lastProviderId;
        } else {
          condition.providerId = serverOptions.providerId;
        }

        const res = await this.$api.providerManagement.getAccompanyservicePage({
          current: this.serviceCurrent,
          size: 10,
          condition: condition,
          // 按orderValue排序
          ascs: 'orderValue'
        });

        if (res.data && res.data.records) {
          // 追加服务列表
          if (this.serviceCurrent === 1) {
            this.currentServices = res.data.records;
          } else {
            this.currentServices = [...this.currentServices, ...res.data.records];
          }

          // 判断是否加载完毕
          if (res.data.records.length < 10) {
            this.serviceLoadEnd = true;
          }

          // 缓存该分类的服务列表
          if (this.serviceCurrent === 1) {
            const cacheKey = `${this.currentCategoryId}_${condition.providerId}_${city || ''}`;
            this.categoryServicesCache[cacheKey] = {
              services: [...this.currentServices],
              loadEnd: this.serviceLoadEnd
            };
          }
        }
      } catch (error) {
        console.error('获取服务列表失败:', error);
        uni.showToast({
          title: '获取服务失败',
          icon: 'none'
        });
      } finally {
        this.isLoading = false;
      }
    },

    // 加载更多服务
    loadMoreServices() {
      if (!this.serviceLoadEnd) {
        this.serviceCurrent++;
        this.getServicesByCategory();
      }
    }
  }
}
</script>

<style lang="scss">
.confirm{
position: fixed;
bottom: 0;
width: 750rpx;
height: 796rpx;
background: #F4F6FA;
border-radius: 24rpx 24rpx 0rpx 0rpx;
padding: 32rpx 32rpx 88rpx 32rpx;
box-sizing: border-box;

.popup-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #1D2029;
  margin-bottom: 24rpx;
  text-align: center;
}

.headerTab{
  width: 686rpx;
  height: 192rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;

  .serverIcon{
    width: 144rpx;
    height: 144rpx;
    background: #D8D8D8;
    border-radius: 12rpx;
    border: 1rpx solid #D9DBE0;
    margin-right: 20rpx;
  }
  .serviceName{
    font-weight: 500;
    font-size: 32rpx;
    color: #1D2029;
    .signal{
      font-weight: 400;
      font-size: 22rpx;
      color: #FF5500;
    }
    .serverNum{
      font-weight: 500;
      font-size: 36rpx;
      color: #FF5500;
    }
    .tag{
      font-weight: 400;
      font-size: 20rpx;
      color: #868C9C;
    }
  }
  .changeServer{
    width: 148rpx;
    height: 52rpx;
    background: #FFFFFF;
    border-radius: 36rpx;
    border: 1rpx solid #D9DBE0;
    margin-left: auto;
  }
}

// 分类服务模式的样式
.category-service-container {
  display: flex;
  height: 680rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;

  .category-scroll {
    width: 220rpx;
    height: 100%;

    .loading-item {
      height: 90rpx;
      line-height: 90rpx;
      font-size: 28rpx;
      color: #999;
      text-align: center;
      padding: 0 24rpx;
    }

    .category-item {
      height: 90rpx;
      line-height: 90rpx;
      font-size: 28rpx;
      color: #666;
      text-align: left;
      padding: 0 24rpx;
      border-bottom: 1rpx solid #EAEAEA;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &.active {
        background: #FFFFFF;
        color: #00B484;
        font-weight: 500;
        position: relative;

        &:before {
          content: "";
          position: absolute;
          left: 0;
          top: 30rpx;
          height: 30rpx;
          width: 6rpx;
          background: #00B484;
          border-radius: 0 3rpx 3rpx 0;
        }
      }
    }
  }

  .service-scroll {
    flex: 1;
    height: 100%;
    background: #FFFFFF;

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200rpx;
      padding-top: 100rpx;

      .loading-spinner {
        width: 40rpx;
        height: 40rpx;
        border: 4rpx solid #f3f3f3;
        border-top: 4rpx solid #00B484;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      .loading-text {
        margin-top: 16rpx;
        font-size: 26rpx;
        color: #999;
      }
    }

    .service-item {
      display: flex;
      padding: 20rpx;
      border-bottom: 1rpx solid #E6E6E6;

      .service-icon {
        width: 120rpx;
        height: 120rpx;
        border-radius: 12rpx;
        border: 1rpx solid #D9DBE0;
        margin-right: 20rpx;
      }

      .service-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        text-align: left;

        .service-name {
          font-size: 28rpx;
          color: #333;
          margin-bottom: 10rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          text-align: left;
        }

        .service-price {
          text-align: left;

          .price-symbol {
            font-size: 22rpx;
            color: #FF5500;
          }

          .price-value {
            font-size: 32rpx;
            font-weight: 500;
            color: #FF5500;
          }

          .price-unit {
            font-size: 22rpx;
            color: #999;
          }
        }
      }
    }

    .loading-tip, .empty-tip {
      text-align: center;
      color: #999;
      font-size: 24rpx;
      padding: 20rpx 0;
    }
  }
}
}

.scroll-Y{
height: 600rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
