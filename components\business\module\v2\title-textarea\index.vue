
<template>
  <view class="title-textarea clear-float"  style="border-bottom: none;border-top: none;" :class="{'bdt':defaultConfig.bdt,'bdb':defaultConfig.bdb}" :style="defaultConfig.style">
    <view class="l-l" :style="{'color': defaultConfig.titleColor}" v-if="defaultConfig.showLabel">
      <text class="star" v-if="defaultConfig.required && !defaultConfig.iconurl && !defaultConfig.nextRequest">*</text>
      <image  :src='defaultConfig.iconurl' class="zxicon" mode="" v-if="defaultConfig.iconurl"></image>
      {{defaultConfig.label}}
      <text class="star" v-if="defaultConfig.required && !defaultConfig.iconurl && defaultConfig.nextRequest">*</text>
    </view>
    <view class="l-r" :style="defaultConfig.inputStyle">
      <textarea class="textarea"  :placeholder="placeholder ? placeholder : `请输入${defaultConfig.label}`" :disabled="disabled"
        :value="value" @input="inputChange" @blur="returnFn" :focus="autofocus" auto-height  :maxlength="defaultConfig.maxlength"
      />
    </view>
  </view>
</template>

<script>
// v1 版title-textarea 的优化版 用法
// <title-textarea v-model="form.previousHistory" :config="config.previousHistory" />


export default {
  data() {
    return {
      autofocus:false,
      array: [],
      index: 0,
      defaultConfig: {
        style: {},
        bdt: true,
        bdb: true,
        titleColor: '#333',
        textColor: '#333',
        label: '多行输入框',
        name: 'input',
        required: false,
        showLabel: true,
        inputStyle: {},
        maxlength: 340
      }
    }
  },
  watch: {
    config: {
      handler(val) {
        this.copyConfig()
      },
      deep: true
    },
  },
  props: {
    value: [String,Number],
    placeholder: String,
    // 是否禁用disable
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    }
  },
  mounted() {
      this.copyConfig()
  },
  methods: {
    inputChange(e){
      this.$emit('input',e.detail.value)
    },
    toFocus(){
      this.autofocus = true;
    },
    /**
       * 初始化拷贝config对象
       */
    copyConfig() {
      const that = this
      const obj = that.config
      console.log('obj',obj)
      Object.keys(obj).forEach(function(key) {
        that.defaultConfig[key] = obj[key]
      })
    },

    /**
       * picker触发选中事件
       * @param e
       */
    returnFn(e) {
      const that = this
      if (that.disabled) return
      that.$emit('updateForm', { key: '' + that.config.name, value: that.form.data.val })
    }
  }
}
</script>

<style lang="scss" scoped>
  .zxicon{
    width: 44upx;
    height: 39upx;
    vertical-align: middle;
    margin-left: 6upx;
    margin-right: 12upx;
  }
  .title-textarea{
    background-color:#ffffff;
    .l-l{
      line-height: 88upx;
      color: #333333;

      font-weight: 500;
      font-size:30upx;
    }
    .l-r{
      margin-bottom: 5px;
      background: #fafafa;
      .textarea {
        min-height: 162upx;
        width: 665upx;
        padding: 18upx 17upx;
        box-sizing: border-box;
        color: #333;
        font-size: 32upx;
        text-align: left;
        display: inline-block;
        vertical-align: middle;
        border-radius: 0;
        font-size: 28upx;
      }
    }
  }
  .star{
    color: #F85E4C;
    padding-top: 6upx;
    font-size: 32upx;
    display: inline-block;
    margin: 0 10upx;
  }
</style>
