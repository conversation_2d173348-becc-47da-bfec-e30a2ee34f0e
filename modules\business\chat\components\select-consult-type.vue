<template>
    <view class="fixed-box">
        <view id="departmentPicker" class="fixed-content-box">
            <view class="content-top">
                <view>
                    <text class="content-top-text" v-if="nodeConfig.nextEnabled === 2" @tap="skip">跳过</text>
                </view>
                <button class="btn" type="primary" size="mini" @tap="confirm">确认</button>
            </view>
            <l-picker-view v-if="!$validate.isNull(consultTypeTags)" v-model="value" :config="{label: 'codeName', value: 'id'}" :list="consultTypeTags" />
            <view style="height:500rpx;position:relative;" v-else>
                <loading-layer :config="{zIndex: 10}" :cData="true" :position="'absolute'"></loading-layer>
            </view>
        </view>
    </view>
</template>

<script>
import lPickerView from './l-picker-view'
import { mapState } from 'vuex'
import HandleConsult from '@/service/ext/modules/websocket/receive/HandleConsult'
export default {
    components: {
        lPickerView
    },
    data () {
        return {
            // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
            $constant: this.$constant,
            $common: this.$common,
            $accurateConversion: this.$accurateConversion,
            file_ctx: this.file_ctx,
            $static_ctx: this.$static_ctx,
            $timePlugin: this.$timePlugin,
            $validate: this.$validate,
            value: [0, 0],
            defaultAvatar: this.file_ctx + 'static/image/system/avatar/icon-default-avatar.png',
        }
    },
    created () {
        this.getOfficeIdList()
    },
    mounted () {
        // 获取高度
        // #ifdef H5
        let getDiv = document.getElementById('departmentPicker')
        // #endif

        // #ifndef H5
        const query = uni.createSelectorQuery().in(this)
        let getDiv = query.select('#departmentPicker')
        // #endif
        this.getEl(getDiv).then(data => {
            this.$common.setKeyVal('chat', 'bottomBoxHeight', data.height)
        })
    },
    computed: {
        ...mapState('chat', {
            ws: state => state.ws,
            chatItem: state => state.chatItem,
            nodereplyconfig: state => state.nodereplyconfig,
            consultTypeTags: state => state.consultTypeTags,
            orderDetail: state => state.orderDetail
        }),
        // 选择科室节点配置
        nodeConfig() {
            return this.nodereplyconfig.find(item => item.pushType === 7)
        }
    },
    methods: {
        // 跳过
        skip () {
            // 之前已经选择过科室，直接跳过 重新触发资讯节点流程
            if (this.orderDetail.gfConsultType) {
                new HandleConsult(this.$ext.webSocket).processMessage()
            } else {
                // 更新订单详情
                const data =  {
                    cmd: this.$constant.chat.CONSULT_CMD,
                    data: {
                        orderId: this.chatItem.orderId,
                        userId: this.chatItem.userId,
                        gfConsultType: "",
                        nodeConfigId: this.nodeConfig.id,
                        nodeConfigReplyContent: ""
                    }
                }
                this.$ext.webSocket.webSocketSend(this.$constant.chat.CONSULT_CMD, data)
            }
        },
        // 发送
        confirm () {
            const [one] = this.value
            const nodeConfigReplyContent = {
                value: this.consultTypeTags[one].id,
                label: this.consultTypeTags[one].codeName
            }
            // 更新订单详情
            const data =  {
                cmd: this.$constant.chat.CONSULT_CMD,
                data: {
                    orderId: this.chatItem.orderId,
                    userId: this.chatItem.userId,
                    gfConsultType: this.consultTypeTags[one].id,
                    nodeConfigId: this.nodeConfig.id,
                    nodeConfigReplyContent: JSON.stringify(nodeConfigReplyContent)
                }
            }
            this.$ext.webSocket.webSocketSend(this.$constant.chat.CONSULT_CMD, data)
            const msgContent = this.consultTypeTags[one].codeName

            let time = new Date().getTime()
            // 发送科室引导语消息
            const msgDto = {
                cmd: this.$constant.chat.SINGLE_CHAT_CMD,
                data: {
                    msgType: 1,
                    msgContent: '',
                    content: msgContent,
                    orderId: this.chatItem.orderId,
                    seatUserId: "",
                    touchType: this.$constant.chat.touchType.consultTypeGuide,
                    createTime: time
                }
            }
            this.$ext.webSocket.webSocketSend(this.$constant.chat.SINGLE_CHAT_CMD, msgDto)

            const listItem = {
                hasBeenSentId: time + '-' + this.chatItem.userId, //已发送过去消息的id
                content: msgContent,
                fromUserHeadImg: this.defaultAvatar, //用户头像
                fromUserId: this.chatItem.userId,
                isItMe: true,
                createTime: time,
                contentType: 1, // 1文字文本 2语音
                msgType: 1, // 1文字文本 2图片
                msgCloudStatus: 2, //消息发送结果状态，1是正常，2是发送中，待回调，3是等待回调失败
            }

            // this.messageList.push(listItem);
            const messageList = this.$common.getKeyVal('chat', 'messageList', false)
            this.$common.setKeyVal('chat', 'messageList', [...messageList, listItem])

            this.$nextTick(() => {
                uni.pageScrollTo({
                    scrollTop: 99999,
                    duration: 0, //小程序如果有滚动效果 input的焦点也会随着页面滚动...
                });
            });

        },
        // 异步获取元素
        getEl (getDiv) {
            return new Promise((resolve, reject) => {
                // #ifdef H5
                resolve(getDiv.getBoundingClientRect())
                // #endif

                // #ifndef H5
                if (getDiv.boundingClientRect) {
                    getDiv.boundingClientRect(data => {
                        resolve(data)
                    }).exec()
                }
                // #endif
            })
        },
    }
}
</script>

<style lang="scss" scoped>
.fixed-box {
    position: fixed;
    z-index: 999;
    bottom: 0;
    width: 100%;
	height: 500upx;
	font-size: 28upx;
    background: #fff;

    .btn {
        margin-right: 20rpx;
        background-color: #4cd964;
        border: none;
        height: 74upx;
        line-height: 74upx;
    }

    .fixed-content-box {
        text-align: right;
    }
    .content-top {
        display: flex;
        justify-content: space-between;
        padding: 24upx 20upx 0;
        &-text {
            color: $topicC;
            font-size: 32upx;
            line-height: 42upx;
        }
    }
}
</style>
