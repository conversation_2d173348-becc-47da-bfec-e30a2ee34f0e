<template>
  <page>
    <view slot="content">
      <view class="main">
        <linzq-citySelect :position="regionInfo.city ? regionInfo.city : ''" @back_city="backCity"></linzq-citySelect>
      </view>
    </view>
  </page>
</template>

<script>
  import { mapState } from 'vuex'
  import LinzqCitySelect from '@/modules/common/components/city-select/linzq-citySelect/linzq-citySelect'

  export default {
    components: {
      LinzqCitySelect
    },
    data() {
      return {
        regionInfo: {}
      }
    },
    onLoad(paramsObj) {
      const query = this.$Route.query
      if (!this.$validate.isNull(query)) {}
      this.init()
    },
    methods: {
      init() {
        this.$nextTick(() => {
          const regionInfo = this.$common.getKeyVal('system', 'longitudeAndLatitude', true)
          if (regionInfo) {
            if (typeof regionInfo === 'object' && JSON.stringify(regionInfo) !== '{}') {
              this.regionInfo = regionInfo
            } else {
              this.regionInfo = {}
            }
          } else {
            this.regionInfo = {}
          }
        })
      },
      backCity(e) {
        if (e !== 'no') {
          this.regionInfo.city = e.cityName
          this.$common.setKeyVal('system', 'longitudeAndLatitude', this.regionInfo, true)
          this.$navto.back(1)
        } else {
          this.$navto.back(1)
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .m-t-40{
    margin-top:40upx;
  }
  .m-b-20{
    margin-bottom: 20upx;
  }
</style>
