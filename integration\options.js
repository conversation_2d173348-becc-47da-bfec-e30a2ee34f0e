import calabashApis from "@/modules/common/api/calabash.js"
import common from '@/common/util/main'
// 获取任务列表
export const taskList = [];
// 定义任务api接口映射哈希表
export const taskAPIsOrRouterMaps = [
  // 删除帖子
  {type:'api',path:'postmessage/delete/one',eventType:-1,id:null,idKey:'id',isRequest:false,getId:'invoke'},
  // 删除帖子评论
  {type:'api',path:'comment/delete/one',eventType:-3,id:null,idKey:'id',isRequest:false,getId:'invoke'},
  // 进入极速咨询
  // {type:'route',path:'modules/business/chat/index',eventType:1,isRequest:false},
  // 进行诊后点评
  // {type:'api',path:'/formtemplatewrite/formWrite',eventType:2,isRequest:false},
  // 进入健康自测
  // {type:'route',path:'modules/activity/health-testing/index',eventType:3,isRequest:false},
  // 完成健康自测
  {type:'api',path:'/casecollectsubmitlog/casecollect/formwrite',eventType:4,id:null,idKey:'id',isRequest:false,getId:'success'},
  // 订阅健康自测
  {type:'api',path:'subscribe/get/one/by/business/account',eventType:5,id:null,idKey:'id',isRequest:false},
  // 分享健康自测
  {type:'route',shareTask:true,path:'modules/activity/health-testing/testing-detail',eventType:6,id:null,isRequest:false},
  // 进入用药提醒
  {type:'route',path:'modules/pharmacy/pharmacy-cyclopedia/pharmacy-remind/index',eventType:7,id:null,isRequest:false},
  // 创建用药提醒
  {type:'api',path:'medicineremind/insert',eventType:8,id:null,isRequest:false},
  // 进入祈福许愿
  {type:'route',path:'modules/activity/calabash/index',eventType:9,id:null,isRequest:false},
  // 进行祈福许愿
  {type:'api',path:'/pointwishbarrage/insert',eventType:10,id:null,isRequest:false},
  // 进入葫芦直播
  {type:'route',path:'modules/directseeding/video-list/index',eventType:11,id:null,isRequest:false},
  // 观看直播
  {type:'api',path:'/meeting/query/one',eventType:12,id:null,idKey:'id',isRequest:false,getId:'invoke'},
  // 观看回放
  {type:'api',path:'get/playback/by/meetingid',eventType:13,id:null,idKey:'meetingId',isRequest:false,getId:'invoke'},
  // 预约直播
  {type:'api',path:'commoncollectlikes/add/or/update',eventType:14,id:null,idKey:'data',isRequest:false,getId:'success'},
  // 分享直播/回放
  {type:'route',shareTask:true,path:'modules/directseeding/shortVideo/list/index',eventType:15,id:null,isRequest:false},
  // 直播发表评论
  {type:'api',path:'/meetingchat/insert',eventType:16,id:null,idKey:'id',isRequest:false,getId:'success'},
  // 浏览帖子
  {type:'api',path:'/postmessage/query/one',eventType:17,id:null,idKey:'id',isRequest:false,getId:'success',isInvitation:true,invitationOptions:null,judging:(data)=>data.data.materialType == 1,currentApi:false},
  // 观看视频
  {type:'api',path:'/postmessage/query/one',eventType:18,id:null,idKey:'id',isRequest:false,getId:'success',isInvitation:true,invitationOptions:null,judging:(data)=>data.data.materialType == 2,currentApi:false},
  // 点赞帖子
  {type:'api',path:'/postmessage/add/like',eventType:19,id:null,idKey:'id',isRequest:false,getId:'invoke',isInvitation:true,invitationOptions:null},
  // 收藏帖子
  {type:'api',path:'/postmessage/add/collection',eventType:20,id:null,idKey:'id',isRequest:false,getId:'invoke',isInvitation:true,invitationOptions:null},
  // 分享帖子
  {type:'api',path:'/applicationoperatelog/share',eventType:21,id:null,idKey:'id',isRequest:false,getId:'invoke',isInvitation:true,invitationOptions:null},
  // 发表评论
  {type:'api',path:'/comment/post/message/comment',eventType:22,id:null,idKey:'id',isRequest:false,getId:'success',isInvitation:true,invitationOptions:null},
  // 发布帖子
  {type:'api',path:'/postmessage/app/insert',eventType:23,id:null,idKey:'id',isRequest:false,getId:'invoke',successGetId:true,isInvitation:true,invitationOptions:null,judging:(data)=>!JSON.parse(data.data).videosPath,currentApi:false},
  // 发布视频
  {type:'api',path:'/postmessage/app/insert',eventType:24,id:null,idKey:'id',isRequest:false,getId:'invoke',successGetId:true,isInvitation:true,invitationOptions:null,judging:(data)=>!!JSON.parse(data.data).videosPath,currentApi:false},
  // 添加家庭就诊人
  {type:'api',path:'/patientinfo/insert',eventType:25,id:null,idKey:'id',isRequest:false,getId:'invoke'},
  // 完成每日辟谣问答
  {type:'api',path:'/refuterumorrecord/insert',eventType:26,businessType:1,id:null,idKey:'id',isRequest:false,getId:'success',isInvitation:true,invitationOptions:null,judging:(data)=>data.data,currentApi:false},
  // 签到积分完成每日辟谣问答
  {type:'api',path:'/refuterumorrecord/insert',eventType:27,businessType:2,id:null,idKey:'id',isRequest:false,getId:'success',isInvitation:true,invitationOptions:null,judging:(data)=>data.data,currentApi:false},
]
// 判定当前请求的接口里是否为哈希表里的某一项
export function isPathInInterfaceTable(url,type = 'api'){
  return taskAPIsOrRouterMaps.filter((pathOptions,index)=>{
          if(pathOptions.type !== type) return false
          if(url.indexOf(pathOptions.path)>=0)return true
        })
}
// 刷新任务列表
export function reloadTaskList(accountId,key){
    calabashApis.pointtaskQueryPointList({accountId}).then(e=>{
      taskList.length = 0;
      taskList.push(...e.data.taskList)
      console.log('e.data.taskList',taskList);
    })
}

// 任务完成通知
export async function pointtasklogHandle(Options,patchOptions = {},IntegrationData){
  let {businessType,eventType,accountId,classifyId} = Options.taskList[0]
  let queryOptions = {businessType,eventType,accountId,platform:1};
  if(classifyId){
    patchOptions.relateId = classifyId
  }
  let {nickName} = common.getKeyVal('user', 'fansRecord')
  const codeUserInfo = common.getKeyVal('user', 'codeUserInfo',true)
  let {data} = await calabashApis.pointtasklogHandle({...queryOptions,...patchOptions,username:nickName,userId:codeUserInfo.id})
  if(Array.isArray(data) && data.filter(e=>e.point > 0).length){
    let point = 0;
    data.map(e=>(e.point > point) && (point = e.point))
    uni.hideToast();
    // 默认完成任务后都打开积分弹窗
    IntegrationData.codePopupFlag = true;
    setTimeout(()=>{
      uni.showToast({title:`完成任务,获取${point}福币`,icon:'none'})
    },500)
  }
  // 刷新任务列表
  await reloadTaskList(accountId,2)
}
// 获取当前路由
export function getCurrentPath(){
  let pages = getCurrentPages();
  return pages[pages.length - 1].$page.fullPath;
}
