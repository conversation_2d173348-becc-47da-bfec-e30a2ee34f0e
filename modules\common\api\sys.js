import request from '@/common/util/request'
import env from '@/config/env'

/**
 * 系统请求工具类，
 * 注意：API接口只做请求数据和格式化数据的事情，其他事务一概不处理
 */
export default {
  /**
   * 发送短信验证码
   *
   *  const params = {
        phone: this.regForm.phone, // 用户填写的手机号
        code: this.regForm.code, // 随机编码
        unitId: '99' // 单位id
      }
   * @param param
   * @param resolve
   * @param reject
   */
  sendSms(param, resolve, reject) {
    const url = env.ctx + 'basics/api/v1/sms/send/sms'
    request.postForm(url, param).then(res => {
      if (res) {
        if (res.code === 0) {
          resolve(res)
        } else {
          reject(res)
        }
      } else {
        reject(res)
      }
    }).catch(error => {
      reject(error)
    })
  },
  /**
   * 验证旧手机号
   *
   *  const params = {
        phone: this.regForm.phone, // 用户填写的手机号
        code: this.regForm.code, // 随机编码
        captcha: '0' // 验证码
      }
   * @param param
   * @param resolve
   * @param reject
   */
  appUserCheckphone(param) {
    const url = env.ctx + 'auth/api/v1/user/before/update/phone'
    return request.postJson(url, param)
  },
  /**
   * 更换手机号
   *
   *  const params = {
        phone: this.regForm.phone, // 用户填写的手机号
        code: this.regForm.code, // 随机编码
        captcha: '0' // 验证码
      }
   * @param param
   * @param resolve
   * @param reject
   */
  userChangephone(param) {
    const url = env.ctx + 'auth/api/v1/user/update/phone'
    return request.postJson(url, param)
  },
  // 菜单列表新增
  sysuserentryInsertList(param) {
    const url = env.ctx + 'auth/api/v1/sysuserentry/insert/list'
    return request.postJson(url, param)
  },
  // 菜单列表查询
  sysuserentryQueryList(param) {
    const url = env.ctx + 'auth/api/v1/sysuserentry/query/list'
    return request.get(url, param)
  },
  // 添加渠道链访问记录
  minichannellinklogVisit (param) {
    const url = env.ctx + 'dm/api/v1/minichannellinklog/visit'
    return request.postJson(url, param)
  },
  // 查询渠道链插入参数
  minichannellinkQueryOne (param) {
    const url = env.ctx + 'dm/api/v1/minichannellink/query/one/code'
    return request.get(url, param)
  },
  // 前端错误日志数据
  logInsertClientErrorLog (param) {
    const url = env.ctx + 'manage/api/log/insertClientErrorLog'
    return request.postJson(url, param)
  }
}
