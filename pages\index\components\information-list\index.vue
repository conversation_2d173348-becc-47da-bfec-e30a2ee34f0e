<!--最新消息-->
<template>
  <view class="upcoming-remind">
    <view class="content clear-float">
      <view class="wait-to-be-done-list">
        <nui-list :indexlist="list" :posts-params="postsParams" :isShowGambit="true" @cateClick="cateClick"></nui-list>
        <view class="main-empty" v-if="list.length === 0">
          <view class="middle">
            <em class="icon-text-no-data"></em>
            <text>
              {{$constant.noun.emptyDataPrompt}}
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import InformationItem from '../information-item/index'
import nuiList from '@/components/community/nui-list/nui-list.vue'
const launchOptions = uni.getLaunchOptionsSync()
export default {
  name: 'Index',
  components: {
    InformationItem,
    nuiList
  },
  props: {
    index: Number,
    list: { // 数据列表
      type: Array,
      default() {
        return []
      }
    },
    postsParams: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      itemList:[],
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      isLoadingLayer: true
    }
  },
  computed: {
    ...mapState('user', {
      curSelectStore: state => state.curSelectStore, // 当前选中的租户
      codeUserInfo: state => state.codeUserInfo, // 当前登录用户信息
      curSelectUserInfo: state => state.curSelectUserInfo, // 当前选中的用户信息
      curSelectUserInfoId: state => state.curSelectUserInfoId, // 当前选中的档案的ID
      accountId: state => state.accountId
    }),
    ...mapState('system', {
      backlogData: state => state.backlogData
    })
  },
  mounted() {
    // this.init()
  },
  methods: {
    cateClick (data) {
      if(launchOptions.scene === 1154) return this.$uniPlugin.toast('请前往小程序使用完整服务') 
      this.navtoGo('Circle', {cid: data.circleClassifyId})
    },
    async init(params) {
      console.log(params)
      const that = this
      const param = {
        current: 1,
        size: 10,
        condition: {
          // terminalType: 1
          accountId: this.accountId
        }
      }
      that.isLoadingLayer = true
      let res = {}
      if (this.index === 0) {
        res = await that.$ext.community.postmessageQueryRecommendPage(param)
      } else if (this.index === 1) {
        res = await that.$ext.community.postmessageQueryPhysicianHotPage(param)
      }

      if (res && res.data.records) {
        let list = res.data.records || []
        that.itemList = list
      }
      that.isLoadingLayer = false
    },
    jumpGo(item) {

    },
    navtoGo(url = '', obj = {}) {
      this.$navto.push(url, obj)
    }
  }
}
</script>

<style lang="scss" scoped>
.color-border-blue{
  color: #187FFF;
  border: 2upx solid #187FFF;
}
.color-border-red{
  color: #FF4A4A;
  border: 2upx solid #FF4A4A;
}
.upcoming-remind{
  .content{
    padding:0upx 0 30upx;
    background: #fff;
    .wait-to-be-done-list{
      min-height: 400upx;
      position: relative;
      .li{
        background: #fff;
        margin-bottom: 20upx;
        border-bottom: 2upx solid $contentDdt;
        &:last-of-type{
          margin-bottom: 0;
          border-bottom: none;
        }
        .li-view {
          .t{
            margin-bottom: 16upx;
            .l{
              display: inline-block;
              vertical-align: middle;
              width: calc(100% - 160upx);
              font-size: 32upx;
              line-height: 48upx;
              .t-l-l{
                display: inline-block;
                vertical-align: middle;
                margin-right: 16upx;
              }
              .t-l-r{
                display: inline-block;
                vertical-align: middle;
                font-size: 24upx;
                line-height: 36upx;
                padding: 0 16upx;
                @include rounded(6upx);
              }
            }
            .r{
              display: inline-block;
              vertical-align: middle;
              text-align: right;
              width: 160upx;
            }
          }
          .time-text{
            font-size: 24upx;
            line-height: 36upx;
            color: #666;
            margin-bottom: 16upx;
          }
          .text-999-24 {
            font-size: 24upx;
            line-height: 36upx;
            color: #999;
          }
        }
      }
    }
  }
}

.main-empty{
  min-height: 400upx;
  height: 100%;
  position: relative;
  background: #fff;
  .middle{
    width: 100%;
    position: absolute;
    left: 0;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    .icon-text-no-data{
      margin: 0 auto;
      display: block !important;
      @include iconImg(200,200,'/system/invalid/icon-text-no-data.png');
    }
    text{
      text-align: center;
      color: #999;
      font-size: 28upx;
      line-height: 36upx;
      display: inline-block;
      width: 100%;
    }
  }
}
</style>
