<template>
  <view class="main">
    <view style="flex: 1;" class="main-content">
      <scroll-refresh
        :fixed="false"
        :up="upOption"
        :down="downOption"
        :no-page="true"
        :zPageDefault="{
          loadingMoreEnabled: false,
        }"
        :isShowEmptySwitch="false"
        @returnFn="returnFn"
        @scrollInit="scrollInit"
      >
        <view class="main-content-box">
          <view
            class="item"
            v-for="item in list"
            :key="item.id"
            hover-class="message-hover-class"
            @click="navtoGo('Circle', {cid: item.id})"
          >
            <view class="avatar-box">
              <image
                class="avatar"
                mode="aspectFill"
                :src="item.logoPath || defaultAvatar"
              ></image>
            </view>
            <view class="user-box-content">
              <text class="user-name">{{ item.name }}</text>
            </view>
            <button type="primary" size="mini" class="btn" @click.stop="unSubscribe(item)">
              取消关注
            </button>
          </view>
        </view>
        <view class="empty-box" slot="empty">
          <image
            class="empty-img"
            mode="aspectFill"
            :src="file_ctx + 'static/image/business/hulu-v2/icon-circle-empty.png'"
          ></image>
          <text class="empty-text">暂无更多圈子</text>
        </view>
      </scroll-refresh>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
export default {
  props: {
    params: {
      type: Object,
      default: () => {
        return null
      }
    }
  },
  options: { styleIsolation: 'shared' },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      $validate: this.$validate,
      mescroll: null, // mescroll实例对象
      downOption: {
        auto: false // 不自动加载
      },
      upOption: {
        auto: false // 不自动加载
      },
      isInit: false, // 列表是否已经初始化
      list: [],
      defaultAvatar: this.file_ctx + 'static/image/system/avatar/icon-default-v2.png'
    }
  },
  computed: {
      ...mapState('user', {
          accountId: state => state.accountId
      })
  },
  methods: {
    // 取消关注圈子
    unSubscribe (circleInfo) {
      this.$uniPlugin.modal('','确认取消关注该圈子？', {
        showCancel: true, // 是否显示取消按钮，默认为 true
        cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
        cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
        confirmText: '确定', //  确定按钮的文字，默认为"确定"，最多 4 个字符
        confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
        fn: async (n) => {
          if(n) {
            const param = {
              accountId: this.accountId,
              circleClassifyIds: [circleInfo.id]
            }
            this.$api.circleclassify.circleclassifyUnsubscribe(param).then(res => {
              this.$uniPlugin.toast('取消关注成功')
              this.init()
            })
          }
        }
      })
    },
    init(val) {
      this.mescroll.triggerDownScroll()
    },
    scrollInit(scroll) {
      scroll.optUp.page.num = 1
      scroll.optUp.page.size = 10
      this.mescroll = scroll
    },
    returnFn(obj) {
      const that = this
      setTimeout(function () {
        that.$ext.circleclassify.curCircleclassifyQuerySubscribeList().then(res => {
          const data = res.data || []
          that.list = data.map(item => {
            return {
              ...item,
              logoPath: that.$common.getHeadImage(item.logoPath)
            }
          })
          obj.successCallback && obj.successCallback(data || [])
        })
      }, that.$constant.noun.scrollRefreshTime)
    },
    navtoGo(url, obj = {}) {
      this.$navto.push(url, obj)
    }
  }
}
</script>

<style lang="scss" scoped>
@import './list.scss';
</style>