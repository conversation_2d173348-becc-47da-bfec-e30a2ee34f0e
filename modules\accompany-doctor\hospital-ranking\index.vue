<template>
  <page>
    <view slot="content" class="body-main">
        <!-- <div class="mask" v-if="currentActive"></div> -->
        <view class="header" v-if="!isShow">
          <h3>全国医院排行榜</h3>
          <p>复旦大学最新版中国医院排行榜</p>
        </view>
        <view class="hospital-content">
          <!-- <view class="country">
            <view class="country-item" @click="handleClickActive">
              {{ hospitalListText }}<span :class="{'active':currentActive}"></span>
            </view>
              <template v-if="currentActive">
                <lvCascadeSelect
                  class="select-item"
                  isAbsolute
                  :list="cityOptions"
                  useName='label'
                  :value='selectAddress'
                  :selectedKey='selectAddressValue'
                  @change="handleClick"
                >
                </lvCascadeSelect>
              </template>
          </view> -->
          <view class="m-main-body">
            <scroll-refresh class="scroll-refresh-main" :isShowEmptySwitch="true" :fixed="false" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
              <view class="hospital-item" v-for="item in indexlist" :key="item.id">
                <view class="item-l">
                  <image mode="aspectFit" :src="item.logo"></image>
                </view>
                <view class="item-r" @click="handleClickJump(item.id)">
                  <view class="item-title-l">
                    <view class="title">{{ item.hospitalName }}</view>
                    <view class="item-type">{{ item.type }}</view>
                    <view class="ranking" v-if="!isShow">综合排名：{{ item.top }}</view>
                    <view class="item-desc">{{ item.reason }}</view>
                    <view class="item-address">
                      <view class="info-img"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-hospital-address.png'"></image></view>
                      <view class="address-text">{{ item.address }}</view>
                    </view>
                  </view>
                  <!-- <view class="item-title-r"><uni-icons :size="20" color="#666" type="right" /></view> -->
                </view>
              </view>
            </scroll-refresh>
          </view>
        </view>
    </view>
  </page>
</template>

<script>
  import UniIcons from '@/components/uni/uni-icons/uni-icons'
  // import lvCascadeSelect from '@/modules/activity/components/lv-cascade-select/index'
  import scrollRefresh from '@/components/uni/zPaging/index'
  import page from '@/components/basics/frame/page'
  import { isDomainUrl } from '@/utils/index.js'
  export default {
  components: {
    UniIcons,
    scrollRefresh,
    page
    // lvCascadeSelect
  },
    data(){
      return{
        $constant: this.$constant,
        file_ctx:this.file_ctx,
        currentActive:false,
        hospitalList:[],
        cityOptions:[],
        isAbsolute:true,
        selectAddress:[],
        selectAddressValue:'',
        hospitalListText:'全国',
        mescroll: null, // mescroll实例对象
        downOption: {
          auto: false // 不自动加载
        },
        upOption: {
          auto: false, // 不自动加载
          empty: {
            top: 0,
            zIndex: 999,
          },
          onScroll: true
        },
        indexlist:[],
        isShow:null,
        cityName:null,
      }
    },
    onLoad(option){
      const query = this.$validate.isNull(this.$Route.query) ? option : this.$Route.query
      if(query?.isShow){
        this.isShow = query.isShow
      } else {
        this.isShow = false
      }
      if(query?.cityName){
        this.cityName = query.cityName
      }
      const that = this
      // that.getAddressList()
      that.$nextTick(() => {
        that.init()
      })
    },
    methods:{
      returnFn(obj) {
        const that = this
        setTimeout(function () {
          let params = {
            current: obj.pageNum,
            size: obj.pageSize,
            condition: {
              city: that.cityName,
            }
          }
          that.$api.hospital.hospitalQueryPage(params).then(res => {
            console.log(res,'res25555')
            let data = res.data.records.map(item=>({...item,logo:isDomainUrl(item.logo)})) || []
            if (obj.pageNum === 1) {
              that.indexlist = []
            }
            that.indexlist = [...that.indexlist, ...data]
            obj.successCallback && obj.successCallback(data)
          })
        }, that.$constant.noun.scrollRefreshTime)

      },

      scrollInit(scroll) {
        scroll.optUp.page.num = 1
        scroll.optUp.page.size = 10
        this.mescroll = scroll
      },
      init() {
        this.$nextTick(() => {
          this.mescroll.triggerDownScroll()
        })
      },

      // 获取地址
      async getAddressList(){
        const res = await this.$api.community.arealistArea();
        if(res.code === 0){
          this.$common.setKeyVal('address', 'addressData', res.data, true)
          this.cityOptions = res.data
        }
        console.log('res',res)
      },

      // 点击了全国的箭头
      // handleClickActive(){
      //   this.currentActive = !this.currentActive
      // },

      // 跳转详情
      handleClickJump(id){
        console.log(123)
        this.$navto.push('HospitalDetail', {id,isAccompany:true})
      },

      // 选择医院的省市区
      handleClick(obj){
        console.log(obj,'obj')
        const { close,select,selectLabel} = obj;
        if(close){
          // this.selectAddress = [
          //   ...select
          // ]
          // this.selectAddressLabel = selectLabel;
          this.selectAddressValue = select[2]
          this.selectAddress = selectLabel[2]
          console.log(this.selectAddress,'this.selectAddress')
          this.hospitalListText = selectLabel[2]
          this.currentActive = false


        }
      }
    },
 }
</script>
<style lang='scss' scoped>
.img{
  width: 100%;
  height: 100%;
}
.body-main{
    height: 100%;
    background-color: #fff;
  .mask{
    background: rgba(0,0,0,.65);
    position: absolute;
    z-index: 99;
    width: 100%;
    height: 100%;
  }
  .header{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding:20upx 0 50upx;
    h3{
      font-size: 40upx;
      font-weight: bold;
      color:#000;
    }
    p{
      margin-top: 10upx;
      color:#6e7880;
    }
  }
  .hospital-content{
    height: 100%;
    .country{
      position: relative;
      padding-left:20upx;
      .country-item{
        display: flex;
        align-items: center;
        color:rgb(0, 200, 200);
        font-size: 32upx;
      }
      .select-item{
        position: absolute;
        left: 0;
        right: 0;
        z-index:999;
      }
      span{
        display: inline-block;
        vertical-align: top;
        line-height: 0;
        width: 0;
        height: 0;
        margin-left: 10upx;
        border-style: solid;
        border-color: rgb(0, 200, 200) transparent transparent;
        border-width: 5px 4px 0px;
      }
      .active{
        transform: rotate(180deg);
      }
    }
    .m-main-body{
      height: 100%;
      .scroll-refresh-main{
        height: 100%;
        /deep/ .mescroll-uni{
          .z-paging-content{
            background-color: #f5f5f5 !important;
            padding:0 20upx;
            width:calc(100% - 40upx);
          }
        }
        .hospital-item{
          display: flex;
          align-items: center;
          border-radius: 20upx;
          padding:20upx;
          margin-top: 20upx;
          background-color: #fff;
          .item-l{
            width: 120upx;
            height: 120upx;
            image{
              width: 100%;
              height: 100%;
            }
          }
          .item-r{
            display: flex;
            flex: 1;
            justify-content: space-between;
            // align-items: center;
            .item-title-l{
              margin-left: 30upx;
              .title{
                color:#000;
                font-size: 32upx;
              }
              .ranking{
                font-size: 30upx;
              }
              .item-type{
                padding: 2rpx 10rpx;
                border: 1rpx solid #c6c6c6;
                font-size: 22rpx;
                width: fit-content;
                border-radius: 24rpx;
                margin: 4rpx 0;
              }
              .item-desc{
                color:#000;
                overflow:hidden;
                text-overflow:ellipsis;
                display:-webkit-box; //将对象作为弹性伸缩盒子模型显示
                -webkit-box-orient:vertical;//从上到下垂直排列子元素
                -webkit-line-clamp:1; //这个属性不是css的规范属性，需要组合上面两个属性，表示显示的行数
                margin: 4rpx 0;
              }
              .item-address{
                display: flex;
                align-items: center;
                font-size: 22rpx;
                color: #4E5569;
                line-height: 32rpx;
                .info-img{
                  display: flex;
                  width: 24rpx;
                  height: 24rpx;
                }
                .address-text{
                  width: 438rpx;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  margin-left: 8rpx;
                }
              }
            }
            .item-title-r{
              display: flex;
              flex-direction: column;
              align-items: center;
              width: 60upx;
            }
          }
        }
      }
    }

  }
}
</style>
