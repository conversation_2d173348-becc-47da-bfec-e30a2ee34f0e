<template>
  <view>
    <page>
      <view slot="content" class="content">
        <!-- <view class="main-search">
          <search placeholder="搜索" :fixed="false" top="74" v-model="regForm.search" @changeSearch="changeSearch"></search>
        </view> -->
        <!-- tab菜单-->
        <tabs-sticky v-model="curIndex" :fixed="false" :tabs="tabs" @change="changeTab"></tabs-sticky>
        <view class="main">
            <swiper class="swiper" :current="curIndex" @change="swiperChange">
                <!-- 全部 -->
                <swiper-item>
                    <order-list ref="oneCom" :index="curIndex" :params="tabs[0]" :i="0" />
                </swiper-item>

                <!-- 未接诊 -->
                <swiper-item>
                    <order-list ref="twoCom" :index="curIndex" :params="tabs[1]" :i="1" />
                </swiper-item>

                <!-- 已结束 -->
                <swiper-item>
                    <order-list ref="threeCom" :index="curIndex" :params="tabs[2]" :i="2" />
                </swiper-item>
            </swiper>
        </view>
      </view>
    </page>
  </view>

</template>

<script>
// import MescrollUni from '@/components/uni/mescroll-uni'
import search from '@/components/basics/form/search'
import TabsSticky from '@/components/basics/tabs-sticky'
// import orderAll from './order-all.vue'
// import orderConsult from './order-consult.vue'
// import orderEnd from './order-end.vue'
import orderList from './order-list.vue'

import {mapState} from 'vuex'
export default {
  components: {
    // MescrollUni,
    search,
    TabsSticky,
    // orderAll,
    // orderConsult,
    // orderEnd,
    orderList
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      $validate: this.$validate,
      mescroll: null, // mescroll实例对象
      downOption: {
        auto: false // 不自动加载
      },
      upOption: {
        auto: false // 不自动加载
      },
      isInit: false, // 列表是否已经初始化
      scrollY: 0,
      regForm: {
        search: ''
      },
      itemList:[
      ],
      isListEmpty: false,
      curIndex: 0, // 当前菜单下标,
      tabs: [{ name: '全部', consultStatus: '' }, { name: '未接诊', consultStatus: 1 }, { name: '已结束', consultStatus: 3 }]
    }
  },
  watch:{
    "$store.state.chat.chatlist":{
        handler:function(newVal,oldVal){
            if (this.$validate.isNull(newVal)) {
                this.isListEmpty = true
            } else {
                this.isListEmpty = false
            }
        },
        immediate: true,
        deep: true
    },
    // 监听下标的变化
    curIndex(val) {
      // this.$common.setKeyVal('chat', 'chatListIndex', this.curIndex, false)
      this.init()
    }
  },
  onShow(){
    // alert("1111")
    // this.itemList = this.$common.getKeyVal('chat', 'chatlist',false)
    // console.log("1",itemList)
    // this.$forceUpdate();
  },
  computed: {
    ...mapState('chat', {
      ws: state => state.ws,
      chatlist: state => state.chatlist,
      chatListIndex: state => state.chatListIndex
    }),
    ...mapState('user', {
      curSelectUserInfo: state => state.curSelectUserInfo // 当前登录用户信息
    })
  },
  onLoad(paramsObj) {
    const query = this.$Route.query
    if (!this.$validate.isNull(query)) {
    }
    // #ifdef MP-WEIXIN
    // wx.showShareMenu({
    //   withShareTicket:true,
    //   //设置下方的Menus菜单，才能够让发送给朋友与分享到朋友圈两个按钮可以点击
    //   menus:["shareAppMessage","shareTimeline"]
    // })
    // #endif
    // #ifdef MP-WEIXIN
    this.handleClickTrack('OperationDetailsPageView')
    // #endif
  },
  onUnload(){
    // #ifdef MP-WEIXIN
    this.handleClickTrack('EndOperationDetailsPageView')
    // #endif
  },
  mounted() {
    this.init()
  },
  methods: {
    // #ifdef MP-WEIXIN
    handleClickTrack(type){
      let pages = getCurrentPages()
      let current = pages[pages.length - 1]; // 获取到当前页面的信息
      let pageInfo = __wxConfig.page && __wxConfig.page[`${current.route}.html`] || ''; // 内部属性
      getApp().globalData.sensors.track(type,
        {
          'page_name' : pageInfo?.window?.navigationBarTitleText || '',
          'first_operation_name' : '功能使用',
          'second_operation_name' : '我的咨询',
        }
      ) 
    },
    // #endif
    // 轮播菜单
    swiperChange(e) {
        this.changeTab(e.detail.current)
    },
    changeTab(index) {
        this.curIndex = index
    },
    navtoGo(url = '', obj = {}) {
        this.$navto.push(url, obj)
    },
    init(val) {
        this.$nextTick(() => {
            this.isInit = true // 标记为true
            this.regForm.search = ''
            
            const curIndex = this.curIndex
            this.changeTab(curIndex)
            if (curIndex === 0) {
                this.$refs.oneCom.init(curIndex)
            } else if (curIndex === 1) {
                this.$refs.twoCom.init(curIndex)
            } else if (curIndex === 2) {
                this.$refs.threeCom.init(curIndex)
            }
        })
    },
    changeSearch(obj) {
        this.regForm.search = obj.name
        this.init()
    },
  }
}
</script>
<style lang="scss" scoped>
.content {
    height: 100%;
    display: flex;
    flex-direction: column;
}
.un-read{
  background-color: red;
  font-size: 20upx;
  border-radius: 20upx;
  text-align: center;
  padding: 0 10upx 0 10upx;
  margin: auto;
  position: absolute;
  left: 15%;
  top: 10%;
  color: white;
}
.main-container{
  background-color:#f7f7f7;
  height: 100%;
}
.main-search{
  background-color:#FFFFFF;
}
.main-content{
  padding:0upx 16upx 0upx 16upx;
  background: white;
}
.item {
  position: relative;
  width: 100%;
  height: 172upx;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f4f6f8;
  // padding: 20rpx;
  image {
    width: 108rpx;
    height: 108rpx;
    margin: 20rpx;
    border-radius: 12rpx;
    //flex: 0 0 76rpx;
  }
  .right {
    overflow: hidden;
    flex: 1 0;
    padding: 20rpx 20rpx 20rpx 0;
    &_top {
      display: flex;
      justify-content: space-between;
      &_name {
        display: flex;
        justify-content: space-between;
        font-size: 28rpx;
        color: #303133;
        font-weight: 550;
        //flex: 0 0 450rpx;
        overflow: hidden;
        &_desc {
          font-size: 24upx;
          color: #ff9800;
          margin-left: 8upx;
          overflow: hidden;
          border: 2upx solid #ff9800;
          border-radius: 6upx;
          padding: 0 4upx 0 4upx;
          left: 0;
        }
      }
      &_time {
        font-size: 22rpx;
        color: #909399;
      }
    }
    &_center {
      display: flex;
      justify-content: space-between;
      &_name {
        font-size: 24rpx;
        color: #2196f3;
        flex: 0 0 450rpx;
        overflow: hidden;
        em{
          color: #e5e5e5;
          margin: 0 10upx 0 10upx;
        }
      }
    }
    &_btm {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 22rpx;
      color: #909399;
      padding-top: 10rpx;

      .u-line-1 {
        @include ellipsis(1);
      }
    }
  }
}
.code{
  padding: 16upx 16upx 16upx 16upx;
  overflow: hidden;
  background: #FFFFFF;
  /*margin-bottom: 20upx;*/
  height: 40upx;
  line-height: 40upx;
  position: relative;
  .code-num{
    display:inline-block;
    vertical-align: middle;
    margin-right: 8upx;
    @include iconImg(45,45,'/system/icon-sys-new.png');
  }
  text{
    width: calc(100% - 100upx);
    font-size: 28upx;
    color: #333333;
    line-height: 48upx;
    display: inline-block;
    vertical-align: middle;
  }

  .jump{
    position: absolute;
    right: 20upx;
    display: inline-block;
    vertical-align: middle;
    margin-left: 12upx;
    @include iconImg(45, 45, '/business/icon-more.png');
  }
  .jump-text{
    position: absolute;
    right: 70upx;
    font-size: 32upx;
    color: #999999;
    line-height: 48upx;
    display: inline-block;
    vertical-align: middle;
  }
}
.slot-wrap {
  display: flex;
  align-items: center;
  padding: 0 30rpx;
}
.right_center_name {
  display: flex;
  flex-direction: row;
}
.main{
    flex: 1;
    .swiper{
        height: 100%;
    }
}
</style>
