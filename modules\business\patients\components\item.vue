<template>
  <view>
    <!-- @tap="navtoGo('PatientsAdd', {id: itemA.id})" -->
    <view class="portrait-list">
      <view
        class="li"
        v-for="(itemA,indexA) in pdList"
        :key="indexA"

        @tap="navtoGo('PatientsAddTep', {id: itemA.id})">
        <view class="img">
          <image mode="aspectFit" :src="itemA.headPath ? file_ctx + itemA.headPath : $static_ctx + 'image/system/avatar/icon-default-avatar.png'"  class="role-image"/>
        </view>
        <view class="content">
          <view class="title">
            {{itemA.name || '暂无名称'}}
            <view class="title-tag" v-if="recordUserInfo.id == itemA.id">主账号</view>
          </view>
          <view class="footer">
            <view class="time">{{ itemA.genderText }} {{ itemA.age ? itemA.age + '岁' : '' }}</view>
          </view>
        </view>
        <view class="r">
          <image class="width-height-atuo" :src="$static_ctx + 'image/business/icon-more.png'"/>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import { mapState } from "vuex"
export default {
  components: {

  },
  props: {
    // 参数设置
    pdList: {
      type: Array,
      required: false,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      $validate: this.$validate,
    }
  },
  computed: {
    ...mapState('user', {
      recordUserInfo: state => state.recordUserInfo
    }),
  },
  watch: {
    // 监听下标的变化
  },
  onLoad(paramsObj) {
    const query = this.$Route.query
    if (!this.$validate.isNull(query)) {
    }
  },
  mounted() {
  },
  methods: {
    navtoGo(url = '', obj = {}) {
      this.$navto.push(url, obj)
    }
  }
}
</script>
<style lang="scss" scoped>
  .portrait-list{
      /*margin-top: 10upx;*/
      background-color: #fff;
      .li{
        position: relative;
        @include rounded(20upx);
        background-color:#FFFFFF;
        padding: 24upx;
        // margin-bottom: 24upx;
        border-bottom: 1px solid #f4f6f8;
        .img{
          width: 160upx;
          height: 120upx;
          display: inline-block;
          vertical-align: middle;
          @include rounded(20upx);
          .role-image{
            width: 100%;
            height: 100%;
          }
        }
        .content{
          height: 120upx;
          position: relative;
          width: calc(100% - 266upx);
          padding-left: 24upx;
          display: inline-block;
          vertical-align: middle;
          .title{
            font-size: 32upx;
            color: #333333;
            display: flex;
            align-items: center;
            font-weight: 600;
            /*line-height: 40upx;*/
            @include ellipsis(2);
            /*margin-bottom: 16upx;*/
            line-height: 42upx;
            height: 42upx;
          }
          .title-tag{
            font-size: 24upx;
            display: inline-block;
            // height: 42upx;
            // line-height: 42upx;
            text-align: center;
            @include rounded(24upx);
            //background:linear-gradient(90deg,rgba(255,185,36,1),rgba(254,219,55,1));
            background: $topicC;
            color: #fff;
            padding: 0 20upx;
            margin-left: 12upx;
          }
          .footer{
            position: absolute;
            width: 100%;
            bottom: 0;
            .time{
              color: #999999;
              font-size: 24upx;
              display: inline-block;
            }
            .btn{
              display: inline-block;
              right: 0;
              top: 0;
              width: 108upx;
              height: 40upx;
              line-height: 40upx;
              font-size: 24upx;
            }
          }
        }
        .r{
          height: 72upx;
          width: 72upx;
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          .width-height-atuo{
            width: 100%;
            height: 100%;
          }
        }
      }
    }
</style>
