<template>
  <view class="form-component">
    <view class="form-fields">
      <template v-for="(field, index) in formFields">
        <!-- 文本输入字段 -->
        <view v-if="field.type === 'input'" :key="'input-'+index" class="botTabLine">
          <view class="lineTitle"><span v-if="field.required" class="required">*</span>{{field.label}}</view>
          <input
            :type="field.inputType || 'text'"
            :placeholder="field.placeholder"
            class="bookInput"
            :value="value[field.name]"
            @input="(e) => handleInputChange(field.name, e)"
          />
        </view>
        
        <!-- 形象照上传 -->
        <view v-else-if="field.type === 'image'" :key="'image-'+index" class="botTabLine avatar-line">
          <view class="lineTitle"><span v-if="field.required" class="required">*</span>{{field.label}}</view>
          <view class="upload-box" @click="$refs.avatarUploader.uploadImage()">
            <view v-if="value[field.name]" class="preview-container">
              <image class="preview-image" :src="getImageUrl(value[field.name])" mode="aspectFill"/>
            </view>
            <view v-else class="empty-upload">
              <view class="plus-icon"></view>
            </view>
          </view>
        </view>
        
        <!-- 选择器字段 -->
        <picker v-else-if="field.type === 'picker' && field.mode === 'selector'" :key="'picker-'+index" :mode="field.mode" :range="field.range" @change="(e) => handlePickerChange(field.name, e, field.range)">
          <view class="botTabLine">
            <view class="lineTitle"><span v-if="field.required" class="required">*</span>{{field.label}}</view>
            <view class="lineValue">{{value[field.name] || field.placeholder}}</view>
            <image class="lineIcon" :src="iconRightArrow || ''" mode="aspectFit"></image>
          </view>
        </picker>
        
        <!-- 地区选择器 -->
        <picker v-else-if="field.type === 'picker' && field.mode === 'region'" :key="'region-'+index" mode="region" @change="(e) => handleRegionChange(field, e)" level="city">
          <view class="botTabLine">
            <view class="lineTitle"><span v-if="field.required" class="required">*</span>{{field.label}}</view>
            <view class="lineValue">{{getRegionDisplayValue(field) || field.placeholder}}</view>
            <image class="lineIcon" :src="iconRightArrow || ''" mode="aspectFit"></image>
          </view>
        </picker>
        
        <!-- 身份证上传区域 -->
        <view v-else-if="field.type === 'idcard'" :key="'idcard-'+index" class="additionalContent">
          <view class="lineTitle"><span v-if="field.required" class="required">*</span>{{field.label}}</view>
          <view class="idcard-upload-container">
            <!-- 人像面 -->
            <view class="idcard-item" v-if="value[field.frontField]" @click="$refs.idCardFrontUploader.previewImage(0)">
              <image class="idcard-delete" @click.stop="$refs.idCardFrontUploader.del(0)" :src="iconPostMenuClose || ''" mode="aspectFit"></image>
              <image class="idcard-image" :src="getImageUrl(value[field.frontField])"></image>
            </view>
            <view class="idcard-item empty-idcard" v-else @click="$refs.idCardFrontUploader.uploadImage()">
              <image class="idcard-icon" :src="field.frontIcon || IDCardFace" mode="aspectFit"></image>
              <view class="idcard-text">{{field.frontText || '人像面'}}</view>
            </view>
            
            <!-- 国徽面 -->
            <view class="idcard-item" v-if="value[field.backField]" @click="$refs.idCardBackUploader.previewImage(0)">
              <image class="idcard-delete" @click.stop="$refs.idCardBackUploader.del(0)" :src="iconPostMenuClose || ''" mode="aspectFit"></image>
              <image class="idcard-image" :src="getImageUrl(value[field.backField])"></image>
            </view>
            <view class="idcard-item empty-idcard" v-else @click="$refs.idCardBackUploader.uploadImage()">
              <image class="idcard-icon" :src="field.backIcon || IDCardNationalEmblemFace" mode="aspectFit"></image>
              <view class="idcard-text">{{field.backText || '国徽面'}}</view>
            </view>
          </view>
        </view>
        
        <!-- 文本域 -->
        <view v-else-if="field.type === 'textarea'" :key="'textarea-'+index" class="additionalContent">
          <view class="lineTitle"><span v-if="field.required" class="required">*</span>{{field.label}}</view>
          <textarea
            class="experience-input"
            :placeholder="field.placeholder"
            :value="value[field.name]"
            @input="(e) => handleInputChange(field.name, e)"
            :maxlength="field.maxlength"
          ></textarea>
          <text v-if="field.showCounter" class="word-counter">{{ (value[field.name] || '').length }}/{{ field.maxlength }}</text>
        </view>
        
        <!-- 开关 -->
        <view v-else-if="field.type === 'switch'" :key="'switch-'+index" class="botTabLine switch-line">
          <view class="lineTitle"><span v-if="field.required" class="required">*</span>{{field.label}}</view>
          <view class="switch-wrapper">
            <switch 
              :checked="value[field.name]" 
              :color="field.color || '#07C160'"
              @change="(e) => handleSwitchChange(field.name, e)" 
              class="custom-switch"
            />
          </view>
        </view>

        <!-- 自定义插槽 -->
        <view v-else-if="field.type === 'slot'" :key="'slot-'+index" class="custom-slot-container">
          <view class="lineTitle" v-if="field.label"><span v-if="field.required" class="required">*</span>{{field.label}}</view>
          <!-- 这里直接使用具名插槽，小程序环境下嵌套插槽可能存在兼容性问题 -->
          <slot :name="field.slotName || 'default'"></slot>
        </view>
      </template>
    </view>
    
    <!-- 底部按钮和协议区域 -->
    <view v-if="showBottomArea" class="bottomBtn">
      <view v-if="agreementText" class="agreement-row" @click="toggleAgreement">
        <view class="custom-checkbox">
          <view
            class="checkbox-icon"
            :class="{ checked: isAgreed }"
          ></view>
        </view>
        <text class="agreement-text">我已阅读并同意</text>
        <text class="agreement-link" @tap.stop="handleAgreementClick">{{agreementText}}</text>
      </view>
      <view class="btn-group">
        <view v-if="showResetButton" class="resetBtn" @click="resetForm">{{resetText}}</view>
        <view class="comBtn" @click="submitForm">{{submitText}}</view>
      </view>
    </view>
    
    <!-- 全局隐藏上传组件 -->
    <view class="lineHide">
      <title-img :config="{}" ref="avatarUploader" @returnFn="avatarReturnFn" :cData="[]"></title-img>
    </view>
    <view class="lineHide">
      <title-img :config="{}" ref="idCardFrontUploader" @returnFn="idCardFrontReturnFn" :cData="[]"></title-img>
    </view>
    <view class="lineHide">
      <title-img :config="{}" ref="idCardBackUploader" @returnFn="idCardBackReturnFn" :cData="[]"></title-img>
    </view>
    
    <slot></slot>
  </view>
</template>

<script>
import TitleImg from "@/components/business/module/title-img/index.vue";

export default {
  name: 'AppForm',
  components: {
    TitleImg
  },
  props: {
    // 单一字段模式属性
    type: {
      type: String,
      default: 'text', // text, select, idCard, textarea
      validator: (value) => ['text', 'select', 'idCard', 'textarea'].includes(value)
    },
    label: {
      type: String,
      default: ''
    },
    value: {
      type: [String, Object, Number, Array],
      default: () => ({})
    },
    placeholder: {
      type: String,
      default: '请输入'
    },
    required: {
      type: Boolean,
      default: false
    },
    inputType: {
      type: String,
      default: 'text' // text, number等
    },
    // 图片相关属性
    isAvatar: {
      type: Boolean,
      default: false
    },
    // 下拉选择相关
    isSelect: {
      type: Boolean,
      default: false
    },
    // 地区选择器
    isRegionSelect: {
      type: Boolean,
      default: false
    },
    // 选择器相关配置
    options: {
      type: Array,
      default: () => []
    },
    pickerMode: {
      type: String,
      default: 'selector' // selector, multiSelector等
    },
    // 文本域相关
    isTextarea: {
      type: Boolean,
      default: false
    },
    maxlength: {
      type: Number,
      default: 200
    },
    showCounter: {
      type: Boolean,
      default: true
    },
    // 身份证相关
    isIdCard: {
      type: Boolean,
      default: false
    },
    frontValue: {
      type: [String, Object],
      default: ''
    },
    backValue: {
      type: [String, Object],
      default: ''
    },
    // 上传器配置
    uploaderConfig: {
      type: Object,
      default: () => ({})
    },
    // 上传图片数据
    uploaderData: {
      type: Array,
      default: () => []
    },
    // 身份证正面图片数据
    frontUploaderData: {
      type: Array,
      default: () => []
    },
    // 身份证背面图片数据
    backUploaderData: {
      type: Array,
      default: () => []
    },
    
    // 表单字段配置（新增）
    formFields: {
      type: Array,
      default: () => []
    },
    // 表单验证规则
    validRules: {
      type: Object,
      default: () => ({})
    },
    // 静态资源路径
    staticCtx: {
      type: String,
      default: ''
    },
    // 协议文本
    agreementText: {
      type: String,
      default: ''
    },
    // 提交按钮文本
    submitText: {
      type: String,
      default: '提交'
    },
    // 是否显示重置按钮
    showResetButton: {
      type: Boolean,
      default: false
    },
    // 重置按钮文本
    resetText: {
      type: String,
      default: '重置'
    },
    // 是否显示底部按钮区域
    showBottomArea: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      iconRightArrow: '',
      iconPostMenuClose: '',
      IDCardFace: '',
      IDCardNationalEmblemFace: '',
      isAgreed: false // 是否同意协议
    }
  },
  created() {
    // 在created生命周期中安全地加载图片资源
    try {
      this.iconRightArrow = (this.staticCtx || this.$static_ctx) + "image/business/hulu-v2/icon-right-arrow.png";
      this.iconPostMenuClose = (this.staticCtx || this.$static_ctx) + "image/business/hulu-v2/icon-close.png";
      this.IDCardFace = (this.staticCtx || this.$static_ctx) + "image/business/hulu-v2/IDCardFace.png";
      this.IDCardNationalEmblemFace = (this.staticCtx || this.$static_ctx) + "image/business/hulu-v2/IDCardNationalEmblemFace.png";
    } catch (e) {
      console.error('加载图片资源失败:', e);
    }
  },
  computed: {
    countLength() {
      return this.value ? String(this.value).length : 0;
    }
  },
  methods: {
    // 获取地区显示值
    getRegionDisplayValue(field) {
      const province = this.value[field.provinceField];
      const city = this.value[field.cityField];
      
      if (province && city) {
        // 如果省市名称相同（直辖市），只显示一个
        if (province === city || province.includes(city) || city.includes(province)) {
          return city;
        }
        return `${province}: ${city}`;
      }
      
      return '';
    },
    
    // 选择器变化
    handlePickerChange(fieldName, e, range) {
      const index = e.detail.value;
      const selectedValue = range[index];
      this.$emit('change', e);
      this.emitInputEvent(fieldName, selectedValue);
    },
    
    // 地区选择器变化
    handleRegionChange(field, e) {
      this.$emit('change', e);
      
      const cityArray = e.detail.value;
      if (cityArray && cityArray.length >= 2) {
        // 只保留省份和城市，不保留区县
        const [province, city] = cityArray;
        
        // 更新省市字段
        if (field.provinceField) {
          this.emitInputEvent(field.provinceField, province);
        }
        if (field.cityField) {
          this.emitInputEvent(field.cityField, city);
        }
      }
    },
    
    // 开关变化
    handleSwitchChange(fieldName, e) {
      this.emitInputEvent(fieldName, e.detail.value);
    },
    
    // 安全获取图片URL
    getImageUrl(value) {
      if (!value) return '';
      try {
        if (typeof value === 'object') {
          // 如果是对象，则优先使用url字段，其次是filePath字段
          return value.url || value.filePath || '';
        }
        return value || '';
      } catch (e) {
        console.error('处理图片URL错误:', e);
        return '';
      }
    },
    
    // 输入处理
    handleInput(e) {
      this.$emit('input', e.detail.value);
    },
    
    // 字段输入处理
    handleInputChange(fieldName, e) {
      this.emitInputEvent(fieldName, e.detail.value);
    },
    
    // 统一的值更新事件
    emitInputEvent(fieldName, value) {
      const updatedValue = {...this.value, [fieldName]: value};
      this.$emit('input', updatedValue);
    },
    
    // 切换协议选中状态
    toggleAgreement() {
      this.isAgreed = !this.isAgreed;
    },
    
    // 协议点击事件
    handleAgreementClick() {
      this.$emit('agreement-click');
    },
    
    // 验证表单
    validateForm() {
      const errors = [];
      
      // 遍历验证规则进行验证
      Object.entries(this.validRules).forEach(([field, rule]) => {
        const value = this.value[field];
        
        // 必填项验证
        if (rule.required && !value) {
          errors.push(rule.message);
          return;
        }
        
        // 自定义验证
        if (rule.validator && !rule.validator(value)) {
          errors.push(rule.message);
          return;
        }
        
        // 图片类型验证
        if (rule.type === 'image' && rule.required) {
          if (!value || (typeof value === 'object' && !value.url && !value.filePath)) {
            errors.push(rule.message);
          }
        }
      });
      
      return errors;
    },
    
    // 提交表单
    submitForm() {
      // 验证协议
      if (this.agreementText && !this.isAgreed) {
        uni.showToast({
          title: '请先同意协议',
          icon: 'none'
        });
        return;
      }
      
      // 表单验证
      const errors = this.validateForm();
      if (errors.length > 0) {
        uni.showToast({
          title: errors[0],
          icon: 'none',
          duration: 2000
        });
        return;
      }
      
      // 处理表单数据，提取图片的 filePath
      const processedData = {...this.value};
      
      // 遍历表单字段，处理图片类型
      this.formFields.forEach(field => {
        // 处理普通图片字段
        if (field.type === 'image' && processedData[field.name]) {
          if (typeof processedData[field.name] === 'object') {
            processedData[field.name] = processedData[field.name].filePath || processedData[field.name].url || '';
          }
        }
        
        // 处理身份证图片字段
        if (field.type === 'idcard') {
          if (field.frontField && processedData[field.frontField]) {
            if (typeof processedData[field.frontField] === 'object') {
              processedData[field.frontField] = processedData[field.frontField].filePath || processedData[field.frontField].url || '';
            }
          }
          
          if (field.backField && processedData[field.backField]) {
            if (typeof processedData[field.backField] === 'object') {
              processedData[field.backField] = processedData[field.backField].filePath || processedData[field.backField].url || '';
            }
          }
        }
      });
      
      // 提交表单数据
      this.$emit('submit', processedData);
    },
    
    // 重置表单
    resetForm() {
      // 创建一个新的空白表单数据
      const emptyForm = {};
      
      // 遍历表单字段，将每个字段重置为空
      this.formFields.forEach(field => {
        if (field.type === 'switch') {
          // 开关类型默认为true
          emptyForm[field.name] = true;
        } else if (field.type === 'idcard') {
          // 身份证字段
          emptyForm[field.frontField] = '';
          emptyForm[field.backField] = '';
        } else {
          // 其他字段设为空
          emptyForm[field.name] = '';
        }
      });
      
      // 触发重置事件
      this.$emit('input', emptyForm);
      this.$emit('reset');
    },

    // 头像上传返回处理
    avatarReturnFn(imageList) {
      console.log('头像上传返回:', imageList);

      if (imageList && imageList.length > 0) {
        // 获取当前激活的字段
        const activeField = this.formFields.find(field => field.type === 'image');
        if (activeField && activeField.name) {
          // 直接存储上传返回的对象，在提交时再提取filePath
          this.emitInputEvent(activeField.name, imageList[0] || '');
        }
      }
    },

    // 身份证正面上传返回
    idCardFrontReturnFn(imageList) {
      console.log('身份证人像面上传返回:', imageList);

      if (imageList && imageList.length > 0) {
        // 获取当前激活的身份证字段
        const activeField = this.formFields.find(field => field.type === 'idcard');
        if (activeField && activeField.frontField) {
          // 直接存储上传返回的对象，在提交时再提取filePath
          this.emitInputEvent(activeField.frontField, imageList[0] || '');
        }
      }
    },

    // 身份证背面上传返回
    idCardBackReturnFn(imageList) {
      console.log('身份证国徽面上传返回:', imageList);

      if (imageList && imageList.length > 0) {
        // 获取当前激活的身份证字段
        const activeField = this.formFields.find(field => field.type === 'idcard');
        if (activeField && activeField.backField) {
          // 直接存储上传返回的对象，在提交时再提取filePath
          this.emitInputEvent(activeField.backField, imageList[0] || '');
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.form-component {
  width: 100%;
  padding-bottom: 50rpx; /* 添加底部间距，避免内容被底部按钮遮挡 */
}

.botTabLine {
  width: 100%;
  height: 104rpx;
  line-height: 104rpx;
  border-bottom: 2rpx solid #EAEBF0;
  display: flex;
  align-items: center;

  &.avatar-line {
    height: 150rpx; /* 形象照行的高度设置为150rpx */
    line-height: 150rpx;
  }

  .lineValue {
    flex: 1;
    color: #333;
    font-size: 28rpx;
  }

  .lineIcon {
    width: 32rpx;
    height: 32rpx;
  }

  .bookInput {
    width: 438rpx;
    height: 40rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #1D2029;
  }
}

.lineTitle {
  font-weight: 500;
  font-size: 28rpx;
  color: #1D2029;
  width: 212rpx;

  .required {
    font-weight: 500;
    font-size: 14px;
    color: #FF5500;
  }
}

.upload-box {
  width: 140rpx;
  height: 140rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;

  .preview-image {
    width: 100%;
    height: 100%;
    border-radius: 8rpx;
  }

  .empty-upload {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f4f6fa;
    border-radius: 8rpx;
  }

  .plus-icon {
    position: relative;
    width: 40rpx;
    height: 40rpx;

    &:before, &:after {
      content: '';
      position: absolute;
      background-color: #aaa;
    }

    &:before {
      width: 40rpx;
      height: 4rpx;
      top: 18rpx;
      left: 0;
    }

    &:after {
      width: 4rpx;
      height: 40rpx;
      left: 18rpx;
      top: 0;
    }
  }
}

.preview-container {
  position: relative;
  width: 100%;
  height: 100%;
}

// 文本域相关样式
.experience-input {
  width: 100%;
  height: 240rpx;
  padding: 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  border: 1rpx solid #D9DBE0;
}

.word-counter {
  font-size: 24rpx;
  color: #999;
  text-align: right;
  display: block;
  margin-top: 16rpx;
}

// 额外内容区域样式
.additionalContent {
  margin-top: 32rpx;
}

// 身份证上传区域样式
.idcard-upload-container {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
  width: 100%;
}

.idcard-item {
  width: 48%;
  height: 200rpx;
  background: #F4F6FA;
  border-radius: 16rpx;
  position: relative;
  overflow: hidden;
}

.empty-idcard {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.idcard-icon {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 10rpx;
}

.idcard-text {
  font-size: 28rpx;
  color: #666;
}

.idcard-image {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
}

.idcard-delete {
  width: 30rpx;
  height: 30rpx;
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background: white;
  border-radius: 50%;
  z-index: 9;
}

// 隐藏上传组件样式
.lineHide {
  width: 0;
  overflow: hidden;
  height: 0;
}

// 新增样式
.form-fields {
  width: 100%;
}

.btn-group {
  display: flex;
  justify-content: space-between;
}

.bottomBtn {
  width: 750rpx;
  background: #FFFFFF;
  box-shadow: 0rpx -1rpx 0rpx 0rpx #EAEBF0;
  position: fixed;
  bottom: 0;
  left: 0;
  padding: 24rpx 32rpx;
  box-sizing: border-box;
}

.agreement-row {
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 24rpx;
}

.agreement-text {
  font-size: 14px;
  color: #666;
}

.agreement-link {
  font-size: 14px;
  color: #1687F7;
}

.custom-checkbox {
  display: inline-block;
  vertical-align: middle;
  margin-right: 8px;
  margin-left: 32rpx;
}

.checkbox-icon {
  width: 28rpx;
  height: 28rpx;
  border: 2rpx solid #ccc;
  border-radius: 50%;
  position: relative;
  transition: all 0.3s;
}

.checkbox-icon.checked {
  background: #00B578;
  border-color: #00B578;
}

.checkbox-icon.checked::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  width: 16rpx;
  height: 8rpx;
  border: 4rpx solid #fff;
  border-top: none;
  border-right: none;
  transform: translate(-50%, -60%) rotate(-45deg);
}

.comBtn {
  flex: 1;
  height: 88rpx;
  background: #00B484;
  border-radius: 44rpx;
  text-align: center;
  line-height: 88rpx;
  font-weight: 500;
  font-size: 32rpx;
  color: #FFFFFF;
}

.resetBtn {
  width: 220rpx;
  height: 88rpx;
  background: #F5F7FA;
  border-radius: 44rpx;
  text-align: center;
  line-height: 88rpx;
  font-weight: 500;
  font-size: 32rpx;
  color: #666666;
  margin-right: 24rpx;
}

.custom-slot-container {
  width: 100%;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #EAEBF0;
}

.switch-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 0;
  height: 88rpx;
  line-height: 88rpx;
}

.switch-wrapper {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}

.custom-switch {
  transform: scale(0.8);
}
</style>
