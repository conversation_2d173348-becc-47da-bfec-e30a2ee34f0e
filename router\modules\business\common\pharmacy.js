export default [
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/index',
    name: 'PharmacyCyclopedia',
    meta: {
      index: 2,
      headerObj: {
        title: '用药指南',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/doctor-question/index',
    name: 'DoctorQuest<PERSON>',
    meta: {
      index: 2,
      headerObj: {
        title: '药师问答',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/pharmacy-evaluate/index',
    name: 'PharmacyEvaluate',
    meta: {
      index: 2,
      headerObj: {
        title: '病友分享',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/look-more/index',
    name: 'LookMore',
    meta: {
      index: 2,
      headerObj: {
        title: '查看更多',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/enterprise/index',
    name: 'Enterprise',
    meta: {
      index: 2,
      headerObj: {
        title: '企业介绍',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/drugstore/index',
    name: 'Drugstore',
    meta: {
      index: 2,
      headerObj: {
        title: '附近药店',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/pharmacy-remind/index',
    name: 'PharmacyRemind',
    meta: {
      index: 2,
      headerObj: {
        title: '用药提醒',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/pharmacy-remind/add-page',
    name: 'PharmacyAdd',
    meta: {
      index: 2,
      headerObj: {
        title: '创建提醒',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/pharmacy-remind/pharmacy-detail-page',
    name: 'PharmacyDetail',
    meta: {
      index: 2,
      headerObj: {
        title: '用药详情',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/publish-post/index',
    name: 'PublishPost',
    meta: {
      index: 2,
      headerObj: {
        title: '发帖',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/history/index',
    name: 'History',
    meta: {
      index: 2,
      headerObj: {
        title: '我的说明书',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/history/poster-picture',
    name: 'PosterPicture',
    meta: {
      index: 2,
      headerObj: {
        title: '【红卡】中国专家共识',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/history/free-get-glareme',
    name: 'FreeGetGlareme',
    meta: {
      index: 2,
      headerObj: {
        title: '',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/electronic-book/index',
    name: 'ElectronicBook',
    meta: {
      index: 2,
      headerObj: {
        title: '电子说明书',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/electronic-book/detail',
    name: 'ElectronicDetail',
    meta: {
      index: 2,
      headerObj: {
        title: '药品说明书',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/everyday-rumour/index',
    name: 'EverydayRumour',
    meta: {
      index: 2,
      headerObj: {
        title: '每日辟谣',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/everyday-rumour/more-rumour',
    name: 'EverydayMoreRumour',
    meta: {
      index: 2,
      headerObj: {
        title: '更多辟谣',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/everyday-rumour/poster',
    name: 'EverydayMorePoster',
    meta: {
      index: 2,
      headerObj: {
        title: '每日辟谣海报',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/web-white-video/index',
    name: 'WebWhiteVideo',
    meta: {
      index: 2,
      headerObj: {
        title: '视频播放',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/everyday-rumour/free-get-poster',
    name: 'FreeGetPoster',
    meta: {
      index: 2,
      headerObj: {
        title: '红曲茶海报',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/everyday-rumour/rumour-ranking-list',
    name: 'RumourRankingList',
    meta: {
      index: 2,
      headerObj: {
        title: '辟谣排行榜',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/everyday-rumour/rumour-rule',
    name: 'RumourRule',
    meta: {
      index: 2,
      headerObj: {
        title: '辟谣规则',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/everyday-rumour/history-topic-analysis',
    name: 'HistoryTopicAnalysis',
    meta: {
      index: 2,
      headerObj: {
        title: '历史题目解析',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/everyday-rumour/activity-prefecture',
    name: 'ActivityPrefecture',
    meta: {
      index: 2,
      headerObj: {
        title: '活动专区',
        isShow: true
      }
    }
  }
]