import request from '@/common/util/request'
import env from '@/config/env'

/**
 * 袋拉拉请求工具类，
 * 注意：API接口只做请求数据和格式化数据的事情，其他事务一概不处理
 */
export default {
  // 出袋
  dllOutBag(data) {
    const url = env.ctx + 'basics/api/dll/outBag'
    return request.postJson(url, data)
  },
  // 获取订单信息
  dllGetOrder(data) {
    const url = env.ctx + 'basics/api/dll/getOrder'
    return request.postJson(url, data)
  },
}
