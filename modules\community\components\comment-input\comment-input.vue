<template>
  <view class="comment-input" id="comment-input">
    <view class="main flex-box">
      <input
        :value="
          target && target.content
            ? target.content
            : target && !$validate.isNull(target.imagePath)
            ? '[图片]'
            : ''
        "
        class="main-input"
        type="text"
        :placeholder="placeholder"
        disabled
        @click="$emit('input')"
      />
      <view class="main-data flex-box">
        <!-- 点赞 start -->
        <view class="item-box flex-box" @tap="like">
          <!-- #ifdef H5 -->
          <view class="wx-open-wrapper" v-if="isJumpWx">
            <wx-open-launch-weapp
              :appid="$appId"
              :path="`modules/community/posts/detail/index?id=${target.id}`"
            >
              <!-- <script type="text/wxtag-template">
                <style>
                    .box {
                        width: 100vw;
                        height: 100vh;
                    }
                </style> -->
                <component :is="'script'" type="text/wxtag-template">
                <div class="box" style="display: flex;flex-direction: column;align-items: center;box-sizing: border-box;">
                  <img
                    v-if="isLike"
                    class="item-icon"
                    style="display: inline-block;width:18px;height:18px;margin-bottom: 1px;"
                    :src="
                      file_ctx + 'static/image/business/hulu-v2/icon-like-active.png'
                    "
                  >
                  <img
                    v-else
                    class="item-icon"
                    style="display: inline-block;width:18px;height:18px;margin-bottom: 1px;"
                    :src="file_ctx + 'static/image/business/hulu-v2/icon-like.png'"
                  >
                  <text class="item-num" style="font-size: 24rpx;color: #1d2029;line-height: 34rpx;">{{ likeNumber }}</text>
                </div>
              <!-- </script> -->
                </component>
            </wx-open-launch-weapp>
          </view>
          <!-- #endif -->
          
          <!-- #ifndef H5 -->
          <image
            v-if="isLike"
            class="item-icon"
            mode="aspectFit"
            :src="
              file_ctx + 'static/image/business/hulu-v2/icon-like-active.png'
            "
          ></image>
          <image
            v-else
            class="item-icon"
            mode="aspectFit"
            :src="file_ctx + 'static/image/business/hulu-v2/icon-like.png'"
          ></image>
          <text class="item-num">{{ likeNumber }}</text>
          <!-- #endif -->
        </view>
        <!-- 点赞 end -->

        <!-- 收藏 start -->
        <view class="item-box flex-box" @tap="collect">
          <!-- #ifdef H5 -->
          <view class="wx-open-wrapper" v-if="isJumpWx">
            <wx-open-launch-weapp
              :appid="$appId"
              :path="`modules/community/posts/detail/index?id=${target.id}`"
            >
              <!-- <script type="text/wxtag-template">
                <style>
                    .box {
                        width: 100vw;
                        height: 100vh;
                    }
                </style>
                <div class="box"></div>
              </script> -->
              <component :is="'script'" type="text/wxtag-template">
                <div class="box" style="display: flex;flex-direction: column;align-items: center;box-sizing: border-box;">
                  <img
                    v-if="isStar"
                    class="item-icon"
                    style="display: inline-block;width:18px;height:18px;margin-bottom: 1px;"
                    :src="
                      file_ctx + 'static/image/business/hulu-v2/icon-star-active.png'
                    "
                  >
                  <img
                    v-else
                    class="item-icon"
                    style="display: inline-block;width:18px;height:18px;margin-bottom: 1px;"
                    :src="file_ctx + 'static/image/business/hulu-v2/icon-star.png'"
                  >
                  <text class="item-num" style="font-size: 24rpx;color: #1d2029;line-height: 34rpx;">{{ starNumber }}</text>
                </div>
              </component>
            </wx-open-launch-weapp>
          </view>
          <!-- #endif -->

          <!-- #ifndef H5 -->
          <image
            v-if="isStar"
            class="item-icon"
            mode="aspectFit"
            :src="
              file_ctx + 'static/image/business/hulu-v2/icon-star-active.png'
            "
          ></image>
          <image
            v-else
            class="item-icon"
            mode="aspectFit"
            :src="file_ctx + 'static/image/business/hulu-v2/icon-star.png'"
          ></image>
          <text class="item-num">{{ starNumber }}</text>
          <!-- #endif -->
        </view>
        <!-- 收藏 end -->
        
        <!-- 评论 start -->
        <view class="item-box flex-box" @tap="comment">
          <!-- #ifdef H5 -->
          <img
            class="item-icon"
            style="display: inline-block;width:18px;height:18px;margin-bottom: 1px;"
            :src="file_ctx + 'static/image/business/hulu-v2/icon-comment.png'"
          >
          <text class="item-num" style="font-size: 24rpx;color: #1d2029;line-height: 34rpx;">{{
            commentNumber > 0 ? commentNumber : "写留言"
          }}</text>
          <!-- #endif -->

          <!-- #ifndef H5 -->
          <image
            mode="aspectFit"
            :src="file_ctx + 'static/image/business/hulu-v2/icon-comment.png'"
            class="item-icon"
          ></image>
          <text class="item-num">{{
            commentNumber > 0 ? commentNumber : "写留言"
          }}</text>
          <!-- #endif -->
        </view>
        <!-- 评论 end -->
        <button
          v-if="
            target && (target.content || !$validate.isNull(target.imagePath))
          "
          class="confirm-btn"
          @click="confirm(target)"
        >
          发送
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import UniIcons from '@/components/uni/uni-icons/uni-icons.vue';
export default {
  components: {
    UniIcons
  },
  data() {
    return {
      file_ctx: this.file_ctx,
      $appId: this.$appId,
      defaultAvatar: this.file_ctx + 'static/image/system/avatar/icon-default-avatar.png',
      launchOptions: uni.getLaunchOptionsSync()
    }
  },
  props: {
    isJumpWx: {
      type: Boolean,
      default: false
    },
    target: {
      type: Object,
      default: function () {
        return {}
      }
    },
    placeholder: {
      type: String,
      default: '@楼主：'
    },
    isLike: Boolean,
    likeNumber: {
      type: Number,
      default: 1600
    },
    isStar: Boolean,
    starNumber: {
      type: Number,
      default: 1600
    },
    shareNumber: {
      type: Number,
      default: 1600
    },
    commentNumber: {
      type: Number,
      default: 0
    },
    postmessageInfo: {
      type: Object,
      default: () => ({})
    }
  },
  mounted() {
    this.getHeight()
  },
  methods: {
    collect() {
      if (this.launchOptions.scene === 1154) return this.$uniPlugin.toast('请前往小程序使用完整服务')
      this.$emit('collect')
    },
    like() {
      if (this.launchOptions.scene === 1154) return this.$uniPlugin.toast('请前往小程序使用完整服务')
      this.$emit('like')
    },
    confirm(target) {
      if (this.launchOptions.scene === 1154) return this.$uniPlugin.toast('请前往小程序使用完整服务')
      this.$emit('confirm', target)
    },
    comment() {
      if (this.launchOptions.scene === 1154) return this.$uniPlugin.toast('请前往小程序使用完整服务')
      if (this.commentNumber > 0) {
        this.$emit('scrollcomment')
      } else {
        this.$emit('input')
      }
    },
    getHeight() {
      // #ifdef H5
      let getDiv = document.getElementById('comment-input')
      // #endif
      // #ifndef H5
      const query = uni.createSelectorQuery().in(this)
      let getDiv = query.select('#comment-input')
      // #endif
      this.getEl(getDiv).then(res => {
        this.$emit('changeHeight', res.height)
      })
    },
    // 异步获取元素信息
    getEl(getDiv) {
      return new Promise((resolve, reject) => {
        // #ifdef H5
        resolve(getDiv.getBoundingClientRect())
        // #endif

        // #ifndef H5
        if (getDiv.boundingClientRect) {
          getDiv.boundingClientRect(data => {
            resolve(data)
          }).exec()
        }
        // #endif
      })
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .share-btn {
  background: none;
  border: none !important;
  outline: none !important;
}
.share-btn::after {
  border: none;
}
.comment-input {
  position: fixed;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  bottom: 0;
  left: 0;
  right: 0;
  // width: 750rpx;
  background: #ffffff;
  box-shadow: -9rpx -6rpx 7rpx 0rpx rgba(205, 205, 205, 0.28);
  z-index: 999;
  box-sizing: border-box;
  // width: 100%;
  .main {
    height: 112rpx;
    padding: 20rpx 32rpx;
    width: 100%;
    justify-content: space-between;
    &-input {
      flex: 1;
      height: 100%;
      background: #F6F6F6;
      border-radius: 32rpx;
      padding-left: 22rpx;
      margin-right: 38rpx;
    }
  }
  .main-data {
    justify-content: space-around;
  }
  .flex-box {
    display: flex;
    align-items: center;
    box-sizing: border-box;
  }
  .paperplane,
  .star,
  .heart {
    height: 100%;
    position: relative;
    justify-content: space-between;
    margin-right: 23rpx;
    &-num {
      font-size: 22rpx;
      font-weight: 400;
      color: #000000;
      line-height: 28rpx;
    }
    .wx-open-wrapper {
      position: absolute;
      top: -18rpx;
      bottom: 0;
      left: 0;
      right: 0;
    }
  }
  .confirm-btn {
    background: #00d29d;
    padding: 0;
    @include rounded(24rpx);
    width: 88rpx;
    height: 56rpx;
    font-size: 24rpx;
    font-weight: 500;
    color: #ffffff;
    line-height: 56rpx;
    &:active {
      opacity: 0.8;
    }
  }
}
.author-box {
  display: flex;
  align-items: center;
}
.author-img-box {
  position: relative;
  width: 64rpx;
  height: 64rpx;
  margin-right: 16rpx;
}
.head-v-icon {
  position: absolute;
  bottom: -2rpx;
  right: -2rpx;
  width: 28rpx;
  height: 28rpx;
}
.author-img {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  overflow: hidden;
}
.author-name {
  font-weight: 500;
  font-size: 28rpx;
  color: #1d2029;
  line-height: 40rpx;
}
.item-box {
  display: flex;
  flex-direction: column;
  align-items: center;

  & + .item-box {
    padding-left: 48rpx;
  }
}
.item-icon {
  width: 36rpx;
  height: 36rpx;
  margin-bottom: 2rpx;
}
.item-num {
  font-size: 24rpx;
  color: #1d2029;
  line-height: 34rpx;
}
</style>
