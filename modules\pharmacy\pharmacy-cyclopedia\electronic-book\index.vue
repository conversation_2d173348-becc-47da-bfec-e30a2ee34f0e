<template>
  <page>
    <view slot="content" class="body-main">
          <view class="my-data">
            <view class="my-bg">
              <view :style="'height:' + statusBarHeight + 'px;'"></view>
              <view class="top-nav">
                <view class="top-nav-l" @click="back"><image mode="aspectFit" :src="file_ctx + 'static/image/business/hulu-v2/icon-circle-left-arrows.png'" class="header-search-img"/></view>
                <view class="top-nav-c">电子说明书</view>
              </view>
              <view class="top-img"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-specification-top.png'"></image></view>
              <view class="electronic-info">
                <view class="title"><view v-if="detailObj.drugName" style="display:flex;font-size: 36rpx;color: #2D2F38;">{{detailObj.drugName}}<view class="img"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-trade-name-r.png'"></image></view> </view>{{detailObj.commonName}}说明书</view>
                <view class="company">{{ detailObj.brandName }}</view>
                <!-- <view class="info" v-if="detailObj.productId == '2031353194431111174' || detailObj.productId == '2031448999104024578'">不同规格、不同生产日期/批次的产品说明书可能因说明书更新和版本更替等原因，内容有所不同。如本页面显示的电子说明书和您所购买的产品包装盒上的说明书存在内容有所不同的情况，请以包装盒上的说明书为准。</view>
                <view class="info" v-else>不同规格、不同生产日期/批次的药品说明书可能因说明书更新和版本更替等原因，内容有所不同。如本页面显示的电子说明书和您所购买的药物药盒中所附的纸质说明书存在内容有所不同的情况，请以药盒中所附的纸质说明书为准。</view> -->
                <view class="info">{{ detailObj.bookTips }}</view>
              </view>
            </view>
          </view>
          <view class="content">
            <view class="content-item" v-for="item in specificationList" :key="item.id" @click="handleClickDetail(item.id)">
              <view class="content-l">
                <view class="title">
                  <view class="img"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-specification-content-title.png'"></image></view>
                  <span>{{ item.specification }}</span>
                </view>
                <view class="date">{{ item.specificationUpdateTime }}</view>
              </view>
              <view class="content-r"><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-specification-content-right.png'"></image></view>

            </view>
          </view>
          <view v-if="detailObj.indexList">
            <view class="look-bott">本网页服务由绿葆提供，相关内容仅供参考<br>不能替代执业医师或药师的意见，请谨慎参阅</view>
          </view>
    </view>
  </page>
</template>

<script>
  import { mapState } from 'vuex'
  export default {
    data(){
      return{
        file_ctx:this.file_ctx,
        statusBarHeight: 0,
        $constant: this.$constant,
        mescroll: null, // mescroll实例对象
        downOption: {
          auto: false // 不自动加载
        },
        upOption: {
          auto: false, // 不自动加载
          empty: {
            top: 0,
            zIndex: 999,
          },
          onScroll: true
        },
        contentList:[],
        specificationList:[], //说明书列表数据
        detailObj:{},//详情对象
      }
    },
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId,
        publishInfo:state => state.publishInfo,
        curSelectUserInfo:state => state.curSelectUserInfo
      }),
    },
    onLoad(option){
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
      const query = this.$validate.isNull(this.$Route.query) ? option : this.$Route.query
      console.log(query,'query55')
      this.detailObj = query
      // this.$nextTick(() => {
      //   this.init()
      // })
      this.getfullSpecificationQueryPage()
    },
    onShareAppMessage: function (res) {  
      if (res.from === 'share') {  
        // 来自页面内转发按钮  
        console.log(res.target);  
      }
      this.queryAndCreate(3)
      return {
        title: `${this.publishInfo.name}${this.detailObj.labelValue}`, //分享的名称
        path: 'modules/pharmacy/pharmacy-cyclopedia/index?gs='+ encodeURIComponent(this.detailObj.gs),
        mpId:this.$appId, //此处配置微信小程序的AppId
      }
    },
    mounted(){},
    methods:{
      async queryAndCreate(type){
        let { phone = '' } =  this.curSelectUserInfo || {}
        let params = {
          accountId:this.accountId,
          phone: phone,
          productId:this.detailObj?.productId,
          shareType:type,  //	分享类型：1分享按钮2保存二维码3微信好友分享按钮
        }
        await this.$api.drugBook.queryAndCreate(params)
      },
      back(){
        this.$navto.back(1)
      },

      handleClickDetail(id){
        this.$navto.push('ElectronicDetail',{id,...this.detailObj})
      },
      // 说明书接口列表
      getfullSpecificationQueryPage(){
        let params = {
          current: 1,
          size: 15,
          condition:{
            productId:this.detailObj.productId
          },
        }
        this.$api.drugBook.getfullSpecificationQueryPage(params).then(res => {
          let data = res.data.records.map(item=>{
            return {
              ...item,
              specificationUpdateTime:item.specificationUpdateTime ? this.$common.formatDate(new Date(item.specificationUpdateTime), 'yyyy-MM-dd') : ''
            }
          })
          this.specificationList = data



        })
      },
    }
 }  
</script>

<style lang='scss' scoped>
.body-main{
  background: #F4F6FA;
  height: 100%;
  .m-main-body{
    height: 100%;
    .scroll-refresh-main{
      height: 100%;
      // .my-data{
      //   position: relative;
      //   .my-bg {
      //     width: 100%;
      //     height: 372rpx;
      //     background: linear-gradient( 180deg, #B5E7D9 0%, #F4F6FA 100%);
      //     .top-nav{
      //       position: fixed;
      //       width: calc(100% - 56rpx);
      //       display: flex;
      //       align-items: center;
      //       justify-content: space-between;
      //       height: 40px;
      //       line-height: 40px;
      //       z-index: 999;
      //       padding: 0 32rpx 0 24rpx;
      //       .top-nav-l{
      //         display: flex;
      //         width: 48rpx;
      //         height: 48rpx;
      //         image{
      //           width: 100%;
      //           height: 100%;
      //         }
      //       }
      //       .top-nav-c{
      //         flex: 1;
      //         text-align: center;
      //         height: 44rpx;
      //         font-weight: 500;
      //         font-size: 32rpx;
      //         color: #2D2F38;
      //         line-height: 44rpx;
      //         margin-right: 48rpx;
      //       }
      //     }
      //     .top-img{
      //       position: absolute;
      //       top: 26rpx;
      //       right: 68rpx;
      //       display: flex;
      //       width: 230rpx;
      //       height: 240rpx;
      //       image{
      //         width: 100%;
      //         height: 100%;
      //       }
      //     }
      //     .electronic-info{
      //       margin:0 32rpx;
      //       padding:32rpx 24rpx;
      //       background: #FFFFFF;
      //       border-radius: 16rpx;
      //       overflow: hidden;
      //       position: absolute;
      //       width: calc(100% - 112rpx);
      //       top: 196rpx;
      //       .title{
      //         display: flex;
      //         height: 50rpx;
      //         font-weight: 600;
      //         font-size: 36rpx;
      //         color: #2D2F38;
      //         line-height: 50rpx;
      //         .img{
      //           display: flex;
      //           width: 28rpx;
      //           height: 28rpx;
      //           margin-right: 2rpx;
      //           image{
      //             width: 100%;
      //             height: 100%;
      //           }
      //         }
      //       }
      //       .company{
      //         height: 40rpx;
      //         font-size: 28rpx;
      //         color: #4E5569;
      //         line-height: 40rpx;
      //         margin: 8rpx 0 20rpx 0;
      //       }
      //       .info{
      //         width: 100%;
      //         font-size: 28rpx;
      //         color: #868C9C;
      //         line-height: 40rpx;
      //       }
      //     }
      //   }
      // }
      // .content{
      //   background: #FFFFFF;
      //   border-radius: 16rpx;
      //   overflow: hidden;
      //   margin: 230rpx 32rpx 0;
      //   padding:0 24rpx;
      //   .content-item{
      //     display: flex;
      //     align-items: center;
      //     justify-content: space-between;
      //     padding:32rpx 0;
      //     border-bottom: 1px solid #EAEBF0;
      //     .content-l{
      //       display: flex;
      //       flex-direction: column;
      //       .title{
      //         display: flex;
      //         .img{
      //           width: 40rpx;
      //           height: 40rpx;
      //           image{
      //             width: 100%;
      //             height: 100%;
      //           }
      //         }
      //         span{
      //           display: inline-block;
      //           margin: 0 0 16rpx 2rpx;
      //           font-weight: 600;
      //           font-size: 32rpx;
      //           color: #2D2F38;
      //           line-height: 44rpx;
      //         }
      //       }
      //       .date{
      //         font-size: 24rpx;
      //         color: #4E5569;
      //         line-height: 34rpx;
      //       }
      //     }
      //     .content-r{
      //       width: 28rpx;
      //       height: 28rpx;
      //       image{
      //         width: 100%;
      //         height: 100%;
      //       }
      //     }
      //     &:last-child{
      //       border-bottom: 0;
      //     }
      //   }
      // }
      // .look-bott{
      //   text-align: center;
      //   color: #cfcfcf;
      //   padding: 32rpx;
      // }
    }
  }
  .my-data{
    position: relative;
    .my-bg {
      width: 100%;
      height: 372rpx;
      background: linear-gradient( 180deg, #B5E7D9 0%, #F4F6FA 100%);
      .top-nav{
        position: fixed;
        width: calc(100% - 56rpx);
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 40px;
        line-height: 40px;
        z-index: 999;
        padding: 0 32rpx 0 24rpx;
        .top-nav-l{
          display: flex;
          width: 48rpx;
          height: 48rpx;
          image{
            width: 100%;
            height: 100%;
          }
        }
        .top-nav-c{
          flex: 1;
          text-align: center;
          height: 44rpx;
          font-weight: 500;
          font-size: 32rpx;
          color: #2D2F38;
          line-height: 44rpx;
          margin-right: 48rpx;
        }
      }
      .top-img{
        position: absolute;
        top: 26rpx;
        right: 68rpx;
        display: flex;
        width: 230rpx;
        height: 240rpx;
        image{
          width: 100%;
          height: 100%;
        }
      }
      .electronic-info{
        margin:0 32rpx;
        padding:32rpx 24rpx;
        background: #FFFFFF;
        border-radius: 16rpx;
        overflow: hidden;
        position: absolute;
        width: calc(100% - 112rpx);
        top: 196rpx;
        .title{
          display: flex;
          height: 50rpx;
          font-weight: 600;
          font-size: 36rpx;
          color: #2D2F38;
          line-height: 50rpx;
          .img{
            display: flex;
            width: 28rpx;
            height: 28rpx;
            margin-right: 2rpx;
            image{
              width: 100%;
              height: 100%;
            }
          }
        }
        .company{
          height: 40rpx;
          font-size: 28rpx;
          color: #4E5569;
          line-height: 40rpx;
          margin: 8rpx 0 20rpx 0;
        }
        .info{
          width: 100%;
          font-size: 28rpx;
          color: #868C9C;
          line-height: 40rpx;
        }
      }
    }
  }
  .content{
    background: #FFFFFF;
    border-radius: 16rpx;
    overflow: hidden;
    margin: 230rpx 32rpx 0;
    padding:0 24rpx;
    .content-item{
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding:32rpx 0;
      border-bottom: 1px solid #EAEBF0;
      .content-l{
        display: flex;
        flex-direction: column;
        .title{
          display: flex;
          .img{
            width: 40rpx;
            height: 40rpx;
            image{
              width: 100%;
              height: 100%;
            }
          }
          span{
            display: inline-block;
            margin: 0 0 16rpx 2rpx;
            font-weight: 600;
            font-size: 32rpx;
            color: #2D2F38;
            line-height: 44rpx;
          }
        }
        .date{
          font-size: 24rpx;
          color: #4E5569;
          line-height: 34rpx;
        }
      }
      .content-r{
        width: 28rpx;
        height: 28rpx;
        image{
          width: 100%;
          height: 100%;
        }
      }
      &:last-child{
        border-bottom: 0;
      }
    }
  }
  .look-bott{
    text-align: center;
    color: #cfcfcf;
    padding: 32rpx;
  }
}
</style>