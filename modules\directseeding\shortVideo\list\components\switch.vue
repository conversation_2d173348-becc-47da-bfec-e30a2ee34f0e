<template>
  <view class="switch" @click="handleClick" :style="{...getSwitchBackage}">
    <view class="switchRound" :style="{...getSwitchRoundStyle}" ></view>
  </view>  
</template>
<script> 
export default {
  props: {
    value: {
      type: [String, Number, Boolean],
      default: false
    },
    // 开关大小
    width: {
      type: [String, Number],
      default: '98rpx'
    },
    // 开关高度
    height: {
      type: [String, Number],
      default: '48rpx'
    },
    // 确认背景颜色
    confirmObj: {
      type: Object,
      default: () => {
        return {
          borderColor: '#00B484',
          roundColor: 'rgba(0, 180, 132, 0.8)',
        }
      }
    },
    // 取消背景颜色
    cancelObj: {
      type: Object,
      default: () => {
        return {
          borderColor: '#777777',
          roundColor: '#FFFFFF',
        }
      }
    }
  },
  computed: {
    getSwitchBackage() {
      let style = {};
      if (this.valueContent) {
        style = {
          border:`4rpx solid ${this.confirmObj.borderColor}`,
          width: this.width,
          height: this.height,
          borderRadius: this.height
        }
      }else {
        style = {
          border:`4rpx solid ${this.cancelObj.borderColor}`,
          width: this.width,
          height: this.height,
          borderRadius: this.height
        }
      }
      return style;
    },
    getSwitchRoundStyle() {
      let style = {};
      if (this.valueContent) {
        style = {
          backgroundColor: this.confirmObj.roundColor,
          boxShadow: `0 0 0 2rpx ${this.confirmObj.borderColor}`,
          width:  '50%',
          height: this.height,
          borderRadius: this.height,
          marginLeft: '50%',
        }
      }else {
        style = {
          backgroundColor: this.cancelObj.roundColor,
          boxShadow: `0 0 0 2rpx ${this.cancelObj.borderColor}`,
          width:  '50%',
          height: this.height,
          borderRadius: this.height,
          marginLeft: '0',
        }
      }
      return style;
    }
  },
  data() {
    return {
      valueContent:false
    }
  },
  mounted() {
    this.valueContent = this.value;
  },
  watch: {
    value(val) {
      this.valueContent = val;
    }
  },
  methods: {
    handleClick() {
      this.valueContent = !this.valueContent;
      this.$emit('input', this.valueContent);
    }
  }
}
</script>
<style lang="scss">
  .switch{
    transition: all 0.3s;
    background: white;
    .switchRound{
      transition: all 0.3s;
      box-sizing: border-box;
    }
  }
</style>