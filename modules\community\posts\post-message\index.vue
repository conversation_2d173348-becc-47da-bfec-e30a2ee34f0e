<template>
  <page>
    <view slot="content" class="body-main">
      <integration-time-er :backTaskFlag='IntegrationData.backTaskFlag'></integration-time-er>
      <view class="my-data">
        <view class="my-bg" :style="{backgroundImage: 'url(' + file_ctx + 'static/image/business/hulu-v2/icon-post-banner.png)'}">
          <!-- #ifdef MP-WEIXIN || H5 -->
          <view>
            <status-bar-height></status-bar-height>
            <view class="top-nav">
              <view class="top-nav-l" @click.stop="handleBack">
                <image mode="aspectFit" :src="file_ctx + 'static/image/business/hulu-v2/icon-circle-left-arrows.png'" class="header-search-img"/>
              </view>
            </view>
          </view>
          <!-- #endif -->
          <view class="my-user" :class="{
            pdl40:infoObj.lightLogoPath
          }">
          </view>
        </view>
      </view>
      <!-- <scroll-view 
        class="my-scroll-view" 
        scroll-y
      > -->
        <view class="form-wrapper">
          <view class="form-box">
            <view class="form-item-list">
              <view class="line-bottom">
                <title-radio
                  :config="config.materialType"
                  :cData="form.materialType"
                  @updateForm="updateForm"
                />
              </view>
              
              <view class="line-bottom">
                <title-input
                  v-model="form.title"
                  :config="config.title"
                  :placeholder="config.title.placeholder"
                  :maxlength="25"
                  :show-word-limit="true"
                  placeholderStyle="color: #A5AAB8;font-size: 32rpx;"
                ></title-input>
              </view>
              <view class="" style="padding-top: 32upx" v-show="config.content.show">
                <title-textarea
                  style="width: 100%"
                  v-model="form.content"
                  :config="config.content"
                  :list="gambitList"
                  :formObj="form"
                  path='postMessageIndex'
                  :showConfirmBar="false"
                  :placeholder="config.content.placeholder"
                  placeholderStyle="color: #A5AAB8;font-size: 28rpx;"
                />
              </view>
              <view class="" style="padding-top: 32upx" v-show="config.intro.show">
                <title-textarea
                  style="width: 100%"
                  v-model="form.intro"
                  :config="config.intro"
                  :list="gambitList"
                  :formObj="form"                  
                  path='postMessageIndex'
                  :showConfirmBar="false"
                  :placeholder="config.intro.placeholder"
                  placeholderStyle="color: #A5AAB8;font-size: 28rpx;"
                />
              </view>
              <view class="flex-box-start">
                  <!-- style="flex: 1;" -->
                <title-img
                  v-show="config.videosPath.show"
                  :config="config.videosPath"
                  fileType="video"
                  @returnFn="
                    (obj) => {
                      imgReturnFn(obj, 'videosPath');
                    }
                  "
                  :cData="cDataVideosPath"
                  style="margin-right: 30upx;"
                >
                  <!-- <view slot="upload-after" class="upload-after"> -->
                    <!-- <text class="upload-tips">上传短视频</text> -->
                  <view slot="upload-after" class="upload-after">
                    <view class="upload-tips">上传视频</view>
                  </view>
                </title-img>
                  <!-- style="flex: 1;" -->
                <title-img
                  :config="config.imagesPath"
                  @returnFn="
                    (obj) => {
                      imgReturnFn(obj, 'imagesPath');
                    }
                  "
                  :cData="cDataImagesPath"
                >
                  <!-- <view slot="upload-after" class="upload-after">
                    <text class="upload-tips">{{ form.materialType === 2 ? '上传视频封面' : '上传图片' }}</text>
                    <text class="upload-tips">{{ form.materialType === 2 ? '默认视频第一帧' : '助您更清晰描述' }}</text>
                  </view> -->
                  <view slot="upload-after" class="upload-after" v-if="form.materialType === 1">
                    <view class="upload-tips">上传图片</view>
                  </view>
                  <view slot="upload-after" class="upload-after" v-else>
                    <view class="upload-tips">上传视频封面</view>
                    <view class="default-tips">默认视频第一帧</view>
                  </view>
                </title-img>
              </view>
              <view class="gambit-box" @tap="handleClickGambit">
                <template v-if="gambitList.length">
                  <view class="gambit-box-l"><image class="icon-gambit-select" :src="file_ctx + 'static/image/business/hulu-v2/icon-gambit-select.png'"></image>选择话题</view>
                  <view class="gambit-box-r">
                    <view class="item-content">
                      <view class="box-item" v-for="(item,index) in gambitList.slice(0,3)" :key="item.id">
                        # {{ index == gambitList.slice(0,3).length -1 && gambitList.length > 3 ? item.name+'...' : index == gambitList.slice(0,3).length -1 ? item.name : item.name+'、' }}
                      </view>
                    </view>
                    <image class="icon-gambit-right" :src="file_ctx + 'static/image/business/hulu-v2/icon-gambit-right.png'"></image>
                  </view>
                </template>
                <template v-else>
                  <view class="gambit-box-l"><image class="icon-gambit-select" :src="file_ctx + 'static/image/business/hulu-v2/icon-gambit-select.png'"></image>选择话题</view> 
                  <view class="gambit-box-r">请选择</view>
                </template>
              </view>
              <view class="line-bottom" :style="{'marginTop':form.circleClassifyId ? '0' : '22rpx'}">
                <title-selector
                  :config="config.circleClassifyId"
                  v-model="form.circleClassifyId"
                  :cData="circleClassifyInfoObj.id"
                >
                  <view class="selector-label" slot="label" slot-scope="{ data }">
                    <view class="selector-label-title">
                      <view class="img">
                        <image :src="file_ctx + 'static/image/business/hulu-v2/icon-post-circle.png'"></image>
                      </view>
                      <span>{{ data.label }}</span>
                    </view>
                    <view class="selector-label-value" v-if="!form.circleClassifyId">
                      <view slot="placeholder" class="selector-placeholder-box">
                        <!-- <view class="selector-placeholder">选择圈子，精准链接病友、医生、药企，获取帮助</view> -->
                        <view class="selector-placeholder">精准链接病友、医生、药企，获取帮助</view>
                      </view>
                    </view>
                  </view>
                </title-selector>
              </view>
            </view>
            <button class="submit-btn" type="primary" @click="submit" :loading="submitLoading">
              {{ submitLoading ? "发布中..." : "点击发布" }}
              <span v-if="form.materialType === 2">发布后需要等待审核</span>
            </button>
          </view>
        </view>
      <!-- </scroll-view> -->
    </view>
  </page>
</template>

<script>
import { mapState } from 'vuex'
import TitleInput from "@/components/business/module/v1/title-input/index"
import TitleTextarea from "@/components/business/module/v1/title-textarea/index"
import TitleSelector from "@/components/business/module/v1/title-selector/index"
import TitleImg from "@/components/business/module/title-img/index"
import TitleRadio from "@/components/business/module/title-radio/index.vue"
import StatusBarHeight from '@/components/business/module/status-bar-height/index'
// import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'
import env from '@/config/env'
const defaultForm = {
  title: '',
  content: '',
  circleClassifyId: '',
  isAnonymity: 2,
  imagesPath: '',
  materialType: 1,
  videosPath: '',
  topicIds:''//话题
}
const defaultConfig = {
  title: {
    show: true,
    disabled: false,
    required: true,
    maxlength: 25,
    placeholder: '输入标题，获取更多关注与帮助',
    showLabel: false,
    label: '帖子标题',
    style: "border: none;font-size: 31rpx;font-weight: 500;line-height: 42rpx;outline: none;width: calc(100% - 80rpx);"
  },
  content: {
    show: true,
    style: 'border: none',
    disabled: false,
    required: true,
    placeholder: '请输入求助需求描述...',
    label: '帖子正文',
    showLabel: false,
    inputStyle: 'border: none;font-size: 28rpx;line-height: 42rpx;min-height: 300rpx;padding: 0;background-color: #fff;',
    maxlength: -1
  },
  imagesPath: {
    multiSelectCount: 9,
    count: 9,
    show: true,
    padding: '32rpx 0'
  },
  circleClassifyId: {
    show: true,
    label: '选择发帖圈子',
    required: false, // 这里应该为必选，只是为了兼容样式
    array: [],
    titleWidth: '410rpx',
    showJump: false,
    placeholder:'请选择'
  },
  materialType: {
    show: true,
    name: 'materialType',
    // label: '帖子类型',
    required: false, // 这里应该为必选，只是为了兼容样式
    array: [{ key: 1, value: '图文' }, { key: 2, value: '短视频' }],
    marginStyle: '0'
  },
  intro: {
    label: '短视频简介',
    style: 'border: none',
    disabled: false,
    placeholder: '请输入求助需求描述...',
    showLabel: false,
    // inputStyle: 'border: none;font-size: 27rpx;line-height: 42rpx;min-height: 300rpx;padding: 0;',
    inputStyle: 'border: none;font-size: 28rpx;line-height: 42rpx;min-height: 300rpx;padding: 0;background-color: #fff;',
    maxlength: -1
  },
  videosPath: {
    multiSelectCount: 1,
    count: 1,
    label: '短视频',
    padding: '32rpx 0',
    required: true,
    show: false
  },
}
export default {
  components: {
    TitleInput,
    TitleTextarea,
    TitleImg,
    TitleSelector,
    TitleRadio,
    StatusBarHeight,
    // uniNavBar
  },
  data() {
    return {
      file_ctx: this.file_ctx,
      $static_ctx2: this.$static_ctx,
      cDataImagesPath: [],
      cDataVideosPath: [],
      isAnonymous: false, // 是否匿名
      form: JSON.parse(JSON.stringify(defaultForm)),
      config: JSON.parse(JSON.stringify(defaultConfig)),
      submitLoading: false,
      circleClassifyInfoId:null,
      gambitList:[],//话题数组
      businessType:null,//订阅类型
    }
  },
  onLoad(options) {
    const query = this.$validate.isNull(this.$Route.query) ? options : this.$Route.query
    console.log(query,'query000099')
    if(query?.gambitId){
      this.gambitList = [{id:query.gambitId,name:query.gambitName}]
    }

    if(query?.params){
      this.gambitList = JSON.parse(decodeURIComponent(query.params))
    }

    if(query?.form) {
     this.form = JSON.parse(decodeURIComponent(query.form))
     this.circleClassifyInfoId = this.form?.circleClassifyId
     if(this.form?.materialType == 1){
       this.cDataImagesPath = this.form?.imagesPath?.map(item=>({url:this.file_ctx + item}))
     }
     if(this.form?.materialType == 2){
       this.config.content.show = false
       this.config.intro.show = true
       this.config.videosPath.show = true
       this.cDataVideosPath = this.form?.videosPath?.map(item=>({url:this.file_ctx + item}))
       this.cDataImagesPath = this.form?.imagesPath?.map(item=>({url:this.file_ctx + item}))
       this.config.imagesPath.multiSelectCount = 1
     }
    }
    this.getCircleclassifyQueryList()
  },
  watch: {
    circleClassifyInfoId(){
      this.circleClassifyInfoObj.id = this.circleClassifyInfoId
      console.log('this.circleClassifyInfoId',this.circleClassifyInfoId)
    },
  },
  mounted () {
    this.getCircleclassifyQueryList()
  },
  onShow() {
    // #ifdef MP-WEIXIN
    this.setTabBarIndex(2);
    // #endif
  },
  onHide(){
    this.$common.setKeyVal('business', 'circleClassifyInfoObj', {})
  },
  computed: {
    ...mapState('user', {
      accountId: state => state.accountId,
      fansRecord: state => state.fansRecord,
      curSelectUserInfo: state => state.curSelectUserInfo, // 当前选中的用户信息
      isLogin: state => state.isLogin
    }),
    ...mapState('business', {
      circleClassifyInfoObj: state => state.circleClassifyInfoObj,
    }), 
    infoObj() {
      return {
        lightLogoPath: this.fansRecord.lightLogoPath
      }
    }

  },
  methods: {
    toJSON(){
      return this
    },
    handleBack(){
      this.$navto.back(1)
    },
    // 点击话题
    handleClickGambit(){
      this.$navto.push('postGambitIndex',{path:'postMessageIndex',list:this.gambitList,form:{...this.form,circleClassifyId:this.form.circleClassifyId == null ? '' : this.form.circleClassifyId}})
    },
    updateForm(obj) {
      this.form[obj.key] = obj.value
      if (obj.key === 'materialType') {
        Object.keys(this.config).forEach(key => {
          if (['content'].includes(key)) {
            this.config[key].show = obj.value !== 2
          } else if (['videosPath', 'intro'].includes(key)) {
            this.config[key].show = obj.value === 2
          } else if (key === 'imagesPath') {
            this.config[key].multiSelectCount = obj.value === 2 ? 1 : 9
            this.config[key].count = obj.value === 2 ? 1 : 9
            this.form[key] = ''
            this.cDataImagesPath = []
          }
        })
      }
    },
    /**
     * 公众号订阅
     */
    accountSubscribe() {
      return new Promise(async (resolve, reject) => {
        const unionid = await this.$ext.wechat.getUnionId().catch(() => {
          this.submitLoading = false
        })
        if (!unionid) resolve()
        const res = await this.$api.common.accountattentionSubscribeOrNot({
          unionid,
          wxId: 'wx0918ff821e41f5c4'
        })
        if (!this.$validate.isNull(res.data)) resolve()
        this.$uniPlugin.modal('您还没关注消息助手，请前往开启', '', {
          showCancel: true, // 是否显示取消按钮，默认为 true
          cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
          cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
          confirmText: '确定', //  确定按钮的文字，默认为"确定"，最多 4 个字符
          confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
          fn: async (n) => {
            if (n) {
              this.clearForm()
              this.navtoGo('WebHtmlView', { src: env.subscribeUrl, title: '消息订阅' })
              reject()
            } else {
              resolve()
            }
          }
        })
      })
    },
    async submit() {
      if (this.submitLoading) return
      if (!this.isLogin) {
        this.$navto.push('Login', {
          formPage: 'PostMessage',
          formPageParams: encodeURIComponent(
            JSON.stringify({
              loginIn: true,
              isBackVisible: true,
              form:{...this.form,content:this.form.intro ? this.form.intro : this.form.content},
            })
          )
        })
        return
      }

      await this.$ext.user.authCommunityFansInfo()
      // 表单校验
      const config = JSON.parse(JSON.stringify(this.config))
      config.circleClassifyId.required = true
      config.materialType.required = true
      for (const key in this.form) {
        if (!this.$validate.isNull(config[key])) {
          if (config[key].show !== false && config[key].required && !this.form[key]) {
            this.$uniPlugin.toast(`${config[key].label}不得为空！`)
            return
          }
        }
      }
      this.submitLoading = true
      this.form.isAnonymity = this.isAnonymous ? 1 : 2

      let openId = await this.$ext.wechat.getOpenId().catch(() => {
        this.submitLoading = false
      })
      const param = {
        ...this.form,
        imagesPath: !this.$validate.isNull(this.form.imagesPath) ? this.form.imagesPath.join(',') : '',
        videosPath: !this.$validate.isNull(this.form.videosPath) ? this.form.videosPath.join(',') : '',
        source: this.$validate.isNull(this.fansRecord.framerPlanApplyId) ? 1 : 3, // 作者类型：1-用户，2-马甲 3-平台
        type: 2, // 内容产生类型：1-PGC(管理后台新增)；2-UGC(小程序新增)
        isPhysician: 2, // 是否医生帖：1-是，2-否
        accountId: this.accountId,
        isComment: 1, // 是否开启评论：1-是，2-否
        putawayTime: this.$common.formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss'),
        imgShowType: 1, // 帖子详情是否显示图片集合
        openid: openId,
        orientParams: 4, // 1-内部运营 2-广告 3-企业 4-真实用户
      }

      param.topicIds = this.gambitList.length && JSON.stringify(this.gambitList) || ''
      if(param.materialType == 1){
        param.videosPath = ''
      }
      let circleClassifyName = config?.circleClassifyId?.array?.find(item=>(item.id == param.circleClassifyId))
      console.log('param',param)
      // return;
      const res = await this.$api.postmessage.postmessageInsert(param).catch(() => {
        this.submitLoading = false
      })
      // #ifdef MP-WEIXIN
      getApp().globalData.sensors.track("CreateAPost",
        {
          'post_type' : param.materialType == 1 ? '图文' : '短视频',
          'post_title' : param.title,
          'content_belong_circle' : circleClassifyName.name,
        }
      ) 
      // #endif
      this.$common.setKeyVal('business','circleClassifyInfoObj',{})      
      this.$uniPlugin.toast(res.msg)

      if (env.isOpenMsgSubscribe) {
        // 消息订阅
        const allTmplIds = [
          ...this.$constant.system.postsBeLikeTmplIds, // 帖子被赞
          ...this.$constant.system.commentReplyTmplIds, // 评论回复
          ...this.$constant.system.commentReplyOtherTmplIds, // 帖子评论回复
        ]
        await this.$uniPlugin.subscribeMessage(allTmplIds).catch(() => {
          this.submitLoading = false
        })
        const subscribeMessageRes = await this.requestSubscribeMessage(allTmplIds).catch(() => {
          this.submitLoading = false
        })
  
        const { centerUserId = '' } = this.curSelectUserInfo || {};
        const logParamList = Object.keys(subscribeMessageRes).filter(key => {
          return allTmplIds.includes(key)
        }).map(key => {
          // 点赞: 2
          // 帖子回复评论: 3
          // 回复评论: 4
          if (this.$constant.system.postsBeLikeTmplIds.includes(key)) {
            this.businessType = 2
          } else if (this.$constant.system.commentReplyTmplIds.includes(key)) {
            this.businessType = 4
          } else if (this.$constant.system.commentReplyOtherTmplIds.includes(key)) {
            this.businessType = 3
          }
          return {
            appId: this.$appId,
            templateId: key,
            openId: openId,
            subscribeStatus: subscribeMessageRes[key],
            businessType:this.businessType,
            businessId: res.data,
            accountId: this.accountId,
            userId: centerUserId
          }
        })
        this.$api.common.wxsubscribemessagelogInsertBatch({ wxSubscribeMessageLogList: logParamList })
        // 消息订阅调起成功，返回值'accept'、'reject'、'ban'分别代表用户对此条订阅是同意、拒绝、后台禁用
      }

      // #ifdef MP-WEIXIN
      getApp().globalData.sensors.track("Subscription",
        {
          'content_belong_circle' : circleClassifyName,
          'function_name' : this.businessType == 2 ? '帖子被赞通知' : this.businessType == 3 ? '评论回复通知' : '帖子评论回复通知',
          'subscription_type':'一次性订阅'
        }
      )
      // #endif

      if (env.isOpenAccountSubscribe) {
        await this.accountSubscribe().catch(() => {
          this.submitLoading = false
        })
      }
      this.submitLoading = false
      this.clearForm()
      this.$set(this,'gambitList',[])
      if (param.materialType === 2) {
        this.$uniPlugin.modal('提示', '发布成功，等待审核', {
          showCancel: false, // 是否显示取消按钮，默认为 true
          cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
          cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
          confirmText: '前往查看', //  确定按钮的文字，默认为"确定"，最多 4 个字符
          confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
          fn: async (n) => {
            if (n) {
              this.navtoGo('PersonalMyPosts', { processStatus: 1 })
            }
          }
        })
      } else {
        this.navtoGo('PostsDetail', { id: res.data })
      }
    },
    // 消息订阅
    requestSubscribeMessage(tmplIds) {
      return new Promise((resolve, reject) => {
        try {
          this.$uniPlugin.requestSubscribeMessage(tmplIds, (res) => {
            let status = true
            tmplIds.forEach(item => {
              if (res[item.toString()] !== 'accept') status = false
            })
            if (status) {
              this.isShow = false
              this.$uniPlugin.toast('订阅成功')
              resolve(res)
            } else {
              this.isShow = true
              this.$uniPlugin.toast('订阅失败')
              resolve(res)
            }
          })
          this.$uniPlugin.hideLoading()
        } catch (err) {
          reject(err)
        }
      })
    },
    navtoGo(url, obj = {}) {
      this.$navto.push(url, obj)
    },
    clearForm() {
      this.isAnonymous = false, // 是否匿名
      this.form = JSON.parse(JSON.stringify(defaultForm))
      this.config = JSON.parse(JSON.stringify(defaultConfig))
      this.cDataImagesPath = []
      this.cDataVideosPath = []
      this.$forceUpdate()
    },
    changeAnonymous(e) {
      this.isAnonymous = e.detail.value.includes('anonymous')
    },
    // 获取圈子列表
    getCircleclassifyQueryList() {
      const that = this
      that.$api.circleclassify.circleclassifyQueryList().then(res => {
        const data = res.data.map(item => {
          return {
            ...item,
            value: item.id,
            label: item.name
          }
        })
        that.config.circleClassifyId.array = data
      })
    },
    imgReturnFn(obj, key) {
      if (key === 'videosPath') {
        this.cDataVideosPath = obj
        if (this.$validate.isNull(this.cDataImagesPath) || this.cDataImagesPath[0].dir.indexOf('?x-oss-process=video/snapshot,t_0000,f_jpg') !== -1) {
          if (this.$validate.isNull(obj)) {
            this.cDataImagesPath = []
            this.form.imagesPath = []
            return
          }
          const dir = `${obj[0].dir}?x-oss-process=video/snapshot,t_0000,f_jpg`
          this.cDataImagesPath = [{ url: this.file_ctx + dir, filePath: dir, dir: dir }]
          this.form.imagesPath = [dir]
        }
      } else if (key === 'imagesPath') {
        this.cDataImagesPath = obj
      }
      this.form[key] = obj.map(item => item.dir)
    }
  }
}
</script>

<style scoped lang="scss">
.body-main {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
  // #ifdef MP-WEIXIN
  padding-bottom: calc(56px + env(safe-area-inset-bottom));
  // #endif
  .my-data{
    position: relative;
    .my-bg {
      width: 100%;
      height: 498upx;
      /*height: 350upx;*/
      background-size: 100% 100%;
      background-repeat: no-repeat;
      .pdl40.my-user {
        padding-left:40upx
      }
      .top-nav{
        position: relative;
        height: 40px;
        .top-nav-l{
          display: flex;
          align-items: center;
          justify-content: center;
          position: absolute;
          height: 40px;
          left: 24rpx;
          top: 0;
          .header-search-img{
            display: flex;
            width: 48rpx;
            height: 48rpx;
            margin-right: 24rpx;
          }
        }
      }
      .my-user{
        position: relative;
        height: 120upx;
        padding: 24upx 32upx 64upx;
        line-height: 0;
        .user-info {
          display: flex;
          align-items: center;
          position: absolute;
          left: 30upx;
          top: 30upx;
          line-height: 0;
          .img-main{
            position: relative;
            display: inline-block;
            // margin-right: 20upx;
            width: 112upx;
            height: 112upx;
            position: relative;
            .role-image {
              width: 100upx;
              height: 100upx;
              display: inline-block;
            }
            .user-sex{
              position: absolute;
              right: 4upx;
              bottom: 4upx;
              width: 30upx;
              height: 30upx;
              z-index: 1;
              @include rounded(50%);
              overflow: hidden;
            }
          }
          .user-head-name{
            display: inline-block;
            vertical-align: top;
            .name{
              height: 50rpx;
              font-weight: 500;
              font-size: 36rpx;
              color: #1F2021;
              line-height: 50rpx;
            }
            .edit{
              margin-top: 8upx;
              .text{
                height: 34rpx;
                /* font-family: PingFangSC, PingFang SC; */
                font-weight: 400;
                font-size: 24rpx;
                color: #4E5569;
                line-height: 34rpx;
              }
              .icon-edit-more{
                vertical-align: middle;
                display: inline-block;
                margin-left: 18upx;
                @include iconImg(32, 32, '/business/icon-edit-more.png');
              }
            }
          }
          .role {
            color: #da4d01;
            background: #ffc746;
            @include rounded(30upx);
          }
        }
      }
    }
  }
  .keyboard{
    display: flex;
    flex-direction: column;
    position: fixed; /* 或者 absolute */
    bottom: 134px; /* 或者其他合适的位置 */
    width: 100%; /* 宽度 */
    z-index: 100; /* 确保在键盘上面 */
  }

  // .my-scroll-view{
  //   flex: 1;
  //   overflow: hidden;
  // }
  .form-wrapper {
    position: absolute;
    top: 450rpx;
    left: 0;
    width: 100%;
    height: calc(100% - 90rpx);
    border-radius: 24rpx 24rpx 0rpx 0rpx;
    // overflow: hidden;
    overflow-y: scroll;
    background-color: #fff;
  }
  .form-item-list {
    padding: 0 32rpx 20rpx;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    box-sizing: border-box;
  }
  .selector-placeholder {
    height: 32rpx;
    line-height: 32rpx;
    font-size: 22rpx;
    color: #A5AAB8;
  }
  .submit-btn {
    position: relative;
    width: calc(100% - 64rpx);
    font-size: 32upx;
    font-weight: 500;
    color: #ffffff;
    height: 88rpx;
    line-height: 88upx;
    background: #00B484;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    overflow: inherit;
    span{
      position: absolute;
      top:-6rpx;
      right: 56rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 222rpx;
      height: 48rpx;
      font-weight: 500;
      font-size: 22rpx;
      color: #FF712B;
      background: #FFEEE6;
      border-radius: 24rpx 24rpx 24rpx 0rpx;
      z-index:999;
    }
  }
  .upload-after {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    // padding-left: 12rpx;
    .upload-tips {
      // color: #7f7f7f;
      // font-size: 22rpx;
      // letter-spacing: 2rpx;
      height: 36rpx;
      line-height: 36rpx;
      font-size: 24rpx;
      color: #4E5569;
    }
    .default-tips{
      height: 28rpx;
      line-height: 28rpx;
      font-size: 20rpx;
      color: #868C9C;
    }
  }
  .selector-label {
    display: flex;
    flex-direction: column;
    .selector-label-title{
      display: flex;
      align-items: center;
      .img{
        display: flex;
        width: 32rpx;
        height: 32rpx;
        margin-right: 4rpx;
        image{
          width: 100%;
          height: 100%;
        }
      }
      span{
        display: inline-block;
        height: 40rpx;
        line-height: 40rpx;
        font-size: 28rpx;
        color: #1D2029;
      }
    }
    .selector-label-value{
      margin: 4rpx 0 0 36rpx;
    }
  }
}
.tips-text {
  color: #666;
  text-align: center;
  font-size: 26rpx;
  line-height: 42rpx;
  color: $warningColor;
}
.flex-box-start {
  display: flex;
  align-items: flex-start;
}
.gambit-box{
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #E6E6E6;
  .gambit-box-l,.gambit-box-r{
    display: flex;
    align-items: center;
    font-size: 32rpx;
    color: #1D2029;
    .icon-gambit-select{
      display: flex;
      width: 32rpx;
      height: 31rpx;
      margin-right: 6rpx;
    }
  }
  .gambit-box-r{
    .item-content{
      display: flex;
      color: #333333;
      white-space: nowrap;      
      overflow: hidden;         
      text-overflow: ellipsis;  
      max-width: 400rpx;
      .box-item{
        display: flex;
        align-items: center;
        font-size: 24rpx;
        color: #777777;
      }
    }
    .icon-gambit-right{
      display: flex;
      width: 32rpx;
      height: 32rpx;
    }
  }
}
</style>
