<template>
  <page>
    <view slot="content" class="body-main">
        <view class="m-main-body">
          <scroll-refresh class="scroll-refresh-main" :isShowEmptySwitch="true" :fixed="false" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
            <!-- <view class="doctor-content" v-if="indexlist.length == 0" :style="{height:'0%'}"> -->
            <view class="doctor-content">
              <view class="doctor-item" v-for="(item,index) in indexlist" :key="item.id">
                <view class="title">
                  <view class="title-img">
                    <image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/greenBg2.png'"></image>
                    <text class="centered-text">问</text>  
                  </view>
                  <span>{{ item.question }}</span>
                </view>
                <view class="doctor-box" :style="{flexDirection: (item.qaType == 2 || item.qaType == 3) ? 'column':'row'}">
                <!-- <view class="doctor-box"> -->

                  <view class="doctor-img" v-if="item.qaType == 1">
                    <image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/redBg2.png'"></image>
                    <text class="centered-text">答</text>  
                  </view>
                  <!-- <view class="ishow-doctor-info" v-if="item.doctorId"> -->
                  <view class="ishow-doctor-info" v-else-if="item.qaType == 2">
                    <view class="ishow-doctor-info-t">
                      <!-- <view class="ishow-doctor-img" v-if="item.expertPic"><image :src="file_ctx + item.expertPic"></image></view>
                      <view class="ishow-doctor-img" v-else><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-pharmacist-questions-doctor.jpg'"></image></view> -->
                      <view class="ishow-doctor-img" v-if="item.gender == 1"><image :src="file_ctx + (item.expertPic || 'static/image/business/pharmacy-cyclopedia/icon-pharmacist-questions-doctor-boy.png')"></image></view>
                      <view class="ishow-doctor-img" v-else><image :src="file_ctx + (item.expertPic || 'static/image/business/pharmacy-cyclopedia/icon-pharmacist-questions-doctor-girls.png')"></image></view>
                      <view class="ishow-doctor-name" v-if="item.doctorName">{{item.doctorName}}</view><span class="ishow-doctor-slash">|</span>
                      <view class="ishow-doctor-chief-pharmacist" v-if="item.post">{{item.post}}</view>
                      <view class="ishow-doctor-hospital-depart" v-if="item.deptName">{{item.deptName}}</view>
                    </view>
                    <view class="ishow-doctor-info-b">
                      <view class="ishow-doctor-hospital-name" v-if="item.hospital">{{item.hospital}}</view>
                      <view class="ishow-doctor-hospital-class" v-if="item.level">{{hospitalLevel(item.level)}}</view>
                    </view>

                  </view>
                  <view class="ishow-doctor-info" v-else>
                    <view class="ishow-doctor-info-t" style="margin-bottom:24rpx">
                      <view class="ishow-doctor-img" v-if="item.doctorImg"><image :src="file_ctx + (item.doctorImg)"></image></view>
                      <view class="ishow-doctor-img" v-else><image :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-pharmacist-questions-doctor-boy.png'"></image></view>
                      <view class="ishow-doctor-name" v-if="item.doctorName">{{ item.doctorName }}</view>
                      <view class="ishow-doctor-title" v-if="item.title">{{ item.title }}</view>
                    </view>
                  </view>
                  <view class="doctor-info" :style="{marginLeft: (item.qaType == 2 || item.qaType == 3) ? '52rpx':'0'}">
<!-- 
                    <view class="info-item" :class="{lineclamp3:item.isAll}">{{ item.answer }}</view>
                    <view v-if="item.isMore">
                      <span v-if="item.isAll" @click="handleClickType(item,index)">展开</span>
                      <span v-else @click="handleClickType(item,index)">收起</span>
                    </view> -->
                    <moreLinesDivide :line="4" :dt="item.answer"></moreLinesDivide>
                    
                  </view>
                </view>
                <view class="doctor-bottom">
                  <view class="like" @click="handleLike(item)">
                    <span :class="item.isLiked ? 'isLike' : 'notLike'">{{item.likeNum == 0 ? '抢首赞' : item.likeNum}}</span>
                    <img style="width: 14px; height: 14px;" v-if="!item.isLiked"
                      src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAApVJREFUWEfFlz9oFEEUxr93SWEhFkIaBUljo4WgATEaiI0GwVLBQg2EzLd3hSBqLI2lRgWLY+ftSSQgggoWNv5pImLUwjQBg1goqCCIIBqwueSeTLg7Ln9Mcmdub7vdnZnvN9+8fe+toMWXtFgfDQGMjo52zM7OHgMwIyLTg4ODk41upG4A7/0OEbkPYGdFVEQuOOeuNQJRN4CqBvGw+3dm9kZEBsrCPSRf1gvRCIAFETPrjqLodZIkY2Z2SkQmnHMHUgMgOQ8fx3FvJpMZB/CDZEfqACEgi8XidwA/SW5uKkCN2AzJTYscmCTZ1VQA7/2AiNwSkefOuYNBLEmSnJnlATwgebypAKr6CMBRANdJng9i3vu8iORE5Mrc3NyTlQDM7HMul/tYO2bNX0GhUDhRKpXumtlvAPuiKJouOzBuZr117HyBU6sCqOpeAH0AhssiwyQvVwTjOO5va2s7vRaAGtCrJC+GOVUAVb1UTjDVDLd4URHJOuf8WsSWG5MkSbeZTYR3lc+4CpAkyUpWht3fI/m+UfEwL5/Pb2xvb59ZFkBV5zNchazsSBBeYPn/ABQKhZ5SqfQCwBTJXYuPoOkAqnoGwE0Ad0ieTB3Ae39bRPoBDJEcSR1AVd8C2CMifc65p6kCmJkkSfIHwAYAW0h+SxVAVXcDmDSzL1EUbasEc20eaGoQVuoIgMckj6QOoKo3AJwFMEJyqBUAIegOAegnOdYKgK8AtmYyma7aLjqVGPDebxeRDwB+AeggWUzVAVV1ABTAK5L7l+0HVPUTgM7Q7VQGhPJZe99oHagpw4dJPvsXQPixONeoyErzRGTKzB7W9hFLjiA8iOO4M7iw3hDZbLbq6pIeY73F6l1v1Zas3gXrHf8XhhNvMGSmtPYAAAAASUVORK5CYII=" />
                    <img style="width: 14px; height: 14px;" v-else
                      src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAfVJREFUWEfFl79LHEEUx7/vclr4g51TLFQQW9FNDFhFBDvjtcF0gXSp5eYuWJlUAW9NIP9BIFUEOxFtTKF2FrezKFgkkEBS3h4SAtHbJ6vu5W5d1F3cm20WZt+b7+e9mTfzlqD5Ic36SATw+OPJQP2ft+ABJwQ+VFIcJA0kEYBpVfcAehKIMqHoFISVBCI2wOS76mi9g76HxTLwZiqybzcuRGwAc7WWB/NGWIhBu440ZlIHmFitLRPzm+tC9EtJY1gjAP9VMtelEQC2kuJR6gCm5X4BsBAWImDdluJZqgBjH/4MZr3TYzB6IoRWmHnzJgDK0g+1KL4128SqgnD9x432yn5NSfE88L0VYPx9rS/jYR6EJTCPJxQNu60oKV77gw2Ai/Ly1/ZKRElx8c20XL4n0ZZpgvkbAKbl7gCYDax0ALREmjJAo2SbM9A+AKLPqmC8aNkD4bVOMwPEKNlFUdYGkAE9rUhjSxvA2YOOoaPF7t+6AH4qKUauHURt3AObSoq8NgAGyo4UJW0ABH5py9wnbQAAppq76HYfRLXOXmPg4BWdasoA7yuZm47sB0zL9Vvt0TQvI/bqc06pfzsS4KHlWgwUUgKwmWjdKRhvI1q5/0P+T8dZ9jILTjH31X9PlKuNKzppXxDMFeV/a0eUVPSuftoBzgHKR/ohZwAugwAAAABJRU5ErkJggg==" />
                  </view>
                </view>
              </view>
            </view>
          </scroll-refresh>
      </view>
    </view>
  </page>
</template>

<script>
  import { mapState } from 'vuex'
  import UniIcons from '@/components/uni/uni-icons/uni-icons'
  import moreLinesDivide from '@/components/business/module/more-lines-divide/more-lines-divide'
  export default {
    components:{
      UniIcons,
      moreLinesDivide,
    },
    data(){
      return{
        file_ctx: this.file_ctx,
        $constant: this.$constant,
        mescroll: null, // mescroll实例对象
        downOption: {
          auto: false // 不自动加载
        },
        upOption: {
          auto: false, // 不自动加载
          empty: {
            top: 0,
            zIndex: 999,
          },
          onScroll: true
        },
        indexlist:[],
        questionId:null,
        hospitalArr : [
          { label: '三甲医院', value: 1 },
          { label: '三乙医院', value: 2 },
          { label: '三丙医院', value: 3 },
          { label: '二甲医院', value: 4 },
          { label: '二乙医院', value: 5 },
          { label: '二丙医院', value: 6 },
          { label: '一甲医院', value: 7 },
          { label: '一乙医院', value: 8 },
          { label: '一丙医院', value: 9 },
        ],
        labelValue:null,
        channelCode:null,
      }
    },
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId,
        publishInfo:state => state.publishInfo,
        curSelectUserInfo:state => state.curSelectUserInfo,
      })
    },
    onLoad(option){
      const query = this.$validate.isNull(this.$Route.query) ? option : this.$Route.query
      // console.log(query,'query55555')
      this.questionId = query.id
      let title = query?.title
      this.labelValue = query?.labelValue
      this.channelCode = query?.gs
      if(title){
        uni.setNavigationBarTitle({
          title: title
        });
      }
      this.$nextTick(() => {
        this.init()
      })
    },
    onShareAppMessage: function (res) {  
      if (res.from === 'share') {  
        // 来自页面内转发按钮  
        console.log(res.target);  
      }
      this.queryAndCreate(3)
      return {
        title: `${this.publishInfo.name}${this.labelValue}`, //分享的名称
        path: 'modules/pharmacy/pharmacy-cyclopedia/index?gs='+ encodeURIComponent(this.channelCode),
        mpId:this.$appId, //此处配置微信小程序的AppId
      }
    },
    mounted(){
    },
    methods:{
      async queryAndCreate(type){
        let { phone = '' } =  this.curSelectUserInfo || {}
        let params = {
          accountId:this.accountId,
          phone: phone,
          productId:this.questionId,
          shareType:type,  //	分享类型：1分享按钮2保存二维码3微信好友分享按钮
        }
        await this.$api.drugBook.queryAndCreate(params)
      },
      hospitalLevel(val){
        let obj = this.hospitalArr.find(item => (item.value == val)) 
        return obj.label
      },

      handleClickType(item){
        item.isAll = !item.isAll
      },

      async handleLike(item){
        const post = this.indexlist.find(p => p.id === item.id);  
        const params = {
          id:item.id,
          accountId:this.accountId,
          productId:this.questionId
        }
        if(post){
          if(post.isLiked){
            this.$api.drugBook.cancelQuestionanswerLike(params)
            post.likeNum--
          } else {  
            // 点赞  
            this.$api.drugBook.addQuestionanswerLike(params)
            post.likeNum++
          }
          post.isLiked = !post.isLiked;  
        }
      },
      returnFn(obj) {
        const that = this
        setTimeout(function () {
          let params = {
            current: obj.pageNum,
            size: obj.pageSize,
            condition: {
              businessType:2,
              productId:that.questionId
            }
          }
          that.$api.drugBook.getQuestionanswerPage(params).then(res => {
            // console.log(res,'res25555')
            let data = res.data.records.map(item=>{
              if(item.answer.length > 80){
                item.isMore = true
                item.isAll = true
              } else {
                item.isMore = false
                item.isAll = false
              }
              return {
                ...item,
                isLiked:false,
                // arrowsType:false
              }
            })
            if (obj.pageNum === 1) {
              that.indexlist = []
            }
            that.indexlist = [...that.indexlist, ...data]
            obj.successCallback && obj.successCallback(data)
          })
        }, that.$constant.noun.scrollRefreshTime)
      },
      scrollInit(scroll) {
        scroll.optUp.page.num = 1
        scroll.optUp.page.size = 10
        this.mescroll = scroll
      },
      init() {
        this.$nextTick(() => {
          this.mescroll.triggerDownScroll()
        })
      },
    },
 }
</script>

<style lang='scss' scoped>
.body-main{
  height: 100%;
  .m-main-body{
    height: 100%;
    .scroll-refresh-main{
      height: 100%;
      /deep/ .mescroll-empty-box{
        // min-height: 0%;
        position: absolute !important;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        // .mescroll-empty{
        //   .empty-tip{
        //     margin-top: 120rpx;
        //   }
        // }
      }
      .doctor-content{
        height: 100%;
        padding:24upx;
        .doctor-item{
          display: flex;
          flex-direction: column;
          background-color: #fff;
          padding:30upx 40upx;
          margin-bottom: 24upx;
          border-radius: 13upx;
          .title{
            display: flex;
            // align-items: center;
            font-family: Source Han Sans CN;
            font-size: 29upx;
            color: #000000;
            .title-img{
              display: flex;
              flex-shrink: 0;
              position: relative;
              width: 32.44rpx;
              height: 32.44rpx;
              border-radius: 50%;
              // margin-right: 5upx;
              margin-right: 20upx;
              margin-top: 6rpx;
              image{
                width: 100%;
                height: 100%;
              }
              .centered-text {  
                color: white;
                text-align: center;
                position: absolute;  
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                z-index: 1; 
                font-size:19rpx;
              }  
            }
            span{
              font-weight: bold;
            }
          }
          .doctor-box{
            display: flex;
            flex-direction: column;
            margin-top: 24upx;
            .ishow-doctor-info{
              display: flex;
              flex-direction: column;
              .ishow-doctor-info-t{
                display: flex;
                align-items: center;
                font-size: 22rpx;
                color: #858687;
                .ishow-doctor-img{
                  width: 32rpx;
                  height: 32rpx;
                  margin-right: 20rpx;
                  border-radius: 50%;
                  overflow: hidden;
                  image{
                    width: 100%;
                    height: 100%;
                  }
                }
                .ishow-doctor-name,
                .ishow-doctor-slash,
                .ishow-doctor-chief-pharmacist,
                .ishow-doctor-title,
                .ishow-doctor-hospital-depart{
                  font-size: 22rpx;
                }
                .ishow-doctor-name{
                  margin-right: 10rpx;
                }
                .ishow-doctor-chief-pharmacist{
                  margin:0 10rpx;
                }

              }
              .ishow-doctor-info-b{
                display: flex;
                align-items: center;
                color: #858687;
                margin: 5rpx 0 24rpx 52rpx;
                .ishow-doctor-hospital-name,
                .ishow-doctor-hospital-class{
                  font-size: 22rpx;
                  margin-right: 8rpx;
                }
                .ishow-doctor-hospital-class{
                  border-radius: 4rpx;
                  border: 1rpx solid rgba(0,180,132,0.4);
                  color: #19D09F;
                  padding: 0 4rpx;
                  line-height: 28rpx;
                }
              }
            }
            .doctor-img{
              position: relative;
              width: 32.44rpx;
              height: 32.44rpx;
              flex-shrink: 0;
              border-radius: 50%;
              margin-right: 20upx;
              margin-top: 6upx;
              image{
                width: 100%;
                height: 100%;
              }
              .centered-text {  
                color: white;
                text-align: center;
                position: absolute;  
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                z-index: 1; 
                font-size:19rpx;
              }  
            }
            .doctor-info{
              position: relative;
              .info-item{
                line-height: 44.92rpx;
              }
              .lineclamp3{				
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 4; 
                -webkit-box-orient: vertical;
                white-space: normal;
              }
              .info-content{
                // margin-top: 10upx;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 4;
                -webkit-box-orient: vertical;
                // font-size: 34upx;
                // padding:0 30upx;
              }
              span{
                color:#00C1B0;
              }
              // .active{
              //   height:auto;
              //   overflow:visible;
              //   display: block;
              // }
              /deep/ .uni-icons{
                position: absolute;
                right: -20rpx;
                bottom: -10rpx;
              }
            }
            .name{
              color:#ddd;
              margin-left: 5upx;
            }
          }
          .doctor-bottom{
            display: flex;
            align-items: center; 
            margin-top: 15upx;
            margin-left: auto;
            .like{
              display: flex;
              align-items: center; 
              color: #8F8F8F;
              .isLike,.notLike{
                font-size: 28rpx;
                padding-right: 10rpx;
                color: #2D2D2D;
              }
              .notLike {
                color: #999999;
              }
            }
          }
          &:last-child{
            margin-bottom: 0;
          }
        }
      }
    }
  }
}
</style>