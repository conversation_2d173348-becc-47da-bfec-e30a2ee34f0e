<template>
	<view class="live-like">
      <template  v-for="item,idx in imageArr">
        <image :src="item.url" v-if="!item.hidden" class="bubbleico" :key="idx"  :style="{
          left:item.x + 'px',
          top:item.y + 'px'
        }"></image>
      </template>

  </view>
</template>

<script>
let timer = 0;
let ctx = null;
const WIDTH = 90;
const HEIGHT = 200;
const badges = {};
export default {
	name: 'likeLike',
	props:{
		count: {
		  type: Number,
		  value: 0,
		},
		data:{
			type:Object,
			default: function() {
				return {};
			}
		},
    visible2: {
      type: Boolean,
      default: false
    },
	},
	watch:{
		count(n,old){
			console.myLog('检测到更新')
			this.likeChange(n,old)
		},
		// data(){
		// 	console.myLog('检测到更新data',this.data.id);
		// 	this.$set(this, 'queue', {});
		// },
    visible2(n){
      // 关闭
      if(!n){
        // 清空所有飘星
        this.imageArr = [];
        this.imageObject = {};
      }
    }
	},
	data() {
		return {
    //image1,
    imageObject:{},
    imageArr:[],
    image1:'https://lvbao-saas-test.oss-cn-shenzhen.aliyuncs.com/static/image/business/hulu-v2/bg1.png',
    image2:'https://lvbao-saas-test.oss-cn-shenzhen.aliyuncs.com/static/image/business/hulu-v2/bg2.png',
    image3:'https://lvbao-saas-test.oss-cn-shenzhen.aliyuncs.com/static/image/business/hulu-v2/bg3.png',
    image4:'https://lvbao-saas-test.oss-cn-shenzhen.aliyuncs.com/static/image/business/hulu-v2/bg4.png',
    image5:'https://lvbao-saas-test.oss-cn-shenzhen.aliyuncs.com/static/image/business/hulu-v2/bg5.png',
    image6:'https://lvbao-saas-test.oss-cn-shenzhen.aliyuncs.com/static/image/business/hulu-v2/bg6.png',
    image7:'https://lvbao-saas-test.oss-cn-shenzhen.aliyuncs.com/static/image/business/hulu-v2/bg7.png',
    image8:'https://lvbao-saas-test.oss-cn-shenzhen.aliyuncs.com/static/image/business/hulu-v2/bg8.png',
	queue:{}
		};
	},
	mounted(){
	},


	destroyed() {
	  if (timer) {
	    clearTimeout(timer);
	  }
	},
	methods:{
		likeChange(newVal, oldVal) {
		  // if (newVal - oldVal > 0) {
		    this.likeClick();

		  // }
		},

		likeClick() {
		  const anmationData = {
		    id: new Date().getTime(),
		    timer: 0,
		    opacity: 0,
		    pathData: this.generatePathData(),
		    image: [this.image1,this.image2,this.image3,this.image4,this.image5,this.image6,this.image7,this.image8],
		    imageIndex:Math.floor(Math.random() * (4 - 0 + 1)) + 0,
		    factor: {
		      speed: 0.01, // 运动速度，值越小越慢
		      t: 0 //  贝塞尔函数系数
		    }
		  };
		  console.myLog('触发点赞',this.queue)

		  if (Object.keys(this.queue).length > 0) {
		    this.queue[anmationData.id] = anmationData;
		  } else {
		    this.queue[anmationData.id] = anmationData;
		    this.bubbleAnimate();
		  }
		},

		getRandom(min, max) {
		  return Math.random() * (max - min) + min;
		},

		getRandomInt(min, max) {
		  return Math.floor(Math.random() * (max - min + 1)) + min;
		},

		generatePathData() {
      // 200
		  const p0 = { x: -5, y:160  };
		  const p1 = {
		    // x: this.getRandom(20, 30),
		    // y: this.getRandom(200, 150)
        x: -10,
        y: 45
		  };
		  const p2 = {
		    x: 15,
		    y: 65
		  };
		  const p3 = {
		    x: this.getRandom(-25, -40),
		    y: this.getRandom(0, -40),
		    // x: -25,
		    // y: 30
		  };
		  return [p0, p1, p2, p3];
		},

		updatePath(data, factor) {
		  const p0 = data[0]; // 三阶贝塞尔曲线起点坐标值
		  const p1 = data[1]; // 三阶贝塞尔曲线第一个控制点坐标值
		  const p2 = data[2]; // 三阶贝塞尔曲线第二个控制点坐标值
		  const p3 = data[3]; // 三阶贝塞尔曲线终点坐标值

		  const t = factor.t;

		  /*计算多项式系数*/
		  const cx1 = 3 * (p1.x - p0.x);
		  const bx1 = 3 * (p2.x - p1.x) - cx1;
		  const ax1 = p3.x - p0.x - cx1 - bx1;

		  const cy1 = 3 * (p1.y - p0.y);
		  const by1 = 3 * (p2.y - p1.y) - cy1;
		  const ay1 = p3.y - p0.y - cy1 - by1;

		  /*计算xt yt的值 */
		  const x = ax1 * (t * t * t) + bx1 * (t * t) + cx1 * t + p0.x;
		  const y = ay1 * (t * t * t) + by1 * (t * t) + cy1 * t + p0.y;

      // console.log('y',y)
		  return {
		    x,
		    y
		  };
		},
		bubbleAnimate() {
		  console.myLog('触发新增动画',this.imageArr,this.queue)
		  Object.keys(this.queue).forEach(key => {
		    const anmationData = this.queue[+key];
		    const { x, y } = this.updatePath(
		      anmationData.pathData,
		      anmationData.factor
		    );
		    const speed = anmationData.factor.speed;
		    anmationData.factor.t += speed;
          if(!this.imageObject[anmationData.id]){
            this.imageObject[anmationData.id] = this.imageArr.length + '';
            this.imageArr.push({
                x,
                y,
                url:anmationData.image[anmationData.imageIndex],
								get t(){
									return anmationData.factor.t
								}
            })
          }else{
            let idx = this.imageObject[anmationData.id] - 0;
            this.imageArr[idx].x = x;
            this.imageArr[idx].y = y;
          }
		    if (anmationData.factor.t > 1) {
		      delete this.queue[anmationData.id];
          let idx = this.imageObject[anmationData.id] - 0;
          // 从数组中移除元素而不是隐藏
      		this.imageArr.splice(idx, 1);
      		// 更新其他元素的索引映射
      		Object.keys(this.imageObject).forEach(id => {
        		if (this.imageObject[id] > idx) {
        		  this.imageObject[id] = (this.imageObject[id] - 1) + '';
        		}
      		});
      		// 删除当前元素的映射
      		delete this.imageObject[anmationData.id];
		    }
		  });
		  if (Object.keys(this.queue).length > 0 && this.imageArr.length > 0) {
		    timer = setTimeout(() => {
		      this.bubbleAnimate();
		    }, 20);
		  } else {
		    clearTimeout(timer);
				this.imageArr.length = 0;
		    this.imageObject = {};
		    // ctx.draw(); // 清空画面
		  }
		}
	}
};
</script>

<style scoped lang="scss">
  .cs01{
    position: absolute;
    width: 5px;
    height: 5px;
    background-color: yellow;
    left: -10px;
    top: 75px;
  }
  .cs02{
    position: absolute;
    width: 5px;
    height: 5px;
    background-color: yellow;
    right: -10px;
    top: 95px;
  }
  .cs03{
    position: absolute;
    width: 5px;
    height: 5px;
    background-color: yellow;
    left: 15px;
    top: 30px;
  }
  .live-like-box{
     // width: 90px;
     // height: 200px;
     // position: absolute;
     // left: 0;
     // transform: t;
  }
	.live-like {
	    position: absolute;
	    right: 0;
	    bottom: 0px;
      z-index: 999;
      // background-color: yellow;
      // width: 90px;
      // width: 44upx;
      left: 0;
      height:200px;
      // transform: translateX(50%);
      pointer-events: none;
      .bubbleico{
        width: 90upx;
        height: 90upx;
        position: absolute;
        animation:bubble_big .5s linear 1 forwards;
      }

      @keyframes bubble_big{
        0% {
          transform: scale(.5);
        }
        80% {
          transform: scale(1.3);
          opacity: 1;
        }
        100% {
          transform: scale(.5);
          opacity: 0.5;
        }
      }
	}
</style>
