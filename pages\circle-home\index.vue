<template>
  <page>
    <view slot="content" class="body-main">
      <view class="my-data">
        <uni-nav-bar
          v-if='!IntegrationData.backTaskFlag'
          @clickLeft="back"
          color="black"
          :border="false"
          left-icon="left"
          :fixed="false"
          title='交流'
          statusBar
          left-width="48rpx"
          right-width="48rpx"
          backgroundColor="rgba(0,0,0,0)"
        >
        </uni-nav-bar>
        <uni-nav-bar
          v-else
          color="#1d2029"
          :border="false"
          @clickLeft="back"
          :showBtnsLeft='!IntegrationData.backTaskFlag'
          :showBtnsRight='false'
          :fixed="false"
          title='交流'
          statusBar
          left-width="48rpx"
          right-width="48rpx"
          backgroundColor="rgba(0,0,0,0)"
        >
          <integration-time-er :backTaskFlag='IntegrationData.backTaskFlag'></integration-time-er>
        </uni-nav-bar>
      </view>
      <scroll-refresh
        style="height: 100%"
        :isShowEmptySwitch="true"
        :fixed="false"
        :isAbsolute="false"
        :up="upOption"
        :down="downOption"
        @returnFn="returnFn"
        @scrollInit="scrollInit"
      >
        <view class="main-content">
          <view 
            class="gourd-exchange-head-title"
            :style="{
              'background-image':(isShowBanner || !isShowDataList) ? 'url('+ file_ctx +'static/image/business/hulu-v2/gourd-exchange-head-title.png)' : 'url('+ file_ctx +'static/image/business/hulu-v2/gourd-exchange-head-no-banner.png)',
              'height': (isShowBanner || !isShowDataList) ? '503.66rpx' : '406rpx'
            }"
          >
            <view class="main-search">
              <search :hotSearchList="hotSearchList" :fixed="true" top="88" v-model="regForm.search"></search>
            </view>
            <!-- 存在关注圈子 -->
            <view
              class="main-tabs"
            >
            <view v-if="isShowDataList">
              <view class="tabs-all-btn" @click="handleClickTabsAll">
                <view class="text">全</view>
                <view class="text">部</view>
                <image class="gourd-tabs-all" mode="aspectFit" :src="file_ctx + 'static/image/business/hulu-v2/icon-gourd-tabs-all-bg.png'"></image>
              </view>
              <circle-tabs-sticky
                :overflowX="true"
                v-model="circleCurIndex"
                :fixed="false"
                :isShow="false"
                :bdb="false"
                :tabs="followedList"
                @change="handleChangeTab"
                @clickMore="navtoGo('CircleMoreIndex')"
                class="my-circle-tabs-sticky"
              ></circle-tabs-sticky>
            </view>
            <view v-else>
              <view class="tabs-sticky-empty">
                <view class="empty-l">
                  <image class="empty-img" :src="file_ctx + 'static/image/business/hulu-v2/circle-tab-not-followed.png'" mode="aspectFit"></image>
                  <view class="empty-box">
                    <view class="title">您还未关注圈子</view>
                    <view class="info">去探索自己感兴趣的圈子吧</view>
                  </view>
                </view>
                <view class="empty-r" @click="handleClickTabsAll"><button>去关注</button></view>
              </view>
              <view class="empty-hot-chat">
                <image class="hot-chat-bg" mode="aspectFit" :src="file_ctx + 'static/image/business/hulu-v2/icon-hot-chat-bg.png'"></image>
                他们正在热聊中...
              </view>
            </view>
            </view>
          </view>
          <view class="main-list" :style="{ top:isShowBanner ? '-270rpx' : '-142rpx' }">
            <banner-ads v-show="isShowBanner" @informBanner="informBanner" ref="bannerAdsRef" class="banner-ads" :query-params="{useType: 7,circleClassifyId: circleInfo.id}" height="208rpx" />
            <!-- 存在关注圈子 -->
            <view class="main-tabs-sticky">
              <tabs-sticky
                :fontBigger="true"
                :bdb="false"
                :fixed="false"
                :overflowX="true"
                v-model="curIndex"
                :tabs="tabs"
                @change="changeTab"
              ></tabs-sticky>
            </view>
            <template v-for="(item, index) in tabs">
              <view class="main-list-content" :key="item.id">
                <!-- 帖子 -->
                <nui-list 
                  v-if="
                    (curIndex == 0 && index === 0) ||
                    (curIndex == 1 && index === 1) ||
                    (curIndex == index && item.contentType === 1)
                  "
                  :indexlist="indexlist"
                  :posts-params="postsParams"
                  :isShowGambit="true"
                  @cateClick="cateClick"
                ></nui-list>
                <!-- 直播 -->
                <video-item
                  :list="videolist"
                  :is-show-empty="false"
                  v-else-if="curIndex == index && item.contentType === 2"
                ></video-item>
              </view>
            </template>
          </view>
        </view>
      </scroll-refresh>
      <disclaimers :updatecount='disclaimersUpdateCount'></disclaimers>
      <uni-popup ref="circlePopup" class="my-uni-popup" type="top" :mask-click="false">
        <view class="my-popup">
          <!-- <view class="attention-circle-mask"></view> -->
          <view class="attention-circle">
            <view class="circle-head">
              <view class="title">我关注的圈子</view>
              <template>
                <view class="edit" v-if="isEdit" @click="handleClickEditSave">编辑</view>
                <view class="edit" v-else @click="handleClickEditSave">保存</view>
              </template>
            </view>
            <view class="circle-bott" v-if="isShowDataList">
              <view class="circle-item" v-for="(item,index) in followedList" :key="item.id" @tap.stop="handleUnfollow(item)">
                <view class="circle-item-img">
                  <image :src="file_ctx + item.logoPath" mode="aspectFit"></image>
                </view>
                <view class="circle-item-name">{{item.name}}</view>
                <image v-if="!isEdit" class="circle-item-add" :src="file_ctx + 'static/image/business/hulu-v2/icon-gourd-circle-delete.png'" mode="aspectFit"></image>
              </view>
            </view>
            <view class="not-yet-attention" v-else>暂未关注，可在更多圈子中添加</view>
          </view>
          <view class="more-circle">
            <view class="title">更多圈子</view>
            <view class="circle-bott">
              <view class="circle-item" v-for="(item,index) in unfollowedList" :key="item.id" @tap.stop="handleFollow(item)">
                <view class="circle-item-img">
                  <image :src="file_ctx + item.logoPath" mode="aspectFit"></image>
                </view>
                <view class="circle-item-name">{{item.name}}</view>
                <image class="circle-item-add" :src="file_ctx + 'static/image/business/hulu-v2/icon-gourd-circle-add.png'" mode="aspectFit"></image>
              </view>
            </view>
          </view>
          <view class="circle-pack-up" @click="handleClickPackUp">点击收起<image class="circle-pack-up-img" mode="aspectFit" :src="file_ctx + 'static/image/business/hulu-v2/icon-circle-pack-up.png'"></image></view>
        </view>
      </uni-popup>
      <!-- 在线客服 -->
      <customerService v-if="isShowConfig" ref="customerService"></customerService>
    </view>
  </page>
</template>

<script>
import { mapState } from 'vuex'
import circleTabsSticky from './components/circle-tabs-sticky'
import search from '@/components/basics/search-v1/index.vue'
import nuiList from '@/components/community/nui-list/nui-list.vue'
import TabsSticky from '@/components/basics/tabs-sticky-v3'
import videoItem from '../index/components/videoItem/index.vue'
import { isDomainUrl } from '@/utils/index.js'
import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'
import disclaimers from '@/components/basics/disclaimers/index.vue'
import bannerAds from '@/components/basics/banner-ads/index.vue'
import uniPopup from '@/components/uni/uni-popup/index.vue'
import customerService from '@/components/basics/customerService/index.vue'
export default {
  name: 'CircleHome',
  components: {
    circleTabsSticky,
    TabsSticky,
    search,
    nuiList,
    videoItem,
    disclaimers,
    bannerAds,
    uniNavBar,
    uniPopup,
    customerService
  },
  data() {
    return {
      disclaimersUpdateCount:0,
      file_ctx: this.file_ctx,
      $validate: this.$validate,
      regForm: {
        name: ''
      },
      circleTabs: [],
      circleCurIndex: 0,
      curIndex: 0,
      tabs: [
        { name: '推荐', id: 1 },
        // {name: '名医热点', id: 2},
        { name: '精华', id: 3 },
      ],
      indexlist: [],
      mescroll: null, // mescroll实例对象
      downOption: {
        auto: false // 不自动加载
      },
      upOption: {
        auto: false, // 不自动加载
        empty: {
          top: 0,
          zIndex: 999,
        }
      },
      $constant: this.$constant,
      videolist: [],
      hotSearchList:[], // 热搜列表
      timer:null,
      isShowBanner:true,
      isEdit:true,
      isShowDataList:true,
      followedList:[], //已关注列表
      unfollowedList:[], //未关注列表
      operations: new Map(), // 操作记录：key=用户ID, value=操作类型
      timestampLoad:'',
      isShowConfig: false,
    }
  },
  computed: {
    ...mapState('user', {
      codeUserInfo: state => state.codeUserInfo, // 当前登录用户信息
      curSelectUserInfo: state => state.curSelectUserInfo, // 当前选中的用户信息
      curSelectUserInfoId: state => state.curSelectUserInfoId, // 当前选中的档案的ID
      recordUserInfo: state => state.recordUserInfo,
      accountId: state => state.accountId
    }),
    circleInfo() {
      return this.followedList[this.circleCurIndex]
    },
    postsParams() {
      return {
        circleClassifyId: (this.$validate.isNull(this.followedList) || !this.followedList[this.circleCurIndex]) ? '' : this.followedList[this.circleCurIndex]?.id,
        medicineClassifyId: (this.curIndex == 0 || !this.tabs[+this.curIndex]) ? null : this.tabs[+this.curIndex].id
      }
    },
  },
  onLoad() {
    this.loadData(true)
    this.init()
    this.searchbuzzwordQueryList()
    this.timer = setInterval(this.searchbuzzwordQueryList, 600000);  //每十分钟执行一次 轮询获取搜索词
    this.getCustomerserviceprofilesQueryNoParametersOne()
  },
  onShow() {
    // #ifdef MP-WEIXIN
    this.setTabBarIndex(1);
    // #endif
    this.disclaimersUpdateCount += 1;
  },
  watch: {
    curIndex: {
      handler() {
        this.indexlist = []
        this.init()
      }
    },
  },
  methods: {
    // 在线客服
    async getCustomerserviceprofilesQueryNoParametersOne(){
      const res = await this.$api.drugBook.getCustomerserviceprofilesQueryNoParametersOne()
      if(res.data !==""){
        res.data.enableStatus == 1 && res.data.types.includes('2') ? this.isShowConfig = true : this.isShowConfig = false
      }
    },
    async handleChangeTab(index){
      if(this.followedList[index]?.id){
        this.indexlist = []
        this.changeTab(0)
        this.init()
        this.$common.setKeyVal('business', 'circleClassifyInfoObj', this.circleInfo)
        if(this.circleInfo?.id){
          this.$nextTick(()=>{
            // #ifndef MP-ALIPAY
            this.$refs.bannerAdsRef.init()
            // #endif
          })
        }
      }
    },
    // 加载初始数据
    async loadData(flag = false) {
      try {
        const [followedRes, unfollowedRes] = await Promise.all([
          this.$ext.circleclassify.curCircleclassifyQuerySubscribeList(),
          this.$api.circleclassify.circleclassifyQueryUnsubscribeList({accountId: this.accountId})
        ])
        if (followedRes.data.length) {
          this.isShowDataList = true
          this.followedList = followedRes.data
          if(flag && this.circleInfo?.id){
            // #ifndef MP-ALIPAY
            this.$nextTick(()=>{
              this.$refs.bannerAdsRef.init()
            })
            // #endif
          }
        } else {
          this.isShowBanner = false 
          this.isShowDataList = false
          this.followedList = followedRes.data
        }
        if (unfollowedRes.data.length) this.unfollowedList = unfollowedRes.data
        this.operations.clear()
      } catch (e) {
        console.error('数据加载失败:', e)
      }
    },
    
    handleClickEditSave(){
      this.isEdit = !this.isEdit
    },
    // 处理关注操作
    handleFollow(item) {
      // 从未关注列表移除
      this.unfollowedList = this.unfollowedList.filter(i => i.id !== item.id)
      // 添加到已关注列表
      this.followedList = [...this.followedList,item]
      if(this.followedList.length)this.isShowDataList = true
      // 记录操作（新增关注或覆盖取消操作）
      this.operations.set(item.id, 'follow')
    },

    // 处理取消关注
    handleUnfollow(item) {
      if(!this.isEdit){
        // 从已关注列表移除
        this.followedList = this.followedList.filter(i => i.id !== item.id)
        // 添加到未关注列表
        this.unfollowedList = [...this.unfollowedList,item]
        if(!this.followedList.length)this.isShowDataList = false
        // 记录操作（取消关注或覆盖关注操作）
        this.operations.set(item.id, 'unfollow')
      }
    },

     // 处理关闭操作
    async handleClickPackUp() {
      if(!this.isEdit && this.followedList.length)return this.$uniPlugin.toast('请先保存关注的圈子！')
      if(this.operations.size > 0){
        uni.showToast({
          title: '加载中...',
          icon: 'none'
        })
        await this.submitChanges()
        if(this.followedList.length){
          this.handleChangeTab(this.followedList.length && this.followedList.indexOf(this.followedList[0]))
        } else {
          this.init()
        }
        uni.hideToast()
      }
      this.$refs.circlePopup.close()
    },

    // 提交变更
    async submitChanges() { 
      try{
        const followIds = []
        const unfollowIds = []

        this.operations.forEach((operation, id) => {
          if (operation === 'follow') {
            followIds.push(id)
          } else {
            unfollowIds.push(id)
          }
        })
        
        const requests = []
        if (unfollowIds.length) {
          requests.push(this.$api.circleclassify.circleclassifyUnsubscribe({accountId: this.accountId,circleClassifyIds:unfollowIds}))
        }
        if (followIds.length) {
          requests.push(this.$api.circleclassify.circleclassifySubscribe({accountId: this.accountId,circleClassifyIds:followIds}))
        }

        await Promise.all(requests)
        await this.loadData()
      } catch (e) {
        // console.error('操作提交失败:', e)
        await this.loadData()
      } finally {
        this.operations.clear()
      }
    },

    handleClickTabsAll(){ //点击全部 弹窗
      this.loadData()
      this.$refs.circlePopup.open()
    },
    // 是否显示banner
    informBanner(val){
      this.isShowBanner = val
    },
    // #ifdef MP-WEIXIN
    handleClickTrack(data){
      let arr = [
        {name:'情感圈',index:1},{name:'消化圈',index:2},{name:'皮肤圈',index:3},
        {name:'骨科圈',index:4},{name:'妇科圈',index:5},{name:'抗癌圈',index:6},
        {name:'育儿圈',index:7},{name:'呼吸圈',index:8},{name:'肝病圈',index:9},
        {name:'眼科圈',index:10},{name:'男科圈',index:11},{name:'痛风圈',index:12},
        {name:'糖尿病圈',index:13},{name:'口腔圈',index:14},{name:'心脑血管圈',index:15},
        {name:'生殖圈',index:16},{name:'养发圈',index:17},{name:'综合区圈',index:18},
        {name:'养生圈',index:19},{name:'肾病圈',index:20},
      ]
      const currentIndex = arr.findIndex(item=>item.name == this.followedList[this.circleCurIndex]?.name)
      getApp().globalData.sensors.track("OperationClick",
        {
          'page_name':'交流',
          'first_operation_name' : this.followedList[this.circleCurIndex]?.name,
          'second_operation_name' : data?.name,
          'operation_floor' : '1',
          'operation_type':'导航栏',
          'operation_id' : data?.id+'',
          'is_link_third' : data?.is_link_third || '否',
          'target_url' : '',
          'position_rank' : arr[currentIndex]?.index,
        }
      ) 
    },
    // #endif
    back(){
      this.$navto.back()
    },
    // 搜索栏热度词
    async searchbuzzwordQueryList(){
      const res = await this.$api.postmessage.searchbuzzwordQueryList({
        putawayStatus:1
      })
      this.hotSearchList = [{word:'搜问题'},...res.data]
    },
    async getMedicineclassifyQueryList() {
      if (this.$validate.isNull(this.followedList)) return
      const res = await this.$api.medicineclassify.medicineclassifyQueryList({
        classifyAreaType: 2,
        internalClassifyId: this.followedList[this.circleCurIndex]?.id || '',
        classifyPutawayStatus: 1
      })
      this.tabs = [
        { name: '推荐', id: 1 },
        // {name: '名医热点', id: 2},
        { name: '精华', id: 3 },
      ].concat(res.data)
    },
    clickApp(data) {
      const { type, path, appId, name } = data
      switch (type) {
        // 小程序
        case 1:
          this.$uniPlugin.navigateToMiniProgram({
            appId,
            path,
            envVersion: 'release',
            extraData: {}
          }, (res) => {
            console.log(res)
          }, (err) => {
            console.log(err)
          })
          break
        // H5
        case 2:
          this.$navto.push('WebHtmlView', { src: path, title: name })
          break
        default:
      }
    },

    filterTabs() {
      // 是否开启名医热点
      if (this.circleInfo && this.circleInfo.isOpenHotspot === 2) {
        this.tabs.filter(item => item.id !== 2)
      }
    },
    cateClick(data) {
      this.navtoGo('Circle', { cid: data.circleClassifyId })
    },
    getData(){
      let today = new Date();
      //日期
      let DD = String(today.getDate()).padStart(2, '0'); // 获取日
      let MM = String(today.getMonth() + 1).padStart(2, '0'); //获取月份，1 月为 0
      let yyyy = today.getFullYear(); // 获取年
      // 时间
      let hh = String(today.getHours()).padStart(2, '0');       //获取当前小时数(0-23)
      let mm = String(today.getMinutes()).padStart(2, '0');     //获取当前分钟数(0-59)
      let ss = String(today.getSeconds()).padStart(2, '0');     //获取当前秒数(0-59)
      today = yyyy + '-' + MM + '-' + DD + ' ' + hh + ':' + mm + ':' + ss;
      return today
    },
    init() {
      this.$nextTick(() => {
        this.mescroll.triggerDownScroll()
      })
    },

    scrollInit(scroll) {
      scroll.optUp.page.num = 1
      scroll.optUp.page.size = 10
      this.mescroll = scroll
    },
    postReturnFn(obj) {
      const that = this
      setTimeout(async function () {
        if(obj.pageNum === 1) that.timestampLoad = that.getData()
        let params = {
          current: obj.pageNum,
          size: obj.pageSize,
          condition: {
            circleClassifyId: that.$validate.isNull(that.followedList) ? '' : that.followedList[that.circleCurIndex]?.id,
            accountId: that.accountId,
            medicineClassifyId: that.curIndex == 0 ? null : that.tabs[+that.curIndex].id,
            currentDate : that.timestampLoad
          }
        }
        if(!params.condition.circleClassifyId)delete params.condition.circleClassifyId
        let res = null
        if(that.tabs[that.curIndex].id === 3){
          // 精华
          res = await that.$ext.community.postmessageQueryEssencePage(params)
        } else {
          // 推荐
          res = await that.$ext.community.postmessageQueryRecommendPage(params)
        }
        let data = res.data.records.map(item=>{
          return{
            ...item,
            topicIdsArr : item.topicIds && JSON.parse(item.topicIds) || []
          }
        }) || []
        if (obj.pageNum === 1) {
          that.indexlist = []
        }
        that.indexlist = [...that.indexlist, ...data]
        obj.successCallback && obj.successCallback(data)
      }, that.$constant.noun.scrollRefreshTime)
    },
    videoReturnFn(obj) {
      const that = this
      function queryPage(pageNum, pageSize, fn) {
        const param = {
          current: pageNum,
          size: pageSize,
          condition: {
            // terminalType: 1
            // 活动状态：1-草稿，2-预告，3-直播中，4-直播结束，5-回放，6-下架
            // activityStatus: '2,3,5',
            activityStatusList:[2,3,5],
            showType:1,
            businessType: that.tabs[+that.curIndex].liveBusinessType || 7,// 直播活动
            medicineClassifyId: that.curIndex == 0 ? null : that.tabs[+that.curIndex].id,
            // orderByActivityStatus:3
            orderByActivityStatus:'5,2,3'
          },
          descs:"createTime"
        }

        that.$api.cloudClassroom.getMeetingQueryPage(param).then(res => {
          if (res && res.data.records) {
            let list = res.data.records
            if (list.length > 0) {
              for (const a in list) {
                const data = list[a]
                data.startTimeText = that.$common.formatDate(new Date(data.startTime), 'yyyy-MM-dd HH:mm:ss');
                data.endTimeText = that.$common.formatDate(new Date(data.endTime), 'yyyy-MM-dd HH:mm:ss');
                data.coverPathsUrl = isDomainUrl(data.coverPaths)
              }
            }
            fn(res.data.records.length > 0 ? res.data.records : [])
          }
        })
      }
      queryPage(obj.pageNum, obj.pageSize, (data) => {
        if (obj.pageNum === 1) {
          that.videolist = []
        }
        that.videolist = that.videolist.concat(data)
        obj.successCallback && obj.successCallback(data)
      })
    },
    returnFn(obj) {
      if (this.$validate.isNull(this.followedList) || this.tabs[+this.curIndex].contentType !== 2) {
        this.postReturnFn(obj)
      } else if (this.tabs[+this.curIndex].contentType === 2) {
        this.videoReturnFn(obj)
      }

    },
    changeTab(index) {
      this.curIndex = index
      // #ifdef MP-WEIXIN
      this.handleClickTrack(this.tabs[index])
      // #endif
    },
    navtoGo(url, obj = {}) {
      this.$navto.push(url, obj)
    }
  }
}
</script>
<style lang="scss" scoped>
.body-main {
  position: relative;
  background-color: #F4F6FA;
  // #ifdef MP-WEIXIN
  height: calc(100vh - 56px - env(safe-area-inset-bottom));
  // #endif
  // #ifndef MP-WEIXIN
  height: 100%;
  // #endif
  /deep/ .mescroll-empty-box{
    background: #fff !important;
  }
  .my-data{
    width: 100%;
    height: 176rpx;
    background-image: url($imgUrl + '/business/hulu-v2/gourd-exchange-head.png');
    background-size: 100%;
  }
  .gourd-exchange-head-title{
    width: 100%;
    height: 503.66rpx;
    background-image: url($imgUrl + '/business/hulu-v2/gourd-exchange-head-title.png');
    background-size: 100%;
  }
  .main-search{
    padding:0 32rpx;
  }
  .main-tabs {
    position: relative;
    margin-top: 32rpx;
    .tabs-all-btn{
      position: absolute;
      right: 0;
      bottom: 0;
      width: 88rpx;
      height: 118rpx;
      padding: 18rpx 24rpx 8rpx 40rpx;
      box-sizing: border-box;
      background-size: 100%;
      background-image: url($imgUrl + '/business/hulu-v2/gourd-tabs-all.png');
      z-index: 9999;
      .text{
        font-size: 24rpx;
        color: #2D2F38;
      }
      .gourd-tabs-all{
        display: flex;
        width: 24rpx;
        height: 24rpx;
      }
    }
    /deep/.my-circle-tabs-sticky{
      .tabs-sticky{
        padding: 0 90rpx 0 32rpx;
      }
    }
    .tabs-sticky-empty{
      display: flex;
      justify-content: space-between;
      padding: 24rpx;
      margin: 0 32rpx;
      background: linear-gradient( 90deg, #0BCD99 0%, #00B484 100%);
      border-radius: 16rpx;
      .empty-l{
        display: flex;
        align-items: center;
        .empty-img{
          display: flex;
          width: 118rpx;
          height: 96rpx;
          margin-right: 24rpx;
        }
        .empty-box{
          .title{
            font-size: 30rpx;
            color: #FFFFFF;
            font-weight: 600;
            margin-bottom: 6rpx;
          }
          .info{
            font-size: 26rpx;
            color: #FFFFFF;
          }
        }
      }
      .empty-r{
        display: flex;
        align-items: center;
        button{
          display: flex;
          align-items: center;
          justify-content: center;
          height: 64rpx;
          background: #FFFFFF;
          border-radius: 32rpx;
          font-weight: 600;
          font-size: 30rpx;
          color: #00B484;
        }
      }
    }
    .empty-hot-chat{
      display: flex;
      align-items: center;
      font-size: 32rpx;
      color: #1D2029;
      font-weight: 600;
      padding: 40rpx 32rpx 0;
      background-color: #fff;
      .hot-chat-bg{
        display: flex;
        width: 32rpx;
        height: 32rpx;
        margin-right: 12rpx;
      }
    }
  }
  .main-list {
    position: relative;
    top: -150rpx;
    border-radius: 20rpx;
    /deep/.banner-ads{
      .banner-main{
        padding: 20rpx 32rpx 0;
        margin-bottom: 0;
      }
    }
    /deep/.main-tabs-sticky{
      position: sticky;
      top: 0;
      z-index:999;
      .tabs-sticky{
        padding:32rpx 32rpx 0;
      }
    }
    /deep/.tabs-sticky-body{
      padding: 0;
    }

  }
  .main-list-content {
    position: relative;
  }
  .circle-bott{
    display: flex;
    flex-wrap: wrap;
    padding-top: 14rpx;
    .circle-item{
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 196rpx;
      height: 88rpx;
      border-radius: 16rpx;
      border: 2rpx solid #D2E5E0;
      margin: 0 32rpx 32rpx 0;
      box-sizing: border-box;
      .circle-item-img{
        display: flex;
        width: 40rpx;
        height: 40rpx;
        margin-right: 4rpx;
        filter: grayscale(1);
        image{
          width: 100%;
          height: 100%;
        }
      }
      .circle-item-name{
        font-size: 26rpx;
        color: #4E5569;
      }
      .circle-item-add{
        position: absolute;
        right: -12rpx;
        top: -16rpx; 
        display: flex;
        width: 40rpx;
        height: 40rpx;
      }
      &:nth-child(3n){
        margin-right: 0;
      }
    }
  }
  .not-yet-attention{
    display: flex;
    align-items: center;
    justify-content: center;
    height: 88rpx;
    border-radius: 16rpx;
    border: 2rpx dashed #85AFA4;
    font-size: 26rpx;
    color: #1D2029;
    background-color: #fff;
    margin: 32rpx 0 40rpx;
  }
  /deep/.my-uni-popup{
    .uni-popup{
      .uni-popup__wrapper{
        .uni-popup__wrapper-box{
          max-height: 1700rpx;
        }
      }
    }
  }
  .my-uni-popup{
    .my-popup{
      position: relative;
      background-color: #fff;
      padding: 166rpx 42rpx 80rpx;
      .attention-circle-mask{
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 162rpx;
        background: linear-gradient( 180deg, #F1FBF8 0%, #FFFFFF 100%);
      }
      .attention-circle{
        .circle-head{
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 18rpx 0;
          .title{
            font-size: 28rpx;
            color: #2D2F38;
            font-weight: 600;
          }
          .edit{
            font-size: 28rpx;
            color: #00B484;
            background: #DFFBF4;
            border-radius: 16rpx;
            border: 2rpx solid #00B484;
            padding: 6rpx 32rpx;
          }
        }
      }
      .more-circle{
        .title{
          font-weight: 600;
        }
        .circle-bott{
          margin-top: 16rpx;
        }
      }
      .circle-pack-up{
        position: absolute;
        bottom: 32rpx;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        width: 100%;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
        color: #1D2029;
        .circle-pack-up-img{
          display: flex;
          width: 28rpx;
          height: 28rpx;
        }
      }
    }
  }
}

</style>
