<template>
  <view class="loadingbox">
    <image :src="file_ctx+'static/image/business/hulu-v2/loading.svg'" class="loading"></image>
    <view class="txt">
      加载数据中
    </view>
  </view>
</template>

<script>

  export default {
    name:"titleLoading",
    data(){
    return {
      file_ctx: this.file_ctx,
    }
    },
    methods:{

    }

  }
</script>

<style lang="scss" scoped>
  .loadingbox{
    display: flex;
    height: 300upx;
    width: 100%;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }
  .loading{
    width: 200upx;
    height: 200upx;
    animation:loading 1s infinite;
  }
  .txt{
    margin-top: 20rpx;
        color: #7c7c7c;
  }
  @keyframes loading {
    from {transform: rotate(0);}
    to {transform: rotate(360deg);}
  }
</style>
