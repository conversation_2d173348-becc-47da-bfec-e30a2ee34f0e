<template>
  <view class="">
    <!-- 顶部占位 -->
    <view :style="{height:tabHeight+'px'}"></view>
    <uni-icons :color="themeColor" type="back" size='24' />
  </view>
</template>

<script>
  export default{
    props:{
      themeColor:{
        type:String,
        default:'white'
      }
    },
    data(){
      return {
        tabHeight:20
      }
    },
    mounted() {
      console.log('触发子组件');
    },
    created() {
      console.log('触发子组件');
    }
  }
</script>

<style>
</style>
