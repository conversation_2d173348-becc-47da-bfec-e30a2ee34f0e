
/**
 * 系统路由
 */
 export default [
  // 定位页面
  {
    path: '/modules/common/positioning/positioning',
    name: 'CommonPositioning',
    meta: {
      index: 2,
      headerObj: {
        title: '定位',
        isShow: true
      }
    }
  },
  // 定位poi记录页面
  {
    path: '/modules/common/positioning/index',
    name: 'CommonPositioningPoi',
    meta: {
      index: 2,
      headerObj: {
        title: '选择地址',
        isShow: true
      }
    }
  },
  // 定位poi搜索页面
  {
    path: '/modules/common/positioning/positioning-search',
    name: 'CommonPositioningSearch',
    meta: {
      index: 2,
      headerObj: {
        title: '选择地址',
        isShow: true
      }
    }
  },
  // 系统搜索
  {
    path: '/modules/common/system-search/index',
    name: 'CommonSystemSearch',
    meta: {
      index: 2,
      headerObj: {
        title: '搜索',
        isShow: true
      }
    }
  },
  // 系统搜索商品
  {
    path: '/modules/common/system-search/search-data',
    name: 'CommonSystemSearchData',
    meta: {
      index: 2,
      headerObj: {
        title: '搜索',
        isShow: true
      }
    }
  },
  // {
  //   // 管理
  //   path: '/pages/store/index',
  //   name: 'Home',
  //   meta: {
  //     index: 1,
  //     headerObj: {
  //       title: '管理'
  //     }
  //   }
  // }, 
  {
    // 切换角色
    path: '/modules/common/switch-roles/index',
    name: 'SwitchRoles',
    meta: {
      index: 2,
      headerObj: {
        title: '切换角色',
        isShow: true
      }
    }
  },
  {
    path: '/pages/news/index',
    name: 'News',
    meta: {
      index: 1,
      headerObj: {
        title: '消息'
      }
    }
  },
  {
    path: '/modules/common/pc-view/index',
    name: 'PCView',
    meta: {
      index: 2,
      headerObj: {
        title: '登录PC后台',
        isShow: true
      }
    }
  },
  // web-view通用第三方页面
  {
    path: '/modules/common/web-html-view/index',
    name: 'WebHtmlView',
    meta: {
      index: 2,
      headerObj: {
        title: '',
        isShow: true
      }
    }
  },
  // web-view通用第三方页面
  {
    path: '/modules/common/instruction-web-html-view/index',
    name: 'InstructionWebHtmlView',
    meta: {
      index: 2,
      headerObj: {
        title: '',
        isShow: true
      }
    }
  },
  {
    // 选择人员（多选）
    path: '/modules/common/list/phone-multi-select-list',
    name: 'CommonPhoneMultiSelectList',
    meta: {
      index: 2,
      headerObj: {
        title: '选择人员',
        isShow: true
      }
    }
  },
  // 第三方小程序跳转中转页
  {
    path: '/modules/common/app-jump-view/index',
    name: 'AppJumpView',
    meta: {
      index: 2,
      headerObj: {
        title: '',
        isShow: true
      }
    }
  }
]
