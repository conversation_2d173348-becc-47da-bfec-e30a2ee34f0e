!function(){"use strict";function e(t){"@babel/helpers - typeof";return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)}function t(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function r(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e}function i(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&a(e,t)}function o(e){return(o=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function a(e,t){return(a=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function s(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(e){return!1}}function l(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function u(e,t){return!t||"object"!=typeof t&&"function"!=typeof t?l(e):t}function c(e){var t=s();return function(){var n,r=o(e);if(t){var i=o(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return u(this,n)}}function p(e,t){return t={exports:{}},e(t,t.exports),t.exports}var f="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},d=p(function(t){!function(e,n){t.exports=e.document?n(e,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return n(e)}}("undefined"!=typeof window?window:f,function(t,n){function r(e,t){t=t||re;var n=t.createElement("script");n.text=e,t.head.appendChild(n).parentNode.removeChild(n)}function i(e){var t=!!e&&"length"in e&&e.length,n=ge.type(e);return"function"!==n&&!ge.isWindow(e)&&("array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e)}function o(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}function a(e,t,n){return ge.isFunction(t)?ge.grep(e,function(e,r){return!!t.call(e,r,e)!==n}):t.nodeType?ge.grep(e,function(e){return e===t!==n}):"string"!=typeof t?ge.grep(e,function(e){return le.call(t,e)>-1!==n}):Le.test(t)?ge.filter(t,e,n):(t=ge.filter(t,e),ge.grep(e,function(e){return le.call(t,e)>-1!==n&&1===e.nodeType}))}function s(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}function l(e){var t={};return ge.each(e.match(_e)||[],function(e,n){t[n]=!0}),t}function u(e){return e}function c(e){throw e}function p(e,t,n,r){var i;try{e&&ge.isFunction(i=e.promise)?i.call(e).done(t).fail(n):e&&ge.isFunction(i=e.then)?i.call(e,t,n):t.apply(void 0,[e].slice(r))}catch(e){n.apply(void 0,[e])}}function f(){re.removeEventListener("DOMContentLoaded",f),t.removeEventListener("load",f),ge.ready()}function d(){this.expando=ge.expando+d.uid++}function h(e){return"true"===e||"false"!==e&&("null"===e?null:e===+e+""?+e:Re.test(e)?JSON.parse(e):e)}function v(e,t,n){var r;if(void 0===n&&1===e.nodeType)if(r="data-"+t.replace(Ie,"-$&").toLowerCase(),n=e.getAttribute(r),"string"==typeof n){try{n=h(n)}catch(i){}Fe.set(e,t,n)}else n=void 0;return n}function g(e,t,n,r){var i,o=1,a=20,s=r?function(){return r.cur()}:function(){return ge.css(e,t,"")},l=s(),u=n&&n[3]||(ge.cssNumber[t]?"":"px"),c=(ge.cssNumber[t]||"px"!==u&&+l)&&Be.exec(ge.css(e,t));if(c&&c[3]!==u){u=u||c[3],n=n||[],c=+l||1;do o=o||".5",c/=o,ge.style(e,t,c+u);while(o!==(o=s()/l)&&1!==o&&--a)}return n&&(c=+c||+l||0,i=n[1]?c+(n[1]+1)*n[2]:+n[2],r&&(r.unit=u,r.start=c,r.end=i)),i}function m(e){var t,n=e.ownerDocument,r=e.nodeName,i=Xe[r];return i?i:(t=n.body.appendChild(n.createElement(r)),i=ge.css(t,"display"),t.parentNode.removeChild(t),"none"===i&&(i="block"),Xe[r]=i,i)}function y(e,t){for(var n,r,i=[],o=0,a=e.length;o<a;o++)r=e[o],r.style&&(n=r.style.display,t?("none"===n&&(i[o]=Me.get(r,"display")||null,i[o]||(r.style.display="")),""===r.style.display&&Ue(r)&&(i[o]=m(r))):"none"!==n&&(i[o]="none",Me.set(r,"display",n)));for(o=0;o<a;o++)null!=i[o]&&(e[o].style.display=i[o]);return e}function b(e,t){var n;return n="undefined"!=typeof e.getElementsByTagName?e.getElementsByTagName(t||"*"):"undefined"!=typeof e.querySelectorAll?e.querySelectorAll(t||"*"):[],void 0===t||t&&o(e,t)?ge.merge([e],n):n}function x(e,t){for(var n=0,r=e.length;n<r;n++)Me.set(e[n],"globalEval",!t||Me.get(t[n],"globalEval"))}function k(e,t,n,r,i){for(var o,a,s,l,u,c,p=t.createDocumentFragment(),f=[],d=0,h=e.length;d<h;d++)if(o=e[d],o||0===o)if("object"===ge.type(o))ge.merge(f,o.nodeType?[o]:o);else if(Je.test(o)){for(a=a||p.appendChild(t.createElement("div")),s=(Ge.exec(o)||["",""])[1].toLowerCase(),l=Qe[s]||Qe._default,a.innerHTML=l[1]+ge.htmlPrefilter(o)+l[2],c=l[0];c--;)a=a.lastChild;ge.merge(f,a.childNodes),a=p.firstChild,a.textContent=""}else f.push(t.createTextNode(o));for(p.textContent="",d=0;o=f[d++];)if(r&&ge.inArray(o,r)>-1)i&&i.push(o);else if(u=ge.contains(o.ownerDocument,o),a=b(p.appendChild(o),"script"),u&&x(a),n)for(c=0;o=a[c++];)Ye.test(o.type||"")&&n.push(o);return p}function w(){return!0}function C(){return!1}function T(){try{return re.activeElement}catch(e){}}function E(t,n,r,i,o,a){var s,l;if("object"===e(n)){"string"!=typeof r&&(i=i||r,r=void 0);for(l in n)E(t,l,r,i,n[l],a);return t}if(null==i&&null==o?(o=r,i=r=void 0):null==o&&("string"==typeof r?(o=i,i=void 0):(o=i,i=r,r=void 0)),o===!1)o=C;else if(!o)return t;return 1===a&&(s=o,o=function(e){return ge().off(e),s.apply(this,arguments)},o.guid=s.guid||(s.guid=ge.guid++)),t.each(function(){ge.event.add(this,n,o,i,r)})}function L(e,t){return o(e,"table")&&o(11!==t.nodeType?t:t.firstChild,"tr")?ge(">tbody",e)[0]||e:e}function S(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function D(e){var t=ot.exec(e.type);return t?e.type=t[1]:e.removeAttribute("type"),e}function H(e,t){var n,r,i,o,a,s,l,u;if(1===t.nodeType){if(Me.hasData(e)&&(o=Me.access(e),a=Me.set(t,o),u=o.events)){delete a.handle,a.events={};for(i in u)for(n=0,r=u[i].length;n<r;n++)ge.event.add(t,i,u[i][n])}Fe.hasData(e)&&(s=Fe.access(e),l=ge.extend({},s),Fe.set(t,l))}}function j(e,t){var n=t.nodeName.toLowerCase();"input"===n&&Ve.test(e.type)?t.checked=e.checked:"input"!==n&&"textarea"!==n||(t.defaultValue=e.defaultValue)}function N(e,t,n,i){t=ae.apply([],t);var o,a,s,l,u,c,p=0,f=e.length,d=f-1,h=t[0],v=ge.isFunction(h);if(v||f>1&&"string"==typeof h&&!he.checkClone&&it.test(h))return e.each(function(r){var o=e.eq(r);v&&(t[0]=h.call(this,r,o.html())),N(o,t,n,i)});if(f&&(o=k(t,e[0].ownerDocument,!1,e,i),a=o.firstChild,1===o.childNodes.length&&(o=a),a||i)){for(s=ge.map(b(o,"script"),S),l=s.length;p<f;p++)u=o,p!==d&&(u=ge.clone(u,!0,!0),l&&ge.merge(s,b(u,"script"))),n.call(e[p],u,p);if(l)for(c=s[s.length-1].ownerDocument,ge.map(s,D),p=0;p<l;p++)u=s[p],Ye.test(u.type||"")&&!Me.access(u,"globalEval")&&ge.contains(c,u)&&(u.src?ge._evalUrl&&ge._evalUrl(u.src):r(u.textContent.replace(at,""),c))}return e}function _(e,t,n){for(var r,i=t?ge.filter(t,e):e,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||ge.cleanData(b(r)),r.parentNode&&(n&&ge.contains(r.ownerDocument,r)&&x(b(r,"script")),r.parentNode.removeChild(r));return e}function A(e,t,n){var r,i,o,a,s=e.style;return n=n||ut(e),n&&(a=n.getPropertyValue(t)||n[t],""!==a||ge.contains(e.ownerDocument,e)||(a=ge.style(e,t)),!he.pixelMarginRight()&&lt.test(a)&&st.test(t)&&(r=s.width,i=s.minWidth,o=s.maxWidth,s.minWidth=s.maxWidth=s.width=a,a=n.width,s.width=r,s.minWidth=i,s.maxWidth=o)),void 0!==a?a+"":a}function P(e,t){return{get:function(){return e()?void delete this.get:(this.get=t).apply(this,arguments)}}}function q(e){if(e in vt)return e;for(var t=e[0].toUpperCase()+e.slice(1),n=ht.length;n--;)if(e=ht[n]+t,e in vt)return e}function O(e){var t=ge.cssProps[e];return t||(t=ge.cssProps[e]=q(e)||e),t}function M(e,t,n){var r=Be.exec(t);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):t}function F(e,t,n,r,i){var o,a=0;for(o=n===(r?"border":"content")?4:"width"===t?1:0;o<4;o+=2)"margin"===n&&(a+=ge.css(e,n+We[o],!0,i)),r?("content"===n&&(a-=ge.css(e,"padding"+We[o],!0,i)),"margin"!==n&&(a-=ge.css(e,"border"+We[o]+"Width",!0,i))):(a+=ge.css(e,"padding"+We[o],!0,i),"padding"!==n&&(a+=ge.css(e,"border"+We[o]+"Width",!0,i)));return a}function R(e,t,n){var r,i=ut(e),o=A(e,t,i),a="border-box"===ge.css(e,"boxSizing",!1,i);return lt.test(o)?o:(r=a&&(he.boxSizingReliable()||o===e.style[t]),"auto"===o&&(o=e["offset"+t[0].toUpperCase()+t.slice(1)]),o=parseFloat(o)||0,o+F(e,t,n||(a?"border":"content"),r,i)+"px")}function I(e,t,n,r,i){return new I.prototype.init(e,t,n,r,i)}function $(){mt&&(re.hidden===!1&&t.requestAnimationFrame?t.requestAnimationFrame($):t.setTimeout($,ge.fx.interval),ge.fx.tick())}function B(){return t.setTimeout(function(){gt=void 0}),gt=ge.now()}function W(e,t){var n,r=0,i={height:e};for(t=t?1:0;r<4;r+=2-t)n=We[r],i["margin"+n]=i["padding"+n]=e;return t&&(i.opacity=i.width=e),i}function U(e,t,n){for(var r,i=(V.tweeners[t]||[]).concat(V.tweeners["*"]),o=0,a=i.length;o<a;o++)if(r=i[o].call(n,t,e))return r}function z(e,t,n){var r,i,o,a,s,l,u,c,p="width"in t||"height"in t,f=this,d={},h=e.style,v=e.nodeType&&Ue(e),g=Me.get(e,"fxshow");n.queue||(a=ge._queueHooks(e,"fx"),null==a.unqueued&&(a.unqueued=0,s=a.empty.fire,a.empty.fire=function(){a.unqueued||s()}),a.unqueued++,f.always(function(){f.always(function(){a.unqueued--,ge.queue(e,"fx").length||a.empty.fire()})}));for(r in t)if(i=t[r],yt.test(i)){if(delete t[r],o=o||"toggle"===i,i===(v?"hide":"show")){if("show"!==i||!g||void 0===g[r])continue;v=!0}d[r]=g&&g[r]||ge.style(e,r)}if(l=!ge.isEmptyObject(t),l||!ge.isEmptyObject(d)){p&&1===e.nodeType&&(n.overflow=[h.overflow,h.overflowX,h.overflowY],u=g&&g.display,null==u&&(u=Me.get(e,"display")),c=ge.css(e,"display"),"none"===c&&(u?c=u:(y([e],!0),u=e.style.display||u,c=ge.css(e,"display"),y([e]))),("inline"===c||"inline-block"===c&&null!=u)&&"none"===ge.css(e,"float")&&(l||(f.done(function(){h.display=u}),null==u&&(c=h.display,u="none"===c?"":c)),h.display="inline-block")),n.overflow&&(h.overflow="hidden",f.always(function(){h.overflow=n.overflow[0],h.overflowX=n.overflow[1],h.overflowY=n.overflow[2]})),l=!1;for(r in d)l||(g?"hidden"in g&&(v=g.hidden):g=Me.access(e,"fxshow",{display:u}),o&&(g.hidden=!v),v&&y([e],!0),f.done(function(){v||y([e]),Me.remove(e,"fxshow");for(r in d)ge.style(e,r,d[r])})),l=U(v?g[r]:0,r,f),r in g||(g[r]=l.start,v&&(l.end=l.start,l.start=0))}}function X(e,t){var n,r,i,o,a;for(n in e)if(r=ge.camelCase(n),i=t[r],o=e[n],Array.isArray(o)&&(i=o[1],o=e[n]=o[0]),n!==r&&(e[r]=o,delete e[n]),a=ge.cssHooks[r],a&&"expand"in a){o=a.expand(o),delete e[r];for(n in o)n in e||(e[n]=o[n],t[n]=i)}else t[r]=i}function V(e,t,n){var r,i,o=0,a=V.prefilters.length,s=ge.Deferred().always(function(){delete l.elem}),l=function(){if(i)return!1;for(var t=gt||B(),n=Math.max(0,u.startTime+u.duration-t),r=n/u.duration||0,o=1-r,a=0,l=u.tweens.length;a<l;a++)u.tweens[a].run(o);return s.notifyWith(e,[u,o,n]),o<1&&l?n:(l||s.notifyWith(e,[u,1,0]),s.resolveWith(e,[u]),!1)},u=s.promise({elem:e,props:ge.extend({},t),opts:ge.extend(!0,{specialEasing:{},easing:ge.easing._default},n),originalProperties:t,originalOptions:n,startTime:gt||B(),duration:n.duration,tweens:[],createTween:function(t,n){var r=ge.Tween(e,u.opts,t,n,u.opts.specialEasing[t]||u.opts.easing);return u.tweens.push(r),r},stop:function(t){var n=0,r=t?u.tweens.length:0;if(i)return this;for(i=!0;n<r;n++)u.tweens[n].run(1);return t?(s.notifyWith(e,[u,1,0]),s.resolveWith(e,[u,t])):s.rejectWith(e,[u,t]),this}}),c=u.props;for(X(c,u.opts.specialEasing);o<a;o++)if(r=V.prefilters[o].call(u,e,c,u.opts))return ge.isFunction(r.stop)&&(ge._queueHooks(u.elem,u.opts.queue).stop=ge.proxy(r.stop,r)),r;return ge.map(c,U,u),ge.isFunction(u.opts.start)&&u.opts.start.call(e,u),u.progress(u.opts.progress).done(u.opts.done,u.opts.complete).fail(u.opts.fail).always(u.opts.always),ge.fx.timer(ge.extend(l,{elem:e,anim:u,queue:u.opts.queue})),u}function G(e){var t=e.match(_e)||[];return t.join(" ")}function Y(e){return e.getAttribute&&e.getAttribute("class")||""}function Q(t,n,r,i){var o;if(Array.isArray(n))ge.each(n,function(n,o){r||Ht.test(t)?i(t,o):Q(t+"["+("object"===e(o)&&null!=o?n:"")+"]",o,r,i)});else if(r||"object"!==ge.type(n))i(t,n);else for(o in n)Q(t+"["+o+"]",n[o],r,i)}function J(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var r,i=0,o=t.toLowerCase().match(_e)||[];if(ge.isFunction(n))for(;r=o[i++];)"+"===r[0]?(r=r.slice(1)||"*",(e[r]=e[r]||[]).unshift(n)):(e[r]=e[r]||[]).push(n)}}function K(e,t,n,r){function i(s){var l;return o[s]=!0,ge.each(e[s]||[],function(e,s){var u=s(t,n,r);return"string"!=typeof u||a||o[u]?a?!(l=u):void 0:(t.dataTypes.unshift(u),i(u),!1)}),l}var o={},a=e===$t;return i(t.dataTypes[0])||!o["*"]&&i("*")}function Z(e,t){var n,r,i=ge.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((i[n]?e:r||(r={}))[n]=t[n]);return r&&ge.extend(!0,e,r),e}function ee(e,t,n){for(var r,i,o,a,s=e.contents,l=e.dataTypes;"*"===l[0];)l.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(i in s)if(s[i]&&s[i].test(r)){l.unshift(i);break}if(l[0]in n)o=l[0];else{for(i in n){if(!l[0]||e.converters[i+" "+l[0]]){o=i;break}a||(a=i)}o=o||a}if(o)return o!==l[0]&&l.unshift(o),n[o]}function te(e,t,n,r){var i,o,a,s,l,u={},c=e.dataTypes.slice();if(c[1])for(a in e.converters)u[a.toLowerCase()]=e.converters[a];for(o=c.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!l&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),l=o,o=c.shift())if("*"===o)o=l;else if("*"!==l&&l!==o){if(a=u[l+" "+o]||u["* "+o],!a)for(i in u)if(s=i.split(" "),s[1]===o&&(a=u[l+" "+s[0]]||u["* "+s[0]])){a===!0?a=u[i]:u[i]!==!0&&(o=s[0],c.unshift(s[1]));break}if(a!==!0)if(a&&e["throws"])t=a(t);else try{t=a(t)}catch(p){return{state:"parsererror",error:a?p:"No conversion from "+l+" to "+o}}}return{state:"success",data:t}}var ne=[],re=t.document,ie=Object.getPrototypeOf,oe=ne.slice,ae=ne.concat,se=ne.push,le=ne.indexOf,ue={},ce=ue.toString,pe=ue.hasOwnProperty,fe=pe.toString,de=fe.call(Object),he={},ve="3.2.1",ge=function Qt(e,t){return new Qt.fn.init(e,t)},me=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,ye=/^-ms-/,be=/-([a-z])/g,xe=function(e,t){return t.toUpperCase()};ge.fn=ge.prototype={jquery:ve,constructor:ge,length:0,toArray:function(){return oe.call(this)},get:function(e){return null==e?oe.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=ge.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return ge.each(this,e)},map:function(e){return this.pushStack(ge.map(this,function(t,n){return e.call(t,n,t)}))},slice:function(){return this.pushStack(oe.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(n>=0&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:se,sort:ne.sort,splice:ne.splice},ge.extend=ge.fn.extend=function(){var t,n,r,i,o,a,s=arguments[0]||{},l=1,u=arguments.length,c=!1;for("boolean"==typeof s&&(c=s,s=arguments[l]||{},l++),"object"===e(s)||ge.isFunction(s)||(s={}),l===u&&(s=this,l--);l<u;l++)if(null!=(t=arguments[l]))for(n in t)r=s[n],i=t[n],s!==i&&(c&&i&&(ge.isPlainObject(i)||(o=Array.isArray(i)))?(o?(o=!1,a=r&&Array.isArray(r)?r:[]):a=r&&ge.isPlainObject(r)?r:{},s[n]=ge.extend(c,a,i)):void 0!==i&&(s[n]=i));return s},ge.extend({expando:"jQuery"+(ve+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isFunction:function(e){return"function"===ge.type(e)},isWindow:function(e){return null!=e&&e===e.window},isNumeric:function(e){var t=ge.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},isPlainObject:function(e){var t,n;return!(!e||"[object Object]"!==ce.call(e))&&(!(t=ie(e))||(n=pe.call(t,"constructor")&&t.constructor,"function"==typeof n&&fe.call(n)===de))},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},type:function(t){return null==t?t+"":"object"===e(t)||"function"==typeof t?ue[ce.call(t)]||"object":e(t)},globalEval:function(e){r(e)},camelCase:function(e){return e.replace(ye,"ms-").replace(be,xe)},each:function(e,t){var n,r=0;if(i(e))for(n=e.length;r<n&&t.call(e[r],r,e[r])!==!1;r++);else for(r in e)if(t.call(e[r],r,e[r])===!1)break;return e},trim:function(e){return null==e?"":(e+"").replace(me,"")},makeArray:function(e,t){var n=t||[];return null!=e&&(i(Object(e))?ge.merge(n,"string"==typeof e?[e]:e):se.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:le.call(t,e,n)},merge:function(e,t){for(var n=+t.length,r=0,i=e.length;r<n;r++)e[i++]=t[r];return e.length=i,e},grep:function(e,t,n){for(var r,i=[],o=0,a=e.length,s=!n;o<a;o++)r=!t(e[o],o),r!==s&&i.push(e[o]);return i},map:function(e,t,n){var r,o,a=0,s=[];if(i(e))for(r=e.length;a<r;a++)o=t(e[a],a,n),null!=o&&s.push(o);else for(a in e)o=t(e[a],a,n),null!=o&&s.push(o);return ae.apply([],s)},guid:1,proxy:function Jt(e,t){var n,r,Jt;if("string"==typeof t&&(n=e[t],t=e,e=n),ge.isFunction(e))return r=oe.call(arguments,2),Jt=function(){return e.apply(t||this,r.concat(oe.call(arguments)))},Jt.guid=e.guid=e.guid||ge.guid++,Jt},now:Date.now,support:he}),"function"==typeof Symbol&&(ge.fn[Symbol.iterator]=ne[Symbol.iterator]),ge.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){ue["[object "+t+"]"]=t.toLowerCase()});var ke=function(e){function t(e,t,n,r){var i,o,a,s,l,u,c,f=t&&t.ownerDocument,h=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==h&&9!==h&&11!==h)return n;if(!r&&((t?t.ownerDocument||t:$)!==A&&_(t),t=t||A,q)){if(11!==h&&(l=me.exec(e)))if(i=l[1]){if(9===h){if(!(a=t.getElementById(i)))return n;if(a.id===i)return n.push(a),n}else if(f&&(a=f.getElementById(i))&&R(t,a)&&a.id===i)return n.push(a),n}else{if(l[2])return K.apply(n,t.getElementsByTagName(e)),n;if((i=l[3])&&w.getElementsByClassName&&t.getElementsByClassName)return K.apply(n,t.getElementsByClassName(i)),n}if(w.qsa&&!X[e+" "]&&(!O||!O.test(e))){if(1!==h)f=t,c=e;else if("object"!==t.nodeName.toLowerCase()){for((s=t.getAttribute("id"))?s=s.replace(ke,we):t.setAttribute("id",s=I),u=L(e),o=u.length;o--;)u[o]="#"+s+" "+d(u[o]);c=u.join(","),f=ye.test(e)&&p(t.parentNode)||t}if(c)try{return K.apply(n,f.querySelectorAll(c)),n}catch(v){}finally{s===I&&t.removeAttribute("id")}}}return D(e.replace(se,"$1"),t,n,r)}function n(){function e(n,r){return t.push(n+" ")>C.cacheLength&&delete e[t.shift()],e[n+" "]=r}var t=[];return e}function r(e){return e[I]=!0,e}function i(e){var t=A.createElement("fieldset");try{return!!e(t)}catch(n){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function o(e,t){for(var n=e.split("|"),r=n.length;r--;)C.attrHandle[n[r]]=t}function a(e,t){var n=t&&e,r=n&&1===e.nodeType&&1===t.nodeType&&e.sourceIndex-t.sourceIndex;if(r)return r;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function s(e){return function(t){var n=t.nodeName.toLowerCase();return"input"===n&&t.type===e}}function l(e){return function(t){var n=t.nodeName.toLowerCase();return("input"===n||"button"===n)&&t.type===e}}function u(e){return function(t){return"form"in t?t.parentNode&&t.disabled===!1?"label"in t?"label"in t.parentNode?t.parentNode.disabled===e:t.disabled===e:t.isDisabled===e||t.isDisabled!==!e&&Te(t)===e:t.disabled===e:"label"in t&&t.disabled===e}}function c(e){return r(function(t){return t=+t,r(function(n,r){for(var i,o=e([],n.length,t),a=o.length;a--;)n[i=o[a]]&&(n[i]=!(r[i]=n[i]))})})}function p(e){return e&&"undefined"!=typeof e.getElementsByTagName&&e}function f(){}function d(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function h(e,t,n){var r=t.dir,i=t.next,o=i||r,a=n&&"parentNode"===o,s=W++;return t.first?function(t,n,i){for(;t=t[r];)if(1===t.nodeType||a)return e(t,n,i);return!1}:function(t,n,l){var u,c,p,f=[B,s];if(l){for(;t=t[r];)if((1===t.nodeType||a)&&e(t,n,l))return!0}else for(;t=t[r];)if(1===t.nodeType||a)if(p=t[I]||(t[I]={}),c=p[t.uniqueID]||(p[t.uniqueID]={}),i&&i===t.nodeName.toLowerCase())t=t[r]||t;else{if((u=c[o])&&u[0]===B&&u[1]===s)return f[2]=u[2];if(c[o]=f,f[2]=e(t,n,l))return!0}return!1}}function v(e){return e.length>1?function(t,n,r){for(var i=e.length;i--;)if(!e[i](t,n,r))return!1;return!0}:e[0]}function g(e,n,r){for(var i=0,o=n.length;i<o;i++)t(e,n[i],r);return r}function m(e,t,n,r,i){for(var o,a=[],s=0,l=e.length,u=null!=t;s<l;s++)(o=e[s])&&(n&&!n(o,r,i)||(a.push(o),u&&t.push(s)));return a}function y(e,t,n,i,o,a){return i&&!i[I]&&(i=y(i)),o&&!o[I]&&(o=y(o,a)),r(function(r,a,s,l){var u,c,p,f=[],d=[],h=a.length,v=r||g(t||"*",s.nodeType?[s]:s,[]),y=!e||!r&&t?v:m(v,f,e,s,l),b=n?o||(r?e:h||i)?[]:a:y;if(n&&n(y,b,s,l),i)for(u=m(b,d),i(u,[],s,l),c=u.length;c--;)(p=u[c])&&(b[d[c]]=!(y[d[c]]=p));if(r){if(o||e){if(o){for(u=[],c=b.length;c--;)(p=b[c])&&u.push(y[c]=p);o(null,b=[],u,l)}for(c=b.length;c--;)(p=b[c])&&(u=o?ee(r,p):f[c])>-1&&(r[u]=!(a[u]=p))}}else b=m(b===a?b.splice(h,b.length):b),o?o(null,a,b,l):K.apply(a,b)})}function b(e){for(var t,n,r,i=e.length,o=C.relative[e[0].type],a=o||C.relative[" "],s=o?1:0,l=h(function(e){return e===t},a,!0),u=h(function(e){return ee(t,e)>-1},a,!0),c=[function(e,n,r){var i=!o&&(r||n!==H)||((t=n).nodeType?l(e,n,r):u(e,n,r));return t=null,i}];s<i;s++)if(n=C.relative[e[s].type])c=[h(v(c),n)];else{if(n=C.filter[e[s].type].apply(null,e[s].matches),n[I]){for(r=++s;r<i&&!C.relative[e[r].type];r++);return y(s>1&&v(c),s>1&&d(e.slice(0,s-1).concat({value:" "===e[s-2].type?"*":""})).replace(se,"$1"),n,s<r&&b(e.slice(s,r)),r<i&&b(e=e.slice(r)),r<i&&d(e))}c.push(n)}return v(c)}function x(e,n){var i=n.length>0,o=e.length>0,a=function(r,a,s,l,u){var c,p,f,d=0,h="0",v=r&&[],g=[],y=H,b=r||o&&C.find.TAG("*",u),x=B+=null==y?1:Math.random()||.1,k=b.length;for(u&&(H=a===A||a||u);h!==k&&null!=(c=b[h]);h++){if(o&&c){for(p=0,a||c.ownerDocument===A||(_(c),s=!q);f=e[p++];)if(f(c,a||A,s)){l.push(c);break}u&&(B=x)}i&&((c=!f&&c)&&d--,r&&v.push(c))}if(d+=h,i&&h!==d){for(p=0;f=n[p++];)f(v,g,a,s);if(r){if(d>0)for(;h--;)v[h]||g[h]||(g[h]=Q.call(l));g=m(g)}K.apply(l,g),u&&!r&&g.length>0&&d+n.length>1&&t.uniqueSort(l)}return u&&(B=x,H=y),v};return i?r(a):a}var k,w,C,T,E,L,S,D,H,j,N,_,A,P,q,O,M,F,R,I="sizzle"+1*new Date,$=e.document,B=0,W=0,U=n(),z=n(),X=n(),V=function(e,t){return e===t&&(N=!0),0},G={}.hasOwnProperty,Y=[],Q=Y.pop,J=Y.push,K=Y.push,Z=Y.slice,ee=function(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1},te="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",ne="[\\x20\\t\\r\\n\\f]",re="(?:\\\\.|[\\w-]|[^\0-\\xa0])+",ie="\\["+ne+"*("+re+")(?:"+ne+"*([*^$|!~]?=)"+ne+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+re+"))|)"+ne+"*\\]",oe=":("+re+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+ie+")*)|.*)\\)|)",ae=new RegExp(ne+"+","g"),se=new RegExp("^"+ne+"+|((?:^|[^\\\\])(?:\\\\.)*)"+ne+"+$","g"),le=new RegExp("^"+ne+"*,"+ne+"*"),ue=new RegExp("^"+ne+"*([>+~]|"+ne+")"+ne+"*"),ce=new RegExp("="+ne+"*([^\\]'\"]*?)"+ne+"*\\]","g"),pe=new RegExp(oe),fe=new RegExp("^"+re+"$"),de={ID:new RegExp("^#("+re+")"),CLASS:new RegExp("^\\.("+re+")"),TAG:new RegExp("^("+re+"|[*])"),ATTR:new RegExp("^"+ie),PSEUDO:new RegExp("^"+oe),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+ne+"*(even|odd|(([+-]|)(\\d*)n|)"+ne+"*(?:([+-]|)"+ne+"*(\\d+)|))"+ne+"*\\)|)","i"),bool:new RegExp("^(?:"+te+")$","i"),needsContext:new RegExp("^"+ne+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+ne+"*((?:-\\d)?\\d*)"+ne+"*\\)|)(?=[^-]|$)","i")},he=/^(?:input|select|textarea|button)$/i,ve=/^h\d$/i,ge=/^[^{]+\{\s*\[native \w/,me=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ye=/[+~]/,be=new RegExp("\\\\([\\da-f]{1,6}"+ne+"?|("+ne+")|.)","ig"),xe=function(e,t,n){var r="0x"+t-65536;return r!==r||n?t:r<0?String.fromCharCode(r+65536):String.fromCharCode(r>>10|55296,1023&r|56320)},ke=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,we=function(e,t){return t?"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e},Ce=function(){_()},Te=h(function(e){return e.disabled===!0&&("form"in e||"label"in e)},{dir:"parentNode",next:"legend"});try{K.apply(Y=Z.call($.childNodes),$.childNodes),Y[$.childNodes.length].nodeType}catch(Ee){K={apply:Y.length?function(e,t){J.apply(e,Z.call(t))}:function(e,t){for(var n=e.length,r=0;e[n++]=t[r++];);e.length=n-1}}}w=t.support={},E=t.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return!!t&&"HTML"!==t.nodeName},_=t.setDocument=function(e){var t,n,r=e?e.ownerDocument||e:$;return r!==A&&9===r.nodeType&&r.documentElement?(A=r,P=A.documentElement,q=!E(A),$!==A&&(n=A.defaultView)&&n.top!==n&&(n.addEventListener?n.addEventListener("unload",Ce,!1):n.attachEvent&&n.attachEvent("onunload",Ce)),w.attributes=i(function(e){return e.className="i",!e.getAttribute("className")}),w.getElementsByTagName=i(function(e){return e.appendChild(A.createComment("")),!e.getElementsByTagName("*").length}),w.getElementsByClassName=ge.test(A.getElementsByClassName),w.getById=i(function(e){return P.appendChild(e).id=I,!A.getElementsByName||!A.getElementsByName(I).length}),w.getById?(C.filter.ID=function(e){var t=e.replace(be,xe);return function(e){return e.getAttribute("id")===t}},C.find.ID=function(e,t){if("undefined"!=typeof t.getElementById&&q){var n=t.getElementById(e);return n?[n]:[]}}):(C.filter.ID=function(e){var t=e.replace(be,xe);return function(e){var n="undefined"!=typeof e.getAttributeNode&&e.getAttributeNode("id");return n&&n.value===t}},C.find.ID=function(e,t){if("undefined"!=typeof t.getElementById&&q){var n,r,i,o=t.getElementById(e);if(o){if(n=o.getAttributeNode("id"),n&&n.value===e)return[o];for(i=t.getElementsByName(e),r=0;o=i[r++];)if(n=o.getAttributeNode("id"),n&&n.value===e)return[o]}return[]}}),C.find.TAG=w.getElementsByTagName?function(e,t){return"undefined"!=typeof t.getElementsByTagName?t.getElementsByTagName(e):w.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,r=[],i=0,o=t.getElementsByTagName(e);if("*"===e){for(;n=o[i++];)1===n.nodeType&&r.push(n);return r}return o},C.find.CLASS=w.getElementsByClassName&&function(e,t){if("undefined"!=typeof t.getElementsByClassName&&q)return t.getElementsByClassName(e)},M=[],O=[],(w.qsa=ge.test(A.querySelectorAll))&&(i(function(e){P.appendChild(e).innerHTML="<a id='"+I+"'></a><select id='"+I+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&O.push("[*^$]="+ne+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||O.push("\\["+ne+"*(?:value|"+te+")"),e.querySelectorAll("[id~="+I+"-]").length||O.push("~="),e.querySelectorAll(":checked").length||O.push(":checked"),e.querySelectorAll("a#"+I+"+*").length||O.push(".#.+[+~]")}),i(function(e){e.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var t=A.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&O.push("name"+ne+"*[*^$|!~]?="),2!==e.querySelectorAll(":enabled").length&&O.push(":enabled",":disabled"),P.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&O.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),O.push(",.*:")})),(w.matchesSelector=ge.test(F=P.matches||P.webkitMatchesSelector||P.mozMatchesSelector||P.oMatchesSelector||P.msMatchesSelector))&&i(function(e){w.disconnectedMatch=F.call(e,"*"),F.call(e,"[s!='']:x"),M.push("!=",oe)}),O=O.length&&new RegExp(O.join("|")),M=M.length&&new RegExp(M.join("|")),t=ge.test(P.compareDocumentPosition),R=t||ge.test(P.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,r=t&&t.parentNode;return e===r||!(!r||1!==r.nodeType||!(n.contains?n.contains(r):e.compareDocumentPosition&&16&e.compareDocumentPosition(r)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},V=t?function(e,t){if(e===t)return N=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n?n:(n=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1,1&n||!w.sortDetached&&t.compareDocumentPosition(e)===n?e===A||e.ownerDocument===$&&R($,e)?-1:t===A||t.ownerDocument===$&&R($,t)?1:j?ee(j,e)-ee(j,t):0:4&n?-1:1)}:function(e,t){if(e===t)return N=!0,0;var n,r=0,i=e.parentNode,o=t.parentNode,s=[e],l=[t];if(!i||!o)return e===A?-1:t===A?1:i?-1:o?1:j?ee(j,e)-ee(j,t):0;if(i===o)return a(e,t);for(n=e;n=n.parentNode;)s.unshift(n);for(n=t;n=n.parentNode;)l.unshift(n);for(;s[r]===l[r];)r++;return r?a(s[r],l[r]):s[r]===$?-1:l[r]===$?1:0},A):A},t.matches=function(e,n){return t(e,null,null,n)},t.matchesSelector=function(e,n){if((e.ownerDocument||e)!==A&&_(e),n=n.replace(ce,"='$1']"),w.matchesSelector&&q&&!X[n+" "]&&(!M||!M.test(n))&&(!O||!O.test(n)))try{var r=F.call(e,n);if(r||w.disconnectedMatch||e.document&&11!==e.document.nodeType)return r}catch(i){}return t(n,A,null,[e]).length>0},t.contains=function(e,t){return(e.ownerDocument||e)!==A&&_(e),R(e,t)},t.attr=function(e,t){(e.ownerDocument||e)!==A&&_(e);var n=C.attrHandle[t.toLowerCase()],r=n&&G.call(C.attrHandle,t.toLowerCase())?n(e,t,!q):void 0;return void 0!==r?r:w.attributes||!q?e.getAttribute(t):(r=e.getAttributeNode(t))&&r.specified?r.value:null},t.escape=function(e){return(e+"").replace(ke,we)},t.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},t.uniqueSort=function(e){var t,n=[],r=0,i=0;if(N=!w.detectDuplicates,j=!w.sortStable&&e.slice(0),e.sort(V),N){for(;t=e[i++];)t===e[i]&&(r=n.push(i));for(;r--;)e.splice(n[r],1)}return j=null,e},T=t.getText=function(e){var t,n="",r=0,i=e.nodeType;if(i){if(1===i||9===i||11===i){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=T(e)}else if(3===i||4===i)return e.nodeValue}else for(;t=e[r++];)n+=T(t);return n},C=t.selectors={cacheLength:50,createPseudo:r,match:de,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(be,xe),e[3]=(e[3]||e[4]||e[5]||"").replace(be,xe),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||t.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&t.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return de.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&pe.test(n)&&(t=L(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(be,xe).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=U[e+" "];return t||(t=new RegExp("(^|"+ne+")"+e+"("+ne+"|$)"))&&U(e,function(e){return t.test("string"==typeof e.className&&e.className||"undefined"!=typeof e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(e,n,r){return function(i){var o=t.attr(i,e);return null==o?"!="===n:!n||(o+="","="===n?o===r:"!="===n?o!==r:"^="===n?r&&0===o.indexOf(r):"*="===n?r&&o.indexOf(r)>-1:"$="===n?r&&o.slice(-r.length)===r:"~="===n?(" "+o.replace(ae," ")+" ").indexOf(r)>-1:"|="===n&&(o===r||o.slice(0,r.length+1)===r+"-"))}},CHILD:function(e,t,n,r,i){var o="nth"!==e.slice(0,3),a="last"!==e.slice(-4),s="of-type"===t;return 1===r&&0===i?function(e){return!!e.parentNode}:function(t,n,l){var u,c,p,f,d,h,v=o!==a?"nextSibling":"previousSibling",g=t.parentNode,m=s&&t.nodeName.toLowerCase(),y=!l&&!s,b=!1;
if(g){if(o){for(;v;){for(f=t;f=f[v];)if(s?f.nodeName.toLowerCase()===m:1===f.nodeType)return!1;h=v="only"===e&&!h&&"nextSibling"}return!0}if(h=[a?g.firstChild:g.lastChild],a&&y){for(f=g,p=f[I]||(f[I]={}),c=p[f.uniqueID]||(p[f.uniqueID]={}),u=c[e]||[],d=u[0]===B&&u[1],b=d&&u[2],f=d&&g.childNodes[d];f=++d&&f&&f[v]||(b=d=0)||h.pop();)if(1===f.nodeType&&++b&&f===t){c[e]=[B,d,b];break}}else if(y&&(f=t,p=f[I]||(f[I]={}),c=p[f.uniqueID]||(p[f.uniqueID]={}),u=c[e]||[],d=u[0]===B&&u[1],b=d),b===!1)for(;(f=++d&&f&&f[v]||(b=d=0)||h.pop())&&((s?f.nodeName.toLowerCase()!==m:1!==f.nodeType)||!++b||(y&&(p=f[I]||(f[I]={}),c=p[f.uniqueID]||(p[f.uniqueID]={}),c[e]=[B,b]),f!==t)););return b-=i,b===r||b%r===0&&b/r>=0}}},PSEUDO:function(e,n){var i,o=C.pseudos[e]||C.setFilters[e.toLowerCase()]||t.error("unsupported pseudo: "+e);return o[I]?o(n):o.length>1?(i=[e,e,"",n],C.setFilters.hasOwnProperty(e.toLowerCase())?r(function(e,t){for(var r,i=o(e,n),a=i.length;a--;)r=ee(e,i[a]),e[r]=!(t[r]=i[a])}):function(e){return o(e,0,i)}):o}},pseudos:{not:r(function(e){var t=[],n=[],i=S(e.replace(se,"$1"));return i[I]?r(function(e,t,n,r){for(var o,a=i(e,null,r,[]),s=e.length;s--;)(o=a[s])&&(e[s]=!(t[s]=o))}):function(e,r,o){return t[0]=e,i(t,null,o,n),t[0]=null,!n.pop()}}),has:r(function(e){return function(n){return t(e,n).length>0}}),contains:r(function(e){return e=e.replace(be,xe),function(t){return(t.textContent||t.innerText||T(t)).indexOf(e)>-1}}),lang:r(function(e){return fe.test(e||"")||t.error("unsupported lang: "+e),e=e.replace(be,xe).toLowerCase(),function(t){var n;do if(n=q?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return n=n.toLowerCase(),n===e||0===n.indexOf(e+"-");while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(t){var n=e.location&&e.location.hash;return n&&n.slice(1)===t.id},root:function(e){return e===P},focus:function(e){return e===A.activeElement&&(!A.hasFocus||A.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:u(!1),disabled:u(!0),checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,e.selected===!0},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!C.pseudos.empty(e)},header:function(e){return ve.test(e.nodeName)},input:function(e){return he.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:c(function(){return[0]}),last:c(function(e,t){return[t-1]}),eq:c(function(e,t,n){return[n<0?n+t:n]}),even:c(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:c(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:c(function(e,t,n){for(var r=n<0?n+t:n;--r>=0;)e.push(r);return e}),gt:c(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}},C.pseudos.nth=C.pseudos.eq;for(k in{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})C.pseudos[k]=s(k);for(k in{submit:!0,reset:!0})C.pseudos[k]=l(k);return f.prototype=C.filters=C.pseudos,C.setFilters=new f,L=t.tokenize=function(e,n){var r,i,o,a,s,l,u,c=z[e+" "];if(c)return n?0:c.slice(0);for(s=e,l=[],u=C.preFilter;s;){r&&!(i=le.exec(s))||(i&&(s=s.slice(i[0].length)||s),l.push(o=[])),r=!1,(i=ue.exec(s))&&(r=i.shift(),o.push({value:r,type:i[0].replace(se," ")}),s=s.slice(r.length));for(a in C.filter)!(i=de[a].exec(s))||u[a]&&!(i=u[a](i))||(r=i.shift(),o.push({value:r,type:a,matches:i}),s=s.slice(r.length));if(!r)break}return n?s.length:s?t.error(e):z(e,l).slice(0)},S=t.compile=function(e,t){var n,r=[],i=[],o=X[e+" "];if(!o){for(t||(t=L(e)),n=t.length;n--;)o=b(t[n]),o[I]?r.push(o):i.push(o);o=X(e,x(i,r)),o.selector=e}return o},D=t.select=function(e,t,n,r){var i,o,a,s,l,u="function"==typeof e&&e,c=!r&&L(e=u.selector||e);if(n=n||[],1===c.length){if(o=c[0]=c[0].slice(0),o.length>2&&"ID"===(a=o[0]).type&&9===t.nodeType&&q&&C.relative[o[1].type]){if(t=(C.find.ID(a.matches[0].replace(be,xe),t)||[])[0],!t)return n;u&&(t=t.parentNode),e=e.slice(o.shift().value.length)}for(i=de.needsContext.test(e)?0:o.length;i--&&(a=o[i],!C.relative[s=a.type]);)if((l=C.find[s])&&(r=l(a.matches[0].replace(be,xe),ye.test(o[0].type)&&p(t.parentNode)||t))){if(o.splice(i,1),e=r.length&&d(o),!e)return K.apply(n,r),n;break}}return(u||S(e,c))(r,t,!q,n,!t||ye.test(e)&&p(t.parentNode)||t),n},w.sortStable=I.split("").sort(V).join("")===I,w.detectDuplicates=!!N,_(),w.sortDetached=i(function(e){return 1&e.compareDocumentPosition(A.createElement("fieldset"))}),i(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||o("type|href|height|width",function(e,t,n){if(!n)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),w.attributes&&i(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||o("value",function(e,t,n){if(!n&&"input"===e.nodeName.toLowerCase())return e.defaultValue}),i(function(e){return null==e.getAttribute("disabled")})||o(te,function(e,t,n){var r;if(!n)return e[t]===!0?t.toLowerCase():(r=e.getAttributeNode(t))&&r.specified?r.value:null}),t}(t);ge.find=ke,ge.expr=ke.selectors,ge.expr[":"]=ge.expr.pseudos,ge.uniqueSort=ge.unique=ke.uniqueSort,ge.text=ke.getText,ge.isXMLDoc=ke.isXML,ge.contains=ke.contains,ge.escapeSelector=ke.escape;var we=function(e,t,n){for(var r=[],i=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(i&&ge(e).is(n))break;r.push(e)}return r},Ce=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},Te=ge.expr.match.needsContext,Ee=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i,Le=/^.[^:#\[\.,]*$/;ge.filter=function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?ge.find.matchesSelector(r,e)?[r]:[]:ge.find.matches(e,ge.grep(t,function(e){return 1===e.nodeType}))},ge.fn.extend({find:function(e){var t,n,r=this.length,i=this;if("string"!=typeof e)return this.pushStack(ge(e).filter(function(){for(t=0;t<r;t++)if(ge.contains(i[t],this))return!0}));for(n=this.pushStack([]),t=0;t<r;t++)ge.find(e,i[t],n);return r>1?ge.uniqueSort(n):n},filter:function(e){return this.pushStack(a(this,e||[],!1))},not:function(e){return this.pushStack(a(this,e||[],!0))},is:function(e){return!!a(this,"string"==typeof e&&Te.test(e)?ge(e):e||[],!1).length}});var Se,De=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/,He=ge.fn.init=function(e,t,n){var r,i;if(!e)return this;if(n=n||Se,"string"==typeof e){if(r="<"===e[0]&&">"===e[e.length-1]&&e.length>=3?[null,e,null]:De.exec(e),!r||!r[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(r[1]){if(t=t instanceof ge?t[0]:t,ge.merge(this,ge.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:re,!0)),Ee.test(r[1])&&ge.isPlainObject(t))for(r in t)ge.isFunction(this[r])?this[r](t[r]):this.attr(r,t[r]);return this}return i=re.getElementById(r[2]),i&&(this[0]=i,this.length=1),this}return e.nodeType?(this[0]=e,this.length=1,this):ge.isFunction(e)?void 0!==n.ready?n.ready(e):e(ge):ge.makeArray(e,this)};He.prototype=ge.fn,Se=ge(re);var je=/^(?:parents|prev(?:Until|All))/,Ne={children:!0,contents:!0,next:!0,prev:!0};ge.fn.extend({has:function(e){var t=ge(e,this),n=t.length;return this.filter(function(){for(var e=0;e<n;e++)if(ge.contains(this,t[e]))return!0})},closest:function(e,t){var n,r=0,i=this.length,o=[],a="string"!=typeof e&&ge(e);if(!Te.test(e))for(;r<i;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?a.index(n)>-1:1===n.nodeType&&ge.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(o.length>1?ge.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?le.call(ge(e),this[0]):le.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(ge.uniqueSort(ge.merge(this.get(),ge(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),ge.each({parent:function Kt(e){var Kt=e.parentNode;return Kt&&11!==Kt.nodeType?Kt:null},parents:function(e){return we(e,"parentNode")},parentsUntil:function(e,t,n){return we(e,"parentNode",n)},next:function(e){return s(e,"nextSibling")},prev:function(e){return s(e,"previousSibling")},nextAll:function(e){return we(e,"nextSibling")},prevAll:function(e){return we(e,"previousSibling")},nextUntil:function(e,t,n){return we(e,"nextSibling",n)},prevUntil:function(e,t,n){return we(e,"previousSibling",n)},siblings:function(e){return Ce((e.parentNode||{}).firstChild,e)},children:function(e){return Ce(e.firstChild)},contents:function(e){return o(e,"iframe")?e.contentDocument:(o(e,"template")&&(e=e.content||e),ge.merge([],e.childNodes))}},function(e,t){ge.fn[e]=function(n,r){var i=ge.map(this,t,n);return"Until"!==e.slice(-5)&&(r=n),r&&"string"==typeof r&&(i=ge.filter(r,i)),this.length>1&&(Ne[e]||ge.uniqueSort(i),je.test(e)&&i.reverse()),this.pushStack(i)}});var _e=/[^\x20\t\r\n\f]+/g;ge.Callbacks=function(e){e="string"==typeof e?l(e):ge.extend({},e);var t,n,r,i,o=[],a=[],s=-1,u=function(){for(i=i||e.once,r=t=!0;a.length;s=-1)for(n=a.shift();++s<o.length;)o[s].apply(n[0],n[1])===!1&&e.stopOnFalse&&(s=o.length,n=!1);e.memory||(n=!1),t=!1,i&&(o=n?[]:"")},c={add:function(){return o&&(n&&!t&&(s=o.length-1,a.push(n)),function r(t){ge.each(t,function(t,n){ge.isFunction(n)?e.unique&&c.has(n)||o.push(n):n&&n.length&&"string"!==ge.type(n)&&r(n)})}(arguments),n&&!t&&u()),this},remove:function(){return ge.each(arguments,function(e,t){for(var n;(n=ge.inArray(t,o,n))>-1;)o.splice(n,1),n<=s&&s--}),this},has:function(e){return e?ge.inArray(e,o)>-1:o.length>0},empty:function(){return o&&(o=[]),this},disable:function(){return i=a=[],o=n="",this},disabled:function(){return!o},lock:function(){return i=a=[],n||t||(o=n=""),this},locked:function(){return!!i},fireWith:function(e,n){return i||(n=n||[],n=[e,n.slice?n.slice():n],a.push(n),t||u()),this},fire:function(){return c.fireWith(this,arguments),this},fired:function(){return!!r}};return c},ge.extend({Deferred:function(n){var r=[["notify","progress",ge.Callbacks("memory"),ge.Callbacks("memory"),2],["resolve","done",ge.Callbacks("once memory"),ge.Callbacks("once memory"),0,"resolved"],["reject","fail",ge.Callbacks("once memory"),ge.Callbacks("once memory"),1,"rejected"]],i="pending",o={state:function(){return i},always:function(){return a.done(arguments).fail(arguments),this},"catch":function(e){return o.then(null,e)},pipe:function(){var e=arguments;return ge.Deferred(function(t){ge.each(r,function(n,r){var i=ge.isFunction(e[r[4]])&&e[r[4]];a[r[1]](function(){var e=i&&i.apply(this,arguments);e&&ge.isFunction(e.promise)?e.promise().progress(t.notify).done(t.resolve).fail(t.reject):t[r[0]+"With"](this,i?[e]:arguments)})}),e=null}).promise()},then:function(n,i,o){function a(n,r,i,o){return function(){var l=this,p=arguments,f=function(){var t,f;if(!(n<s)){if(t=i.apply(l,p),t===r.promise())throw new TypeError("Thenable self-resolution");f=t&&("object"===e(t)||"function"==typeof t)&&t.then,ge.isFunction(f)?o?f.call(t,a(s,r,u,o),a(s,r,c,o)):(s++,f.call(t,a(s,r,u,o),a(s,r,c,o),a(s,r,u,r.notifyWith))):(i!==u&&(l=void 0,p=[t]),(o||r.resolveWith)(l,p))}},d=o?f:function(){try{f()}catch(e){ge.Deferred.exceptionHook&&ge.Deferred.exceptionHook(e,d.stackTrace),n+1>=s&&(i!==c&&(l=void 0,p=[e]),r.rejectWith(l,p))}};n?d():(ge.Deferred.getStackHook&&(d.stackTrace=ge.Deferred.getStackHook()),t.setTimeout(d))}}var s=0;return ge.Deferred(function(e){r[0][3].add(a(0,e,ge.isFunction(o)?o:u,e.notifyWith)),r[1][3].add(a(0,e,ge.isFunction(n)?n:u)),r[2][3].add(a(0,e,ge.isFunction(i)?i:c))}).promise()},promise:function(e){return null!=e?ge.extend(e,o):o}},a={};return ge.each(r,function(e,t){var n=t[2],s=t[5];o[t[1]]=n.add,s&&n.add(function(){i=s},r[3-e][2].disable,r[0][2].lock),n.add(t[3].fire),a[t[0]]=function(){return a[t[0]+"With"](this===a?void 0:this,arguments),this},a[t[0]+"With"]=n.fireWith}),o.promise(a),n&&n.call(a,a),a},when:function(e){var t=arguments.length,n=t,r=Array(n),i=oe.call(arguments),o=ge.Deferred(),a=function(e){return function(n){r[e]=this,i[e]=arguments.length>1?oe.call(arguments):n,--t||o.resolveWith(r,i)}};if(t<=1&&(p(e,o.done(a(n)).resolve,o.reject,!t),"pending"===o.state()||ge.isFunction(i[n]&&i[n].then)))return o.then();for(;n--;)p(i[n],a(n),o.reject);return o.promise()}});var Ae=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;ge.Deferred.exceptionHook=function(e,n){t.console&&t.console.warn&&e&&Ae.test(e.name)&&t.console.warn("jQuery.Deferred exception: "+e.message,e.stack,n)},ge.readyException=function(e){t.setTimeout(function(){throw e})};var Pe=ge.Deferred();ge.fn.ready=function(e){return Pe.then(e)["catch"](function(e){ge.readyException(e)}),this},ge.extend({isReady:!1,readyWait:1,ready:function(e){(e===!0?--ge.readyWait:ge.isReady)||(ge.isReady=!0,e!==!0&&--ge.readyWait>0||Pe.resolveWith(re,[ge]))}}),ge.ready.then=Pe.then,"complete"===re.readyState||"loading"!==re.readyState&&!re.documentElement.doScroll?t.setTimeout(ge.ready):(re.addEventListener("DOMContentLoaded",f),t.addEventListener("load",f));var qe=function Zt(e,t,n,r,i,o,a){var s=0,l=e.length,u=null==n;if("object"===ge.type(n)){i=!0;for(s in n)Zt(e,t,s,n[s],!0,o,a)}else if(void 0!==r&&(i=!0,ge.isFunction(r)||(a=!0),u&&(a?(t.call(e,r),t=null):(u=t,t=function(e,t,n){return u.call(ge(e),n)})),t))for(;s<l;s++)t(e[s],n,a?r:r.call(e[s],s,t(e[s],n)));return i?e:u?t.call(e):l?t(e[0],n):o},Oe=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};d.uid=1,d.prototype={cache:function(e){var t=e[this.expando];return t||(t={},Oe(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var r,i=this.cache(e);if("string"==typeof t)i[ge.camelCase(t)]=n;else for(r in t)i[ge.camelCase(r)]=t[r];return i},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][ge.camelCase(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,r=e[this.expando];if(void 0!==r){if(void 0!==t){Array.isArray(t)?t=t.map(ge.camelCase):(t=ge.camelCase(t),t=t in r?[t]:t.match(_e)||[]),n=t.length;for(;n--;)delete r[t[n]]}(void 0===t||ge.isEmptyObject(r))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!ge.isEmptyObject(t)}};var Me=new d,Fe=new d,Re=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,Ie=/[A-Z]/g;ge.extend({hasData:function(e){return Fe.hasData(e)||Me.hasData(e)},data:function(e,t,n){return Fe.access(e,t,n)},removeData:function(e,t){Fe.remove(e,t)},_data:function(e,t,n){return Me.access(e,t,n)},_removeData:function(e,t){Me.remove(e,t)}}),ge.fn.extend({data:function en(t,n){var r,i,en,o=this[0],a=o&&o.attributes;if(void 0===t){if(this.length&&(en=Fe.get(o),1===o.nodeType&&!Me.get(o,"hasDataAttrs"))){for(r=a.length;r--;)a[r]&&(i=a[r].name,0===i.indexOf("data-")&&(i=ge.camelCase(i.slice(5)),v(o,i,en[i])));Me.set(o,"hasDataAttrs",!0)}return en}return"object"===e(t)?this.each(function(){Fe.set(this,t)}):qe(this,function(e){var n;if(o&&void 0===e){if(n=Fe.get(o,t),void 0!==n)return n;if(n=v(o,t),void 0!==n)return n}else this.each(function(){Fe.set(this,t,e)})},null,n,arguments.length>1,null,!0)},removeData:function(e){return this.each(function(){Fe.remove(this,e)})}}),ge.extend({queue:function tn(e,t,n){var tn;if(e)return t=(t||"fx")+"queue",tn=Me.get(e,t),n&&(!tn||Array.isArray(n)?tn=Me.access(e,t,ge.makeArray(n)):tn.push(n)),tn||[]},dequeue:function(e,t){t=t||"fx";var n=ge.queue(e,t),r=n.length,i=n.shift(),o=ge._queueHooks(e,t),a=function(){ge.dequeue(e,t)};"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,a,o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return Me.get(e,n)||Me.access(e,n,{empty:ge.Callbacks("once memory").add(function(){Me.remove(e,[t+"queue",n])})})}}),ge.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?ge.queue(this[0],e):void 0===t?this:this.each(function(){var n=ge.queue(this,e,t);ge._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&ge.dequeue(this,e)})},dequeue:function(e){return this.each(function(){ge.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,r=1,i=ge.Deferred(),o=this,a=this.length,s=function(){--r||i.resolveWith(o,[o])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";a--;)n=Me.get(o[a],e+"queueHooks"),n&&n.empty&&(r++,n.empty.add(s));return s(),i.promise(t)}});var $e=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,Be=new RegExp("^(?:([+-])=|)("+$e+")([a-z%]*)$","i"),We=["Top","Right","Bottom","Left"],Ue=function(e,t){return e=t||e,"none"===e.style.display||""===e.style.display&&ge.contains(e.ownerDocument,e)&&"none"===ge.css(e,"display")},ze=function(e,t,n,r){var i,o,a={};for(o in t)a[o]=e.style[o],e.style[o]=t[o];i=n.apply(e,r||[]);for(o in t)e.style[o]=a[o];return i},Xe={};ge.fn.extend({show:function(){return y(this,!0)},hide:function(){return y(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){Ue(this)?ge(this).show():ge(this).hide()})}});var Ve=/^(?:checkbox|radio)$/i,Ge=/<([a-z][^\/\0>\x20\t\r\n\f]+)/i,Ye=/^$|\/(?:java|ecma)script/i,Qe={option:[1,"<select multiple='multiple'>","</select>"],thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};Qe.optgroup=Qe.option,Qe.tbody=Qe.tfoot=Qe.colgroup=Qe.caption=Qe.thead,Qe.th=Qe.td;var Je=/<|&#?\w+;/;!function(){var e=re.createDocumentFragment(),t=e.appendChild(re.createElement("div")),n=re.createElement("input");n.setAttribute("type","radio"),n.setAttribute("checked","checked"),n.setAttribute("name","t"),t.appendChild(n),he.checkClone=t.cloneNode(!0).cloneNode(!0).lastChild.checked,t.innerHTML="<textarea>x</textarea>",he.noCloneChecked=!!t.cloneNode(!0).lastChild.defaultValue}();var Ke=re.documentElement,Ze=/^key/,et=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,tt=/^([^.]*)(?:\.(.+)|)/;ge.event={global:{},add:function(e,t,n,r,i){var o,a,s,l,u,c,p,f,d,h,v,g=Me.get(e);if(g)for(n.handler&&(o=n,n=o.handler,i=o.selector),i&&ge.find.matchesSelector(Ke,i),n.guid||(n.guid=ge.guid++),(l=g.events)||(l=g.events={}),(a=g.handle)||(a=g.handle=function(t){return"undefined"!=typeof ge&&ge.event.triggered!==t.type?ge.event.dispatch.apply(e,arguments):void 0}),t=(t||"").match(_e)||[""],u=t.length;u--;)s=tt.exec(t[u])||[],d=v=s[1],h=(s[2]||"").split(".").sort(),d&&(p=ge.event.special[d]||{},d=(i?p.delegateType:p.bindType)||d,p=ge.event.special[d]||{},c=ge.extend({type:d,origType:v,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&ge.expr.match.needsContext.test(i),namespace:h.join(".")},o),(f=l[d])||(f=l[d]=[],f.delegateCount=0,p.setup&&p.setup.call(e,r,h,a)!==!1||e.addEventListener&&e.addEventListener(d,a)),p.add&&(p.add.call(e,c),c.handler.guid||(c.handler.guid=n.guid)),i?f.splice(f.delegateCount++,0,c):f.push(c),ge.event.global[d]=!0)},remove:function(e,t,n,r,i){var o,a,s,l,u,c,p,f,d,h,v,g=Me.hasData(e)&&Me.get(e);if(g&&(l=g.events)){for(t=(t||"").match(_e)||[""],u=t.length;u--;)if(s=tt.exec(t[u])||[],d=v=s[1],h=(s[2]||"").split(".").sort(),d){for(p=ge.event.special[d]||{},d=(r?p.delegateType:p.bindType)||d,f=l[d]||[],s=s[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),a=o=f.length;o--;)c=f[o],!i&&v!==c.origType||n&&n.guid!==c.guid||s&&!s.test(c.namespace)||r&&r!==c.selector&&("**"!==r||!c.selector)||(f.splice(o,1),c.selector&&f.delegateCount--,p.remove&&p.remove.call(e,c));a&&!f.length&&(p.teardown&&p.teardown.call(e,h,g.handle)!==!1||ge.removeEvent(e,d,g.handle),delete l[d])}else for(d in l)ge.event.remove(e,d+t[u],n,r,!0);ge.isEmptyObject(l)&&Me.remove(e,"handle events")}},dispatch:function(e){var t,n,r,i,o,a,s=ge.event.fix(e),l=new Array(arguments.length),u=(Me.get(this,"events")||{})[s.type]||[],c=ge.event.special[s.type]||{};for(l[0]=s,t=1;t<arguments.length;t++)l[t]=arguments[t];if(s.delegateTarget=this,!c.preDispatch||c.preDispatch.call(this,s)!==!1){for(a=ge.event.handlers.call(this,s,u),t=0;(i=a[t++])&&!s.isPropagationStopped();)for(s.currentTarget=i.elem,n=0;(o=i.handlers[n++])&&!s.isImmediatePropagationStopped();)s.rnamespace&&!s.rnamespace.test(o.namespace)||(s.handleObj=o,s.data=o.data,r=((ge.event.special[o.origType]||{}).handle||o.handler).apply(i.elem,l),void 0!==r&&(s.result=r)===!1&&(s.preventDefault(),s.stopPropagation()));return c.postDispatch&&c.postDispatch.call(this,s),s.result}},handlers:function(e,t){var n,r,i,o,a,s=[],l=t.delegateCount,u=e.target;if(l&&u.nodeType&&!("click"===e.type&&e.button>=1))for(;u!==this;u=u.parentNode||this)if(1===u.nodeType&&("click"!==e.type||u.disabled!==!0)){for(o=[],a={},n=0;n<l;n++)r=t[n],i=r.selector+" ",void 0===a[i]&&(a[i]=r.needsContext?ge(i,this).index(u)>-1:ge.find(i,this,null,[u]).length),a[i]&&o.push(r);o.length&&s.push({elem:u,handlers:o})}return u=this,l<t.length&&s.push({elem:u,handlers:t.slice(l)}),s},addProp:function(e,t){Object.defineProperty(ge.Event.prototype,e,{enumerable:!0,configurable:!0,get:ge.isFunction(t)?function(){if(this.originalEvent)return t(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set:function(t){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:t})}})},fix:function(e){return e[ge.expando]?e:new ge.Event(e)},special:{load:{noBubble:!0},focus:{trigger:function(){if(this!==T()&&this.focus)return this.focus(),!1},delegateType:"focusin"},blur:{trigger:function(){if(this===T()&&this.blur)return this.blur(),!1},delegateType:"focusout"},click:{trigger:function(){if("checkbox"===this.type&&this.click&&o(this,"input"))return this.click(),!1},_default:function(e){return o(e.target,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},ge.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},ge.Event=function(e,t){return this instanceof ge.Event?(e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&e.returnValue===!1?w:C,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&ge.extend(this,t),this.timeStamp=e&&e.timeStamp||ge.now(),void(this[ge.expando]=!0)):new ge.Event(e,t)},ge.Event.prototype={constructor:ge.Event,isDefaultPrevented:C,isPropagationStopped:C,isImmediatePropagationStopped:C,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=w,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=w,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=w,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},ge.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,"char":!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:function(e){var t=e.button;return null==e.which&&Ze.test(e.type)?null!=e.charCode?e.charCode:e.keyCode:!e.which&&void 0!==t&&et.test(e.type)?1&t?1:2&t?3:4&t?2:0:e.which}},ge.event.addProp),ge.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,t){ge.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,r=this,i=e.relatedTarget,o=e.handleObj;return i&&(i===r||ge.contains(r,i))||(e.type=o.origType,n=o.handler.apply(this,arguments),e.type=t),n}}}),ge.fn.extend({on:function(e,t,n,r){return E(this,e,t,n,r)},one:function(e,t,n,r){return E(this,e,t,n,r,1)},off:function(t,n,r){var i,o;if(t&&t.preventDefault&&t.handleObj)return i=t.handleObj,ge(t.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"===e(t)){for(o in t)this.off(o,n,t[o]);return this}return n!==!1&&"function"!=typeof n||(r=n,n=void 0),r===!1&&(r=C),this.each(function(){ge.event.remove(this,t,r,n)})}});var nt=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([a-z][^\/\0>\x20\t\r\n\f]*)[^>]*)\/>/gi,rt=/<script|<style|<link/i,it=/checked\s*(?:[^=]|=\s*.checked.)/i,ot=/^true\/(.*)/,at=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;ge.extend({htmlPrefilter:function(e){return e.replace(nt,"<$1></$2>")},clone:function nn(e,t,n){var r,i,o,a,nn=e.cloneNode(!0),s=ge.contains(e.ownerDocument,e);if(!(he.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||ge.isXMLDoc(e)))for(a=b(nn),o=b(e),r=0,i=o.length;r<i;r++)j(o[r],a[r]);if(t)if(n)for(o=o||b(e),a=a||b(nn),r=0,i=o.length;r<i;r++)H(o[r],a[r]);else H(e,nn);return a=b(nn,"script"),a.length>0&&x(a,!s&&b(e,"script")),nn},cleanData:function(e){for(var t,n,r,i=ge.event.special,o=0;void 0!==(n=e[o]);o++)if(Oe(n)){if(t=n[Me.expando]){if(t.events)for(r in t.events)i[r]?ge.event.remove(n,r):ge.removeEvent(n,r,t.handle);n[Me.expando]=void 0}n[Fe.expando]&&(n[Fe.expando]=void 0)}}}),ge.fn.extend({detach:function(e){return _(this,e,!0)},remove:function(e){return _(this,e)},text:function(e){return qe(this,function(e){return void 0===e?ge.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return N(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=L(this,e);t.appendChild(e)}})},prepend:function(){return N(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=L(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return N(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return N(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(ge.cleanData(b(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return ge.clone(this,e,t)})},html:function(e){return qe(this,function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!rt.test(e)&&!Qe[(Ge.exec(e)||["",""])[1].toLowerCase()]){e=ge.htmlPrefilter(e);try{for(;n<r;n++)t=this[n]||{},1===t.nodeType&&(ge.cleanData(b(t,!1)),t.innerHTML=e);t=0}catch(i){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var e=[];return N(this,arguments,function(t){var n=this.parentNode;ge.inArray(this,e)<0&&(ge.cleanData(b(this)),n&&n.replaceChild(t,this))},e)}}),ge.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){ge.fn[e]=function(e){for(var n,r=[],i=ge(e),o=i.length-1,a=0;a<=o;a++)n=a===o?this:this.clone(!0),ge(i[a])[t](n),se.apply(r,n.get());return this.pushStack(r)}});var st=/^margin/,lt=new RegExp("^("+$e+")(?!px)[a-z%]+$","i"),ut=function(e){var n=e.ownerDocument.defaultView;return n&&n.opener||(n=t),n.getComputedStyle(e)};!function(){function e(){if(s){s.style.cssText="box-sizing:border-box;position:relative;display:block;margin:auto;border:1px;padding:1px;top:1%;width:50%",s.innerHTML="",Ke.appendChild(a);var e=t.getComputedStyle(s);n="1%"!==e.top,o="2px"===e.marginLeft,r="4px"===e.width,s.style.marginRight="50%",i="4px"===e.marginRight,Ke.removeChild(a),s=null}}var n,r,i,o,a=re.createElement("div"),s=re.createElement("div");s.style&&(s.style.backgroundClip="content-box",s.cloneNode(!0).style.backgroundClip="",he.clearCloneStyle="content-box"===s.style.backgroundClip,a.style.cssText="border:0;width:8px;height:0;top:0;left:-9999px;padding:0;margin-top:1px;position:absolute",a.appendChild(s),ge.extend(he,{pixelPosition:function(){return e(),n},boxSizingReliable:function(){return e(),r},pixelMarginRight:function(){return e(),i},reliableMarginLeft:function(){return e(),o}}))}();var ct=/^(none|table(?!-c[ea]).+)/,pt=/^--/,ft={position:"absolute",visibility:"hidden",display:"block"},dt={letterSpacing:"0",fontWeight:"400"},ht=["Webkit","Moz","ms"],vt=re.createElement("div").style;ge.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=A(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{"float":"cssFloat"},style:function rn(t,n,r,i){if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style){var o,a,s,l=ge.camelCase(n),u=pt.test(n),rn=t.style;return u||(n=O(l)),s=ge.cssHooks[n]||ge.cssHooks[l],void 0===r?s&&"get"in s&&void 0!==(o=s.get(t,!1,i))?o:rn[n]:(a=e(r),"string"===a&&(o=Be.exec(r))&&o[1]&&(r=g(t,n,o),a="number"),null!=r&&r===r&&("number"===a&&(r+=o&&o[3]||(ge.cssNumber[l]?"":"px")),he.clearCloneStyle||""!==r||0!==n.indexOf("background")||(rn[n]="inherit"),s&&"set"in s&&void 0===(r=s.set(t,r,i))||(u?rn.setProperty(n,r):rn[n]=r)),void 0)}},css:function(e,t,n,r){var i,o,a,s=ge.camelCase(t),l=pt.test(t);return l||(t=O(s)),a=ge.cssHooks[t]||ge.cssHooks[s],a&&"get"in a&&(i=a.get(e,!0,n)),void 0===i&&(i=A(e,t,r)),"normal"===i&&t in dt&&(i=dt[t]),""===n||n?(o=parseFloat(i),n===!0||isFinite(o)?o||0:i):i}}),ge.each(["height","width"],function(e,t){ge.cssHooks[t]={get:function(e,n,r){if(n)return!ct.test(ge.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?R(e,t,r):ze(e,ft,function(){return R(e,t,r)})},set:function(e,n,r){var i,o=r&&ut(e),a=r&&F(e,t,r,"border-box"===ge.css(e,"boxSizing",!1,o),o);return a&&(i=Be.exec(n))&&"px"!==(i[3]||"px")&&(e.style[t]=n,n=ge.css(e,t)),M(e,n,a)}}}),ge.cssHooks.marginLeft=P(he.reliableMarginLeft,function(e,t){if(t)return(parseFloat(A(e,"marginLeft"))||e.getBoundingClientRect().left-ze(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),ge.each({margin:"",padding:"",border:"Width"},function(e,t){ge.cssHooks[e+t]={expand:function(n){for(var r=0,i={},o="string"==typeof n?n.split(" "):[n];r<4;r++)i[e+We[r]+t]=o[r]||o[r-2]||o[0];return i}},st.test(e)||(ge.cssHooks[e+t].set=M)}),ge.fn.extend({css:function(e,t){return qe(this,function(e,t,n){var r,i,o={},a=0;if(Array.isArray(t)){for(r=ut(e),i=t.length;a<i;a++)o[t[a]]=ge.css(e,t[a],!1,r);return o}return void 0!==n?ge.style(e,t,n):ge.css(e,t)},e,t,arguments.length>1)}}),ge.Tween=I,I.prototype={constructor:I,init:function(e,t,n,r,i,o){this.elem=e,this.prop=n,this.easing=i||ge.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=o||(ge.cssNumber[n]?"":"px")},cur:function(){var e=I.propHooks[this.prop];return e&&e.get?e.get(this):I.propHooks._default.get(this)},run:function(e){var t,n=I.propHooks[this.prop];return this.options.duration?this.pos=t=ge.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):I.propHooks._default.set(this),this}},I.prototype.init.prototype=I.prototype,I.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=ge.css(e.elem,e.prop,""),t&&"auto"!==t?t:0)},set:function(e){ge.fx.step[e.prop]?ge.fx.step[e.prop](e):1!==e.elem.nodeType||null==e.elem.style[ge.cssProps[e.prop]]&&!ge.cssHooks[e.prop]?e.elem[e.prop]=e.now:ge.style(e.elem,e.prop,e.now+e.unit);
}}},I.propHooks.scrollTop=I.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},ge.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},ge.fx=I.prototype.init,ge.fx.step={};var gt,mt,yt=/^(?:toggle|show|hide)$/,bt=/queueHooks$/;ge.Animation=ge.extend(V,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return g(n.elem,e,Be.exec(t),n),n}]},tweener:function(e,t){ge.isFunction(e)?(t=e,e=["*"]):e=e.match(_e);for(var n,r=0,i=e.length;r<i;r++)n=e[r],V.tweeners[n]=V.tweeners[n]||[],V.tweeners[n].unshift(t)},prefilters:[z],prefilter:function(e,t){t?V.prefilters.unshift(e):V.prefilters.push(e)}}),ge.speed=function(t,n,r){var i=t&&"object"===e(t)?ge.extend({},t):{complete:r||!r&&n||ge.isFunction(t)&&t,duration:t,easing:r&&n||n&&!ge.isFunction(n)&&n};return ge.fx.off?i.duration=0:"number"!=typeof i.duration&&(i.duration in ge.fx.speeds?i.duration=ge.fx.speeds[i.duration]:i.duration=ge.fx.speeds._default),null!=i.queue&&i.queue!==!0||(i.queue="fx"),i.old=i.complete,i.complete=function(){ge.isFunction(i.old)&&i.old.call(this),i.queue&&ge.dequeue(this,i.queue)},i},ge.fn.extend({fadeTo:function(e,t,n,r){return this.filter(Ue).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(e,t,n,r){var i=ge.isEmptyObject(e),o=ge.speed(t,n,r),a=function(){var t=V(this,ge.extend({},e),o);(i||Me.get(this,"finish"))&&t.stop(!0)};return a.finish=a,i||o.queue===!1?this.each(a):this.queue(o.queue,a)},stop:function(e,t,n){var r=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=void 0),t&&e!==!1&&this.queue(e||"fx",[]),this.each(function(){var t=!0,i=null!=e&&e+"queueHooks",o=ge.timers,a=Me.get(this);if(i)a[i]&&a[i].stop&&r(a[i]);else for(i in a)a[i]&&a[i].stop&&bt.test(i)&&r(a[i]);for(i=o.length;i--;)o[i].elem!==this||null!=e&&o[i].queue!==e||(o[i].anim.stop(n),t=!1,o.splice(i,1));!t&&n||ge.dequeue(this,e)})},finish:function(e){return e!==!1&&(e=e||"fx"),this.each(function(){var t,n=Me.get(this),r=n[e+"queue"],i=n[e+"queueHooks"],o=ge.timers,a=r?r.length:0;for(n.finish=!0,ge.queue(this,e,[]),i&&i.stop&&i.stop.call(this,!0),t=o.length;t--;)o[t].elem===this&&o[t].queue===e&&(o[t].anim.stop(!0),o.splice(t,1));for(t=0;t<a;t++)r[t]&&r[t].finish&&r[t].finish.call(this);delete n.finish})}}),ge.each(["toggle","show","hide"],function(e,t){var n=ge.fn[t];ge.fn[t]=function(e,r,i){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(W(t,!0),e,r,i)}}),ge.each({slideDown:W("show"),slideUp:W("hide"),slideToggle:W("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,t){ge.fn[e]=function(e,n,r){return this.animate(t,e,n,r)}}),ge.timers=[],ge.fx.tick=function(){var e,t=0,n=ge.timers;for(gt=ge.now();t<n.length;t++)e=n[t],e()||n[t]!==e||n.splice(t--,1);n.length||ge.fx.stop(),gt=void 0},ge.fx.timer=function(e){ge.timers.push(e),ge.fx.start()},ge.fx.interval=13,ge.fx.start=function(){mt||(mt=!0,$())},ge.fx.stop=function(){mt=null},ge.fx.speeds={slow:600,fast:200,_default:400},ge.fn.delay=function(e,n){return e=ge.fx?ge.fx.speeds[e]||e:e,n=n||"fx",this.queue(n,function(n,r){var i=t.setTimeout(n,e);r.stop=function(){t.clearTimeout(i)}})},function(){var e=re.createElement("input"),t=re.createElement("select"),n=t.appendChild(re.createElement("option"));e.type="checkbox",he.checkOn=""!==e.value,he.optSelected=n.selected,e=re.createElement("input"),e.value="t",e.type="radio",he.radioValue="t"===e.value}();var xt,kt=ge.expr.attrHandle;ge.fn.extend({attr:function(e,t){return qe(this,ge.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){ge.removeAttr(this,e)})}}),ge.extend({attr:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return"undefined"==typeof e.getAttribute?ge.prop(e,t,n):(1===o&&ge.isXMLDoc(e)||(i=ge.attrHooks[t.toLowerCase()]||(ge.expr.match.bool.test(t)?xt:void 0)),void 0!==n?null===n?void ge.removeAttr(e,t):i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:(e.setAttribute(t,n+""),n):i&&"get"in i&&null!==(r=i.get(e,t))?r:(r=ge.find.attr(e,t),null==r?void 0:r))},attrHooks:{type:{set:function(e,t){if(!he.radioValue&&"radio"===t&&o(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,r=0,i=t&&t.match(_e);if(i&&1===e.nodeType)for(;n=i[r++];)e.removeAttribute(n)}}),xt={set:function(e,t,n){return t===!1?ge.removeAttr(e,n):e.setAttribute(n,n),n}},ge.each(ge.expr.match.bool.source.match(/\w+/g),function(e,t){var n=kt[t]||ge.find.attr;kt[t]=function(e,t,r){var i,o,a=t.toLowerCase();return r||(o=kt[a],kt[a]=i,i=null!=n(e,t,r)?a:null,kt[a]=o),i}});var wt=/^(?:input|select|textarea|button)$/i,Ct=/^(?:a|area)$/i;ge.fn.extend({prop:function(e,t){return qe(this,ge.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each(function(){delete this[ge.propFix[e]||e]})}}),ge.extend({prop:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&ge.isXMLDoc(e)||(t=ge.propFix[t]||t,i=ge.propHooks[t]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){var t=ge.find.attr(e,"tabindex");return t?parseInt(t,10):wt.test(e.nodeName)||Ct.test(e.nodeName)&&e.href?0:-1}}},propFix:{"for":"htmlFor","class":"className"}}),he.optSelected||(ge.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),ge.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){ge.propFix[this.toLowerCase()]=this}),ge.fn.extend({addClass:function(e){var t,n,r,i,o,a,s,l=0;if(ge.isFunction(e))return this.each(function(t){ge(this).addClass(e.call(this,t,Y(this)))});if("string"==typeof e&&e)for(t=e.match(_e)||[];n=this[l++];)if(i=Y(n),r=1===n.nodeType&&" "+G(i)+" "){for(a=0;o=t[a++];)r.indexOf(" "+o+" ")<0&&(r+=o+" ");s=G(r),i!==s&&n.setAttribute("class",s)}return this},removeClass:function(e){var t,n,r,i,o,a,s,l=0;if(ge.isFunction(e))return this.each(function(t){ge(this).removeClass(e.call(this,t,Y(this)))});if(!arguments.length)return this.attr("class","");if("string"==typeof e&&e)for(t=e.match(_e)||[];n=this[l++];)if(i=Y(n),r=1===n.nodeType&&" "+G(i)+" "){for(a=0;o=t[a++];)for(;r.indexOf(" "+o+" ")>-1;)r=r.replace(" "+o+" "," ");s=G(r),i!==s&&n.setAttribute("class",s)}return this},toggleClass:function(t,n){var r=e(t);return"boolean"==typeof n&&"string"===r?n?this.addClass(t):this.removeClass(t):ge.isFunction(t)?this.each(function(e){ge(this).toggleClass(t.call(this,e,Y(this),n),n)}):this.each(function(){var e,n,i,o;if("string"===r)for(n=0,i=ge(this),o=t.match(_e)||[];e=o[n++];)i.hasClass(e)?i.removeClass(e):i.addClass(e);else void 0!==t&&"boolean"!==r||(e=Y(this),e&&Me.set(this,"__className__",e),this.setAttribute&&this.setAttribute("class",e||t===!1?"":Me.get(this,"__className__")||""))})},hasClass:function(e){var t,n,r=0;for(t=" "+e+" ";n=this[r++];)if(1===n.nodeType&&(" "+G(Y(n))+" ").indexOf(t)>-1)return!0;return!1}});var Tt=/\r/g;ge.fn.extend({val:function(e){var t,n,r,i=this[0];{if(arguments.length)return r=ge.isFunction(e),this.each(function(n){var i;1===this.nodeType&&(i=r?e.call(this,n,ge(this).val()):e,null==i?i="":"number"==typeof i?i+="":Array.isArray(i)&&(i=ge.map(i,function(e){return null==e?"":e+""})),t=ge.valHooks[this.type]||ge.valHooks[this.nodeName.toLowerCase()],t&&"set"in t&&void 0!==t.set(this,i,"value")||(this.value=i))});if(i)return t=ge.valHooks[i.type]||ge.valHooks[i.nodeName.toLowerCase()],t&&"get"in t&&void 0!==(n=t.get(i,"value"))?n:(n=i.value,"string"==typeof n?n.replace(Tt,""):null==n?"":n)}}}),ge.extend({valHooks:{option:{get:function(e){var t=ge.find.attr(e,"value");return null!=t?t:G(ge.text(e))}},select:{get:function(e){var t,n,r,i=e.options,a=e.selectedIndex,s="select-one"===e.type,l=s?null:[],u=s?a+1:i.length;for(r=a<0?u:s?a:0;r<u;r++)if(n=i[r],(n.selected||r===a)&&!n.disabled&&(!n.parentNode.disabled||!o(n.parentNode,"optgroup"))){if(t=ge(n).val(),s)return t;l.push(t)}return l},set:function(e,t){for(var n,r,i=e.options,o=ge.makeArray(t),a=i.length;a--;)r=i[a],(r.selected=ge.inArray(ge.valHooks.option.get(r),o)>-1)&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),ge.each(["radio","checkbox"],function(){ge.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=ge.inArray(ge(e).val(),t)>-1}},he.checkOn||(ge.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})});var Et=/^(?:focusinfocus|focusoutblur)$/;ge.extend(ge.event,{trigger:function(n,r,i,o){var a,s,l,u,c,p,f,d=[i||re],h=pe.call(n,"type")?n.type:n,v=pe.call(n,"namespace")?n.namespace.split("."):[];if(s=l=i=i||re,3!==i.nodeType&&8!==i.nodeType&&!Et.test(h+ge.event.triggered)&&(h.indexOf(".")>-1&&(v=h.split("."),h=v.shift(),v.sort()),c=h.indexOf(":")<0&&"on"+h,n=n[ge.expando]?n:new ge.Event(h,"object"===e(n)&&n),n.isTrigger=o?2:3,n.namespace=v.join("."),n.rnamespace=n.namespace?new RegExp("(^|\\.)"+v.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,n.result=void 0,n.target||(n.target=i),r=null==r?[n]:ge.makeArray(r,[n]),f=ge.event.special[h]||{},o||!f.trigger||f.trigger.apply(i,r)!==!1)){if(!o&&!f.noBubble&&!ge.isWindow(i)){for(u=f.delegateType||h,Et.test(u+h)||(s=s.parentNode);s;s=s.parentNode)d.push(s),l=s;l===(i.ownerDocument||re)&&d.push(l.defaultView||l.parentWindow||t)}for(a=0;(s=d[a++])&&!n.isPropagationStopped();)n.type=a>1?u:f.bindType||h,p=(Me.get(s,"events")||{})[n.type]&&Me.get(s,"handle"),p&&p.apply(s,r),p=c&&s[c],p&&p.apply&&Oe(s)&&(n.result=p.apply(s,r),n.result===!1&&n.preventDefault());return n.type=h,o||n.isDefaultPrevented()||f._default&&f._default.apply(d.pop(),r)!==!1||!Oe(i)||c&&ge.isFunction(i[h])&&!ge.isWindow(i)&&(l=i[c],l&&(i[c]=null),ge.event.triggered=h,i[h](),ge.event.triggered=void 0,l&&(i[c]=l)),n.result}},simulate:function(e,t,n){var r=ge.extend(new ge.Event,n,{type:e,isSimulated:!0});ge.event.trigger(r,null,t)}}),ge.fn.extend({trigger:function(e,t){return this.each(function(){ge.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return ge.event.trigger(e,t,n,!0)}}),ge.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,t){ge.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}}),ge.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),he.focusin="onfocusin"in t,he.focusin||ge.each({focus:"focusin",blur:"focusout"},function(e,t){var n=function(e){ge.event.simulate(t,e.target,ge.event.fix(e))};ge.event.special[t]={setup:function(){var r=this.ownerDocument||this,i=Me.access(r,t);i||r.addEventListener(e,n,!0),Me.access(r,t,(i||0)+1)},teardown:function(){var r=this.ownerDocument||this,i=Me.access(r,t)-1;i?Me.access(r,t,i):(r.removeEventListener(e,n,!0),Me.remove(r,t))}}});var Lt=t.location,St=ge.now(),Dt=/\?/;ge.parseXML=function(e){var n;if(!e||"string"!=typeof e)return null;try{n=(new t.DOMParser).parseFromString(e,"text/xml")}catch(r){n=void 0}return n&&!n.getElementsByTagName("parsererror").length||ge.error("Invalid XML: "+e),n};var Ht=/\[\]$/,jt=/\r?\n/g,Nt=/^(?:submit|button|image|reset|file)$/i,_t=/^(?:input|select|textarea|keygen)/i;ge.param=function(e,t){var n,r=[],i=function(e,t){var n=ge.isFunction(t)?t():t;r[r.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==n?"":n)};if(Array.isArray(e)||e.jquery&&!ge.isPlainObject(e))ge.each(e,function(){i(this.name,this.value)});else for(n in e)Q(n,e[n],t,i);return r.join("&")},ge.fn.extend({serialize:function(){return ge.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=ge.prop(this,"elements");return e?ge.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!ge(this).is(":disabled")&&_t.test(this.nodeName)&&!Nt.test(e)&&(this.checked||!Ve.test(e))}).map(function(e,t){var n=ge(this).val();return null==n?null:Array.isArray(n)?ge.map(n,function(e){return{name:t.name,value:e.replace(jt,"\r\n")}}):{name:t.name,value:n.replace(jt,"\r\n")}}).get()}});var At=/%20/g,Pt=/#.*$/,qt=/([?&])_=[^&]*/,Ot=/^(.*?):[ \t]*([^\r\n]*)$/gm,Mt=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,Ft=/^(?:GET|HEAD)$/,Rt=/^\/\//,It={},$t={},Bt="*/".concat("*"),Wt=re.createElement("a");Wt.href=Lt.href,ge.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Lt.href,type:"GET",isLocal:Mt.test(Lt.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Bt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":ge.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?Z(Z(e,ge.ajaxSettings),t):Z(ge.ajaxSettings,e)},ajaxPrefilter:J(It),ajaxTransport:J($t),ajax:function(n,r){function i(e,n,r,i){var l,c,d,h,k,w=n;p||(p=!0,u&&t.clearTimeout(u),o=void 0,s=i||"",T.readyState=e>0?4:0,l=e>=200&&e<300||304===e,r&&(h=ee(v,T,r)),h=te(v,h,T,l),l?(v.ifModified&&(k=T.getResponseHeader("Last-Modified"),k&&(ge.lastModified[a]=k),k=T.getResponseHeader("etag"),k&&(ge.etag[a]=k)),204===e||"HEAD"===v.type?w="nocontent":304===e?w="notmodified":(w=h.state,c=h.data,d=h.error,l=!d)):(d=w,!e&&w||(w="error",e<0&&(e=0))),T.status=e,T.statusText=(n||w)+"",l?y.resolveWith(g,[c,w,T]):y.rejectWith(g,[T,w,d]),T.statusCode(x),x=void 0,f&&m.trigger(l?"ajaxSuccess":"ajaxError",[T,v,l?c:d]),b.fireWith(g,[T,w]),f&&(m.trigger("ajaxComplete",[T,v]),--ge.active||ge.event.trigger("ajaxStop")))}"object"===e(n)&&(r=n,n=void 0),r=r||{};var o,a,s,l,u,c,p,f,d,h,v=ge.ajaxSetup({},r),g=v.context||v,m=v.context&&(g.nodeType||g.jquery)?ge(g):ge.event,y=ge.Deferred(),b=ge.Callbacks("once memory"),x=v.statusCode||{},k={},w={},C="canceled",T={readyState:0,getResponseHeader:function(e){var t;if(p){if(!l)for(l={};t=Ot.exec(s);)l[t[1].toLowerCase()]=t[2];t=l[e.toLowerCase()]}return null==t?null:t},getAllResponseHeaders:function(){return p?s:null},setRequestHeader:function(e,t){return null==p&&(e=w[e.toLowerCase()]=w[e.toLowerCase()]||e,k[e]=t),this},overrideMimeType:function(e){return null==p&&(v.mimeType=e),this},statusCode:function(e){var t;if(e)if(p)T.always(e[T.status]);else for(t in e)x[t]=[x[t],e[t]];return this},abort:function(e){var t=e||C;return o&&o.abort(t),i(0,t),this}};if(y.promise(T),v.url=((n||v.url||Lt.href)+"").replace(Rt,Lt.protocol+"//"),v.type=r.method||r.type||v.method||v.type,v.dataTypes=(v.dataType||"*").toLowerCase().match(_e)||[""],null==v.crossDomain){c=re.createElement("a");try{c.href=v.url,c.href=c.href,v.crossDomain=Wt.protocol+"//"+Wt.host!=c.protocol+"//"+c.host}catch(E){v.crossDomain=!0}}if(v.data&&v.processData&&"string"!=typeof v.data&&(v.data=ge.param(v.data,v.traditional)),K(It,v,r,T),p)return T;f=ge.event&&v.global,f&&0===ge.active++&&ge.event.trigger("ajaxStart"),v.type=v.type.toUpperCase(),v.hasContent=!Ft.test(v.type),a=v.url.replace(Pt,""),v.hasContent?v.data&&v.processData&&0===(v.contentType||"").indexOf("application/x-www-form-urlencoded")&&(v.data=v.data.replace(At,"+")):(h=v.url.slice(a.length),v.data&&(a+=(Dt.test(a)?"&":"?")+v.data,delete v.data),v.cache===!1&&(a=a.replace(qt,"$1"),h=(Dt.test(a)?"&":"?")+"_="+St++ +h),v.url=a+h),v.ifModified&&(ge.lastModified[a]&&T.setRequestHeader("If-Modified-Since",ge.lastModified[a]),ge.etag[a]&&T.setRequestHeader("If-None-Match",ge.etag[a])),(v.data&&v.hasContent&&v.contentType!==!1||r.contentType)&&T.setRequestHeader("Content-Type",v.contentType),T.setRequestHeader("Accept",v.dataTypes[0]&&v.accepts[v.dataTypes[0]]?v.accepts[v.dataTypes[0]]+("*"!==v.dataTypes[0]?", "+Bt+"; q=0.01":""):v.accepts["*"]);for(d in v.headers)T.setRequestHeader(d,v.headers[d]);if(v.beforeSend&&(v.beforeSend.call(g,T,v)===!1||p))return T.abort();if(C="abort",b.add(v.complete),T.done(v.success),T.fail(v.error),o=K($t,v,r,T)){if(T.readyState=1,f&&m.trigger("ajaxSend",[T,v]),p)return T;v.async&&v.timeout>0&&(u=t.setTimeout(function(){T.abort("timeout")},v.timeout));try{p=!1,o.send(k,i)}catch(E){if(p)throw E;i(-1,E)}}else i(-1,"No Transport");return T},getJSON:function(e,t,n){return ge.get(e,t,n,"json")},getScript:function(e,t){return ge.get(e,void 0,t,"script")}}),ge.each(["get","post"],function(e,t){ge[t]=function(e,n,r,i){return ge.isFunction(n)&&(i=i||r,r=n,n=void 0),ge.ajax(ge.extend({url:e,type:t,dataType:i,data:n,success:r},ge.isPlainObject(e)&&e))}}),ge._evalUrl=function(e){return ge.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,"throws":!0})},ge.fn.extend({wrapAll:function(e){var t;return this[0]&&(ge.isFunction(e)&&(e=e.call(this[0])),t=ge(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(e){return ge.isFunction(e)?this.each(function(t){ge(this).wrapInner(e.call(this,t))}):this.each(function(){var t=ge(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)})},wrap:function(e){var t=ge.isFunction(e);return this.each(function(n){ge(this).wrapAll(t?e.call(this,n):e)})},unwrap:function(e){return this.parent(e).not("body").each(function(){ge(this).replaceWith(this.childNodes)}),this}}),ge.expr.pseudos.hidden=function(e){return!ge.expr.pseudos.visible(e)},ge.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},ge.ajaxSettings.xhr=function(){try{return new t.XMLHttpRequest}catch(e){}};var Ut={0:200,1223:204},zt=ge.ajaxSettings.xhr();he.cors=!!zt&&"withCredentials"in zt,he.ajax=zt=!!zt,ge.ajaxTransport(function(e){var n,r;if(he.cors||zt&&!e.crossDomain)return{send:function(i,o){var a,s=e.xhr();if(s.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(a in e.xhrFields)s[a]=e.xhrFields[a];e.mimeType&&s.overrideMimeType&&s.overrideMimeType(e.mimeType),e.crossDomain||i["X-Requested-With"]||(i["X-Requested-With"]="XMLHttpRequest");for(a in i)s.setRequestHeader(a,i[a]);n=function(e){return function(){n&&(n=r=s.onload=s.onerror=s.onabort=s.onreadystatechange=null,"abort"===e?s.abort():"error"===e?"number"!=typeof s.status?o(0,"error"):o(s.status,s.statusText):o(Ut[s.status]||s.status,s.statusText,"text"!==(s.responseType||"text")||"string"!=typeof s.responseText?{binary:s.response}:{text:s.responseText},s.getAllResponseHeaders()))}},s.onload=n(),r=s.onerror=n("error"),void 0!==s.onabort?s.onabort=r:s.onreadystatechange=function(){4===s.readyState&&t.setTimeout(function(){n&&r()})},n=n("abort");try{s.send(e.hasContent&&e.data||null)}catch(l){if(n)throw l}},abort:function(){n&&n()}}}),ge.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),ge.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return ge.globalEval(e),e}}}),ge.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),ge.ajaxTransport("script",function(e){if(e.crossDomain){var t,n;return{send:function(r,i){t=ge("<script>").prop({charset:e.scriptCharset,src:e.url}).on("load error",n=function(e){t.remove(),n=null,e&&i("error"===e.type?404:200,e.type)}),re.head.appendChild(t[0])},abort:function(){n&&n()}}}});var Xt=[],Vt=/(=)\?(?=&|$)|\?\?/;ge.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Xt.pop()||ge.expando+"_"+St++;return this[e]=!0,e}}),ge.ajaxPrefilter("json jsonp",function(e,n,r){var i,o,a,s=e.jsonp!==!1&&(Vt.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&Vt.test(e.data)&&"data");if(s||"jsonp"===e.dataTypes[0])return i=e.jsonpCallback=ge.isFunction(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,s?e[s]=e[s].replace(Vt,"$1"+i):e.jsonp!==!1&&(e.url+=(Dt.test(e.url)?"&":"?")+e.jsonp+"="+i),e.converters["script json"]=function(){return a||ge.error(i+" was not called"),a[0]},e.dataTypes[0]="json",o=t[i],t[i]=function(){a=arguments},r.always(function(){void 0===o?ge(t).removeProp(i):t[i]=o,e[i]&&(e.jsonpCallback=n.jsonpCallback,Xt.push(i)),a&&ge.isFunction(o)&&o(a[0]),a=o=void 0}),"script"}),he.createHTMLDocument=function(){var e=re.implementation.createHTMLDocument("").body;return e.innerHTML="<form></form><form></form>",2===e.childNodes.length}(),ge.parseHTML=function(e,t,n){if("string"!=typeof e)return[];"boolean"==typeof t&&(n=t,t=!1);var r,i,o;return t||(he.createHTMLDocument?(t=re.implementation.createHTMLDocument(""),r=t.createElement("base"),r.href=re.location.href,t.head.appendChild(r)):t=re),i=Ee.exec(e),o=!n&&[],i?[t.createElement(i[1])]:(i=k([e],t,o),o&&o.length&&ge(o).remove(),ge.merge([],i.childNodes))},ge.fn.load=function(t,n,r){var i,o,a,s=this,l=t.indexOf(" ");return l>-1&&(i=G(t.slice(l)),t=t.slice(0,l)),ge.isFunction(n)?(r=n,n=void 0):n&&"object"===e(n)&&(o="POST"),s.length>0&&ge.ajax({url:t,type:o||"GET",dataType:"html",data:n}).done(function(e){a=arguments,s.html(i?ge("<div>").append(ge.parseHTML(e)).find(i):e)}).always(r&&function(e,t){s.each(function(){r.apply(this,a||[e.responseText,t,e])})}),this},ge.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){ge.fn[t]=function(e){return this.on(t,e)}}),ge.expr.pseudos.animated=function(e){return ge.grep(ge.timers,function(t){return e===t.elem}).length},ge.offset={setOffset:function(e,t,n){var r,i,o,a,s,l,u,c=ge.css(e,"position"),p=ge(e),f={};"static"===c&&(e.style.position="relative"),s=p.offset(),o=ge.css(e,"top"),l=ge.css(e,"left"),u=("absolute"===c||"fixed"===c)&&(o+l).indexOf("auto")>-1,u?(r=p.position(),a=r.top,i=r.left):(a=parseFloat(o)||0,i=parseFloat(l)||0),ge.isFunction(t)&&(t=t.call(e,n,ge.extend({},s))),null!=t.top&&(f.top=t.top-s.top+a),null!=t.left&&(f.left=t.left-s.left+i),"using"in t?t.using.call(e,f):p.css(f)}},ge.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each(function(t){ge.offset.setOffset(this,e,t)});var t,n,r,i,o=this[0];if(o)return o.getClientRects().length?(r=o.getBoundingClientRect(),t=o.ownerDocument,n=t.documentElement,i=t.defaultView,{top:r.top+i.pageYOffset-n.clientTop,left:r.left+i.pageXOffset-n.clientLeft}):{top:0,left:0}},position:function(){if(this[0]){var e,t,n=this[0],r={top:0,left:0};return"fixed"===ge.css(n,"position")?t=n.getBoundingClientRect():(e=this.offsetParent(),t=this.offset(),o(e[0],"html")||(r=e.offset()),r={top:r.top+ge.css(e[0],"borderTopWidth",!0),left:r.left+ge.css(e[0],"borderLeftWidth",!0)}),{top:t.top-r.top-ge.css(n,"marginTop",!0),left:t.left-r.left-ge.css(n,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===ge.css(e,"position");)e=e.offsetParent;return e||Ke})}}),ge.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,t){var n="pageYOffset"===t;ge.fn[e]=function(r){return qe(this,function(e,r,i){var o;return ge.isWindow(e)?o=e:9===e.nodeType&&(o=e.defaultView),void 0===i?o?o[t]:e[r]:void(o?o.scrollTo(n?o.pageXOffset:i,n?i:o.pageYOffset):e[r]=i)},e,r,arguments.length)}}),ge.each(["top","left"],function(e,t){ge.cssHooks[t]=P(he.pixelPosition,function(e,n){if(n)return n=A(e,t),lt.test(n)?ge(e).position()[t]+"px":n})}),ge.each({Height:"height",Width:"width"},function(e,t){ge.each({padding:"inner"+e,content:t,"":"outer"+e},function(n,r){ge.fn[r]=function(i,o){var a=arguments.length&&(n||"boolean"!=typeof i),s=n||(i===!0||o===!0?"margin":"border");return qe(this,function(t,n,i){var o;return ge.isWindow(t)?0===r.indexOf("outer")?t["inner"+e]:t.document.documentElement["client"+e]:9===t.nodeType?(o=t.documentElement,Math.max(t.body["scroll"+e],o["scroll"+e],t.body["offset"+e],o["offset"+e],o["client"+e])):void 0===i?ge.css(t,n,s):ge.style(t,n,i,s)},t,a?i:void 0,a)}})}),ge.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)}}),ge.holdReady=function(e){e?ge.readyWait++:ge.ready(!0)},ge.isArray=Array.isArray,ge.parseJSON=JSON.parse,ge.nodeName=o;var Gt=t.jQuery,Yt=t.$;return ge.noConflict=function(e){return t.$===ge&&(t.$=Yt),e&&t.jQuery===ge&&(t.jQuery=Gt),ge},n||(t.jQuery=t.$=ge),ge})}),h=window.sensorsDataAnalytic201505,v=h._,g=window.open,m=window.history.pushState,y=window.history.replaceState,b=!0,x=!1,k=function(){function e(){t(this,e);var n=this;this.propdefineTool={listPropHandler:function(e,t){var n=d(".sa-vtrack-event-active")[0],r=this.getAllListPropEl(n,e);h._.each(r,function(e,n){t(e,n)})},isCollectable:function(e){var t=d(".sa-vtrack-event-active")[0],n=this.positionIsLimit();if(n)return!0;var r=h.heatmap.getClosestLi(t),i=h.heatmap.getClosestLi(e);return!(!i||r!==i)},positionIsLimit:function(){var e=d(".sa-vtrack-event-active")[0],t=h.heatmap.getClosestLi(e),n=d(".sa-vtrack-event-active").data("vtrackdata");return!t||void 0!==n.element_position&&!d(".sa-vtrack-samelevel").length},removePropHighLight:function(){d(".sa-vtrack-prop-highlight").removeClass("sa-vtrack-prop-highlight"),d(".sa-vtrack-prop-highlight-samelevel").removeClass("sa-vtrack-prop-highlight-samelevel"),d(".sa-vtrack-selfprop").removeClass("sa-vtrack-selfprop"),d(".sa-vtrack-selfprop-samelevel").removeClass("sa-vtrack-selfprop-samelevel"),d(".sa-vtrack-prop-focus").removeClass("sa-vtrack-prop-focus").removeData("vtrackdata"),d(".sa-vtrack-prop-focus-samelevel").removeClass("sa-vtrack-prop-focus-samelevel"),d(".sa-vtrack-propable").removeClass("sa-vtrack-propable"),d(".sa-vtrack-propable-samelevel").removeClass("sa-vtrack-propable-samelevel")},addPropHighLight:function(e){var t=this;return!!h._.isArray(e)&&void h._.each(e,function(e){if(e.element_selector){var n=h._.getDomBySelector(e.element_selector);n&&t.addHighLightClass("sa-vtrack-prop-highlight",n)}else v.isString(e.list_selector)&&t.listPropHandler(e.list_selector,function(e,n){0===n?t.addHighLightClass("sa-vtrack-prop-highlight",e):t.addHighLightClass("sa-vtrack-prop-highlight-samelevel",e)})})},getAllListPropEl:function(e,t){var r,i,o=h.heatmap.getClosestLi(e),a=[];return v.isString(t)?(h._.isElement(o)&&(r=h.heatmap.getElementPath(o,h.para.heatmap&&"not_use_id"===h.para.heatmap.element_selector),i=n.filterElements({element_path:r}),h._.each(i,function(e){var n=h.vtrackcollect.customProp.getPropElInLi(e,t);h._.isElement(n)&&(d(e).find(".sa-vtrack-samelevel").length||d(e).find(".sa-vtrack-event-active").length||d(e).hasClass("sa-vtrack-samelevel")||d(e).hasClass("sa-vtrack-event-active"))&&(e===o?a.unshift(n):a.push(n))})),a):[]},addHighLightClass:function(e,t){t=d(t),"sa-vtrack-prop-highlight"===e?t.hasClass("sa-vtrack-event-active")||t.hasClass("sa-vtrack-samelevel")?t.addClass("sa-vtrack-selfprop"):t.addClass("sa-vtrack-prop-highlight"):"sa-vtrack-prop-highlight-samelevel"===e&&(t.hasClass("sa-vtrack-event-active")||t.hasClass("sa-vtrack-samelevel")?t.addClass("sa-vtrack-selfprop-samelevel"):t.addClass("sa-vtrack-prop-highlight-samelevel"))},getListSelector:function(e){var t="",n=h.heatmap.getClosestLi(e);if(n){var r=h.heatmap.getDomSelector(n,[],!0),i=h.heatmap.getDomSelector(e,[],!0);t=i.replace(r,"")}return t}}}return r(e,[{key:"addScreenshotClass",value:function(){var e=d(document).find(".sa-vtrack-clickable");e.removeClass("sa-vtrack-clickable"),e.is("input[type=radio]")||e.is("input[type=checkbox]")?e.wrap('<span class="sa-vtrack-event-active"></span>'):(e.addClass("sa-vtrack-event-active"),"inline"===e.css("display")&&e.addClass("sa-vtrack-event-active_inlineblock"))}},{key:"windowopenHandler",value:function(e){e?window.open=function(e){N.postMessage({source:"sa-web-sdk",type:"v-open-url"}),setTimeout(function(){window.location.href=e},100)}:window.open=g}},{key:"spaHandler",value:function(e){var t="pushState"in window.history?"popstate":"hashchange",n=function(){d(document).trigger("locationchange")},r=function(){N.postMessage({source:"sa-web-sdk",type:"v-location-change",data:{title:document.title,url:v.getURL(),url_path:location.pathname,lib:"js"}})};if(e){if(x)return!1;history.pushState=function(){m.apply(window.history,arguments),d(document).trigger("locationchange")},history.replaceState=function(){y.apply(window.history,arguments),d(document).trigger("locationchange")},d(window).on(t,n),d(document).on("locationchange",r),x=!0}else history.pushState=m,history.replaceState=y,d(window).off(t,n),d(document).off("locationchange",r),x=!1}},{key:"filterElements",value:function(e){var t;if("element_selector"!==e.element_field&&e.element_path)e.element_path&&(t=d(e.element_path),t=t.filter(function(t,n){return h.heatmap.getElementPath(n,h.para.heatmap&&"not_use_id"===h.para.heatmap.element_selector)===e.element_path}),"string"==typeof e.element_content&&(t=t.filter(function(t,n){return v.getEleInfo({target:n}).$element_content===e.element_content})),d.isNumeric(e.element_position)&&(t=t.filter(function(){var t=this.tagName.toLowerCase();if("li"===t)return d(this).parent().children("li").index(d(this))==e.element_position;var n=d(this).parents("li");if(0===n.length)return!1;var r=h.heatmap.getElementPosition(this,e.element_path,h.para.heatmap&&"not_use_id"===h.para.heatmap.element_selector);return r==e.element_position})));else{var n=e.elementSelector||e.element_selector;t=d(n)}return t}},{key:"highlightElements",value:function(e){if(!e||!d.isArray(e))return!1;var t=this;d.each(e,function(e,n){var r=t.filterElements(n);return!r.length||void r.data("vtrackdata",n).addClass("sa-vtrack-highlight")})}},{key:"unHighlightElements",value:function(){this.unHighlightEventEle(),this.propdefineTool.removePropHighLight()}},{key:"unHighlightEventEle",value:function(){d(".sa-vtrack-samelevel").removeClass("sa-vtrack-samelevel"),d(".sa-vtrack-inlineblock").removeClass("sa-vtrack-inlineblock"),d('[data-clicked="true"]').removeAttr("data-clicked"),d(".sa-vtrack-highlight").removeData("vtrackdata").removeClass("sa-vtrack-highlight"),d(".sa-vtrack-clickable").removeClass("sa-vtrack-clickable"),d(".sa-vtrack-event-active").removeData("vtrackdata").removeClass("sa-vtrack-event-active sa-vtrack-event-active_inlineblock");var e=d("*").filter(function(){return void 0!==d(this).data("vtrackdata")});e.removeData("vtrackdata")}},{key:"highLightEventEle",value:function(e){if(!v.isObject(e))return!1;var t=this.filterElements(e);if(t.length){var n=e.element_selector||e.elementSelector||"",r=d(n);r.length?(r.addClass("sa-vtrack-event-active"),r.data("vtrackdata",e),"inline"===r.css("display")&&r.addClass("sa-vtrack-event-active_inlineblock"),t.not(function(e,t){return r[0]===t}).addClass("sa-vtrack-samelevel").addClass(function(){return"inline"===d(this).css("display")?"sa-vtrack-inlineblock":""})):h.log("目标元素未找到",n)}}}]),e}(),w=function(e){function n(){return t(this,n),o.apply(this,arguments)}i(n,e);var o=c(n);return r(n,[{key:"init",value:function(){this.addListener(),this.unHighlightElements(),this.spaHandler(!0),this.windowopenHandler(!0)}},{key:"addListener",value:function(){d(document).on("click","a",this.clickHandlerViewMode)}},{key:"removeListener",value:function(){d(document).off("click","a",this.clickHandlerViewMode)}},{key:"clickHandlerViewMode",value:function(){var e=d(this).attr("href");return!(!e||0===e.indexOf("#")||0===e.indexOf("javascript:"))&&(N.postMessage({source:"sa-web-sdk",type:"v-open-url"}),setTimeout(function(){window.location.href=e},100),!1)}}]),n}(k),C=function(n){function o(){return t(this,o),a.apply(this,arguments)}i(o,n);var a=c(o);
return r(o,[{key:"init",value:function(e){this.initUnlimitedDiv(e.data),b="boolean"!=typeof e.data.isHighLight||e.data.isHighLight,this.refreshEventDefineMode(e.data)}},{key:"initUnlimitedDiv",value:function(e){var t=!1;v.isObject(e)&&e.unlimitedDiv&&(t=!0),this.isOpenUnlimitedDiv=h.para.heatmap&&h.para.heatmap.get_vtrack_config&&t}},{key:"isUnlimitedDiv",value:function(t,n){return!!this.isOpenUnlimitedDiv&&("object"!==e(t)?null:"div"===t.tagName.toLowerCase()&&!h.heatmap.getTargetElement(t,n))}},{key:"refreshEventDefineMode",value:function(e){this.addListener(),this.unHighlightElements(),b&&this.highlightElements(e.eventList),this.spaHandler(!1),this.windowopenHandler(!1)}},{key:"addListener",value:function(){document.body.addEventListener("click",this.clickHandler,!0),d(document).on("mouseover","*",this.mouseEnterHandler),d(document).on("mouseout","*",this.mouseLeaveHandler)}},{key:"removeListener",value:function(){document.body.removeEventListener("click",this.clickHandler,!0),d(document).off("mouseover","*",this.mouseEnterHandler),d(document).off("mouseout","*",this.mouseLeaveHandler)}},{key:"clickHandler",value:function(e){e.stopPropagation(),e.preventDefault();var t=e.target,n=null,r=!1,i=N.pageController;if(h.heatmap.getTargetElement(t,e))n=h.heatmap.getTargetElement(t,e);else{if(!i.isUnlimitedDiv(t,e))return!1;n=t,r=!0}if(!n)return!1;var o=n;d(".sa-vtrack-clickable").removeClass("sa-vtrack-clickable"),d(".sa-vtrack-highlight").removeClass("sa-vtrack-highlight"),d('[data-clicked="true"]').removeAttr("data-clicked"),d(o).addClass("sa-vtrack-clickable"),d(o).attr("data-clicked","true");var a=n.getClientRects()[0],s={screen_x:a.left||a.x,screen_y:a.top||a.y,element_width:a.width,element_height:a.height},l=d(o).data("vtrackdata");if(l){var u=d.extend({url:v.getURL(),url_path:location.pathname,title:document.title},l,s);u.element_selector=l.elementSelector,u.target_element_selector=h.heatmap.getDomSelector(n),u.tag_type=n.tagName.toUpperCase(),delete u.elementSelector,"undefined"==typeof u.element_content&&(u.element_content=v.getEleInfo({target:n}).$element_content);var c=h.heatmap.getElementPosition(n,u.element_path,h.para.heatmap&&"not_use_id"===h.para.heatmap.element_selector);"undefined"==typeof u.element_position&&v.isNumber(c)&&(u.element_position=c),setTimeout(function(){N.postMessage({source:"sa-web-sdk",type:"v-update-click",data:u})},300);var p=v.extend(l,{elementSelector:h.heatmap.getDomSelector(n)});i.highLightEventEle(p)}else{var f=h.heatmap.getDomSelector(n),g={unlimitedDiv:r,url:v.getURL(),url_path:location.pathname,title:document.title,element_selector:f,target_element_selector:f,element_content:v.getEleInfo({target:n}).$element_content,tag_type:n.tagName.toUpperCase(),lib:"js"};g.element_path=h.heatmap.getElementPath(n,h.para.heatmap&&"not_use_id"===h.para.heatmap.element_selector);var m=h.heatmap.getElementPosition(n,g.element_path,h.para.heatmap&&"not_use_id"===h.para.heatmap.element_selector);v.isNumber(m)&&(g.element_position=m),d.extend(g,s),g.isMultiPathWithoutPosition=function(){var e=h.heatmap.getClosestLi(o),t=!1;return document.querySelectorAll&&(t=document.querySelectorAll(g.element_path).length>1),!e&&t}(),0===f.indexOf("#")&&0===d(f).length?N.postMessage({source:"sa-web-sdk",type:"v-invalid-elementid",data:g}):setTimeout(function(){N.postMessage({source:"sa-web-sdk",type:"v-define-click",data:g})},300)}return i.addScreenshotClass(),l&&l.prop&&i.propdefineTool.addPropHighLight(l.prop),!1}},{key:"mouseEnterHandler",value:function(e){function t(e){e=d(e),e.hasClass("sa-vtrack-highlight")===!1&&e.addClass("sa-vtrack-clickable")}var n=null,r=e.target;return n=h.heatmap.getTargetElement(r,e),n?t(n):N.pageController.isUnlimitedDiv(r,e)&&t(r),!1}},{key:"mouseLeaveHandler",value:function(){return!d(this).hasClass("sa-vtrack-highlight")&&("true"!==d(this).attr("data-clicked")&&void d(this).removeClass("sa-vtrack-clickable"))}}]),o}(k),T=function(e){function n(){return t(this,n),o.apply(this,arguments)}i(n,e);var o=c(n);return r(n,[{key:"init",value:function(e){this.unHighlightElements(),e.data&&(this.highLightEventEle(e.data),e.data.prop&&this.propdefineTool.addPropHighLight(e.data.prop))}}]),n}(k),E=function(e){function n(){return t(this,n),o.apply(this,arguments)}i(n,e);var o=c(n);return r(n,[{key:"init",value:function(e){if(!d(".sa-vtrack-event-active")[0])return!1;this.refreshPropDefineMode(e.data);var t=this.propdefineTool.positionIsLimit();N.postMessage({source:"sa-web-sdk",type:"v-prop-limit",data:{isLimitList:!t}})}},{key:"refreshPropDefineMode",value:function(e){this.unHighlightElements(),this.highLightEventEle(e.event),this.addListener(),this.spaHandler(!1),this.windowopenHandler(!1)}},{key:"addListener",value:function(){return d(".sa-vtrack-event-active")[0]?(document.body.addEventListener("click",this.clickHandler,!0),d(document).on("mouseover","*",this.mouseEnterHandler),void d(document).on("mouseout","*",this.mouseLeaveHandler)):(h.log("事件元素异常,属性模式退出"),!1)}},{key:"removeListener",value:function(){document.body.removeEventListener("click",this.clickHandler,!0),d(document).off("mouseover","*",this.mouseEnterHandler),d(document).off("mouseout","*",this.mouseLeaveHandler)}},{key:"mouseEnterHandler",value:function(e){var t=e.target,n=N.pageController;if(!v.isElement(t)||"body"===t.tagName.toLowerCase()||"html"===t.tagName.toLowerCase())return!1;var r,i=n.propdefineTool.isCollectable(t),o=n.propdefineTool.positionIsLimit();if(i&&(t=d(t),t.hasClass("sa-vtrack-propable")===!1&&t.addClass("sa-vtrack-propable"),!o)){r=n.propdefineTool.getListSelector(e.target);var a=n.propdefineTool.getAllListPropEl(e.target,r);a.length&&d(a).not(function(e){return 0===e}).addClass("sa-vtrack-propable-samelevel")}return!1}},{key:"mouseLeaveHandler",value:function(){d(this).hasClass("sa-vtrack-propable")&&(d(this).removeClass("sa-vtrack-propable"),d(".sa-vtrack-propable-samelevel").removeClass("sa-vtrack-propable-samelevel"))}},{key:"clickHandler",value:function(e){e.stopPropagation(),e.preventDefault();var t,n=d(e.target),r=d(".sa-vtrack-event-active"),i=N.pageController;if(!r[0]||!(t=r.data("vtrackdata")))return h.log("事件元素未知"),!1;i.propdefineTool.removePropHighLight();var o,a,s=i.propdefineTool.isCollectable(e.target),l="";if("input"===e.target.tagName.toLowerCase())l=e.target.value;else if("select"===e.target.tagName.toLowerCase()){var u=e.target.selectedIndex;v.isNumber(u)&&v.isElement(e.target[u])&&(l=h._.getElementContent(e.target[u],"select"))}else l=h._.getElementContent(e.target,c);if(n.hasClass("sa-vtrack-propable")&&n.removeClass("sa-vtrack-propable"),s){i.propdefineTool.addHighLightClass("sa-vtrack-prop-highlight",e.target);var c=e.target.tagName.toLowerCase();if(i.propdefineTool.positionIsLimit())o={event:t,prop:{element_selector:h.heatmap.getDomSelector(e.target),list_selector:null,method:"content",value:l}};else{a=i.propdefineTool.getListSelector(e.target),o={event:t,prop:{element_selector:null,list_selector:a,method:"content",value:l}};var p=i.propdefineTool.getAllListPropEl(e.target,a);v.each(p,function(t){t!==e.target&&i.propdefineTool.addHighLightClass("sa-vtrack-prop-highlight-samelevel",t)})}N.postMessage({source:"sa-web-sdk",type:"v-prop-value",data:o})}return!1}}]),n}(k),L=function(e){function n(){return t(this,n),o.apply(this,arguments)}i(n,e);var o=c(n);return r(n,[{key:"init",value:function(e){v.isObject(e.data)&&(e.data.event&&(this.unHighlightEventEle(),this.highLightEventEle(e.data.event)),e.data.prop&&(this.propdefineTool.removePropHighLight(),this.propdefineTool.addPropHighLight(e.data.prop)))}}]),n}(k),S=function(e){function n(){return t(this,n),o.apply(this,arguments)}i(n,e);var o=c(n);return r(n,[{key:"init",value:function(e){if(!d(".sa-vtrack-event-active")[0])return!1;if(v.isObject(e.data)&&v.isObject(e.data.prop))if(e.data.prop.element_selector){var t=v.getDomBySelector(e.data.prop.element_selector);t&&(d(t).hasClass("sa-vtrack-prop-highlight")||d(t).hasClass("sa-vtrack-selfprop"))&&d(t).addClass("sa-vtrack-prop-focus")}else v.isString(e.data.prop.list_selector)&&this.propdefineTool.listPropHandler(e.data.prop.list_selector,function(e,t){(d(e).hasClass("sa-vtrack-prop-highlight")||d(e).hasClass("sa-vtrack-prop-highlight-samelevel")||d(e).hasClass("sa-vtrack-selfprop")||d(e).hasClass("sa-vtrack-selfprop-samelevel"))&&(0===t?d(e).addClass("sa-vtrack-prop-focus"):d(e).addClass("sa-vtrack-prop-focus-samelevel"))});else d(".sa-vtrack-prop-focus").removeClass("sa-vtrack-prop-focus"),d(".sa-vtrack-prop-focus-samelevel").removeClass("sa-vtrack-prop-focus-samelevel")}}]),n}(k),D=function(e){function n(){var e;return t(this,n),e=o.call(this),e.debugData=null,e}i(n,e);var o=c(n);return r(n,[{key:"init",value:function(e){this.refreshDebugMode(e.data),h.unlimitedDiv.filterWebClickEvents(e.data)}},{key:"refreshDebugMode",value:function(e){this.debugData=e,this.addListener(),this.unHighlightElements(),this.spaHandler(!1),this.windowopenHandler(!1)}},{key:"addListener",value:function(){document.body.addEventListener("click",this.clickHandler,!0),d(document).on("mouseover","*",this.mouseEnterHandler),d(document).on("mouseout","*",this.mouseLeaveHandler)}},{key:"removeListener",value:function(){document.body.removeEventListener("click",this.clickHandler,!0),d(document).off("mouseover","*",this.mouseEnterHandler),d(document).off("mouseout","*",this.mouseLeaveHandler)}},{key:"clickHandler",value:function(e){e.stopPropagation(),e.preventDefault(),d(".sa-vtrack-highlight")&&d(".sa-vtrack-highlight").removeClass("sa-vtrack-highlight");var t=N.pageController,n=e.target,r=h.heatmap.getTargetElement(n,e);if(!r&&h.unlimitedDiv.isTargetEle(n)&&(r=n),r){d(r).hasClass("sa-vtrack-highlight")===!1&&d(r).addClass("sa-vtrack-highlight");var i=h.heatmap.getDomSelector(r),o=v.getEleInfo({target:r}).$element_content,a=h.heatmap.getElementPath(r,h.para.heatmap&&"not_use_id"===h.para.heatmap.element_selector),s=h.heatmap.getElementPosition(r,a,h.para.heatmap&&"not_use_id"===h.para.heatmap.element_selector),l={$element_content:o,$element_path:a,$element_selector:i};null!==s&&(l.$element_position=s);var u={configs:null,event:{},prop:{custom:null,pre:{$title:document.title,$url:location.href,$element_content:o}}},c={event:"$WebClick",properties:l};if(v.isObject(t.debugData)&&v.isArray(t.debugData.eventList)&&t.debugData.eventList.length>0){var p=h.vtrackcollect.customProp.filterConfig(c,t.debugData.eventList),f=h.vtrackcollect.customProp.clickCustomPropMaker(c,t.debugData.eventList,p);"{}"!==JSON.stringify(f)&&(u.prop.custom=f),p.length>0&&(u.configs=p)}var g=r.getClientRects()[0],m={screen_x:g.left||g.x,screen_y:g.top||g.y,element_width:g.width,element_height:g.height};u.event=v.extend({},m,l),N.postMessage({source:"sa-web-sdk",type:"v-debug-click",data:u})}return!1}},{key:"mouseEnterHandler",value:function(e){var t=null,n=e.target;return t=h.heatmap.getTargetElement(n,e),!t&&h.unlimitedDiv.isTargetEle(n)&&(t=n),t&&(t=d(t),t.hasClass("sa-vtrack-highlight")===!1&&t.addClass("sa-vtrack-clickable")),!1}},{key:"mouseLeaveHandler",value:function(){d(this).removeClass("sa-vtrack-clickable")}}]),n}(k),H=function(e){function n(){return t(this,n),o.apply(this,arguments)}i(n,e);var o=c(n);return r(n,[{key:"init",value:function(e){this.removeHighlight(e.data)}},{key:"removeHighlight",value:function(e){if(!(v.isObject(e)&&v.isObject(e.event)&&e.event.$element_selector))return!1;var t=v.getDomBySelector(e.event.$element_selector);t&&d(t).hasClass("sa-vtrack-highlight")&&d(t).removeClass("sa-vtrack-highlight")}}]),n}(k),j=function(){return{"v-view-mode":new w,"v-define-mode":new C,"v-limit-condition":new T,"v-prop-define":new E,"v-highlight-update":new L,"v-prop-hover":new S,"v-debug-mode":new D,"v-debug-close":new H}},N={vtrack_version:"1.24.13",messageHandler:{},pageController:null,postMessage:function(t){if(document.getElementById("parent")){try{console.error("window.parent 被重写，请检查页面有无id为parent的元素")}catch(n){}return!1}"object"===e(t)&&("object"!==e(t.data)&&(t.data={}),t.data.sdkversion=h.lib_version),window.parent.postMessage(t,"*")},listenMessage:function(){var e=this;d(window).on("message",function(t){var n=t.originalEvent.data;return!(!n||"sa-fe"!==n.source)&&void(e.messageHandler[n.type]&&(e.messageHandler[n.type].addListener&&(e.pageController&&e.pageController.removeListener&&e.pageController.removeListener(),e.pageController=e.messageHandler[n.type]),e.messageHandler[n.type].init(n)))})},setCssStyle:function(){var e=".sa-vtrack-highlight { background: rgba(176, 229, 161, 0.2) !important; outline: 1px solid #55B837 !important; outline-offset: -1px !important;}";e+=".sa-vtrack-clickable { outline: 1px solid #55B837 !important; outline-offset: -1px !important;  }",e+="a.sa-vtrack-highlight, a.sa-vtrack-clickable { display: inline-block; }",e+=".sa-vtrack-event-active { background: rgba(176, 229, 161, 0.2) !important; outline: 2px solid #55B837 !important; outline-offset: -2px !important; }",e+=".sa-vtrack-event-active_inlineblock { display: inline-block; }",e+=".sa-vtrack-samelevel { background: rgba(225, 245, 220, 0.2) !important; outline: 2px dashed #55B837 !important; outline-offset: -2px !important; }",e+=".sa-vtrack-inlineblock { display: inline-block; }",e+=".sa-vtrack-prop-highlight { background: rgba(250, 187, 212, 0.2) !important; outline: 1px solid #EB4688 !important; outline-offset: -1px !important;}",e+=".sa-vtrack-prop-highlight-samelevel { background: rgba(250, 187, 212, 0.2) !important; outline: 1px dashed #EB4688 !important; outline-offset: -1px !important;}",e+=".sa-vtrack-propable { outline: 1px solid #EB4688 !important;outline-offset: -1px !important;  }",e+=".sa-vtrack-propable-samelevel { outline: 1px dashed #EB4688 !important;outline-offset: -1px !important;}",e+=".sa-vtrack-selfprop { outline: 1px solid #EB4688 !important;outline-offset: -1px !important;  }",e+=".sa-vtrack-selfprop-samelevel { outline: 1px dashed #EB4688 !important;outline-offset: -1px !important;  }",e+=".sa-vtrack-prop-focus {background: rgba(250, 187, 212, 0.3) !important;outline: 2px solid #EB4688 !important;outline-offset: -2px !important; }",e+=".sa-vtrack-prop-focus-samelevel {background: rgba(250, 187, 212, 0.3) !important;outline: 2px dashed #EB4688 !important;outline-offset: -2px !important; }";var t=document.createElement("style");t.type="text/css";try{t.appendChild(document.createTextNode(e))}catch(n){t.styleSheet.cssText=e}document.getElementsByTagName("head")[0].appendChild(t)},init:function(){var e=v.isObject(h.para.heatmap)&&"default"===h.para.heatmap.clickmap;e&&(this.setCssStyle(),this.messageHandler=j(),this.listenMessage());var t="",n=null;try{t=h.para.server_url,v.isArray(t)&&t.length>0?(n=[],v.each(t,function(e){var t=v.URL(e).searchParams.get("project")||"default";n.push(t)})):n=v.URL(t).searchParams.get("project")||"default"}catch(r){n="default"}this.postMessage({source:"sa-web-sdk",type:"v-ready-state",data:{project_name:n,server_url:t,heatmapEnable:e,lib:"js",title:document.title,url_path:location.pathname,url:v.getURL(),vtrackConfigEnable:h.para.heatmap&&h.para.heatmap.get_vtrack_config}})}};N.init(),window.sensorsDataAnalytic201505VTrack=N}();