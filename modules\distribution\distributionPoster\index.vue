<template>
  <page>
    <view slot="content" class="page-content">
      <m-nav-bar title="分销海报" left-icon="left" @clickLeft="back" />

      <view class="content-main">
        <!-- 标签切换栏 -->
        <view class="tabs-container">
          <view 
            class="tab-item" 
            :class="{ active: activeTab === 'daily' }" 
            @tap="switchTab('daily')"
          >每日海报</view>
          <view 
            class="tab-item" 
            :class="{ active: activeTab === 'festival' }" 
            @tap="switchTab('festival')"
          >节日海报</view>
        </view>

        <!-- 每日海报标签内容 -->
        <view v-if="activeTab === 'daily'" class="poster-grid">
          <view 
            v-for="(poster, index) in dailyPosters" 
            :key="index"
            class="poster-item"
            @tap="selectPoster(poster, 'daily')"
          >
            <image 
              :src="poster.imageUrl" 
              mode="aspectFill" 
              class="poster-image"
              :class="{ selected: selectedPoster && selectedPoster.id === poster.id }"
            />
          </view>
          <!-- 添加空白占位元素以保持九宫格布局 -->
          <view v-for="i in getEmptyGridCount('daily')" :key="i" class="poster-item empty"></view>
        </view>

        <!-- 节日海报标签内容 -->
        <view v-if="activeTab === 'festival'" class="poster-grid">
          <view 
            v-for="(poster, index) in festivalPosters" 
            :key="index"
            class="poster-item"
            @tap="selectPoster(poster, 'festival')"
          >
          <image 
              :src="poster.imageUrl" 
            mode="aspectFill" 
              class="poster-image"
              :class="{ selected: currentFestivalPoster && currentFestivalPoster.id === poster.id }"
          />
          </view>
          <!-- 添加空白占位元素以保持九宫格布局 -->
          <view v-for="i in getEmptyGridCount('festival')" :key="i" class="poster-item empty"></view>
        </view>
      </view>
    </view>
  </page>
</template>

<script>
export default {
  data() {
    return {
      activeTab: 'daily',
      selectedPoster: null,
      currentFestivalPoster: null,
      dailyPosters: [
        {
          id: 8,
          imageUrl: this.$static_ctx + 'image/business/hulu-v2/distribution-poster-professional-care.png',
          title: '周二海报'
        },
        {
          id: 9,
          imageUrl: this.$static_ctx + 'image/business/hulu-v2/distribution-poster-sunday.png',
          title: '周日'
        },
        // {
        //   id: 1,
        //   imageUrl: this.$static_ctx + 'image/business/hulu-v2/distribution-poster-monday.png',
        //   title: '周一海报'
        // },
        // {
        //   id: 3,
        //   imageUrl: this.$static_ctx + 'image/business/hulu-v2/distribution-poster-wednesday.png',
        //   title: '周三海报'
        // },
        // {
        //   id: 4,
        //   imageUrl: this.$static_ctx + 'image/business/hulu-v2/distribution-poster-thursday.png',
        //   title: '周四海报'
        // },
        // {
        //   id: 5,
        //   imageUrl: this.$static_ctx + 'image/business/hulu-v2/distribution-poster-friday.png',
        //   title: '周五海报'
        // },
        // {
        //   id: 6,
        //   imageUrl: this.$static_ctx + 'image/business/hulu-v2/distribution-poster-saturday.png',
        //   title: '周六海报'
        // },
      ],
      festivalPosters: [
        {
          id: 101,
          imageUrl: this.$static_ctx + 'image/business/hulu-v2/english-poster.png',
          title: '节日海报1'
        }
      ]
    }
  },
  mounted() {
    // 初始化时设置默认选中的节日海报
    if (this.festivalPosters.length > 0) {
      this.currentFestivalPoster = this.festivalPosters[0];
    }
  },
  methods: {
    back() {
      uni.navigateBack();
    },
    switchTab(tab) {
      this.activeTab = tab;
    },
    // 获取需要填充的空白格子数量
    getEmptyGridCount(type) {
      const totalGrids = 9; // 九宫格总数
      const posters = type === 'daily' ? this.dailyPosters : this.festivalPosters;
      const emptyCount = Math.max(0, totalGrids - posters.length);
      return emptyCount;
    },
    selectPoster(poster, type) {
      this.selectedPoster = poster;
      if (type === 'festival') {
        this.currentFestivalPoster = poster;
      }
      
      // 将选中的海报数据存储到本地缓存
      uni.setStorageSync('selectedPosterData', poster);
      
      console.log('选择了海报:', poster.id, poster.title, '类型:', type);
      
      // 跳转到海报详情页
      this.$navto.push('DistributionPosterDetail', {
        id: poster.id,
        type: type,
        title: poster.title
      });
    },
    savePoster() {
      // 获取当前要保存的海报
      const posterToSave = this.activeTab === 'daily' 
        ? this.selectedPoster 
        : this.currentFestivalPoster;
      
      if (!posterToSave) {
        uni.showToast({
          title: '请先选择一个海报',
          icon: 'none'
        });
        return;
      }

      uni.showLoading({
        title: '保存中...'
      });

      // 下载海报图片
      uni.downloadFile({
        url: posterToSave.imageUrl,
        success: (res) => {
          if (res.statusCode === 200) {
            // 保存图片到相册
            uni.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                uni.hideLoading();
                uni.showToast({
                  title: '保存成功',
                  icon: 'success'
                });
              },
              fail: (err) => {
                uni.hideLoading();
                // 如果是用户拒绝授权导致的失败
                if (err.errMsg.indexOf('auth deny') >= 0) {
                  uni.showModal({
                    title: '提示',
                    content: '需要您授权保存图片到相册',
                    confirmText: '去设置',
                    cancelText: '取消',
                    success: (res) => {
                      if (res.confirm) {
                        uni.openSetting();
                      }
                    }
                  });
                } else {
                  uni.showToast({
                    title: '保存失败',
                    icon: 'none'
                  });
                }
              }
            });
          } else {
            uni.hideLoading();
            uni.showToast({
              title: '下载海报失败',
              icon: 'none'
            });
          }
        },
        fail: () => {
          uni.hideLoading();
          uni.showToast({
            title: '下载海报失败',
            icon: 'none'
          });
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.page-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.content-main {
  position: relative;
  flex: 1;
  overflow: auto;
  display: flex;
  flex-direction: column;
}

/* 标签切换栏 */
.tabs-container {
  display: flex;
  background-color: #fff;
  padding: 0 30rpx;
}

.tab-item {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #333;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #2196f3;
}

/* 每日海报网格 */
.poster-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
  flex: 1;
}

.poster-item {
  width: calc(33.33% - 20rpx);
  aspect-ratio: 3/4;
  margin: 10rpx;
  background-color: #fff;
  border-radius: 8rpx;
  overflow: hidden;
}

.poster-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.poster-image.selected {
  border: 4rpx solid #2196f3;
}

/* 空白占位元素 */
.poster-item.empty {
  background-color: transparent;
  box-shadow: none;
  pointer-events: none;
}

/* 底部保存按钮 */
.save-button {
  height: 88rpx;
  line-height: 88rpx;
  background-color: #2196f3;
  color: #fff;
  text-align: center;
  font-size: 30rpx;
  margin: 20rpx 40rpx 40rpx;
  border-radius: 44rpx;
}
</style>
