/**
 * 这里是uni-app内置的常用样式变量
 */


/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */


/* 颜色变量 */

// /* 行为相关颜色 */
// $uni-color-primary: #007aff;
// $uni-color-success: #4cd964;
// $uni-color-warning: #f0ad4e;
// $uni-color-error: #dd524d;
//
// /* 文字基本颜色 */
// $uni-text-color: #333; //基本色
// $uni-text-color-inverse: #fff; //反色
// $uni-text-color-grey: #999; //辅助灰色，如加载更多的提示信息
// $uni-text-color-placeholder: #808080;
// $uni-text-color-disable: #c0c0c0;
//
// /* 背景颜色 */
// $uni-bg-color: #ffffff;
// $uni-bg-color-grey: #f8f8f8;
// $uni-bg-color-hover: #f1f1f1; //点击状态颜色
// $uni-bg-color-mask: rgba(0, 0, 0, 0.4); //遮罩颜色
//
// /* 边框颜色 */
// $uni-border-color: #c8c7cc;
// 主题色
$topicC: #00B484;
$contentDdt:#E5E5E5;
$pageBg:#f7f7f7;
$contentBg:#ffffff;
$borderColor:#f5f5f5;
$static_ctx: 'http://file.greenboniot.cn/static/'; // 静态资源库
//$imgUrl: '~@/static/image';
$imgUrl: $static_ctx+'image';
$bgColor: #F7F7F7;
$warningColor: #ee9e2f;
$calcelColor: #ddd;
$dangerColor: #EE5F5D;
$headerH: 0;
// 初始化样式
view {
    font-size: 28upx;
    line-height: 1.5;
}

progress,
checkbox-group {
    width: 100%;
}

form {
    width: 100%;
}

.bg-warning {
    background-color: $warningColor !important;
}

// 一级主题色
.topicC {
    color: $topicC!important;
}

// 清除浮动
.clear-float {
    zoom: 1;
}

.clear-float:before,
.clear-float:after {
    display: table;
    content: " ";
}

.clear-float:after {
    clear: both;
}

.f-l {
    float: left;
}

.f-r {
    float: right;
}

.add-btn {
    position: fixed;
    bottom: 50upx;
    right: 30upx;
    width: 130upx;
    height: 130upx;
    z-index: 100;
}

//@include ellipsis(); // 这里引用上面的 mixin，并设置值 10px
//溢出省略（$v:int）
@mixin ellipsis($v:1) {
    word-break: break-all;
    display: -webkit-inline-box;
    -webkit-line-clamp: $v;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

//@include rounded(10px); // 这里引用上面的 mixin，并设置值 10px
//圆角（$v:int）
@mixin rounded($conor: 10upx) {
    // 定义 mixin 并设置默认值 5px
    -webkit-border-radius: $conor;
    -moz-border-radius: $conor;
    border-radius: $conor;
    overflow: hidden;
}

//向下控制圆角阴影（$v:int）
@mixin downRadiusBoxShadow($v1:0upx, $v2:2upx, $v3:6upx, $v4:10upx) {
    -webkit-box-shadow: $v1 $v2 $v3 rgba(0, 0, 0, 0.1);
    box-shadow: $v1 $v2 $v3 rgba(0, 0, 0, 0.1);
    @include rounded($v4);
}

//向下控制阴影（$v:int）
@mixin downBoxShadow($v1:0upx, $v2:2upx, $v3:6upx, $v4:0.2, $v5:0, $v6:0, $v7:0) {
    -webkit-box-shadow: $v1 $v2 $v3 rgba($v5, $v6, $v7, $v4);
    box-shadow: $v1 $v2 $v3 rgba($v5, $v6, $v7, $v4);
}

//big-button
@mixin btnBg() {
    width: 690upx;
    height: 88upx;
    line-height: 88upx;
    //background: linear-gradient(90deg, rgba(255, 185, 36, 1), rgba(254, 219, 55, 1));
    background: #22daad;
    @include rounded(44upx);
    font-size: 36upx;
    text-align: center;
    color: #ffffff;
    display: block;
    margin: 0 auto;
}

// 透明度
@mixin opacity($v:1) {
    opacity: $v;
}

// 伸缩盒子模型兼容
@mixin displayFlex() {
    display: -webkit-box;
    /* 老版本语法: Safari, iOS, Android browser, older WebKit browsers. */
    display: -moz-box;
    /* 老版本语法: Firefox (buggy) */
    display: -ms-flexbox;
    /* 混合版本语法: IE 10 */
    display: -webkit-flex;
    /* 新版本语法: Chrome 21+ */
    display: flex;
    /* 新版本语法: Opera 12.1, Firefox 22+ */
    -webkit-flex-wrap: nowrap;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
}

// 主轴左右留白
@mixin spaceAround() {
    -moz-justify-content: space-around;
    -webkit-justify-content: space-around;
    justify-content: space-around;
}

@mixin btnBgDisable() {
    width: 690upx;
    height: 88upx;
    line-height: 88upx;
    background: linear-gradient(90deg, rgba(237, 109, 70, 1), rgba(248, 94, 76, 1));
    opacity: 0.5;
    @include rounded(44upx);
    font-size: 36upx;
    text-align: center;
    color: #ffffff;
}

@mixin btnBgWarning() {
    width: 690upx;
    height: 88upx;
    line-height: 88upx;
    background: rgba(244, 53, 48, 1);
    @include rounded(44upx);
    font-size: 36upx;
    text-align: center;
    color: #ffffff;
    margin: 0 auto;
}

//@include iconImg(34,34,'/business/icon-gengduo.png');
@mixin iconImg($w:44, $h:44, $url:'') {
    width: $w + upx;
    height: $h + upx;
    background-image: url($imgUrl + $url);
    background-size: 100%;
    background-repeat: no-repeat;
    display: inline-block;
}
@mixin iconImg($w:44, $h:44, $url:'') {
    width: $w + upx;
    height: $h + upx;
    background-image: url($imgUrl + $url);
    background-size: 100%;
    background-repeat: no-repeat;
    display: inline-block;
}

@mixin boxBgBYOUnit($w:44, $h:44, $url:'') {
    background-image: url($imgUrl + $url);
    background-size:$w, $h;
}
//字体层级
@mixin font($type:1) {
    //主文（title/nav）
    @if $type==1 {
        color: #333333;
        font-size: 30upx;
        //次级主文
    }
    @else if $type==2 {
        color: #666666;
        font-size: 26upx;
        //次级辅文
    }
    @else if $type==3 {
        color: #999999;
        font-size: 24upx;
    }
}


/* 旋转角度 */

@mixin rotate($val:40deg) {
    transform: rotate($val);
    -ms-transform: rotate($val);
    /* Internet Explorer */
    -moz-transform: rotate($val);
    /* Firefox */
    -webkit-transform: rotate($val);
    /* Safari 和 Chrome */
    -o-transform: rotate($val);
    /* Opera */
}

.topic-bg {
    background-color: $topicC;
}

.topic-c {
    color: $topicC;
}

.topic-border {
    border: 2upx solid $topicC;
}

.content-bdt {
    border-top: 2upx solid $contentDdt;
}

.content-bdb {
    border-bottom: 2upx solid $contentDdt;
}

.btn-bg {
    @include btnBg();
}


.bd {
    border: 1px solid $borderColor;
}

.bdt {
    border-top: 2upx solid $borderColor;
}

.bdb {
    border-bottom: 1px solid $borderColor;
}
// 输入框提示语默认样式
.placeholderclass{
  font-size: 24rpx;
  color: #9a9a9a;
}

.no-border {
    border: none!important;
}

main {
    background-color: $pageBg;
}

// 添加圆圈按钮默认样式
.add-btn {
    position: fixed;
    bottom: 50upx;
    right: 30upx;
    width: 130upx;
    height: 130upx;
    z-index: 100;
}


uni-page-body {
    background-color: $pageBg;
    height: 100%;
}

.uni-input-placeholder {
    color: #CCCCCC;
}

em {
    font-style: normal;
}

.display-none {
    display: none !important;
}

.display-block {
    display: block !important;
}

.color-topicC {
    color: $topicC !important;
}

.color-red {
    color: #FF4A4A !important;
}

.color-green {
    color: #03bf16 !important;
}

.color-blue {
    color: #187FFF !important;
}

.color-333 {
    color: #333 !important;
}

.color-666 {
    color: #666 !important;
}

.color-999 {
    color: #999 !important;
}

.color-4a4a4a {
    color: #4a4a4a !important;
}

.bg-f7f7f7 {
    background: #f7f7f7 !important;
}

.bg-fff {
    background: #FFFFFF !important;
}

.bg-topicC {
    background: $topicC !important;
}

.bg-red {
    background: #FF4A4A !important;
}

.bg-green {
    background: #03bf16 !important;
}

.bg-blue {
    background: #187FFF !important;
}

.bg-666 {
    background: #666 !important;
}

.bg-999 {
    background: #999 !important;
}

.f-s-24 {
    font-size: 24upx !important;
}

.m-tb-20 {
    margin-top: 20upx !important;
    margin-bottom: 20upx !important;
}

.m-tb-30 {
    margin-top: 30upx !important;
    margin-bottom: 30upx !important;
}

.m-tb-30-auto {
    margin: 30upx auto !important;
}

.m-b-20 {
    margin-bottom: 20upx !important;
}

.m-lr-8 {
    margin: 0 8upx !important;
}

.m-t-0 {
    margin-top: 0 !important;
}

.m-t-8 {
    margin-top: 8upx !important;
}

.m-t-10 {
    margin-top: 10upx !important;
}

.m-t-60 {
    margin-top: 60upx !important;
}

.m-t-40 {
    margin-top: 40upx !important;
}

.m-t-20 {
    margin-top: 20upx !important;
}

.m-b-16 {
    margin-bottom: 16upx !important;
}

.m-r-20 {
    margin-right: 20upx !important;
}

.overflow-hidden {
    overflow: hidden !important;
}

.p-t-2 {
    padding-top: 2upx !important;
}

.p-30 {
    padding: 30upx !important;
}

.p-30-top-0 {
    padding: 0 30upx 30upx !important;
}

.p-30-bottom-0 {
    padding: 30upx 30upx 0 !important;
}

.p-t-30 {
    padding-top: 30upx !important;
}

.p-b-30 {
    padding-bottom: 30upx !important;
}

.p-b-16 {
    padding-bottom: 16upx !important;
}

.p-b-40 {
    padding-bottom: 40upx !important;
}

.f-w-medium {
    color: #bfbfbf;
    font-weight: Medium;
    font-size: 30upx;
}

/* 添加icon，需要加两个地方
 *添加上面集合和下面单独位置
 */


/* 背景图片 */

@mixin iconCoomonBgAttribute() {
    background-size: 100%;
    background-repeat: no-repeat;
    display: inline-block;
}


/* system start */

.icon-nav-close {
    background-image: url($imgUrl + '/system/icon-nav-close.png');
    @include iconCoomonBgAttribute();
}

.icon-yijianfankui-d {
    background-image: url($imgUrl + '/system/icon-yijianfankui-d.png');
    @include iconCoomonBgAttribute();
}

.icon-yijianfankui-d-ok {
    background-image: url($imgUrl + '/system/icon-yijianfankui-d-ok.png');
    @include iconCoomonBgAttribute();
}

.icon-image {
    background-image: url($imgUrl + '/system/icon-image.png');
    @include iconCoomonBgAttribute();
}


/* system end */


/* business start */

// 最新消息
.icon-boy {
    background-image: url($imgUrl + '/business/icon-boy.png');
    @include iconCoomonBgAttribute();
}

.icon-girl {
    background-image: url($imgUrl + '/business/icon-girl.png');
    @include iconCoomonBgAttribute();
}




.icon-gengduo {
    background-image: url($imgUrl + '/business/icon-gengduo.png');
    @include iconCoomonBgAttribute();
}

.icon-dagou {
    background-image: url($imgUrl + '/business/icon-dagou.png');
    @include iconCoomonBgAttribute();
}

.icon-edit {
    background-image: url($imgUrl + '/business/icon-edit.png');
    @include iconCoomonBgAttribute();
}

.icon-search {
    background-image: url($imgUrl + '/business/teaching-plan/icon-search.png');
    @include iconCoomonBgAttribute();
}

.icon-add {
    background-image: url($imgUrl + '/business/teaching-plan/icon-add.png');
    @include iconCoomonBgAttribute();
}


.icon-xuanze {
    background-image: url($imgUrl + '/business/teaching-plan/icon-xuanze.png');
    @include iconCoomonBgAttribute();
}


.icon-back {
    background-image: url($imgUrl + '/business/icon-back.png');
    @include iconCoomonBgAttribute();
}


/*提醒数字(圆圈加数字)*/

//全局配置CSS函数属性
//@import './static/css/init/img-url.scss';
// @import './static/css/init/common.scss';
// 快速导航
.saHoverMenuRootClass {
  top: calc(100vh - 636upx) !important;
}
// 免责声明
.disclaimersDialogRootClass {
  border-radius: 10upx!important;
  left:35upx!important;
  width: calc(100vw - 70upx)!important;
  height: 750upx!important;
  top:calc(50vh - 325upx)!important;
}
