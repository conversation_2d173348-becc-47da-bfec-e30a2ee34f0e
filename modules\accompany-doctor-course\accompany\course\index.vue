<template>
  <view class='course'>
    <view class="my-data">
      <view :style="'height:' + statusBarHeight + 'px;'"></view>
      <view class="top-nav">
        <view class="top-nav-l">
          <view class="back-btn" @click.stop="handleBack">
            <image mode="aspectFit" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-left-arrows.png'" class="header-search-img"/>
          </view>
          <view class="title">课程</view>
        </view>
        <view class="top-nav-r">
          <view class="input-view">
            <i class="icon-positioning-search"></i>
            <input confirm-type="search" placeholder="搜索" placeholder-style="color: #A5AAB8" class="input" type="text" :value="search" @input="searchInputFn" @confirm="searchFn" @tap="searcFn()">
          </view>
        </view>
      </view>
    </view>
    <view class="course-content">
      <scroll-view class="course-l" :scroll-y="true">
        <view>
          <view 
            :class="courseLeftActive == index ? 'course-l-item active' : 'course-l-item'" 
            v-for="(item,index) in courseclassifyList" 
            :key="index" 
            @click="handleCurrentActive(index)"
          >
            <view v-if="courseLeftActive == index" class="course-l-item-border"></view>
            <view v-else class="course-l-item-default"></view>
            <view class="text">{{ item.classifyName }}</view>
          </view>
        </view>
      </scroll-view>
      <scroll-view class="course-r" :scroll-y="true" :scroll-top="rightScrollTop" :scroll-with-animation="true">
        <view>
          <view class="course-r-item">
            <view class="title">{{ courseclassifyList[courseLeftActive].classifyName }}</view>
            <view class="item-classify-content">
              <view class="item-classify" v-for="itemChildren in courseclassifyList[courseLeftActive].subClassifyList" :key="itemChildren.id" @click="handleClickJump(courseclassifyList[courseLeftActive])">
                <view class="img"><image :src="file_ctx + itemChildren.classifyImg"></image></view>
                <view class="text">{{ itemChildren.classifyName }}</view>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
  export default {
    data(){
      return{
        file_ctx:this.file_ctx,
        statusBarHeight: 0,
        search:'',
        courseListLeft:['特别推荐','考研·升学','职业·考证','职业·考证','职业·考证','职业·考证','职业·考证'],
        courseList:[],
        courseLeftActive:0,
        rightScrollTop:0,
        rightDomsTop:[],
        courseclassifyList:[],
      }
    },
    onLoad(){
    },
    mounted(){
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
      this.accompanycourseclassifyQueryPage()
    },
    methods:{
      handleBack(){
        this.$navto.back(1)
      },
      searcFn(){
        this.$navto.push('CourseSearch')
      },
      handleClickJump(item){
        this.$navto.push('SecondClassify', {id:item.id,name:item.classifyName})
      },
      accompanycourseclassifyQueryPage(){
        this.$api.accompanyDoctor.accompanycourseclassifyQueryPage({current:1,size:30,condition:{state:1,layer:1}}).then(res=>{
          this.courseclassifyList = res.data.records.map(item=>{
            return {
              ...item,
              subClassifyList:item.subClassifyList && JSON.parse(item.subClassifyList) || [],
            }
          })
        })
      },
      handleCurrentActive(index){
        this.courseLeftActive = index
      },
      searchInputFn(){

      },
      searchFn(){

      },
    },
 }
</script>

<style lang='scss' scoped>
.course{
  .my-data{
    position: fixed;
    top: 0;
    width: 100%;
    .top-nav{
      display: flex;
      align-items: center;
      padding-top: 6rpx;
      .top-nav-l{
        display: flex;
        align-items: center;
        margin:0 32rpx;
        .back-btn{
          margin-right: 20rpx;
          .header-search-img{
            width: 32rpx;
            height: 32rpx;
          }
        }
        .title{
          font-size: 32rpx;
          color: #1D2029;
        }
      }
      .top-nav-r{
        .input-view {
          display: flex;
          align-items: center;
          vertical-align: middle;
          // width: calc(100% - 220upx);
          width: 400rpx;
          // @include rounded(38upx);
          line-height: 64rpx;
          height: 64rpx;
          padding: 0 20upx;
          background: #FFFFFF;
          border-radius: 36rpx;
          border: 2rpx solid #D9DBE0;
          box-sizing: border-box;
          .icon-positioning-search{
            display: inline-block;
            vertical-align: middle;
            margin-right: 6upx;
            @include iconImg(32, 32, '/system/icon-positioning-search.png');
          }
          .input {
            width: calc(100% - 78upx);
            display: inline-block;
            vertical-align: middle;
            font-size: 28upx;
            line-height: 42upx;
            color: #333;
          }
        }
      }
    }
  }
  .course-content{
    display: flex;
    height: calc(100vh - 99px);
    margin-top: 99px;
    .course-l{
      width: 180rpx;
      height: 100%;
      background: #F4F6FA;
      .course-l-item{
        display: flex;
        align-items: center;
        // justify-content: center;
        padding:16rpx 0;
        background: #F4F6FA;
        .course-l-item-border{
          width: 6rpx;
          height: 56rpx;
          background: #00B484;
          border-radius: 4rpx;
        }
        .course-l-item-default{
          width: 6rpx;
          height: 56rpx;
        }
        .text{
          font-size: 24rpx;
          color: #000000;
          padding: 16rpx 0;
          margin-left: 18rpx;
        }
      }
      .active{
        color: #000000;
        background-color: #fff;
      }
    }
    .course-r{
      display: flex;
      flex: 1;
      .course-r-item{
        padding:0 38rpx;
        .title{
          font-size: 28rpx;
          color: #000000;
          margin-bottom: 32rpx;
        }
        .item-classify-content{
          display: flex;
          flex-wrap: wrap;
          // justify-content: space-between;
          border-bottom: 1rpx solid #EAEBF0;
          .item-classify{
            display: flex;
            flex-direction: column;
            align-items: center;
            // margin-bottom: 40rpx;
            margin: 0 46rpx 40rpx 0;
            .img{
              width: 112rpx;
              height: 112rpx;
              border-radius: 16rpx;
              overflow: hidden;
              image{
                width: 100%;
                height: 100%;
              }
            }
            .text{
              margin-top: 24rpx;
              font-size: 24rpx;
              color: #1D2029;
            }
            :nth-child(3n){
              margin-right: 0;
            }
          }
        }
      }
    }
  }
}
</style>