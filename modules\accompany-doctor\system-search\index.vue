<template>
  <view class="main">
    <view class="top-nav">
      <view class="top-nav-body">
        <!-- <em class="icon-back-left" @tap="$navto.back(1)"></em> -->
        <view class="input-view">
          <i class="icon-positioning-search"></i>
          <input confirm-type="search" placeholder="搜索你想要的内容" placeholder-style="color: #BFBFBF" class="input" type="text" :value="search" @input="searchInputFn" @confirm="searchFn">
        </view>
        <view class="click" @tap="searchFn">
          搜索
        </view>
      </view>
    </view>
    <view class="title">
      历史搜索
    </view>
    <view class="tag" v-if="pdList.length">
      <view class="tag-list" v-for="(item, index) in pdList.slice(0,6)" :key="index" @tap="clickFn(item)">
        {{item}}
      </view>
    </view>
  </view>
</template>

<script>


export default {
  data() {
    return {
      search: '',
      pdList: [],
      hotList: [],
      query: {}
    }
  },
  
  onLoad(paramsObj) {
    const query = this.$Route.query
    if (!this.$validate.isNull(query)) {
      this.query = query
    }
  },
  onShow() {
    this.init()
  },
  methods: {
    init() {
      this.$nextTick(() => {
        this.$refs.hotListRef.init()
        this.search = ''
        let pdList = this.$common.getKeyVal('system', 'searchHistoryArrStorage', true)
        if (typeof pdList === 'string' && pdList === '') {
          pdList = []
        } else if (typeof pdList === 'string' && pdList !== '') {
          pdList = JSON.parse(pdList)
        }
        if (pdList) {
          if (typeof pdList === 'object' && pdList.length > 0) {
            this.pdList = pdList
          } else {
            this.pdList = []
          }
        } else {
          this.pdList = []
        }
      })
    },
    clickFn(item) {
      let arr = this.$common.getKeyVal('system', 'searchHistoryArrStorage', true)
      if (typeof arr === 'string' && arr === '') {
        arr = []
      } else if (typeof arr === 'string' && arr !== '') {
        arr = JSON.parse(arr)
      }
      if (arr) {
        if (typeof arr === 'object' && arr.length > 0) {
          let state = false
          let index = ''
          for (let i = 0; i < arr.length; i++) {
            if (item === arr[i]) {
              state = true
              index = i
              break
            }
          }
          if (state) {
            if (typeof index === 'number') {
              arr.splice(index, 1)
            }
          }
        }
        arr.unshift(item)
        this.$common.setKeyVal('system', 'searchHistoryArrStorage', arr, true)
      } else {
        this.$common.setKeyVal('system', 'searchHistoryArrStorage', [], true)
      }
      this.$navto.push('accompanyDoctorSystemSearchSystem', { search: item, query: JSON.stringify(this.query) })
    },
    searchInputFn(e) {
      this.search = e.target.value
    },
    searchFn() {
      if (this.search) {
        this.clickFn(this.search)
      } else {
        this.$navto.push('accompanyDoctorSystemSearchSystem', { search: '', query: JSON.stringify(this.query) })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .m-t-40{
    margin-top:40upx;
  }
  .m-b-20{
    margin-bottom: 20upx;
  }
  .main{
    height: 100vh;
    background-color: #fff;
    .top-nav{
      padding: 10upx 30upx 10upx 30upx;
      background-color: #fff;
      .top-nav-body{
        display: flex;
        align-items: center;
        .icon-back-left{
          display: inline-block;
          vertical-align: middle;
          @include iconImg(64, 64, '/system/icon-back-left.png');
          margin-right: 16upx;
        }
        .input-view {
          flex: 1;
          display: inline-block;
          vertical-align: middle;
          width: calc(100% - 220upx);
          @include rounded(38upx);
          line-height: 64upx;
          height: 64upx;
          padding: 0 20upx;
          background: #F7F7F7;
          .icon-positioning-search{
            display: inline-block;
            vertical-align: middle;
            margin-right: 6upx;
            @include iconImg(32, 32, '/system/icon-positioning-search.png');
          }
          .input {
            width: calc(100% - 78upx);
            display: inline-block;
            vertical-align: middle;
            font-size: 28upx;
            line-height: 42upx;
            color: #333;
          }
        }
        .click{
          display: inline-block;
          vertical-align: middle;
          text-align: right;
          width: 100upx;
          line-height: 64upx;
          height: 64upx;
        }
      }
    }
    .title{
      padding: 30upx 30upx 10upx 30upx;
      color: #333;
      font-size: 32upx;
      line-height: 48upx;
    }
    .tag{
      padding: 10upx 30upx 10upx 30upx;
      .tag-list{
        display: inline-block;
        vertical-align: middle;
        margin: 0upx 30upx 30upx 0;
        background-color: #F5F5F5;
        @include rounded(6upx);
        color: #333;
        padding: 0 20upx;
        height: 48upx;
        line-height: 48upx;
      }
    }
  }
</style>
