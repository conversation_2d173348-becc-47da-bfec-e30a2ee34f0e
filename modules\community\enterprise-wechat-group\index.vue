<template>
  <page>
    <view slot="content" class="body-main">
      <scroll-refresh style="height: 100%;" :isShowEmptySwitch="true" :fixed="false" :isAbsolute="false" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
        <view class="content">
            <view class="main-search">
                <search placeholder="搜索你想进的群" :fixed="false" v-model="regForm.name" @changeSearch="changeSearch"></search>
            </view>
            <view class="group">
                <view class="group-item" v-for="item in indexList" :key="item.id">
                    <view class="group-item-left">
                        <image class="group-logo" :src="item.logoPath ? file_ctx + item.logoPath : ''" />
                        <text class="group-name">{{ item.name }}</text>
                    </view>
                    <button type="default" class="group-inbtn" @tap="inGroup(item)">立即进群</button>
                </view>
            </view>
        </view>
      </scroll-refresh>
      <view class="fixed-bottom">
        <input class="input" placeholder="未找到群，请输入希望开通的群" v-model="form.name" />
        <button type="default" class="submit-btn" @click="submit">提交登记</button>
      </view>
    </view>
  </page>
</template>

<script>
import search from '@/components/basics/form/search'
import { mapState } from 'vuex'
import env from '@/config/env'
export default {
  components: {
    search
  },
  data() {
    return {
      file_ctx: this.file_ctx,
      mescroll: null, // mescroll实例对象
      downOption: {
          auto: false // 不自动加载
      },
      upOption: {
          auto: false, // 不自动加载
          empty: {
            top: 0,
            zIndex: 999,
          }
      },
      regForm: {
        name: ''
      },
      form: {
        name: ''
      },
      isInit: false, // 列表是否已经初始化
      indexList: [],
      pageStartDate: null
    }
  },
  computed: {
    ...mapState('user', {
      codeUserInfo: state => state.codeUserInfo, // 当前登录用户信息
      curSelectUserInfo: state => state.curSelectUserInfo, // 当前选中的用户信息
      curSelectUserInfoId: state => state.curSelectUserInfoId, // 当前选中的档案的ID
      recordUserInfo: state => state.recordUserInfo,
      accountId: state => state.accountId
    })
  },
  onLoad(paramsObj) {
    const that = this
    const query = that.$Route.query
    this.pageStartDate = new Date().getTime()
    if (!that.$validate.isNull(query)) {

    }
    this.$nextTick(() => {
      that.init()
    })
  },
  onShow() {

  },
  methods: {
    inGroup (data) {
      this.recordsClickInGroup(data)
      // #ifndef H5
      const tenantId = this.$common.getKeyVal('user', 'curSelectStoreId',true) || env.tenantId
      const url = `${env.domain_ctx}#/modules/community/enterprise-wechat-group/check/index?id=${data.id}&accountId=${this.accountId}&tenantId=${tenantId}`
      this.$navto.push('WebHtmlView', { src: url, title: data.name })
      // #endif

      // #ifdef H5
      this.navtoGo('EnterpriseWechatGroupCheck', {id: data.id})
      // #endif
    },
    recordsClickInGroup (data) {
        const param = {
            accountId: this.accountId,
            businessType: 3, // 业务类型:1-横幅，2-外部服务应用，3-企业微信群，4-圈子，5-帖子，6-帖子评论
            businessId: data.id,
            source: 1, // 来源：1-真实用户，2-马甲
            type: 2 // 类型：1-访问，2-点击，3-长按，4-关注，5-取关，6-点赞，7-收藏，8-删除评论，9-取消点赞，10-取消收藏，11-阅读
        }
        this.$api.community.applicationoperatelogInsert(param)
    },
    submit () {
      const param = {
          businessType: 3, // 业务类型:1-横幅，2-外部服务应用，3-企业微信群，4-圈子，5-帖子，6-帖子评论
          name: this.form.name,
          accountId: this.accountId,
          desc: this.form.name
      }
      this.$api.community.matterregisterInsert(param).then(res => {
          this.$uniPlugin.toast(res.msg)
          this.form.name = ''
      })
    },
    init() {
      this.$nextTick(() => {
        this.mescroll.triggerDownScroll()
      })
    },
    scrollInit(scroll) {
        scroll.optUp.page.num = 1
        scroll.optUp.page.size = 10
        this.mescroll = scroll
    },
    returnFn(obj) {
        const that = this
        setTimeout(function() {
            let params = {
              current: obj.pageNum,
              size: obj.pageSize,
              condition: {
                ...that.regForm
              }
            }
            that.$api.community.enterprisewechatgroupQueryPage(params).then(res => {
              let data = res.data.records || []
              data = data.map(item => {
                return {
                  ...item
                }
              })
              if (obj.pageNum === 1) {
                that.indexList = []
              }
              that.indexList = [...that.indexList, ...data]
              obj.successCallback && obj.successCallback(data || [])
            })
        }, that.$constant.noun.scrollRefreshTime)

    },
    changeSearch(obj) {
      this.mescroll.optUp.page.num = 1
      this.mescroll.optUp.page.size = 10
      this.regForm.name = obj.name
      this.mescroll.triggerDownScroll()
    },
    navtoGo(url = '', obj = {}) {
      this.$navto.push(url, obj)
    },
  }
}
</script>

<style lang="scss" scoped>
  .body-main {
    height: 100%;
    overflow-y: auto;
    background: #f6f6f6;
    padding-bottom: 0rpx;
    padding-bottom: calc(constant(safe-area-inset-bottom) + 130upx);
    padding-bottom: calc(env(safe-area-inset-bottom) + 130upx);
    box-sizing: border-box;
    .fixed-bottom {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 129upx;
        background: #FFFFFF;
        display: flex;
        align-items: center;
        padding: 20upx 20upx 34upx;
        bottom: 0;
        bottom: constant(safe-area-inset-bottom);
        bottom: env(safe-area-inset-bottom);
        box-sizing: border-box;
        .input {
            flex: 1;
            height: 70upx;
            background: #EEEEEE;
            @include rounded(35upx);
            font-size: 30upx;
            line-height: 42upx;
            padding-left: 20upx;
            box-sizing: border-box;
        }
        .submit-btn {
            height: 70upx;
            box-sizing: border-box;
            background: linear-gradient(-63deg, #00D29D, #16E4B0);
            @include rounded(35upx);
            padding: 0 32upx;
            font-size: 33upx;
            font-weight: 500;
            color: #FFFFFF;
            line-height: 70upx;
            margin-left: 12upx;
        }
    }
  }
  .group {
    width: 710upx;
    margin: 0 auto;
    background-color: #fff;
    @include rounded(15upx);
    padding: 0 19upx;
    box-sizing: border-box;
  }
  .group-item {
    width: 100%;
    display: flex;
    align-items: center;
    flex-direction: row;
    justify-content: space-between;
    padding: 46upx 14upx 46upx 24upx;
    box-sizing: border-box;
    &+.group-item {
        border-top: 2upx solid #E5E5E5;
    }
    &-left {
        display: flex;
        flex-direction: row;
        align-items: center;
    }
    .group-logo {
        width: 42upx;
        height: 42upx;
    }
    .group-name {
        font-size: 31upx;
        font-weight: 500;
        color: #2D2D2D;
        line-height: 47upx;
        margin-left: 12upx;
    }
    .group-inbtn {
        width: 170upx;
        height: 47upx;
        background: #00D29D;
        @include rounded(24upx);
        color: #fff;
        font-size: 31upx;
        font-weight: 500;
        line-height: 47upx;
        padding: 0;
        margin: 0;
        border: 0;
    }
  }
</style>
