<template>
  <view class="" :class="{backgroundColor:'moduleBox1-' + skinColor}">
    <!-- 服务项目部分 -->
    <view class="service-project" v-if="serviceList.length">
      <view class="service-project-head">
        <view class="service-project-head-l">服务项目</view>
        <view class="service-project-head-r" @tap="handleServiceMore" v-if="serviceList.length > 1">更多<image class="head-r-img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-right-arrows.png'"></image></view>
      </view>
      <view class="service-project-item" v-for="item in serviceList" :key="item.id" @tap="handleJumpService(item.id)">
        <view class="project-item-l"><image class="img" :src="file_ctx + item.listImg"></image></view>
        <view class="project-item-r">
          <view class="project-item-r-title">{{ item.comboName }}</view>
          <view class="project-item-r-info">{{ item.comboDesc }}</view>
          <view class="project-item-r-box" v-if="item.tag && item.tag.split(',').length > 1">
            <view class="project-item-r-tag" v-for="(item2,index) in item.tag.split(',')" :key="index">{{ item2 }}</view>
          </view>
          <view class="project-item-r-tag" v-else-if="item.tag && item.tag.split(',').length == 1">{{ item.tag }}</view>
          <view class="project-item-r-money">
            <span v-if="item.cityPrice">¥{{ item.cityPrice }}</span>
            <span v-else>¥{{ item.price / 100 }}</span>
          </view>
        </view>
      </view>
    </view>
    <!-- 社区交流部分 -->
    <view class="understand-accompany">
      <view class="service-project-head">
        <view class="service-project-head-l">社区交流</view>
        <view class="service-project-head-r" v-if="postList.length" @tap="$navto.push('accompanyTeacher')">更多<image class="head-r-img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-right-arrows.png'"></image></view>
      </view>
      <template v-if="postList.length">
        <view class="service-project-item" v-for="item in postList" :key="item.id" @tap="$navto.push('PostsDetail', {id: item.id,isShowBtn:false})">
          <view class="accompany-item-l">
            <view class="title">{{ item.title }}</view>
            <view class="user-box">
              <view class="user-profile"><image class="img" :src="file_ctx + item.headPath"></image></view>
              <view class="user-name">{{ item.nickName }}</view>
            </view>
          </view>
          <view class="accompany-item-r" v-if="item.imagesPath"><image class="img" :src="item.imagesPath.includes(file_ctx) ? (item.imagesPath.split(',')[0]) : file_ctx + (item.imagesPath.split(',')[0])"></image></view>
        </view>
      </template>
      <view class="empty" v-else>
        <view class="empty-img"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-empty.png'"></image></view>
        暂无数据~
      </view>
    </view>

    <!-- 本地名医部分 -->
     <view class="understand-accompany">
           <view class="service-project-head">
             <view class="service-project-head-l">本地名医</view>
             <view class="service-project-head-r" v-if="indexlist.length" @tap="$navto.push('doctorList',{cityName})">更多<image class="head-r-img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-right-arrows.png'"></image></view>
           </view>
           <template v-if="indexlist.length">
             <view class="depa-doctor-item" v-for="item in indexlist" :key="item.id" @click="handleJumpDoctor(item.id)">
               <view class="doctor-item-box">
                 <view class="doctor-item-l">
                   <image mode="aspectFit" :src="item.expertPic"></image>
                 </view>
                 <view class="doctor-item-r">
                   <view class="item-r-head">
                     <view class="name">{{ item.name }}<span>{{ item.post }}</span></view>
                   </view>
                   <view class="item-r-bott">
                     擅长领域：{{ item.introduction }}
                   </view>
                 </view>
               </view>
               <view class="line"></view>
             </view>
           </template>
           <view class="empty" v-else>
             <view class="empty-img"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-empty.png'"></image></view>
             暂无数据~
           </view>
         </view>

         <!-- 本地陪诊师 -->
        <view class="service-project accompany-box" style="margin:20rpx 0rpx 20rpx;">
           <view class="service-project-head">
             <view class="service-project-head-l" style="font-weight:600">
               本地陪诊师
               <view class="pzsTitleIcon">
                 <image class="pzsTitleLogo" :src="pzsTitleLogo" mode=""></image>
                 持证率100%
               </view>
               </view>
             <view class="service-project-head-r" v-if="accompanylist.length > 1" @tap="$navto.push('accompanyList',{isShow:true,cityName:cityName})">更多<image class="head-r-img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-right-arrows.png'"></image></view>
           </view>
           <view class="accompany-List" v-if="accompanylist.length">
             <view class="accompany-box" v-for="item in accompanylist" :key="item.id" @click="handleJump(item.id)">
                 <image class="accompany-Icon" mode="aspectFit" :src="item.avatar"></image>
                 <!--<image v-if="item.sex == 0" class="accompany-Icon" mode="aspectFit" :src="file_ctx + 'static/image/business/hulu-v2/icon-list-avatar-default-grild.png'"></image>
                <image v-if="item.sex == 1" class="accompany-Icon" mode="aspectFit" :src="file_ctx + 'static/image/business/hulu-v2/icon-list-avatar-default-man.png'"></image>-->
                <view class="accompany-username">
                  {{item.username}}
                  <image class="pzsNameLogo" :src="pzsNameLogo"></image>
                </view>
                <!-- <view class="accompany-province">{{item.province + item.city}}</view> -->
                <view class="languageMap" v-if="item.language && item.language.filter(l => l).length > 0">
                  <view class="languageItem" :key="index" v-for="(languageItem,index) in item.language.slice(0, 2).filter(l => l)">{{languageItem}}</view>
                </view>
                <view class="languageMap" v-if="!(item.language && item.language.filter(l => l).length > 0)">
                  <view class="languageItem" :key="index" v-for="(languageItem,index) in item.language.slice(0, 2).filter(l => l)">{{languageItem}}</view>
                </view>
             </view>
           </view>
           <view class="empty" v-else>
             <view class="empty-img"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-empty.png'"></image></view>
             暂无数据~
           </view>
         </view>

         <!-- 热门医院部分 -->
         <view class="service-project" style="margin:20rpx 0rpx 88rpx;">
           <view class="service-project-head">
             <view class="service-project-head-l">热门医院</view>
             <view class="service-project-head-r" v-if="hospitalList.length > 1" @tap="$navto.push('HospitalRanking',{isShow:true,cityName:cityName})">更多<image class="head-r-img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-right-arrows.png'"></image></view>
           </view>
           <template v-if="hospitalList.length">
             <view class="service-project-item" v-for="item in hospitalList" :key="item.id" @tap="$navto.push('HospitalDetail',{id:item.id})">
               <view class="project-item-l" style="border-radius:50%"><image class="hospitalImage img" mode="aspectFit" :src="item.logo"></image></view>
               <view class="project-item-r">
                 <view class="project-item-r-title">{{ item.hospitalName }}</view>
                 <view class="project-item-r-box">
                   <view class="project-item-r-hospital" v-if="item.type">{{ item.type }}</view>
                   <view v-if="item.level && toString(item.level).split(',').length > 1">
                     <view class="project-item-r-hospital" v-for="(item2,index) in toString(item.level).split(',')" :key="index">{{ item2 }}</view>
                   </view>
                   <view v-else-if="item.level && toString(item.level).split(',').length == 1" class="project-item-r-hospital">{{ item.level }}</view>
                 </view>
                 <view class="project-item-r-address">
                   <view class="info-img"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-hospital-address.png'"></image></view>
                   <view class="address-text">{{ item.address }}</view>
                 </view>
               </view>
             </view>
           </template>
           <view class="empty" v-else>
             <view class="empty-img"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-empty.png'"></image></view>
             暂未录入~
           </view>
         </view>

  </view>
</template>

<script>
  export default {
    data(){
      return {
        file_ctx: this.file_ctx,
        pzsTitleLogo: this.file_ctx + 'static/image/business/hulu-v2/pzsTitleLogo.png',
        pzsNameLogo: this.file_ctx + 'static/image/business/hulu-v2/pzsNameLogo.png',
      }
    },
    props:{
      serviceList:{
        type: Array,
        default:[]
      },
      postList:{
        type: Array,
        default:[]
      },
      indexlist:{
        type: Array,
        default:[]
      },
      hospitalList:{
        type: Array,
        default:[]
      },
      accompanylist:{
        type: Array,
        default:[]
      },
      cityName:{
        type: String,
        default:''
      },
      skinColor:{
        type: String,
        default:''
      }
    },
    methods:{
      handleServiceMore(){
        // this.$navto.push('ServiceIndex')
        uni.switchTab({url:'/pages/service/index'})
        // this.navCurrent = 1
        // this.serviceCurrent = 2
      },
      handleJumpDoctor(id){
        this.$navto.push('DoctorDetail', {id})
      },
      handleJumpService(id){
        this.$navto.push('comboDetail',{id,city:this.cityName})
      },
      handleJump(id){
        this.$navto.push('accompanyDoctorDetails', {id})
      },
    }
  }
</script>

<style lang="scss">
  .service-project,.understand-accompany{
    margin: 20rpx 0;
    padding:24rpx 24rpx 0;
    background: #FFFFFF;
    border-radius: 16rpx;
    .service-project-head{
      display: flex;
      align-items: center;
      justify-content: space-between;
      .service-project-head-l{
        display: flex;
        align-items: center;
        font-size: 32rpx;
        color: #1D2029;
        line-height: 44rpx;
      }
      .service-project-head-r{
        display: flex;
        align-items: center;
        font-size: 24rpx;
        color: #868C9C;
        line-height: 34rpx;
        .head-r-img{
          display: flex;
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
    .service-project-item{
      display: flex;
      padding: 24rpx 0;
      border-bottom: 1rpx solid #EAEBF0;
      .project-item-l{
        width: 144rpx;
        height: 144rpx;
        border-radius: 12rpx;
        // background-color: skyblue;
        margin-right: 20rpx;
        overflow: hidden;
      }
      .hospitalImage{
        width: 120rpx;
        height: 120rpx;
        margin: auto;
        display: block;
      }
      .project-item-r{
        display: flex;
        flex: 1;
        flex-direction: column;
        .project-item-r-title{
          font-size: 30rpx;
          color: #1D2029;
          line-height: 42rpx;
        }
        .project-item-r-info{
          width: 474rpx;
          font-size: 22rpx;
          color: #868C9C;
          line-height: 32rpx;
          margin: 4rpx 0 10rpx;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .project-item-r-box{
          display: flex;
          .project-item-r-tag{
            padding: 2rpx 8rpx;
            font-size: 20rpx;
            color: #00A277;
            line-height: 28rpx;
            margin-right: 8rpx;
            border-radius: 4rpx;
            border: 1rpx solid rgba(0,180,132,0.4);
            &:last-child{
              margin-right: 0;
            }
          }
          .project-item-r-hospital{
            padding: 2rpx 8rpx;
            margin: 8rpx 0 12rpx;
            background: #FCF0DA;
            border-radius: 4rpx;
            font-size: 20rpx;
            color: #693E13;
            line-height: 28rpx;
            margin-right: 8rpx;
            &:last-child{
              margin-right: 0;
            }
          }
        }
        .project-item-r-address{
          display: flex;
          align-items: center;
          font-size: 22rpx;
          color: #4E5569;
          line-height: 32rpx;
          .info-img{
            display: flex;
            width: 24rpx;
            height: 24rpx;
          }
          .address-text{
            width: 438rpx;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-left: 8rpx;
          }
        }
        .project-item-r-money{
          color: #FF5500;
          span{
            font-size: 36rpx;
            line-height: 50rpx;
          }
        }
      }
      .accompany-item-l{
        width: 474rpx;
        margin-right: 20rpx;
        .title{
          font-size: 30rpx;
          color: #1D2029;
          line-height: 42rpx;
          margin-bottom: 16rpx;
        }
        .user-box{
          display: flex;
          align-items: center;
          .user-profile{
            width: 32rpx;
            height: 32rpx;
            // background-color: purple;
            margin-right: 8rpx;
            border-radius: 50%;
            overflow: hidden;
          }
          .user-name{
            font-size: 22rpx;
            color: #1D2029;
            line-height: 32rpx;
          }
        }
      }
      .accompany-item-r{
        width: 144rpx;
        height: 144rpx;
        border-radius: 12rpx;
        border: 1rpx solid #D9DBE0;
        overflow: hidden;
      }
      &:last-child{
        border-bottom: none;
      }
    }
    .empty{
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 50rpx 0;
      .empty-img{
        width: 286rpx;
        height: 212rpx;
        margin-bottom: 20rpx;
      }
    }
  }
  .depa-doctor-item{
    padding:20upx 0upx;
    background-color: #fff;
    .doctor-item-box{
      display: flex;
      padding-bottom:20upx;
      .doctor-item-l{
        width: 140upx;
        height: 100upx;
        image{
          width: 100%;
          height: 100%;
        }
      }
        .doctor-item-r{
          display: flex;
          flex-direction: column;
          flex: 1;
          margin-left: 15upx;
          .item-r-head{
            .name{
              color:#000;
            }
            span{
              margin-left: 10upx;
            }
          }
          .item-r-bott{
            margin-top: 20upx;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
            color:#333;
          }
        }
    }
  }
  .accompany-box{
    background: linear-gradient( 180deg, #E9F7F4 0%, #FFFFFF 100%);
    border-radius: 16rpx;
  }
  .pzsTitleIcon{
    display: flex;
    width: 196rpx;
    height: 40rpx;
    align-items: center;
    background: #E7E7F7;
    font-weight: 500;
    font-size: 24rpx;
    color: #5959F7;
    margin-left: 16rpx;
    border-radius: 40rpx;
    .pzsTitleLogo{
      width: 44rpx;
      height: 44rpx;
      margin-right: 8rpx;
    }
  }
  .accompany-List{
    display: flex;
    margin-top: 24rpx;
    .accompany-box{
      display: flex;
      flex-direction: column;
      // align-items: center;
      justify-content: center;
      padding-bottom: 32rpx;
      &:not(:last-child){
        margin-right: 26rpx;
      }
      .accompany-Icon{
        width: 198rpx;
        height: 198rpx;
        border-radius: 8rpx;
      }
      .accompany-username{
        display: flex;
        align-items: center;
        font-weight: 600;
        font-size: 30rpx;
        color: #1D2029;
        margin-top: 12rpx;
        .pzsNameLogo{
          width: 36rpx;
          height: 34rpx;
        }
      }
      .accompany-province{
        font-weight: 400;
        font-size: 24rpx;
        color: #4E5569;
      }
    }
  }
  .languageMap{
    display: flex;
    margin-top: 8rpx;
    min-height: 32rpx;
    .languageItem{
      height: 36rpx;
      padding: 2rpx 8rpx;
      margin-right: 8rpx;
      background: #EBF7F5;
      border-radius: 4rpx;
      font-weight: 400;
      font-size: 22rpx;
      color: #007959;
    }
  }
</style>
