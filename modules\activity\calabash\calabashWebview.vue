<template>
  <view class="">
    <!-- #ifdef H5 -->
    <view class="poster-content" v-if="isWx">
        <wx-open-launch-weapp
          id="launch-btn"
          :appid="$appId"
          @launch="handleClickBtn(1)"
          :path="`modules/activity/calabash/calabashWebview?gs=YFN3ui`"
          style="width: 750rpx;height: 1292rpx;position:absolute;"
        >
          <script type="text/wxtag-template">
            <div style="width: 750rpx;height: 1292rpx;position: relative;z-index:-1;">
              <img style="width: 100%;height: 100%;" src="https://file.greenboniot.cn/static/image/business/pharmacy-cyclopedia/pray-poster.png"></img>
            </div>

          </script>
        </wx-open-launch-weapp>
        <wx-open-launch-weapp
          id="launch-btn"
          :appid="$appId"
          @launch="handleClickBtn(2)"
          :path="`modules/activity/calabash/calabashWebview?gs=YFN3ui`"
          style="width: 512.91rpx;height: 98.97rpx;position:absolute;bottom:40rpx;left:50%;transform: translate(-50%, -50%);"
        >
          <script type="text/wxtag-template">
            <div class="testing-btn" style="
              display: flex;
              align-items: center;
              justify-content: center;
              position: relative;
              width: 512.91rpx;
              height: 98.97rpx;
            ">
              <img style="width: 100%;height: 100%;" src="https://file.greenboniot.cn/static/image/business/pharmacy-cyclopedia/pray-poster-btn.png"></img>
              <div style="width:314.02rpx;38.62rpx;position:absolute;">
                <img src="https://file.greenboniot.cn/static/image/business/pharmacy-cyclopedia/pray-poster-btn-text.png" style="position:absolute;top:50%;left:50%;transform: translate(-50%, -50%);width:157.01px;height:19.31px;">
              </div>
            </div>
          </script>
        </wx-open-launch-weapp>
    </view>
    <!-- #endif -->
    <!-- #ifndef H5 -->
    <view class="" v-if="url">
      <web-view @message='message' :src="url"></web-view>
    </view>
    <!-- #endif -->
  </view>
</template>

<script>
  import { mapState } from 'vuex'
  import common from '@/common/util/main'
  import calabashApis from "@/modules/common/api/calabash.js"
  export default{
    data(){
      return {
        $appId: this.$appId,
        file_ctx: this.file_ctx,
        $constant:this.$constant,
        isWx:false,
        url:'',
        queryId:'',
      }
    },
    computed: {
      ...mapState('user', {
        fansRecord: state => state.fansRecord,
      }),
    },
    onLoad (options) {
      const query = this.$validate.isNull(this.$Route.query) ? options : this.$Route.query
      console.log('query',query)
      if(query?.afterPopId){
        this.queryId = query?.afterPopId
      }
      if(!window) return
      let ua = window.navigator.userAgent.toLowerCase();
      if (ua.match(/MicroMessenger/i) == 'micromessenger') {
        this.isWx = true
      }
      this.wxh5ShareInit()
      // #ifdef H5
      this.pageexposurerecordInsert()
      // #endif
    },
    onShareAppMessage (res) {
      return {
        title: '今天的好运，从这里开始！', //分享的名称
        path: 'modules/activity/calabash/calabashWebview',
        mpId:this.$appId, //此处配置微信小程序的AppId
        imageUrl: this.file_ctx + 'static/image/business/pharmacy-cyclopedia/icon-pray-transpond.jpg',
      }
    },
    onShareTimeline(){
      return {
        title: '每天一个小祈福，还能积攒积分换好礼哦！快来试试吧！',
        path: 'modules/activity/calabash/calabashWebview',
      }
    },
    onUnload(){
      this.handleLookBrowse('EndOperationDetailsPageView')
    },
    methods: {
      handleLookBrowse(type){
        getApp().globalData.sensors.track(type,
          {
            'page_name' : '首页',
            'first_operation_name' : '祈福保平安',
            'second_operation_name' : '',
          }
        )
      },
      // 按钮点击
      async handleClickBtn(type){
        this.handleClickCommonPort(type)
      },
      async handleClickCommonPort(type){
        let parmas = {
          imageUrl:this.file_ctx + 'static/image/business/pharmacy-cyclopedia/pray-poster.png',
          accountId:this.accountId,
          businessType:this.$constant.drugBook.businessTypeObj.h5BlessingTypePage,
        }
        if(type == 1){
          await this.$api.drugBook.visitpagerecordInsert(parmas)
        } else {
          await this.$api.drugBook.imageoperationrecordInsert(parmas)
        }
      },
      // #ifdef H5
      async pageexposurerecordInsert(){
        let parmas = {
          imageUrl:this.file_ctx + 'static/image/business/pharmacy-cyclopedia/pray-poster.png',
          accountId:this.accountId,
          businessType:this.$constant.drugBook.businessTypeObj.h5BlessingTypePage,
        }
        await this.$api.drugBook.pageexposurerecordInsert(parmas)
      },
      // #endif
    },
    mounted(){
    },
    async onShow() {
      this.handleLookBrowse('OperationDetailsPageView')
      const accountId = common.getKeyVal('user', 'accountId', true);
      const token = common.getKeyVal('user', 'token', true);
      const {nickName,headPath} = this.fansRecord;
      console.log('username',this.fansRecord);
      console.log('process.env.NODE_ENV',process.env.NODE_ENV);
      let {data:isExist} = await calabashApis.pointuserIsExist({accountId})
      if(isExist) await calabashApis.pointuserInsert({accountId,username:nickName});
      // 判断当前环境
      let urlHeader = 'http://192.168.3.17:8080/'
      // let urlHeader = 'http://h5.ngrok.greenboniot.cn/'
      if(process.env.NODE_ENV == 'production'){
        urlHeader = 'https://saas.greenboniot.cn/'
      }
      this.url =`${urlHeader}ps/#/modules/activity/calabash/index?accountId=${accountId}&token=${token}&username=${nickName}&headPath=${headPath}&queryId=${this.queryId}&keyIndex=${Math.random()}`
      console.log('this.url',this.url);
    },
    onHide() {
      this.url = ''
      this.handleLookBrowse('EndOperationDetailsPageView')
    }
  }
</script>

<style  scoped lang="scss">
  .poster-content{
    position: relative;
    width: 750rpx;
    // height: 100vh;
    height: 1292rpx;
    overflow: hidden;
  }
</style>
