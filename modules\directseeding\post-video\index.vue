<template>
  <view class="bgm">
    <view class="integration-time-er-box">
      <integration-time-er :isBackIndex='true' :point='IntegrationData.integrationNum' :tracks='IntegrationData.integrationRuntimeNum'></integration-time-er>
    </view>
    <swiper circular class="sliderbox" vertical :indicator-dots="false" :current="currentRLIndex" :autoplay="false" :duration="duration" @change="onSwiperChange">
      <block v-for="(canlendar_item2, canlendar_idx) in displayList" :key="canlendar_idx">
        <swiper-item>
         <video-item
            :idx="canlendar_idx"
            :once="canlendar_item2.once"
            :editid="canlendar_item2.id"
            :data="canlendar_item2"
            :visible="canlendar_item2.visible"
            :width="areaWidth"
            :height="areaHeight"
            :Theight='Theight'
            :ishidden='ishidden'
            :businessType='businessType'
            @pre='preFn'
            :isShowBtn="isShowBtn"
            :systemType='systemType'
          ></video-item>
        </swiper-item>
      </block>
    </swiper>
    <!-- 竖屏标题 -->
    <lv-header
      :headerobj="headerobj"
      @pre="preNext"
      position="absolute"
    >
      <view class="livePlayerHeader">
        <view
          class="livePlayerHeader-c"
          style="padding-top: 10upx; padding-bottom: 10upx"
          :style="{
            'background-color': 'rgba(0,0,0,0)',
          }"
        >
        </view>
      </view>
    </lv-header>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import { isDomainUrl } from '@/utils';
import videoItem from './components/videoitem.nvue';
import { getQueryObject } from '@/utils/index'
import lvHeader from "@/modules/directseeding/shortVideo/list/components/lv-header.nvue";
export default {
  components: {
    videoItem,
    lvHeader
  },
  onLoad(options) {
    // console.log('options', options);
    this.$ext.wechat.getOpenId()

    const query = this.$Route.query
    // console.log("query------------",query)
    const that = this

    if(!query?.isShowBtn){
      this.isShowBtn = query.isShowBtn
    }

    if(query?.entryType){
      this.entryType = query.entryType
    }

    if(this.$validate.isNull(query.id)){
      let params = decodeURIComponent(query.scene)
      // console.log("params:====",params)
      query.id= getQueryObject(params).id
      query.params= getQueryObject(params).params
      if (this.$validate.isNull(query.id) && query.scene){
        that.$uniPlugin.toast('参数异常');
        return
      }
    }
    options = query;
    if (options && options.id) {
      this.currentid = options.id;
    }
    if (options && options.params) {
      this.params = typeof(options.params) === 'string' ? JSON.parse(options.params) : options.params
    }
    this.getDevice();
    this.getMeetingQueryPage().then(() => {
      this.recordPostmessageVisit()
    });
  },
  computed: {
    ...mapState('user', {
      accountId: state => state.accountId
    })
  },
  onShow() {
    this.ishidden = false;
  },
  beforeDestroy(){
    // console.log('beforeDestroy')
    this.ishidden = true;
  },
  onHide(){
    this.ishidden = true;

    if(uni.$cookieCount == 1){
      this.meetingviewlogvisit2();
    }
    // console.log('onHide')

  },
  destroyed(){
    this.ishidden = true;
    // console.log('destroyed')
  },

    /**
     * 生命周期函数--监听页面卸载
     */
  onUnload() {
    // console.log('onUnload')
    if(uni.$cookieCount == 1){
      this.meetingviewlogvisit2();
    }
    this.ishidden = true;
  },
  data() {
    return {
      systemType: null,
      currentCount:100,
      clickCount:0,
      // 是否正在加载分页数据
      isLoading:true,
      // 总条数
      totalCount:0,
      ishidden:false,
      // 要渲染到swiper-item的数组
      displayList: [],
      currentIndex: 0,
      currentRLIndex: 0,
      index: 0,
      // 视频列表
      totalList: [],

      duration: 200,
      currentItem: null,
      currentid: null,
      areaWidth: '100vw',
      areaHeight: '100vh',
      Theight:"100vh",
      businessType:7,
      pageSize:150,
      pageNum: 1,
      headerobj: {
        currentIndex: 3,
        headBgColor: "transparent",
        contentColor: "#fff",
      },
      params: {}, // 页面参数 过滤短视频
      launchOptions: uni.getLaunchOptionsSync(),
      isShowBtn:true, //默认显示
      entryType:null, //入口类型 病友分享进来为2
    };
  },
  onShareAppMessage(res) {
    return {
      title: this.displayList[this.currentIndex].title,
      path: '/modules/directseeding/post-video/index?id=' + this.currentid,
      imageUrl: isDomainUrl(this.displayList[this.currentIndex].imagesPath),
    };
  },

  methods: {
    preNext() {
      this.ishiddenFn = true;
      this.$emit("pre");

      this.$nextTick(() => {
        let pages = getCurrentPages();
        // console.log(pages)
        if (pages.length == 1) {
          uni.switchTab({
            url: "pages/index/index",
          });
        } else {
          uni.navigateBack({
            delta: 1,
            fail: function () {
              // console.log("kkk");
            },
          });
        }
      });
      // this.$emit()
    },
    // 触发加载第二页数据
    async loadMore(){
      if(this.isLoad || this.totalCount < this.pageSize){
        return
      }

      this.isLoading = true;
      this.pageNum += 1;
      await this.getMeetingQueryPage({},true)
      this.isLoading = false;
    },
    // 查找是否有正在播放的项目
    findPlayItem(arr){
      // 存在播放项目
      if(uni.$playingMeetingId){
          for(let i=0;i<arr.length;i++){
            if(arr[i].id == uni.$playingMeetingId){
              uni.$playingMeetingId = null;
            }
          }
      }
    },
    preFn(){
      this.ishidden = true;
    },
    // 刷新对应帖子详情
    async getPostDetail(){
      const res = await this.$api.postmessage.postmessageQueryOne({
        id:this.currentid,
        accountId: this.accountId
      })
      res.data.screenDirectionType = 1;// 默认是竖屏
      res.data.activityStatus = 5; // 默认回放类型
      res.data.videoPath = res.data.videosPath
      res.data.videoPath = '0/dm-service/970012297265192962.mp4';
      res.data.showCommentNumber = +res.data.virtualCommentNumber + res.data.commentNumber
      res.data.showLikeNumber = +res.data.virtualLikeNumber + res.data.likeNumber
      res.data.showCollectNumber = +res.data.virtualCollectNumber + res.data.collectNumber
      res.data.showShareNumber = +res.data.virtualShareNumber + res.data.shareNumber
      return res.data;
    },
    updateLoadMore() {
      this.clickCount += 1;
      if(this.clickCount === this.currentCount) {
        this.currentCount += this.currentCount;
        this.loadMore();
      }
    },
    // /dm/api/v1/meetingviewuser/save/view/user
    upDateDisplayList(once) {
      // 加载更多
      this.updateLoadMore();
      // 更新列表前执行结束观看记录请求
      this.findPlayItem(this.displayList);
      let displayList = [];
      displayList[this.currentIndex] = this.totalList[this.index];
      displayList[this.currentIndex].visible = true;
      displayList[this.currentIndex].once = once;
      displayList[this.currentIndex].ewmurl = isDomainUrl(displayList[this.currentIndex].qrCodePath);
      this.currentid = displayList[this.currentIndex].id;
      this.businessType = displayList[this.currentIndex].businessType

      let idx = this.index - 1 == -1 ? this.totalList.length - 1 : this.index - 1;
      displayList[this.currentIndex - 1 == -1 ? 2 : this.currentIndex - 1] = this.totalList[idx];
      displayList[this.currentIndex - 1 == -1 ? 2 : this.currentIndex - 1].visible = false;
      displayList[this.currentIndex - 1 == -1 ? 2 : this.currentIndex - 1].once = false;
      displayList[this.currentIndex - 1 == -1 ? 2 : this.currentIndex - 1].ewmurl = isDomainUrl(displayList[this.currentIndex - 1 == -1 ? 2 : this.currentIndex - 1].qrCodePath);

      let idx2 = this.index + 1 == this.totalList.length ? 0 : this.index + 1;
      displayList[this.currentIndex + 1 == 3 ? 0 : this.currentIndex + 1] = this.totalList[idx2];
      displayList[this.currentIndex + 1 == 3 ? 0 : this.currentIndex + 1].visible = false;
      displayList[this.currentIndex + 1 == 3 ? 0 : this.currentIndex + 1].once = false;
      displayList[this.currentIndex + 1 == 3 ? 0 : this.currentIndex + 1].ewmurl = isDomainUrl(displayList[this.currentIndex + 1 == 3 ? 0 : this.currentIndex + 1].qrCodePath);

      // console.log(displayList);
      this.displayList = displayList;
      this.$forceUpdate();
    },
    async onSwiperChange(e) {
      let current = e.detail.current;
      this.currentItem = this.displayList[current]
      if (this.currentIndex - current == 2 || this.currentIndex - current == -1) {
        this.index = this.index + 1 == this.totalList.length ? 0 : this.index + 1;
        this.currentIndex = this.currentIndex + 1 == 3 ? 0 : this.currentIndex + 1;
      } else if (this.currentIndex - current == -2 || this.currentIndex - current == 1) {
        this.index = this.index - 1 == -1 ? this.totalList.length - 1 : this.index - 1;
        this.currentIndex = this.currentIndex - 1 == -1 ? 2 : this.currentIndex - 1;
      }
      // 更新当前帖子视频的数据，某些数据只在query/one里才有
      this.currentid = this.displayList[this.currentIndex].id
      const res = await this.getPostDetail()
      this.totalList[this.index] = res
      this.upDateDisplayList();
      this.recordPostmessageVisit()
    },
    recordPostmessageVisit () {
      if (this.launchOptions.scene === 1154) return
      const param = {
        accountId: this.accountId,
        id: this.currentid
      }
      this.$api.postmessage.postmessageVisit(param)
    },
    // 获取设备宽高
    getDevice() {
      var systemInfo = uni.getSystemInfoSync();
      var areaWidth = systemInfo.windowWidth;
      var areaHeight = systemInfo.windowHeight;
      if(systemInfo.safeArea.bottom && systemInfo.windowHeight - systemInfo.safeArea.bottom < 100){
        areaHeight = systemInfo.safeArea.bottom
      }else{

      }

      this.areaWidth = areaWidth + 'px';
      this.areaHeight = areaHeight + 'px';
      this.Theight = areaHeight - 60 + 'px'
      this.systemType = systemInfo.system;
    },
    // 获取帖子列表
    getMeetingQueryPage(param = {},loadMore) {
      return new Promise((resolve,reject) => {
        const { mode, ...defaultParams } = this.params || {}
        let params = {
            current: this.pageNum,
            size: this.pageSize,
            condition: {
                materialType:2,// 帖子类型 1 图片 2 视频
                accountId: this.accountId,
                entryType:this.entryType,
                ...defaultParams
            }
        }
        if(!params?.condition?.entryType){
          delete params.condition.entryType
        }
        if(this.pageNum === 1) {
          this.currentCount = 100;
          this.clickCount = 0;
        }

        let api = this.$ext.community.postmessageQueryRecommendPage
        if (mode === 'personal') {
          api = this.$ext.community.postmessageQueryPage
        }
        api(params).then(async res => {
            // console.log('res-----',res)
            if(res.data.records.length === 0){
              return
            }
            let list = res.data.records.map(item => {
              item.screenDirectionType = 1;// 默认是竖屏
              item.activityStatus = 5; // 默认回放类型
              item.videoPath = item.videosPath;
              item.videoPath = '0/dm-service/970012297265192962.mp4';
              item.showCommentNumber = +item.virtualCommentNumber + item.commentNumber
              item.showLikeNumber = +item.virtualLikeNumber + item.likeNumber
              item.showCollectNumber = +item.virtualCollectNumber + item.collectNumber
              item.showShareNumber = +item.virtualShareNumber + item.shareNumber
              return item;
            });
            if (list.length > 0) {
              if (this.currentid) {
                // 需要获取详情，
                const tempData = await this.getPostDetail()
                let idx = list.findIndex(item => item.id === this.currentid);
                if(idx !== -1) {
                  list.splice(idx,1)
                }
                list.unshift(tempData)

                this.index = 0;
              } else {
                this.index = 0;
              }
            }

            if (list.length == 1) {
              list.push(JSON.parse(JSON.stringify(list[0])));
              list.push(JSON.parse(JSON.stringify(list[0])));
              list.push(JSON.parse(JSON.stringify(list[0])));
            } else if (list.length == 2) {
              list.push(JSON.parse(JSON.stringify(list[0])));
              list.push(JSON.parse(JSON.stringify(list[1])));
              list.push(JSON.parse(JSON.stringify(list[0])));
            } else if (list.length == 3) {
              list.push(JSON.parse(JSON.stringify(list[0])));
            }
            if(loadMore){
             this.totalList = this.totalList.concat(list)
            }else {
             this.totalList = list;
             this.upDateDisplayList(true);
            }
            this.totalCount = res.data.total - 0;
            // 没有更多了
            if(this.totalCount <= this.pageSize * this.pageNum) {
              this.isLoad = true;
            }
            resolve(true)
        });
      })
    }
  }
};
</script>

<style lang="scss">
.bgm {
  background: #000;
  // background: skyblue;
}
.sliderbox {
  height: 100vh;
  overflow: hidden;
}
</style>

<style lang="scss" scoped>
  .integration-time-er-box{
     position: fixed;
     top: 284rpx;
     left: 52rpx;
     color: white;
     z-index: 999999999999999;
   }
  .livePlayerHeader {
    display: flex;
    align-items: center;
    width: 100%;
    padding-left: 5upx;
    z-index: 9999;
    tansform: tanslate3d(0, 0, 0);
  }

  .livePlayerHeader-c {
    display: inline-flex;
    padding: 0upx 20upx;
    align-items: center;
    font-size: 28upx;
    color: #fff;
    border-radius: 50upx;
    overflow: hidden;
    padding-left: 60rpx;
    margin-top: 5upx;
  }
</style>
