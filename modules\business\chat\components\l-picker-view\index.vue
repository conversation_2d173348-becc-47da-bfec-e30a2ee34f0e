<template>
    <picker-view :indicator-style="indicatorStyle" :value="value" @change="bindChange" class="picker-view">
        <picker-view-column>
            <view class="item" v-for="(item,index) in list" :key="index">{{ item[pickerConfig.label] }}</view>
        </picker-view-column>
        <picker-view-column v-if="!$validate.isNull(childrenList)">
            <view class="item" v-for="(item,index) in childrenList" :key="index">{{ item[pickerConfig.label] }}</view>
        </picker-view-column>
    </picker-view>
</template>

<script>
export default {
    props: {
        value: {
            type: Array,
            default: () => [0, 0]
        },
        list: {
            type: Array,
            default: () => []
        },
        config: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            selectvVal: [0, 0],
            indicatorStyle: `height: 50px;`,
            pickerConfig: {
                children: 'children',
                label: 'label',
                value: 'value'
            },
            $validate: this.$validate
        }
    },
    watch: {
        config: {
            handler () {
                this.pickerConfig = {
                    ...this.pickerConfig,
                    ...this.config
                }
            },
            deep: true,
            immediate: true
        },
        value: {
            handler () {
                this.selectvVal = this.value
            },
            deep: true,
            immediate: true
        },
        selectvVal: {
            handler () {
                this.$emit('input', this.selectvVal)
                this.$emit('change', this.selectvVal)
            },
            deep: true
        }
    },
    computed: {
        childrenList () {
            if (!this.list.length) return []
            const item = this.list.find((item,index) => {
                return index === this.selectvVal[0]
            })
            if (item && Object.keys(item).length) {
                return item[this.pickerConfig.children]
            } else {
                return []
            }
            // return (item && Object.keys(item).length) ? item[this.pickerConfig.children] : []
        }
    },
    methods: {
        bindChange (value) {
            this.selectvVal = value.detail.value
            // if (value.detail)
        }
    }
}
</script>

<style lang="scss" scoped>
    .picker-view {
		width: 100%;
		height: 500upx;
		font-size: 28upx;
        background: #fff;
	}
	.item {
		height: 100upx;
		line-height: 100upx;
        font-size: 28upx;
		align-items: center;
		justify-content: center;
		text-align: center;
	}
</style>