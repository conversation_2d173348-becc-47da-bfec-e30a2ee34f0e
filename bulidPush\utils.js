// 参数	说明
// --platform	必填，发行平台(MP-WEIXIN)
// --project	必填，项目名称
// --appid	如果上传发行小程序到微信平台，则必填微信小程序appid
// --subPackage	发行为混合包, 示例：--subPackage xxx
// --upload	打包后是否上传到微信平台,只有值为true时生效
// --description	上传的小程序描述
// --privatekey	微信代码上传密钥文件 详情
// --version	上传小程序的版本号；选填。如果不填写，则会读取manifest.json中的版本号
// --sourceMap	生成SourceMap,值为 true 时生效，默认为 false
// --robot	指定微信ci机器人编号（取值范围：1 ~ 30），默认为 1
// 当前ip地址*************
import {exec} from "child_process"
import fs from "fs"
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import path from 'path';
// 定义项目名称
const project = 'gb-hybrid-app';
// 获取当前模块的文件路径
const __filename = fileURLToPath(import.meta.url);
// 获取当前模块的目录名称
const __dirname = dirname(__filename);
// 运行主函数 接受指令集合
export async function runTimeMain(instructionSet) {
  for (var index = 0; index < instructionSet.length; index++) {
    var element = instructionSet[index];
    // await delay(5000); // 等待 10 秒
    // 等待覆写
    await override(element.id)
    const stopLoading = showLoading('等待编译');
    // 等待编译
    await commandCode(element.code)
    stopLoading()
    if(index === instructionSet.length - 1) return
    // 立即执行下一次编译会造成Hbuliderx进入资源锁困境 所以在此等待十秒给予编译器缓冲时间
    await delay(10000); // 等待 10 秒
  }
}

// 获取指令参数
export function ParsingOptions(){
  // 获取指令传递来的参数
  const args = process.argv.slice(2); // 忽略前两个默认参数
  if(args.length === 0) return {}
  // 解析参数集合
  return args.reduce((accumulator, currentValue)=>{
    let [key,value] = currentValue.split('=');
    if(!key || !value) throw '参数格式错误'
    if(key === 'idList') {
      value = value.split(',').map(e=>+e);
    }
    accumulator[key] = value;
    return accumulator
  },{})
}

// let order = `cli publish --platform mp-weixin --project gb-hybrid-app-accompany`
// 生成编译指令集合
export function createInstructionSet(optionsMap,parameters){
  if(parameters.idList){
    optionsMap = optionsMap.filter(e=>parameters.idList.indexOf(e.id) >= 0)
    delete parameters.idList
  }
  return optionsMap.map((e,index)=>({
    ...parameters,
    platform:'mp-weixin',
    project,
    appid: e.appId,
    upload: true,
    privatekey:path.join(__dirname, 'keys', e.privatekey),
    robot: 1,
    id:e.id
  })).map(e=>{
    let id = e.id
    delete e.id
    return Object.keys(e).reduce((accumulator,currentValue)=>{
      accumulator.code+=` --${currentValue} ${e[currentValue]}`
      return accumulator;
    },{code:'cli publish',id})
  })
}

const delay = (ms) => {
  console.log('为防止进入资源锁困境在此等待' + ms / 1000 + '秒');
  const stopLoading = showLoading('等待缓冲');
  new Promise(resolve => setTimeout(()=>{
    stopLoading(); // 停止动画并显示完成
    resolve()
  }, ms));
}

// 覆写文件
export function override(id) {
  let resFn;
  let pro = new Promise((res) => resFn = res)
  let filePath = path.join(__dirname,'..', 'config', '/env/options.js');
  // 读取文件内容
  let fileContent = fs.readFileSync(filePath, 'utf8');
  console.log('开始覆写文件',id);
  // 将文件内容按行分割成数组
  let lines = fileContent.split('\n');
  // 覆写
  lines[5] = `let currentId = ${id};\r`;
  let modifiedContent = lines.join('\n');
  fs.writeFileSync(filePath, modifiedContent, 'utf8');
  console.log('覆写成功',id);
  resFn(1)
  return pro
}

// 运行指令
export function commandCode(code) {
  let resFn;
  let pro = new Promise((res) => resFn = res)
  exec(code, (err, stdout, stderr) => {
    if (err) {
      console.log('err', stdout);
      console.log('stderr', stderr);
      return;
    }
    console.log('stderr', stderr);
    console.log('stdout', stdout);
    console.log('编译完成');
    resFn()
  })
  return pro
}
function showLoading(loadText) {
  const symbols = ['|', '/', '-', '\\'];
  let index = 0;

  const timer = setInterval(() => {
    process.stdout.write(`\r${loadText} ${symbols[index++]}  `);
    index %= symbols.length;
  }, 100);

  // 返回停止函数
  return () => {
    clearInterval(timer);
    process.stdout.write('\r完成!            \n'); // 覆盖并换行
  };
}
