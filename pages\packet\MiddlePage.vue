<template>
  <view class="content">
    <uni-nav-bar backgroundColor="rgba(0,0,0,0)" :border="false" fixed statusBar>
      <image :src="file_ctx + 'h5/home/<USER>/icon-home.png'" class="icon-home" mode="aspectFill" slot="left" @tap="back" />
      <view class="nav-bar-title">绿葆自助取袋机</view>
    </uni-nav-bar>
    <view class="content-bg"></view>
    <view class="content-cell">
      <!-- #ifdef MP-WEIXIN -->
      <view v-if="gdtAdSwitch === 1 && !adCustomError" class="banner">
        <ad-custom unit-id="adunit-f4e74821e9b434a7" @load="adLoad" @error="adError($event, item)" @close="adClose($event, item)"></ad-custom>
      </view>
      <view id="ADS16577961605243122" class="banner van-image" v-else>
        <image :src="imgList.banner" class="banner-img" mode="widthFix" />
      </view>
      <!-- #endif -->
      <!-- #ifndef MP-WEIXIN -->
      <view id="ADS16577961605243122" class="banner van-image">
        <image :src="imgList.banner" class="banner-img" mode="widthFix" />
      </view>
      <!-- #endif -->
      <!--<view id="ADS6"></view>-->
      <view class="content-center">
        <image
          v-if="!showError"
          mode="aspectFill"
          :src="imgList.whiteTop"
          class="whiteTop"
        />

        <!-- 故障图文 -->
        <template v-if="showError">
          <image
            mode="aspectFill"
            :src="imgList.repair"
            alt=""
            class="repair"
          >
        </template>

        <view
          v-if="showError"
          class="scanH5QrCodeText error-tips"
        >{{scanH5QrCodeText}}</view>

        <view
          v-if="!showError && showLoading"
          class="scanH5QrCodeText"
        >加载中...</view>

        <view class="button-list" v-if="!showError && !showLoading">
          <view class="old-packet-panel">
            <view class="guide-box" v-if="showFreeButton || showPayButton">
              <text class="guide-text">
                <template v-if="showFreeButton && showPayButton">您可以自由选择</template>
                <template v-else-if="showFreeButton">点击下方按钮领取环保袋</template>
                <template v-else-if="showPayButton">点击下方按钮支付出袋</template>
              </text>
              <image mode="aspectFit" :src="file_ctx + 'h5/home/<USER>/icon-guide-bottom.png'" alt="" class="icon-guide-bottom">
            </view>
            <template v-if="!isStartNewPacketBtn">
              <button
                class="button"
                @tap="jump(1)"
              >
                <text>点击这里</text>&nbsp;
                <text>领取环保袋</text>
              </button>
            </template>
            <template v-else>
              <button v-if="showFreeButton" class="button" type="primary" @tap="jump(2)">
                <view class="tag-box free-box" :style="'background-image: url(' + file_ctx + 'h5/home/<USER>/icon-free-tag.png);'">免费</view>
                <text>
                  完成关注操作出袋
                </text>
              </button>
              <button v-if="showPayButton" class="button pay-btn" type="primary" @tap="jump(3)">
                <view class="tag-box pay-box" :style="'background-image: url(' + file_ctx + 'h5/home/<USER>/icon-pay-tag.png);'">付费</view>
                <text>{{ payPrice }}元购买</text>
              </button>
              <view style="font-size: 18px" v-if="!showFreeButton && !showPayButton">请联系客服，配置机器规则</view>
            </template>
          </view>

          <image
            v-if="(showPayButton || showFreeButton) && !qrCodeUrl"
            :src="imgList.hand"
            class="hand"
            mode="aspectFit"
          ></image>
        </view>
      </view>
      <!-- 底部 -->
      <view class="content-bottom">
        <image :src="imgList.logo" class="logo" mode="aspectFit"></image>
        <view class="tips-text">
          <text>遇到出袋问题请拨打客服电话：</text>
          <text @tap="callPhone">{{ kefuPhone }}</text>
        </view>
      </view>
    </view>
    <mobile-authorization ref="mobileAuthorizationRef" @add-record="addMobileAuthorizationRecord" @confirm="jump(3)" />
  </view>
</template>


<script>
import { getQueryObject } from '@/utils/index'
import env from '@/config/env'
import { mapState } from 'vuex'
import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'
import mobileAuthorization from './components/mobile-authorization'
export default {
  name: 'MiddlePage',
  components: {
    uniNavBar,
    mobileAuthorization
  },
  data() {
    return {
      file_ctx: env.file_ctx,
      $appId: this.$appId,
      kefuPhone: '************', // 客服电话
      // 小程序跳转地址
      ghId: null,
      appletUrl: null,
      showPayButton: false,
      showFreeButton: true,
      qrCodeUrl: null,
      associateId: null,
      code: null,
      deviceId: null,
      payAmount: 1,
      goodsId: null,
      payData: null,
      showReload: false,
      background: env.file_ctx + 'h5/background.jpg',
      man: env.file_ctx + 'h5/man.png',
      women: env.file_ctx + 'h5/women.png',
      text: '①长按 ②识别 ③关注',
      //请求执行前
      beforloadfun: true,
      entryWxId: '',
      wxId: '',
      scanH5QrCodeText: null,
      textBg: env.file_ctx + 'h5/freeText.png',
      height: '0px',
      showLoading: true, // 页面loading
      entryType: 2,
      authType: null,
      reloadText: '请重新扫码进入',
      authId: null,
      appId: null,
      hidenChooseGender: true,
      scanH5QrCodeData: {},
      imgList: {
        bg: env.file_ctx + 'h5/home/<USER>/home-bg.png',
        white: env.file_ctx + 'h5/home/<USER>',
        code: env.file_ctx + 'h5/home/<USER>',
        logo: env.file_ctx + 'h5/home/<USER>/icon-logo.png',
        xiaobao: env.file_ctx + 'h5/home/<USER>',
        repair: env.file_ctx + 'h5/home/<USER>/device-repair.png',
        banner: env.file_ctx + 'h5/home/<USER>/home-banner.png',
        close: env.file_ctx + 'h5/home/<USER>',
        guide: env.file_ctx + 'h5/home/<USER>',
        hand: env.file_ctx + 'h5/home/<USER>/press-tips.gif',
        tips: env.file_ctx + 'h5/home/<USER>',
        whiteTop: env.file_ctx + 'h5/home/<USER>/device-bag.png'
      },
      showVideo: false,
      showError: false,
      showHand: false,
      timeOutEvent: null,
      AdsKeyPrefix: 'ADS',
      needSaveRecommend: true,
      isStartNewPacketBtn:false, //是否启用新旧领袋模式
      payPrice: 0, //支付金额
      gbPacketScene: '',
      openSubscribeStatus: null, // 是否开启一次性订阅消息
      visitTime: Date.now(),
      deviceInfo: null,
      gdtAdSwitch: 2, // 广点通广告开关 1-开 2-关
      adCustomError: false // 广点通广告是否异常
    }
  },
  computed: {
    ...mapState('user', {
      accountId: state => state.accountId,
      curSelectUserInfo: state => state.curSelectUserInfo,
      fansRecord: state => state.fansRecord
    })
  },
  watch: {
  },
  async onLoad(paramsObj = {}) {
    const query = this.$Route.query
    const that = this
    if (!this.$validate.isNull(query)) {
      uni.setNavigationBarTitle({
        title: '欢迎使用绿葆自助取袋机'
      })
      if(this.$validate.isNull(query.gbPacketScene) && this.$validate.isNull(query.deviceId) && query.scene){
        let params = decodeURIComponent(query.scene)
        params = getQueryObject(params)
        query.gbPacketScene = (params.gbPacketScene && params.gbPacketScene !== 'null') ? params.gbPacketScene : null
        query.deviceId = (params.deviceId && params.deviceId !== 'null') ? params.deviceId : null
        if (this.$validate.isNull(query.gbPacketScene) && this.$validate.isNull(query.deviceId)){
          that.errorTip()
          return
        }
      }
      
      that.gbPacketScene = query.gbPacketScene
      that.deviceId = query.deviceId
      this.showLoading = true
      if (that.gbPacketScene){
        that.$uniPlugin.loading('加载中', true)
        await that.getH5Link().catch(() => this.showLoading = false)
        await that.checkDevice().catch(() => this.showLoading = false)
        that.$uniPlugin.hideLoading()
      } else if (that.deviceId) {
        that.$uniPlugin.loading('加载中', true)
        that.checkDevice()
        await that.getH5LinkByDeviceId().catch(() => this.showLoading = false)
        that.$uniPlugin.hideLoading()
      } else {
        that.errorTip()
      }
      this.showLoading = false
    } else {
      that.errorTip()
    }
  },
  onShow() {
    let visitTimeNew = Date.now();
    let visitTime = this.visitTime
    if (visitTime!=null){
      if ((visitTimeNew - visitTime)/1000 >= 20) {
        this.$navto.replaceAll('Index')
      }
    }
  },
  destroyed() {

  },
  methods: {
    // #ifdef MP-WEIXIN
    adLoad(e) {},
    adError(e, item) {
      this.adCustomError = true
    },
    adClose(e, item) {
      this.adCustomError = true
    },
    // #endif
    /**
     * @param {number} type 类型
     * 1、自定义授权窗口-曝光
     * 2、自定义授权窗口-确认授权
     * 3、自定义授权窗口-取消
     * 4、官方授权窗口-允许
     * 5、官方授权窗口-取消
     * 6、成功授权手机
     */
    addMobileAuthorizationRecord(type) {
      const { centerUserId = '' } = this.curSelectUserInfo || {}
      const { nickName } = this.fansRecord
      this.$api.packet.packetoperationrecordInsert({
        accountId: this.accountId,
        userId: centerUserId,
        operationType: type,
        nickName
      })
    },
    // 获取当前用户是否已绑定了手机号（已授权过手机号）
    getUserIsBindPhone() {
      return new Promise((resolve, reject) => {
        this.$ext.user.getCommunityFansRecord().then(() => {
          this.$ext.user.getFansBindRecord().then(res => {
            const { phone } = res || {}
            return resolve(!!phone)
          }).catch(() => {
            resolve(false)
          })
        }).catch(() => {
          resolve(false)
        })
      })
    },
    // 判断是否打开授权手机号弹窗
    async showMobileAuthorizationFn() {
      const isBind = await this.getUserIsBindPhone()
      if (isBind) return
      this.$refs.mobileAuthorizationRef.open()
      this.addMobileAuthorizationRecord(1)
    },
    back() {
      let pages = getCurrentPages() // 获取栈实例
      if (pages.length > 1) {
        this.$navto.back()
      } else {
        this.$navto.replaceAll('Index')
      }
    },
    /**
     * 错误提示
     */
    errorTip(){
      this.$uniPlugin.toast('入口过期，请重新扫码！')
    },
    /**
     * 参数异常提示，并延迟跳转到首页
     */
    timingErrorJump() {
      const that = this
      that.$uniPlugin.hideLoading()
      that.$uniPlugin.toast('入口过期，请重新扫码！')
      setTimeout(()=>{
        that.$navto.replaceAll('Index', {})
      }, 3000)
    },
    /**
     * 根据设备id获取h5出袋页
     */
    async getH5LinkByDeviceId() {
      const that = this
      const res = await this.$api.packet.getAppSubscribeScreenPacketUrl({ deviceId: this.deviceId }).catch((res)=>{
        this.$uniPlugin.hideLoading()
        that.errorTip()
      })
      if (res.data) {
        that.authUrl = res.data.authUrl || ""
        if (!that.authUrl || that.authUrl === 'null') {
          that.timingErrorJump()
        }
      }else {
        that.timingErrorJump()
      }
    },
    /**
     * 获取链接
     */
    async getH5Link(){
      const that = this
      const res = await this.$api.packet.getSourceH5PacketUrl({gbPacketScene:this.gbPacketScene}).catch((res)=>{
        this.$uniPlugin.hideLoading()
        that.errorTip()
      })
      if (res.data) {
        that.authUrl = res.data.authUrl || ""
        that.deviceId = res.data.deviceId || ""
        if (!that.authUrl || !that.deviceId || that.deviceId === 'null' || that.authUrl === 'null') {
          that.timingErrorJump()
        }
      }else {
        that.timingErrorJump()
      }
    },
    showModal({ message } = {}) {
      return new Promise((resolve, reject) => {
        this.$uniPlugin.modal('', message, {
          showCancel: false, // 是否显示取消按钮，默认为 true
          cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
          cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
          confirmText: '确定', //  确定按钮的文字，默认为"确定"，最多 4 个字符
          confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
          fn: async (n) => {
            if(n) {
              resolve()
            } else {
              reject()
            }
          }
        })
      })
    },
    callPhone() {
      wx.makePhoneCall({
        phoneNumber: this.kefuPhone, //仅为示例，并非真实的电话号码
        fail: res => {
          this.$uniPlugin.toast('调起拨打电话失败 err：' + JSON.stringify(res))
        }
      })
    },
    /**
     * 获取设备信息，用于判断按钮样式
     * packetAuthType 出袋授权模式(1-先授权(旧),2-后授权(新))
     */
    async checkDevice(){
      const that = this
      if(!that.deviceId || that.deviceId === 'null'){
        that.errorTip()
        return
      }
      const res = await that.$api.packet.getDeviceInfoByDeviceId({ deviceId: that.deviceId}).catch((err) => {
        // debugger
        that.isStartNewPacketBtn = false
        that.showError = true
        that.scanH5QrCodeText = err.msg
        console.log("getDeviceInfoByDeviceId error:", err)
        if (err.data && that.$validate.judgeTypeOf(err.data) === 'Object') {
          that.gdtAdSwitch = err.data.gdtAdSwitch || 2
        }
      })
      that.openSubscribeStatus = res.data.openSubscribeStatus
      that.payPrice = res.data.price
      that.deviceInfo = res.data
      that.gdtAdSwitch = res.data.gdtAdSwitch || 2
      // if (res.data.openPhoneAuth === 1) {
      //   that.showMobileAuthorizationFn()
      // }
      if (res.data.packetAuthType === 1) {
        that.isStartNewPacketBtn = false
      } else {
        // 付费开关，1开  0关
        that.showFreeButton = res.data.freeSwitch === 1
        that.showPayButton = res.data.paySwitch === 1
        that.isStartNewPacketBtn = true
      }
    },
    /**
     * type:1默认 2只有免费 3只有付费 pType=P-TYPE
     * @param type
     */
    async jump(type){
      const that = this
      if (!this.authUrl) {
        this.showModal({message: '跳转页面异常，请重新扫码！'})
        return
      }

      // 消息订阅
      // #ifdef MP-WEIXIN
      const permanentSubscribeRes = await this.permanentSubscribe()
      if (!permanentSubscribeRes) {
        await this.onceSubscribe()
      }
      // #endif

      this.$uniPlugin.loading('跳转中...', true)
      let rUrl = this.authUrl
      rUrl = rUrl.replace('P-TYPE',type)
      // debugger
      console.log("==== p-type:",type)
      this.$navto.replaceAll('PagesWebHtmlView', { src: encodeURIComponent(rUrl), title: '欢迎使用绿葆自助取袋机'})
    },
    /**
     * 获取订阅记录
     */
    async getWxsubscribemessagelogByAccountIdAndTemplateId(templateId) {
      const accountId = this.accountId
      const res = await this.$api.common.wxsubscribemessagelogQueryAccountIdAndTemplateId({ accountId, templateId })
      return Promise.resolve(res.data)
    },
    /**
     * 永久订阅
     */
    permanentSubscribe() {
      return new Promise(async (resolve, reject) => {
        const { permanentSubscribe } = this.deviceInfo
        if (permanentSubscribe !== 1) return resolve(false)
        const res = await this.getWxsubscribemessagelogByAccountIdAndTemplateId(this.$constant.system.packetPermanentTmplIds[0])
        if (!this.$validate.isNull(res) && this.openSubscribeStatus === 1) return resolve(false)
        const { centerUserId = '' } = this.curSelectUserInfo || {}
        // 获取openid
        let openId = await this.$ext.wechat.getOpenId()
        const templateIds = this.$constant.system.packetPermanentTmplIds
        const subscribeMessageRes = await this.requestSubscribeMessage(templateIds)
        const logParamList = Object.keys(subscribeMessageRes).filter(key => {
          return templateIds.includes(key)
        }).map(key => {
          return {
            appId: this.$appId,
            templateId: key,
            openId: openId,
            subscribeStatus: subscribeMessageRes[key],
            businessType: 10,
            businessId: this.deviceId,
            accountId: this.accountId,
            userId: centerUserId
          }
        })
        // #ifdef MP-WEIXIN
        getApp().globalData.sensors.track("Subscription",
          {
            'content_belong_circle' : '',
            'function_name' : '出袋订阅',
            'subscription_type':'一次性订阅'
          }
        )
        // #endif
        this.$api.common.wxsubscribemessagelogInsertBatch({wxSubscribeMessageLogList: logParamList})
        return resolve(true)

      })

    },
    /**
     * 一次性订阅
     */
    onceSubscribe() {
      return new Promise(async (resolve, reject) => {
        if (this.openSubscribeStatus !== 1) return resolve(false)
        const { centerUserId = '' } = this.curSelectUserInfo || {}
        // 获取openid
        let openId = await this.$ext.wechat.getOpenId()
        const templateIds = this.$constant.system.packetTmplIds
        const subscribeMessageRes = await this.requestSubscribeMessage(templateIds)
        const logParamList = Object.keys(subscribeMessageRes).filter(key => {
          return templateIds.includes(key)
        }).map(key => {
          return {
            appId: this.$appId,
            templateId: key,
            openId: openId,
            subscribeStatus: subscribeMessageRes[key],
            businessType: 6,
            businessId: this.deviceId,
            accountId: this.accountId,
            userId: centerUserId
          }
        })
        if (!this.$validate.isNull(logParamList)) this.$api.common.wxsubscribemessagelogInsertBatch({wxSubscribeMessageLogList: logParamList})
        return resolve(true)

      })
    },
    // 消息订阅
    requestSubscribeMessage(tmplIds) {
      return new Promise((resolve, reject) => {
        try {
          this.$uniPlugin.requestSubscribeMessage(tmplIds, (res) => {
            let status = true
            tmplIds.forEach(item => {
              if(res[item.toString()] !== 'accept') status = false
            })
            if (status) {
              this.isShow = false
              this.$uniPlugin.toast('订阅成功')
              resolve(res)
            } else {
              this.isShow = true
              this.$uniPlugin.toast('订阅失败')
              resolve(res)
            }
          }, (err) => {
            resolve({})
          })
          this.$uniPlugin.hideLoading()
        } catch(err) {
          reject(err)
        }
      })
    },
    isEmpty(obj){
      if(typeof obj == "undefined" || obj == null || obj == ""){
        return true;
      }else{
        return false;
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@import './common.scss';
.error-tips {
  text-align: center;
}
</style>
