<template>
  <page>
    <view slot="content" class="main-body">
      <view class="main">
        <scroll-view scroll-y="true" class="scroll-view-main">
          <view class="m-main">
            <!-- #ifndef MP-ALIPAY -->
            <view class="list m-t-20" @tap="navtoGo('About')">
              <view class="title">关于绿葆</view>
              <view class="icon">
                <image class="width-height-atuo" :src="$static_ctx + 'image/business/icon-more.png'"/>
              </view>
            </view>
            <!-- #endif -->
            <!--    <view class="list m-t-20" @tap="clearCache">-->
            <!--      <view class="title">清除缓存</view>-->
            <!--      <view class="icon">-->
            <!--        <image class="width-height-atuo" :src="$static_ctx + 'image/business/icon-more.png'"/>-->
            <!--      </view>-->
            <!--      <view class="text">150M</view>-->
            <!--    </view>-->
            <view class="list m-t-20">
              <view class="title">发布版本</view>
              <view class="text">
                {{version_ctx}}
              </view>
            </view>
            <view class="list m-t-20" @tap="loginOut">
              <view class="title">退出登录</view>
              <view class="icon">
                <image class="width-height-atuo" :src="$static_ctx + 'image/business/icon-more.png'"/>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
  </page>
</template>

<script>
export default {
  name: 'Personal',
  components: {

  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $static_ctx: this.$static_ctx,
      version_ctx: this.version_ctx,
      roleList: [{ name: '' }]
    }
  },
  onLoad(){
    // #ifdef MP-WEIXIN
    this.handleClickTrack('OperationDetailsPageView')
    // #endif
  },
  onUnload(){
    // #ifdef MP-WEIXIN
    this.handleClickTrack('EndOperationDetailsPageView')
    // #endif
  },
  methods: {
    // #ifdef MP-WEIXIN
    handleClickTrack(type){
      let pages = getCurrentPages()
      let current = pages[pages.length - 1]; // 获取到当前页面的信息
      let pageInfo = __wxConfig.page && __wxConfig.page[`${current.route}.html`] || ''; // 内部属性
      getApp().globalData.sensors.track(type,
        {
          'page_name' : pageInfo?.window?.navigationBarTitleText || '',
          'first_operation_name' : '更多功能',
          'second_operation_name' : '系统设置',
        }
      ) 
    },
    // #endif
    navtoGo(url = '', obj = {}) {
      this.$navto.push(url, obj)
    },
    helpCenter() {
      this.$navto.push('WebHtmlView', { src: 'http://www.greenbon.cn/', title: '绿葆' })
    },
    clearCache() {
      uni.showToast({ title: '清除缓存' })
    },
    loginOut() {
      const that = this
      uni.showModal({
        content: '是否退出登录？',
        confirmText: '确定',
        cancelText: '取消',
        success: async(data)=> {
          if (data.confirm) {
            that.$ext.user.loginOut()
            // #ifdef MP-WEIXIN
            const openId = await this.$ext.wechat.getOpenId()
            getApp().globalData.sensors.logout(openId)
            getApp().globalData.sensors.resetAnonymousIdentity()
            // #endif
          } else if (data.cancel) {

          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .main-body{
    height: 100%;
    .main{
      height: 100%;
      .scroll-view-main {
        height: 100%;
      }
    }
  }
  .m-main {
    overflow: hidden;
    background-color: #f7f7f7;
    .list{
      background-color: #fff;
      padding: 0 30upx;
      height: 88upx;
      .title{
        display: inline-block;
        float: left;
        height: 88upx;
        line-height: 88upx;
        font-size: 32upx;
        color: #333;
      }
      .text{
        display: inline-block;
        float: right;
        height: 88upx;
        line-height: 88upx;
        font-size: 30upx;
        color: #999999;
        margin-right: 20upx;
      }
      .icon{
        display: inline-block;
        float: right;
        height: 44upx;
        width: 44upx;
        margin-top: 22upx;
      }
    }
  }
  .width-height-atuo{
    width: 100%;
    height: 100%;
  }
  .m-t-20{
    margin-top: 20upx;
  }
  .p-t-20{
    padding-top: 20upx;
  }
</style>
