<template>
  <view class="myLuckyCoinBg">
     <uni-nav-bar
        class="my-nav-bar"
        v-if='!IntegrationData.backTaskFlag'
        @clickLeft="back"
        color="black"
        :border="false"
        :left-icon="isAliPay ? '' : 'left'"
        :fixed="false"
        statusBar
        left-width="48rpx"
        right-width="100px"
        backgroundColor="rgba(0,0,0,0)"
      >
      </uni-nav-bar>
     <uni-nav-bar
        v-else
        color="#1d2029"
        :border="false"
        @clickLeft="back"
        :showBtnsLeft='!IntegrationData.backTaskFlag'
        :showBtnsRight='false'
        :fixed="false"
        title='交流'
        statusBar
        left-width="48rpx"
        right-width="100px"
        backgroundColor="rgba(0,0,0,0)"
      >
        <integration-time-er :isBackIndex='true' :backTaskFlag='IntegrationData.backTaskFlag'></integration-time-er>
      </uni-nav-bar>
      <!-- 任务背景 -->
      <view class="taskBg">
        <view class="luckyCoinTop">
          <image class="luckyCoinTitle" :src="luckyCoinTitle" mode="aspectFill"></image>
          <view class="">任务:<text class="luckyCoinTitleNum">{{finishNum}}/{{totalNum}}</text></view>
        </view>
        <image class="luckyoinIcon" :src="luckyoinIcon" mode="aspectFill"></image>
      </view>
      <!-- 任务列表 -->
      <view class="luckyCoinTaskMap">
        <view class="luckyCoinTask" v-for="(item,index) in taskList" :key="index">
          <image class="taskIcon" :src="taskIconPrefix + item.icon" mode="aspectFill"></image>
          <view class="taskContent">
            <view class="taskTitle">{{item.taskTitle}}</view>
            <view class="taskPoint">
              <image class="minGold" :src="minGold" mode="aspectFill"></image>
              +{{item.point}}
              {{item.taskNum > 1 ? '(' + item.finishNum + '/' + item.taskNum + ')' : ''}}
              </view>
          </view>
          <view v-if="!item.finish" class="gotoFinish" @click="gotoFinish(item)">去完成</view>
          <view v-else class="done gotoFinish">已完成</view>
        </view>
      </view>
      <!-- 占位元素 -->
      <view class="h-100"></view>
  </view>
</template>

<script>
  import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'
  import { mapState } from 'vuex'
  import calabashApis from "@/modules/common/api/calabash.js"
  import cloudClassroomApis from "@/modules/common/api/cloudClassroom.js"
  import common from '@/common/util/main'
  import {EventTypeList} from "./components/mapOptions"
  export default{
    components:{
      uniNavBar
    },
    data(){
      return {
        file_ctx: this.file_ctx,
        accountId:0,
        luckyCoinTitle: this.$static_ctx + "image/business/hulu-v2/luckyCoinTitle.png",
        luckyoinIcon: this.$static_ctx + "image/business/hulu-v2/luckyoinIcon.png",
        taskIconPrefix: this.$static_ctx + "image/business/hulu-v2/",
        minGold: this.$static_ctx + "image/business/hulu-v2/minGold.png",
        taskList:[],
        // 1 浏览（进入）
        // 2 观看
        // 3 订阅
        // 4 完成（进行、创建、预约、发表、发布、发视频、添加家庭就诊人,点赞、收藏）
        // 5 分享
        taskIconMap:[
          {icon:'task-browse.png',MappingIds:[17,1,3,7,9,11]},
          {icon:'task-watch.png',MappingIds:[12,13,18]},
          {icon:'task-sub.png',MappingIds:[5]},
          {icon:'task-finish.png',MappingIds:[2, 4, 8, 10, 14, 16, 22, 23, 24, 25,19,20,26]},
          {icon:'task-share.png',MappingIds:[6, 15, 21]},
          {icon:'icon-sign-conin-rumour.png',MappingIds:[27]},
        ],
        finishNum:0,
        totalNum:0
      }
    },
    methods:{
      gotoFinish({businessType,eventType,accountId,routhPath}){
        // 所有路径都要执行的路由选项解析和状态更新
        const pathWithoutParams = routhPath?.split('?')[0]
        const routerOptions = this.parsedHashRouterOptions(routhPath)
        if (routerOptions.backTaskFlag) {
          this.$store.dispatch('system/UpdateBackTaskFlag', true)
        }

        if (routhPath.startsWith('pages')) {
          // 跳转到 tabBar 页面
          uni.switchTab({ url: routhPath })
        } else {
          // 使用自定义方法跳转非 tabBar 页面
          this.$navto.replacePath(pathWithoutParams)
        }
      },
      // 解析hash模式路由参数
      parsedHashRouterOptions(router){
        let routerOptions = router.split('?')[1]?.split('&')?.reduce((res,index)=>{
          let map = index.split('=');
          res[map[0]] = map[1];
          return res
        },{})
        return routerOptions || {}
      },
      createUrl(url,options) {
      		if(!options) return
      		let keys = Object.keys(options)
      		let s = '?'
      		keys.map(e=>s+=`${e}=${options[e]}&`)
      		return url += s.slice(0,s.length -1)
      	},
      async getPointtaskQueryPointList(){
        let {data} = await calabashApis.pointtaskQueryPointList({accountId:this.accountId})
        let {data:BusinessTypeList} = await calabashApis.getBusinessTypeList();
        data.taskList && data.taskList.map(async (e,index)=>{
          // 判定是否指定帖子作者
          if(e.articleAccount){
            let queryOptions = {size: 1,current: 1,condition: {exitAccountId: e.articleAccount}}
            let {data:{records}} = await calabashApis.fansrecordQueryPage(queryOptions);
            let currentAuthor = records.filter(record=>record.accountId == e.articleAccount)[0]
            if(currentAuthor)e.username = currentAuthor.nickName
          }
          // 判断是否指定栏目
          if(e.videoColumn){
            console.log('videoColumn',e.videoColumn,index)
            let {data} = await cloudClassroomApis.getMeetingclassifyQueryList()
            console.log('getMeetingclassify',data);
            let currentClass = data.filter(cla=>cla.id == e.videoColumn)[0]
            console.log('currentClass',currentClass);
            e.videoColumnName = currentClass.name
            console.log('eeee',e);
          }
          this.taskIconMap.map(taskIcon=>{
            if(taskIcon.MappingIds.indexOf(e.eventType)>=0){
              e.icon = taskIcon.icon
            }
          })
          let {desc:event,path} = EventTypeList.filter(event=>event.id === e.eventType)[0]
          // articleSetup 帖子是否设置浏览/观看时间：1-是 0-否
          e.taskTitle = `${event}${e.videoColumnName ? `(${e.videoColumnName})` : ``}`
          e.routhPath = path
          if(e.videoSetup || e.articleSetup){
            let times = e.videoSetup && e.videoTime || e.articleSetup && e.articleTime;
            e.taskTitle+=(times + '秒')
          }
          this.$set(this.taskList,index,e)
        })
        this.finishNum = data.finishNum || 0
        this.totalNum = data.totalNum || 0
      },
      back() {
        let pages = getCurrentPages() // 获取栈实例
        if (pages.length > 1) {
          this.$navto.back()
        } else {
          this.$navto.replaceAll('Index')
        }
      },
    },
    onShow() {
      this.accountId = common.getKeyVal('user', 'accountId', true)
      this.getPointtaskQueryPointList()
    },
  }
</script>
<style>
  page{
    background-color: #FFFFFF;
  }
</style>
<style lang="scss">
  .myLuckyCoinBg{
    width: 100vw;
    height: 100vh;
    @include boxBgBYOUnit(100vw, 176rpx, '/business/hulu-v2/luckyCoinTaskBGTop.png');
    background-repeat: no-repeat;
    position: relative;
    /deep/ .my-nav-bar{
      .uni-navbar{
        .uni-navbar__content{
          z-index: 999;
        }
      }
    }
  }
  .taskBg{
    width: 100vw;
    height: 346rpx;
    margin-top: -12rpx;
    @include boxBgBYOUnit(100vw, 346rpx, '/business/hulu-v2/luckyCoinTaskBGBot.png');
    background-repeat: no-repeat;
    font-size: 28rpx;
    color: #1D2029;
    padding: 0 48rpx;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    position: absolute;
    top: 88px;
    left: 0;
    z-index: -1;
    .luckyoinIcon{
      width: 248rpx;
      height: 208rpx;
    }
    .luckyCoinTop{
      .luckyCoinTitleNum{
        font-weight: 500;
        font-size: 28rpx;
        color: #1D2029;
      }
      .luckyCoinTitle{
        width: 264rpx;
        height: 66rpx;
        margin-bottom: 4rpx;
        margin-top: 30rpx;
      }
    }
  }
  .luckyCoinTaskMap{
    width: 100vw;
    padding: 0 32rpx;
    box-sizing: border-box;
    margin-top: 164rpx;
    margin-bottom: 100rpx;
    .luckyCoinTask{
      width: 686rpx;
      height: 148rpx;
      padding: 32rpx 24rpx;
      box-sizing: border-box;
      background: #FFFFFF;
      border-radius: 16rpx;
      border: 1rpx solid #D9DBE0;
      margin-bottom: 20rpx;
      display: flex;
      align-items: center;
      .taskIcon{
        width: 84rpx;
        height: 84rpx;
      }
      .taskContent{
        margin-left: 16rpx;
        .taskTitle{
          font-weight: 500;
          font-size: 28rpx;
          color: #1D2029;
        }
        .taskPoint{
          margin-top: 4rpx;
          font-weight: 500;
          font-size: 28rpx;
          color: #00B484;
          display: flex;
          align-items: center;
          .minGold{
            width: 30rpx;
            height: 32rpx;
            margin-right: 8rpx;
          }
        }
      }

      .gotoFinish{
        width: 120rpx;
        height: 52rpx;
        line-height: 52rpx;
        background: #00B484;
        border-radius: 40rpx;
        text-align: center;
        font-weight: 500;
        font-size: 24rpx;
        color: #FFFFFF;
        margin-left: auto;
      }
      .done{
        background: #C9CCD4;
      }
    }
  }
</style>
