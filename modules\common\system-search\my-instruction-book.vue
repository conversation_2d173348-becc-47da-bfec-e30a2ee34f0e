<template>
  <view class="m-main-body">
    <scroll-refresh class="scroll-refresh-main" :isShowEmptySwitch="true" :fixed="false" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
      <view class="wrapper" :style="{height:contentList.length==0 && '0'}">
        <view class="wrapper-item" v-for="(item,index) in contentList" :key="index" @click="handleClickJump(item)">
          <view class="item-l">
            <!-- <image mode="widthFix" :src="item.banner[0]"></image> -->
            <image :src="item.listCover"></image>
          </view>
          <view class="item-r">
            <view class="item-r-head">{{ item.name }}</view>
            <view class="item-r-bott" v-if="item.brandName">产家：{{ item.brandName }}</view>
          </view>
        </view>
      </view>
    </scroll-refresh>
  </view>
</template>

<script>
  import { mapState } from 'vuex'
  import { isDomainUrl } from '@/utils/index.js'
  export default {
    props: {
      search:{
        type:String,
        default:''
      }
    },
    data(){
      return{
        file_ctx: this.file_ctx,
        $constant: this.$constant,
        mescroll: null, // mescroll实例对象
        downOption: {
          auto: false // 不自动加载
        },
        upOption: {
          auto: false, // 不自动加载
          empty: {
            top: 0,
            zIndex: 999,
          },
          onScroll: true
        },
        contentList:[],
      }
    },
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId,
      }),
    },
    onLoad(){
      this.$nextTick(() => {
        this.init()
      })
    },
    mounted(){},
    methods:{
      handleClickJump(item){
        this.$navto.push('PharmacyCyclopedia',{gs:item.code})
      },
      returnFn(obj) {
        const that = this
        setTimeout(function () {
          let params = {
            current: obj.pageNum,
            size: obj.pageSize,
            condition: {
              accountId:that.accountId,
              linkId:null,
            }
          }
          // that.$api.drugBook.pharmacyQueryHistory(params).then(res => {
          that.$api.drugBook.getProductQueryPage({...params,condition:{shelfStatus:1,name:that.search,}}).then(res => {
            // let data = res.data.records.map(item=>({...item,banner:item.banner.split(',').map(item=>(isDomainUrl(item)))}))
            let data = res.data.records.map(item=>({...item,banner:item.banner.split(',').map(item=>(isDomainUrl(item))),listCover:isDomainUrl(item.listCover)}))
            if (obj.pageNum === 1) {
              that.contentList = []
            }
            that.contentList = [...that.contentList, ...data]
            obj.successCallback && obj.successCallback(data)
          })
        }, that.$constant.noun.scrollRefreshTime)
      },
      scrollInit(scroll) {
        scroll.optUp.page.num = 1
        scroll.optUp.page.size = 10
        this.mescroll = scroll
      },
      init() {
        this.$nextTick(() => {
          this.mescroll.triggerDownScroll()
        })
      },
    },
 }
</script>

<style lang='scss' scoped>
  .m-main-body{
    height: 100%;
    .scroll-refresh-main{
      height: 100%;
      .wrapper{
        height: 100%;
        // padding:24rpx 32rpx;
        .wrapper-item{
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 32rpx 24rpx;
          background-color: #fff;
          border-radius: 26rpx;
          margin-bottom: 20rpx;
          .item-l{
            display: flex;
            align-items: center;
            width: 160rpx;
            height: 112rpx;
            border-radius: 8rpx;
            border: 1rpx solid #D9DBE0;
            image{
              width: 100%;
              height: 100%;
            }
          }
          .item-r{
            display: flex;
            flex-direction: column;
            flex: 1;
            justify-content: center;
            margin-left: 25rpx;
            // padding: 15rpx 0;
            .item-r-head,.item-r-bott{
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2; 
              -webkit-box-orient: vertical;
              white-space: normal;
              font-size: 22rpx;
              color: #4E5569;
              line-height: 32rpx;
            }
            .item-r-head{
              margin-bottom: 8rpx;
              font-weight: 600;
              font-size: 28rpx;
              color: #2D2F38;
              line-height: 40rpx;
            }
          }
          &:last-child{
            margin-bottom: 0;
          }
        }
      }
    }
  }
</style>