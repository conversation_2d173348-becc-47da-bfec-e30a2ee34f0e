<template>
  <view class="bg" :style="'background-image: url('+ posterPath +');'">
    <button class="btn" @tap="jumpApp">点击领取</button>
  </view>
</template>

<script>
import env from '@/config/env'
export default {
  data() {
    return {
      posterPath: '',
      appId: '',
      path: ''
    }
  },
  onLoad(query) {
    if (!this.$validate.isNull(this.$Route.query)) {
      query = this.$Route.query
    }
    let { appId, path, posterPath } = query
    this.appId = appId
    this.path = path ? decodeURIComponent(path) : ''
    posterPath = posterPath ? decodeURIComponent(posterPath) : ''
    this.posterPath = posterPath.indexOf('http') === -1 ? env.file_ctx + posterPath : posterPath
  },
  methods: {
    jumpApp() {
      const vm = this
      uni.navigateToMiniProgram({
        appId: this.appId,
        path: this.path,
        success(res) {
          // 打开成功
        },
        fail(err) {
          // console.log('err------------', err)
          vm.$uniPlugin.toast('跳转失败：' + JSON.stringify(err))
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.bg {
  height: 100vh;
  width: 100vw;
  background-size: cover;
  background-position: center;
  .btn {
    position: fixed;
    bottom: 5%;
    left: 50%;
    transform: translateX(-50%);
    display: inline-block;
    width: 80%;
    height: 80rpx;
    line-height: 80rpx;
    font-size: 32rpx;
    text-align: center;
    border: 2rpx solid $topicC;
    color: #fff;
    background: $topicC;
    @include rounded(18rpx);
    margin-left: 12rpx;
    &:active {
      opacity: 0.9;
    }
  }
}
</style>