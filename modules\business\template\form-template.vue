<template>
  <view>
    <view class="template-content">
      <template v-for="(item, index) in config">
        <!--<slot v-if="item.slot" :name="item.name"></slot>-->
        <view>
          <!--类型：1-单选题(radio)，2-多选题(checkbox)，3-文本题，4-数值题(slider)，5-文件上传(file)，6-共用题干题（嵌套题），7-手机号码，8-邮寄地址-->
          <!--文本题类型：1-单行输入框(input)，2-日期，3-时间，4-日期时间，5-多行输入框(textarea)-->
          <template v-if="item.type === 'radio'">
            <title-radio :disabled="item.disabled" :config="item" :cData="regForm[item.name]"
                         @updateForm="updateForm"></title-radio>
          </template>
          <template v-if="item.type === 'checkbox'">
            <title-checkbox :disabled="item.disabled" :config="item" :cData="regForm[item.name]"
                            @updateForm="updateForm"></title-checkbox>
          </template>
          <template v-if="item.type === 'slider'">
            <title-slider :disabled="item.disabled" :config="item" :cData="regForm[item.name]"
                          @updateForm="updateForm"></title-slider>
          </template>
          <template v-if="item.type === 'file'">
            <title-lvfile :config="item" :attachmentList="regForm[item.name]"
                         @updateForm="updateForm" @returnFn='imgReturnFn'>

            </title-lvfile>
            <!-- <title-file :config="{padding: 0, background: 'none'}" :disabled="true" :cData="regForm.attachmentList"></title-file> -->
          </template>
          <template v-if="item.type === 'input'">
            <title-input :disabled="item.disabled" :config="item" :cData="regForm[item.name]"
                         @updateForm="updateForm"></title-input>
          </template>
          <template v-if="item.type === 'textarea'">
            <title-textarea :disabled="item.disabled" :config="item" :cData="regForm[item.name]"
                            @updateForm="updateForm"></title-textarea>
          </template>
          <template v-if="item.type === 'phone'">
            <title-phone :disabled="item.disabled" :config="item" :cData="regForm[item.name]"
                         @updateForm="updateForm"></title-phone>
          </template>
          <template v-if="item.type === 'address'">
            <title-address :disabled="item.disabled" :config="item" :cData="regForm[item.name]"
                            @updateForm="updateForm"></title-address>
          </template>
           <template v-if="item.type === 'lvSlider'">
             <!-- lvSlider -->
              <title-lvsliver   :config='item' :cdata="regForm[item.name]" :onlykey='item.name' @update='updateValue'></title-lvsliver>
            </template>
                <!-- 我是公干题 -->
            <template v-if="item.type === 'lvQuestion'">
              <!-- :cdata="regForm[item.name]" -->
                <titleLvquestion @returnFn='imgReturnFn' :config='item' @updateForm='updateForm' @updateValue='updateValue2' :onlykey='item.name'></titleLvquestion>
            </template>
        </view>
      </template>
    </view>
    <view class="template-footer">
      <view class="btn-bg m-tb-20-auto"
            :class="{
                'b-btn-color': isSubmit,
              }"
            @tap="submit()" >{{ !isSubmit ? "确认提交" : "已提交" }}</view>
    </view>
  </view>
</template>

<script>
import TitleRadio from '@/modules/business/template/components/title-radio/index'
import TitleCheckbox from '@/modules/business/template/components/title-checkbox/index'
import TitleInput from "@/modules/business/template/components/title-input/index";
import TitleTextarea from "@/modules/business/template/components/title-textarea/index";
import TitleSlider from "@/modules/business/template/components/title-slider/index";
import TitleFile from "@/modules/business/template/components/title-file/index";
import TitlePhone from "@/modules/business/template/components/title-phone/index";
import TitleAddress from "@/modules/business/template/components/title-address/index";
import titleLvsliver from '@/modules/business/template/components/title-lvsliver/index.vue'
import titleLvquestion from '@/modules/business/template/components/title-lvquestion/index.vue'
import titleLvfile from '@/modules/business/template/components/title-lvfile/index.vue'

export default {
  name: "form-template",
  components: {
    TitleInput,
    TitleRadio,
    TitleCheckbox,
    TitleTextarea,
    TitleSlider,
    TitleFile,
    TitlePhone,
    TitleAddress,
    titleLvsliver,
    titleLvquestion,
    titleLvfile
  },
  props: {
    // 参数设置
    templateData: {
      type: Array,
      required: true,
      default: () => {
        return []
      }
    },
    // 业务主键
    businessId: {
      type: [String, Number],
      required: true,
      default() {
        return ''
      }
    },
  },
  data() {
    return {
       barHeight: 3,
            blockSize: 26,
            backgroundColor: '#EEEEF6',
            slider1: {
              min: 50,
              max: 200,
              step: 10,
              rangeValue: [50, 150],
            },
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $timePlugin: this.$timePlugin,

      isSubmit: false,//是否提交
      regForm: {

          // isNotTo: 1,
          // isNotTo1: '',
          // isNotTo2: '',
          // isNotTo3: '',
          // isNotTo4: ''

      },
      config: [
        // {
        //   label: '1、为什么天空是蓝色，请回答！！',
        //   name: 'isNotTo',
        //   required: true,
        //   dicKey: 'deductType',
        //   array: [
        //     {
        //       key: 1,
        //       value: '不扣'
        //     },
        //     {
        //       key: 2,
        //       value: '扣'
        //     }
        //   ],
        //   type: 'radio'
        // },
        // {
        //   label: '1、为什么天空是蓝色，请回答！！',
        //   name: 'isNotTo1',
        //   required: true,
        //   dicKey: 'deductType',
        //   array: [
        //     {
        //       key: 1,
        //       value: '不扣'
        //     },
        //     {
        //       key: 2,
        //       value: '扣'
        //     }
        //   ],
        //   type: 'checkbox'
        // },
        // {
        //   label: '1、为什么天空是蓝色，请回答！！',
        //   name: 'isNotTo2',
        //   required: true,
        //   type: 'slider'
        // },
        // {
        //   label: '1、为什么天空是蓝色，请回答！！',
        //   name: 'isNotTo3',
        //   required: true,
        //   type: 'input'
        // },
        // {
        //   label: '1、为什么天空是蓝色，请回答！！',
        //   name: 'isNotTo4',
        //   required: true,
        //   type: 'textarea'
        // },

      ],
      formItem:{

      }
    }
  },
  mounted() {
    this.initi()
  },
  watch: {
    templateData: {
      handler(val) {
        this.templateData = val
        this.initi()
      },
      deep: true
    },
  },
  methods: {
    updateValue2(obj){
      this.regForm[obj.onlykey2].child[obj.onlykey] = obj.value
    },
    updateValue(obj = {}){
      // console.log("kval")
      // console.log(obj)

      // if(obj.lvchild){

      //   if(that.regForm[obj.onlykey].child){

      //   }
      //   console.log(obj.key)
      //   console.log(that.regForm[obj.onlykey])
      // }else{

      // }
      this.regForm[obj.onlykey] = obj.value;
    },
     format1(val) {
          return val
        },
        // format2(val) {
        //   return `${val}%`
        // },
        handleRangeChange(e) {
          this.rangeValue = e
        },

    initi(){
      const that = this
      // debugger
      that.config = []
      that.regForm = {}
      if (this.templateData.length > 0) {
        for (let item1 of that.templateData) {
          //模板类型:1-文档模板，2-表单模板
          if (item1.businessTemplate.type === 2) {
            that.formItem = item1
            break
          }
        }
      }
      // console.log(that.formItem)
      if (!that.$validate.isNull(this.formItem.formTemplates)) {
          console.log('kkk001')
        for (let item of this.formItem.formTemplates) {
          // debugger
          let ff = {
            label: item.order + '、' + item.title,
            name: item.id,
            required: item.mandatoryStatus === 1,
            templateId: item.id,
            disabled: that.formItem.businessTemplate.filled,
            openStatus: item.openStatus,
            unitDesc: item.unitDesc
          }
          that.isSubmit = that.formItem.businessTemplate.filled
          // debugger

          ff['type'] = ''
          console.log(item.type)
          //   <!--类型：1-单选题(radio)，2-多选题(checkbox)，3-文本题，4-数值题(slider)，5-文件上传(file)，6-共用题干题（嵌套题）9 滑块选择-->
          //   <!--文本题类型：1-单行输入框(input)，2-日期，3-时间，4-日期时间，5-多行输入框(textarea)-->
          switch (item.type) {
            case 1:
              ff['type'] = 'radio'
              ff['array'] = that.getFormTemplateOptions(item)
              if (item.optionValue.length > 0){
                that.regForm[item.id] = [{
                  id: item.optionIds,
                  value: item.optionValue,
                  label: '其他',
                  openStatus: 1
                }]
              }
              break;
            case 2:
              // debugger
              ff['type'] = 'checkbox'
              ff['array'] = that.getFormTemplateOptions(item)

              let optionIds = []
              let optionValue = []
              if (item.optionIds.length > 0 ) {
                optionIds = item.optionIds.split(",")
                optionValue = item.optionValue.split(",")
              }

              // debugger
              let optionList = []
              // debugger
              for (let i = 0; i < optionIds.length; i++) {
                const id = ['null','undefined'].includes(optionIds[i]) ? '' : optionIds[i]
                if(item.openStatus === 1 && !id) {
                  optionList.push({
                    id,
                    value: optionValue[i],
                    label: '其他',
                    openStatus: 1
                  })
                }else {
                  optionList.push({
                    id,
                    value: optionValue[i]
                  })
                }
              }
              // debugger
              that.regForm[item.id] = optionList
              break;
            case 3:
              if (item.textType === 1){
                ff['type'] = 'input'
              } else if (item.textType === 5){
                ff['type'] = 'textarea'
              }
              that.regForm[item.id] = ''
              that.regForm[item.id] = item.optionValue
              break;
            case 4:
              ff['type'] = 'slider'
              ff['numberScope'] = item.numberScope.split("-")
              that.regForm[item.id] = ''
              that.regForm[item.id] = item.optionValue
              break;
            case 5:
              ff['type'] = 'file'
              // if(item.op)
              console.log('item.optionValue',item.optionValue)
              let arr = item.optionValue ? item.optionValue.split(',') : [];

              let temp = [];
              console.log(arr)
              for(let i=0;i<arr.length;i++){
                temp.push({
                  dir:arr[i],
                  url:that.file_ctx + arr[i]
                })
              }
              console.log('arr',temp)
              that.regForm[item.id] = temp;
              break;
            case 7:
              ff['type'] = 'phone'
              that.regForm[item.id] = item.optionValue
              break;
            case 8:
              ff['type'] = 'address'
              that.regForm[item.id] = {value: item.optionValue, extendValue: item.extendValue}
              break;
            case 9:
              ff['type'] = 'lvSlider';
              ff['config'] = item.numberScope && item.numberScope != '' ? JSON.parse(item.numberScope) : {};
              this.regForm[item.id] = item.optionValue;
              break;
            case 6:
              ff['type'] = 'lvQuestion';
              this.regForm[item.id] = {
                  value:item.optionValue,
                  child:{

                  }
              };

              ff['config'] = this.initform(item.childFormTemplates,item.order,item.id);
              break;


          }
          that.config.push(ff)
        }
        console.log("=========")
        console.log(that.config)
        console.log(that.regForm)
      }
    },
    initform(arr = [],order,idd){
      const regForm = {};
      const config = [];
      const that = this;
      for (let item of arr) {
        // debugger
        let ff = {
          label: order + '.' + (item.order + 1) + '、' + item.title,
          name: item.id,
          required: item.mandatoryStatus === 1,
          templateId: item.id,
          disabled: that.formItem.businessTemplate.filled,
          openStatus: item.openStatus,
          unitDesc:item.unitDesc
        }
        // that.isSubmit = that.formItem.businessTemplate.filled
        // debugger

        ff['type'] = ''
        console.log(item.type)
        //   <!--类型：1-单选题(radio)，2-多选题(checkbox)，3-文本题，4-数值题(slider)，5-文件上传(file)，6-共用题干题（嵌套题）9 滑块选择-->
        //   <!--文本题类型：1-单行输入框(input)，2-日期，3-时间，4-日期时间，5-多行输入框(textarea)-->
        switch (item.type) {
          case 1:
            ff['type'] = 'radio'
            ff['array'] = that.getFormTemplateOptions(item)
            if (item.optionValue.length > 0){
              regForm[item.id] = [{
                id: item.optionIds,
                value: item.optionValue,
                label: '其他',
                openStatus: 1
              }]
            }
            break;
          case 2:
            // debugger
            ff['type'] = 'checkbox'
            ff['array'] = that.getFormTemplateOptions(item)

            let optionIds = []
            let optionValue = []
            if (item.optionIds.length > 0 ) {
              optionIds = item.optionIds.split(",")
              optionValue = item.optionValue.split(",")
            }

            // debugger
            let optionList = []
            // debugger
            for (let i = 0; i < optionIds.length; i++) {
              const id = ['null','undefined'].includes(optionIds[i]) ? '' : optionIds[i]
              if(item.openStatus === 1 && !id) {
                optionList.push({
                  id,
                  value: optionValue[i],
                  label: '其他',
                  openStatus: 1
                })
              }else {
                optionList.push({
                  id,
                  value: optionValue[i]
                })
              }
            }
            // debugger
            regForm[item.id] = optionList
            break;
          case 3:
            if (item.textType === 1){
              ff['type'] = 'input'
            } else if (item.textType === 5){
              ff['type'] = 'textarea'
            }
            regForm[item.id] = ''
            regForm[item.id] = item.optionValue
            break;
          case 4:
            ff['type'] = 'slider'
            ff['numberScope'] = item.numberScope.split("-")
            regForm[item.id] = ''
            regForm[item.id] = item.optionValue
            break;
          case 5:
            ff['type'] = 'file'
            let arr = item.optionValue ? item.optionValue.split(',') : [];

            let temp = [];
            console.log(arr)
            for(let i=0;i<arr.length;i++){
              temp.push({
                dir:arr[i],
                url:that.file_ctx + arr[i]
              })
            }
            console.log('arr',temp)
            regForm[item.id] = temp
            that.regForm[idd].child[item.id] = temp;
            // regForm[item.id] = '模拟值'
            // that.regForm[idd].child[item.id] = '模拟值'
            break;
          case 7:
            ff['type'] = 'phone'
            regForm[item.id] = item.optionValue
            break;
          case 8:
            ff['type'] = 'address'
            regForm[item.id] = {value: item.optionValue, extendValue: item.extendValue}
            break;
          case 9:
            ff['type'] = 'lvSlider';
            ff['config'] = item.numberScope && item.numberScope != '' ? JSON.parse(item.numberScope) : {};
            regForm[item.id] = item.optionValue;
            break;

        }
        config.push(ff)
      }

      return {
        regForm,
        config,
      }

    },
    /**
     * 解析选项数组
     */
    getFormTemplateOptions(item){
      const { formTemplateOptions,openStatus } = item
      let array = []
      if (formTemplateOptions.length > 0){
        for (let itemOption of formTemplateOptions) {
          let o = {
            id: itemOption.id,
            value: itemOption.value
          }
          array.push(o)
        }

        // 预览时 选项其他在模板选项中 编辑时不存在模板选项中
        if(openStatus === 1) {
          array.push({
            id: '',
            label: '其他',
            value: '',
            openStatus: 1
          })
        }
      }
      return array
    },
    imgReturnFn(obj) {
      console.log(obj)
      if(obj.child){
        // this.regForm[obj.name] = obj.arr;
        if(this.regForm[obj.onlykey].child){
          this.regForm[obj.onlykey].child[obj.name] = obj.arr
        }
      }else{
        this.regForm[obj.name] = obj.arr;
      }
      console.log(this.regForm)
      // this.regForm.attachmentList = v;

    },
    updateForm(obj) {
      const that = this
      console.log(obj)
      if(obj.lvchild){
        obj.key = obj.key.trim()
        if(that.regForm[obj.onlykey].child){
          that.regForm[obj.onlykey].child[obj.key] = obj.value
        }
        console.log(obj.key)
        console.log(that.regForm[obj.onlykey])
      }else{
        obj.key = obj.key.trim()
        that.regForm[obj.key] = obj.value
      }

    },
    // 验证公干题
    validPublic(that,order,param,onlykey){
      console.log(this.regForm)
      console.log(this.regForm[onlykey].child)

      const tipArr = {}
      for (let j = 0; j < that.config.length; j++) {
        let item = that.config[j]

        if (item.required) {
          tipArr[item.name] = '请填写序号为' + (order + 1) + '.' + (j + 1) + '的题目'
        }
      }
      console.log(that.config)
      console.log(that.regForm)
      if (!this.$common.validationForm(tipArr, this.regForm[onlykey].child)) {
        return 'novalid'
      }

      for (let i = 0; i < that.config.length; i++) {
        // debugger
        let item = that.config[i]
        // const rf = that.regForm[item.name]
        const rf = this.regForm[onlykey].child[item.name];
        if (this.$validate.judgeTypeOf(rf) === 'Array'){
          if(that.config[i].type == 'file'){
            let val = ''
            for (let i = 0; i < rf.length; i++) {
              let r = rf[i]
              if(val == ''){
                val = rf[i].dir
              }else{
                val += ',' + rf[i].dir
              }
            }
            let fv = {
              templateId: item.templateId,
              templateOptionId: '',
              value: val,
              // extendValue: r.extendValue ? JSON.stringify(r.extendValue) : ''
            }
            param.formWriteValueDTOS.push(fv)

            continue;
          }
          for (let i = 0; i < rf.length; i++) {
            let r = rf[i]
            let fv = {
              templateId: item.templateId,
              templateOptionId: r.id,
              value: r.value,
              extendValue: r.extendValue ? JSON.stringify(r.extendValue) : ''
            }
            param.formWriteValueDTOS.push(fv)
          }
        } else {
          // 非
          let fv = {
            templateId: item.templateId,
            templateOptionId: '',
            value: rf,
          }
          param.formWriteValueDTOS.push(fv)
        }
        // console.log(param.formWriteValueDTOS)

        // console.log("that.regForm[item.name]",that.regForm[item.name])
      }



      console.log(param)
       return param;
    },
    submit() {
      console.log(this.regForm)
      // return;
      if (this.isSubmit) {
        this.$uniPlugin.toast("已提交")
        return
      }
      const that = this
      const tipArr = {}
      for (let j = 0; j < that.config.length; j++) {
        let item = that.config[j]
        if (item.required) {
          tipArr[item.name] = '请填写序号为' + (j + 1) + '的题目'
        }
      }
      // debugger
      // 表单验证
      // debugger
      if (!that.$common.validationForm(tipArr, that.regForm)) {
        return
      }
      var param = {
        activityId: that.businessId,
        // "formWriteValueDTOS": [
        //   {
        //     "templateId": 0,
        //     "templateOptionId": 0,
        //     "value": ""
        //   }
        // ],
        mainId: this.formItem.businessTemplate.id
      }
      param.formWriteValueDTOS = []
      console.log(that.regForm)

      // return;
      // debugger
      for (let i = 0; i < that.config.length; i++) {
        // debugger
        let item = that.config[i]

        if(item.type == 'lvQuestion'){
          // let fv = {
          //   templateId: item.templateId,
          //   templateOptionId: '',
          //   value: '我是集合题',
          // }
          // param.formWriteValueDTOS.push(fv)
          // item.config.ruleForm =
          param = this.validPublic(item.config,i,param,item.name)
          if(param == 'novalid'){
            return;
          }

          continue;
        }
        const rf = that.regForm[item.name]
        if (this.$validate.judgeTypeOf(rf) === 'Array'){
          if(that.config[i].type == 'file'){
            let val = ''
            for (let i = 0; i < rf.length; i++) {
              let r = rf[i]
              if(val == ''){
                val = rf[i].dir
              }else{
                val += ',' + rf[i].dir
              }
            }
            let fv = {
              templateId: item.templateId,
              templateOptionId: '',
              value: val,
              // extendValue: r.extendValue ? JSON.stringify(r.extendValue) : ''
            }
            param.formWriteValueDTOS.push(fv)

            continue;
          }
          for (let i = 0; i < rf.length; i++) {
            let r = rf[i]
            let fv = {
              templateId: item.templateId,
              templateOptionId: r.id,
              value: r.value,
              extendValue: r.extendValue ? JSON.stringify(r.extendValue) : ''
            }
            param.formWriteValueDTOS.push(fv)
          }
        } else {
          // 非
          let fv = {
            templateId: item.templateId,
            templateOptionId: '',
            value: rf,
          }
          param.formWriteValueDTOS.push(fv)
        }
        console.log(param.formWriteValueDTOS)



        // console.log("that.regForm[item.name]",that.regForm[item.name])
      }
      console.log(param)
      // return
      // return
      that.$emit('returnFn', param)
      console.log(param)
    }
  }
}
</script>

<style lang="scss" scoped>
.m-tb-20-auto {
  margin: 40upx 0 70upx 0;
}

.template-content {
  height: 100%
}
.b-btn-color{
  background: #f56c6c;
}

</style>
