// 移动项目总入口
import commonIndex from './common/index'
// import activityIndex from './activity/index'
// import MyWalletIndex from './my-wallet/index'
import distribution from './distribution/index'
import providerManagementIndex from './provider-management/index'
const objectRouter = {
    commonIndex: commonIndex,
    // activityIndex:activityIndex,
    // MyWalletIndex:MyWalletIndex,
    distribution,
    providerManagementIndex
}

const routerList = []
for (const a in objectRouter) {
    if (Array.isArray(objectRouter[a])) {
        routerList.push(...objectRouter[a])
    } else {
        for (const b in objectRouter[a]) {
            routerList.push(objectRouter[a][b])
        }
    }
}

export default routerList
