<template>
  <view
    class="comment-submit-box"
    :style="{
      display: submit ? 'flex' : 'none',
    }"
    @click="closeInput"
  >
    <!-- 下边的click.stop.prevent用于让上边的click不传下去，以防点到下边的空白处触发closeInput方法 -->
    <view
      class="comment-add"
      @click.stop.prevent="stopPrevent"
      :style="'bottom:' + KeyboardHeight + 'px'"
    >
      <textarea
        class="textarea"
        v-model="commentReq.content"
        :placeholder="placeholder ? placeholder : '回复楼主'"
        :adjust-position="false"
        :show-confirm-bar="false"
        @blur="blur"
        @focus="focusOn"
        :focus="focus"
        @input="onInput"
        maxlength="250"
        hold-keyboard
      ></textarea>

      <template v-if="!$validate.isNull(sendImgages)">
        <view
          v-for="(e, eIndex) in sendImgages.map((item) => item.dir)"
          :key="eIndex"
          class="send-img-box"
        >
          <view class="send-img-remove" @click.stop="sendImgages = []"></view>
          <image
            @click="previewImage(sendImgages, eIndex)"
            :src="file_ctx + e"
            mode="aspectFill"
            class="send-img"
          ></image>
        </view>
      </template>
      <view class="comment-add-bottom">
        <view class="d-t">
          <image
            @tap="clickSendImg"
            :src="file_ctx + 'static/image/business/hulu-v2/icon-image.png'"
            mode="aspectFill"
            class="select-icon"
          ></image>
          <image
            @click="showEmoji"
            :src="file_ctx + 'static/image/business/hulu-v2/icon-emoji.png'"
            mode="aspectFill"
            class="select-icon"
          ></image>
        </view>
        <title-img
          disabled
          ref="sendImgRef"
          :config="config.img"
          @uploadFinish="sendImg"
        ></title-img>
        <view class="comment-add-bottom-right">
          <button
            @click="handleRelease"
            class="release-btn"
            type="default"
            size="mini"
            :disabled="!commentReq.content && $validate.isNull(sendImgages)"
            @touchend.prevent
          >
            发布
          </button>
        </view>
      </view>

      <!-- 表情包的引用 -->
      <view
        class="reply_panel_wrp"
        :style="{ height: emojiShow ? 300 + 'px' : 200 + 'px' }"
        :hidden="!emojiShow && !functionShow"
      >
        <view
          class="reply_panel"
          :class="[emojiShow ? 'show' : '']"
          :hidden="!emojiShow"
        >
          <nui-emoji
            ref="emojiRef"
            :source="emojiSource"
            class="mp-emoji"
            @insertemoji="insertEmoji"
            @delemoji="deleteEmoji"
            @send="onsend"
            :padding="0"
          ></nui-emoji>
        </view>
      </view>

      <!-- 快捷评论 -->
      <view class="comment-list">
        <scroll-refresh
          :fixed="false"
          :up="upOption"
          :down="downOption"
          :no-page="true"
          :zPageDefault="{
            loadingMoreEnabled: false,
          }"
          bgColor="transparent"
          @returnFn="returnFn"
          @scrollInit="scrollInit"
        >
          <view class="comment-item" v-for="(item, index) in commentList" :key="index">
            {{ item.desc }}
            <view class="comment-select-btn" @tap="selectComment(item)">选择</view>
          </view>
        </scroll-refresh>
      </view>
    </view>
  </view>
</template>

<script>
import NuiEmoji from './nui-emoji/nui-emoji.vue'
import UniIcons from '@/components/uni/uni-icons/uni-icons.vue'
import TitleImg from "@/components/business/module/title-img/index.vue"
export default {
  props: {
    postmessageInfo: {
      type: Object,
      default: () => ({})
    }
  },
  components: {
    NuiEmoji,
    UniIcons,
    TitleImg
  },
  data() {
    return {
      // 表情业务逻辑
      titleHeight: "45",
      statusHeight: 0,
      // 评论输入框
      keyboardHeight: 0,
      lineHeight: 60,
      functionShow: false,
      emojiShow: false,
      comment: '',
      focus: false,
      cursor: 0,
      _keyboardShow: false,
      emojiSource: this.$static_ctx + 'image/system/bg-face.png',
      parsedComment: [{
        type: 2,
        content: "[憨笑]",
        imageClass: "smiley_28"
      },
      {
        type: 1,
        content: "44"
      },
      {
        type: 2,
        content: "[呲牙]",
        imageClass: "smiley_13"
      },
      {
        type: 2,
        content: "[调皮]",
        imageClass: "smiley_12"
      }
      ],
      placeholder: "请输入评论",
      commentReq: {
        "pId": null, // 评论父id
        "content": null // 评论内容
      },
      "focus": false, // 输入框自动聚焦
      "submit": false, // 弹出评论
      "KeyboardHeight": 0, // 键盘高度
      $validate: this.$validate,
      file_ctx: this.file_ctx,
      isAnonymous: false, // 发表评论是否匿名
      replyTarget: null,
      config: {
        img: {
          count: 1,
          background: 'rgba(0,0,0,0)',
          formData: {
            groupId: "26000",
            relId: ''
          },
          showImg: false
        }
      },
      sendImgages: [],
      launchOptions: uni.getLaunchOptionsSync(),
      commentList: [],
      mescroll: null, // mescroll实例对象
      downOption: {
        auto: false // 不自动加载
      },
      upOption: {
        auto: false // 不自动加载
      },
    }
  },
  mounted: function () {
    uni.onKeyboardHeightChange(res => {
      this.KeyboardHeight = res.height
    })

    // 表情尺寸
    const systemInfo = uni.getSystemInfoSync();
    var radio = 750 / systemInfo.windowWidth;
    this.lineHeight = 50 / radio;
  },
  methods: {
    scrollInit(scroll) {
      scroll.optUp.page.num = 1
      scroll.optUp.page.size = 10
      this.mescroll = scroll
    },
    returnFn(obj) {
      const that = this
      if (!that.postmessageInfo.circleClassifyId) return obj.successCallback && obj.successCallback([])
      setTimeout(function () {
        const param = {
          circleClassifyId: that.postmessageInfo.circleClassifyId
        }
        that.$api.postmessage.sockpuppetcommenttemplateitemCommonRandom(param).then(res => {
          const data = res.data || []
          that.commentList = data
          obj.successCallback && obj.successCallback(data || [])
        })
      }, that.$constant.noun.scrollRefreshTime)
    },
    selectComment(data) {
      this.commentReq.content = data.desc
    },
    async getCommentRandomList() {
      this.mescroll.triggerDownScroll()
    },
    showEmoji() {
      this.focus = false;
      this.functionShow = false;
      this.emojiShow = this._keyboardShow || !this.emojiShow
    },
    showFunction() {
      this.functionShow = this._keyboardShow || !this.functionShow;
      this.emojiShow = false;
    },
    chooseImage() { },
    onFocus() {
      this._keyboardShow = true;

      this.hideAllPanel()
    },
    onBlur(e) {
      this._keyboardShow = false
      this.cursor = e.detail.cursor || 0
    },
    onInput(e) {
      const value = e.detail.value
      // this.comment = value
      // this.cursor = value.length
    },
    onConfirm() {
      this.onsend()
    },
    insertEmoji(evt) {
      const emotionName = evt.emotionName
      let {
        cursor,
        comment
      } = this;
      comment = this.commentReq.content || ''
      const newComment =
        comment.slice(0, cursor) + emotionName + comment.slice(cursor)

      // this.comment = newComment;
      this.commentReq.content = newComment;

      this.cursor = cursor + emotionName.length
    },
    onsend() {
      // const comment = this.comment;
      const comment = this.commentReq.content
      // console.log(comment);
      // const parsedComment = this.parseEmoji(this.comment);
      let parsedComment = this.$refs.emojiRef.parseEmoji(this.commentReq.content)
      // console.log(parsedComment)
      this.CommentArr.push({
        avator: '/image/bg.jpg',
        parsedComment: parsedComment
      })
    },
    deleteEmoji: function () {
      const pos = this.data.cursor
      const comment = this.data.comment
      let result = '',
        cursor = 0

      let emojiLen = 6
      let startPos = pos - emojiLen
      if (startPos < 0) {
        startPos = 0
        emojiLen = pos
      }
      const str = comment.slice(startPos, pos)
      const matchs = str.match(/\[([\u4e00-\u9fa5\w]+)\]$/g)
      // 删除表情
      if (matchs) {
        const rawName = matchs[0]
        const left = emojiLen - rawName.length
        if (this.emojiNames.indexOf(rawName) >= 0) {
          const replace = str.replace(rawName, '')
          result = comment.slice(0, startPos) + replace + comment.slice(pos)
          cursor = startPos + left
        }
        // 删除字符
      } else {
        let endPos = pos - 1
        if (endPos < 0) endPos = 0
        const prefix = comment.slice(0, endPos)
        const suffix = comment.slice(pos)
        result = prefix + suffix
        cursor = endPos
      }
      this.commentReq.content = result;
      // this.comment = result;
      this.cursor = cursor;

    },

    // ----------------------
    userClick(data) {
      const accountId = data.accountId
      const id = data.userId
      // console.log(data, 'data6666')
      if (data.businessType == 10 && id) {
        this.navtoGo('DoctorDetail', { id })
      } else if (data.businessType == 10 && !id) {
        this.navtoGo('PersonalHomePage', { homePageAccountId: "807651524714201090" })
      } else if (this.$common.getKeyVal('user', 'accountId') === accountId) {
        // 是否是自己
        this.$navto.pushTab('Personal', {})
      } else {
        this.navtoGo('PersonalHomePage', { homePageAccountId:accountId,isShowBtn:this.isShowBtn })
      }
    },
    navtoGo(url, obj = {}) {
      this.$navto.push(url, obj)
    },
    clickSendImg() {
      this.$refs.sendImgRef.uploadImage()
    },
    sendImg(list) {
      this.sendImgages = list
      // this.$emit('comment', {target: this.replyTarget, imagePath: list, type: 2})
    },
    // 预览图片
    previewImage(list, index) {
      uni.previewImage({
        current: index,
        urls: list.map(item => this.file_ctx + item)
      })
    },
    handleRelease() {
      this.$emit('comment', { target: this.replyTarget, content: this.commentReq.content, imagePath: this.sendImgages })
    },
    changeAnonymous(e) {
      this.isAnonymous = e.detail.value.includes('anonymous')
    },
    openComment(item) {
      item.pageCurrent += 1 
      let list = item.children.reverse()
      const lastItem = list.find(item => !item.isAddCm)
      const params = {
        lastMsgId: (this.$validate.isNull(item.children) || this.$validate.isNull(lastItem)) ? '' : lastItem.id,
        level: 2,
        ownerCommentId: item.id,
        pageSize: this.pageSize,
        pageCurrent: item.pageCurrent,
      }
      this.$emit('loadMore', params, arguments[0])
    },
    // 初始化评论
    init(cmData) {
      // 表情包内容替换
      for (var i in cmData.comment) {
        if (!cmData.comment[i].parsedComment) {
          cmData.comment[i].parsedComment = this.$refs.emojiRef.parseEmoji(cmData.comment[i].content)
          // cmData.comment[i].parsedinit = true;
        }
        if (cmData.comment[i].children.length > 0) {
          for (let j = 0; j < cmData.comment[i].children.length; j++) {
            if (!cmData.comment[i].children[j].parsedComment) {
              cmData.comment[i].children[j].parsedComment = this.$refs.emojiRef.parseEmoji(cmData.comment[i].children[j].content)
            }
          }
        }
      }
      this.commentData = cmData;
      this.commentData.comment.forEach(item=>{
        if(!item.pageCurrent){
          item.pageCurrent = 0
        } 
      })
    },
    // 没用的方法，但不要删
    stopPrevent() { },
    // 回复评论
    reply(pUser, reUser, pId, e) {
      if (this.navtoGoLogin()) return
      this.$emit('replyBefore')
      this.replyTarget = e
      this.pUser = pUser;
      this.commentReq.pId = pId;
      if (reUser) {
        // this.commentReq.content = '@' + reUser + ' ';
        this.placeholder = '回复' + reUser
      } else {
        // this.commentReq.content = '';
        this.placeholder = '回复楼主'
      }
      this.showTag = true;
      this.commentInput();
    },
    // 删除评论前确认
    confirmDelete(item) {
      var that = this;
      uni.showModal({
        title: '警告',
        content: that.deleteTip,
        confirmText: '确认删除',
        success: function (res) {
          if (res.confirm) {
            that.$emit('del', item);
          }
        }
      });
    },
    // 新增评论
    add() {
      if (this.commentReq.content == null || this.commentReq.content.length < 2) {
        uni.showToast({
          title: '评论内容过短',
          duration: 2000
        });
        return
      }
      this.$emit('add', this.commentReq);
    },
    // 点赞评论
    like(item) {
      this.$emit('like', item);
    },
    // 新增完成
    addComplete() {
      this.commentReq.content = null;
      this.tagClose();
      this.closeInput();
    },
    stopPrevent(){},
    onkeyboardHeightChange(e) {
      const {
        height
      } = e.detail
      this.keyboardHeight = height;
    },
    hideAllPanel() {
      this.functionShow = false;
      this.emojiShow = false;
    },
    // 输入框失去焦点
    blur(e) {
      this.focus = false;
      this.onBlur(e)
    },
    // 输入框聚焦
    focusOn() {
      // 表情包焦点偏移
      this.onFocus()
      this.$emit('focusOn');
    },
    // 标签关闭
    tagClose() {
      this.showTag = false;
      this.pUser = null;
      this.commentReq.pId = null;
    },
    onFocus() {
      this._keyboardShow = true;
      this.hideAllPanel()
    },
    // 输入评论
    commentInput() {
      if (this.launchOptions.scene === 1154) return this.$uniPlugin.toast('请前往小程序使用完整服务')
      this.getCommentRandomList()
      // TODO 调起键盘方法
      this.submit = true;
      setTimeout(() => {
        this.focus = true;
      }, 50)
    },
    // 关闭输入评论
    closeInput() {
      // this.clearInput()
      this.focus = false;
      this.submit = false;
    },
    // 清空输入框
    clearInput() {
      this.commentReq = {
        "pId": null, // 评论父id
        "content": null // 评论内容
      }
      this.sendImgages = []
    },
    addComment() {
      if (this.launchOptions.scene === 1154) return this.$uniPlugin.toast('请前往小程序使用完整服务')
      if (this.navtoGoLogin()) return
      this.$emit('replyBefore')
      this.placeholder = ''
      this.replyTarget = null
      this.clearInput()
      this.commentInput()
    }
  },
  destroyed() {
    uni.offKeyboardHeightChange()
  }
}
</script>

<style lang="scss" scoped>
.comment-submit-box {
  position: fixed;
  display: flex;
  align-items: flex-end;
  z-index: 9900;
  left: 0;
  top: var(--window-top);
  bottom: 0;
  background-color: rgba($color: #000000, $alpha: 0.5);
  width: 100%;
}

.comment-add {
  position: absolute;
  background-color: #ffffff;
  width: 100%;
  border: 1px solid #ddd;
  transition: 0.3s;
  -webkit-transition: 0.3s;
  padding: 24rpx;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  box-sizing: border-box;
  &-bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12rpx 0;
    &-right {
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }
  .d-t {
    display: flex;
    align-items: center;
  }
  .select-icon {
    width: 36rpx;
    height: 36rpx;
    &+.select-icon {
      margin-left: 48rpx;
    }
  }
}

.btn-click {
  color: #007aff;
  font-size: 28rpx;
  padding: 10rpx;
}

.cancel {
  color: #606266;
}

.textarea {
  height: 208rpx;
  padding: 24rpx;
  width: 100%;
  background: #F0F2F7;
  border-radius: 16rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #1D2029;
  line-height: 40rpx;
}

.comment-submit {
  padding: 5rpx 20rpx 0 20rpx;
  border-bottom: 1px dashed #ddd;
  width: calc(100% - 40rpx);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.comment-add-btn {
  position: static;
  font-weight: 500;
  font-size: 28rpx;
  color: $topicC;
  line-height: 40rpx;
  color: $topicC;
}
.more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 24rpx;
  &-text {
    font-size: 24rpx;
    font-weight: 400;
    color: $topicC;
    line-height: 34rpx;
  }
}
.comment-sub-more {
  display: flex;
  align-items: center;
  padding-top: 12rpx;
  &-text {
    font-size: 24rpx;
    font-weight: 400;
    color: #8b8b8b;
    line-height: 34rpx;
  }
}
.comment-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 12rpx;
  &-text {
    font-size: 24rpx;
    font-weight: 400;
    color: #8b8b8b;
    line-height: 34rpx;
  }
}
.mutual-comment-box {
  flex: 1;
  display: flex;
  align-items: center;
  padding-left: 12rpx;
  .reply-text {
    width: 0;
    height: 0;
    border-top: 8rpx solid transparent;
    border-left: 14rpx solid #8b8b8b;
    border-bottom: 8rpx solid transparent;
    margin: 0 24rpx;
  }
  .nick-name {
    font-size: 26rpx;
    color: #4E5569;
    line-height: 36rpx;
  }
}
.foot-source {
  font-size: 26rpx;
  color: #8b8b8b;
  span {
    background-color: #ecf5ff;
    display: inline-block;
    height: 40rpx;
    line-height: 40rpx;
    border-radius: 10rpx;
    color: #409eff;
    border: 1px solid #d9ecff;
    white-space: nowrap;
    padding: 0 10rpx;
    margin-left: 5rpx;
  }
}
.foot-time {
  font-size: 24rpx;
  color: #868C9C;
  line-height: 34rpx;
}
.content-img {
  width: 200rpx;
  height: 100%;
}
.release-btn {
  height: 60rpx;
  background-color: $topicC;
  border-radius: 30rpx;
  font-weight: 500;
  font-size: 28rpx;
  color: #FFFFFF;
  line-height: 60rpx;
  padding: 0 24rpx;
}
.nick-name-width-1 {
  display: inline-block;
  vertical-align: middle;
  @include ellipsis(1);
}
.nick-name-width-2 {
  display: inline-block;
  vertical-align: middle;
  @include ellipsis(1);
}
.zan-box {
  padding-left: 32rpx;
}
.like-icon {
  width: 26rpx;
  height: 26rpx;
}
.send-img-box {
  width: 116rpx;
  height: 116rpx;
  background-size: cover;
  background-position: center center;
  display: inline-block;
  position: relative;
  .send-img {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
  .send-img-remove {
    position: absolute;
    top: 0rpx;
    right: 0rpx;
    @include iconImg(40, 40, "/business/icon-close-black-circle.png");
    margin: 0;
    z-index: 2;
  }
}
.flex-align-center-box {
  display: flex;
  align-items: center;
}

.comment-list {
  padding: 20rpx 0;
  height: 380rpx;
  overflow: auto;
}

.comment-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: bold;
  font-size: 28rpx;
  color: #535353;
  padding: 16rpx 0;
}

.comment-select-btn {
  border-radius: 36rpx;
  border: 1px solid $topicC;
  font-weight: bold;
  font-size: 28rpx;
  color: $topicC;
  padding: 4rpx 36rpx;
  white-space: nowrap;
  margin-left: 12rpx;
}
</style>