<template>
  <view class="bgm">
    <!-- <lv-header :headerobj='headerobj' position='absolute'>
    </lv-header> -->
    <view class="integration-time-er-box">
      <integration-time-er
        :isBackIndex="true"
        :point="IntegrationData.integrationNum"
        :tracks="IntegrationData.integrationRuntimeNum"
      ></integration-time-er>
    </view>
    <template v-if="meetingDetail">
      <video-item
        :idx="canlendar_idx"
        :once="meetingDetail.once"
        :editid="meetingDetail.id"
        :initialCoverPaths="meetingDetail.coverPaths"
        :data="meetingDetail"
        :visible="meetingDetail.visible"
        :width="areaWidth"
        :height="areaHeight"
        :Theight="Theight"
        :ishidden="ishidden"
        :businessType="businessType"
        :scrollViewTop="scrollViewTop"
        :clickMoreUpdateCount="clickMoreUpdateCount"
        :hideFlag="hideFlag"
        @pre="preFn"
        @updateSwiperHeight="updateSwiperHeight"
        @hiddenInput="hiddenInput"
        @touchstart="touchstart"
        @touchend="touchend"
        @resetCommon="resetCommon"
        @changeEmojiShow="changeEmojiShow"
      ></video-item>
    </template>
    <template v-else>
      <view class="live-loading">
        <image :src="loadingUrl" class="live-img" alt="" />
      </view>
    </template>
  </view>
</template>

<script>
// import lvHeader from './components/lv-header.vue'

import { isDomainUrl } from "../../../../utils";
import videoItem from "./components/videoitem.nvue";
import { getQueryObject } from "@/utils/index";
export default {
  name: "shortVideoIndex",
  components: {
    videoItem,
    // lvHeader
  },
  onLoad(options) {
    console.log('触发listOnoad');
    
    this.loopIsDetail = true;
    this.coverPathsUrl = options.coverPathsUrl;
    const systemInfo = uni.getSystemInfoSync();
    this.$ext.wechat.getOpenId();
    const query = this.$Route.query;
    const that = this;
    if (this.$validate.isNull(query.id)) {
      let params = decodeURIComponent(query.scene);
      query.id = getQueryObject(params).id;
      if (this.$validate.isNull(query.id) && query.scene) {
        that.$uniPlugin.toast("参数异常");
        return;
      }
    }
    options = query;
    if (options && options.id) {
      this.currentid = options.id;
    }
    if (options && options.logConditionParams) {
      this.logConditionParams = JSON.parse(
        decodeURIComponent(options.logConditionParams)
      );
    }
    console.log("options", options);
    if (!this.currentid || this.currentid === '') {
      return;
    }
    this.getDevice();
  },
  onShow() {
    // 通过读取当前页面栈来判断当前是否是分享进来的页面
    this.isSharePage();
    this.hideFlag = true;
    if (!this.loopIsDetail) {
      this.currentid = null;
    }
    // 根据页面加载状态创建回调函数
    const cb = this.isOnload
      ? () => !this.loopIsDetail && this.getVideoDetail(0)
      : () => this.getVideoDetail(0);
    if (!this.currentid || this.currentid === '') {
      return;
    }
    cb();
    // this.getMeetingQueryPage({},cb);
    this.ishidden = false;
    this.isOnload = true;
  },
  beforeDestroy() {
    // console.log('beforeDestroy')
    this.ishidden = true;
  },
  onHide() {
    this.hideFlag = false;
    this.ishidden = true;
    // this.meetingviewlogvisit2();
    if (uni.$cookieCount == 1) {
      this.meetingviewlogvisit2();
    }
    // console.log('onHide')
  },
  destroyed() {
    this.ishidden = true;
    // console.log('destroyed')
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // console.log('onUnload')
    if (uni.$cookieCount == 1) {
      this.meetingviewlogvisit2();
    }
    this.ishidden = true;
  },
  watch: {
    ishidden(n) {
      if (!n) {
        
        this.getMeetingQueryOne({
          id: this.meetingDetail.id,
        });
      }
    },
  },

  data() {
    return {
      loadingUrl: this.$static_ctx + 'image/business/live/icon-live-loading.gif',
      canlendar_item2: null,
      isOnload: false,
      hideFlag: true,
      circular: true,
      logConditionParams: {},
      coverPathsUrl: "",
      emojiShow: false,
      activityStatus: null,
      scrollIntoView: "",
      commentShow: true,
      downMoreVisible: true,
      resetScrollViewVisible: true,
      resetScrollViewTop: 0,
      clickMoreLoading: false,
      clickMoreUpdateCount: 0,
      loadMoreVisible: false,
      scrollViewHeight: 0,
      scrollViewTop: 0,
      ishidden: false,
      // 要渲染到swiper-item的数组
      displayList: [],
      currentIndex: 0,
      currentRLIndex: 0,
      index: 0,
      // 直播列表
      totalList: [],

      duration: 200,
      currentItem: null,
      currentid: null,
      areaWidth: "100vw",
      areaHeight: "100vh",
      Theight: "100vh",
      businessType: 7,
      loopIsDetail: true,
      meetingDetail: null,
      swiperRunOptions: {
        onLoadSize: -1, // 加载直播资源数量
        maxDisplayItems: 3, // 当前最多维护的直播资源
        circular: true, //是否可衔接
      },
    };
  },
  // 设置分享朋友圈
  onShareTimeline() {
    return {
      title: this.meetingDetail.title,
      // path: this.share.path,
      path: "/modules/directseeding/shortVideo/list/index?id=" + this.currentid,
      imageUrl: isDomainUrl(this.meetingDetail.coverPaths),
      // desc: this.share.desc,
      // content: this.share.content
    };
  },
  // 设置分享微信好友
  onShareAppMessage(res) {
    // if (res.from === 'button') {
    //   // 来自页面内分享按钮
    //   console.log(res.target);
    // }
    return {
      title: this.meetingDetail.title,
      // path: this.share.path,
      path: "/modules/directseeding/shortVideo/list/index?id=" + this.currentid,
      imageUrl: this.meetingDetail.shareImg
        ? isDomainUrl(this.meetingDetail.shareImg)
        : isDomainUrl(this.meetingDetail.coverPaths),
      // desc: this.share.desc,
      // content: this.share.content
    };
  },

  methods: {
    // 处理分享页面视频轮播情况
    checkShareVideo() {
      console.log("isSharePage当前页面是分享页面");
      this.swiperRunOptions.circular = false;
      this.swiperRunOptions.onLoadSize = 10;
      this.swiperRunOptions.maxDisplayItems = 10;
    },
    isSharePage() {
      let pages = getCurrentPages();
      if (pages.length === 1) return this.checkShareVideo();
      // 上一页
      let previousPage = pages[pages.length - 2];
      if (previousPage.route !== "modules/directseeding/video-list/index") return this.checkShareVideo();
      console.log("isSharePage当前页面是正常页面");
    },
    changeEmojiShow({ visible }) {
      this.emojiShow = visible;
    },
    resetCommon() {
      this.scrollIntoView = "top-comment";
      setTimeout(() => {
        this.scrollIntoView = "";
      }, 300);
    },
    touchstart() {
      this.downMoreVisible = false;
    },
    touchend() {
      setTimeout(() => {
        this.downMoreVisible = true;
      }, 400);
    },
    hiddenInput(e) {
      // console.log('hiddenInput',e)
      if (this.meetingDetail.id === e.uuid) {
        this.commentShow = e.commentShow;
      }
    },
    loadMoreChat() {
      if (this.clickMoreLoading) {
        return;
      }
      this.clickMoreLoading = true;
      this.clickMoreUpdateCount += 1;
    },
    updateSwiperHeight(obj) {
      // console.log('updateSwiperHeight',obj)
      if (this.meetingDetail.id !== obj.uuid) {
        return;
      }
      this.scrollViewHeight = obj.height;
      this.loadMoreVisible = obj.loadMoreVisible;
      this.$nextTick(() => {
        this.clickMoreLoading = false;
      });
    },
    scrollOverlayFn(e) {
      // console.log('scrollOverlayFn',this.scrollViewTop)
      this.scrollViewTop = e.detail.scrollTop;
    },
    nextLiveEnd() {
      let id = this.meetingDetail.id;
      this.$navto.push("liveComputeEndPage", {
        id: id,
      });
    },
    // 查找是否有正在播放的项目
    findPlayItem(arr) {
      // console.log('arr',arr)
      // console.log('this.$playingMeetingId',uni.$playingMeetingId)
      // 存在播放项目
      if (uni.$playingMeetingId) {
        for (let i = 0; i < arr.length; i++) {
          if (arr[i].id == uni.$playingMeetingId) {
            uni.$playingMeetingId = null;
            this.$api.cloudClassroom
              .meetingviewlogvisit({
                mainId: arr[i].id,
                source: 2,
                // cookieId: this.$common.getTokenUuid()
                cookieId: this.$common.getCookieUuid(
                  arr[i].id,
                  this.$api.cloudClassroom.meetingviewlogvisit
                ),
              })
              .then((ret) => {});

            return;
          }
        }
      }
    },
    meetingviewlogvisit2() {
      console.log("meetingviewlogvisit2");
      this.$api.cloudClassroom
        .meetingviewlogvisit({
          mainId: this.meetingDetail.id,
          source: 2,
          // cookieId: this.$common.getTokenUuid()
          cookieId: this.$common.getCookieUuid(
            this.meetingDetail.id,
            this.$api.cloudClassroom.meetingviewlogvisit
          ),
        })
        .then((ret) => {});
    },
    preFn() {
      this.ishidden = true;
    },
    // 刷新对应直播详情
    getVideoDetail(timer = 2000) {
      setTimeout(() => {
        this.getMeetingQueryOne({
          id: this.meetingDetail ? this.meetingDetail?.id : this.currentid,
        });
      }, timer);
    },
    // 进入课堂创建用户
    meetingviewusersaveuser(param = {}) {
      this.$api.cloudClassroom.meetingviewusersaveuser(param).then((res) => {
        console.log("res", res);
      });
    },
    // 获取设备宽高
    getDevice() {
      var systemInfo = uni.getSystemInfoSync();
      var areaWidth = systemInfo.windowWidth;
      var areaHeight = systemInfo.windowHeight;
      if (
        systemInfo.safeArea.bottom &&
        systemInfo.windowHeight - systemInfo.safeArea.bottom < 100
      ) {
        areaHeight = systemInfo.safeArea.bottom;
      } else {
      }

      this.areaWidth = areaWidth + "px";
      this.areaHeight = areaHeight + "px";
      this.Theight = areaHeight + "px";
    },
    startPlay(data = {}) {
      data.visible = true;
      data.once = true;
      data.ewmurl = isDomainUrl(data.qrCodePath);
      this.currentid = data.id;
      this.businessType = data.businessType;
      this.activityStatus = data.activityStatus;
      this.meetingDetail = data;
      console.log("this.startPlay=========", this.meetingDetail);
    },
    // 获取详情
    getMeetingQueryOne(param = {}) {
      this.$api.cloudClassroom
        .getMeetingQueryOne(param)
        .then((res) => {
          // 安全检查：确保响应数据存在
          if (!res || !res.data) {
            console.warn("getMeetingQueryOne: 响应数据为空");
            return;
          }
          if (!this.meetingDetail) {
              this.startPlay(res.data);
          }
          // 更新观看人数和业务状态
          this.meetingDetail.virtualViewNumber = res.data.virtualViewNumber;
          this.meetingDetail.realViewNumber = res.data.realViewNumber;
          this.businessType = res.data.businessType;
          this.activityStatus = res.data.activityStatus;
          this.meetingDetail.activityStatus = res.data.activityStatus;
          let timer = 3 * 1000;
          // 检查状态或屏幕方向是否改变，需要重新渲染
          const needRerender =
            this.meetingDetail.activityStatus !== res.data.activityStatus ||
            this.meetingDetail.screenDirectionType !==
              res.data.screenDirectionType;
          if (needRerender) {
            // 更新状态
            if (this.meetingDetail.activityStatus !== res.data.activityStatus) {
              this.meetingDetail.activityStatus = res.data.activityStatus;
            }
            if (
              this.meetingDetail.screenDirectionType !==
              res.data.screenDirectionType
            ) {
              this.meetingDetail.screenDirectionType =
                res.data.screenDirectionType;
            }
            // 重新渲染组件
            this.meetingDetail.visible = false;
            this.$nextTick(() => {
              this.meetingDetail.visible = true;
            });
          }
          // 处理直播结束
          if (this.meetingDetail.activityStatus === 4) {
            this.meetingDetail.title = res.data.title;
            this.meetingDetail.coverPaths = res.data.coverPaths;
            this.nextLiveEnd();
            this.loopIsDetail = false;
            return;
          }
          // 继续轮询
          if (!this.ishidden) {
            // console.log("调用递归", timer);
            this.getVideoDetail(timer);
          }
        })
        .catch((error) => {
          console.error("getMeetingQueryOne 请求失败:", error);
          // 错误情况下也要继续轮询，避免轮询中断
          if (!this.ishidden && this.loopIsDetail) {
            const timer = 3 * 1000;
            this.getVideoDetail(timer);
          }
        });
    },
  },
};
</script>

<style lang="scss">
.integration-time-er-box {
  position: fixed;
  top: 284rpx;
  left: 52rpx;
  color: white;
  z-index: 999999999999999;
}
.seekmore {
  font-size: 24upx;
  color: #fff;
  line-height: 1.5;
  display: flex;
  justify-content: center;
  padding: 0;
  margin: 0;
  background-color: rgba(0, 0, 0, 0.2);
  display: inline-flex;
  padding: 5upx 30rpx;
}
.seekmore::after {
  border: none;
}
.scroll-view-box {
  height: 450upx;
}
.scrollViewBox {
  display: flex;
}
.bgm {
  background: #000;
  overflow: hidden;
  height: 100vh;
  // background: skyblue;
}
.sliderbox {
  height: 100vh;
  overflow: hidden;
}
.live-loading {
  height: 100vh;
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;
  .live-img {
    width: 180upx;
    height: 180upx;

  }
}
</style>
