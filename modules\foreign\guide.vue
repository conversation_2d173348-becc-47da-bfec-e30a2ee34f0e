<template>
  <view>
    <image class='txLandingPage' :src="txLandingPage" mode="aspectFill"></image>
    <button class="btnBom" :class='className' @click="gotomini">点击了解更多></button>
  </view>
</template>

<script>
  import { mapState } from 'vuex'
  import common from '@/common/util/main'
  import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'
  export default {
    components: {
      uniNavBar
    },
    data(){
      return {
        pageOptions:[
          {
            pageImg:this.$static_ctx + "image/business/hulu-v2/txLandingPage.png",
            title:'腾讯医典-五子衍宗丸',
            className:'btnBom-1'
          },
          {
            pageImg:this.$static_ctx + "image/business/hulu-v2/txLandingPage-2.png",
            title:'腾讯医典-红卡',
            className:'btnBom-2'
          }
        ],
        txLandingPage: '',
        className:'',
        currentIndex:0
      }
    },
    computed: {
      ...mapState('user', {
        isLogin: state => state.isLogin,
        accountId: state => state.accountId,
        recordUserInfo: state => state.recordUserInfo, // 当前登录用户信息
        codeUserInfo: state => state.codeUserInfo,
      }),
      ...mapState('system', {
        isShowSubscribePanel: state => state.isShowSubscribePanel
      })
    },
    async onLoad(res){
      let currentOptions = this.pageOptions[+res.currentIndex];
      this.currentIndex = res.currentIndex;
      console.log('currentOptions',currentOptions);
      this.txLandingPage = currentOptions.pageImg;
      uni.setNavigationBarTitle({title:currentOptions.title});
      this.className = currentOptions.className;
      // 开启曝光量记录
      this.$api.drugBook.pageexposurerecordInsert({
        ...(await this.getOptions()),
      })
    },
    methods:{
      gotomini(){
        this.lacktap()
        uni.navigateTo({
          url:'modules/foreign/target?currentIndex=' + this.currentIndex
        })
      },

      async getOptions(){
        let openId = await this.$ext.wechat.getOpenId();
        let accountId = this.accountId;
        return {openId,accountId,imageUrl:this.txLandingPage}
      },
      lacktap(){
        let userId = this.codeUserInfo.id;
        let accountId = common.getKeyVal('user', 'accountId', true);
        let openId = this.$common.getCache('openId');
        this.$api.activity.insertiIageoperationrecord({
          userId,accountId,openId,imageUrl:this.txLandingPage,operatingType:1
        })
      },
    }
  }
</script>

<style lang="scss">
  .txLandingPage{
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
  }
  .btnBom{
    border-radius: 59rpx;
    width: 615rpx;
    height: 98rpx;
    line-height: 98rpx;
    border: 1rpx solid white;
    position: fixed;
    left: 50%;
    transform: translateX(-50%);
    bottom: 87rpx;
    text-align: center;
    color: white;
    font-size: 33.33rpx;
    font-weight: bold;
  }
  .btnBom-1{
    background-color: #EA7800;
  }
  .btnBom-2{
    background: linear-gradient(#F37A80, #E50446);
    bottom: 58rpx !important;
  }
</style>
