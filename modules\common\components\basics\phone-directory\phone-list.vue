<template>
	<view class="width-auto">
		<scroll-view
    class="scroll-list"
		:scroll-top="1"
		scroll-y="true"
		:scroll-with-animation="scrollAnimationOFF"
		:scroll-into-view="scrollViewId"
		:style="{height:winHeight + 'px'}"
		@scroll="handleScroll">
			<view class="phone-list" v-if="JSON.stringify(phones)!=='{}'">
				<view
        class="list-item"
				v-for="(item, key) of phones"
				:key="key"
				:id="key">
					<view class="list-item-title list-item-title-key">{{key}}</view>
<!--          <view-->
<!--            class="list-item-phone"-->
<!--            @click="handleClick"-->
<!--            hover-class="commonly-hover"-->
<!--            :hover-start-time="20"-->
<!--            :hover-stay-time="70"-->
<!--            v-for="innerItem in item"-->
<!--            :key="innerItem.id"-->
<!--            :data-name="innerItem.name"-->
<!--            :data-id="innerItem.id"-->
<!--            :data-phoneNumber="innerItem.phoneNumber"-->
<!--          >-->
					<view
            class="width-auto"
            @click="handleClick(innerItem)"
            hover-class="commonly-hover"
            :hover-start-time="20"
            :hover-stay-time="70"
            v-for="(innerItem, index) in item"
            :key="index"
          >
            <view class="communication">
              <i v-if="iconState" :class="innerItem.customChecked?'icon-yijianfankui-d-ok':'icon-xuanze'"></i>
<!--              <image v-if="imgState" mode="scaleToFill" :src="innerItem.avatar ? file_ctx + innerItem.avatar : $static_ctx + 'image/system/avatar/icon-default-avatar.png'"/>-->
              <default-img v-if="imgState" :config="config.avatar" :cData="innerItem.avatar" :cName="innerItem.name" class="role-image"/>
              <text>{{innerItem.name}}</text>
            </view>
					</view>
				</view>
			</view>
      <view class="no-data" v-if="JSON.stringify(phones)==='{}'">
        <em></em>
        <text>暂无数据</text>
      </view>
		</scroll-view>
	</view>
</template>

<script>
export default {
  name: 'PhoneList',
  props: {
    phones: Object,
    letter: String,
    scrollAnimationOFF: Boolean,
    // 控制icon是否显示
    iconState: {
      type: Boolean,
      default() {
        return true
      }
    },
    // 控制图片是否显示
    imgState: {
      type: Boolean,
      default() {
        return true
      }
    },
    // 控制单选还是多选（默认多选）
    chooseState: {
      type: Boolean,
      default() {
        return true
      }
    }
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      $validate: this.$validate,
      winHeight: 0,
      scrollTop: 0,
      letterDetails: [],
      timer: null,
      config: {
        avatar: {
          widthHeightAuto: true
        }
      }
    }
  },
  computed: {
    scrollViewId() {
      return this.letter
    }
  },
  mounted() {
    // #ifndef APP-PLUS
    this.winHeight = uni.getSystemInfoSync().windowHeight - 49.50 - (this.chooseState ? 49 : 0)
    // #endif

    // #ifdef APP-PLUS
    this.winHeight = uni.getSystemInfoSync().windowHeight - 100 - (this.chooseState ? 49 : 0)
    // #endif
  },
  methods: {
    handleClick(e) {
      // this.$emit('handleClick', e.target.dataset)
      this.$emit('handleClick', e)
    },
    handleScroll(e) {
      if (this.letterDetails.length === 0) {
        const view = uni.createSelectorQuery().selectAll('.list-item')
        view.boundingClientRect(data => {
          const top = !this.$validate.isNull(data) ? data[0].top : 0
          data.forEach((item, index) => {
            item.top = item.top - top
            item.bottom = item.bottom - top
            this.letterDetails.push({
              id: item.id,
              top: item.top,
              bottom: item.bottom
            })
          })
        }).exec()
      }

      const scrollTop = e.detail.scrollTop
      this.letterDetails.some((item, index) => {
        if (scrollTop >= item.top && scrollTop <= item.bottom - 5) {
          this.$emit('change', item.id)
          this.$emit('reset', true)
          return true
        }
      })
    }
  }

}
</script>

<style lang="scss" scoped>
.width-auto{
  width: 100%;
}
.commonly-hover{
  background-color: #eee;
}
.communication{
  padding: 0 30upx;
  background-color: #fff;
  border-bottom: 2upx solid $contentDdt;
  line-height: 120upx;
  height: 120upx;
  i{
    vertical-align: middle;
    display: inline-block;
    height: 46upx;
    width: 46upx;
    margin-right: 14upx;
  }
  .role-image{
    vertical-align: middle;
    display: inline-block;
    width: 90upx;
    height: 90upx;
    @include rounded(50%);
    margin-right: 10upx;
  }
  text{
    vertical-align: middle;
    font-size: 32upx;
    line-height: 48upx;
    display: inline-block;
    width: calc(100% - 160upx);
    color: #333;
  }
}
.scroll-list{
  flex: 1;
  /*height: 100vh;*/
  width: 100%;
  overflow-y: hidden;
}

.phone-list{
  display: flex;
  background-color: #fff;
  flex-direction:column;
  position:relative;
  width: 100%;
}

.list-item {
  width: 100%;
  display: flex;
  align-items: center;
  flex-wrap:wrap;
  background-color: #fff;
  height: 100%;

}

.list-item >.list-item-phone{
  font-weight: normal;
}

.list-item-title{
  background-color: #eee;
}

.list-item-title,.list-item-phone{
  width: 100%;
  height: 92upx;
  line-height: 92upx;
  font-size: 32upx;
  font-weight: bold;
  padding: 0 20upx;
  border-bottom: 2upx solid $contentDdt;
}
.list-item-title-key {
  background-color: $pageBg;
  color: #999;
  font-size: 24upx;
  height: 60upx;
  line-height: 60upx;
  border-bottom: none;
}
.no-data{
  width: 100%;
  text-align: center;
  margin:340upx 0 0 30upx;
  em{
    @include iconImg(300, 200, '/system/invalid/icon-no-data.png');
  }
  text{
    display: block;
    font-size: 28upx;
    margin-top: 10upx;
  }
}
</style>
