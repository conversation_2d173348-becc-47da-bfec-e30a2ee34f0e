<template>
  <view class="frame-page" :style="{height: screenHeight + 'px'}">
    <view class="uni-flex uni-column">
      <main class="content" v-if="pageNetworkStatus">
        <slot name="content"></slot>
      </main>
      <main class="content" v-if="!pageNetworkStatus">
        <div class="network-error-panel">
          <image mode="scaleToFill" :src="iconNetworkErrorPng"/>
          <div>糟糕，网络已断开了</div>
          <button type="default" @tap="submitBtn()">重新刷新</button>
        </div>

      </main>
      <!-- </view> -->
    </view>
  </view>
</template>
<!--页面框架组件-->
<script>
import { mapState } from 'vuex'
export default {
  name: 'Page',
  components: {

  },
  computed: {
    isShow() {
      if (this.headerObj) {
        return this.headerObj.isShow
      }
      return false
    },
    ...mapState('navigation', {
      headerObj: state => state.headerObj
    }),
    ...mapState('system', {
      networkStatus: state => state.networkStatus
    })
  },
  data() {
    return {
      pageNetworkStatus: true,
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      file_ctx: this.file_ctx,
      screenHeight: 0,
      pathParams: {
        redirect: '',
        redirectParams: ''
      },
      timer: undefined,
      iconNetworkErrorPng: this.file_ctx + 'static/image/business/hulu-v2/icon-network-error.png',

    }
  },
  mounted() {
    this.screenHeight = uni.getSystemInfoSync().windowHeight
    this.pageNetworkStatus = this.networkStatus
  },
  methods: {
    submitBtn() {
      if (!this.networkStatus) {
        this.$uniPlugin.toast('您的网络好像掉线了，请稍后再试')
      } else {
        this.$uniPlugin.toast('网络已连接，请稍等')
      }
      this.timer && clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.pageNetworkStatus = this.networkStatus
        this.$navto.replace(this.pathParams.redirect, this.pathParams.redirectParams)
      }, this.$constant.noun.delayedOperationTime1000)
    }
  },
  props: {
    type: {
      type: String,
      default: ''
    },
    content: {
      type: String,
      default: ''
    }
  },
  onReady() {
    const query = this.$Route?.query
    const path = this.$Route?.path
    this.pathParams.redirect = path
    this.pathParams.redirectParams = query
  }
}
</script>

<style lang="scss" scoped>
  .frame-page {
    height: 100%;
    width: 100%;
    height: 100%;
    background: $bgColor;
    .uni-flex{
      width: 100%;
      height: 100%;
      .nav-top-set{
        margin-top:88upx !important;
      }
      .content{
        background-color: #f7f7f7;
        height: 100%;
      }
    }
    .network-error-panel{
      text-align: center;
      padding-top: 40%;
      image{
        width: 340upx;
        height: 310upx;
      }
      button{
        width: 55%;
        margin-top: 60upx;
        background: #8BC34A;
        color: white;
        border: none;
      }
    }
  }
</style>
