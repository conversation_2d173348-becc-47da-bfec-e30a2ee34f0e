import request from '@/common/util/request'
import env from '@/config/env'

/**
 * 医院点评请求工具类，
 * 注意：API接口只做请求数据和格式化数据的事情，其他事务一概不处理
 */

export default {
    // 医院分页列表
    hospitalQueryPage(param){
        const url = env.ctx + 'dm/api/v1/crawlershospital/query/page'
        return request.postJson(url, param)
    },

    // 医院列表单一查询
    hospitalQueryOne(param){
        const url = env.ctx + 'dm/api/v1/crawlershospital/query/one'
        return request.get(url, param)
    },

    // 科室分页列表
    departmentsQueryPage(param){
        const url = env.ctx + 'dm/api/v1/crawlershospitaldept/query/page'
        return request.postJson(url, param)
    },

    // 科室分页列表
    // departmentsQueryPage(param){
    //     const url = env.ctx + '/dm/api/v1/crawlershospitaldept/query/page'
    //     return request.postJson(url, param)
    // },

    // 科室列表单一查询
    departmentsQueryOne(param){
        const url = env.ctx + 'dm/api/v1/crawlershospitaldept/query/one'
        return request.get(url, param)
    },

    // 医院下的科室---根据主键查询医院下的科室
    crawlershospitaldeptQuery(param){
        const url = env.ctx + 'dm/api/v1/crawlershospitaldept/hospital/dept'
        return request.postForm(url, param)
    },

    // 通过医院主键和科室主键查询其下医生
    crawlershospitaldoctor(param){
        let {hospitalId,deptId} = param
        const url = env.ctx + `dm/api/v1/crawlershospitaldoctor/hospital/dept/doctors?deptId=${deptId}&hospitalId=${hospitalId}`
        return request.postJson(url, param)
    },

    // 通过医院主键和科室主键查询其下医生
    crawlershospitaldoctorQuery(param){
        const url = env.ctx + 'dm/api/v1/crawlershospitaldoctor/hospital/dept/doctors/page'
        return request.postJson(url, param)
    },

    // 根据医生主键单一查询
    crawlershospitaldoctorQueryOne(param){
        const url = env.ctx + 'dm/api/v1/crawlershospitaldoctor/query/one'
        return request.get(url, param)
    },

    // 通过医院主键和科室主键和医生名---查询其下单个医生
    crawlershospitaldoctorQueryGetOne(param){
        const url = env.ctx + 'dm/api/v1/crawlershospitaldoctor/hospital/dept/doctors/getOne'
        return request.postForm(url, param)
    },

    // 医生分页
    crawlershospitaldoctorQueryPage(param){
      const url = env.ctx + 'dm/api/v1/crawlershospitaldoctor/query/page'
      return request.postJson(url, param)
    }

}
