<template>
  <view class="chat">
    <view class="chat-list">
        <view class="chat-item" v-for="(item, index) in list" :key="index">
            <text class="item-name">{{item.nickName}}：</text>
            <text class="item-content">{{item.content}}</text>
        </view>
    </view>
    <view class="input-wrap">
        <input class="input" type="text" placeholder="请输入内容" v-model="regForm.content" />
        <button class="btn" type="primary" size="mini" @tap="confirm" :disabled="!regForm.content && loading">{{ loading ? '发送中...' : '发送' }}</button>
    </view>
  </view>
</template>

<script>
export default {
    props: {
        mainId: String
    },
    data () {
        return {
            list: [],
            chatTimer: null, // 聊天室定时器 实时拉取聊天列表
            regForm: {
                content: ''
            },
            loading: false
        }
    },
    beforeDestroy() {
        if (this.chatTimer) {
            clearInterval(this.chatTimer)
            this.chatTimer = null
        }
    },
    created() {
        this.init()
    },
    methods: {
        init() {
            const that = this
            function getMeetingchatQueryList () {
                that.meetingchatQueryList().then(res => {
                    that.list = res.data.map(item => {
                        return {
                            ...item,
                            nickName: item.nickName.substring(0, 4) + '****' + item.nickName.substr(-3)
                        }
                    })
                })
            }
            getMeetingchatQueryList()
            // 实时拉取聊天列表
            that.chatTimer = setInterval(() => {
                getMeetingchatQueryList()
            }, 10000)
        },
        /**
         * 获取聊天室聊天列表
         */
        async meetingchatQueryList () {
            const res = await this.$api.meeting.meetingchatQueryList({ mainId: this.mainId })
            return Promise.resolve(res)
        },
        confirm() {
            const centeruserInfo = this.$common.getKeyVal('user', 'centeruserInfo', true)
            this.loading = true
            const param = {
                ...this.regForm,
                mainId: this.mainId,
                userId: centeruserInfo.id,
                phone: centeruserInfo.phone
            }
            this.$api.meeting.meetingchatInsert(param).then(() => {
                setTimeout(() => {
                    this.loading = false
                    this.regForm.content = ''
                    this.meetingchatQueryList()
                }, 500)
            }).catch(() => {
                this.loading = false
            })

        }
    }
}
</script>

<style lang="scss" scoped>
.chat {
    display: flex;
    flex-direction: column;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    .chat-list {
        flex: 1;
        overflow: auto;
        .item-name {
            color: #269be5;
            font-size: 24upx;
        }
        .item-content {
            font-size: 24upx;
            color: #878787;
        }
    }
    .input-wrap {
        display: flex;
        align-items: center;
        height: 80upx;
        width: 100%;
        bottom: 0;
        box-sizing: border-box;
        .input {
            flex: 1;
            height: 100%;
            background: #F6F6F6;
            @include rounded(32upx);
            padding-left: 22upx;
            margin-right: 24upx;
            box-sizing: border-box;
        }
    }
    .btn {
        margin-right: 20upx;
        background-color: #4cd964;
        border: none;
        height: 74upx;
        line-height: 74upx;
    }
}
</style>