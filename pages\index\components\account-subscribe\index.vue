<template>
  <view class="wrap" v-if="isShow">
    <text class="tip-text">请关注消息助手，获得实时通知服务！</text>
    <view>
      <text class="go-open" @click="navtoGo('WebHtmlView', { src: env.subscribeUrl, title: '消息订阅' })">去开启</text>
      <uni-icons class="icon-close" @tap="isShow = false" type="closeempty" size="12" color="#868c9c" />
    </view>
  </view>
</template>

<script>
import uniIcons from '@/components/uni/uni-icons/uni-icons.vue'
import env from '@/config/env/index.js'
export default {
  components: {
    uniIcons
  },
  data() {
    return {
      env,
      $static_ctx: this.$static_ctx,
      isShow: true
    }
  },
  methods: {
    init() {
      this.checkAccountSubscribeStatus().then(res => {
        this.isShow = !res
      })
    },
    /**
     * 检查用户公众号订阅状态
     * @return Promise<boolean> 是否已经订阅
     */
    async checkAccountSubscribeStatus() {
      const unionId = await this.$ext.wechat.getUnionId()
      const res = await this.$api.common.accountattentionSubscribeOrNot({
        unionId,
        wxId: 'wx0918ff821e41f5c4'
      })
      if (!this.$validate.isNull(res.data)) return Promise.resolve(true)
      return Promise.resolve(false)
    },
    navtoGo(url = '', obj = {}) {
      this.$navto.push(url, obj)
    }
  }
}
</script>

<style lang="scss" scoped>
  /* #ifdef H5 */
  .wrap{
    margin:0 32rpx;
  }
  /* #endif */
.wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #FF712B;
  box-sizing: border-box;
  height: 60rpx;
  background: #FFF1EB;
  border-radius: 16rpx;
  padding: 0 24rpx;
  @include rounded(16rpx);
  // margin-bottom: 32rpx;
  .go-open {
    height: 40rpx;
    border-radius: 8rpx;
    border: 1rpx solid #FF712B;
    padding: 0 10rpx;
    font-size: 24rpx;
    color: #FF712B;
    line-height: 40rpx;
    margin-right: 26rpx;
  }
  .tip-text {
    color: #FF712B;
    font-size: 26rpx;
    line-height: 42rpx;
  }
  .jump-icon {
    color: #FF712B;
    line-height: 42rpx;
  }
}
</style>