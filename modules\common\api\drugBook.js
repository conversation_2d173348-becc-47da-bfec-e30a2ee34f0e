import request from '@/common/util/request'
import env from '@/config/env'

/**
 * 药品说明书请求工具类，
 * 注意：API接口只做请求数据和格式化数据的事情，其他事务一概不处理
 */

export default {
  // 根据渠道链code 单一查询
  getChannelQueryOneCode(param){
    const url = env.ctx + 'dm/api/v1/eiproduct/query/one/code'
    return request.get(url, param)
  },
  // 药师问答分页列表
  getQuestionanswerPage(param){
      const url = env.ctx + 'dm/api/v1/crawlersquestionanswer/query/page'
      return request.postJson(url, param)
  },
  // 问答点赞
  addQuestionanswerLike(param){
    const url = env.ctx + 'dm/api/v1/crawlersquestionanswer/add/like'
    return request.postForm(url, param)
  },
  // 问答取消点赞
  cancelQuestionanswerLike(param){
    const url = env.ctx + 'dm/api/v1/crawlersquestionanswer/cancel/like'
    return request.postForm(url, param)
  },
  // 企业列表
  getEnterpriseQueryPage(param){
    const url = env.ctx + 'dm/api/v1/brand/query/page'
    return request.postJson(url, param)
  },
  // 产品列表
  getProductQueryPage(param){
    const url = env.ctx + 'dm/api/v1/eiproduct/query/page'
    return request.postJson(url, param)
  },
  // 企业的单一查询
  getEnterpriseQueryOne(param){
    const url = env.ctx + 'dm/api/v1/brand/query/one'
    return request.get(url, param)
  },
  // 企业的单一查询
  getEiproductQueryOne(param){
    const url = env.ctx + 'dm/api/v1/eiproduct/query/one'
    return request.get(url, param)
  },
  // 附近药店-分页列表
  getPharmacyQueryPage(param){
    // const url = env.ctx + 'dm/api/v1/pharmacy/query/page'
    const url = env.ctx + 'dm/api/v1/pharmacy/page/nearby'
    return request.postJson(url, param)
  },
  // 药店提醒-分页列表
  getPharmacyRemindQueryPage(param){
    const url = env.ctx + 'dm/api/v1/medicineremind/query/page'
    return request.postJson(url, param)
  },
  // 药店提醒-用药管理-分页列表
  getPharmacyRemindQueryDistinctPage(param){
    const url = env.ctx + 'dm/api/v1/medicineremind/query/distinct/page'
    return request.postJson(url, param)
  },
  // 用药提醒-保存数据
  pharmacyRemindInsert(param){
    const url = env.ctx + 'dm/api/v1/medicineremind/insert'
    return request.postJson(url, param)
  },
  // 用药提醒-更新数据
  pharmacyRemindUpdate(param){
    const url = env.ctx + 'dm/api/v1/medicineremind/update'
    return request.putJson(url, param)
  },
  // 用药提醒-主键单一查询
  pharmacyRemindQueryOne(param){
    const url = env.ctx + 'dm/api/v1/medicineremind/query/one'
    return request.get(url, param)
  },
  // 用药提醒-关闭计划
  pharmacyRemindDelete(param){
    const url = env.ctx + 'dm/api/v1/medicineremind/shutdown/plan'
    return request.delete(url, param)
  },
  // 用药提醒-发送消息通知
  pharmacyRemindSendMessage(param){
    const url = env.ctx + 'dm/api/v1/medicineremind/notify/switch'
    return request.postForm(url, param)
  },
  // 用药提醒-查询是否关注
  pharmacyRemindQueryAttention(param){
    const url = env.ctx + 'manage/api/v1/authFans/get/by/unionId'
    return request.postForm(url, param)
  },
  // 用药说明书-观看记录
  pharmacyQueryHistory(param){
    const url = env.ctx + 'dm/api/v1/eiproduct/visit/history'
    return request.postJson(url, param)
  },
  // 完整说明书分页列表
  getfullSpecificationQueryPage(param){
    const url = env.ctx + 'dm/api/v1/fullinstructions/query/page'
    return request.postJson(url, param)
  },
  // 完整说明书主键单一查询
  fullSpecificationQueryOne(param){
    const url = env.ctx + 'dm/api/v1/fullinstructions/query/one'
    return request.get(url, param)
  },

  // 完整说明书首页路轮播弹窗
  bannerQueryPage(param){
    const url = env.ctx + 'dm/api/v1/banner/query/page'
    return request.postJson(url, param)
  },

  // 用药指南配置入口-查询单一
  productmedicationguideQueryOne (param) {
    const url = env.ctx + 'dm/api/v1/productmedicationguide/query/one/productId'
    return request.get(url, param)
  },

  // 辟谣类型-辟谣分页列表
  refuterumortypeQueryPage (param) {
    const url = env.ctx + 'dm/api/v1/refuterumortype/query/page'
    return request.postJson(url, param)
  },
  // 辟谣类型-辟谣分页列表
  refuterumortypeQueryOne (param) {
    const url = env.ctx + 'dm/api/v1/refuterumordetail/query/one'
    return request.get(url, param)
  },
  // 每日辟谣分页列表
  refuterumordetailQueryPage (param) {
    const url = env.ctx + 'dm/api/v1/refuterumordetail/query/page'
    return request.postJson(url, param)
  },
  // 每日辟谣-获取每日辟谣题目
  refuterumordetailDayDetail (param) {
    const url = env.ctx + 'dm/api/v1/refuterumordetail/dayDetail'
    return request.get(url, param)
  },
  // 每日辟谣-辟谣订阅
  refuterumorsubscribeInsert (param) {
    const url = env.ctx + 'dm/api/v1/refuterumorsubscribe/insert'
    return request.postJson(url, param)
  },
  // 每日辟谣-新增答题记录
  refuterumorrecordInsert (param,headers = {}) {
    const url = env.ctx + 'dm/api/v1/refuterumorrecord/insert'
    return request.postJson(url, param,headers)
  },
  // 查询或者创建分享码
  queryAndCreate (param) {
    const url = env.ctx + 'dm/api/v1/usersharestatistics/queryAndCreate'
    return request.postJson(url, param)
  },
  // 首页弹窗 记录点击次数
  visitpagerecordInsert (param) {
    const url = env.ctx + 'dm/api/v1/visitpagerecord/insert'
    return request.postJson(url, param)
  },
  // 弹窗曝光量
  pageexposurerecordInsert (param) {
    const url = env.ctx + 'dm/api/v1/pageexposurerecord/insert'
    return request.postJson(url, param)
  },
  // 首页弹窗 每天首次显示
  visitpagerecordValidPop (param) {
    const url = env.ctx + 'dm/api/v1/visitpagerecord/valid/pop'
    return request.get(url, param)
  },
  // 小程序 - 根据accountId和 类型, 获取对应的弹窗
  advertisementmanagementQueryOneAccountId (param) {
    const url = env.ctx + 'dm/api/v1/advertisementmanagement/query/one/accountId'
    return request.get(url, param)
  },
  // 小程序 - 子项(按钮) - 点击记录
  advertisementmanagementitemrecordInsert (param) {
    const url = env.ctx + 'dm/api/v1/advertisementmanagementitemrecord/insert'
    return request.postJson(url, param)
  },
  // 弹窗广告 - 校验当前是否弹窗
  advertisementmanagementValidPopAccount (param) {
    const url = env.ctx + 'dm/api/v1/advertisementmanagement/valid/pop/account'
    return request.postJson(url, param)
  },
  // 小程序关闭弹窗-调用关闭
  advertisementmanagementrecordInsert (param) {
    const url = env.ctx + 'dm/api/v1/advertisementmanagementrecord/insert'
    return request.postJson(url, param)
  },
  // H5功能统计 - 按钮点击
  imageoperationrecordInsert (param) {
    const url = env.ctx + 'dm/api/v1/imageoperationrecord/insert'
    return request.postJson(url, param)
  },
  // 辟谣 - 本周排行
  refuterumorweekstatisticsQueryWeekRank (param) {
    const url = env.ctx + 'dm/api/v1/refuterumorweekstatistics/query/weekRank'
    return request.get(url, param)
  },
  // 辟谣 - 上周排行
  refuterumorweekstatisticsQueryLastWeekRank (param) {
    const url = env.ctx + 'dm/api/v1/refuterumorweekstatistics/query/lastWeekRank'
    return request.get(url, param)
  },
  // 辟谣 - 本人所在排行
  refuterumorweekstatisticsQueryRank (param) {
    const url = env.ctx + 'dm/api/v1/refuterumorweekstatistics/query/rank'
    return request.get(url, param)
  },
  // 辟谣 - 历史题目统计
  refuterumorweekstatisticsQueryHistoryStatistic (param) {
    const url = env.ctx + 'dm/api/v1/refuterumorweekstatistics/query/historyStatistic'
    return request.get(url, param)
  },
  // 辟谣 - 获取默认辟谣规则
  refuterumorweekstatisticsQueryDefault (param) {
    const url = env.ctx + 'dm/api/v1/refuterumorrule/query/default'
    return request.get(url, param)
  },
  // 辟谣 - 历史题目分页
  refuterumorrecordQueryPage (param) {
    const url = env.ctx + 'dm/api/v1/refuterumorrecord/query/page'
    return request.postJson(url, param)
  },
  // 辟谣 - 每天答题正确数
  refuterumorrecordAnswerRightNum (param) {
    const url = env.ctx + 'dm/api/v1/refuterumorrecord/answerRightNum'
    return request.get(url, param)
  },
  // 辟谣 - 每天答题数
  refuterumorrecordAnswerNum (param) {
    const url = env.ctx + 'dm/api/v1/refuterumorrecord/answerNum'
    return request.get(url, param)
  },
  // 弹窗广告 - 校验当前是否弹窗-多个
  advertisementmanagementListValidPopAccount (param) {
    const url = env.ctx + 'dm/api/v1/advertisementmanagement/list/valid/pop/account'
    return request.postJson(url, param)
  },
  // 获取每日辟谣未答题目1条
  getRefuterumordetailOne (param) {
    const url = env.ctx + 'dm/api/v1/refuterumordetail/one'
    return request.get(url, param)
  },
  //签到积分 - 签到记录列表
  getRefuterumorrecordSignRecord (param) {
    const url = env.ctx + 'dm/api/v1/refuterumorrecord/signRecord'
    return request.get(url, param)
  },
  //签到积分 - 获取辟谣签到答题记录
  getRefuterumorrecordQueryAnswerRecord (param) {
    const url = env.ctx + 'dm/api/v1/refuterumorrecord/query/answerRecord'
    return request.get(url, param)
  },
  //小葫芦客服设置 - 根据主键单一(无参数版)
  getCustomerserviceprofilesQueryNoParametersOne (param) {
    const url = env.ctx + 'dm/api/v1/customerserviceprofiles/query/no/parameters/one'
    return request.get(url, param)
  },
}
