/**
 * 系统路由
 */
export default [
  {
    // 结果
    path: '/pages/result/index',
    name: 'Result',
    meta: {
      index: 2,
      headerObj: {
        title: '',
        isShow: true
      }
    }
  },
  {
    // 首页
    path: '/pages/index/index',
    name: 'Index',
    aliasPath: '/',
    meta: {
      index: 1,
      headerObj: {
        title: '首页'
      }
    }
  },
  {
    // 首页
    path: '/pages/index/my',
    name: 'IndexMy',
    meta: {
      index: 1,
      headerObj: {
        title: ''
      }
    }
  },
  {
    // 首页-领袋
    path: '/pages/home/<USER>',
    name: 'Home',
    meta: {
      index: 1,
      headerObj: {
        title: '首页'
      }
    }
  },
  // #ifdef MP-ALIPAY
  {
    // 首页-灯火
    path: '/pages/lights/index',
    name: 'Lights',
    meta: {
      index: 1,
      headerObj: {
        title: '首页-lights'
      }
    }
  },
  {
    // 出袋进度
    path: '/pages/lights/packet-result',
    name: 'LightsPacketResult',
    meta: {
      index: 1,
      headerObj: {
        title: '灯火出袋进度'
      }
    }
  },
  // #endif
  {
    // 免费出袋
    path: '/pages/freeOut/index',
    name: 'FreeOut',
    meta: {
      index: 1,
      headerObj: {
        title: '免费领袋'
      }
    }
  },
  {
    // 出袋进度
    path: '/pages/resultLoading/index',
    name: 'ResultLoading',
    meta: {
      index: 1,
      headerObj: {
        title: '出袋进度'
      }
    }
  },
  {
      // 登录
      path: '/modules/system/login/index',
      name: 'Login',
      meta: {
          index: 1,
          headerObj: {
              title: '登录'
          }
      }
  },
  {
      // 忘记密码
      path: '/modules/system/pwd/index',
      name: 'Password',
      meta: {
          index: 1,
          headerObj: {
              title: '忘记密码'
          }
      }
  },
  {
      // 接收通知
      path: '/modules/system/receipt-inform/index',
      name: 'ReceiptInform',
      meta: {
          index: 1,
          headerObj: {
              title: '接收通知'
          }
      }
  },
  {
      // 注册
      path: '/modules/system/reg/index',
      name: 'Register',
      meta: {
          index: 1,
          headerObj: {
              title: '注册'
          }
      }
  },
  {
      // 个人中心
      path: '/pages/personal/index',
      name: 'Personal',
      meta: {
          index: 1,
          headerObj: {
              title: '我的'
          }
      }
  },
  {
      path: '/modules/system/user/index',
      name: 'User',
      meta: {
          index: 2,
          headerObj: {
              title: '',
              isShow: true
          }
      }
  },
  {
      path: '/modules/system/user-introduce/index',
      name: 'UserIntroduce',
      meta: {
          index: 2,
          headerObj: {
              title: '编辑个人介绍',
              isShow: true
          }
      }
  },
  {
      path: '/modules/system/setting/index',
      name: 'Setting',
      meta: {
          index: 2,
          headerObj: {
              title: '系统设置',
              isShow: true
          }
      }
  },
  {
      // 关于我们
      path: '/modules/system/setting/about/index',
      name: 'About',
      meta: {
          index: 2,
          headerObj: {
              title: '关于绿葆',
              isShow: true
          }
      }
  },
  {
      // 修改手机号（旧手机号页面）
      path: '/modules/system/modify-phone/index',
      name: 'ModifyPhone',
      meta: {
          index: 2,
          headerObj: {
              title: '',
              isShow: true
          }
      }
  },
  {
      // 陪诊师申请
      path: '/modules/system/application/index',
      name: 'Application',
      meta: {
          index: 2,
          headerObj: {
              title: '',
              isShow: true
          }
      }
  },
  {
      // 陪诊师报名费详情
      path: '/modules/system/application/intro',
      name: 'ApplicationIntro',
      meta: {
          index: 3,
          headerObj: {
              title: '陪诊师申请须知”',
              isShow: true
          }
      }
  },
  {
      // 就诊人档案
      path: '/modules/accompany-doctor/service-reservation/add-patient/index',
      name: 'Patient',
      meta: {
          index: 2,
          headerObj: {
              title: '',
              isShow: true
          }
      }
  },
  {
      // 修改手机号（新手机号页面）
      path: '/modules/system/modify-phone/bind-phone',
      name: 'ModifyPhoneBindPhone',
      meta: {
          index: 2,
          headerObj: {
              title: '',
              isShow: true
          }
      }
  },
  {
      // 验证码登录
      path: '/modules/system/verification-code/index',
      name: 'VerificationCode',
      meta: {
          index: 2,
          headerObj: {
              title: '',
              isShow: true
          }
      }
  },
  {
      // 意见反馈
      path: '/modules/system/feedback/index',
      name: 'Feedback',
      meta: {
          index: 2,
          headerObj: {
              title: '',
              isShow: true
          }
      }
  },
  {
      // 帮助中心
      path: '/modules/system/help-center/index',
      name: 'HelpCenter',
      meta: {
          index: 2,
          headerObj: {
              title: '',
              isShow: true
          }
      }
  },
  {
      // 帮助中心
      path: '/modules/system/my-name/index',
      name: 'MyName',
      meta: {
          index: 2,
          headerObj: {
              title: '',
              isShow: true
          }
      }
  },
  {
      // 中间页面
      path: '/modules/system/middle-page/index',
      name: 'MiddlePage',
      meta: {
          index: 2,
          headerObj: {
              title: '',
              isShow: true
          }
      }
  },
  {
      // 菜单列表
      path: '/modules/system/menu-list/index',
      name: 'SystemMenuList',
      meta: {
        index: 2,
        headerObj: {
          title: '管理我的应用',
          isShow: true
        }
      }
  },
  {
    // 下单协议
    path: '/modules/system/agreement-gather/order-agreement/index',
    name: 'OrderAgreement',
    meta: {
        index: 2,
        headerObj: {
            title: '下单协议',
            isShow: true
        }
    }
  },
  {
    // 入驻协议
    path: '/modules/system/agreement-gather/arrival-agreement/index',
    name: 'ArrivalAgreement',
    meta: {
        index: 2,
        headerObj: {
            title: '入驻协议',
            isShow: true
        }
    }
  },
  {
      // 服务协议
      path: '/modules/system/agreement-gather/user-agreement/index',
      name: 'UserAgreement',
      meta: {
          index: 2,
          headerObj: {
              title: '服务协议',
              isShow: true
          }
      }
  },
  {
    // 免责条款
    path: '/modules/system/agreement-gather/exeception-clause/index',
    name: 'ExeceptionClause',
    meta: {
      index: 2,
      headerObj: {
        title: '免责条款',
        isShow: true
      }
    }
  },
  {
      // 隐秘政策
      path: '/modules/system/agreement-gather/secrecy-policy/index',
      name: 'SecrecyPolicy',
      meta: {
          index: 2,
          headerObj: {
              title: '隐秘政策',
              isShow: true
          }
      }
  },
  {
      // 平台服务协议
      path: '/modules/system/agreement-gather/platform-agreement/index',
      name: 'PlatformAgreement',
      meta: {
          index: 2,
          headerObj: {
              title: '平台服务协议',
              isShow: true
          }
      }
  },
  {
    // 首页
    path: '/pages/web-html-view/index',
    name: 'PagesWebHtmlView',
    aliasPath: '/',
    meta: {
      index: 1,
      headerObj: {
        title: '首页'
      }
    }
  }
]
