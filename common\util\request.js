import common from '@/common/util/main'
import Request from '@/common/plugins/luch-request/request'
import uniPlugin from '@/common/util/uni-plugin'
import ext from '@/service/ext'
import navto from '@/router/config/nav-to'
const http = new Request()
import constant from '@/constant'
import validate from "./validate";
import env from '@/config/env'
import router from '@/router'
import { getQueryObject, debounce } from '@/utils/index'
import integration from  "@/integration"
const launchOptions = uni.getLaunchOptionsSync()

// 上传文件请求头的terminalType参数在不同环境下的配置
let terminalType = ''
// #ifdef H5
terminalType = constant.system.terminal.h5
// #endif
// #ifdef APP-PLUS
terminalType = constant.system.terminal.app
// #endif
// #ifdef MP-WEIXIN
terminalType = constant.system.terminal.miniProgram
// #endif
// #ifdef MP-ALIPAY
terminalType = constant.system.terminal.alipay
// #endif

/**
 * @description 修改全局默认配置
 * @param {Function}
 */
// http.setConfig((config) => { /* config 为默认全局配置*/
// 	config.baseUrl = env.ctx; /* 根域名 */
// 	return config
// })
//
/**
 * 白名单配置
 * @type {string[]}
 */
// const whiteList = [
// 	'auth/api/v2/app/login/temporary', 'home/index'
// ]

/**
 * 在请求之前拦截
 */
http.interceptor.request((config, cancel) => { /* 请求之前拦截器 */
  const networkStatus = common.getKeyVal('system', 'networkStatus')
  if (!networkStatus) {
    cancel()
  }
  const token = common.getToken()
  const tenantId = common.getKeyVal('user', 'curSelectStoreId', true)
  config.header = config.header || {}
  config.header = {
    ...config.header,
    'terminal-type': terminalType,
    'accept': 'application/json; charset=utf-8',
    'satoken': token,
    'auth-version': 'v2',
    'gb-tenant-id': (tenantId && tenantId !== 'undefined') ? tenantId : env.tenantId
  }
  // 插入积分任务记录
  integration.integrationInterceptor.invoke(config)
  return config
})

/**
 * 在请求之后拦截
 */
http.interceptor.response((res) => { /* 对响应成功做点什么 （statusCode === 200），必须return response*/
  if (res.statusCode === 200) {
    if (!getCodeMsg(res.data)) {
      return Promise.reject(res.data)
    }
  }
  // 插入积分任务记录
  setTimeout(()=>{
    integration.integrationInterceptor.success(JSON.parse(JSON.stringify(res)))
  },10)
  return res.data
}, (response) => { /*  对响应错误做点什么 （statusCode !== 200），必须return response*/
  getStatusMsg(parseInt(response.statusCode))
  return response
})

export default {

  /**
   * GET 资源请求
   * 特别用于查询操作
   * @param url
   * @param params
   * @returns {*}
   */
  get(url, params, expandHeaders = {}) {
    const headers = {
      'content-type': 'application/x-www-form-urlencoded',
      ...expandHeaders
    }
    return this.createForm(url, 'GET', headers, params)
  },
  /**
   * DELETE 资源请求
   * 特用于删除操作
   * @param url
   * @param params
   * @returns {*}
   */
  delete(url, params, expandHeaders = {}) {
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      ...expandHeaders
    }
    return this.createForm(url, 'DELETE', headers, params)
  },
  /**
   * POST 资源请求
   * 特用于保存操作，支持DTO
   * @param url
   * @param params
   * @returns {*}
   */
  postJson(url, params, expandHeaders = {}) {
    const headers = {
      'Content-Type': 'application/json; charset=utf-8',
      ...expandHeaders
    }
    params = JSON.stringify(params)
    return this.createJson(url, 'POST', headers, params)
  },
  /**
   * POST 资源请求
   * 特用于保存操作，支持FORM参数提交
   * @param url
   * @param params
   * @returns {*}
   */
  postForm(url, params, expandHeaders = {}) {
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      ...expandHeaders
    }
    return this.createForm(url, 'POST', headers, params)
  },
  /**
   * PUT 资源请求
   * 特用于保存操作，支持DTO
   * @param url
   * @param params
   * @returns {*}
   */
  putJson(url, params, expandHeaders = {}) {
    const headers = {
      'Content-Type': 'application/json; charset=UTF-8',
      ...expandHeaders
    }
    return this.createJson(url, 'PUT', headers, params)
  },
  /**
   * PUT 资源请求
   * 特用于保存操作，支持FORM参数提交
   * @param url
   * @param params
   * @returns {*}
   */
  putForm(url, params, expandHeaders = {}) {
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      ...expandHeaders
    }
    return this.createForm(url, 'PUT', headers, params)
  },
  /**
   * 原生请求 支持FORM表单参数提交
   * @param url
   * @param method
   * @param headers
   * @param params
   * @returns {Promise<any>}
   */
  createForm(url, method, headers, params) {
    headers = headers || {}
    return this.uniRequest(url, method, headers, params)
  },
  /**
   * 原生请求 支持DTO 提交
   * @param url
   * @param method
   * @param headers
   * @param params
   * @returns {Promise<any>}
   */
  createJson(url, method, headers, params) {
    return this.uniRequestBody(url, method, headers, params)
  },
  uniRequestBody(url, method, headers, params) {
    return new Promise((resolve, reject) => {
      http.request({
        method: method,
        url: url,
        data: params,
        // params: params,
        header: headers,
        dataType: 'json'
      }).then((res) => {
        if (res) {
          if (res.code === 0) {
            resolve(res)
          } else {
            reject(res)
          }
        } else {
          reject(res)
        }
      }).catch((err) => {
        reject(err)
      })
    })
  },
  uniRequest(url, method, headers, params) {
    return new Promise((resolve, reject) => {
      http.request({
        method: method,
        url: url,
        // data: params,
        params: params,
        header: headers,
        dataType: 'json'
      }).then((res) => {
        if (res) {
          if (res.code === 0) {
            resolve(res)
          } else {
            reject(res)
          }
        } else {
          reject(res)
        }
      }).catch((err) => {
        reject(err)
      })
    })
  }
}

const debounceLoginExpired = debounce(loginExpired, 1000, false)

/**
 * 获取业务code 转移提醒
 * @param code
 * @returns {boolean}
 */
function getCodeMsg(data) {
  if (data.code === 0) {
    if (data.msg === 'ok') {
      data.msg = '操作成功'
    }
    return true
  }
  if (data.code === 401) {
    debounceLoginExpired()
    return false
  }
  if (data.code === -1) {
    if (!validate.isNull(data.msg)) {
      uniPlugin.toast(data.msg)
      return false
    }
    uniPlugin.toast('系统异常！')
    return false
  } else {
    uniPlugin.toast(data.msg)
    return false
  }
}

/**
 * 登录过期主方法
 * @param code
 */
function loginExpired(code){
  return new Promise((resolve, reject) => {
    if (launchOptions.scene === 1154) return resolve()
    try {
      let pages = getCurrentPages();
      let routeUrl = ''
      console.log("401 ready")
      // #ifdef H5
      routeUrl = window.location.hash.replace('#/', '')
      routeUrl = routeUrl ? routeUrl.split('?')[0] : ''
      // #endif

      // #ifndef H5
      let page = pages[pages.length - 1];
      routeUrl = page.route
      // #endif
      routeUrl = routeUrl === '' ? '/pages/index/index' : routeUrl
      routeUrl = routeUrl.startsWith('/') ? routeUrl : '/' + routeUrl
      // if (common.getCache("isLoginOut")=== ''){
      console.log("401 in")
      // common.setCache('isLoginOut', true)
      ext.user.clearSession(async () => {
        common.setKeyVal('user', 'isLogin', false)
        common.setKeyVal('user', 'isLogin', false, true)
        const isLoginByPhone = await loginByPhone()
        if (!isLoginByPhone) {
          await ext.user.accountLogin()
        }
        // await doSomething()
        // uniPlugin.toast('登录已失效，请重新登录')
        setTimeout(()=>{
          // 获取当前路由name
          let curRoute = router.options.routes.find(item => item.path === routeUrl)

          // 所处白名单的页面登陆过期是否需要跳转登陆页面,不确定，先把视频列表加上
          if(constant.system.whiteRouterList.includes(curRoute.name)){
            // console.log('视频直播页面登陆过期不跳转登陆页面')
            // 登陆过期重新打开该页面
            let query = ''
            // #ifdef H5
            query = getQueryObject(window.location.hash)
            // #endif

            // #ifndef H5
            page = pages[pages.length - 1]
            // 参数
            query = page.options
            if (!validate.isNull(query) && !validate.isNull(query.query) && typeof(query.query) === 'string') {
              try {
                query = {
                  ...JSON.parse(query.query),
                  ...query
                }
              } catch (err) {
                console.log('重定向参数异常----------', err)
              }
            }
            // #endif
            console.log('--------------', routeUrl, query)
            // navto.replacePath(`${routeUrl}`, query);
            let paramStr = ''
            Object.keys(query).forEach(key => {
              let p = query[key]
              while(p !== decodeURIComponent(p)) {
                p = decodeURIComponent(p)
              }
              p = encodeURIComponent(p)
              paramStr += paramStr ? `&${key}=${p}` : `${key}=${p}`
            })
            uni.reLaunch({
                url: routeUrl + '?' + paramStr
            })
            resolve()
          }else{
            if (routeUrl === 'modules/business/chat/index') {
              navto.replaceAll('ChatLogin')
            } else {
              navto.replaceAll('Login')
            }
            resolve()
          }

          // common.setCache('isLoginOut', true)
        }, 0)
      })
    } catch(err) {
      reject(err)
    }
  })
}

/**
 * 根据粉丝档案绑定的手机号登录
 * 有就正常登录 没有就走accountId登录
 */
async function loginByPhone() {
  return new Promise((resolve, reject) => {
    ext.user.getCommunityFansRecord().then(() => {
      ext.user.getFansBindRecord().then(res => {
        const { phone } = res || {}
        if (!phone) return resolve(false)
        ext.wechat.quickLogin({ phone, cacheNumber: common.getTokenUuid()}).then(res => {
          resolve(true)
        }).catch(() => {
          resolve(false)
        })
      }).catch(() => {
        resolve(false)
      })
    }).catch(() => {
      resolve(false)
    })
  })
}

/**
 * 获取响应状态 转义提醒
 * @param code
 * @returns {boolean}
 */
function getStatusMsg(code) {
  switch (code) {
    case 400:
      uniPlugin.toast('请求参数异常！')
      return false
    case 401:
      debounceLoginExpired()
      return false
    case 403:
      uniPlugin.toast('您没有操作权限！')
      return false
    case 500:
      uniPlugin.toast('网络开小差啦！')
      return false
    case 503:
      uniPlugin.toast('服务未启动，请稍后重试！')
      // uniPlugin.toast('503')
      return false
    case 404:
      uniPlugin.toast('请求地址找不到！')
      // navto.push('NotFind')
      return false
    default:
      uniPlugin.toast('网络开小差啦！')
      return true
  }
}
