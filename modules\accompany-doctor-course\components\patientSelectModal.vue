<template>
  <view>
    <view class="patient-select-modal" v-if="showPatientSelectModal">
      <view class="modal-mask" @click="closePatientSelectModal"></view>
      <view class="patient-select-container">
        <view class="patient-select-header">
          <text class="patient-select-title">请选择就诊人</text>
          <view class="close-icon" @click="closePatientSelectModal">
            <image
              :src="patientCloseIcon"
              mode="aspectFit"
              class="close-icon-image"
            ></image>
          </view>
        </view>
        <!-- 搜索框 -->
        <view class="search" v-if="queryAll">
          <view class="searchBox">
            <image :src="searchIcon" mode="aspectFit" class="searchIcon"></image>
            <input type="text" placeholder="请输入姓名或手机号" class="search-input" @input="searchNull" v-model.lazy="searchText"/>
            <view class="searchBtn" @click="searchCallBack">搜索</view>
          </view>
        </view>

        <scroll-view :scroll-top="scrollTop" :show-scrollbar="false" scroll-y="true" class="patient-list" @scrolltolower="upper">
          <view
            class="patient-item"
            v-for="(item, index) in patientRecords"
            :key="index"
            @click="selectPatientItem(item)"
          >
            <view class="patient-info">
              <text class="patient-name">姓名：{{ item.name }}</text>
              <text class="patient-phone" v-if="item.phone">{{
                formatPhone(item.phone)
              }}</text>
            </view>
            <view class="edit-icon" @click.stop="goToEditPatient(item)">
              <image
                :src="patientEditIcon"
                mode="aspectFit"
                class="edit-icon-image"
              ></image>
            </view>
          </view>
        </scroll-view>

        <view class="add-btn-wrapper">
          <view class="add-patient-btn" @click="goToAddPatient">
            <text class="add-patient-btn-text">新增就诊人</text>
            <image
              :src="patientAddIcon"
              mode="aspectFit"
              class="add-icon-image"
            ></image>
          </view>
        </view>

        <view class="indicator-bar"></view>
      </view>
    </view>
    <view class="custom-modal" v-if="showCustomModal">
      <view class="modal-mask" @click="closeCustomModal"></view>
      <view class="modal-container">
        <view class="modal-title">提示</view>
        <view class="modal-content">您还没有添加就诊人，快去添加吧~</view>
        <view class="modal-btns">
          <view class="modal-btn cancel-btn" @click="closeCustomModal"
            >取消</view
          >
          <view class="modal-btn confirm-btn" @click="goToAddPatient"
            >去添加</view
          >
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import common from '@/common/util/main'
import serverOptions from '@/config/env/options'

export default {
  props: {
    queryAll:{
      type:Boolean,
      default:false
    },
    // 服务商信息
    provinceValue: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      showPatientSelectModal: false, // 控制就诊人选择弹窗的显示与隐藏
      patientRecords: [], // 存储就诊人记录的数组
      showCustomModal: false, // 控制自定义弹窗的显示与隐藏
      loadEnd:false,
      patientCloseIcon: this.$static_ctx + "image/business/hulu-v2/patient-close.png",
      patientEditIcon: this.$static_ctx + "image/business/hulu-v2/patient-edit.png",
      patientAddIcon: this.$static_ctx + "image/business/hulu-v2/patient-add.png",
      providerId: null,
      searchIcon: this.$static_ctx + "image/business/hulu-v2/searchIcon.png",
      current: 1, // 当前页码
      pageSize: 10, // 每页显示的记录数
      searchText:'', // 搜索文本
      condition:{}
    };
  },
  computed: {
    queryApiFunc() {
      return this.queryAll ? this.$api.accompanyDoctor.accompanypatientQueryPage : this.$api.accompanyDoctor.accompanypatientQueryAll
    },
    queryApiOptions() {
      return this.queryAll ? { 
        current: this.current, 
        size: this.pageSize,
        condition:{providerId:serverOptions.providerId,...this.condition}
      } : {}
    },
  },
  mounted() {
    this.getProviderId();
    // 监听页面显示事件
    uni.$on('onShow', () => {
      if (this.providerId) {
        this.loadPatientRecords();
      } else {
        this.getProviderId();
      }
      this.resetList();
      console.log('触发子组件加载',this.showCustomModal);
    });
  },
  // 组件销毁时移除监听
  beforeDestroy() {
    uni.$off('onShow');
  },
  methods: {
    // 获取服务商ID
    async getProviderId() {
      try {
        // 直接从全局配置中获取服务商ID
        this.providerId = serverOptions.providerId;
        
        // 加载患者列表
        this.loadPatientRecords();
      } catch(error) {
        console.error('获取服务商ID失败', error);
      }
    },

    searchNull(){
      if(this.searchText.trim() === ''){ // 去除首尾空格
        this.resetList(); // 重置列表
        this.condition = {}; // 清空搜索条件
        this.loadPatientRecords(); // 重新加载患者记录
      }
    },
    searchCallBack(){
      let queryOptions = {};
      let value = +this.searchText.trim(); // 去除首尾空格
      // 如果当前输入的字符不是纯数字 则认为输入的是名称
      if(value !== value){
        queryOptions.name = this.searchText.trim(); // 去除首尾空格
      }else{
        queryOptions.phone = this.searchText.trim(); // 去除首尾空格
      }
      this.resetList(); // 重置列表
      this.condition = queryOptions; // 保存搜索条件
      this.loadPatientRecords(); // 重新加载患者记录
    },
    // 重置列表
    resetList(){
      this.current = 1; // 当前页码
      this.patientRecords = []; // 存储就诊人记录的数组
      this.loadEnd = false; // 加载结束
    },
    // 显示没有患者档案的提示弹窗
    showNoPatientDialog() {
      this.showCustomModal = true;
    },
    upper(){
        console.log('触底');
        if(this.loadEnd) return
        this.current++;
        this.loadPatientRecords();
      },
    // 加载患者档案记录
    async loadPatientRecords() {
      try {
        if (!this.providerId) {
          console.error('服务商ID不存在');
          return;
        }
        
        // 获取当前用户的所有患者档案
        const { data } = await this.queryApiFunc(this.queryApiOptions);
        console.log("获取患者档案", data);
        if(data.records){
        if(data.total <= this.patientRecords.length){
          this.loadEnd = true
        }
          return this.patientRecords.push(...data.records)
        }
        this.patientRecords = data || [];
      } catch (error) {
        console.error("获取患者档案失败", error);
      }
    },
    // 关闭患者选择弹窗
    closePatientSelectModal() {
      this.showPatientSelectModal = false;
      this.resetList(); // 重置列表
      this.condition = {}; // 清空搜索条件
      this.searchText = ''; // 清空搜索文本
      this.loadPatientRecords(); // 重新加载患者记录
    },
    // 选择患者
    selectPatientItem(patient) {  
      console.log('this.provinceValue',this.provinceValue.insureButton);
      
      // 判断是否绑定身份证 如果当前服务商开启了保险按钮为1则需要绑定身份证 如果是0或2则不需要绑定
      if(this.provinceValue.insureButton === 1 && !patient.idcard){
        setTimeout(() => {
            uni.showToast({
              title: '该患者未绑定身份证',
              icon: 'none' 
            })
        }, 500);
        return this.$navto.push('editPatient', {
          action: 'edit',
          id: patient.id
        });
      }
      this.$emit('selectPatient', patient);
      this.closePatientSelectModal();
      uni.showToast({
        title: '已选择就诊人',
        icon: 'none'
      });
    },
    // 格式化手机号
    formatPhone(phone) {
      if (!phone || phone.length < 11) return phone;
      // 截取中间4位用*号代替，保留前3后4
      return phone.substring(0, 3) + '****' + phone.substring(7);
    },
          // 跳转到编辑患者页面
    goToEditPatient(patient) {
        if (!patient || !patient.id) {
          uni.showToast({
            title: '患者信息不完整',
            icon: 'none'
          });
          return;
        }
        this.closePatientSelectModal();
        // 跳转到就诊人档案编辑页面
        this.$navto.push('editPatient', {
          action: 'edit',
          id: patient.id
        });
    },
          // 跳转到添加患者档案页面
    goToAddPatient() {
        this.closeCustomModal();
        this.closePatientSelectModal();
        // 跳转到就诊人档案页面
        this.$navto.push('addPatient', { action: 'add' });
    },
    // 关闭自定义弹窗
    closeCustomModal() {
      this.showCustomModal = false;
    },
    // 显示患者选择弹窗
    showPatientSelect() {
      if (this.patientRecords.length > 0) {
        // 有患者记录，不需要做特殊处理
        console.log("已找到患者档案", this.patientRecords.length, "条");
      } else {
        return this.showNoPatientDialog();
      }
      this.showPatientSelectModal = true;
    },
  },
};
</script>
<style lang="scss">
.search{
  width: 100%;
  background: #F4F6FA;
  padding:32rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  .searchBox{
    width: 100%;
    height: 72rpx;
    background: #FFFFFF;
    border-radius: 36rpx 36rpx 36rpx 36rpx;
    border: 2rpx solid #D9DBE0;
    display: flex;
    align-items: center;
    position: relative;
    padding-left: 78rpx;
    box-sizing: border-box;
    .searchIcon{
      position: absolute;
      width: 32rpx;
      height: 32rpx;
      top: 20rpx;
      left: 30rpx;
    }
    .search-input{
      flex: 1;
      height: 100%;
    }
    .searchBtn{
      width: 112rpx;
      font-size: 28rpx;
      color: #1D2029;
      line-height: 33rpx;
      text-align: center;
      box-sizing: border-box;
      border-left: 2rpx solid #D9DBE0;
    }
  }
}
.modal-container {
      width: 85%;
      height: 330rpx;
      background-color: #FFFFFF;
      border-radius: 24rpx;
      overflow: hidden;
      position: relative;
      z-index: 1000;
      padding-top: 40rpx;
    }

    .modal-content {
      padding: 20rpx 40rpx 50rpx;
      text-align: center;
      font-size: 30rpx;
      color: #777777;
      line-height: 1.5;
    }

    .modal-btns {
      display: flex;
      justify-content: center;
    }
    /* 自定义弹窗样式 */
    .custom-modal {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 999;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .modal-title {
      text-align: center;
      font-size: 36rpx;
      color: #1D2029;
    }
    

    .cancel-btn {
      color: #666666; 
      border: 2rpx solid #e0e0e0 !important;
      margin-right: 20rpx;
    }

    .confirm-btn {
      color: #FFFFFF;
      background-color: #00C28D;
      margin-left: 20rpx;
    }
    .modal-btn {
      line-height: 90rpx;
      text-align: center;
      font-size: 30rpx;
      width: 260rpx;
      height: 90rpx;
      border: none;
      border-radius: 40rpx;
    }
.modal-mask {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
    }
        /* 就诊人选择弹窗样式 - 新UI */
    .patient-select-modal {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 999;
    }
    .patient-select-container {
      position: absolute;
      width: 100%;
      background-color: #FFFFFF;
      border-radius: 24rpx 24rpx 0 0;
      overflow: hidden;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      z-index: 1000;
      display: flex;
      flex-direction: column;
      min-height: 400rpx;
      box-shadow: 0 -4rpx 24rpx rgba(0, 0, 0, 0.05);
    }

    .patient-select-header {
      padding: 30rpx 20rpx;
      text-align: center;
      position: relative;
    }

    .patient-select-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #1D2029;
    }

    .close-icon {
      position: absolute;
      right: 30rpx;
      top: 26rpx;
      padding: 10rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .close-icon-image {
      width: 36rpx;
      height: 36rpx;
    }

    .patient-list {
      height: 800rpx;
      overflow-y: auto;
      padding: 0 32rpx;
      width: 100%;
      box-sizing: border-box;
      // css隐藏滑动条
      -ms-overflow-style: none; /* IE and Edge */
      scrollbar-width: none; /* Firefox */
      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
      } 
    }

    .patient-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24rpx 0;
      border-bottom: 1rpx solid #EAEAEA;
      position: relative;
    }

    .patient-info {
      display: flex;
      flex-direction: column;
      flex: 1;
    }

    .patient-name {
      font-size: 30rpx;
      color: #333333;
      margin-bottom: 8rpx;
      font-weight: 500;
    }

    .patient-phone {
      font-size: 26rpx;
      color: #999999;
    }
    .edit-icon {
      padding: 10rpx;
      margin-left: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .edit-icon-image {
      width: 36rpx;
      height: 36rpx;
    }
    .add-btn-wrapper {
      padding: 20rpx 32rpx;
      width: 90%;
      border-top: 1rpx solid #EAEAEA;
    }
    .add-patient-btn {
      height: 90rpx;
      line-height: 90rpx;
      text-align: center;
      background-color: #00B484;
      color: #fff;
      font-size: 30rpx;
      font-weight: 500;
      border-radius: 45rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
    }
    .add-patient-btn-text {
        margin-right: 80rpx;
    }
    .add-icon-image {
      width: 36rpx;
      height: 36rpx;
      position: absolute;
      right: 250rpx;
    }

    .patient-actions {
      padding: 10rpx 32rpx 40rpx;
      display: flex;
      justify-content: space-between;
    }

    .action-btn {
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      font-size: 28rpx;
      border-radius: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 1;
    }

    .edit-btn {
      background-color: #F7F7F7;
      color: #333;
      margin-right: 20rpx;
    }

    .archive-btn {
      background-color: #EBF3FE;
      color: #0084FF;
    }

    .indicator-bar {
      width: 80rpx;
      height: 6rpx;
      background-color: #333;
      border-radius: 3rpx;
      margin: 20rpx auto 30rpx;
      opacity: 0.3;
    }


</style>