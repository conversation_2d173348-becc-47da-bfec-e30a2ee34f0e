<template>
  <page>
    <view slot="content" class="main-body">
      <view class="main">
        <view class="m-main">
          <view class="bg-img-view"></view>
          <view class="url-view">
            <text class="l">{{copyText}}</text>
            <!-- #ifndef H5 -->
            <text class="r" @tap="copyFn()">复制</text>
            <!-- #endif -->
          </view>
          <view class="text-view">复制以上网址在电脑浏览器中打开，</view>
          <view class="text-view">使用账号登录，或点击下方"扫一扫"进行扫码登录</view>
          <view class="button-view">
            <view class="but-view" @tap="textFn()">
              <text class="l"></text>
              <text class="r">扫一扫</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </page>
</template>

<script>
import { mapState } from 'vuex'
export default {
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $static_ctx: this.$static_ctx,
      version_ctx: this.version_ctx,
      file_ctx: this.file_ctx,
      copyText: 'http://saas.greenboniot.cn/admin/'
    }
  },
  computed: {
    ...mapState('user', {
      codeUserInfo: state => state.codeUserInfo,
      isLogin: state => state.isLogin
    })
  },
  onLoad(paramsObj) {
    const query = this.$Route.query
    if (!this.$validate.isNull(query)) {

    }
  },
  onShow() {

  },
  methods: {
    init() {

    },
    navtoGo(url, obj = {}) {
      this.$navto.push(url, obj)
    },
    textFn() {
      this.$uniPlugin.toast('敬请期待！')
    },
    copyFn() {
      const that = this
      uni.setClipboardData({
        data: that.copyText,
        success: function() {
          that.$uniPlugin.toast('复制成功')
        },
        fail: function() {
          that.$uniPlugin.toast('复制失败')
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .main-body{
    height: 100%;
    .main{
      height: 100%;
      background: #fff;
    }
  }
  .m-main{
    overflow: hidden;
    padding-bottom: 120upx;
    background: #fff;
    .bg-img-view {
      @include iconImg(320, 320, '/business/bg-icon-PC.png');
      margin: 120upx auto 0;
      display: block;
    }
    .url-view {
      margin: 120upx 0;
      text-align: center;
      .l {
        font-size: 28upx;
        line-height: 42upx;
      }
      .r {
        color: $topicC;
        font-size: 32upx;
        line-height: 48upx;
        vertical-align: middle;
        margin-left: 16upx;
      }
    }
    .text-view {
      font-size: 24upx;
      line-height: 36upx;
      text-align: center;
      color: #666;
    }
    .button-view {
      position: fixed;
      left: 0;
      right: 0;
      bottom: 0;
      height: 120upx;
      box-sizing: border-box;
      padding: 0 30upx;
      .but-view {
        background: linear-gradient(90deg,rgba(255,185,36,1),rgba(254,219,55,1));
        height: 88upx;
        line-height: 88upx;
        @include rounded(44upx);
        text-align: center;
        color: #fff;
        .l {
          display: inline-block;
          vertical-align: middle;
          @include iconImg(48, 48, '/business/icon-saoyisao.png');
          margin-right: 12upx;
        }
        .r {
          display: inline-block;
          vertical-align: middle;
          font-size: 32upx;
          line-height: 48upx;
        }
      }
    }
  }
</style>
