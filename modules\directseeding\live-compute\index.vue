<template>
  <view class="page-box">
    <!-- 标题 -->
    <lv-header :headerobj="headerobj" @pre="preNext" position="absolute" v-if="orientation != 'horizontal'">
    	<view class="livePlayerHeader">
    		<view
				class="livePlayerHeader-c" >
    			<image :src="coverPathsUrl" class="livePlayerHeader-ico"></image>
    			<view class="liveTitle">{{ data.title }}</view>
    		</view>
    	</view>
    </lv-header>
    <!-- 直播已结束 -->
    <view class="live-compute-bgBox">
      <view class="live-compute-Mask"></view>
      <image :src="poster" class="live-compute-bg"></image>
    </view>
   <scroll-refresh
      bgColor="transparent"
      :fixed="true"
      top="92"
      :up="upOption"
      :down="downOption"
      :zPageDefault='zPageDefault'
      @returnFn="returnFn"
      @scrollInit="scrollInit"
      :isShowOptUpSwitch="false"
      :isShowEmptySwitch="false"
    >
      <view class="live-compute-box" >
        <view class="live-compute-top">
          <view class="live-compute-t"> 直播已结束 </view>
		  <!-- 设计稿上有 但是暂时不展示 -->
		 <view class="number-box">
		  	<!-- {{ data.playStatisticsCount }} 人看过 -->
		  </view>
          <button class="gotoIndex" @click="preNext">回到首页</button>
        </view>

       <!-- <view class="live-compute-c">
          <view class="live-compute-li" v-for="item in 5">
            <image
              :src="poster"
              class="live-compute-li-icon"
              mode="widthFix"
            ></image>
            <view class="live-compute-li-c">
              <view class="live-compute-li-ct"> 本场直播回放 </view>
              <view class="live-compute-li-cinfo">
                第二届医药数字品牌大会 第二届医药数字品牌大会
              </view>
            </view>
            <button type="primary" class="live-compute-li-btn">观看</button>
          </view>
        </view> -->
        <view class="recommend-box">
          <view class="recommend-t">
            推荐直播
          </view>
          <videoItem :pdList="pdList" model='horizontal'></videoItem>
          <view class="empty-box" v-if="pdList.length === 0">
            <image
              class="empty-img"
              mode="widthFix"
              :src="$static_ctx + 'image/business/hulu-v2/icon-circle-empty.png'"
            ></image>
            <text class="empty-text">暂无直播</text>
          </view>
        </view>
      </view>
    </scroll-refresh>
  </view>
</template>

<script>
import { getQueryObject, isDomainUrl } from "@/utils/index";
import lvHeader from "@/modules/directseeding/shortVideo/list/components/lv-header.nvue";
import videoItem from '@/modules/directseeding/components/video-item/index'
export default {
  components: {
    lvHeader,
    videoItem
  },
  data() {
    return {
      $static_ctx: this.$static_ctx,
      zPageDefault:{
        refresherNoLogoIsVisible:true,
      },
      regForm:{},
      headerobj: {
        currentIndex: 3,
        headBgColor: "transparent",
        contentColor: "#fff",
      },
      currentid: null,
      poster: "",
      data: {},
      coverPathsUrl: "",
      downOption: {
        auto: false, // 不自动加载
      },
      upOption: {
        auto: false, // 不自动加载
      },
      pdList: [], // 列表数据
      isInit: false, // 列表是否已经初始化
    };
  },
  methods: {
    preNext() {
      uni.switchTab({
        url: "pages/index/index",
      });
    },
    init(val) {
      this.$nextTick(() => {
        this.isInit = true; // 标记为true
        this.regForm.search = "";
        this.mescroll.triggerDownScroll();
      });
    },
    scrollInit(scroll) {
      scroll.optUp.page.num = 1;
      scroll.optUp.page.size = 7;
      this.mescroll = scroll;
    },
    returnFn(obj = {pageNum:1,pageSize:20,successCallback:()=>{}}) {
      const that = this;
      function queryPage(pageNum, pageSize, fn) {
        const param = {
          current: pageNum,
          size: pageSize,
          condition: {
            activityStatusList: [3,5],
            showType: 1,
			      liveFirst: 1,
            externalType: 1,
            businessType: that.businessType || 7, // 直播活动
            // productId: that.productId,
          },
        };

        if (that.regForm.search) {
          param.condition.title = that.regForm.search;
        }

        if (that.businessType === "all") {
          delete param.condition.businessType;
        }

        that.$api.cloudClassroom.getMeetingQueryPage(param).then((res) => {
            for (const a in res.data.records) {
              const data = res.data.records[a];
              data.coverPathsUrl = isDomainUrl(data.coverPaths);
              // data.endTimeText = that.$common.formatDate(new Date(data.endTime), 'yyyy-MM-dd HH:mm')
            }
            //
            // debugger
            fn(res.data.records);
            // fn([])
        });
      }

      setTimeout(function () {
        queryPage(obj.pageNum, obj.pageSize, (data) => {
          if (obj.pageNum === 1) {
            that.pdList = [];
          }
          that.pdList = that.pdList.concat(data);
          obj.successCallback && obj.successCallback(data || []);
        });
      }, that.$constant.noun.scrollRefreshTime);
    },
    getMeetingQueryOne() {
      const param = {
        id: this.currentid,
      };
      this.$api.cloudClassroom.getMeetingQueryOne(param).then((res) => {
        const data = res.data;
        if (data === "") {
          return this.$uniPlugin.toast("条目不存在");
        }
        this.poster = isDomainUrl(data.coverPaths);
        this.coverPathsUrl = isDomainUrl(data.coverPaths);
        this.data = data;
      });
    },
  },
  onLoad(options) {
    const query = this.$Route.query;
    console.log("query------------", query);
    const that = this;
    if (this.$validate.isNull(query.id)) {
      let params = decodeURIComponent(query.scene);
      console.log("params:====", params);
      query.id = getQueryObject(params).id;
      if (this.$validate.isNull(query.id) && query.scene) {
        that.$uniPlugin.toast("参数异常");
        return;
      }
    }
    options = query;
    if (options && options.id) {
      this.currentid = options.id;
    }
    this.getMeetingQueryOne();
    this.init();
	this.returnFn()
  },
};
</script>
<style>
	.zp-l-container{
		display: none !important;
	}
</style>
<style lang="scss" scoped>
	.number-box {
		font-weight: 400;
		font-size: 28rpx;
		color: #FFFFFF;
		margin:24rpx 0 40rpx;
	}
	.livePlayerHeader-ico {
		width: 64rpx;
		height: 64rpx;
		border-radius: 50%;
		margin-right: 16rpx;
	}
	.livePlayerHeader {
		// position: absolute;
		// top: 0px;
		// height: 100upx;
		display: flex;
		// height: 100%;
		align-items: center;
		width: 100%;
		padding-left: 5upx;
		z-index: 9999;
		tansform: tanslate3d(0, 0, 0);
		.liveTitle{
			    white-space: nowrap;
			    overflow: hidden;
			    text-overflow: ellipsis;
				color: white;
		}
	}

	.livePlayerHeader-c {
		// background-color: rgba(0, 0, 0, 0.4);
		display: flex;
		height: 70rpx;
		align-items: center;
		font-size: 28upx;
		color: #fff;
		border-radius: 50upx;
		overflow: hidden;
		background-color: rgba(0, 0, 0, 0.2);
		max-width: 404rpx;
		margin-left: 80rpx;
		margin-top: 4rpx;
		padding-right: 10rpx;
	}

	.pl20.livePlayerHeader-c {
		padding-left: 20upx;
		max-width: 100vh;
	}

	.livePlayerHeader-txt {
		margin-left: 20upx;
		max-width: 350upx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		overflow-x: auto;
	}

	.livePlayerHeader-txt.max200 {
		max-width: 380upx;
	}

	.textelipse {
		text-overflow: ellipsis;
		white-space: nowrap;
		overflow: hidden;
		max-width: 100%;
	}

	.txt {
		font-size: 24rpx;
		// line-height: 55upx;
	}

	.txt2 {
		// line-height: 55upx;

		margin: 0 10upx;
	}
  .live-compute-tw{
    word-break: break-all;
    padding: 0 30upx;
  }
  .empty-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items:center;
    height: 456upx;
    .empty-img {
      width:250upx;
      // height: 250upx;
    }
    .empty-text {
      height: 70rpx;
      display: flex;
      align-items: center;
      align-items: center;
      color: #7c7c7c;
    }
  }
.recommend-box {
  // padding: 0 30upx;
  width: 654rpx;
  background: rgba(255,255,255,0.1);
  border-radius: 16rpx;
  overflow-x: scroll;
  padding-left: 32rpx;
  padding-right: 32rpx;
  margin: 0 auto;
  box-sizing: border-box;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 和 Edge */
  .recommend-t {
    margin: 32rpx 32rpx 24rpx 4rpx;
	 position: sticky;
	 left: 4rpx;
     font-weight: 500;
     font-size: 32rpx;
     color: #FFFFFF;
  }
}
.recommend-box::-webkit-scrollbar {
  width: 0; /* 对于竖向滚动条 */
  height: 0; /* 对于横向滚动条 */
}

/* 如果需要同时兼容Firefox，可以使用以下规则 */
.hide-scrollbar {

}

// .hide-scrollbar {
//   overflow: -moz-scrollbars-none; /* Older Firefox browsers */
//   overflow-y: scroll; /* 保持滚动功能 */
// }
.page-box {
  display: flex;
  align-items: center;
  height: 100vh;
  width: 100vw;
  background: rgb(0, 0, 0);
  // background: rgba(0,0,0,0.7);
  // backdrop-filter: blur(15px);
  color: #fff;
}
.live-compute-bgBox{
  position: fixed;
}
.live-compute-Mask{
  top: 0;
  position: fixed;
  z-index: 2;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.7);
  backdrop-filter: blur(15px);
}
.live-compute-bg {
  top: 0;
  z-index: 1;
  position: fixed;
  width: 100%;
  height: 100%;
  // object-fit: contain;
}
.live-compute-box {
  // position: absolute;
  // top: 55px;
  // left: 0;
  // right: 0;
  // bottom: 0;
  .live-compute-top {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 90rpx;
    .live-compute-t {
    }
  }
  .live-compute-t {
    margin-top: 128rpx;
    font-weight: 500;
    font-size: 44rpx;
    color: #FFFFFF;
  }
  .gotoIndex{
	  width: 272rpx;
	  height: 72rpx;
	  background: #00B484;
	  border-radius: 48rpx;
	  font-size: 28rpx;
	  color: #FFFFFF;
  }
  .live-compute-icon {
    width: 90upx;
    height: 90upx;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 10px;
  }
  .live-compute-info {
    color: #878e96;
    margin-top: 20rpx;
  }
  .live-compute-li-icon {
    width: 90upx;
    height: 100%;
  }
  .live-compute-li {
    padding: 20upx;
    display: flex;
    align-items: center;
    height: 128upx;
    margin: 0 30rpx;
    background: rgba(58, 66, 77,0.8);
    border-radius: 15rpx;
    margin-bottom: 20upx;
  }
  .live-compute-li-c {
    flex: 1;
    padding: 0px 30rpx;
    max-width: 380upx;
  }
  .live-compute-li-ct {
    font-size: 28upx;
  }
  .live-compute-li-cinfo {
  }
  .live-compute-li-btn {
    font-size: 25upx;
    padding: 0 32upx;
    margin-left: 0;
    margin-right: 0;
    background-color: rgb(251, 94, 66);
    color: #fff;
  }
  .live-compute-li-cinfo {
    /* 隐藏溢出容器的文本 */
    overflow: hidden;
    /* 防止文本换行，使其在一行内显示 */
    white-space: nowrap;
    /* 使用省略号 (...) 来表示被隐藏的文本 */
    text-overflow: ellipsis;
  }
  .live-compute-c {
    margin-bottom: 50upx;
  }
}
</style>
