
<template>
  <view class="title-input clear-float" :class="{'bdt':defaultConfig.bdt,'bdb':defaultConfig.bdb && !child}">
    <view class="l-l" :style="{'color': defaultConfig.titleColor}">
      {{defaultConfig.label}}150
      <text class="star" v-if="defaultConfig.required">*</text>
    </view>
    <view class="l-r" >
      <DateSelector :disabled='disabled' :defaultStartDate='form.data.val' style='width: 100%;' :mode="selectDateType" @onChange="onDateSelectorChange" @onSubmit="onDateSelectorSubmit" />
      <view class="util" v-if="config.unitDesc">
        {{config.unitDesc}}
      </view>
    </view>
  </view>
</template>

<script>
import DateSelector from './dateSelector/index.vue';
export default {
  data() {
    return {
      selectDateType: 4, // 选中的日期类型
      form: {
        data: {
          val: ''
        }
      },
      array: [],
      index: 0,
      defaultConfig: {
        bdt: false,
        bdb: true,
        titleColor: '#333',
        textColor: '#333',
        label: '单行输入框',
        name: 'input',
        required: false,
      }
    }
  },
  watch: {
    cData: {
      handler(val) {
        if(this.form.data.val !== val){
          this.watchDataMain(val)
        }
      },
      deep: true
    },
    config: {
      handler(val) {
        this.copyConfig()
      },
      deep: true
    }
  },
  props: {
    child:{
      type:Boolean,
      default:false,
    },
    // 是否禁用disable
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 初始值传值，用于回显
    cData: {
      type: [String, Number],
      required: false,
      default() {
        return ''
      }
    },
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    },
    isOne:{
      type:Boolean,
      default:true,
    }
  },
  computed:{
     // ...mapState('user', {
     //   // codeUserInfo: state => state.codeUserInfo, // 当前登录用户信息
     //   curSelectUserInfo: state => state.curSelectUserInfo, // 当前选中的用户信息
     //   // curSelectUserInfoId: state => state.curSelectUserInfoId, // 当前选中的档案的ID
     // }),
  },
  components:{
    DateSelector
  },
  mounted() {
    console.log(this.config,this.cData)
      this.copyConfig()
      if (!this.$validate.isNull(this.cData)) {
        this.watchDataMain(this.cData)
      }
  },
  methods: {
    onDateSelectorChange({ startDate, endDate }) {
      console.log('onDateSelectorChange', startDate, endDate);
    },
    onDateSelectorSubmit({ startDate, endDate }) {
      console.log('onDateSelectorSubmit', startDate, endDate);
      const that = this
      if (that.disabled) return
      if(this.isOne){
        this.form.data.val = startDate
      }
      console.log('onDateSelectorSubmit', startDate, endDate);

      that.$emit('updateForm', { key: '' + that.config.name, value: that.form.data.val })
    },
    focusInput(){
      // console.log('this.config',this.config)
      if(this.config.quickInputType === 1 && !this.disabled){
        // console.log(this.config.quickInputTexts)
        this.$common.setKeyVal('system', 'temporaryStorage', {
          quickInputTexts:this.config.quickInputTexts,
          config:this.config,
          child:this.child
        })

        this.$navto.push('activityQuickInputIndex')


      }
    },
    /**
       * 初始化拷贝config对象
       */
    copyConfig() {
      const that = this
      const obj = that.config
      Object.keys(obj).forEach(function(key) {
        that.defaultConfig[key] = obj[key]
      })
    },
    /**
       * 监听Data主逻辑方法
       */
    watchDataMain(val) {
      this.form.data.val = val
    },
    /**
       * picker触发选中事件
       * @param e
       */
    returnFn(e) {
      const that = this
      if (that.disabled) return
      that.$emit('updateForm', { key: '' + that.config.name, value: that.form.data.val })
    }
  }
}
</script>

<style lang="scss" scoped>
  .color-topicC{
    color: $topicC !important;
  }
  .title-input{
    background-color:#ffffff;
    .l-l{
      line-height: 88upx;
      color: #333333;
      font-weight: 600;
      font-size:30upx;
    }
    .l-r{
      display: flex;
      align-items: center;
      input {
        height: 80upx;
        line-height: 80upx;
        font-size: 30rpx;
        color: #1D2029;
        text-align: left;
        display: inline-block;
        vertical-align: middle;
        width: calc(100% - 64upx);
        border-bottom: 1upx solid $borderColor;
        border-radius: 10upx;
        padding: 0 20rpx;

      }
      input.disabled{
          background-color: #dbdbdb;
          color: #fff;
      }
      .util{
        width: 64upx;
        font-size: 28upx;
            overflow: hidden;
            text-align: center;

      }
    }
  }
  .star{
    font-weight: 500;
    font-size: 30rpx;
    color: #F53F3F;
    line-height: 42rpx;
    padding: 0 2rpx;
    display: inline-block;
  }
</style>
