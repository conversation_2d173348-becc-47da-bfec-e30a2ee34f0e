<template>
  <view class="confirm-exchange">
    <uniPopup type="bottom" ref='uniPopup' id="convertibilityPopup" @change="changeShow">
      <view class="confirm">
        <!-- 标题 -->
        <view class="confirmTitle">
          确认兑换
          <image class="iconClose" @click="close" :src="iconClose" mode="aspectFill"></image>
        </view>
        <!-- 内容模块 -->
        <view class="confirmContent">
          <image class="confirmIcon" :src="file_ctx + pageData.productImage" mode="aspectFill"></image>
          <view class="confirmText">
            <view class="confirmName">{{pageData.productName}}</view>
            <view class="goodsNumsMin">{{pageData.needPoint}}<text class="goodsSings">福币</text></view>
          </view>
        </view>
        <!-- 地址 -->
        <view class="address" @click="gotoAddress">
          <view class="addressName" v-if='currentAddress'>{{currentAddress.username}} {{currentAddress.phone}}</view>
          <view class="addressContent" v-if='currentAddress'>{{currentAddress.address}}</view>
          <view class="addressContent" v-else>
            请选择地址，再提交兑换申请
            <view class="addressContentMakeup">现在填写</view>
          </view>
          <image class="iconRightArrow" :src="iconRightArrow" mode="aspectFill"></image>
        </view>
        <!-- 确认按钮 -->
        <view class="actionsConfirm" @click="confirmActions">提交兑换申请</view>
      </view>
    </uniPopup>
    <uniPopup type="center" ref='subscriptionPopup' id="subscriptionPopup">
      <view class="my-subscription">
        <view class="header">
          <view class="title">已提交申请，扫码关注公众号</view>
          <view class="info">留意审核通知及发货提醒</view>
        </view>
        <view class="content"><image :show-menu-by-longpress="true" :src="file_ctx + 'static/image/business/pharmacy-cyclopedia/integral-subscribe-messages-bg.jpg'"></image></view>
        <view class="bottom-text">长按识别或者保存图片扫一扫</view>
      </view>
    </uniPopup>
  </view>
</template>

<script>
  import calabashApis from "@/modules/common/api/calabash.js"
  import { mapState } from "vuex";
  import uniPopup from '@/components/uni/uni-popup/uni-popup.vue'
  export default{
    components: {
        uniPopup
    },
    props:{
      openFlag:{
        type:Boolean,
        default:false
      },
      pageData:{
        type:Object,
        default:{}
      }
    },
    watch:{
      openFlag(n){
          if(n){
            this.getCurrentAddress()
            this.$refs.uniPopup.open()
            // #ifdef MP-WEIXIN
            this.handleClickTrack(1)
            // #endif
          }else{
            this.$refs.uniPopup.close()
            // #ifdef MP-WEIXIN
            this.handleClickTrack(2)
            // #endif
          }
      }
    },
    data(){
      return {
        file_ctx: this.file_ctx,
        iconClose: this.$static_ctx + "image/business/hulu-v2/icon-close2.png",
        iconRightArrow: this.$static_ctx + "image/business/hulu-v2/icon-right-arrow.png",
        currentAddress:null
      }
    },
    computed: {
      ...mapState("user", {
        accountId: (state) => state.accountId,
        fansRecord: (state) => state.fansRecord,
      }),
    },
     mounted() {

    },
    methods:{
      // #ifdef MP-WEIXIN
      handleClickTrack(type){
        getApp().globalData.sensors.track("PopupClick",
          {
            'page_name' : '兑换详情',
            'popup_id' : 'convertibilityPopup',
            'popup_name' : '确认兑换',
            'click_type' : type == 1 ? '进入弹窗' : '取消弹窗',
          }
        ) 
      },
      // #endif
      async confirmActions(){
        console.log('fansRecord',this.fansRecord);
        if(!this?.currentAddress?.id) return uni.showToast({icon:'none',title:"请选择地址"})
        const unionId = await this.$ext.wechat.getUnionId()
        let queryOptions = {
            accountId:this.accountId,
            username:this.fansRecord.nickName,
            giftId:this.pageData.id,
            addressId:this.currentAddress.id,
            unionId,
          }
        console.log('queryOptions',queryOptions);
        let {data} = await calabashApis.pointgiftexchangeInsert(queryOptions);
        console.log('data',data);
        if(data){
          this.$refs.subscriptionPopup.open()
          // uni.showModal({
          //   showCancel:false,
          // 	title: '提示',
          // 	content: '已提交申请，请留意审核通知',
          // 	success:(res)=> {
          // 		if (res.confirm) {
          //       this.close()
          // 		}
          // 	}
          // });
        }
      },
      async getCurrentAddress(){
        // 获取默认地址
        let queryOptions = {
          condition:{isPrimary:1,accountId:this.accountId},
          }
        if(uni.getStorageSync('currentAddressId')){
          queryOptions.condition.id = uni.getStorageSync('currentAddressId');
          delete queryOptions.condition.isPrimary
        }
        let {data:{records:[defaultAddress]}} = await calabashApis.pointaddressQueryPage(queryOptions)
        if(!defaultAddress){
          this.currentAddress = null;
          return
        }
        this.currentAddress = defaultAddress;
        let {province,city,county,address} = defaultAddress
        this.currentAddress.address = province + city + county + address
      },
      gotoAddress(){
        uni.navigateTo({url:'modules/activity/calabash/exchangeWings/meAddress'})
        this.close()
      },
      changeShow(res){
        !res.show && this.$emit('change',res.show)
      },
      close(){
        this.$emit('change',false)
      }
    }
  }
</script>

<style lang="scss">
  .my-subscription{
    width: 600rpx;
    padding: 60rpx 20rpx;
    background-color: #fff;
    border-radius: 15rpx;
    .header{
      display: flex;
      flex-direction: column;
      align-items: center;
     .title{
      
     } 
     .info{

     }
    }
    .content{
      display: flex;
      width: 258rpx;
      height: 258rpx;
      margin: 30rpx auto 20rpx;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .bottom-text{
      font-size: 12px;
      color:darkgray;
      text-align: center;
    }
  }
  .confirm{
    width: 750rpx;
    height: 722rpx;
    background: #FFFFFF;
    border-radius: 24rpx 24rpx 0rpx 0rpx;
    position: fixed;
    bottom: 0;
    padding: 32rpx;
    box-sizing: border-box;
    .confirmTitle{
      font-weight: 500;
      font-size: 32rpx;
      color: #1D2029;
      text-align: center;
      position: relative;
      .iconClose{
        width: 32rpx;
        height: 32rpx;
        position: absolute;
        right: 0;
        top: 6rpx;
      }
    }
    .confirmContent{
      display: flex;
      margin-top: 48rpx;
      .confirmIcon{
        width: 144rpx;
        height: 144rpx;
        background: #FFFFFF;
        border-radius: 7rpx;
        border: 1rpx solid #EAEBF0;
        margin-right: 20rpx;
      }
      .confirmText{
        display: flex;
        flex-wrap: wrap;
      }
      .confirmName{
        font-weight: 500;
        font-size: 28rpx;
        color: #1D2029;
        width: 100%;
      }
      .goodsNumsMin{
        font-weight: 500;
        font-size: 40rpx;
        color: #FF5500;
        margin-top: auto;
        .goodsSings{
          font-weight: 400;
          font-size: 20rpx;
          color: #FF4100;
        }
      }
    }
    .address{
      width: 686rpx;
      height: 130rpx;
      background: #F4F6FA;
      border-radius: 16rpx;
      margin-top: 32rpx;
      padding: 24rpx;
      box-sizing: border-box;
      position: relative;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      .iconRightArrow{
        width: 32rpx;
        height: 32rpx;
        position: absolute;
        top:50%;
        transform: translateY(-50%);
        right: 14rpx;
      }
      .addressName{
        font-weight: 400;
        font-size: 28rpx;
        color: #1D2029;
        width: 100%;
      }
      .addressContent{
        font-weight: 400;
        font-size: 24rpx;
        color: #4E5569;
        width: 100%;
        display: flex;
        .addressContentMakeup{
          margin-left: auto;
          margin-right: 20rpx;
          color: rgb(249, 104, 68);
        }
      }
    }
    .actionsConfirm{
      width: 686rpx;
      height: 88rpx;
      background: #00B484;
      border-radius: 44rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #FFFFFF;
      text-align: center;
      line-height: 88rpx;
      margin-top: 144rpx;
    }
  }
</style>
