<template>
  <view class='rumour-rule'>
    <rich-text class="name" :nodes="ruleObj.rule"  preview  @click ="itemclick(ruleObj.rule)" :style="{letterSpacing:'2rpx'}"></rich-text>
  </view>
</template>

<script>
  export default {
    data(){
      return{
        ruleObj:null,
      }
    },
    onLoad(){
      this.refuterumorweekstatisticsQueryDefault()
    },
    mounted(){},
    methods:{
      async refuterumorweekstatisticsQueryDefault(){
        const res = await this.$api.drugBook.refuterumorweekstatisticsQueryDefault({})
        this.ruleObj = res.data
      },
      itemclick(item){
        // 判断含有图片
        if (item.indexOf("src") >= 0) {
          const imgs = [];
          item.replace(/]*src=['"]([^'"]+)[^>]*>/gi, function (match, capture) {
            imgs.push(capture);
          })
          
          uni.previewImage({
            current: imgs[0], // 当前显示图片的http链接
            urls: imgs
          })
        }
      },
    },
 }
</script>

<style lang='scss' scoped>
.rumour-rule{
  padding: 0 32rpx;
}
</style>