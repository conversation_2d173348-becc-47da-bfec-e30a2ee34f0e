<template>
  <page>
    <view slot="content" class="body-main">
      <scroll-refresh
        style="height: 100%"
        :isShowEmptySwitch="true"
        :fixed="false"
        :isAbsolute="false"
        :up="upOption"
        :down="downOption"
        @returnFn="returnFn"
        @scrollInit="scrollInit"
      >
        <view class="content">
          <view class="content-main">
            <template v-for="item in replyList">
              <div :id="`reply-${item.replyId}`" :key="item.id" class="item">
                <reply-item :info="item" />
              </div>
            </template>
          </view>
        </view>
      </scroll-refresh>
    </view>
  </page>
</template>

<script>

import { mapState } from 'vuex'
import replyItem from './components/reply-item.vue'
  import scrollRefresh from '@/components/uni/zPaging/index'
  import page from '@/components/basics/frame/page'
export default {
  components: {
    replyItem,
    scrollRefresh,
    page
  },
  data() {
    return {
      file_ctx: this.file_ctx,
      mescroll: null, // mescroll实例对象
      downOption: {
        auto: false // 不自动加载
      },
      upOption: {
        auto: false, // 不自动加载
        empty: {
          top: 0,
          zIndex: 999,
        },
        onScroll: true
      },
      isInit: false, // 列表是否已经初始化
      replyList: [],
      pageStartDate: null
    }
  },
  computed: {
    ...mapState('user', {
      codeUserInfo: state => state.codeUserInfo, // 当前登录用户信息
      curSelectUserInfo: state => state.curSelectUserInfo, // 当前选中的用户信息
      curSelectUserInfoId: state => state.curSelectUserInfoId, // 当前选中的档案的ID
      recordUserInfo: state => state.recordUserInfo,
      accountId: state => state.accountId
    })
  },
  onLoad(paramsObj) {
    const that = this
    const query = that.$Route.query
    this.pageStartDate = that.$common.formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss')
    if (!that.$validate.isNull(query)) {

    }
    this.$nextTick(() => {
      that.init()
      this.sendReadStatus()
    })
  },
  onShow() {

  },
  methods: {
    setReadStatus() {
      this.replyList.forEach((item, index) => {
        this.$set(this.replyList, index, { ...item, readStatus: 1 })
      })
    },
    sendReadStatus() {
      const param = {
        businessTypes: [5, 6, 7],
        accountId: this.accountId
      }
      this.setReadStatus()
      this.$api.community.noticelogUpdateAllNoticeLog(param)
    },
    init() {
      this.$nextTick(() => {
        this.mescroll.triggerDownScroll()
      })
    },
    scrollInit(scroll) {
      scroll.optUp.page.num = 1
      scroll.optUp.page.size = 10
      this.mescroll = scroll
    },
    returnFn(obj) {
      const that = this
      setTimeout(function () {
        let params = {
          current: obj.pageNum,
          size: obj.pageSize,
          condition: {
            createTime: that.pageStartDate,
            accountId: that.accountId,
            businessType: [5, 6, 7]
          }
        }
        that.$api.community.noticelogQueryCommentPage(params).then(res => {
          let data = res.data.records || []
          data = data.map(item => {
            return {
              ...item,
              replyId: item.id,
              putawayTimeText: that.$common.formatDate(new Date(item.putawayTime || new Date().getTime()), 'yyyy-MM-dd HH:mm:ss')
            }
          })
          if (obj.pageNum === 1) {
            that.replyList = []
          }
          that.replyList = [...that.replyList, ...data]
          obj.successCallback && obj.successCallback(data)
        })
      }, that.$constant.noun.scrollRefreshTime)

    },
  },
  watch: {
    replyList: {
      handler(newData, oldData) {
        if (this.$validate.isNull(oldData) && !this.$validate.isNull(newData)) {
          this.$nextTick(() => {
            this.checkDivShow()
          })
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.body-main {
  height: 100%;
  overflow-y: auto;
  background-color: #fff;
  .content {
    padding-bottom: env(safe-area-inset-bottom);
    padding-bottom: constant(safe-area-inset-bottom);
    &-main {
      padding: 44upx 34upx 0;
    }
  }
}
.item + .item {
  margin-top: 32upx;
}
</style>
