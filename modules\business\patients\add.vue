<template>
  <page>
    <view slot="content" class="main-body">
      <view class="form-content">
        <title-radio v-model="form.memberType" :config="config.memberType" />
      </view>
      <view class="head">
        <view class="l">
        </view>
        <view class="r">
          个人身份信息
        </view>
      </view>
      <view class="form-content">
        <view class="img-title">
            个人照：
        </view>
        <title-img
          :config="config.headPath"
          @returnFn="(obj) => {imgReturnFn(obj,'headPath')}"
          :cData="cDataHeadPath"
        ></title-img>
        <title-input v-model="form.name" :config="config.name" :disabled="config.name.disabled"></title-input>
        <title-input v-model="form.phone" :config="config.phone" :disabled="config.phone.disabled"></title-input>
        <title-input v-model="form.idNumber" :config="config.idNumber" :disabled="config.idNumber.disabled"></title-input>
        <title-jump-date v-model="form.birthDate" title="出生日期" :endDate="$timePlugin.parseTime(new Date(), '{y}-{m}-{d}')"></title-jump-date>
        <title-input v-model="form.origo" :config="config.origo"></title-input>
        <title-input v-model="form.address" :config="config.address"></title-input>
        <!-- <title-select-address @updateForm="addressReturnFn" :config="{label: '详细地址'}" /> -->
        <title-radio v-model="form.gender" :config="config.gender" />
        <title-selector v-model="form.familyTies" :config="config.familyTies" />
      </view>
      <view class="head">
        <view class="l">
        </view>
        <view class="r">
          病史情况
        </view>
      </view>
      <view class="form-content">
        <title-textarea v-model="form.previousHistory" :config="config.previousHistory" />
        <title-textarea v-model="form.familyHistory" :config="config.familyHistory" />
        <title-textarea v-model="form.allergicHistory" :config="config.allergicHistory" />
      </view>

      <view class="btn-view-main">
        <view class="btn-view-li">
          <view class="btn-view-li-view" @tap="save()">保存档案</view>
        </view>
      </view>
    </view>
  </page>
</template>

<script>
import TitleRadio from '@/components/business/module/v1/title-radio/index.vue'
import TitleInput from "@/components/business/module/v1/title-input/index"
import TitleTextarea from "@/components/business/module/v1/title-textarea/index"
import TitleJumpDate from "@/components/business/module/v1/title-jump-date/index.vue"
import TitleSelectAddress from "@/modules/business/components/title-select-address/index"
import TitleImg from "@/components/business/module/title-img/index.vue"
import TitleSelector from "@/components/business/module/v1/title-selector/index.vue"
import {mapState} from "vuex"
export default {
  name: "PatientsAdd",
  components: {
    TitleInput,
    TitleRadio,
    TitleJumpDate,
    TitleSelectAddress,
    TitleImg,
    TitleSelector,
    TitleTextarea
  },
  data () {
    return {
      query: {},
      cDataHeadPath: [],
      form: {
        tenantId: '', // 租户id
        memberType: '', // 成员类型
        name: '', // 姓名
        phone: '', // 手机号码
        idNumber: '' ,// 身份证
        birthDate: '', // 出生日期
        origo: '', // 籍贯
        address: '', // 详细地址
        gender: '', // 性别
        familyTies: '',
        headPath: '', // 个人照
        previousHistory: '',
        familyHistory: '',
        allergicHistory: '',
        registerSource: 1, // 注册来源
        physicianUserId: '', // 邀请人
        // 是否为主账号 暂时无用
        isPrimaryAccount: 2, // 是否为主账号 1-是 2-否
        masterId: '', // 主账号id
      },
      $timePlugin: this.$timePlugin,
      $constant: this.$constant,
      file_ctx: this.file_ctx,
      config: {
        memberType: {label: '成员类型', required: true, array: [{ label: '普通成员', value: 1 },{ label: '新生儿', value: 2 }]},
        name: {label: '真实姓名', required: true},
        phone: {label: '手机号码', required: true},
        idNumber: {label: '身份证'},
        origo: {label: '籍贯'},
        address: {label: '详细地址'},
        gender: {label: '性别', required: true, array: [{label: '男',value: 1},{label: '女',value: 2},{label: '未知',value: 3}]},
        familyTies: {label: '家庭关系', required: true, array: [
          { label: '配偶', value: 1 },
          { label: '子女', value: 2 },
          { label: '父母', value: 3 },
          { label: '兄弟', value: 4 },
          { label: '姐妹', value: 5 },
          { label: '其他', value: 6 }],
        },
        previousHistory: {label: '既往史'},
        familyHistory: {label: '家族史'},
        allergicHistory: {label: '过敏史'},
        headPath: {count: 1, multiSelectCount: 1, theCluesText: '最多只能上传1张图片'},
      }
    }
  },
  onLoad() {
    this.query = this.$Route.query
    if(!this.$validate.isNull(this.query)) {
      this.getDetail()
    }
  },
  mounted () {
    this.getSmallprogramqrcodeGetCodeByScene()
    // 档案不存在时，新增自己的档案
    if(this.$validate.isNull(this.recordUserInfo)) {
      if(!this.$validate.isNull(this.storeList)) {
        this.$set(this.config, 'phone', {...this.config.phone, disabled: true})
        this.form.phone = this.storeList[0].phone
        // 默认为主账号
        this.form.isPrimaryAccount = 1
      }
    } else {
      this.form.masterId = this.recordUserInfo.id
    }
  },
  computed: {
    ...mapState('user', {
      recordUserInfo: state => state.recordUserInfo
    }),
    storeList () {
      return this.$common.getKeyVal('user', 'storeList', true)
    },
    scene () {
      return this.$common.getKeyVal('system', 'scene', true)
    },
    shareUserId () {
      return this.$common.getKeyVal('system', 'shareUserId',true)
    }
  },
  methods: {
    async save () {
      const that = this
      // 表单校验
      for(const key in this.form) {
        if(!this.$validate.isNull(this.config[key])) {
          if(this.config[key].required && !this.form[key]) {
            this.$uniPlugin.toast(`${this.config[key].label}不得为空！`)
            return
          }
        }
      }

      const isPhone = that.$validate.isMobile(that.form.phone)
      if (!isPhone.flag) {
        this.$uniPlugin.toast(isPhone.msg)
        return
      }

      let url = ''
      if(!this.$validate.isNull(this.query)) {
        url = this.$api.patientinfo.patientinfoUpdate
        this.form.id = this.query.id
      } else {
        url = this.$api.patientinfo.patientinfoInsert
      }

      that.$uniPlugin.loading('正在提交', true)
      const res = await url(this.form).catch(err => {
        that.$uniPlugin.hideToast()
      })

      // 没有档案的情况下新增，重新获取档案
      if(this.$validate.isNull(this.recordUserInfo)) {
        await this.$ext.user.getRecordUserInfo()
      }

      setTimeout(() => {
        that.$uniPlugin.hideToast()
        that.$navto.back(1)
      }, that.$constant.noun.delayedOperationTime)
    },
    getSmallprogramqrcodeGetCodeByScene () {
      if(!this.$validate.isNull(this.scene)) {
        this.$api.common.smallprogramqrcodeGetCodeByScene({scene: this.scene.sceneCode}).then(res => {
          if(!this.$validate.isNull(res.data)) {
            const { userId,tenantId } = res.data
            // 邀约注册
            this.form = {
              ...this.form,
              registerSource: 2,
              physicianUserId: userId,
              tenantId: tenantId
            }
          }
        })
      }
    },
    async getDetail () {
      const {id} = this.query
      const res = await this.$api.patientinfo.patientinfoQueryOne({id})
      if(!this.$validate.isNull(res.data)) {
        for(const key in this.form) {
          if(key === 'birthDate') {
            this.form[key] = res.data[key] ? this.$timePlugin.parseTime(new Date(res.data[key]), '{y}-{m}-{d}') : ''
          } else if (key === 'headPath') {
            this.form[key] = res.data[key]
            if(!this.$validate.isNull(this.form.headPath)) {
              this.cDataHeadPath = [{url: this.file_ctx + this.form.headPath}]
              this.$forceUpdate()
            }
          } else {
            this.form[key] = res.data[key]
          }
        }
        this.$set(this.config, 'phone', {...this.config.phone, disabled: true})
      }
    },
    imgReturnFn (obj,key) {
      this.form[key] = obj[0].dir
    }
  }
}
</script>

<style lang="scss" scoped>
  .head {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    display: -webkit-flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    -webkit-justify-content: flex-start;
    justify-content: flex-start;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
    padding: 24upx 24upx 16upx;
    .l{
      width: 10upx;
      height: 32upx;
      background: #00D29D;
      border-radius: 1px;
    }
    .r{
      margin-left: 10upx;
    }
  }
  .form-content {
    padding: 0 24upx;
    background-color: #fff;
    .img-title {
      padding-top: 24upx;
    }
  }
  .btn-view-main{
    bottom: 0;
    padding: 20upx 0 64upx;
    height: 164upx;
    box-sizing: border-box;
    text-align: center;
    z-index: 2;
    .btn-view-li{
      display: inline-block;
      vertical-align: middle;
      box-sizing: border-box;
      overflow: hidden;
      width: 100%;
      padding: 0 30upx;
      margin: 0 auto;
      .btn-view-li-view{
        height: 64upx;
        line-height: 64upx;
        font-size: 32upx;
        text-align: center;
        @include rounded(32upx);
        //background:linear-gradient(90deg,rgba(255,185,36,1),rgba(254,219,55,1));
        background: $topicC;
        color: #fff;
      }
      .border-color-but{
        border: 2upx solid $topicC;
        color: $topicC;
        background: none;
      }
    }
  }
</style>
