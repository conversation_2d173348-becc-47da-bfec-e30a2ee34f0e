<template>
  <view class="myLuckyCoinBg">
      <uni-nav-bar
        @clickLeft="back"
        color="white"
        :border="false"
        left-icon="left"
        :fixed="false"
        statusBar
        left-width="48rpx"
        right-width="200rpx"
        backgroundColor="rgba(0,0,0,0)"
        title="我的福币"
      >
        <view class="main-search">我的福币</view>
      </uni-nav-bar>
      <!-- 头部 -->
      <view class="tabHeader">
        <image class="topIcon" :src="bigLuckyCoin" mode="aspectFill"></image>
        <!-- 头像&用户名 -->
        <view class="tabTopBox">
          <image class="headimg" :src="file_ctx+avatarUrl" mode="aspectFill"></image>
          <text>{{fansRecord.nickName}}</text>
        </view>
        <!-- 福币模块 -->
        <view class="luckCoinNum">
          <view class="luckCoinTitle">我的福币（个）</view>
          <view class="luckyCionNums">{{luckyCionNums}}</view>
          <view class="addLuckyCoinBox">
            <view class="addLuckyCoin" @click="gotoPages('/modules/activity/calabash/luckyCoinaTask')">攒福币</view>
            <view class="exchangeGift" @click="gotoPages('/modules/activity/calabash/exchangeWings/integrationShop')">兑换好礼</view>
          </view>
        </view>
      </view>
      <!-- 任务列表 -->
        <view class="" style="position: relative;">
          <view class="luckyCoinTaskTitle">
            <view class="luckyCoinTaskTitleContent">福币记录</view>
          </view>
          <scroll-view class="luckyCoinTaskMap" :scroll-top="scrollTop" scroll-y="true" @scrolltolower="lower">
          	<view class="luckyCoinTask" v-for="(item,index) in luckyCoinTaskMap" :key="index">
              <view class="taskL">
                <view class="taskTitle">{{item.taskTitle}}</view>
                <view class="luckCoinTime">{{formatTimestamp(item.createTime)}}</view>
              </view>
              <view class="taskR">
                <view class='operate1' v-if="item.operate == 1">+{{item.point}}</view>
                <view class='operate2' v-if="item.operate == 2">-{{item.point}}</view>
                <view class='operate2' v-if="item.operate == 3">-{{item.point}}</view>
              </view>
          	</view>
            <view class="loadEnd" v-if="loadEnd">暂无更多数据</view>
          </scroll-view>
        </view>
  </view>
</template>

<script>
  import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'
  import { mapState } from 'vuex'
  import calabashApis from "@/modules/common/api/calabash.js"
  import common from '@/common/util/main'
  export default{
    components:{
      uniNavBar
    },
    data(){
      return {
        file_ctx: this.file_ctx,
        defaultAvatar: this.$static_ctx + 'image/system/avatar/icon-default-avatar.png',
        bigLuckyCoin: this.$static_ctx + 'image/business/hulu-v2/bigLuckyCoin.png',
        gotoRight: this.$static_ctx + 'image/business/hulu-v2/goto-right.png',
        luckyCionNums:0,
        accountId:0,
        current:0,
        luckyCoinTaskMap:[],
        loadEnd:false
      }
    },
      computed: {
        ...mapState('user', {
          fansRecord: state => state.fansRecord,
        }),
        avatarUrl(){
          return this.fansRecord.headPath ? this.fansRecord.headPath : this.defaultAvatar
        },
      },
    methods:{
      gotoPages(url){
        uni.navigateTo({url})
      },
      async getPointuserQueryUser(){
        console.log('this.fansRecord.accountId',this.accountId);
        let {data} = await calabashApis.pointuserQueryUser({accountId:this.accountId})
        this.luckyCionNums = data.totalPoint
      },
      async getPointrecordQueryPage(){
        if(this.loadEnd) return
        this.current+=1
        let {data:{records,total}} = await calabashApis.pointrecordQueryPage({
          current:this.current,
          size:10,
          condition:{accountId:this.accountId}
        })
        if(total <= this.luckyCoinTaskMap.length){
           this.loadEnd = true
           return
        }
        let {data:EventTypeList} = await calabashApis.getEventTypeList();
        let {data:BusinessTypeList} = await calabashApis.getBusinessTypeList();
        BusinessTypeList.push(...[{id:-1,desc:'回收积分'},{id:-2,desc:'消耗积分'}])
        EventTypeList.push(...[{id:-1,desc:'删除帖子'},{id:-2,desc:'删除帖子回复评论'},{id:-3,desc:'删除评论'},{id:-4,desc:'兑换商品'}])
        this.luckyCoinTaskMap.push(...records)
        this.luckyCoinTaskMap.map(e=>{
          let business = BusinessTypeList.filter(business=>business.id === e.businessType)[0]?.desc
          let event = EventTypeList.filter(event=>event.id === e.eventType)[0]?.desc
          let prefixMap = ['','参与','福币支出','福币']
          this.$set(e,'taskTitle',`${prefixMap[e.operate]}${business}_${event}`)
        })
      },
      lower(){
        this.getPointrecordQueryPage()
      },
      gotoTask(){
        uni.navigateTo({
          url:'/modules/activity/calabash/luckyCoinaTask'
        })
      },
      formatTimestamp(timestamp) {
        var date = new Date(timestamp);
        var year = date.getFullYear();
        var month = ('0' + (date.getMonth() + 1)).slice(-2);
        var day = ('0' + date.getDate()).slice(-2);
        var hour = ('0' + date.getHours()).slice(-2);
        return `${year}年-${month}月-${day}日-${hour}时`;
      },
      back() {
        let pages = getCurrentPages() // 获取栈实例
        if (pages.length > 1) {
          this.$navto.back()
        } else {
          this.$navto.replaceAll('Index')
        }
      },
    },
    onLoad() {
      this.accountId = common.getKeyVal('user', 'accountId', true),
      this.$nextTick(this.getPointuserQueryUser)
      this.$nextTick(this.getPointrecordQueryPage)
    }
  }
</script>
<style>
  page{
    background-color: #F4F6FA;
  }
</style>
<style lang="scss">
  .myLuckyCoinBg{
    width: 100vw;
    height: 100vh;
    @include boxBgBYOUnit(100vw, 852rpx, '/business/hulu-v2/myLuckyCoinBg.png');
    background-repeat: no-repeat;
  }
  .main-search{
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .tabHeader{
    position: relative;
    padding: 38rpx 32rpx;
    .topIcon{
      position: absolute;
      top: 66rpx;
      right: 52rpx;
      width: 216rpx;
      height: 246rpx;
      z-index: 99999;
    }
    .tabTopBox{
      font-weight: 500;
      font-size: 32rpx;
      color: #FFFFFF;
      display: flex;
      align-items: center;
      .headimg{
        width: 56rpx;
        height: 56rpx;
        border-radius: 50%;
        margin-right: 16rpx;
      }
    }
    .luckCoinNum{
      border-radius: 20rpx;
      margin-top: 22rpx;
      width: 100%;
      height: 352rpx;
      @include boxBgBYOUnit(100%, 352rpx, '/business/hulu-v2/luckCoinNumCard.png');
      backdrop-filter: blur(4rpx);
      padding: 46rpx 42rpx;
      box-sizing: border-box;
      .luckCoinTitle{
        font-weight: 400;
        font-size: 28rpx;
        color: #4E5569;
      }
      .luckyCionNums{
        font-weight: 500;
        font-size: 72rpx;
        color: #1D2029;
        font-weight: 600;
      }
      .addLuckyCoinBox{
        display: flex;
        margin-top: 36rpx;
        font-weight: 500;
        font-size: 32rpx;
        text-align: center;
        justify-content: space-between;
          .addLuckyCoin{
            width: 284rpx;
            height: 80rpx;
            line-height: 80rpx;
            background: #FFFFFF;
            border-radius: 40rpx;
            border: 2rpx solid #D9DBE0;
            color: #1D2029;
          }
          .exchangeGift{
            width: 282rpx;
            height: 80rpx;
            line-height: 80rpx;
            background: #00B484;
            border-radius: 40rpx;
            color: #FFFFFF;
          }
      }

    }
  }
  .luckyCoinTaskTitle{
    border-radius: 16rpx 16rpx 0 0;
    display: flex;
    position: absolute;
    top: 0;
    left: 32rpx;
    justify-content: center;
    justify-content: space-between;
    width: 100;
    width: 686rpx;
    padding: 32rpx;
    box-sizing: border-box;
    background: white;
    z-index: 99999;
    .luckyCoinTaskTitleContent{
      font-weight: 600;
      font-size: 32rpx;
      color: #1D2029;
    }
    .luckyCoinTaskTitleGoto{
      font-weight: 500;
      font-size: 32rpx;
      color: #1D2029;
    }
  }
  .luckyCoinTaskMap{
    width: 686rpx;
    height: 694rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    margin-top: 24rpx;
    padding: 32rpx;
    box-sizing: border-box;
    margin-left: 32rpx;
    padding-top: 114rpx;
    .loadEnd{
      text-align: center;
    }
    .luckyCoinTask{
      height: 126rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .taskL{
        .taskTitle{
          font-weight: 400;
          font-size: 28rpx;
          color: #1D2029;
        }
        .luckCoinTime{
          font-weight: 400;
          font-size: 24rpx;
          color: #868C9C;
          margin-top: 4rpx;
        }
      }
      .taskR{
        font-weight: 500;
        font-size: 36rpx;
        .operate1{
          color: #00B484;
        }
        .operate2{
          color: #FF5500;
        }
      }

    }
  }
</style>
