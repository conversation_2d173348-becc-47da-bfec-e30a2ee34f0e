<template>
  <div class="list">
    <template v-if="!$validate.isNull(pdList)">
      <div v-for="item in pdList" :key="item.id" class="item" @click="navTo(item)">
        <el-card :body-style="{ padding: '0px', height: '100%' }" class="item-card" shadow="hover">
          <div class="image-box">
            <img :src="item.coverPath ? $env.file_ctx + item.coverPath : ''" class="image">
          </div>
          <div class="item-content">
            <span>{{ item.title }}</span>
            <span class="item-desc">{{ item.descText }}</span>
            <div class="bottom clearfix">
              <span class="time">{{ item.endTimeText }}</span>
            </div>
          </div>
        </el-card>
      </div>
    </template>
    <template v-else>
      <div class="empty">
        暂无数据
      </div>
    </template>
  </div>
</template>

<script>
export default {
  props: {
    pdList: {
      default: function() {
        return []
      },
      type: Array
    }
  },
  methods: {
    navTo(item) {
      this.$router.push({
        path: '/activity/case-collect',
        query: { id: item.id }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
// @import "~@/styles/variables.scss";
// @import "~@/styles/mixin.scss";
.list {
  display: flex;
  flex-wrap: wrap;
  min-height: 320px;
  .item {
    display: flex;
    flex-direction: column;
    min-width: 270px;
    width: calc(20% - 12px);
    height: 320px;
    margin-right: 12px;
    cursor: pointer;
    margin-bottom: 36px;
    &:hover {
      .image {
        transform: scale(1.2);
      }
    }
    .image-box {
      overflow: hidden;
      height: 164px;
      background-color: #000;
      text-align: center;
      .image {
        object-fit: cover;
        height: 100%;
        transition-duration: .5s;
        transform-origin: 50% 50%;
      }
    }
    &-card {
      height: 100%;
    }
    .time {
      font-size: 14px;
      color: #999;
    }
    &-content {
      height: calc(100% - 164px);
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 14px 14px 32px;
      flex: 1;
    }
    &-desc {
      flex: 1;
      color: #777;
      line-height: 24px;
      font-size: 14px;
      @include ellipsis(3);
    }
  }
}
.empty {
  width: 100%;
  font-size: 12px;
  color: #999;
  line-height: 60px;
  text-align: center;
}
</style>
