import router from '../index'
import store from '@/store'
import constant from '@/constant'
import ext from '@/service/ext'
import validate from '@/common/util/validate'
import common from '@/common/util/main'
import navto from '@/router/config/nav-to'
const launchOptions = uni.getLaunchOptionsSync()
import serverOptions from '@/config/env/options'

/*
NAVTYPE取值类型 1.1.0+
1. push
跳转到普通页面，新开保留历史记录
2. replace
动态的导航到一个新 URL 关闭当前页面，跳转到的某个页面。
3. replaceAll
动态的导航到一个新 URL 关闭所有页面，打开到应用内的某个页面
4. pushTab
动态的导航到一个新 url 关闭所有页面，打开到应用内的某个tab
*/

/**
 * 前置处理
 */
router.beforeEach(async (to, from, next) => {
  console.log("to->:",to)
  console.log("from->:",from)
  // 加载云陪诊参数
  await serverOptions.handlingCloudProviderId(to)
  mainHandler(store, to, from, next)
})

/**
 * 后置处理
 */
router.afterEach((to, from, next) => {
})
/**
 * 处理主逻辑
 * @param store
 * @param to
 * @param from
 * @param next
 */
async function mainHandler(store, to, from, next) {
  if (to.name === 'NotFind') {
    next()
    return
  }
  // #ifdef MP-WEIXIN || H5 || MP-ALIPAY
  // 获取accoutId 绑定accountId
  await ext.user.getAccountIdGroup(launchOptions.scene)
  // #endif
  ext.common.addMinichannellinklogLaunchVisit()

  // #ifdef H5
  // 获取哈希模式下的路由参数
    let routerOptions = parsedHashRouterOptions()
    if(routerOptions.token && from.path === "/modules/activity/calabash/index"){
      common.setKeyVal('user', 'token', routerOptions.token, true)
      common.setKeyVal('user', 'accountId', routerOptions.accountId, true)
      common.setKeyVal('user', 'username', routerOptions.username, true)
      common.setKeyVal('user', 'isLogin', true, true)
      nextHandler(to, from, next)
      return
    }
  // #endif
  // 判断是否已经登录
  if (common.getIsLogin() && !validate.isNull(common.getToken())) {
    common.setKeyVal('user', 'isLogin', true, true)
    // 已经登录逻辑
    if (to.name === 'Login') {
      navto.pushTab('Index')
      return
    }

    if (validate.isNull(store.state.user.curSelectStore)) {
      dmOfBusiness(store, to, from, next, () => {
        // #ifdef MP-WEIXIN || H5
        ext.user.addFans().then(() => {
          ext.user.usertenantrecordBindFans()
        })
        // #endif
        nextHandler(to, from, next)
      })
      if (!ext.webSocket.webSocket) {
        ext.webSocket.webSocketInit()
      }
    } else {
      nextHandler(to, from, next)
    }

  } else {
    // 临时登录token过期时间
    const tokenExpirationTime = common.getKeyVal('user', 'tokenExpirationTime', true)
    // 没有登录逻辑
    // #ifdef MP-WEIXIN || H5 || MP-ALIPAY
    if (validate.isNull(common.getToken()) || (tokenExpirationTime && Date.now() > tokenExpirationTime)) {
      await ext.user.accountLogin()
    }
    if (validate.isNull(common.getKeyVal('user', 'fansRecord'))) {
      await ext.user.addFans()
    }
    // #endif

    common.setKeyVal('user', 'isLogin', false, true)
    if (to.name === 'Login' && from.name === 'Chat') {
      // #ifdef MP-WEIXIN || H5
      next({
        name: 'ChatLogin',
        params: { redirect: to.name, redirectParams: encodeURIComponent(JSON.stringify(to.query)), formPage: from.name, formPageParams: encodeURIComponent(JSON.stringify(from.query)) },
        NAVTYPE: 'replaceAll'
      })
      // #endif
      // #ifndef MP-WEIXIN
      next()
      // #endif
    } else {
      whiteHandler(to, from, next)
    }
  }
}
/**
 * 游客模式
 */
function touristMode(store, next) {
  jumpInvalid500(next)
}

/**
 * 登录过程中出现错误，跳转到该页面
 */
function jumpInvalid500(next) {
  router.replace({ name: 'NotFind' })
}

// 解析hash模式路由参数
function parsedHashRouterOptions(){
  let routerOptions = window.location.href.split('?')[1]?.split('&')?.reduce((res,index)=>{
    let map = index.split('=');
    res[map[0]] = map[1];
    return res
  },{})
  return routerOptions || {}
}

/**
 * 校园业务
 * @param store
 * @param to
 * @param from
 * @param next
 */
function dmOfBusiness(store, to, from, next, callback) {
  ext.user.getInfoGroup(() => { // 正常
    callback()
  }, () => { // 没有数据
    callback()
  }, (e) => { // 错误回调
    console.log("getInfoGroup error:",e)
    if (e.statusCode!==401){
      touristMode(store, next)
    }
    next(false)
  })
}
/**
 * 白名单跳转处理
 * @param to
 * @param from
 * @param next
 */
function whiteHandler(to, from, next) {
  if (constant.system.whiteRouterList.indexOf(to.name) !== -1) {
    nextHandler(to, from, next)
  } else {
    console.log("401 out")
    next({
      name: 'Login',
      params: { redirect: to.name, redirectParams: encodeURIComponent(JSON.stringify(to.query)), formPage: from.name, formPageParams: encodeURIComponent(JSON.stringify(from.query)) },
      NAVTYPE: 'replaceAll'
    })
  }
}

/**
 * 跳转回到原始路由操作
 */
function nextHandler(to, from, next) {
  if (to.name !== 'Index') {
    next()
  } else {
    if (from.name === 'Index' && to.name !== 'Index') {
      next({ name: to.name })
    } else {
      next()
    }
  }
}
